package middlewares

import (
	"context"
	"database/sql"
	"finbox/go-api/errorHandler"
	"finbox/go-api/functions/logger"
	"finbox/go-api/internal/util/tags"
	"finbox/go-api/models/users"
	"net/http"
)

func UserExistsCheck(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.RecoveryV3(w, r, http.StatusConflict)

		sourceEntityID := r.Context().Value("sourceEntityID").(string)

		attributes := r.Context().Value("attributes").(map[string]interface{})
		customerID := attributes["customerID"].(string)

		user, err := users.GetByUniqueID(customerID, sourceEntityID)
		if err != nil {
			logger.WithRequest(r).Errorln(err)
			if err == sql.ErrNoRows {
				ctx := context.WithValue(r.Context(), UserExistsKey, false)
				next.ServeHTTP(w, r.WithContext(ctx))
				return
			}
			panic(err)
		}

		tags.SetTagValue(r.Context(), tags.UserObject, user)

		ctx := context.WithValue(r.Context(), UserExistsKey, true)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}
