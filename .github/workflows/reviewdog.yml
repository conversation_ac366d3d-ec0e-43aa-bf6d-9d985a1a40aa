name: ReviewDog - Codebase Sanity Check

on:
  pull_request:
    branches:
      - develop
  workflow_dispatch:

jobs:
  reviewdog:
    name: Run tests for go lint, reviewdog, build and vet
    runs-on: ubuntu-latest
    env:
      # This is required to download dependencies during 'go build'
      # which are private repositories to finbox - like road-runner
      GITHUB_TOKEN: ${{ secrets.GH_PAT }}

    steps:
      # No need to cache this as they are already cached from github's side on their runners
      - name: Install dependencies (xslt + build tools)
        run: |
          sudo apt-get update
          sudo apt-get install -y --no-install-recommends \
            build-essential libxml2-dev libxslt-dev xz-utils zlib1g-dev liblzma-dev

      # This is required to download dependencies during 'go build'
      # which are private repositories to finbox - like road-runner
      - name: Setup GitHub PAT (token) based access
        run: git config --global url.https://$<EMAIL>/.insteadOf https://github.com/

      - uses: actions/checkout@v4
        with:
          fetch-depth: 0 # This needs to be set 0 to fetch all the history
          # so that common base commit is found and we can run lint on the diff only

      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version-file: go.mod

      - name: Set up reviewdog
        uses: reviewdog/action-setup@v1
        with:
          reviewdog_version: latest

      - name: Cache Go mod and build
        uses: actions/cache@v4
        with:
          path: |
            ~/go/pkg/mod
            ~/.cache/go-build
          key: middleware-go-${{ runner.os }}-${{ hashFiles('**/go.sum') }}
          restore-keys: |
            middleware-go-${{ runner.os }}-

      - name: Cache golangci-lint results
        uses: actions/cache@v4
        with:
          path: ~/.cache/golangci-lint
          key: middleware-golangci-${{ runner.os }}-v1
          restore-keys: |
            middleware-golangci-${{ runner.os }}-

      - name: Set up golangci-lint
        if: steps.cache-golangci-lint.outputs.cache-hit != 'true'
        run: |
          curl -sSfL https://raw.githubusercontent.com/golangci/golangci-lint/master/install.sh | sh -s -- -b $(go env GOPATH)/bin
          $(go env GOPATH)/bin/golangci-lint --version

      - name: Run golangci-lint
        run: |
          BASE_REF=$(git merge-base origin/develop HEAD)
          echo "BASE_REF: $BASE_REF"
          echo "Running golangci-lint from revision $BASE_REF..."
          $(go env GOPATH)/bin/golangci-lint run -v --verbose --config=.github/.golangci.yml --new-from-rev=$BASE_REF --allow-parallel-runners 2>&1 | tee golangci-lint.log

      - name: Display golangci-lint log
        run: |
          if [[ -f golangci-lint.log ]]; then
            cat golangci-lint.log
          else
            echo "No golangci-lint log found."
            exit 1
          fi

      - name: Run golangci-lint with reviewdog
        env:
          REVIEWDOG_GITHUB_API_TOKEN: ${{ secrets.GH_PAT }}
        run: cat golangci-lint.log | reviewdog -f=golangci-lint -reporter=github-pr-review -fail-on-error=false

      - name: Run SQL migrations linter with reviewdog
        env:
          REVIEWDOG_GITHUB_API_TOKEN: ${{ secrets.GH_PAT }}
        run: reviewdog -conf=.github/reviewdog-config.yml -reporter=github-pr-review

      - name: Build Go code
        run: go build ./...

      - name: Vet Go code
        run: go vet ./...
