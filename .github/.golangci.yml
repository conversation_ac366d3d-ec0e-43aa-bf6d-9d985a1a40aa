version: "2"

cache:
  enabled: true

run:
  concurrency: 4
  timeout: 12m
  allow-parallel-runners: true

linters:
  default: none
  enable:
    - errcheck
    - ineffassign
    - staticcheck
    - unused
    - gosec
    - misspell
    - forbidigo
  settings:
    forbidigo:
      exclude-godoc-examples: true
      forbid:
        - pattern: ^fmt\.Print.*$
          msg: Do not commit print statements.
        - pattern: ^log\.Panic$
          msg: Do not use panic
        - pattern: ^log\.Fatal$
          msg: Do not use fatal
        - pattern: ^log\.Print.*$
          msg: Do not use console print
        - pattern: ^errorHandler\.ReportToSentryWithFields\(\s*map\[string\]interface{}\{\s*\}\s*,
          msg: Do not report to sentry without adequate debug info
        - pattern: ^errorHandler.ReportToSentryWithoutRequest
          msg: Do not report to sentry without adequate debug info
        - pattern: ^(?i)(database|db|namedStmt|namedQuery)\.(Get|Select|Queryx)(:?Context)?\(
          msg: Please ensure that a partial or complete index exists on the column used in where clause if any

  exclusions:
    warn-unused: false
    paths:
      - cmd/
      - migrations/scripts/

formatters:
  exclusions:
    warn-unused: false
    paths:
      - cmd/
      - migrations/scripts/
