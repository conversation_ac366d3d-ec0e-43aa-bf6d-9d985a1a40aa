package sentinel

import (
	"finbox/go-api/constants"
)

const maxTry = 7

const (
	Progressing     = 0
	DefaultProgress = -1
)

// constants for service names
const (
	TriggerEvalServiceName     = "trigger_evaluation_sentinel"
	DataDumpServiceName        = "sentinel_data_dump"
	FetchEvalServiceName       = "fetch_evaluation_sentinel"
	DownloadCamServiceName     = "download_cam_sentinel"
	FetchPredictorServiceName  = "fetch_predictor_sentinel"
	DebugEvaluationServiceName = "debug_evaluation_sentinel"
)

var abcdSentinelRankedLenderNamesMap = map[string]string{
	"ltfs":     constants.LandTID,
	"fibe":     constants.FibeID,
	"prefr":    constants.PrefrMCID,
	"ring":     constants.RingMCID,
	"abcd-pfl": constants.ABCDPFLID,
}

var mcSentinelRankedLenderNamesMap = map[string]string{
	"ltfs":   constants.LandTID,
	"fibe":   constants.FibeID,
	"niro":   constants.NiroID,
	"abfl":   constants.ABFLPLID,
	"cashe":  constants.CasheMCID,
	"prefr":  constants.PrefrMCID,
	"ring":   constants.RingMCID,
	"bajaj":  constants.BajajID,
	"incred": constants.IncredApiStackID,
	"pfl":    constants.MCPFLID,
	"kb":     constants.MCKreditbeeID,
}

var offerRankNonPRODBreEndPointMap = map[string]string{
	constants.MoneyControlID:    "emb_lr_tes_rou_kJH",
	constants.ABCDMarketPlaceID: "adi_per_abc_rou_fZS",
}

var offerRankPRODBreEndPointMap = map[string]string{
	constants.MoneyControlID:    "emb_len_len_rou_lIe",
	constants.ABCDMarketPlaceID: "adi_per_abc_len_EWW",
}

var fetchActiveLendersNonPRODBreEndPointMap = map[string]string{
	constants.TataPLID: "emb_lr_lr_len_Fas",
}

var fetchActiveLendersPRODBreEndPointMap = map[string]string{
	constants.TataPLID: "emb_len_len_len_HVY",
}
