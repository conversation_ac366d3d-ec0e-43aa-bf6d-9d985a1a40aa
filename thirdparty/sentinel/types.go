package sentinel

import (
	"finbox/go-api/functions/structs"
	"finbox/go-api/models/personalloanoffer"
)

type TriggerEvalReqStruct struct {
	ReferenceID  string                 `json:"referenceID"`
	EndPointCode string                 `json:"endpointCode"`
	UserID       string                 `json:"userID"`
	Source       string                 `json:"source"`
	Additional   map[string]interface{} `json:"additional"`
}

type FetchEvalReqStruct struct {
	EvaluationID string `json:"evalEndpointID"`
	EndPointCode string `json:"endpointCode"`
}

type TriggerEvalResStruct struct {
	Data struct {
		EvaluationID string `json:"evalEndpointID"`
	} `json:"data"`
	Error string `json:"error"`
}

type FinalOutput struct {
	Decision           string                  `json:"decision"`
	DecisionFrom       string                  `json:"decisionFrom"`
	Output             EvaluationDataStruct    `json:"output"`
	ReasonsOfRejection map[string][]RuleStruct `json:"reasonsOfRejection"`
}

type PolicyOutput struct {
	PolicyVersion string               `json:"policyVersion"`
	Output        EvaluationDataStruct `json:"output"`
}

type RuleOutput struct {
	Name   string      `json:"name"`
	Output interface{} `json:"output"`
}

type RuleSetOutput struct {
	Name     string       `json:"name"`
	Decision string       `json:"decision"`
	Rules    []RuleStruct `json:"rules"`
	ID       string       `json:"id"`
	Tag      string       `json:"tag"`
}

type ModelOutput struct {
	Name   string                 `json:"name"`
	Output map[string]interface{} `json:"output"`
}

type BranchesOutput struct {
	Name   string `json:"name"`
	Output any    `json:"output"`
}

type FetchEvaluationResStruct struct {
	Data     DataWithWorkflows `json:"data"`
	Progress int               `json:"progress"`
	Error    string            `json:"error"`
}

type DataWithWorkflows struct {
	Final     FinalOutput       `json:"final"`
	Policies  []PolicyOutput    `json:"policies"`
	Rules     []RuleOutput      `json:"rules"`
	Workflows []WorkflowsOutput `json:"workflows"`
	RuleSet   []RuleSetOutput   `json:"ruleset"`
	Models    []ModelOutput     `json:"models"`
	Branches  []BranchesOutput  `json:"branches"`
}

type WorkflowsOutput struct {
	Name     string               `json:"name"`
	Decision string               `json:"decision"`
	Output   EvaluationDataStruct `json:"output"`
}

type EvaluationDataStruct struct {
	RequestStatus      int                     `json:"request_status"`
	Amount             float64                 `json:"amount"`
	BPIAmount          float64                 `json:"bpi_amount"`
	FirstEmiDate       string                  `json:"final_emi_date"`
	Decision           string                  `json:"decision"`
	Interest           float64                 `json:"interest"`
	MaxTenure          int                     `json:"max_tenure"`
	ProcessingFee      float64                 `json:"processing_fee"`
	ProcessingFeeType  string                  `json:"processing_fee_type"`
	RiskBucket         string                  `json:"risk_bucket"`
	Rules              []RuleStruct            `json:"rules"`
	EMI6M              float64                 `json:"emi_6m"`
	EMI12M             float64                 `json:"emi_12m"`
	EMI18M             float64                 `json:"emi_18m"`
	EMI24M             float64                 `json:"emi_24m"`
	EMI30M             float64                 `json:"emi_30m"`
	EMI36M             float64                 `json:"emi_36m"`
	EMI42M             float64                 `json:"emi_42m"`
	EMI48M             float64                 `json:"emi_48m"`
	EMI54M             float64                 `json:"emi_54m"`
	EMI60M             float64                 `json:"emi_60m"`
	MaxEMI             float64                 `json:"max_emi"`
	MaxEDI             float64                 `json:"max_edi"`
	OutputVariables    map[string]interface{}  `json:"output"`
	Income             float64                 `json:"income"`
	MinTenure          int                     `json:"min_tenure"`
	MinAmount          float64                 `json:"min_amount"`
	DecisionNode       string                  `json:"decisionNode"`
	ReasonsOfRejection map[string][]RuleStruct `json:"reasonsOfRejection"`
	EValidationOutput  string                  `json:"e_validation_output"`
	UANFlowOutput      string                  `json:"uan_flow_output"`
	APR                float64                 `json:"apr"`
}

type EvaluationRunStruct struct {
	ReferenceID  string               `json:"referenceID"`
	EvaluationID string               `json:"evaluationID"`
	Data         EvaluationDataStruct `json:"data"`
	Error        string               `json:"error"`
	DumpID       string               `json:"dumpID"`
}

type RuleStruct struct {
	Decision string                 `json:"decision"`
	Details  string                 `json:"details"`
	Rule     string                 `json:"rule"`
	RuleID   string                 `json:"ruleID"`
	Value    string                 `json:"value"`
	Output   map[string]interface{} `json:"output"`
}

type FetchPredictorsStruct struct {
	Data struct {
		JSON PredictorList `json:"json"`
		URL  string        `json:"url"`
	} `json:"data"`
	Error  string `json:"error"`
	Status bool   `json:"status"`
}

type PredictorList struct {
	Application      map[string]interface{} `json:"application"`
	Bank             map[string]interface{} `json:"bank"`
	Bureau           map[string]interface{} `json:"bureau"`
	Device           map[string]interface{} `json:"device"`
	Global           map[string]interface{} `json:"global"`
	GST              map[string]interface{} `json:"gst"`
	Input            map[string]interface{} `json:"input"`
	Additional       map[string]interface{} `json:"additional"`
	MultiBank        map[string]interface{} `json:"multibank"`
	ScoreCard        map[string]interface{} `json:"scorecard"`
	CommercialBureau map[string]interface{} `json:"commercialbureau"`
}

type OutputVariablesOfferDetails struct {
	MaxAmount               float64                     `json:"maxAmount"`
	MinAmount               float64                     `json:"minAmount"`
	Tenure                  int                         `json:"tenure"`
	ProcessingFee           float64                     `json:"processingFee"`
	Interest                float64                     `json:"interest"`
	Charges                 []personalloanoffer.Charges `json:"charges"`
	EligibleLoanAmount      float64                     `json:"eligible_loan_amount,omitempty"`
	ProcessingFeeGST        float64                     `json:"processing_fee_gst_amount,omitempty"`
	ProcessingFeePercentage float64                     `json:"processing_fee_percentage,omitempty"`
	ROIAmount               float64                     `json:"rate_of_interest_amount,omitempty"`
	RateOfInterest          float64                     `json:"rate_of_interest_percentage,omitempty"`
	MaxEMI                  float64                     `json:"max_emi,omitempty"`
	MaxEDI                  float64                     `json:"max_edi,omitempty"`
	MaxTenure               int                         `json:"max_tenure,omitempty"`
	Amount                  float64                     `json:"amount"`
	BPIAmount               float64                     `json:"bpi_amount"`
	EMI6M                   float64                     `json:"emi_6m"`
	EMI12M                  float64                     `json:"emi_12m"`
	EMI18M                  float64                     `json:"emi_18m"`
	EMI24M                  float64                     `json:"emi_24m"`
	EMI30M                  float64                     `json:"emi_30m"`
	EMI36M                  float64                     `json:"emi_36m"`
	EMI42M                  float64                     `json:"emi_42m"`
	EMI48M                  float64                     `json:"emi_48m"`
	EMI54M                  float64                     `json:"emi_54m"`
	EMI60M                  float64                     `json:"emi_60m"`
}

type DebugEvaluationRes struct {
	Data struct {
		Final        FinalOutput    `json:"final"`
		Policies     []PolicyOutput `json:"policies"`
		Rules        []RuleOutput   `json:"rules"`
		EvaluationID string         `json:"evalEndpointID"`
	} `json:"data"`
	Progress int    `json:"progress"`
	Error    string `json:"error"`
}

type SentinelDataDumpReqStruct struct {
	UserID      string         `json:"user_id"`
	ReferenceID string         `json:"reference_id"`
	Data        map[string]any `json:"data"`
}

type Result struct {
	Error   string `json:"error"`
	Success bool   `json:"success"`
}

type DataRes struct {
	ReferenceID string            `json:"referenceID"`
	Result      map[string]Result `json:"result"`
}

type SentinelDataDumpResStruct struct {
	Data   DataRes `json:"data"`
	Error  string  `json:"error"`
	Status bool    `json:"status"`
}

// FetchEvaluationResStructV2 new sentinel workflow response structure
type FetchEvaluationResStructV2 struct {
	Data     DataWithWorkflowsV2 `json:"data"`
	Progress int                 `json:"progress"`
	Error    string              `json:"error"`
}

type DataWithWorkflowsV2 struct {
	Final     FinalOutput         `json:"final"`
	Policies  []PolicyOutput      `json:"policies"`
	Rules     []RuleOutput        `json:"rules"`
	Models    []ModelOutput       `json:"models"`
	Workflows []WorkflowsOutputV2 `json:"workflows"`
}

type WorkflowsOutputV2 struct {
	Name     string                 `json:"name"`
	Decision string                 `json:"decision"`
	Output   map[string]interface{} `json:"output"`
}

type FetchEvalOptions struct {
	MaxTries int
	LogInDB  bool
}

type RankingOffersData struct {
	LenderID         string
	UserID           string
	SourceEntityID   string
	Offers           *[]structs.LoanOfferDetailsStruct
	SentinelResponse FetchEvaluationResStructV2 // sentinel ranking resp
}

type OfferRankingConfig struct {
	NiroTenure                    int     `json:"niro_tenure"`
	NiroIsPreapproved             bool    `json:"niro_isPreapproved"`
	LtfsTenure                    int     `json:"ltfs_tenure"`
	AbflBankStatementMandatory    bool    `json:"abfl_bankStatementMandatory"`
	FibeIsPreapproved             bool    `json:"fibe_isPreapproved"`
	NiroBankStatementMandatory    bool    `json:"niro_bankStatementMandatory"`
	FibeAmount                    float64 `json:"fibe_amount"`
	LtfsBankStatementMandatory    bool    `json:"ltfs_bankStatementMandatory"`
	AbflRoi                       float64 `json:"abfl_roi"`
	NiroAmount                    float64 `json:"niro_amount"`
	LtfsIsPreapproved             bool    `json:"ltfs_isPreapproved"`
	NiroRoi                       float64 `json:"niro_roi"`
	LtfsAmount                    float64 `json:"ltfs_amount"`
	AbflIsPreapproved             bool    `json:"abfl_isPreapproved"`
	FibeTenure                    int     `json:"fibe_tenure"`
	AbflAmount                    float64 `json:"abfl_amount"`
	FibeRoi                       float64 `json:"fibe_roi"`
	LtfsRoi                       float64 `json:"ltfs_roi"`
	FibeBankStatementMandatory    bool    `json:"fibe_bankStatementMandatory"`
	AbflTenure                    int     `json:"abfl_tenure"`
	CasheIsPreapproved            bool    `json:"cashe_isPreapproved"`
	CasheTenure                   int     `json:"cashe_tenure"`
	CasheAmount                   float64 `json:"cashe_amount"`
	CasheRoi                      float64 `json:"cashe_roi"`
	CasheBankStatementMandatory   bool    `json:"cashe_bankStatementMandatory"`
	PrefrAmount                   float64 `json:"prefr_amount"`
	PrefrRoi                      float64 `json:"prefr_roi"`
	PrefrTenure                   int     `json:"prefr_tenure"`
	PrefrIsPreapproved            bool    `json:"prefr_isPreapproved"`
	PrefrBankStatementMandatory   bool    `json:"prefr_bankStatementMandatory"`
	RingAmount                    float64 `json:"ring_amount"`
	RingRoi                       float64 `json:"ring_roi"`
	RingTenure                    int     `json:"ring_tenure"`
	RingIsPreapproved             bool    `json:"ring_isPreapproved"`
	RingBankStatementMandatory    bool    `json:"ring_bankStatementMandatory"`
	BajajAmount                   float64 `json:"bajaj_amount"`
	BajajRoi                      float64 `json:"bajaj_roi"`
	BajajTenure                   int     `json:"bajaj_tenure"`
	BajajIsPreapproved            bool    `json:"bajaj_isPreapproved"`
	BajajBankStatementMandatory   bool    `json:"bajaj_bankStatementMandatory"`
	IncredAmount                  float64 `json:"incred_amount"`
	IncredRoi                     float64 `json:"incred_roi"`
	IncredTenure                  int     `json:"incred_tenure"`
	IncredIsPreapproved           bool    `json:"incred_isPreapproved"`
	IncredBankStatementMandatory  bool    `json:"incred_bankStatementMandatory"`
	ABCDPFLAmount                 float64 `json:"abcd_pfl_amount"`
	ABCDPFLRoi                    float64 `json:"abcd_pfl_roi"`
	ABCDPFLTenure                 int     `json:"abcd_pfl_tenure"`
	ABCDPFLIsPreapproved          bool    `json:"abcd_pfl_isPreapproved"`
	ABCDPFLBankStatementMandatory bool    `json:"abcd_pfl_bankStatementMandatory"`
	PFLAmount                     float64 `json:"pfl_amount"`
	PFLRoi                        float64 `json:"pfl_roi"`
	PFLTenure                     int     `json:"pfl_tenure"`
	PFLIsPreapproved              bool    `json:"pfl_isPreapproved"`
	PFLBankStatementMandatory     bool    `json:"pfl_bankStatementMandatory"`
	KBAmount                      float64 `json:"kb_amount"`
	KBRoi                         float64 `json:"kb_roi"`
	KBTenure                      int     `json:"kb_tenure"`
	KBIsPreapproved               bool    `json:"kb_isPreapproved"`
	KBBankStatementMandatory      bool    `json:"kb_bankStatementMandatory"`
}

type OfferRankingResp struct {
	LenderObj []LenderRankStruct   `json:"lender_obj"`
	Result    []LenderResultStruct `json:"result"`
}

type LenderResultStruct struct {
	Name string `json:"name"`
	Tag  string `json:"tag"`
	Rank int    `json:"rank"`
}

type LenderRankStruct struct {
	Amount                 int    `json:"amount"`
	BankStatementMandatory bool   `json:"bankStatementMandatory"`
	IsPreapproved          bool   `json:"isPreapproved"`
	Name                   string `json:"name"`
	Rank                   int    `json:"rank"`
	Roi                    int    `json:"roi"`
	Tag                    string `json:"tag"`
	Tenure                 int    `json:"tenure"`
}

type InputStruct struct {
	ID           string `json:"id"`
	Name         string `json:"name"`
	DataType     string `json:"dataType"`
	IsNullable   bool   `json:"isNullable"`
	DefaultInput string `json:"defaultInput"`
}

// "error": "json: cannot unmarshal array into Go struct field BranchesOutput.data.payload.data.branches.output of type string",
type FetchEndpointDetailsPayloadStruct struct {
	Data    DataWithWorkflows `json:"data"`
	Input   []InputStruct     `json:"input"`
	Name    string            `json:"name"`
	Version string            `json:"version"`
	Sources []string          `json:"sources"`
}

type FetchEndpointDetailsDataStruct struct {
	Payload    FetchEndpointDetailsPayloadStruct `json:"payload"`
	PolicyName string                            `json:"policy_name"`
	Version    string                            `json:"version"`
}

type FetchEndpointDetailsResStruct struct {
	Data   FetchEndpointDetailsDataStruct `json:"data"`
	Error  string                         `json:"error"`
	Status bool                           `json:"status"`
}
