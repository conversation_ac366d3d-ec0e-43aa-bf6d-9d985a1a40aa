package sentinel

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"finbox/go-api/conf"
	"finbox/go-api/constants"
	"finbox/go-api/functions/logger"
	"finbox/go-api/functions/requestutils"
	"finbox/go-api/functions/retry"
	"finbox/go-api/functions/serviceslib"
	"finbox/go-api/functions/tracer"
	"finbox/go-api/models/personalloanoffer"
	"finbox/go-api/utils/general"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"
)

var log = logger.Log

// SentinelDataDump facilitates data dumping to Sentinel system
func SentinelDataDump(ctx context.Context, userID, sourceEntityID, lenderID string, reqBody map[string]any) (string, string, SentinelDataDumpResStruct, error) {
	sentinelConf := conf.GetSentinelCreds(lenderID, sourceEntityID)
	baseURL := sentinelConf["baseURL"]
	apiKey := sentinelConf["apiKey"]

	url := fmt.Sprintf("%s/v2/sentinel/push", baseURL)

	var resBody []byte
	var resObj SentinelDataDumpResStruct

	sentinelError := func() error {
		tags := map[string]interface{}{
			"client": sourceEntityID,
			"userID": userID,
		}
		client := tracer.GetTraceableHTTPClientV2(nil, DataDumpServiceName, sourceEntityID, tags)
		payload := new(bytes.Buffer)
		if err := json.NewEncoder(payload).Encode(reqBody); err != nil {
			logger.WithUser(userID).Error(err)
			return err
		}
		req, err := requestutils.GetMockableHTTPRequest(userID, DataDumpServiceName, "POST", url, payload)
		if err != nil {
			logger.WithUser(userID).Error(err)
			return err
		}

		req.Header.Add("x-api-key", apiKey)
		req.Header.Add("Content-Type", "application/json")
		res, err := client.Do(req)
		if err != nil {
			logger.WithUser(userID).Error(err)
			return err
		}

		resObj, resBody, err = processSentinelDataDumpRes(res, userID)
		return err
	}()

	return string(resBody), url, resObj, sentinelError
}

// TriggerEvaluation initiates an trigger evaluation request
func TriggerEvaluation(ctx context.Context, userID, sourceEntityID, lenderID string, reqBody TriggerEvalReqStruct) (error, string, string, TriggerEvalResStruct) {
	sentinelConf := conf.GetSentinelCreds(lenderID, sourceEntityID)
	baseURL := sentinelConf["baseURL"]
	apiKey := sentinelConf["apiKey"]

	url := fmt.Sprintf("%v/v1/sentinel/triggerEvaluation", baseURL)

	var resBody []byte
	var resObj TriggerEvalResStruct

	sentinelError := func() error {
		tags := map[string]interface{}{
			"client": sourceEntityID,
			"userID": userID,
		}
		client := tracer.GetTraceableHTTPClientV2(nil, TriggerEvalServiceName, sourceEntityID, tags)
		payload := new(bytes.Buffer)
		encoder := json.NewEncoder(payload)
		encoder.SetEscapeHTML(false)

		if err := encoder.Encode(reqBody); err != nil {
			logger.WithUser(userID).Error(err)
			return err
		}
		req, err := requestutils.GetMockableHTTPRequest(userID, TriggerEvalServiceName, "POST", url, payload)
		if err != nil {
			logger.WithUser(userID).Error(err)
			return err
		}

		req.Header.Add("x-api-key", apiKey)
		req.Header.Add("Content-Type", "application/json")
		res, err := client.Do(req)
		if err != nil {
			logger.WithUser(userID).Error(err)
			return err
		}

		resObj, resBody, err = processTriggerEvalRes(res, userID)
		return err
	}()
	return sentinelError, string(resBody), url, resObj
}

// FetchEvaluation initiates an fetch evaluation request
func FetchEvaluation(ctx context.Context, userID, sourceEntityID, lenderID, evaluationID string, reqBody FetchEvalReqStruct, options FetchEvalOptions) (error, string, string, FetchEvaluationResStruct) {
	sentinelConf := conf.GetSentinelCreds(lenderID, sourceEntityID)
	baseURL := sentinelConf["baseURL"]
	apiKey := sentinelConf["apiKey"]

	url := fmt.Sprintf("%s/v1/sentinel/fetchEvaluation?reasonsOfRejection=true", baseURL)

	var resObj FetchEvaluationResStruct
	var resBody []byte

	currentTry := 0
	sentinelError := retry.CustomRetry(options.MaxTries, 3*time.Second, func() error {
		currentTry++
		log.Debugln("sentinel try -", currentTry)
		tags := map[string]interface{}{
			"client": sourceEntityID,
			"userID": userID,
		}
		client := tracer.GetTraceableHTTPClientV2(nil, FetchEvalServiceName, sourceEntityID, tags)
		payload := new(bytes.Buffer)
		if err := json.NewEncoder(payload).Encode(reqBody); err != nil {
			logger.WithUser(userID).Error(err)
			return err
		}
		req, err := requestutils.GetMockableHTTPRequest(userID, FetchEvalServiceName, "POST", url, payload)
		if err != nil {
			logger.WithUser(userID).Error(err)
			return err
		}

		req.Header.Add("x-api-key", apiKey)
		req.Header.Add("Content-Type", "application/json")
		res, err := client.Do(req)
		if err != nil {
			logger.WithUser(userID).Error(err)
			return err
		}

		resObj, resBody, err = processFetchEvalRes(ctx, res, userID, evaluationID)
		return err
	})
	return sentinelError, string(resBody), url, resObj
}

// FetchEndpointDetails facilitates details against provided endpoint
func FetchEndpointDetails(sourceEntityID, lenderID, endpoint string) (string, string, FetchEndpointDetailsResStruct, error) {
	sentinelConf := conf.GetSentinelCreds(lenderID, sourceEntityID)
	baseURL := sentinelConf["baseURL"]
	apiKey := sentinelConf["apiKey"]

	url := fmt.Sprintf("%s/v1/sentinel/endpoint/%s/active", baseURL, endpoint)

	var resBody []byte
	var resObj FetchEndpointDetailsResStruct

	sentinelError := func() error {
		tags := map[string]interface{}{
			"client": sourceEntityID,
		}
		client := tracer.GetTraceableHTTPClientV2(nil, "fetch_endpoint_details", sourceEntityID, tags)
		req, err := http.NewRequest("GET", url, nil)
		if err != nil {
			logger.Log.Errorln(err)
			return err
		}

		req.Header.Add("x-api-key", apiKey)
		req.Header.Add("Content-Type", "application/json")
		res, err := client.Do(req)
		if err != nil {
			logger.Log.Errorln(err)
			return err
		}

		resObj, resBody, err = processFetchEndpointDetailsRes(res)
		return err
	}()

	return string(resBody), url, resObj, sentinelError
}

// FetchEvaluationWithNoRetries initiates an fetch evaluation request
func FetchEvaluationWithNoRetries(userID, sourceEntityID, lenderID, evaluationID string, reqBody FetchEvalReqStruct) (error, string, string, FetchEvaluationResStruct) {
	sentinelConf := conf.GetSentinelCreds(lenderID, sourceEntityID)
	baseURL := sentinelConf["baseURL"]
	apiKey := sentinelConf["apiKey"]

	url := fmt.Sprintf("%s/v1/sentinel/fetchEvaluation?reasonsOfRejection=true", baseURL)

	var resObj FetchEvaluationResStruct
	var resBody []byte

	sentinelError := func() error {
		log.Debugln("sentinel eval debug ")
		client := tracer.GetTraceableHTTPClientV2(nil, FetchEvalServiceName, sourceEntityID)
		payload := new(bytes.Buffer)
		if err := json.NewEncoder(payload).Encode(reqBody); err != nil {
			logger.WithUser(userID).Error(err)
			return err
		}
		req, err := http.NewRequest("POST", url, payload)
		if err != nil {
			logger.WithUser(userID).Error(err)
			return err
		}

		req.Header.Add("x-api-key", apiKey)
		res, err := client.Do(req)
		if err != nil {
			logger.WithUser(userID).Error(err)
			return err
		}

		ctx := context.Background()
		resObj, resBody, err = processFetchEvalRes(ctx, res, userID, evaluationID)
		return err
	}()
	return sentinelError, string(resBody), url, resObj
}

func FetchEvaluationV2(userID, sourceEntityID, lenderID, evaluationID string, reqBody FetchEvalReqStruct) (error, string, string, FetchEvaluationResStructV2) {
	sentinelConf := conf.GetSentinelCreds(lenderID, sourceEntityID)
	baseURL := sentinelConf["baseURL"]
	apiKey := sentinelConf["apiKey"]

	url := fmt.Sprintf("%s/v1/sentinel/fetchEvaluation", baseURL)

	var resObj FetchEvaluationResStructV2
	var resBody []byte

	currentTry := 0
	sentinelError := retry.CustomRetry(maxTry, 3*time.Second, func() error {
		currentTry++
		log.Debugln("sentinel try -", currentTry)
		client := tracer.GetTraceableHTTPClientV2(nil, FetchEvalServiceName, sourceEntityID)
		payload := new(bytes.Buffer)
		if err := json.NewEncoder(payload).Encode(reqBody); err != nil {
			logger.WithUser(userID).Error(err)
			return err
		}
		req, err := http.NewRequest("POST", url, payload)
		if err != nil {
			logger.WithUser(userID).Error(err)
			return err
		}

		req.Header.Add("x-api-key", apiKey)
		res, err := client.Do(req)
		if err != nil {
			logger.WithUser(userID).Error(err)
			return err
		}

		resObj, resBody, err = processFetchEvalResV2(res, userID, evaluationID)
		return err
	})
	return sentinelError, string(resBody), url, resObj
}

func DownloadCAM(userID string, sourceEntityID string, lenderID, referenceID string) string {
	sentinelConf := conf.GetSentinelCreds(lenderID, sourceEntityID)
	baseURL := sentinelConf["baseURL"]
	apiKey := sentinelConf["apiKey"]
	url := fmt.Sprintf("%s/v2/sentinel/downloadCAM?referenceID=%s", baseURL, referenceID)
	var gobj = map[string]string{
		"url":     url,
		"strReq":  "",
		"strRes":  "",
		"success": "0",
		"userID":  userID,
		"id":      general.GetUUID(),
	}
	body := make([]byte, 0)
	err := retry.CustomRetry(10, 1000*time.Millisecond, func() error {
		body = make([]byte, 0)
		client := tracer.GetTraceableHTTPClientV2(nil, DownloadCamServiceName, sourceEntityID)
		req, err := requestutils.GetMockableHTTPRequest(userID, DownloadCamServiceName, "GET", url, nil)
		if err != nil {
			logger.WithUser(userID).Errorln(err)
			return err
		}
		req.Header.Add("x-api-key", apiKey)
		req.Header.Add("Content-Type", "application/json")
		res, err := client.Do(req)
		if err != nil {
			logger.WithUser(userID).Errorln(err)
			return err
		}
		defer res.Body.Close()
		if res.StatusCode != 200 {
			err = fmt.Errorf("status code %d not in 2xx series for user %s, service_name %s", res.StatusCode, userID, DownloadCamServiceName)
			logger.WithUser(userID).Error(err)
			return err
		}
		body, err = io.ReadAll(res.Body)
		if err != nil {
			logger.WithUser(userID).Errorln(err)
			return err
		}
		return nil
	})
	gobj["strRes"] = string(body)
	if err != nil {
		logger.WithUser(userID).Errorln(err)
		go serviceslib.WriteToDB(DownloadCamServiceName, gobj["strReq"], gobj["strRes"], 0, gobj["userID"], gobj["url"], err.Error(), gobj["id"])
		return ""
	}
	go serviceslib.WriteToDB(DownloadCamServiceName, gobj["strReq"], gobj["strRes"], 1, gobj["userID"], gobj["url"], "", gobj["id"])
	response := make(map[string]interface{})
	err = json.Unmarshal(body, &response)
	if err != nil {
		logger.WithUser(userID).Errorln(err)
	}
	if _, ok := response["data"]; !ok {
		logger.WithUser(userID).Errorln("data field not present in response: ", response)
		return ""
	}
	return response["data"].(string)
}

// FetchPredictorsByEvaluationID gets the predictors used for generating a user's initial offer
func FetchPredictorsByEvaluationID(userID, sourceEntityID, lenderID, evaluationID string) (FetchPredictorsStruct, error) {
	sentinelConf := conf.GetSentinelCreds(lenderID, sourceEntityID)
	baseURL := sentinelConf["baseURL"]
	apiKey := sentinelConf["apiKey"]

	url := fmt.Sprintf("%v/v1/sentinel/downloadEvalPreds", baseURL)

	var resObj FetchPredictorsStruct

	reqBody := struct {
		EvalEndpointID string `json:"evalEndpointID"`
	}{
		EvalEndpointID: evaluationID,
	}

	var gobj = map[string]string{
		"url":     url,
		"strReq":  "",
		"strRes":  "",
		"success": "0",
		"userID":  userID,
		"id":      general.GetUUID(),
	}

	sentinelError := func() error {
		client := tracer.GetTraceableHTTPClientV2(nil, FetchPredictorServiceName, sourceEntityID)
		payload, err := json.Marshal(reqBody)
		if err != nil {
			logger.WithUser(userID).Error(err)
			return err
		}
		gobj["strReq"] = string(payload)

		log.Debug("Request Body: ", string(payload))

		req, err := requestutils.GetMockableHTTPRequest(userID, FetchPredictorServiceName, "POST", url, bytes.NewBuffer(payload))
		if err != nil {
			logger.WithUser(userID).Error(err)
			return err
		}
		log.Debug("Request Obj: ", req)

		req.Header.Add("x-api-key", apiKey)
		req.Header.Add("Content-Type", "application/json")
		res, err := client.Do(req)
		if err != nil {
			logger.WithUser(userID).Error(err)
			go serviceslib.WriteToDB(FetchEvalServiceName, gobj["strReq"], gobj["strRes"], 0, gobj["userID"], gobj["url"], err.Error(), gobj["id"])
			return err
		}
		log.Debug("Response: ", res)
		defer res.Body.Close()
		if err = json.NewDecoder(res.Body).Decode(&resObj); err != nil {
			logger.WithUser(userID).Error(err)
			go serviceslib.WriteToDB(FetchEvalServiceName, gobj["strReq"], gobj["strRes"], 0, gobj["userID"], gobj["url"], err.Error(), gobj["id"])
			return err
		}
		resObjStr, err := json.Marshal(resObj)
		if err == nil {
			go serviceslib.WriteToDB(FetchEvalServiceName, gobj["strReq"], string(resObjStr), 1, gobj["userID"], gobj["url"], "", gobj["id"])
		}
		return nil
	}()

	return resObj, sentinelError
}

// DebugAPI generates an offer based on the custom predictors passed to the api
func DebugAPI(userID, uniqueID, sourceEntityID, lenderID, evaluationCode string, predictors PredictorList) (DebugEvaluationRes, error) {
	sentinelConf := conf.GetSentinelCreds(lenderID, sourceEntityID)
	baseURL := sentinelConf["baseURL"]
	apiKey := sentinelConf["apiKey"]

	url := fmt.Sprintf("%v/v1/sentinel/debugEndpoint", baseURL)

	var resObj DebugEvaluationRes

	reqBody := struct {
		UserID         string        `json:"userID"`
		EvaluationCode string        `json:"endpointCode"`
		Variables      PredictorList `json:"variables"`
	}{
		UserID:         uniqueID,
		EvaluationCode: evaluationCode,
		Variables:      predictors,
	}

	var gobj = map[string]string{
		"url":     url,
		"strReq":  "",
		"strRes":  "",
		"success": "0",
		"userID":  userID,
		"id":      general.GetUUID(),
	}

	sentinelError := func() error {
		client := tracer.GetTraceableHTTPClientV2(nil, FetchPredictorServiceName, sourceEntityID)
		payload, err := json.Marshal(reqBody)
		if err != nil {
			logger.WithUser(userID).Error(err)
			return err
		}
		gobj["strReq"] = string(payload)

		log.Debug("Request Body: ", string(payload))

		req, err := requestutils.GetMockableHTTPRequest(userID, FetchPredictorServiceName, "POST", url, bytes.NewBuffer(payload))
		if err != nil {
			logger.WithUser(userID).Error(err)
			return err
		}
		log.Debug("Request Obj: ", req)

		req.Header.Add("x-api-key", apiKey)
		req.Header.Add("Content-Type", "application/json")
		res, err := client.Do(req)
		if err != nil {
			logger.WithUser(userID).Error(err)
			go serviceslib.WriteToDB(DebugEvaluationServiceName, gobj["strReq"], gobj["strRes"], 0, gobj["userID"], gobj["url"], err.Error(), gobj["id"])
			return err
		}

		defer res.Body.Close()
		if err = json.NewDecoder(res.Body).Decode(&resObj); err != nil {
			logger.WithUser(userID).Error(err)
			go serviceslib.WriteToDB(DebugEvaluationServiceName, gobj["strReq"], gobj["strRes"], 0, gobj["userID"], gobj["url"], err.Error(), gobj["id"])
			return err
		}

		resObjStr, err := json.Marshal(resObj)
		if err == nil {
			go serviceslib.WriteToDB(DebugEvaluationServiceName, gobj["strReq"], string(resObjStr), 1, gobj["userID"], gobj["url"], "", gobj["id"])
		}
		log.Debug("Response: ", string(resObjStr))
		return nil
	}()
	return resObj, sentinelError
}

func GetOfferRankingPayload(ctx context.Context, offersData RankingOffersData) (map[string]interface{}, string, error) {
	var (
		sentinelAdditional map[string]interface{}
		source             string
		err                error
	)
	switch offersData.SourceEntityID {
	case constants.MoneyControlID, constants.ABCDMarketPlaceID:
		var payload OfferRankingConfig

		if offersData.Offers == nil {
			err := errors.New("unable to get ranking payload, err: invalid offers")
			logger.WithUser(offersData.UserID).Error(err)
			return sentinelAdditional, source, err
		}

		for _, offerItr := range *offersData.Offers {

			offer := offerItr

			if json.Valid([]byte(offer.OfferMetadata)) {
				err := json.Unmarshal([]byte(offer.OfferMetadata), &offer.OfferMetadataObj)
				if err != nil {
					logger.WithUser(offersData.UserID).Error(err)
					return sentinelAdditional, source, err
				}
			}

			switch offer.LenderID {
			case constants.LandTID:
				payload.LtfsAmount = offer.Amount
				payload.LtfsRoi = offer.Interest
				payload.LtfsTenure = offer.Tenure
				payload.LtfsIsPreapproved = offer.OfferMetadataObj.IsPreApproved
				payload.LtfsBankStatementMandatory = false
			case constants.FibeID:
				payload.FibeAmount = offer.Amount
				payload.FibeRoi = offer.Interest
				payload.FibeTenure = offer.Tenure
				payload.FibeIsPreapproved = offer.OfferMetadataObj.IsPreApproved
				payload.FibeBankStatementMandatory = false
			case constants.NiroID:
				payload.NiroAmount = offer.Amount
				payload.NiroRoi = offer.Interest
				payload.NiroTenure = offer.Tenure
				payload.NiroIsPreapproved = offer.OfferMetadataObj.IsPreApproved
				payload.NiroBankStatementMandatory = false
			case constants.ABFLPLID:
				payload.AbflAmount = offer.Amount
				payload.AbflRoi = offer.Interest
				payload.AbflTenure = offer.Tenure
				payload.AbflIsPreapproved = offer.OfferMetadataObj.IsPreApproved
				payload.AbflBankStatementMandatory = false
			case constants.CasheMCID:
				payload.CasheAmount = offer.Amount
				payload.CasheRoi = offer.Interest
				payload.CasheTenure = offer.Tenure
				payload.CasheIsPreapproved = offer.OfferMetadataObj.IsPreApproved
				payload.CasheBankStatementMandatory = false
			case constants.PrefrMCID:
				payload.PrefrAmount = offer.Amount
				payload.PrefrRoi = offer.Interest
				payload.PrefrTenure = offer.Tenure
				payload.PrefrIsPreapproved = offer.OfferMetadataObj.IsPreApproved
				payload.PrefrBankStatementMandatory = false
			case constants.RingMCID:
				payload.RingAmount = offer.Amount
				payload.RingRoi = offer.Interest
				payload.RingTenure = offer.Tenure
				payload.RingIsPreapproved = offer.OfferMetadataObj.IsPreApproved
				payload.RingBankStatementMandatory = false
			case constants.BajajID:
				payload.BajajAmount = offer.Amount
				payload.BajajRoi = offer.Interest
				payload.BajajTenure = offer.Tenure
				payload.BajajIsPreapproved = offer.OfferMetadataObj.IsPreApproved
				payload.BajajBankStatementMandatory = false
			case constants.IncredApiStackID:
				payload.IncredAmount = offer.Amount
				payload.IncredRoi = offer.Interest
				payload.IncredTenure = offer.Tenure
				payload.IncredIsPreapproved = offer.OfferMetadataObj.IsPreApproved
				payload.IncredBankStatementMandatory = false
			case constants.ABCDPFLID:
				payload.ABCDPFLAmount = offer.Amount
				payload.ABCDPFLRoi = offer.Interest
				payload.ABCDPFLTenure = offer.Tenure
				payload.ABCDPFLIsPreapproved = offer.OfferMetadataObj.IsPreApproved
				payload.ABCDPFLBankStatementMandatory = false
			case constants.MCPFLID:
				payload.PFLAmount = offer.Amount
				payload.PFLRoi = offer.Interest
				payload.PFLTenure = offer.Tenure
				payload.PFLIsPreapproved = offer.OfferMetadataObj.IsPreApproved
				payload.PFLBankStatementMandatory = false
			case constants.MCKreditbeeID:
				payload.KBAmount = offer.Amount
				payload.KBRoi = offer.Interest
				payload.KBTenure = offer.Tenure
				payload.KBIsPreapproved = offer.OfferMetadataObj.IsPreApproved
				payload.KBBankStatementMandatory = false
			}
		}

		b, err := json.Marshal(&payload)
		if err != nil {
			logger.WithUser(offersData.UserID).Error(err)
			return sentinelAdditional, source, err
		}

		err = json.Unmarshal(b, &sentinelAdditional)
		if err != nil {
			logger.WithUser(offersData.UserID).Error(err)
			return sentinelAdditional, source, err
		}

		source = "Test2"
		return sentinelAdditional, source, err

	default:
		err = errors.New("ranking payload config not available")
		logger.WithUser(offersData.UserID).Error(err)
		return sentinelAdditional, source, err
	}
}

func SetOffersRanking(ctx context.Context, offersData RankingOffersData) error {

	var (
		err error
	)

	type rankedLenderStruct struct {
		rank int
		tag  string
	}

	switch offersData.SourceEntityID {
	case constants.MoneyControlID, constants.ABCDMarketPlaceID:

		if len(offersData.SentinelResponse.Data.Models) > 0 && strings.ToLower(offersData.SentinelResponse.Data.Models[0].Name) == "ranking" {
			var rankingData OfferRankingResp
			err = general.DecodeToStruct(offersData.SentinelResponse.Data.Models[0].Output, &rankingData)
			if err != nil {
				logger.WithUser(offersData.UserID).Error(err)
				return err
			}

			rankedLenders := make(map[string]rankedLenderStruct)

			for _, lenders := range rankingData.Result {

				var lenderID string

				switch offersData.SourceEntityID {
				case constants.MoneyControlID:
					lenderID = mcSentinelRankedLenderNamesMap[strings.ToLower(lenders.Name)]
				case constants.ABCDMarketPlaceID:
					lenderID = abcdSentinelRankedLenderNamesMap[strings.ToLower(lenders.Name)]
				default:
					err = errors.New("source entity not supported for ranking")
					logger.WithUser(offersData.UserID).Error(err)
					return err
				}

				rankedLenders[lenderID] = rankedLenderStruct{
					rank: lenders.Rank,
					tag:  lenders.Tag,
				}
			}

			for _, offerItr := range *offersData.Offers {

				offer := offerItr

				if json.Valid([]byte(offer.OfferMetadata)) {
					err := json.Unmarshal([]byte(offer.OfferMetadata), &offer.OfferMetadataObj)
					if err != nil {
						logger.WithUser(offersData.UserID).Error(err)
						continue
					}
				}

				rankValue := rankedLenders[offer.LenderID].rank

				offer.OfferMetadataObj.CardDetails.Rank = &rankValue
				offer.OfferMetadataObj.CardDetails.Tag = rankedLenders[offer.LenderID].tag

				offerMetadataBytes, err := json.Marshal(offer.OfferMetadataObj)
				if err != nil {
					logger.WithUser(offersData.UserID).Error(err)
				}

				err = personalloanoffer.UpdateOfferMetadata(nil, offer.LoanOfferID, string(offerMetadataBytes))
				if err != nil {
					logger.WithUser(offersData.UserID).Error(err)
				}
			}

		} else {
			err = errors.New("ranking data not found")
			logger.WithUser(offersData.UserID).Error(err)
			return err
		}

	}

	return nil

}
