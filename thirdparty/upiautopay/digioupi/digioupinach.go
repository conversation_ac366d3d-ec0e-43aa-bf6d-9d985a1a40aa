// Package digioupi deals with upi autopay integration section
package digioupi

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"finbox/go-api/common/usersutil"
	"finbox/go-api/conf"
	"finbox/go-api/constants"
	"finbox/go-api/errorHandler"
	"finbox/go-api/functionalityModels"
	"finbox/go-api/functions/activity"
	finboxEvents "finbox/go-api/functions/activity"
	"finbox/go-api/functions/journey"
	"finbox/go-api/functions/logger"
	"finbox/go-api/functions/requestutils"
	"finbox/go-api/functions/retry"
	"finbox/go-api/functions/services/digio"
	"finbox/go-api/functions/serviceslib"
	"finbox/go-api/functions/tracer"
	"finbox/go-api/infra/db"
	"finbox/go-api/infra/temporalclient"
	"finbox/go-api/models/temporalsignallogging"
	"finbox/go-api/models/upiautopay"
	"finbox/go-api/models/usermodulemapping"
	"finbox/go-api/models/userworkflows"
	"finbox/go-api/utils/calc"
	"finbox/go-api/utils/general"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/jmoiron/sqlx"
	"github.com/sirupsen/logrus"
)

var (
	MaxRetry = 3
	log      = logger.Log
	database = db.GetDB()
)

// CreateMandate takes in userID and loanApplicationID and creates new mandate, return type is of object UPIMandateCreateResult
func CreateMandate(userID, loanApplicationID string) (*UPIMandateCreateResult, error) {
	resourceName := "digio_create_upimand"

	var (
		upirequstobj  CreateUPIMandateRequest
		mflUPIReqObj  MFLBLUPIMandateRequest
		upirespobj    CreateMandateResponse
		dbObj         CreateMandatedbStruct
		finalresponse UPIMandateCreateResult
	)
	var gobj = map[string]string{
		"url":    "",
		"strReq": "",
		"strRes": "",
		"userID": "",
		"id":     "",
	}

	baseURL := conf.DigioUPIconf["baseURL"]
	createMandateURL := "/v3/client/mandate/create_form"
	url := baseURL + createMandateURL
	token := "Basic " + conf.DigioUPIconf["token"]

	query := `select 
				u.user_id as userid, 
				u.name, 
				coalesce(u.email, '') as email, 
				u.mobile, 
				coalesce(ubd.account_number, '') as accountnumber, 
				coalesce(ubd.ifsc_code, '') as ifsc, 
				coalesce(
				ubd.account_type, $3
				) as accounttype, 
				coalesce(ubd.name, '') as accountholdername, 
				la.loan_application_no as loanapplicationno, 
				coalesce(la.tenure, 0) as tenure, 
				coalesce(la.interest, 0) as interest, 
				coalesce(lo.method, 'rb') as method, 
				coalesce(la.amount, 0) as amount, 
				la.source_entity_id as sourceentityid, 
				coalesce(la.lender_id :: TEXT, '') as lenderid 
			from 
				loan_application la 
				join users u on la.user_id = u.user_id 
				left join user_bank_details ubd on ubd.user_bank_details_id = la.user_bank_details_id 
				and ubd.status = $2 
				left join loan_offer lo on la.loan_offer_id = lo.loan_offer_id 
			where 
				la.loan_application_id = $1`

	err := database.Get(&dbObj, query, loanApplicationID, constants.UserBankStatusApproved, constants.BankAccountTypeSavings)
	if err != nil {
		return &finalresponse, err
	}

	if dbObj.Amount == 0 {
		return &finalresponse, errors.New("application is not approved by lender yet")
	}
	if (dbObj.LenderID == constants.IIFLID && !strings.HasPrefix(dbObj.LoanApplicationNo, "SL") && conf.ENV == conf.ENV_PROD) && !journey.PushToLenderOnNACH(dbObj.SourceEntityID, dbObj.LenderID) {
		return &finalresponse, errors.New("prospect not created in lender system yet")
	}

	if dbObj.AccountHolderName == "" {
		// if account holder name not present set it as applicant name
		dbObj.AccountHolderName = dbObj.Name
	}
	// keep only alphanumeric
	dbObj.AccountHolderName = general.GetOnlyAlphaSpace(dbObj.AccountHolderName)

	if len(dbObj.AccountHolderName) > 40 {
		// take first 40 characters
		dbObj.AccountHolderName = dbObj.AccountHolderName[0:40]
	}
	if dbObj.AccountType == "" {
		dbObj.AccountType = constants.BankAccountTypeSavings
	}
	emi, _, _ := calc.GetEMI(dbObj.Method, dbObj.Amount, dbObj.Tenure, dbObj.Interest, time.Now(), dbObj.SourceEntityID, dbObj.LenderID, dbObj.UserID)
	mandateAmount := calc.GetMandateAmount(dbObj.SourceEntityID, dbObj.Amount, emi, dbObj.LenderID)
	firstCollectionDate := time.Now().Format(constants.DateFormat)

	// set now + 99 years as last collection date
	lastCollectionDate := time.Now().AddDate(50, 0, 0).Format(constants.DBTimeFormat)

	configID := conf.GetDigioUPIConfigID(dbObj.LenderID)
	if configID == "" {
		return &finalresponse, errors.New("NACH is not configure for lender")
	}

	var requestparam = map[string]interface{}{
		"firstCollectionDate": firstCollectionDate,
		"lastCollectionDate":  lastCollectionDate,
		"configID":            configID,
		"mandateAmount":       mandateAmount,
	}

	// init request body
	var reqbytes []byte
	if journey.IsMFLBLSourcing(dbObj.SourceEntityID) {
		createMandateURL = "/v3/client/mandate/upi/create_form"
		url = baseURL + createMandateURL
		token = "Basic " + conf.MFLBLDigioUPICreds["token"]
		requestparam["lastCollectionDate"] = time.Now().AddDate(0, 0, dbObj.Tenure+365).Format(constants.DBTimeFormat)
		mflUPIReqObj = MFLBLMandaterequestinit(&mflUPIReqObj, &dbObj, requestparam)
		reqbytes, err = json.Marshal(&mflUPIReqObj)
	} else {
		upirequstobj = Mandaterequestinit(&upirequstobj, &dbObj, requestparam)
		reqbytes, err = json.Marshal(&upirequstobj)
	}

	if err != nil {
		logger.WithUser(userID).Error(err)
		return &finalresponse, err
	}

	gobj["strReq"] = string(reqbytes)
	gobj["url"] = url
	gobj["id"] = general.GetUUID()
	gobj["userID"] = userID

	payload := bytes.NewReader(reqbytes)

	err = retry.CustomRetry(MaxRetry, 10*time.Second, func() error {
		req, err := requestutils.GetMockableHTTPRequest(userID, resourceName, http.MethodPost, url, payload)
		if err != nil {
			logger.WithUser(userID).Println(err)
			return err
		}

		req.Header.Add("Authorization", token)
		req.Header.Add("Content-Type", "application/json")

		client := tracer.GetTraceableHTTPClient(nil, resourceName)

		resp, err := client.Do(req)
		if err != nil {
			logger.WithUser(userID).Println(err)
			return err
		}
		defer resp.Body.Close()

		respbody, err := io.ReadAll(resp.Body)

		if err != nil {
			logger.WithUser(userID).Println(err)
			return err
		}

		gobj["strRes"] = string(respbody)
		// check if response is successful
		err = json.Unmarshal(respbody, &upirespobj)
		if err != nil {
			logger.WithUser(userID).Println(err)
			return err
		}
		if upirespobj.ID == "" {
			log.Printf("UPI mandate not created for customer ID: %v", userID)
			return errors.New("UPI mandate not created for customer")
		} else {
			log.Printf("UPI mandate successfully created for customer ID: %v", userID)

		}
		return nil
	})

	if err != nil {
		serviceslib.WriteToDB(resourceName, gobj["strReq"], gobj["strRes"], 0, gobj["userID"], gobj["url"], err.Error(), gobj["id"])
		return &finalresponse, err
	}

	tx, dberr := database.Begin()
	if dberr != nil {
		logger.WithUser(dbObj.UserID).Println(err)
		return &finalresponse, dberr
	}

	entryObj := upiautopay.CreateUPIEntryStruct{
		MandateID:          upirespobj.ID,
		UserID:             dbObj.UserID,
		LoanApplicationID:  loanApplicationID,
		MandateAmount:      mandateAmount,
		AccountHolderName:  dbObj.AccountHolderName,
		AccountType:        dbObj.AccountType,
		CustomerIdentifier: dbObj.Mobile,
	}

	finalresponse.MandateID = upirespobj.ID
	finalresponse.CustomerIdentifier = dbObj.Mobile
	finalresponse.UserID = dbObj.UserID

	err = upiautopay.Create(entryObj, tx)
	if err != nil {
		serviceslib.WriteToDB(resourceName, gobj["strReq"], gobj["strRes"], 0, gobj["userID"], gobj["url"], err.Error(), gobj["id"])
		return &finalresponse, err
	}
	tx.Commit()

	serviceslib.WriteToDB(resourceName, gobj["strReq"], gobj["strRes"], 1, gobj["userID"], gobj["url"], "", gobj["id"])

	return &finalresponse, nil
}

func CancelUPIMandate(loanApplicationID string) error {

	resourceName := "digio_cancel_upimand"
	var dbObj []CancelMandatedbStruct
	var cancelmandateObj CancelMandateRequest
	var upirespobj CancelMandateResponse
	var gobj = map[string]string{
		"url":    "",
		"strReq": "",
		"strRes": "",
		"userID": "",
		"id":     "",
	}

	query := `SELECT 
				mandate_id as mandateid,  
				user_id as userid 
			from 
				upi_autopay 
			where 
				loan_application_id = $1 
				and status in ($2, $3)`
	err := database.Get(&dbObj, query, loanApplicationID, upiautopay.ThirdPartyAuthSuccess, upiautopay.ThirdPartyCompleted)
	if err != nil {
		logger.WithLoanApplication(loanApplicationID).Println(err)
		return err
	}

	// one loan application can have multiple entries in some edge cases (rare),
	// so fetch all mandate ids at once iterate over loop  and do rest of steps.
	for _, obj := range dbObj {

		cancelmandateObj.DigioMandateID = obj.MandateID

		reqbytes, err := json.Marshal(cancelmandateObj)
		if err != nil {
			logger.WithUser(obj.UserID).Error(err)
			return err
		}

		payload := bytes.NewReader(reqbytes)

		baseURL := conf.DigioUPIconf["baseURL"]
		cancelMandateURL := "/v3/client/mandate/upi/registration_cancel"
		url := baseURL + cancelMandateURL
		token := "Basic " + conf.DigioUPIconf["token"]

		gobj["strReq"] = string(reqbytes)
		gobj["url"] = url
		gobj["id"] = general.GetUUID()
		gobj["userID"] = obj.UserID

		err = retry.CustomRetry(MaxRetry, 10*time.Second, func() error {
			req, err := requestutils.GetMockableHTTPRequest(obj.UserID, resourceName, http.MethodPost, url, payload)
			if err != nil {
				logger.WithUser(obj.UserID).Println(err)
				return err
			}

			req.Header.Add("Authorization", token)
			req.Header.Add("Content-Type", "application/json")

			client := tracer.GetTraceableHTTPClient(nil, resourceName)

			resp, err := client.Do(req)
			if err != nil {
				logger.WithUser(obj.UserID).Println(err)
				serviceslib.WriteToDB(resourceName, gobj["strReq"], gobj["strRes"], 0, gobj["userID"], gobj["url"], err.Error(), gobj["id"])
				return err
			}
			defer resp.Body.Close()

			respbody, err := io.ReadAll(resp.Body)
			log.Debug("---------------------------------")
			log.Println(string(respbody))

			if err != nil {
				logger.WithUser(obj.UserID).Println(err)
				serviceslib.WriteToDB(resourceName, gobj["strReq"], gobj["strRes"], 0, gobj["userID"], gobj["url"], err.Error(), gobj["id"])
				return err
			}
			gobj["strRes"] = string(respbody)
			// check if response is successful
			err = json.Unmarshal(respbody, &upirespobj)
			if err != nil {
				logger.WithUser(obj.UserID).Println(err)
				serviceslib.WriteToDB(resourceName, gobj["strReq"], gobj["strRes"], 0, gobj["userID"], gobj["url"], err.Error(), gobj["id"])
				return err
			}
			if upirespobj.ID == "" {
				log.Printf("UPI mandate not cancelled for customer ID: %v", obj.UserID)
				serviceslib.WriteToDB(resourceName, gobj["strReq"], gobj["strRes"], 0, gobj["userID"], gobj["url"], err.Error(), gobj["id"])
				return errors.New("UPI mandate not cancelled for customer")
			} else {
				log.Printf("UPI mandate successfully cancelled for customer ID: %v", obj.UserID)
			}
			return nil
		})

		if err != nil {
			logger.WithLoanApplication(loanApplicationID).Println(err)
			return err
		}
		tx, dberr := database.Begin()
		if dberr != nil {
			logger.WithUser(obj.UserID).Println(err)
			return dberr
		}
		updatestructObj := upiautopay.UpdateUPIAutopayStruct{
			Status:    upiautopay.ThirdPartyCancelled,
			MandateID: obj.MandateID,
		}
		err = upiautopay.UpdateDetailsStatus(updatestructObj, tx)

		if err != nil {
			return err
		}
		tx.Commit()
		serviceslib.WriteToDB(resourceName, gobj["strReq"], gobj["strRes"], 1, gobj["userID"], gobj["url"], err.Error(), gobj["id"])

	}
	return nil
}

func VerifyAndSaveNACHAccountInfo(respObj UPIWebhookresp, userID, sourceEntityID string) (string, error) {

	var GetEnachDetails struct {
		Status        int    `db:"status"`
		AccountNumber string `db:"accountnumber"`
	}

	var ismatched bool
	var errorstring string
	var dbobj upiautopay.SelectUPIAutopayRespStruct
	accobj := GetEnachDetails

	dbobj, err := upiautopay.GetByMandateID(respObj.Payload.UpiMandates.MandateID)

	if err != nil {
		logger.WithUser(userID).Println(err)
		return "", err
	}

	tx, dberr := database.Begin()
	if dberr != nil {
		logger.WithUser(userID).Println(err)
		return "Some issue fetching DB connection", dberr
	}

	if respObj.Payload.UpiMandates.DebitAccount != "" && respObj.Event == "upi.mndt.authsuccess" {

		query := `SELECT status, coalesce(account_number, '') as accountnumber from user_bank_details where user_id = $1 
					and status = $2 order by created_at desc limit 1`
		err := database.Get(&accobj, query, userID, constants.UserBankStatusApproved)
		if err != nil {
			logger.WithUser(userID).Println(err)
			return "", err
		}
		ismatched = general.MatchAccountNumber(accobj.AccountNumber, respObj.Payload.UpiMandates.DebitAccount)

		if dbobj.AccountNumber == "" {
			dbobj.AccountNumber = respObj.Payload.UpiMandates.DebitAccount
		}

		if !ismatched {

			defer tx.Rollback()
			updatestructObj := upiautopay.UpdateUPIAutopayStruct{
				Status:    upiautopay.ThirdPartyAccountMissmatch,
				MandateID: respObj.Payload.UpiMandates.MandateID,
			}
			// mark upimandate as failed
			err := upiautopay.UpdateDetailsStatus(updatestructObj, tx)
			if err != nil {
				logger.WithUser(userID).Println(err)
			}
			query = `update user_module_mapping set module_status = $3, updated_at = NOW() where user_id = $1 and module_name = $2`
			_, err = tx.Exec(query, userID, "ENACH", constants.UserModuleStatusFailed)
			if err != nil {
				logger.WithUser(userID).Println(err)
				return "Some issue updating user_module_mapping", err
			}
			err = tx.Commit()
			if err != nil {
				logger.WithUser(userID).Println(err)
			}
			errorstring = "UPI Account does not match with user Account"
		}
	}

	txn, _ := database.Begin()

	mandateid := respObj.Payload.UpiMandates.MandateID
	if mandateid != "" {
		updatestructObj := upiautopay.UpdateUPIAutopayStruct{
			AccountNo: dbobj.AccountNumber,
			IFSC:      respObj.Payload.UpiMandates.DebitIfsc,
			UMRN:      respObj.Payload.UpiMandates.Umrn,
			MandateID: respObj.Payload.UpiMandates.MandateID,
			VPA:       respObj.Payload.UpiMandates.CustomerVpa,
		}

		err := upiautopay.UpdateDetailsOnWebhook(updatestructObj, txn)

		if err != nil {
			logger.WithUser(userID).Println(err)
			return "", err
		}
		txn.Commit()
	}

	if respObj.Payload.UpiMandates.Umrn != "" {
		if journey.IsTemporalFlow(userID, sourceEntityID, usermodulemapping.ENACH) {
			wf, err := userworkflows.Get(userID, usermodulemapping.ENACH)
			if err != nil {
				err = fmt.Errorf("[VerifyAndSaveNACHAccountInfo] error getting userworkflows for userID: %s, moduleName: %s, signalName: %s, err: %s", userID, usermodulemapping.ENACH, "upi_enach_success_webhook", err.Error())
				logger.WithUser(userID).Errorln(err)
				return err.Error(), err
			}

			if wf.IsTerminalStatus() {
				err = fmt.Errorf("invalid workflow status, workflowID: %s, runID: %s, moduleName: %s, userID: %s",
					wf.WorkflowID, wf.RunID, usermodulemapping.ENACH, userID)
				logger.WithUser(userID).Errorln(err)
				return err.Error(), err
			}

			signalName := "upi_enach_success_webhook"
			err = temporalclient.Client.SignalWorkflow(context.Background(), wf.WorkflowID, wf.RunID, signalName, nil)
			if err != nil {
				logger.WithUser(userID).Errorln(err)
				err = fmt.Errorf("unable to send signal for user %s, err : %s", userID, err)
				errorHandler.ReportToSentryWithFields(errorHandler.ErrorFields{"userID": userID, "workflowID": wf.WorkflowID}, err)
				return err.Error(), err
			}
			go temporalsignallogging.Insert(signalName, userID, wf.WorkflowID, nil)
		}
	}

	return errorstring, nil
}

// GetUPIMandateDetails creates url for the customer to approve the mandate
func GetUPIMandateDetails(userID, loanApplicationID string, isIFrame bool) (*digio.MandateResult, error) {
	createmandateObj, err := CreateMandate(userID, loanApplicationID)
	var UPImandateObj digio.MandateResult
	var mandateURL string
	if err != nil {
		logger.WithUser(userID).Println(err)
		return &UPImandateObj, err
	}

	tx, dberr := database.Begin()
	if dberr != nil {
		logger.WithUser(userID).Println(err)
		return &UPImandateObj, dberr
	}

	txnID := general.GenerateRandomString(8)
	redirectURL := conf.BaseURL + "/v1/loan/digioUPImandateCallback"
	if isIFrame {
		redirectURL = conf.BaseURL + "/v1/loan/iframeUPImandateCallback"
	}

	//mflbl has the same redirect URL so not adding any extra conditions
	UPIAutoPaybaseURL := strings.Replace(conf.DigioUPIconf["redirectbaseURL"], ":444", "", 1) + conf.DigioUPIconf["extensionURL"]

	mandateURL = fmt.Sprintf(`%s/%s/%s/%s?redirect_url=%s`, UPIAutoPaybaseURL, createmandateObj.MandateID, txnID, createmandateObj.CustomerIdentifier, redirectURL)

	UPImandateObj.AuthLink = mandateURL
	UPImandateObj.MandateID = createmandateObj.MandateID

	updatestructObj := upiautopay.UpdateUPIAutopayStruct{
		UPIAutoPayURL: mandateURL,
		Status:        upiautopay.ThirdPartyInitiated,
		MandateID:     createmandateObj.MandateID,
	}
	// update upi_autopay table
	err = upiautopay.UpdateDetailsOnURLCreation(updatestructObj, tx)
	if err != nil {
		logger.WithUser(userID).Errorln(err)
		return nil, err
	}
	tx.Commit()

	return &UPImandateObj, nil

}

func GetUPIAutopayDetails(userID, mandateID, lenderID string) (*UPIAutoPayDetailsResp, error) {
	token := conf.DigioUPIconf["token"]
	url := conf.DigioUPIconf["baseURL"] + "/v3/client/mandate/upi/" + mandateID
	if lenderID == constants.MFLBLID {
		token = conf.MFLBLDigioUPICreds["token"]
		url = conf.MFLBLDigioUPICreds["baseURL"] + "/v3/client/mandate/upi/" + mandateID
	}

	method := "GET"

	var gobj = map[string]string{
		"url":    url,
		"strRes": "",
		"userID": userID,
		"id":     general.GetUUID(),
	}

	client := tracer.GetTraceableHTTPClient(nil, GetDigioUPIAutopayDetailsService)
	req, err := http.NewRequest(method, url, nil)
	if err != nil {
		logger.WithUser(userID).Errorln(err)
		return nil, err
	}

	req.Header.Add("Authorization", "Basic "+token)
	req.Header.Add("Content-Type", "application/json")

	res, err := client.Do(req)
	if err != nil {
		logger.WithUser(userID).Errorln(err)
		return nil, err
	}

	defer res.Body.Close()

	body, err := io.ReadAll(res.Body)
	if err != nil {
		serviceslib.WriteToDB(GetDigioUPIAutopayDetailsService, gobj["strReq"], gobj["strRes"], serviceslib.ErrorStatusCode, gobj["userID"], gobj["url"], err.Error(), gobj["id"])
		return nil, err
	}

	gobj["strRes"] = string(body)

	if res.StatusCode < 200 || res.StatusCode >= 300 {
		errDesc := fmt.Sprintf("status code: %d", res.StatusCode)
		serviceslib.WriteToDB(GetDigioUPIAutopayDetailsService, gobj["strReq"], gobj["strRes"], serviceslib.ErrorStatusCode, gobj["userID"], gobj["url"], errDesc, gobj["id"])
		return nil, errors.New(errDesc)
	}

	var resp UPIAutoPayDetailsResp

	err = json.Unmarshal(body, &resp)
	if err != nil {
		serviceslib.WriteToDB(GetDigioUPIAutopayDetailsService, gobj["strReq"], gobj["strRes"], serviceslib.ErrorStatusCode, gobj["userID"], gobj["url"], err.Error(), gobj["id"])
		return nil, err
	}

	serviceslib.WriteToDB(GetDigioUPIAutopayDetailsService, gobj["strReq"], gobj["strRes"], serviceslib.SuccessStatusCode, gobj["userID"], gobj["url"], "", gobj["id"])
	return &resp, nil
}

// FetchAndUpdateUPIUMRN fetches the UPI mandate details and updates the UPI autopay record with the UMRN
func FetchAndUpdateUPIUMRN(tx *sqlx.Tx, mandateID string) (err error) {
	autopayDetails, err := functionalityModels.GetDataForUMRNFetch(mandateID)
	if err != nil {
		return err
	}
	// check if umrn is already marked and status is completed or status is rejected or status is auth fail
	upiCurrentData, err := upiautopay.GetByMandateID(mandateID)
	if err != nil {
		logger.WithUser(autopayDetails.UserID).Infoln("umrn already updated")
		return err
	}

	if upiCurrentData.UMRN != "" {
		logger.WithUser(autopayDetails.UserID).Infoln("umrn already updated")
		return nil
	}

	if upiCurrentData.Status != constants.ENACHThirdPartyAuthSuccess {
		logger.WithUser(autopayDetails.UserID).Infoln("enach is not in auth success state, mandateID: ", mandateID)
		return nil
	}

	autopayDetailsResp, err := GetUPIAutopayDetails(autopayDetails.UserID, mandateID, autopayDetails.LenderID)
	if err != nil {
		logger.WithUser(autopayDetails.UserID).Error("fetching from digio-upi-autopay details api failed")
		return err
	}

	var (
		desc      string
		eventType string
	)

	if tx == nil {
		tx, err = database.Beginx()
		if err != nil {
			logger.WithUser(autopayDetails.UserID).Errorln(err)
			return err
		}
	}
	defer tx.Rollback()

	if autopayDetailsResp.UMRN != "" && strings.EqualFold(autopayDetailsResp.Status, "register_success") {
		status := constants.ENACHThirdPartyCompleted
		if err = upiautopay.UpdateByMandateID(context.TODO(), tx, upiautopay.UPIAutoPayStruct{
			MandateID:     mandateID,
			UMRN:          autopayDetailsResp.UMRN,
			Status:        &status,
			VPA:           autopayDetailsResp.CustomerVPA,
			AccountNumber: autopayDetailsResp.DebitAccount,
			IFSC:          autopayDetailsResp.DebitIFSC,
		}); err != nil {
			logger.WithUser(autopayDetails.UserID).Error(err)
			return err
		}

		//removing wait state for user and marking ENACH module success
		if err = handlePostAutoPaySuccess(tx, autopayDetails); err != nil {
			logger.WithUser(autopayDetails.UserID).Error(err)
			return err
		}
		eventType = constants.ActivityUPIAutoPayCompleted
		desc = "upi autopay umrn fetch success"
	} else {
		eventType = constants.ActivityUPIAutoPayFailed
		desc = fmt.Sprintf("upi autopay failed, received status : %s", autopayDetailsResp.Status)
		err = errors.New("upi autopay failed, received status : " + autopayDetailsResp.Status)
	}

	_ = tx.Commit()

	activityObj := activity.ActivityEvent{
		UserID:            autopayDetails.UserID,
		SourceEntityID:    autopayDetails.SourceEntityID,
		LoanApplicationID: autopayDetails.LoanApplicationID,
		EntityType:        constants.EntityTypeSystem,
		EntityRef:         autopayDetails.UserID,
		EventType:         eventType,
		Description:       desc,
	}
	activity.RegisterEvent(&activityObj, general.GetTimeStampString())

	return err
}

func handlePostAutoPaySuccess(tx *sqlx.Tx, autopayDetails functionalityModels.EnachCallbackData) (err error) {
	if err = usersutil.SetUserWaitState(autopayDetails.UserID, false); err != nil {
		logger.Log.WithFields(logrus.Fields{"userID": autopayDetails.UserID, "errCustom": "failure in removing wait state"}).Error(err)
		panic(err)
	}
	// mark or update enach module success after pennydrop
	lastModule, err := usermodulemapping.GetLast(autopayDetails.UserID)
	if err != nil {
		logger.WithUser(autopayDetails.UserID).Errorln(err)
		return err
	}

	if lastModule.ModuleName == usermodulemapping.PennyDrop && lastModule.ModuleStatus == constants.UserModuleStatusCompleted {
		err = usermodulemapping.Create(tx,
			autopayDetails.UserID,
			autopayDetails.UserID,
			usermodulemapping.ENACH,
			constants.UserModuleStatusCompleted, autopayDetails.LoanApplicationID)
	} else {
		err = usermodulemapping.UpdateStatus(tx, autopayDetails.UserID, usermodulemapping.ENACH, constants.UserModuleStatusCompleted, constants.UserModuleStatusCompleted)
	}
	if err != nil {
		logger.WithUser(autopayDetails.UserID).Errorln(err)
		return err
	}

	return err
}

func HandleUPIPollingFailure(payloadBytes []byte) (err error) {
	var payload map[string]string
	if err := json.Unmarshal(payloadBytes, &payload); err != nil {
		log.Error(err)
		return err
	}

	userID := payload["user_id"]
	if err = usersutil.SetUserWaitState(userID, false); err != nil {
		logger.Log.WithFields(logrus.Fields{"userID": userID, "errCustom": "failure in removing wait state"}).Error(err)
		panic(err)
	}

	lastModule, err := usermodulemapping.GetLast(userID)
	if err != nil {
		logger.WithUser(userID).Errorln(err)
		return err
	}

	//penny drop is completed or enach is failed
	if (lastModule.ModuleName == usermodulemapping.PennyDrop && lastModule.ModuleStatus == constants.UserModuleStatusCompleted) || (lastModule.ModuleName == usermodulemapping.ENACH && lastModule.ModuleStatus == constants.UserModuleStatusFailed) {
		err = usermodulemapping.Create(nil,
			userID,
			userID,
			usermodulemapping.ENACH,
			constants.UserModuleStatusFailed, "")
		if err != nil {
			logger.WithUser(userID).Errorln(err)
			return err
		}
	}

	dateTimeNowString := general.GetTimeStampString()
	activityObj := finboxEvents.ActivityEvent{
		UserID:      userID,
		EntityType:  constants.EntityTypeCustomer,
		EntityRef:   userID,
		EventType:   "UPI Autopay Failure",
		Description: "UPI Autopay failed, retry with another UPI ID",
	}
	finboxEvents.RegisterEvent(&activityObj, dateTimeNowString)

	return err
}

func Mandaterequestinit(upirequstobj *CreateUPIMandateRequest, dbObj *CreateMandatedbStruct, requestparam map[string]interface{}) CreateUPIMandateRequest {
	upirequstobj.CustomerIdentifier = dbObj.Mobile
	upirequstobj.AuthMode = "upi"
	upirequstobj.MandateType = "create"
	upirequstobj.NotifyCustomer = false
	upirequstobj.GenerateAccessToken = true
	upirequstobj.CorporateConfigID = requestparam["configID"].(string)

	upirequstobj.MandateData.FirstCollectionDate = requestparam["firstCollectionDate"].(string)
	upirequstobj.MandateData.FinalCollectionDate = requestparam["lastCollectionDate"].(string)

	upirequstobj.MandateData.MaximumAmount = requestparam["mandateAmount"].(float64)
	upirequstobj.MandateData.InstrumentType = "debit"
	upirequstobj.MandateData.IsRecurring = true
	upirequstobj.MandateData.Frequency = "Adhoc"
	upirequstobj.MandateData.CustomerName = dbObj.Name
	upirequstobj.MandateData.CustomerRefNumber = dbObj.Mobile

	return *upirequstobj
}

func MFLBLMandaterequestinit(upirequstobj *MFLBLUPIMandateRequest, dbObj *CreateMandatedbStruct, requestparam map[string]interface{}) MFLBLUPIMandateRequest {

	upirequstobj.CustomerIdentifier = dbObj.Mobile
	upirequstobj.CustomerName = dbObj.Name
	upirequstobj.GenerateAccessToken = true
	upirequstobj.NotifyCustomer = true
	upirequstobj.FirstCollectionDate = requestparam["firstCollectionDate"].(string)
	upirequstobj.FinalCollectionDate = requestparam["lastCollectionDate"].(string)
	upirequstobj.MaximumAmount = 15_000
	upirequstobj.CorporateConfigID = requestparam["configID"].(string)
	upirequstobj.MandateType = "create"
	upirequstobj.IncludeAuthenticationURL = true
	upirequstobj.ExpireInDays = 30
	upirequstobj.Frequency = "Adhoc"
	upirequstobj.InstrumentType = "debit"
	upirequstobj.CustomerRefNumber = dbObj.Mobile
	upirequstobj.SchemeRefNumber = dbObj.Mobile
	upirequstobj.AccountValidation = true
	upirequstobj.CustomerAccountNumber = dbObj.AccountNumber
	upirequstobj.Narration = "registration request"
	upirequstobj.AutoDebitAmount = 100
	upirequstobj.IncludeUPIURL = true

	return *upirequstobj
}
