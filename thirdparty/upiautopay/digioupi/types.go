package digioupi

// UPILIMIT to 15000 as mandate amount would be 3 times of EMI
const UPILIMIT = 15000

// UPI related structs

type UPIMandateCreateResult struct {
	MandateID          string
	UserID             string
	CustomerIdentifier string
}

type CreateMandatedbStruct struct {
	UserID            string  `db:"userid"`
	Name              string  `db:"name"`
	Email             string  `db:"email"`
	Mobile            string  `db:"mobile"`
	AccountNumber     string  `db:"accountnumber"`
	IFSC              string  `db:"ifsc"`
	AccountType       string  `db:"accounttype"`
	AccountHolderName string  `db:"accountholdername"`
	Amount            float64 `db:"amount"`
	SourceEntityID    string  `db:"sourceentityid"`
	LenderID          string  `db:"lenderid"`
	LoanApplicationNo string  `db:"loanapplicationno"`
	Tenure            int     `db:"tenure"`
	Interest          float64 `db:"interest"`
	Method            string  `db:"method"`
}

type UPIAutoPayDetailsResp struct {
	CustomerID       string  `json:"customer_id"`
	CustomerName     string  `json:"customer_name"`
	CustomerVPA      string  `json:"customer_vpa"`
	PartnerBankCode  string  `json:"partner_bank_code"`
	SchemeRefNumber  string  `json:"scheme_ref_number"`
	IsRecurring      bool    `json:"is_recurring"`
	Frequency        string  `json:"frequency"`
	FirstCollection  string  `json:"first_collection_date"`
	FinalCollection  string  `json:"final_collection_date"`
	MaximumAmount    float64 `json:"maximum_amount"`
	MandateType      string  `json:"mandate_type"`
	AuthType         string  `json:"auth_type"`
	Status           string  `json:"status"`
	OrgPSPRefNo      string  `json:"org_psp_ref_no"`
	UMRN             string  `json:"umrn"`
	AuthenticationAt string  `json:"authentication_time"`
	DebitAccount     string  `json:"debit_account"`
	Narration        string  `json:"narration"`
	CorporateConfig  string  `json:"corporate_config_id"`
	MerchantCode     string  `json:"merchant_code"`
	ServiceProvider  string  `json:"service_provider_name"`
	UpdatedAt        string  `json:"updated_at"`
	DebitIFSC        string  `json:"debit_ifsc"`
}

type CreateUPIMandateRequest struct {
	CustomerIdentifier  string `json:"customer_identifier"`
	AuthMode            string `json:"auth_mode"`
	MandateType         string `json:"mandate_type"`
	NotifyCustomer      bool   `json:"notify_customer"`
	GenerateAccessToken bool   `json:"generate_access_token"`
	CorporateConfigID   string `json:"corporate_config_id"`
	Narration           string `json:"narration"`
	MandateData         struct {
		MaximumAmount       float64 `json:"maximum_amount"`
		InstrumentType      string  `json:"instrument_type"`
		FirstCollectionDate string  `json:"first_collection_date"`
		FinalCollectionDate string  `json:"final_collection_date"`
		IsRecurring         bool    `json:"is_recurring"`
		Frequency           string  `json:"frequency"`
		CustomerName        string  `json:"customer_name"`
		CustomerRefNumber   string  `json:"customer_ref_number"`
		SchemeRefNumber     string  `json:"scheme_ref_number"`
		// CustomerVpa         string `json:"customer_vpa"`
	} `json:"mandate_data"`
}

type MFLBLUPIMandateRequest struct {
	CustomerIdentifier       string  `json:"customer_identifier"`
	MandateType              string  `json:"mandate_type"`
	NotifyCustomer           bool    `json:"notify_customer"`
	GenerateAccessToken      bool    `json:"generate_access_token"`
	CorporateConfigID        string  `json:"corporate_config_id"`
	Narration                string  `json:"narration"`
	IncludeAuthenticationURL bool    `json:"include_authentication_url"`
	ExpireInDays             int64   `json:"expire_in_days"`
	Frequency                string  `json:"frequency"`
	AccountValidation        bool    `json:"account_validation"`
	CustomerAccountNumber    string  `json:"customer_account_number"`
	AutoDebitAmount          float64 `json:"auto_debit_amount_in_paise"`
	IncludeUPIURL            bool    `json:"include_upi_url"`
	MaximumAmount            float64 `json:"maximum_amount"`
	InstrumentType           string  `json:"instrument_type"`
	FirstCollectionDate      string  `json:"first_collection_date"`
	FinalCollectionDate      string  `json:"final_collection_date"`
	CustomerName             string  `json:"customer_name"`
	CustomerRefNumber        string  `json:"customer_ref_number"`
	SchemeRefNumber          string  `json:"scheme_ref_number"`
}

type CreateMandateResponse struct {
	ID             string `json:"id"`
	MandateDetails struct {
		CustomerIdentifier  string `json:"customer_identifier"`
		CustomerRefNumber   string `json:"customer_ref_number"`
		SchemeRefNumber     string `json:"scheme_ref_number"`
		AuthType            string `json:"auth_type"`
		IsRecurring         bool   `json:"is_recurring"`
		Frequency           string `json:"frequency"`
		FirstCollectionDate string `json:"first_collection_date"`
		FinalCollectionDate string `json:"final_collection_date"`
		MaximumAmount       int    `json:"maximum_amount"`
	} `json:"mandate_details"`
	AccessToken struct {
		CreatedAt string `json:"created_at"`
		ID        string `json:"id"`
		EntityID  string `json:"entity_id"`
		ValidTill string `json:"valid_till"`
	} `json:"access_token"`
}

type CancelMandatedbStruct struct {
	MandateID string
	UserID    string
}

type CancelMandateRequest struct {
	DigioMandateID string `json:"digio_mandate_id"`
}

type CancelMandateResponse struct {
	ID                  string  `json:"id"`
	CustomerID          string  `json:"customer_id"`
	CustomerName        string  `json:"customer_name"`
	CustomerIdentifier  string  `json:"customer_identifier"`
	CustomerVpa         string  `json:"customer_vpa"`
	PartnerBankCode     string  `json:"partner_bank_code"`
	IsRecurring         bool    `json:"is_recurring"`
	Frequency           string  `json:"frequency"`
	FirstCollectionDate string  `json:"first_collection_date"`
	FinalCollectionDate string  `json:"final_collection_date"`
	MaximumAmount       float64 `json:"maximum_amount"`
	MandateType         string  `json:"mandate_type"`
	AuthType            string  `json:"auth_type"`
	Status              string  `json:"status"`
	OrgPspRefNo         string  `json:"org_psp_ref_no"`
	Umrn                string  `json:"umrn"`
	AuthenticationTime  string  `json:"authentication_time"`
	DebitIfsc           string  `json:"debit_ifsc"`
	DebitAccount        string  `json:"debit_account"`
	CorporateConfigID   string  `json:"corporate_config_id"`
	ServiceProviderName string  `json:"service_provider_name"`
	UpdatedAt           string  `json:"updated_at"`
}

type GetdandatedetailsResponse struct {
	ID             string `json:"id"`
	State          string `json:"state"`
	MandateDetails struct {
		CustomerIdentifier    string  `json:"customer_identifier"`
		CustomerName          string  `json:"customer_name"`
		CustomerRefNumber     string  `json:"customer_ref_number"`
		SchemeRefNumber       string  `json:"scheme_ref_number"`
		AuthType              string  `json:"auth_type"`
		IsRecurring           bool    `json:"is_recurring"`
		Frequency             string  `json:"frequency"`
		FirstCollectionDate   string  `json:"first_collection_date"`
		FinalCollectionDate   string  `json:"final_collection_date"`
		MaximumAmount         float64 `json:"maximum_amount"`
		CustomerAccountNumber string  `json:"customer_account_number"`
		DestinationBankID     string  `json:"destination_bank_id"`
		CustomerVpa           string  `json:"customer_vpa"`
	} `json:"mandate_details"`
	Umrn string `json:"umrn"`
}

type UPIMandate struct {
	MandateID         string `json:"id"`
	CustomerRefNumber string `json:"customer_ref_number"`
	SchemeRefNumber   string `json:"scheme_ref_number"`
	CustomerVpa       string `json:"customer_vpa"`
	DebitIfsc         string `json:"debit_ifsc"`
	DebitAccount      string `json:"debit_account"`
	TxnID             string `json:"txn_id"`
	CurrentStatus     string `json:"current_status"`
	TxnTimestamp      int64  `json:"txn_timestamp"`
	TxnRejectCode     string `json:"txn_reject_code"`
	RejectReason      string `json:"txn_reject_reason"`
	Umrn              string `json:"umrn"`
	Others            string `json:"others"`
}

type UPIMandatestruct struct {
	UpiMandates UPIMandate `json:"upi_mandate"`
}

type UPIWebhookresp struct {
	Entities  []string         `json:"entities"`
	Payload   UPIMandatestruct `json:"payload"`
	CreatedAt int64            `json:"created_at"`
	ID        string           `json:"id"`
	Event     string           `json:"event"`
}
