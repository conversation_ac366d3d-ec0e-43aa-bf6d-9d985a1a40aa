package redgate

import "net/http"

const (
	authorizationURIPath  = "/v1/authorize"
	QueryHierarchyURIPath = "/v1/users/hierarchy/query"
	executePolicyURIPath  = "/v1/policy/execute"
)

type InitiateSSORequest struct {
	OrganizationID string `json:"organizationID,omitempty"` // used for Ory flow
	AppClientID    string `json:"appClientID"`
	SSOProvider    string `json:"ssoProvider"`
	Email          string `json:"email"`
	AppID          string `json:"appID"`
	ReturnURL      string `json:"returnURL"`
}

type InitiateSSOResponse struct {
	RedirectBrowserTo string `json:"redirect_browser_to"`
	HTTPCookie        []*http.Cookie
}

type ValidSSOSessionResponse struct {
	Active  bool   `json:"active"`
	EmailId string `json:"emailId"`
}

type AddUserRequest struct {
	Email       string `json:"email"`
	Name        string `json:"name"`
	SSOProvider string `json:"ssoProvider"`
	AppClientID string `json:"appClientID"`
	AppID       string `json:"appID"`
	Enabled     bool   `json:"enabled"`
}

type AuthorizeUserRequest struct {
	AuthorizationToken string                 `json:"authorizationToken"`
	LoanApplicationID  string                 `json:"loanApplicationID"`
	ResourceName       string                 `json:"resource"`
	Action             string                 `json:"action"`
	AppClientID        string                 `json:"appClientID"`
	AppID              string                 `json:"appID"`
	Hierarchy          HierarchyQuery         `json:"hierarchy"`
	Data               map[string]interface{} `json:"data,omitempty"`
	UserID             string
}

type AuthorizationResponse struct {
	IsAllowed bool     `json:"isAllowed"`
	Policies  []Policy `json:"policies"`
}

type Policy struct {
	PolicyID string `json:"policyId"`
	Allowed  bool   `json:"allowed"`
	Reason   string `json:"reason"`
}

// query user hierarchy
type QueryHierarchyRequest struct {
	AppID            string         `json:"appID"`
	AppClientID      string         `json:"appClientID"`
	Hierarchy        HierarchyQuery `json:"hierarchy"`
	UserData         UserDataQuery  `json:"userData"`
	LookupUniqueCode string         `json:"lookupUniqueCode"`
}

type HierarchyQuery struct {
	Metadata map[string]string `json:"metadata"`
}

type UserDataQuery struct {
	Type string `json:"type"`
}

type ExecutePolicyRequest struct {
	AppID             string         `json:"appID"`
	AppClientID       string         `json:"appClientID"`
	ResourceName      string         `json:"resource"`
	PolicyName        string         `json:"policy"`
	Hierarchy         HierarchyQuery `json:"hierarchy"`
	Data              map[string]any `json:"data"`
	LoanApplicationID string         `json:"loanApplicationID"`
	BearerToken       string
	UserID            string
}

type ExecutePolicyResponse struct {
	Data any `json:"data"`
}
