package redgate

import (
	"context"
	"encoding/json"
	"finbox/go-api/conf"
	"finbox/go-api/constants"
	"finbox/go-api/functions/logger"
	httpRepo "finbox/go-api/internal/http"
	"fmt"
	"net/http"
	"strings"

	"golang.org/x/exp/slices"
)

func InitiateSSOCallback(ctx context.Context, reqSSOBody InitiateSSORequest) (*InitiateSSOResponse, error) {
	reqBody, err := json.Marshal(reqSSOBody)
	if err != nil {
		logger.WithContext(ctx).Errorf("[InitiateSSOCallbackURL] failed to marshal request. err: %v, httpBaseReq: %+v", err, reqSSOBody)
		return nil, err
	}

	redGateCreds := conf.GetRedGateServiceCreds()
	baseURL, authBearerToken := redGateCreds["baseURL"], redGateCreds["token"]

	headersMap := map[string]string{
		"Content-Type":  "application/json",
		"Authorization": "Bearer " + authBearerToken,
	}

	httpBaseReq := httpRepo.HTTPRequest{
		Method:         http.MethodPost,
		URL:            baseURL,
		Path:           "/v1/auth/login-sso",
		Body:           reqBody,
		Headers:        headersMap,
		IsClientTypeV2: false,
	}

	httpDoReq := httpRepo.DoHTTPRequest{
		ResourceName: httpRepo.HTTPRedGateServiceConstant,
	}

	var initiateSSOResponse InitiateSSOResponse

	resp, _, err := httpRepo.RedGateHTTPHystrixClient.Do(ctx, &httpBaseReq, &httpDoReq)
	if err != nil {
		return nil, fmt.Errorf("[InitiateSSOCallback] error making POST request: %v", err)
	}

	initiateSSOResponse.HTTPCookie = resp.Cookies

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("[InitiateSSOCallback] Received %d response from redgate: %s", resp.StatusCode, string(resp.Body))
	}
	if err := json.Unmarshal(resp.Body, &initiateSSOResponse); err != nil {
		return nil, fmt.Errorf("[InitiateSSOCallback] error parsing response: %v \n Body: %s | Status: %d", err, string(resp.Body), resp.StatusCode)
	}
	return &initiateSSOResponse, nil
}

func ValidateSSOSession(ctx context.Context, appClientID string, sessionID string, ssoProvider string, appID string) (*ValidSSOSessionResponse, error) {
	redGateCreds := conf.GetRedGateServiceCreds()
	baseURL, authBearerToken := redGateCreds["baseURL"], redGateCreds["token"]

	headersMap := map[string]string{
		"Content-Type":  "application/json",
		"Authorization": "Bearer " + authBearerToken,
	}
	queryParamsMap := map[string]string{
		"sessionID":   sessionID,
		"ssoProvider": ssoProvider,
		"appClientID": appClientID,
		"appID":       appID,
	}

	httpBaseReq := httpRepo.HTTPRequest{
		Method:         http.MethodGet,
		URL:            baseURL,
		Path:           "/v1/auth/validate-session",
		Headers:        headersMap,
		QueryParams:    queryParamsMap,
		IsClientTypeV2: false,
	}

	httpDoReq := httpRepo.DoHTTPRequest{
		ResourceName: httpRepo.HTTPRedGateServiceConstant,
	}

	resp, _, err := httpRepo.RedGateHTTPHystrixClient.Do(ctx, &httpBaseReq, &httpDoReq)
	if err != nil {
		return nil, fmt.Errorf("[ValidateSSOSession] error making GET request: %v", err)
	}

	// Parse the response into the ResponsePayload struct
	var sessionValid ValidSSOSessionResponse
	if err := json.Unmarshal(resp.Body, &sessionValid); err != nil {
		return nil, fmt.Errorf("[ValidateSSOSession] failed to parse response body: %v", err)
	}

	return &sessionValid, nil
}

func AddUser(ctx context.Context, request AddUserRequest) error {
	redGateCreds := conf.GetRedGateServiceCreds()
	baseURL, authBearerToken := redGateCreds["baseURL"], redGateCreds["token"]

	jsonBody, err := json.Marshal(request)
	if err != nil {
		return fmt.Errorf("[AddUser] error marshaling request body: %v ", err)
	}

	headersMap := map[string]string{
		"Content-Type":  "application/json",
		"Authorization": "Bearer " + authBearerToken,
	}
	httpBaseReq := httpRepo.HTTPRequest{
		Method:         http.MethodPost,
		URL:            baseURL,
		Path:           "/v1/users/add",
		Headers:        headersMap,
		Body:           jsonBody,
		IsClientTypeV2: false,
	}
	httpDoReq := httpRepo.DoHTTPRequest{
		ResourceName: httpRepo.HTTPRedGateServiceConstant,
	}

	resp, _, err := httpRepo.RedGateHTTPHystrixClient.Do(ctx, &httpBaseReq, &httpDoReq)
	if err != nil {
		return fmt.Errorf("[AddUser] error making request: %v", err)
	}

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("[AddUser] Error %d from Redgate: %s", resp.StatusCode, string(resp.Body))
	}

	return nil
}

func LogoutUser(ctx context.Context, appID string, appClientID string, userID string) error {
	redGateCreds := conf.GetRedGateServiceCreds()
	baseURL, authBearerToken := redGateCreds["baseURL"], redGateCreds["token"]

	headersMap := map[string]string{
		"Content-Type":  "application/json",
		"Authorization": "Bearer " + authBearerToken,
	}
	queryParamsMap := map[string]string{
		"ssoProvider": constants.KEYCLOAK,
		"userID":      userID,
		"appID":       appID,
		"appClientID": appClientID,
	}

	httpBaseReq := httpRepo.HTTPRequest{
		Method:         http.MethodPost,
		URL:            baseURL,
		Path:           "/v1/auth/logout",
		Headers:        headersMap,
		QueryParams:    queryParamsMap,
		IsClientTypeV2: false,
	}
	httpDoReq := httpRepo.DoHTTPRequest{
		ResourceName: httpRepo.HTTPRedGateServiceConstant,
	}

	resp, _, err := httpRepo.RedGateHTTPHystrixClient.Do(ctx, &httpBaseReq, &httpDoReq)
	if err != nil {
		return fmt.Errorf("[LogoutUser] error making POST request: %v", err)
	}
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("[LogoutUser] Error %d from Redgate: %s", resp.StatusCode, string(resp.Body))
	}

	return nil
}

func IsSSOEnabledUser(appClientId string, userID string) bool {
	ssoEnabledClients := []string{constants.ABFLOrganizationID, constants.ABFLID}
	ssoDomains := []string{"@adityabirlacapital.com"}

	isSsoUser := slices.ContainsFunc(ssoDomains, func(domain string) bool { return strings.HasSuffix(userID, domain) })
	return isSsoUser && slices.Contains(ssoEnabledClients, appClientId)
}

func CheckUserAuthorization(ctx context.Context, req *AuthorizeUserRequest) (*AuthorizationResponse, error) {

	if req == nil {
		return nil, fmt.Errorf("[CheckUserAuthorization] empty request params")
	}

	redGateCreds := conf.GetRedGateServiceCreds()
	baseURL := redGateCreds["baseURL"]

	headersMap := map[string]string{
		"Content-Type":  "application/json",
		"Authorization": req.AuthorizationToken,
	}

	bodyParamsMap := map[string]interface{}{
		"appClientID": req.AppClientID,
		"appID":       req.AppID,
		"action":      req.Action,
		"resource":    req.ResourceName,
		"hierarchy":   req.Hierarchy,
		"data":        req.Data,
	}

	reqBody, err := json.Marshal(bodyParamsMap)
	if err != nil {
		return nil, fmt.Errorf("[CheckUserAuthorization] error marshaling request body: %v", err)
	}

	httpBaseReq := httpRepo.HTTPRequest{
		Method:  http.MethodPost,
		URL:     baseURL,
		Path:    authorizationURIPath,
		Body:    reqBody,
		Headers: headersMap,
	}

	httpDoReq := httpRepo.DoHTTPRequest{
		ResourceName: httpRepo.HTTPRedGateServiceConstant,
	}

	resp, _, err := httpRepo.RedGateHTTPHystrixClient.Do(ctx, &httpBaseReq, &httpDoReq)
	if err != nil {
		return nil, fmt.Errorf("[CheckUserAuthorization] error making POST authorization request: %v", err)
	}
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("[CheckUserAuthorization] Error %d from Redgate forauthorization : %s", resp.StatusCode, string(resp.Body))
	}

	var authResponse AuthorizationResponse
	if err := json.Unmarshal(resp.Body, &authResponse); err != nil {
		return nil, fmt.Errorf("[CheckUserAuthorization] error parsing authorization response: %v", err)
	}

	return &authResponse, nil
}

// Function to query the user hierarchy based.
func QueryUserHierarchy(ctx context.Context, req *QueryHierarchyRequest) (*QueryHierarchyResponse, error) {

	var resp QueryHierarchyResponse

	if req == nil {
		return nil, fmt.Errorf("[QueryUserHierarchy] empty request params")
	}

	redGateCreds := conf.GetRedGateServiceCreds()
	baseURL := redGateCreds["baseURL"]

	headersMap := map[string]string{
		"Content-Type": "application/json",
	}

	reqBody, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("[QueryUserHierarchy] error marshaling request body: %v, req: %v", err, req)
	}

	httpBaseReq := httpRepo.HTTPRequest{
		Method:  http.MethodPost,
		URL:     baseURL,
		Path:    QueryHierarchyURIPath,
		Body:    reqBody,
		Headers: headersMap,
	}

	httpDoReq := httpRepo.DoHTTPRequest{
		ResourceName: httpRepo.HTTPRedGateServiceConstant,
	}

	httpResp, _, err := httpRepo.RedGateHTTPHystrixClient.Do(ctx, &httpBaseReq, &httpDoReq)
	if err != nil {
		return nil, fmt.Errorf("[QueryUserHierarchy] error making POST hierarchy query request err: %v, httpBaseReq: %v, httpDoReq: %v", err, httpBaseReq, httpDoReq)
	}

	if httpResp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("[QueryUserHierarchy] Error %d from Redgate for hierarchy respbody: %s, httpBaseReq: %v, httpDoReq: %v", httpResp.StatusCode, string(httpResp.Body), httpBaseReq, httpDoReq)
	}

	if err := json.Unmarshal(httpResp.Body, &resp); err != nil {
		return nil, fmt.Errorf("[QueryUserHierarchy] error parsing hierarchy query response err: %v, respbody: %s", err, string(httpResp.Body))
	}

	return &resp, nil
}

func ExecutePolicy(ctx context.Context, req ExecutePolicyRequest) (resp *ExecutePolicyResponse, err error) {
	redGateCreds := conf.GetRedGateServiceCreds()
	baseURL := redGateCreds["baseURL"]
	headersMap := map[string]string{
		"Content-Type":  "application/json",
		"Authorization": req.BearerToken,
	}
	reqBody, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("[ExecutePolicy] error marshaling request body: %v, req: %v", err, req)
	}

	httpBaseReq := httpRepo.HTTPRequest{
		Method:  http.MethodPost,
		URL:     baseURL,
		Path:    executePolicyURIPath,
		Body:    reqBody,
		Headers: headersMap,
	}

	httpDoReq := httpRepo.DoHTTPRequest{
		ResourceName: httpRepo.HTTPRedGateServiceConstant,
	}

	httpResp, _, err := httpRepo.RedGateHTTPHystrixClient.Do(ctx, &httpBaseReq, &httpDoReq)
	if err != nil {
		return nil, fmt.Errorf("[ExecutePolicy] error making POST hierarchy query request err: %v, httpBaseReq: %v, httpDoReq: %v", err, httpBaseReq, httpDoReq)
	}

	if httpResp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("[ExecutePolicy] Error %d from Redgate for hierarchy respbody: %s, httpBaseReq: %v, httpDoReq: %v", httpResp.StatusCode, string(httpResp.Body), httpBaseReq, httpDoReq)
	}

	if err := json.Unmarshal(httpResp.Body, &resp); err != nil {
		return nil, fmt.Errorf("[ExecutePolicy] error parsing hierarchy query response err: %v, respbody: %s", err, string(httpResp.Body))
	}

	return resp, nil
}
