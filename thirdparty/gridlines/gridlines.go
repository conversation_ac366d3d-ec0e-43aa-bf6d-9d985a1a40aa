package gridlines

import (
	"context"
	"encoding/json"
	"errors"
	"finbox/go-api/conf"
	"finbox/go-api/constants"
	"finbox/go-api/errorHandler"
	"finbox/go-api/functions/logger"
	"finbox/go-api/functions/requestutils"
	"finbox/go-api/functions/retry"
	"finbox/go-api/functions/services/pincodeapi"
	"finbox/go-api/functions/serviceslib"
	"finbox/go-api/internal/fbxerrors"
	"finbox/go-api/utils/general"
	panUtils "finbox/go-api/utils/pan"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/finbox-in/octoclient"
)

var log = logger.Log

var OctoOption = octoclient.Options{
	BaseURL:       conf.OctopusBaseURL,
	Token:         conf.OctopusConfig["ClientID"],
	Authorization: fmt.Sprintf("Bearer %s", conf.OctopusConfig["BearerAuth"]),
}

var octoClient = octoclient.New(OctoOption)

type returnObject struct {
	url      string
	request  string
	response string
	success  int
	userID   string
	id       string
}

func gridlinePANToStandardPANResponseTransformer(gridlineResp GridLinePANDetailedAPI) (standardPANResp panUtils.PANDetailedAPI) {

	// Gridline has multiple sources, which might yield state sometimes, while sometimes not.
	// Used state from gridline whenever provided, otherwise map it manually
	var state string
	gridlineSentState := gridlineResp.Data.PanData.Address.State
	state = gridlineSentState
	if state == "" {
		_, state = pincodeapi.GetCityState(gridlineResp.Data.PanData.Address.Pincode)
	}

	/*
		JQ Query for the same - the struct approach was already done, hence not used jq
			jq '{ status:"success", statusCode:"\(.status)", result:{ data:{ message:"\(.data.message)", panData:{ name: "\(.data.pan_data.first_name) \(.data.pan_data.last_name)", gender: "\(.data.pan_data.gender)", pan: "\(.data.pan_data.document_id)", firstName: "\(.data.pan_data.first_name)", middleName: null, lastName: "\(.data.pan_data.last_name)", dateOfBirth: "\(.data.pan_data.date_of_birth)", maskedAadhaarNumber: "\(.data.pan_data.masked_aadhaar_number)", address: { city: "\(.data.pan_data.address_data.city)", state: null, pincode: "\(.data.pan_data.address_data.pincode)", line1: "\(.data.pan_data.address_data.line_1)", line2: "\(.data.pan_data.address_data.line_2)" }, aadhaarLinked: .data.pan_data.aadhaar_linked } } } }'
	*/
	name := gridlineResp.Data.PanData.Name
	if len(name) < 3 {
		name = gridlineResp.Data.PanData.FirstName + gridlineResp.Data.PanData.MiddleName + gridlineResp.Data.PanData.LastName
	}
	standardAPIResp := panUtils.PANDetailedAPI{
		StatusCode: strconv.Itoa(gridlineResp.Status),
		Result: struct {
			Data panUtils.PANDetailStruct "json:\"data\""
		}{

			Data: panUtils.PANDetailStruct{

				Message: gridlineResp.Data.Message,
				PanData: panUtils.PANDetailDataStruct{
					Phone:               gridlineResp.Data.PanData.Phone,
					MiddleName:          gridlineResp.Data.PanData.MiddleName,
					FirstName:           gridlineResp.Data.PanData.FirstName,
					LastName:            gridlineResp.Data.PanData.LastName,
					Name:                name,
					Gender:              gridlineResp.Data.PanData.Gender,
					PAN:                 gridlineResp.Data.PanData.DocumentID,
					DateOfBirth:         gridlineResp.Data.PanData.DateOfBirth,
					MaskedAadhaarNumber: gridlineResp.Data.PanData.MaskedAadhaarNumber,
					AadhaarLinked:       gridlineResp.Data.PanData.AadhaarLinked,
					Address: panUtils.PANDetailAddressStruct{
						City:    gridlineResp.Data.PanData.Address.City,
						Line1:   gridlineResp.Data.PanData.Address.Line1,
						Line2:   gridlineResp.Data.PanData.Address.Line2,
						Pincode: gridlineResp.Data.PanData.Address.Pincode,
						State:   state,
					},
				},
			},
		},
	}
	if gridlineResp.Status == 200 {
		standardAPIResp.Status = "success"
	} else {
		standardAPIResp.Status = "failure"
	}
	return standardAPIResp
}

// PanExtendedAPI fetches PAN Detail from Gridline, and returns an object which complies with hyperverge response type
func PanExtendedAPI(userID, pan string) (resp panUtils.PANDetailedAPI, externalServID string, err error) {
	var externalServiceID = general.GetUUID()

	serviceID, err := conf.GetServiceID(constants.ServiceTypePANDetailsGridline)

	if err != nil {
		logger.WithUser(userID).Errorln(err)
		return resp, externalServiceID, err
	}

	serviceName := constants.ServicePANDetailsGridline

	returnObject := returnObject{
		userID: userID,
		url:    conf.OctopusBaseURL,
		id:     externalServiceID,
	}

	var payload = octoclient.OctoPayload{
		ServiceID: serviceID,
		Data: map[string]interface{}{
			"pan_number": pan,
			"consent":    "Y",
		},
	}

	payloadString, err := json.Marshal(payload)

	if err != nil {
		logger.WithUser(userID).Errorln(err)
		return resp, externalServiceID, err
	}
	returnObject.request = string(payloadString)

	var jsonBytes []byte

	var apiResp GridLinePANDetailedAPI

	err = retry.CustomRetry(3, 10*time.Millisecond, func() error {
		response, err := requestutils.GetMockableOctoInvoke(context.TODO(), userID, serviceName, octoClient, payload)
		if err != nil {
			logger.WithUser(userID).Errorln(err)
			return retry.NewStop(err.Error())
		}

		jsonBytes, err = json.Marshal(response.Data)

		if err != nil {
			errorHandler.ReportToSentryWithoutRequest(fmt.Errorf("error received in marshaling response into struct for user: %s, response: %v", userID, response.Data))
			return errors.New("marshaling fail")

		}

		err = json.Unmarshal(jsonBytes, &apiResp)
		if err != nil {
			errorHandler.ReportToSentryWithoutRequest(fmt.Errorf("error received in unmarshaling response into struct for user: %s, response: %v", userID, string(jsonBytes)))
			logger.WithUser(userID).Errorln(err)
			return errors.New("unmarshaling fail")
		}

		if apiResp.Data.Code == "1004" || strings.Contains(strings.ToLower(apiResp.Data.Message), "does not exist") {
			return retry.NewStop(constants.VendorMessageWrongPAN)
		} else if apiResp.Status == 429 {
			return retry.NewStop(constants.VendorRateLimitExceeded)
		} else if apiResp.Status == 400 && apiResp.Data.Code == "INVALID_PAN" {
			return retry.NewStop(constants.VendorMessageWrongPAN)
		} else if apiResp.Status == 200 {
			return nil
		} else {
			logger.WithUser(userID).Errorln("Errors returned in response:", apiResp)
			return fbxerrors.ErrExternalAPIFailed
		}
	})

	returnObject.response = string(jsonBytes)

	if err != nil {
		logger.WithUser(userID).Errorln(err)
		if err.Error() == constants.VendorRateLimitExceeded {
			// point to single error type
			err = fbxerrors.ErrVendorRateLimitExceeded
		}
		serviceslib.WriteToDB(serviceName, returnObject.request, returnObject.response, 0, returnObject.userID, returnObject.url, err.Error(), returnObject.id)
		return resp, externalServiceID, err
	}

	standardAPIResp := gridlinePANToStandardPANResponseTransformer(apiResp)

	go serviceslib.WriteToDB(serviceName, returnObject.request, returnObject.response, 1, returnObject.userID, returnObject.url, "", returnObject.id)

	return standardAPIResp, externalServiceID, nil

}

// FetchPANName returns name on PAN, external service ID, boolean indicating whether service is down, error string and error.
// Custom Service ID used when some client wants to use Gridline but with their own serviceID already onboarded on octopus
func FetchPANName(pan string, userID string, customServiceID string) (panName string, extServiceID string, isServiceDown bool, errString string, erro error) {
	//https://gridlines.stoplight.io/docs/gridlines-api-docs/4a8ffb4a9f33e-fetch-pan-lite
	var serviceID string
	if len(strings.TrimSpace(customServiceID)) != 0 {
		serviceID = customServiceID
	} else {
		serviceIDMapping, err := conf.GetServiceID(constants.ServiceTypePANNameGridline)
		if err != nil {
			return "", "", true, constants.ErrServiceIDDoesNotExist, errors.New(constants.ErrServiceIDDoesNotExist)
		}
		serviceID = serviceIDMapping
	}
	serviceName := constants.ServicePANNameGridline

	var externalServiceID = general.GetUUID()
	returnObject := returnObject{
		id:     externalServiceID,
		userID: userID,
		url:    conf.OctopusBaseURL,
	}

	var payload = octoclient.OctoPayload{
		ServiceID: serviceID,
		Data: map[string]interface{}{
			"pan_number": pan,
			"consent":    "Y",
		},
	}

	payloadString, err := json.Marshal(payload)
	if err != nil {
		log.Errorln(err)
		return "", externalServiceID, false, "", err
	}
	returnObject.request = string(payloadString)

	var jsonBytes []byte
	var serviceResp panLiteStruct

	err = retry.CustomRetry(MaxTry, 10*time.Millisecond, func() error {
		response, err := requestutils.GetMockableOctoInvoke(context.TODO(), userID, serviceName, octoClient, payload)
		if err != nil {
			logger.WithUser(userID).Errorln(err)
			return retry.NewStop(err.Error())
		}

		// OctoClient: Recreating the payload in which function was consuming it
		jsonBytes, err = json.Marshal(response.Data)

		err = json.Unmarshal(jsonBytes, &serviceResp)
		if err != nil {
			logger.WithUser(userID).Errorln(err)
			errorHandler.ReportToSentryWithoutRequest(fmt.Errorf("error received in unmarshalling response into struct for user: %s, response: %v", userID, string(jsonBytes)))
			return errors.New("unmarshalling fail")
		}

		//https://gridlines.stoplight.io/docs/gridlines-api-docs/4a8ffb4a9f33e-fetch-pan-lite

		//Invalid PAN Number from gridline error code
		if serviceResp.Status == 400 && serviceResp.Error.Code == "INVALID_PAN" {
			return errors.New(constants.VendorMessageWrongPAN)
		}
		//Valid Request but pan does not exist
		if serviceResp.Status == 200 && serviceResp.Data.Code == "1004" {
			return errors.New(constants.VendorMessageWrongPAN)
		}
		if serviceResp.Status == 200 {
			return nil
		} else {
			logger.WithUser(userID).Warnln(serviceResp.Error.Message)
			return retry.NewStop(serviceResp.Error.Message)
		}
	})

	returnObject.response = string(jsonBytes)
	var errorString string
	// custom error messages to user

	if err != nil {
		switch serviceResp.Status {
		case 200:
			if serviceResp.Data.Code == "1004" {
				errorString = constants.VendorMessageWrongPAN
			}
		case 400:
			if serviceResp.Error.Code == "INVALID_PAN" {
				errorString = constants.VendorMessageWrongPAN
			}
		}
		// TODO - Shift this from synchronus to asyncrhonus by adding into a thread pool
		serviceslib.WriteToDB(serviceName, returnObject.request, returnObject.response, 0, returnObject.userID, returnObject.url, err.Error(), returnObject.id)
		return "", externalServiceID, true, errorString, errors.New(errorString)
	}
	// TODO - Shift this from synchronus to asyncrhonus by adding into a thread pool
	serviceslib.WriteToDB(serviceName, returnObject.request, returnObject.response, 1, returnObject.userID, returnObject.url, "", returnObject.id)
	return serviceResp.Data.PanData.Name, externalServiceID, false, errorString, err

}

// Verify PAN returns boolean indicating name match, dob match, external service id, and error string and an error
// The API does not provide pan status active or not. Hence this is always set to true
func VerifyPAN(pan string, name string, dob string, userID string, sourceEntityID string) (isNameMatch bool, isDobMatch bool, isPANActive bool, externServID string, errorString string, err error) {
	dobObj, err := time.Parse("2006-01-02", dob)
	externalServiceID := general.GetUUID()

	if err != nil {
		log.Errorln(err)
		return false, false, false, "", err.Error(), err
	}

	serviceID, err := conf.GetServiceID(constants.ServiceTypeVerifyPANGridline)
	if err != nil {
		return false, false, false, externalServiceID, constants.ErrServiceIDDoesNotExist, errors.New(constants.ErrServiceIDDoesNotExist)
	}

	returnObject := returnObject{
		id:     externalServiceID,
		url:    conf.OctopusBaseURL,
		userID: userID,
	}

	payload := octoclient.OctoPayload{
		ServiceID: serviceID,
		Data: map[string]interface{}{
			"pan_id":        pan,
			"name":          name,
			"date_of_birth": dobObj.Format("2006-01-02"),
			"consent":       "Y",
		},
	}

	payloadString, err := json.Marshal(payload)
	if err != nil {
		log.Errorln(err)
		return false, false, false, externalServiceID, err.Error(), err
	}

	returnObject.request = string(payloadString)

	var firstResp panVerifyStruct
	var jsonBytes []byte

	err = retry.CustomRetry(MaxTry, 10*time.Millisecond, func() error {
		firstResp = panVerifyStruct{}

		response, err := requestutils.GetMockableOctoInvoke(context.TODO(), userID, constants.ServiceVerifyPANGridline, octoClient, payload)
		if err != nil {
			logger.WithUser(userID).Errorln(err)
			return retry.NewStop(err.Error())
		}

		jsonBytes, err = json.Marshal(response.Data)
		if err != nil {
			errorHandler.ReportToSentryWithoutRequest(fmt.Errorf("error received in marshalling response into struct for user: %s, response: %v", userID, response.Data))
			return errors.New("marshalling fail")
		}
		err = json.Unmarshal(jsonBytes, &firstResp)
		if err != nil {
			errorHandler.ReportToSentryWithoutRequest(fmt.Errorf("error received in unmarshalling response into struct for user: %s, response: %v", userID, string(jsonBytes)))
			return errors.New("unmarshalling fail")
		}

		if msg, ok := response.Data["message"]; ok && msg == "API rate limit exceeded" {
			errorHandler.ReportToSentryWithoutRequest(fmt.Errorf("Gridline Rate Limit for PAN Verify API for user: %s, response: %v", userID, response.Data))
			return errors.New("rate limit")
		}

		// Either successful or error from user
		if firstResp.Status == 200 || firstResp.Status == 400 {
			return nil
		} else {
			logger.WithUser(userID).Warnln(firstResp.Error.Message)
			return retry.NewStop(firstResp.Error.Message)
		}
	})

	returnObject.response = string(jsonBytes)

	// Custom error messages to users
	if err != nil {
		errorString := constants.ErrPANServiceDown
		go serviceslib.WriteToDB(constants.ServiceVerifyPANGridline, returnObject.request, returnObject.response, 0, returnObject.userID, returnObject.url, err.Error(), returnObject.id)
		return false, false, false, externalServiceID, errorString, err
	}

	nameMatch := false
	dobMatch := false

	if firstResp.Data.PanData.NameMatchStatus == "MATCH" {
		nameMatch = true
	} else {
		errorString = "name is not matching with than on PAN"
		err = errors.New(errorString)
	}
	if firstResp.Data.PanData.DobMatchStatus == "MATCH" {
		dobMatch = true
	} else {
		errorString = "date of birth is not matching with that on PAN"
		err = errors.New(errorString)
	}

	switch firstResp.Data.Code {
	// case "1001": //Valid details.
	// case "1002": //Partial details matched.
	// case "1003": //Invalid details.
	case "1004": //Pan does not exist.
	case "INVALID_PAN": //Invalid PAN number.
		errorString = "invalid pan number"
		err = errors.New(constants.VendorMessageWrongPAN)
	}

	if err != nil {
		go serviceslib.WriteToDB(constants.ServiceVerifyPANGridline, returnObject.request, returnObject.response, 0, returnObject.userID, returnObject.url, err.Error(), returnObject.id)
		return nameMatch, dobMatch, true, externalServiceID, "", nil
	}

	go serviceslib.WriteToDB(constants.ServiceVerifyPANGridline, returnObject.request, returnObject.response, 1, returnObject.userID, returnObject.url, "", returnObject.id)

	// PAN Status is always set to true, because API does not give information about it.
	return nameMatch, dobMatch, true, externalServiceID, "", nil

}

// GetActiveGSTINFromPANOcto returns list of Active GSTINs for a given PAN
func GetActiveGSTINFromPANOcto(userID, pan string) []string {
	var externalServiceID = general.GetUUID()

	serviceID, err := conf.GetServiceID(constants.ServiceTypePANGSTDetailOnGrid)
	if err != nil {
		logger.WithUser(userID).Errorln(err)
		return nil
	}

	serviceName := constants.ServicePANGSTDetails

	var gobj = map[string]string{
		"url":     conf.OctopusBaseURL,
		"strReq":  "",
		"strRes":  "",
		"success": "0",
		"userID":  userID,
		"id":      externalServiceID,
	}

	//OctoClient: Create a sample payload
	var payload = octoclient.OctoPayload{
		ServiceID: serviceID,
		Data: map[string]interface{}{
			"consent":    "Y",
			"pan_number": pan,
		},
	}

	payloadString, err := json.Marshal(payload)
	if err != nil {
		logger.WithUser(userID).Errorln(err)
		return nil
	}
	gobj["strReq"] = string(payloadString)

	var jsonBytes []byte

	resp := gstSearchStruct{}

	err = retry.CustomRetry(3, 10*time.Millisecond, func() error {

		response, err := requestutils.GetMockableOctoInvoke(context.TODO(), userID, serviceName, octoClient, payload)
		if err != nil {
			logger.WithUser(userID).Errorln(err)
			return retry.NewStop(err.Error())
		}

		jsonBytes, err = json.Marshal(response.Data)
		if err != nil {
			errorHandler.ReportToSentryWithoutRequest(fmt.Errorf("error received in marshaling response into struct for user: %s, response: %v", userID, response.Data))
			return errors.New("marshaling fail")
		}

		err = json.Unmarshal(jsonBytes, &resp)
		if err != nil {
			errorHandler.ReportToSentryWithoutRequest(fmt.Errorf("error received in unmarshaling response into struct for user: %s, response: %v", userID, string(jsonBytes)))
			return errors.New("unmarshaling fail")
		}

		if !general.InArr(resp.Data.Code, []string{"1002", "1004"}) {
			return errors.New("error in api response")
		} else {
			return nil
		}

	})

	gobj["strRes"] = string(jsonBytes)

	if err != nil {
		logger.WithUser(userID).Errorln(err)
		serviceslib.WriteToDB(serviceName, gobj["strReq"], gobj["strRes"], 0, gobj["userID"], gobj["url"], fmt.Sprintf("%d", resp.StatusCode), gobj["id"])
		return nil
	}
	toReturn := []string{}
	for _, row := range resp.Data.Results {
		if row.DocumentType == "GSTIN" && row.Status == "Active" {
			toReturn = append(toReturn, strings.ToUpper(row.DocumentID))
		}
	}

	serviceslib.WriteToDB(serviceName, gobj["strReq"], gobj["strRes"], 1, gobj["userID"], gobj["url"], "", gobj["id"])

	return toReturn
}

// GetGSTDetailsOcto returns GST details - gst details, trade name, company name, registration date,
// constitution, address, gst status and error string if any
func GetGSTDetailsOcto(userID string, gstin string) (string, string, string, string, string, string, string, string, string) {
	var externalServiceID = general.GetUUID()

	serviceID, err := conf.GetServiceID(constants.ServiceTypeGSTDetailOnGrid)
	if err != nil {
		logger.WithUser(userID).Errorln(err)
		return "", "", "", "", "", "", "", "", connectErrorMessage
	}

	serviceName := constants.ServiceGSTDetails

	var gobj = map[string]string{
		"url":     conf.OctopusBaseURL,
		"strReq":  "",
		"strRes":  "",
		"success": "0",
		"userID":  userID,
		"id":      externalServiceID,
	}

	//OctoClient: Create a sample payload
	var payload = octoclient.OctoPayload{
		ServiceID: serviceID,
		Data: map[string]interface{}{
			"consent": "Y",
			"gstin":   gstin,
		},
	}

	payloadString, err := json.Marshal(payload)
	if err != nil {
		logger.WithUser(userID).Errorln(err)
		return "", "", "", "", "", "", "", "", connectErrorMessage
	}
	gobj["strReq"] = string(payloadString)

	var jsonBytes []byte

	resp := gstDetailsStruct{}

	err = retry.CustomRetry(3, 10*time.Millisecond, func() error {

		response, err := requestutils.GetMockableOctoInvoke(context.TODO(), userID, serviceName, octoClient, payload)
		if err != nil {
			logger.WithUser(userID).Errorln(err)
			return retry.NewStop(err.Error())
		}

		jsonBytes, err = json.Marshal(response.Data)
		if err != nil {
			errorHandler.ReportToSentryWithoutRequest(fmt.Errorf("error received in marshaling response into struct for user: %s, response: %v", userID, response.Data))
			return errors.New("marshaling fail")
		}

		err = json.Unmarshal(jsonBytes, &resp)
		if err != nil {
			errorHandler.ReportToSentryWithoutRequest(fmt.Errorf("error received in unmarshaling response into struct for user: %s, response: %v", userID, string(jsonBytes)))
			return errors.New("unmarshaling fail")
		}

		if general.InArr(resp.Data.Code, []string{"1000", "1005"}) {
			return nil
		} else {
			return errors.New("error in api response")
		}

	})

	gobj["strRes"] = string(jsonBytes)

	if err != nil {
		logger.WithUser(userID).Errorln(err)
		serviceslib.WriteToDB(serviceName, gobj["strReq"], gobj["strRes"], 0, gobj["userID"], gobj["url"], fmt.Sprintf("%d", resp.StatusCode), gobj["id"])
		return externalServiceID, "", "", "", "", "", "", "", connectErrorMessage
	}

	switch resp.Data.Code {
	case "1005":
		return externalServiceID, "", "", "", "", "", "", "", "No details found for GSTIN"
	case "1000":
		if resp.Data.GSTINData.Status != "Active" {
			serviceslib.WriteToDB(serviceName, gobj["strReq"], gobj["strRes"], 1, gobj["userID"], gobj["url"], fmt.Sprintf("%d", resp.StatusCode), gobj["id"])
			return externalServiceID, "", "", "", "", "", "", "", "GSTIN is not active, only active GSTIN can be used to connect"
		}
	}

	dateStr := ""
	dateObj, dateErr := time.Parse("2006-01-02", resp.Data.GSTINData.RegistrationDate)
	if dateErr != nil {
		logger.WithUser(userID).Errorln(dateErr)
	} else {
		dateStr = dateObj.Format("2006-01-02")
	}

	serviceslib.WriteToDB(serviceName, gobj["strReq"], gobj["strRes"], 1, gobj["userID"], gobj["url"], "", gobj["id"])

	return externalServiceID, gobj["strRes"], resp.Data.GSTINData.TradeName, resp.Data.GSTINData.CompanyName, dateStr, resp.Data.GSTINData.Constitution, resp.Data.GSTINData.BusinessAddress.Address, resp.Data.GSTINData.Status, ""
}
