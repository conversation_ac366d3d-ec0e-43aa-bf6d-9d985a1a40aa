package gridlines

type PANDetailAddressStruct struct {
	City    string `json:"city"`
	Line1   string `json:"line_1"`
	Line2   string `json:"line_2"`
	<PERSON><PERSON><PERSON> string `json:"pincode"`
	State   string `json:"state"`
}
type PANDetailDataStruct struct {
	Category            string                 `json:"category"`
	DateOfBirth         string                 `json:"date_of_birth"`
	DocumentID          string                 `json:"document_id"`
	DocumentType        string                 `json:"document_type"`
	Email               string                 `json:"email"`
	FirstName           string                 `json:"first_name"`
	MiddleName          string                 `json:"middle_name"`
	Gender              string                 `json:"gender"`
	LastName            string                 `json:"last_name"`
	MaskedAadhaarNumber string                 `json:"masked_aadhaar_number"`
	Address             PANDetailAddressStruct `json:"address_data"`
	AadhaarLinked       bool                   `json:"aadhaar_linked"`
	Name                string                 `json:"name"`
	Phone               string                 `json:"phone"`
}
type PANDetailStruct struct {
	Code    string              `json:"code"`
	Message string              `json:"message"`
	PanData PANDetailDataStruct `json:"pan_data"`
}
type GridLinePANDetailedAPI struct {
	Status    int             `json:"status"`
	Data      PANDetailStruct `json:"data"`
	Path      string          `json:"path"`
	RequestID string          `json:"request_id"`
	Timestamp int64           `json:"timestamp"`
}

type panLiteStruct struct {
	RequestID string             `json:"request_id"`
	Status    int                `json:"status"`
	Data      panNameDataStruct  `json:"data"`
	Error     panNameErrorStruct `json:"error"`
	Timestamp int64              `json:"timestamp"`
	Path      string             `json:"path"`
}
type panNameErrorStruct struct {
	Code    string `json:"code"`
	Message string `json:"message"`
	Type    string `json:"type"`
}
type panNameDataStruct struct {
	Code    string                   `json:"code"`
	Message string                   `json:"message"`
	PanData panNameDataDetailsStruct `json:"pan_data"`
	Error   string                   `json:"error"`
}
type panNameDataDetailsStruct struct {
	DocumentType string `json:"document_type"`
	Name         string `json:"name"`
}

// Gridline Verify PAN
type panVerifyStruct struct {
	RequestID string               `json:"request_id"`
	Status    int                  `json:"status"`
	Data      panVerifyDataStruct  `json:"data"`
	Error     panVerifyErrorStruct `json:"error"`
	Timestamp int64                `json:"timestamp"`
	Path      string               `json:"path"`
}

type panVerifyErrorStruct struct {
	Code     string                         `json:"code"`
	Message  string                         `json:"message"`
	Metadata []panVerifyErrorMetadataStruct `json:"metadata"`
	Type     string                         `json:"type"`
}
type panVerifyErrorMetadataStruct struct {
	Field   string `json:"field"`
	Message string `json:"message"`
}
type panVerifyDataStruct struct {
	Code    string                 `json:"code"`
	Message string                 `json:"message"`
	PanData panVerifyPANDataStruct `json:"pan_data"`
}

type panVerifyPANDataStruct struct {
	DocumentType    string `json:"document_type"`
	NameMatchStatus string `json:"name_match_status"`
	DobMatchStatus  string `json:"dob_match_status"`
}

type gstSearchResultStruct struct {
	DocumentType string `json:"document_type"`
	DocumentID   string `json:"document_id"`
	Status       string `json:"status"`
	State        string `json:"state"`
	StateCode    string `json:"state_code"`
}

type gstSearchData struct {
	Code    string                  `json:"code"`
	Message string                  `json:"message"`
	Results []gstSearchResultStruct `json:"results"`
}

type gstSearchStruct struct {
	StatusCode int           `json:"status"`
	Data       gstSearchData `json:"data"`
}

type businessAddressStruct struct {
	Address string `json:"address"`
}

type gstDetailResultStruct struct {
	DocumentType     string                `json:"document_type"`
	DocumentID       string                `json:"document_id"`
	Status           string                `json:"status"`
	Pan              string                `json:"pan"`
	CompanyName      string                `json:"legal_name"`
	TradeName        string                `json:"trade_name"`
	Constitution     string                `json:"constitution_of_business"`
	RegistrationDate string                `json:"date_of_registration"`
	BusinessAddress  businessAddressStruct `json:"principal_address"`
}

type gstDetailsData struct {
	Code      string                `json:"code"`
	Message   string                `json:"message"`
	GSTINData gstDetailResultStruct `json:"gstin_data"`
}

type gstDetailsStruct struct {
	StatusCode int            `json:"status"`
	Data       gstDetailsData `json:"data"`
}
