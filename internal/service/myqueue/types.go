package myqueue

// MyQueueLenderDashboardParams request and response
type MyQueueLenderDashboardParams struct {
	BranchCodes     []string
	WorkFlowTypes   []string
	CurrentAssignee string
	WorkflowState   string
	LenderID        string
	DashboardType   string
	Page            int
	PageSize        int
}

type MyQueueResponse struct {
	LenderQueueDetails  []LenderQueueDetail `json:"lenderQueueDetails"`
	IsNextPageAvailable bool                `json:"isNextPageAvailable"`
}

type LenderQueueDetail struct {
	LoanApplicationID string `json:"loanApplicationID"`
}

// my queue variables
var (
	myqueueRegistry = make(map[string]IMyQueueFetcher)
	defaultFetcher  = &DefaultMyQueueFetcher{} // Default implementation
)

type DefaultMyQueueFetcher struct{}

// QueryCondition represents a SQL condition with its arguments
type QueryCondition struct {
	Condition string
	Args      []interface{}
}

// errors

var (
	ErrQueueFetcherNotFound = "Queue fetcher not found for the given lender ID"
)
