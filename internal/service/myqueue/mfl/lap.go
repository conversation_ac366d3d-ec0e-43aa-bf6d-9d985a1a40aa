package mfl

import (
	"context"
	"strings"

	"finbox/go-api/functions/logger"
	"finbox/go-api/internal/service/myqueue"

	"github.com/jmoiron/sqlx"
)

type MFLLAPMyQueueFetcher struct {
	DB                 *sqlx.DB
	WorkFlowMap        map[string]string
	ReverseWorkflowMap map[string]string
}

func NewMFLLAPMyQueueFetcher(val MFLLAPMyQueueFetcher) *MFLLAPMyQueueFetcher {
	return &MFLLAPMyQueueFetcher{
		DB: val.DB,
	}
}

func (m *MFLLAPMyQueueFetcher) MyQueueLenderDashboard(ctx context.Context, params *myqueue.MyQueueLenderDashboardParams) (*myqueue.MyQueueResponse, error) {

	var (
		dbResp []myqueue.LenderQueueDetail
		resp   myqueue.MyQueueResponse
	)

	var workDBTypes []string
	for _, val := range params.WorkFlowTypes {
		if value, ok := m.WorkFlowMap[val]; ok {
			workDBTypes = append(workDBTypes, value)
		}
	}

	// Base query with joins
	query := `
	SELECT DISTINCT la.loan_application_id, tt.type
	FROM loan_application la
	JOIN users u ON la.user_id = u.user_id
	LEFT JOIN tasks t 
		ON t.identifier_id = la.loan_application_id
	LEFT JOIN task_types tt
		ON t.type_id = tt.id 
	LEFT JOIN workflow_instances wi 
		ON wi.identifier_id = t.id 
	WHERE 1=1
	`

	// Dynamic filters
	var conditions []string
	var args []interface{}

	getBranchCodeCondition, err := m.withBranchCodeCondition(params)
	if err != nil {
		logger.WithContext(ctx).Errorf("[MFLLAPMyQueueFetcher.FetchMyQueueInfo] failed to build branch code condition. query: %v, err: %v", query, err)
		return nil, err
	}

	if getBranchCodeCondition != nil && len(getBranchCodeCondition.Condition) > 0 {
		conditions = append(conditions, getBranchCodeCondition.Condition)
		args = append(args, getBranchCodeCondition.Args...)
	}

	withAssigneeUserCondition := myqueue.WithAssigneeUserCondition(params)
	if withAssigneeUserCondition != nil {
		conditions = append(conditions, withAssigneeUserCondition.Condition)
		args = append(args, withAssigneeUserCondition.Args...)
	}

	getWorkflowStateCondition := myqueue.GetWorkflowStateCondition(params)
	if getWorkflowStateCondition != nil {
		conditions = append(conditions, getWorkflowStateCondition.Condition)
		args = append(args, getWorkflowStateCondition.Args...)
	}

	getTaskTypesCondition, err := withTaskTypes(workDBTypes)
	if err != nil {
		logger.WithContext(ctx).Errorf("[MFLLAPMyQueueFetcher.FetchMyQueueInfo] failed to build task types condition. query: %v, err: %v", query, err)
		return nil, err
	}
	if getTaskTypesCondition != nil && len(getTaskTypesCondition.Condition) > 0 {
		conditions = append(conditions, getTaskTypesCondition.Condition)
		args = append(args, getTaskTypesCondition.Args...)
	}

	// Add conditions to query
	if len(conditions) > 0 {
		query += " AND " + strings.Join(conditions, " AND ")
	}

	// Pagination
	var limit, offset int
	if params.Page < 0 || params.PageSize < 0 {
		logger.WithContext(ctx).Errorf("[MFLLAPMyQueueFetcher.FetchMyQueueInfo] incorrect pagination params: %v", params)
		return nil, err
	}

	// Add pagination only if Page and PageSize are valid
	// Add +1 in PageSize to handle next page availability
	offset = (params.Page - 1) * params.PageSize
	limit = params.PageSize + 1 // Fetch one extra
	args = append(args, limit)
	args = append(args, offset)

	query += " LIMIT ? OFFSET ? "

	query = m.DB.Rebind(query)

	err = m.DB.SelectContext(ctx, &dbResp, query, args...)
	if err != nil {
		logger.WithContext(ctx).Errorf("[MFLLAPMyQueueFetcher.FetchMyQueueInfo] failed to get my queue. query: %v, args: %v, err: %v", query, args, err)
		return nil, err
	}

	if len(dbResp) > params.PageSize {
		resp.LenderQueueDetails = dbResp[:params.PageSize]
		resp.IsNextPageAvailable = true
	} else {
		resp.LenderQueueDetails = dbResp
	}

	return &resp, nil
}

// withBranchCodeCondition handles branch code filtering
func (m *MFLLAPMyQueueFetcher) withBranchCodeCondition(params *myqueue.MyQueueLenderDashboardParams) (*myqueue.QueryCondition, error) {
	if params != nil && len(params.BranchCodes) == 0 {
		return nil, nil
	}

	query, args, err := sqlx.In("(u.dynamic_user_info::jsonb->>'fdgl_code') IN (?)", params.BranchCodes)
	if err != nil {
		return nil, err
	}

	return &myqueue.QueryCondition{
		Condition: query,
		Args:      args,
	}, nil
}

// withTaskTypes handles task type filtering
func withTaskTypes(taskTypes []string) (*myqueue.QueryCondition, error) {
	if len(taskTypes) == 0 {
		return nil, nil
	}

	query, args, err := sqlx.In(`( tt.name ) IN (?)`, taskTypes)
	if err != nil {
		return nil, err
	}

	return &myqueue.QueryCondition{
		Condition: query,
		Args:      args,
	}, nil
}
