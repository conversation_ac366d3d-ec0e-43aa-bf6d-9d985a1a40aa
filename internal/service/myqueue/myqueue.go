package myqueue

import (
	"context"
	"errors"

	"finbox/go-api/constants"
	"finbox/go-api/functions/logger"
)

func FetchMyQueue(ctx context.Context, params *MyQueueLenderDashboardParams) (*MyQueueResponse, error) {

	var (
		resp *MyQueueResponse
		err  error
	)

	if params == nil {
		logger.WithContext(ctx).<PERSON>rrorf("[FetchMyQueue] empty params in FetchMyQueue")
		return nil, errors.New("empty params")
	}

	fetcher := GetQueueFetcher(params.LenderID)
	if fetcher == nil {
		logger.WithContext(ctx).Erro<PERSON>("[FetchMyQueue] queue fetcher not found for params: %v", params)
		return nil, errors.New(ErrQueueFetcherNotFound)
	}

	switch params.DashboardType {
	case constants.LenderDashboardRef:
		resp, err = fetcher.MyQueueLenderDashboard(ctx, params)
		if err != nil {
			logger.WithContext(ctx).<PERSON>rrorf("[FetchMyQueue] failed to fetch my queue. err: %v, params: %v", err, params)
			return nil, err
		}

	default:
		logger.WithContext(ctx).Errorf("[FetchMyQueue] invalid dashboard type: %v", params.DashboardType)
		return nil, errors.New("invalid dashboard type")
	}

	return resp, nil
}
