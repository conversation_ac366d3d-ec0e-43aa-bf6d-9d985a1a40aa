package configmanagement

import (
	"context"
	"database/sql"
	"encoding/json"
	"finbox/go-api/functions/logger"
	"finbox/go-api/thirdparty/karix"
)

func (cmr *ConfigManagementSrvRepository) GetSMSConfigDetails(ctx context.Context, sourceEntityID, lenderID, smsType, smsSubType, userID string) (karix.DltDetails, error) {
	var templateConfig karix.DltDetails

	templateConfigResp, err := cmr.GetConfigInfoByParam(ctx, &GetConfigInfoParam{
		ResourceName: smsType},
		map[string]any{
			"source_entity_id": sourceEntityID,
			"lender_id": lenderID,
			"type":             smsSubType,
			"action_type": smsSubType,
		},
	)

	if err != nil {
		return templateConfig, err
	}

	if err := json.Unmarshal([]byte(templateConfigResp.Config), &templateConfig); err != nil {
		return templateConfig, err
	}

	return templateConfig, nil
}

// GetValidationsOnConfig Retrieves the validation configuration for a specific resource based on the source entity and lender
func (cmr *ConfigManagementSrvRepository) GetValidationsOnConfig(ctx context.Context, sourceEntityID, lenderID, resourceName, validationsOn string) (configMap map[string]interface{}, err error) {

	validationsConfig, err := cmr.GetConfigInfoByParam(ctx, &GetConfigInfoParam{
		ResourceName: resourceName,
	}, map[string]any{
		"source_entity_id": sourceEntityID,
		"lender_id":        lenderID,
		"validations_on":   validationsOn,
	})

	if err != nil {
		logger.WithContext(ctx).Errorln(err)
		return nil, err
	}

	if err := json.Unmarshal([]byte(validationsConfig.Config), &configMap); err != nil {
		logger.WithContext(ctx).Errorln(err)
		return nil, err
	}

	return configMap, nil
}

func (cmr *ConfigManagementSrvRepository) GetEvaluationCodeConfigDetails(ctx context.Context, userID, sourceEntityID, parentSourceEntityID, lenderID, source string) (evaluationCodeQuery map[string]interface{}, err error) {
	// var configMap map[string]interface{}
	evalCodeConfigResp, err := cmr.GetConfigInfoByParam(ctx, &GetConfigInfoParam{
		ResourceName: "eval_code_config"},
		map[string]any{
			"source_entity_id": sourceEntityID,
			"lender_id":        lenderID,
			"source":           source,
		},
	)

	logger.Log.Debug("Eval Code Config By Parama", evalCodeConfigResp)

	if err != nil {
		// If there is no rows, then search with parent source id
		if err == sql.ErrNoRows {
			logger.WithUser(userID).Error("couldn't find evaluation config for source enitity: ", sourceEntityID)
			evalCodeConfigResp, err = cmr.GetConfigInfoByParam(ctx, &GetConfigInfoParam{
				ResourceName: "eval_code_config"},
				map[string]any{
					"source_entity_id": parentSourceEntityID,
					"lender_id":        lenderID,
					"source":           source,
				},
			)

			if err != nil {
				if err == sql.ErrNoRows {
					return evaluationCodeQuery, nil
				}

				return evaluationCodeQuery, err
			}

		}

		return evaluationCodeQuery, err
	}

	if err := json.Unmarshal([]byte(evalCodeConfigResp.Config), &evaluationCodeQuery); err != nil {
		return evaluationCodeQuery, err
	}

	logger.Log.Debug("Eval Code Config Query.....", evaluationCodeQuery)

	return evaluationCodeQuery, nil
}

// GetJQConfigForDataConversion Retrieves the JQ configuration for a specific resource based on the source entity, lender, schemaType and configType
func (cmr *ConfigManagementSrvRepository) GetJQConfigForDataConversion(
	ctx context.Context,
	client, resourceName, schemaType, configType string,
) (configMap string, err error) {

	JQConfig, err := cmr.GetConfigInfoByParam(ctx, &GetConfigInfoParam{
		ResourceName: resourceName,
	}, map[string]any{
		"client":      client,
		"schema_type": schemaType,
		"config_type": configType,
	})

	if err != nil {
		logger.WithContext(ctx).Errorln(err)
		return "", err
	}

	return JQConfig.Config, nil
}

// GetCustomClientFlags Retrieves the CustomClientFlags for a specific resource based on the source entity, lender
// This will govern the client based data conversions
func (cmr *ConfigManagementSrvRepository) GetCustomClientFlags(
	ctx context.Context,
	client, resourceName string,
) (configMap string, err error) {

	CustomClientConfig, err := cmr.GetConfigInfoByParam(ctx, &GetConfigInfoParam{
		ResourceName: resourceName,
	}, map[string]any{
		"client": client,
	})

	if err != nil {
		logger.WithContext(ctx).Errorln(err)
		return "", err
	}

	return CustomClientConfig.Config, nil
}

func (cmr *ConfigManagementSrvRepository) IsAbflBLNonHigherTicketDSA(ctx context.Context, sourceEntityID string) (bool, error) {
	var response bool

	resp, err := cmr.GetConfigInfoByParam(ctx, &GetConfigInfoParam{
		ResourceName: "abfl_bl_non_higher_ticket_dsa"},
		map[string]any{
			"source_entity_id": sourceEntityID,
		},
	)

	if err != nil {
		return false, err
	}

	if err := json.Unmarshal([]byte(resp.Config), &response); err != nil {
		return false, err
	}

	return response, nil
}

// GetCustomClientConfig Retrieves the GetCustomClientConfig for a specific resource based on the source entity, lender
// This will have the details for each conversion, statusCode, encryption, route based schemaType
func (cmr *ConfigManagementSrvRepository) GetCustomClientConfig(
	ctx context.Context, client, resourceName string,
) (configMap string, err error) {

	CustomClientConfig, err := cmr.GetConfigInfoByParam(ctx, &GetConfigInfoParam{
		ResourceName: resourceName,
	}, map[string]any{
		"client": client,
	})

	if err != nil {
		logger.WithContext(ctx).Errorln(err)
		return "", err
	}

	return CustomClientConfig.Config, nil
}

func (cmr *ConfigManagementSrvRepository) GetCustomRouteClient(ctx context.Context, sourceEntityID string) (client string, err error) {
	customClientConfig, err := cmr.GetConfigInfoByParam(ctx, &GetConfigInfoParam{
		ResourceName: ResourceIdentifierCustomRouteClientName,
	}, map[string]any{
		"source_entity_id": sourceEntityID,
	})

	if err != nil {
		logger.WithContext(ctx).Errorln(err)
		return "", err
	}

	return customClientConfig.Config, nil
}
