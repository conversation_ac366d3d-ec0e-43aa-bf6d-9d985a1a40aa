package dashboard

import (
	"context"
	"encoding/json"
	"finbox/go-api/authorization/authProviders"
	"finbox/go-api/constants"
	"finbox/go-api/functions/logger"
	configsql "finbox/go-api/internal/repository/psql/configmanagement"
	"finbox/go-api/thirdparty/redgate"
	"fmt"
	"golang.org/x/exp/maps"
	"golang.org/x/exp/slices"
)

func ProcessUIConfig(ctx context.Context, param ProcessUIConfigParam) (UIConfigResponse, error) {

	var uiConfigResponse UIConfigResponse
	var policy UIConfigPolicy
	var decodedUIElement UIElement
	err := json.Unmarshal([]byte(param.UIConfig), &decodedUIElement)
	if err != nil {
		logger.WithContext(ctx).Errorf("[ProcessUIConfig] failed to marshal UIConfig err: %v, uiConfig: %v", err, param.UIConfig)
		return uiConfigResponse, err
	}

	uiConfigResponse.UIElements = decodedUIElement
	uiConfigResponse.GraphQLResponse = param.GraphQLResponse

	err = ProcessUIElements(ctx, &uiConfigResponse.UIElements, uiConfigResponse.GraphQLResponse)
	if err != nil {
		logger.WithContext(ctx).Errorf("[ProcessUIConfig] failed to process and validate UIElement err: %v, uiConfig: %v", err, param.UIConfig)
		return uiConfigResponse, err
	}

	clientAuthorizer := authProviders.NewAuthorizer(param.LoanInfo.LenderID)
	if clientAuthorizer == nil {
		// Strategy pattern is not applicable for this lender
		return uiConfigResponse, nil
	}
	executePolicyRequest := redgate.ExecutePolicyRequest{
		AppID:             constants.AppIdLendingDashboard,
		AppClientID:       param.LoanInfo.LenderID,
		BearerToken:       param.BearerToken,
		UserID:            param.LoanInfo.UserID,
		LoanApplicationID: param.LoanInfo.LoanApplicationID,
		ResourceName:      param.ResourceName,
	}
	response, err := clientAuthorizer.ExecutePolicy(ctx, executePolicyRequest)
	if err != nil {
		logger.WithContext(ctx).Errorf(err.Error())
		return uiConfigResponse, fmt.Errorf("[ProcessUIConfig] Error during Authorization: %v", err)
	}

	policyJson, _ := json.Marshal(response.Data)
	err = json.Unmarshal(policyJson, &policy)
	if err != nil {
		return uiConfigResponse, fmt.Errorf("[getMFLUiConfigPolicy] Could not parse ui config policy: %v", err)
	}
	err = ApplyConfigPolicy(ctx, &uiConfigResponse.UIElements, policy)
	if err != nil {
		logger.WithContext(ctx).Errorf("[ProcessUIConfig] failed to apply policy err: %v", err)
		return uiConfigResponse, err
	}

	return uiConfigResponse, nil
}

func MergeConfigs(base, override interface{}) interface{} {
	baseMap, isBaseMap := base.(map[string]interface{})
	overrideMap, isOverrideMap := override.(map[string]interface{})
	if isBaseMap && isOverrideMap {
		for key, overrideValue := range overrideMap {
			if baseValue, exists := baseMap[key]; exists {
				baseMap[key] = MergeConfigs(baseValue, overrideValue)
			} else {
				baseMap[key] = overrideValue
			}
		}
		return baseMap
	}

	baseSlice, isBaseSlice := base.([]interface{})
	overrideSlice, isOverrideSlice := override.([]interface{})
	if isBaseSlice && isOverrideSlice {
		mergedSlice := make([]interface{}, len(baseSlice))
		copy(mergedSlice, baseSlice)

		for _, overrideItem := range overrideSlice {
			overrideItemMap, isOverrideMap := overrideItem.(map[string]interface{})
			if !isOverrideMap {
				mergedSlice = append(mergedSlice, overrideItem)
				continue
			}

			found := false
			for i, baseItem := range mergedSlice {
				baseItemMap, isBaseMap := baseItem.(map[string]interface{})
				if isBaseMap && baseItemMap["id"] == overrideItemMap["id"] {
					mergedSlice[i] = MergeConfigs(baseItemMap, overrideItemMap)
					found = true
					break
				}
			}

			if !found {
				mergedSlice = append(mergedSlice, overrideItem)
			}
		}

		return mergedSlice
	}

	return override
}

func ProcessAndMergeConfigs(configs []configsql.DBGetConfigInfoByParamResponse) (string, error) {
	if len(configs) == 0 {
		return "", fmt.Errorf("no configs found")
	}

	if len(configs) > 2 {
		return "", fmt.Errorf("invalid number of configs: expected 1 or 2, got %d", len(configs))
	}

	// If only one config, return it directly
	if len(configs) == 1 {
		return configs[0].Config, nil
	}

	// Find default config and override config
	var baseConfig, overrideConfig configsql.DBGetConfigInfoByParamResponse
	for _, cfg := range configs {
		if cfg.KeyCombinationVal == "default|default" {
			baseConfig = cfg
		} else {
			overrideConfig = cfg
		}
	}

	// Verify we have the default config
	if baseConfig.Config == "" {
		return "", fmt.Errorf("default config not found")
	}

	// Convert default config to map
	var baseConfigMap map[string]interface{}
	err := json.Unmarshal([]byte(baseConfig.Config), &baseConfigMap)
	if err != nil {
		return "", fmt.Errorf("failed to unmarshal default config: %v", err)
	}

	// Convert override config to map
	var overrideConfigMap map[string]interface{}
	err = json.Unmarshal([]byte(overrideConfig.Config), &overrideConfigMap)
	if err != nil {
		return "", fmt.Errorf("failed to unmarshal override config: %v", err)
	}

	// Merge configs
	mergedConfig := MergeConfigs(baseConfigMap, overrideConfigMap).(map[string]interface{})

	// Convert merged config back to string
	resultJSON, err := json.Marshal(mergedConfig)
	if err != nil {
		return "", fmt.Errorf("failed to marshal merged config: %v", err)
	}

	return string(resultJSON), nil
}

func ApplyConfigPolicy(ctx context.Context, uiElement *UIElement, policy UIConfigPolicy) error {
	uiSections := uiElement.UIConfig.Sections
	for buttonLabel, buttonRule := range policy.Buttons {
		button, ok := uiElement.UIConfig.Buttons[buttonLabel]
		if !ok {
			logger.WithContext(ctx).Warnf("[ProcessUIConfig] failed to find button %s in section button list %v", buttonLabel, uiElement.UIConfig.Buttons)
			continue
		}
		if buttonRule.IsVisible != nil {
			button.Visible.IsVisible = *buttonRule.IsVisible
		}
		uiElement.UIConfig.Buttons[buttonLabel] = button
	}

	for sectionID, sectionRule := range policy.Sections {
		section, ok := uiSections[sectionID]
		if !ok {
			logger.WithContext(ctx).Warnf("Could not find section ID %s in uiSections. Keys: %v", sectionID, maps.Keys(uiSections))
		}
		if sectionRule.IsVisible != nil {
			section.Visible.IsVisible = *sectionRule.IsVisible
		}
		uiSections[sectionID] = section

		for fieldID, fieldRule := range sectionRule.Fields {
			fieldIndex := slices.IndexFunc(section.FieldList, func(field Field) bool {
				return field.ID == fieldID
			})
			if fieldIndex == -1 {
				logger.WithContext(ctx).Warnf("[ProcessUIConfig] failed to find field %s in section field list %v", fieldID, section.FieldList)
				continue
			}
			field := section.FieldList[fieldIndex]
			if fieldRule.IsViewable != nil {
				field.Visible.IsVisible = *fieldRule.IsViewable
			}
			if fieldRule.IsEditable != nil {
				field.Editable.IsEditable = *fieldRule.IsEditable
			}
			section.FieldList[fieldIndex] = field
		}

		for buttonLabel, buttonRule := range sectionRule.Buttons {
			button, ok := section.ButtonList[buttonLabel]
			if !ok {
				logger.WithContext(ctx).Warnf("[ProcessUIConfig] failed to find button %s in section button list %v", buttonLabel, section.ButtonList)
				continue
			}
			if buttonRule.IsVisible != nil {
				button.Visible.IsVisible = *buttonRule.IsVisible
			}
			section.ButtonList[buttonLabel] = button
		}
	}
	return nil
}
