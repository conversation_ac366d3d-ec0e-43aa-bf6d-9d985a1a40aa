package documents

import (
	"context"
	"finbox/go-api/constants"
	"finbox/go-api/infra/s3"
	documentsql "finbox/go-api/internal/repository/psql/document"
	"finbox/go-api/models/dashboard"
	"finbox/go-api/utils/general"
	"finbox/go-api/utils/kycutils"
	"fmt"
	"github.com/samber/lo"
	"path/filepath"
)

// GetAdditionalDashboardDocuments is the service level function that calls the DB function and processes the results
func GetAdditionalDashboardDocuments(ctx context.Context, loanApplicationID string, entityTypes []string) ([]dashboard.DocsInfo, error) {
	// Call the DB function
	param := &documentsql.DBGetDashboardDocsParam{
		LoanApplicationID: loanApplicationID,
		EntityTypes:       entityTypes,
	}

	docsInfo, err := documentsql.DBGetAdditionalDashboardDocuments(ctx, param)
	if err != nil {
		return nil, err
	}

	// Process the documents with the common function
	return processDocsInfo(docsInfo, loanApplicationID, "additional_docs")
}

func processDocsInfo(docsInfo []dashboard.DBDocsInfo, loanApplicationNo string, fileNamePrefix string) ([]dashboard.DocsInfo, error) {
	currentIndex := -1
	var docsInfoObj []dashboard.DocsInfo
	docMap := make(map[string]int)

	for i, docInfo := range docsInfo {
		extension := filepath.Ext(docInfo.MediaPath)
		customFileName := fmt.Sprintf("%s_%s_%d%s", loanApplicationNo, fileNamePrefix, i+1, extension)
		if index, found := docMap[docInfo.DocID]; found {
			doc := docsInfoObj[index]
			doc.DocType = docInfo.DocType
			urls := doc.URLs
			urls = append(urls, s3.GetPresignedURLS3CustomName(docInfo.MediaPath, 60, customFileName))
			docsInfoObj[index].URLs = urls
		} else {
			docsInfoObj = append(docsInfoObj, dashboard.DocsInfo{
				DocID:        docInfo.DocID,
				DocType:      docInfo.DocType,
				Comment:      docInfo.Comment,
				URLs:         []string{s3.GetPresignedURLS3CustomName(docInfo.MediaPath, 60, customFileName)},
				ReviewStatus: constants.AdditionalKYCDocStatusText[docInfo.ReviewStatus],
			})
			currentIndex += 1
			docMap[docInfo.DocID] = currentIndex
		}
	}
	if docsInfoObj == nil {
		return []dashboard.DocsInfo{}, nil
	}

	return docsInfoObj, nil
}

func GetAdditionalKYCDocs(ctx context.Context, loanApplicationID, lenderID, userID string) ([]kycutils.LoanKYCDataResp, error) {

	additionalDocs, err := documentsql.GetAdditionalKYCDocs(ctx, loanApplicationID, lenderID, userID)
	if err != nil {
		return nil, err
	}

	// Convert to DocumentDataInterface
	docsInterface := make([]documentsql.DocumentDataInterface, len(additionalDocs))
	for i, doc := range additionalDocs {
		docsInterface[i] = documentsql.DocumentDataInterface{
			DocumentID:       doc.DocumentID,
			DocumentCategory: doc.DocumentCategory,
			DocumentName:     doc.DocumentName,
			Path:             doc.Path,
			ReviewStatus:     doc.ReviewStatus,
			Password:         doc.Password,
			CreatedAt:        doc.CreatedAt,
		}
	}

	kycDataResp, err := processDocuments(docsInterface)
	if err != nil {
		return nil, err
	}

	return lo.Map(kycDataResp, func(item kycutils.KYCDataResp, _ int) kycutils.LoanKYCDataResp {
		return kycutils.LoanKYCDataResp{
			KYCDataResp: item,
		}
	}), nil
}

func GetDashboardDocumentsWithMedia(ctx context.Context, loanApplicationID, userID string) ([]kycutils.KYCDataResp, error) {
	dashboardDocs, err := documentsql.DBGetDashboardDocumentsWithMedia(ctx, &documentsql.DBGetDashboardDocumentsParam{
		LoanApplicationID: loanApplicationID,
	})
	dashboardDocs = lo.Filter(dashboardDocs, func(doc documentsql.DBGetUserDocumentsResponse, _ int) bool {
		return doc.UserID == userID
	})
	if err != nil {
		return nil, err
	}

	// Convert to DocumentDataInterface
	docsInterface := make([]documentsql.DocumentDataInterface, len(dashboardDocs))
	for i, doc := range dashboardDocs {
		docsInterface[i] = documentsql.DocumentDataInterface{
			DocumentID:       doc.DocumentID,
			DocumentCategory: doc.DocumentCategory,
			DocumentName:     doc.DocumentName,
			Path:             doc.Path,
			ReviewStatus:     doc.ReviewStatus,
			Password:         doc.Password,
			DocID:            doc.DocID,
			CreatedAt:        doc.CreatedAt,
		}
	}

	return processDocuments(docsInterface)
}

// Common processing function that works with the interface struct
func processDocuments(docs []documentsql.DocumentDataInterface) ([]kycutils.KYCDataResp, error) {
	var result []kycutils.KYCDataResp
	for _, doc := range docs {
		url := s3.GetPresignedURLS3(doc.Path, 60)
		statusText := constants.AdditionalKYCDocStatusText[doc.ReviewStatus]
		rejectionReason := ""
		if general.InArr(doc.ReviewStatus, []int{constants.DocumentNotVerified, constants.DocumentUnderReview}) {
			rejectionReason = statusText
		}
		isEditable := true
		if doc.DocumentName == constants.DocumentNameAdditionalDocUANFromAPI {
			isEditable = false
		}
		result = append(result,
			kycutils.KYCDataResp{
				FrontMediaID:    url,
				DocType:         doc.DocumentCategory,
				DocumentName:    doc.DocumentName,
				DocumentID:      doc.DocumentID,
				RejectionReason: rejectionReason,
				StatusText:      statusText,
				IsEditable:      isEditable,
				Password:        doc.Password,
				DocID:           doc.DocID,
				CreatedAt:       doc.CreatedAt,
			})
	}
	return result, nil
}
