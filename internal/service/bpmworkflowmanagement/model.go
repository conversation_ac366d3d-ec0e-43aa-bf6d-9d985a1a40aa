package bpmworkflowmanagement

import (
	"context"

	"github.com/jmoiron/sqlx"
)

type WorkflowActionResponse struct {
	Data map[string]interface{}
}

// WorkflowFunctionSignature defines the signature for functions executed by actions.
type WorkflowFunctionSignature func(ctx context.Context, workflowCtx *ActionFuncRefExecReq) (*WorkflowActionResponse, error)

// FunctionRegistryMap maps workflow names to a map of function names and their Go implementations.
var FunctionRegistryMap = make(map[string]map[string]WorkflowFunctionSignature)

type Error struct {
	status  int
	message string
}

func NewError(status int, msg string) *Error {
	return &Error{
		status:  status,
		message: msg,
	}
}

func (e *Error) Error() string {
	return e.message
}
func (e *Error) Status() int {
	return e.status
}

type Workflow struct {
	ID           string           `json:"id"`
	Name         string           `json:"name"`
	Version      string           `json:"version"`
	InitialState string           `json:"initialState"`
	States       map[string]State `json:"states"`
}

type StateType string

// TODO: confirm state types names
const (
	InitialState   StateType = "initial"
	TerminalState  StateType = "terminal"
	OperationState StateType = "operation"
)

// State struct definition
type State struct {
	ID           string       `json:"id"`
	Name         string       `json:"name"`
	Type         StateType    `json:"type"`
	Permissions  Permissions  `json:"permissions"`
	Reassignment Reassignment `json:"reassignment"` // Reassignment struct added here
	Actions      []Action     `json:"actions"`
}

// Permissions struct definition
type Permissions struct {
	AllowedGroups []string `json:"allowedGroups"`
}

// Reassignment struct definition
type Reassignment struct {
	ReassignmentGroups []string `json:"reassignmentGroups"`
	FuncRef            *FuncRef `json:"funcRef"`
}

type Action struct {
	ID              string           `json:"id"`
	Name            string           `json:"name"`
	Permissions     Permissions      `json:"permissions"`
	FuncRef         *FuncRef         `json:"funcRef"`
	TaskEvent       *TaskEvent       `json:"taskEvent"`
	Transitions     []Transition     `json:"transitions"`
	ValidateActions []ValidateAction `json:"validateActions"`
}

type ValidateAction struct {
	ID            string      `json:"id"`
	Name          string      `json:"name"`
	Description   string      `json:"description"`
	WorkflowID    string      `json:"workflowID"`
	ValidateValue interface{} `json:"validateValue"`
	JQ            string      `json:"jq"`
	DataSourceKey string      `json:"dataSourceKey"`
	FailedMessage string      `json:"failedMessage"`
}

type TaskEvent struct {
	Event          string      `json:"event"`
	AdditionalData interface{} `json:"additionalData"`
}

type FuncRef struct {
	Name       string                 `json:"name"`
	Parameters map[string]interface{} `json:"parameters,omitempty"` // Parameters are optional
}

type Transition struct {
	Next TransitionStateInfo `json:"next"`
}

type TransitionStateInfo struct {
	StateName string `json:"stateName"`
	GroupName string `json:"groupName"`
}

type ActionResult struct { // New struct to combine Transition and FuncRef
	Transition      *Transition      `json:"transition"`
	FuncRef         *FuncRef         `json:"funcRef"`
	TaskEvent       *TaskEvent       `json:"taskEvent"`
	ValidateActions []ValidateAction `json:"validateActions"`
}

// StateActionsTransitions model to hold actions and transitions for a state
type StateActionsTransitions struct {
	StateID     string
	StateName   string
	Actions     []Action
	Transitions []Transition
}

type ActionFuncRefExecReq struct { // New struct to combine Transition and FuncRef
	WorkflowName string   `json:"workflowName"`
	FuncRef      *FuncRef `json:"funcRef"`
	Tx           *sqlx.Tx
	MetaData     map[string]interface{} `json:"metadata,omitempty"` // Optional metadata for the action
}

// GenericInitiateWorkflowReq model to initiate a workflow
type GenericInitiateWorkflowReq struct {
	WorkflowConfig     string
	ConfigResourceName string
	ConfigResourceMap  map[string]interface{}
	IdentifierType     string
	IdentifierID       string
	LoanApplicationID  string
	Remarks            string
	Metadata           map[string]interface{}
}

// GenericInitiateWorkflowResponse model to return workflow response
type GenericInitiateWorkflowResponse struct {
	WorkflowInstanceID string `json:"workflowInstanceId"`
}
