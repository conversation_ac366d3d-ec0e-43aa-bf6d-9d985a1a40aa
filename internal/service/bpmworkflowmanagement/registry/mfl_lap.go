package registry

import (
	"finbox/go-api/infra/db"
	loansql "finbox/go-api/internal/repository/psql/loan"
	taskmanagementsql "finbox/go-api/internal/repository/psql/taskmanagement"
	usersql "finbox/go-api/internal/repository/psql/user"
	workflowinstancessql "finbox/go-api/internal/repository/psql/workflowinstance"
	mflbpmworkflows "finbox/go-api/internal/service/bpmworkflowmanagement/bpmworkflows/mfl"
)

func initMFLLAPWF() {

	wfRepo := workflowinstancessql.NewWorkflowInstancesRepository(db.GetDB())
	taskRepo := taskmanagementsql.NewTaskManagementDBRepository(db.GetDB())
	loanRepo := loansql.NewLoanDBRepository(db.GetDB())
	userRepo := usersql.NewUserDBRepository(db.GetDB())
	sanctionWFExecuter := mflbpmworkflows.NewMflLapWorkflowExecuter(wfRepo, taskRepo, loanRepo, userRepo)
	//initiate mfl lap workflow
	RegisterFunction("mfl_lap_workflow", "InitiateMFlLAPWFFunc", sanctionWFExecuter.InitiateMFlLAPWFFunc)

	//ro related functions
	RegisterFunction("mfl_lap_workflow", "MoveROToCOFunc", sanctionWFExecuter.MoveROToCOFunc)
	RegisterFunction("mfl_lap_workflow", "MoveCOToBPOFunc", sanctionWFExecuter.MoveCOToBPOFunc)
	RegisterFunction("mfl_lap_workflow", "InitiateLegalFunc", sanctionWFExecuter.InitiateLegalFunc)
	RegisterFunction("mfl_lap_workflow", "InitiateRCUFunc", sanctionWFExecuter.InitiateRCUFunc)
	RegisterFunction("mfl_lap_workflow", "InitiateTechnicalFunc", sanctionWFExecuter.InitiateTechnicalFunc)

	// co related functions
	RegisterFunction("mfl_lap_workflow", "MoveCOToROFunc", sanctionWFExecuter.MoveCOToROFunc)
	RegisterFunction("mfl_lap_workflow", "MoveCOToBPOFunc", sanctionWFExecuter.MoveCOToBPOFunc)

	// bpo related functions
	RegisterFunction("mfl_lap_workflow", "MoveBPOToBCMFunc", sanctionWFExecuter.MoveBPOToBCMFunc)
	RegisterFunction("mfl_lap_workflow", "MoveBPOToCOFunc", sanctionWFExecuter.MoveBPOToCOFunc)

	// bcm related functions
	RegisterFunction("mfl_lap_workflow", "ValidateBCMApproveFunc", sanctionWFExecuter.ValidateBCMApproveFunc)
	RegisterFunction("mfl_lap_workflow", "ValidateBCMRejectFunc", sanctionWFExecuter.ValidateBCMRejectFunc)
	RegisterFunction("mfl_lap_workflow", "InitiateLegalFunc", sanctionWFExecuter.InitiateLegalFunc)
	RegisterFunction("mfl_lap_workflow", "InitiateRCUFunc", sanctionWFExecuter.InitiateRCUFunc)
	RegisterFunction("mfl_lap_workflow", "InitiateTechnicalFunc", sanctionWFExecuter.InitiateTechnicalFunc)

	RegisterFunction("mfl_bcm_technical_workflow", "InitiateTechnicalBCMFunc", sanctionWFExecuter.InitiateTechnicalBCMFunc)
	RegisterFunction("mfl_bcm_technical_workflow", "RecommendedFunc", sanctionWFExecuter.RecommendedFunc)
	RegisterFunction("mfl_bcm_legal_workflow", "InitiateLegalBCMFunc", sanctionWFExecuter.InitiateLegalBCMFunc)
	RegisterFunction("mfl_bcm_rcu_workflow", "InitiateRCUBCMFunc", sanctionWFExecuter.InitiateRCUBCMFunc)
	RegisterFunction("mfl_bcm_legal_workflow", "InitiateLegalBCMFunc", sanctionWFExecuter.InitiateLegalBCMFunc)
	RegisterFunction("mfl_bcm_rcu_workflow", "InitiateRCUBCMFunc", sanctionWFExecuter.InitiateRCUBCMFunc)
	RegisterFunction("manual_credit_review_workflow", "ApproveManualCreditReviewFunc", sanctionWFExecuter.ApproveManualCreditReviewFunc)
	RegisterFunction("manual_credit_review_workflow", "RejectManualCreditReviewFunc", sanctionWFExecuter.RejectManualCreditReviewFunc)
	RegisterFunction("manual_credit_review_workflow", "MoveManualCreditReviewTaskUpwardsFunc", sanctionWFExecuter.MoveManualCreditReviewTaskUpwardsFunc)
	RegisterFunction("manual_credit_review_workflow", "MoveManualCreditReviewTaskBelowFunc", sanctionWFExecuter.MoveManualCreditReviewTaskBelowFunc)
	RegisterFunction("mfl_bcm_rcu_workflow", "MoveRCUTORCUM", sanctionWFExecuter.MoveRCUTORCUM)
	RegisterFunction("mfl_bcm_rcu_workflow", "CheckForDocumentScreening", sanctionWFExecuter.CheckForDocumentScreening)
}
