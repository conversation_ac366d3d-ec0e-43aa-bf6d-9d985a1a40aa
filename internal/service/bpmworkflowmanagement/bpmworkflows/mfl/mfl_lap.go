package mflworkflows

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"finbox/go-api/authentication"
	"finbox/go-api/constants"
	"finbox/go-api/functions/logger"
	"finbox/go-api/functions/taskmanagement/event"
	"finbox/go-api/infra/db"
	"finbox/go-api/internal/repository/psql"
	documentsql "finbox/go-api/internal/repository/psql/document"
	loansql "finbox/go-api/internal/repository/psql/loan"
	taskmanagementsql "finbox/go-api/internal/repository/psql/taskmanagement"
	usersql "finbox/go-api/internal/repository/psql/user"
	workflowinstancessql "finbox/go-api/internal/repository/psql/workflowinstance"
	"finbox/go-api/internal/service/bpmworkflowmanagement"
	mdashboard "finbox/go-api/models/dashboard"
	"finbox/go-api/utils/general"
	"fmt"
)

type mflLapWorkflowContext struct {
	WorkflowInstanceID string
	User               authentication.DashboardUser
	Group              string
	NextGroup          string
	LoanApplicationID  string
	TaskID             string
	Token              string
}

type mflLapWorkflowExecuter struct {
	wfRepo   workflowinstancessql.WorkflowInstancesRepositoryProvider
	taskRepo taskmanagementsql.TaskManagementDBRepositoryProvider
	loanRepo loansql.LoanDBRepositoryProvider
	userRepo usersql.UserDBRepositoryProvider
}

func NewMflLapWorkflowExecuter(
	wfRepo workflowinstancessql.WorkflowInstancesRepositoryProvider,
	taskRepo taskmanagementsql.TaskManagementDBRepositoryProvider,
	loanRepo loansql.LoanDBRepositoryProvider,
	userRepo usersql.UserDBRepositoryProvider,
) *mflLapWorkflowExecuter {
	return &mflLapWorkflowExecuter{
		wfRepo:   wfRepo,
		taskRepo: taskRepo,
		loanRepo: loanRepo,
		userRepo: userRepo,
	}
}

var ErrInvalidSanctionWorkflowContext = ("invalid sanction workflow context")

func NewMflLapWorkflowContext(ctx context.Context, mp map[string]interface{}) (*mflLapWorkflowContext, error) {
	var (
		workflowInstanceID string
		user               authentication.DashboardUser
		group              string
		loanApplicationID  string
		taskId             string
		ok                 bool
		nextGroup          string
		token              string
	)

	if workflowInstanceIDVal, ok := mp["workflowInstanceID"]; ok {
		if workflowInstanceID, ok = workflowInstanceIDVal.(string); !ok {
			logger.WithContext(ctx).Errorf("[NewMflLapWorkflowContext] Invalid workflowInstanceID type. param: %+v", workflowInstanceIDVal)
			return nil, fmt.Errorf("invalid sanction workflow context")
		}
	} else {
		logger.WithContext(ctx).Errorf("[NewMflLapWorkflowContext] Missing workflowInstanceID")
		return nil, fmt.Errorf("invalid sanction workflow context")
	}

	if userVal, ok := mp["user"]; ok {
		if user, ok = userVal.(authentication.DashboardUser); !ok {
			logger.WithContext(ctx).Errorf("[NewMflLapWorkflowContext] Invalid user type. param: %+v", userVal)
			return nil, fmt.Errorf("invalid sanction workflow context")
		}
	} else {
		logger.WithContext(ctx).Errorf("[NewMflLapWorkflowContext] Missing user")
		return nil, fmt.Errorf("invalid sanction workflow context")
	}

	if groupVal, ok := mp["group"]; ok {
		if group, ok = groupVal.(string); !ok {
			logger.WithContext(ctx).Errorf("[NewMflLapWorkflowContext] Invalid group type. param: %+v", groupVal)
			return nil, fmt.Errorf("invalid sanction workflow context")
		}
	} else {
		logger.WithContext(ctx).Infof("[NewMflLapWorkflowContext] Missing group")
		return nil, fmt.Errorf("invalid sanction workflow context")
	}
	if loanApplicationID, ok = mp["loanApplicationID"].(string); !ok {
		logger.WithContext(ctx).Errorf("[NewMflLapWorkflowContext] Invalid group type. param: %+v", mp)
		return nil, fmt.Errorf("invalid sanction workflow context")
	}

	if val, ok := mp["taskID"]; ok {
		if taskId, ok = val.(string); !ok {
			logger.WithContext(ctx).Errorf("[NewMflLapWorkflowContext] Invalid taskID type, param: %+v", mp)
			return nil, fmt.Errorf("invalid type for taskID")
		}
	}

	if nextGroupVal, ok := mp["nextGroup"]; ok {
		if nextGroup, ok = nextGroupVal.(string); !ok {
			logger.WithContext(ctx).Errorf("[NewMflLapWorkflowContext] Invalid next group type. param: %+v", nextGroupVal)
			return nil, fmt.Errorf("invalid sanction workflow context")
		}
	} else {
		logger.WithContext(ctx).Infof("[NewMflLapWorkflowContext] Missing next group")
	}

	if tokenVal, ok := mp["token"]; ok {
		if token, ok = tokenVal.(string); !ok {
			logger.WithContext(ctx).Errorf("[NewMflLapWorkflowContext] Invalid token. param: %+v", tokenVal)
			return nil, fmt.Errorf("invalid token")
		}
	} else {
		logger.WithContext(ctx).Infof("[NewMflLapWorkflowContext] Missing token")
	}

	return &mflLapWorkflowContext{
		WorkflowInstanceID: workflowInstanceID,
		User:               user,
		Group:              group,
		LoanApplicationID:  loanApplicationID,
		TaskID:             taskId,
		NextGroup:          nextGroup,
		Token:              token,
	}, nil
}

func (svc *mflLapWorkflowExecuter) InitiateMFlLAPWFFunc(ctx context.Context, workflowCtx *bpmworkflowmanagement.ActionFuncRefExecReq) (*bpmworkflowmanagement.WorkflowActionResponse, error) {
	return nil, nil
}

func (svc *mflLapWorkflowExecuter) MoveROToCOFunc(ctx context.Context, workflowCtx *bpmworkflowmanagement.ActionFuncRefExecReq) (*bpmworkflowmanagement.WorkflowActionResponse, error) {
	return nil, nil
}

func (svc *mflLapWorkflowExecuter) MoveCOToBPOFunc(ctx context.Context, workflowCtx *bpmworkflowmanagement.ActionFuncRefExecReq) (*bpmworkflowmanagement.WorkflowActionResponse, error) {
	logger.WithContext(ctx).Debug("Starting MoveCOToBPOFunc")

	wfData, err := NewMflLapWorkflowContext(ctx, workflowCtx.FuncRef.Parameters)
	if err != nil {
		logger.WithContext(ctx).Errorf("[MoveCOToBPOFunc] Error creating workflow context: %v", err)
		return nil, err
	}

	masterTaskList, err := svc.getMasterTasksForBPO(ctx)
	if err != nil {
		return nil, err
	}

	loanApplication, err := svc.loanRepo.DBGetLoanApplicationByParams(ctx, loansql.DBGetLoanApplicationParam{LoanApplicationID: wfData.LoanApplicationID})
	if err != nil {
		logger.WithContext(ctx).Errorf("[MoveCOToBPOFunc] Error getting loan application: %v", err)
		return nil, err
	}

	// Get co-applicant list
	coAppsList, err := usersql.DBGetMultiUserLoanRelationsListByParam(ctx, usersql.DBGetMultiUserLoanRelationsParam{ParentUserID: loanApplication.UserID})
	if err != nil {
		logger.WithContext(ctx).Errorf("[MoveCOToBPOFunc] Error getting loan relations: %v", err)
		return nil, err
	}

	err = svc.processTasks(ctx, masterTaskList, coAppsList, &loanApplication)
	if err != nil {
		return nil, err
	}

	return nil, nil
}

func (svc *mflLapWorkflowExecuter) getMasterTasksForBPO(ctx context.Context) ([]taskmanagementsql.DBGetTaskMasterResponse, error) {
	taskTypeName := mdashboard.MFL_BPO_Verifications

	taskType, err := svc.taskRepo.DBGetTaskTypesByParams(ctx, &taskmanagementsql.DBGetTaskTypesParam{
		Name: &taskTypeName,
	}, nil)
	if err != nil {
		logger.WithContext(ctx).Errorf("[MoveCOToBPOFunc] Error getting task types: %v", err)
		return nil, err
	}

	masterTasks, err := taskmanagementsql.DBListTaskMasterByParams(ctx, &taskmanagementsql.DBListTaskMasterParam{TypeId: &taskType.Id}, nil, nil)
	if err != nil {
		logger.WithContext(ctx).Errorf("[MoveCOToBPOFunc] Error listing tasks: %v", err)
		return nil, err
	}

	return masterTasks, nil
}

func (svc *mflLapWorkflowExecuter) processTasks(
	ctx context.Context,
	masterTaskList []taskmanagementsql.DBGetTaskMasterResponse,
	coAppsList []usersql.DBGetMultiUserLoanRelationsResponse,
	loanApplication *loansql.DBGetLoanApplicationResponse,
) error {
	for _, masterTask := range masterTaskList {
		metadataMap, err := unmarshalTaskMetadata(ctx, masterTask.Metadata)
		if err != nil {
			continue
		}

		applicableFor, ok := metadataMap["applicableFor"].(string)
		if !ok {
			logger.WithContext(ctx).Error("Error getting applicableFor from metadata")
			continue
		}
		primaryUser, err := svc.userRepo.DBGetUserByParamsV2(ctx, usersql.DBGetUserInfoParam{UserID: loanApplication.UserID})
		if err != nil {
			logger.WithContext(ctx).Errorf("[processTasks] Error fetching Primary User details")
			return err
		}
		location := primaryUser.DynamicUserInfo.ReferBranchName

		switch applicableFor {
		case Primary:
			if err := svc.createTaskForUser(ctx, masterTask, metadataMap, &primaryUser, loanApplication, location); err != nil {
				return err
			}
		case CoApplicant:
			for _, coApp := range coAppsList {
				coAppUser, err := svc.userRepo.DBGetUserByParamsV2(ctx, usersql.DBGetUserInfoParam{UserID: coApp.UserID})
				if err != nil {
					logger.WithContext(ctx).Errorf("Error getting co-applicant user info: %v", err)
					continue
				}

				if err := svc.createTaskForUser(ctx, masterTask, metadataMap, &coAppUser, loanApplication, location); err != nil {
					return err
				}
			}
		}
	}

	return nil
}

// Helper function to create a task for a specific user
func (svc *mflLapWorkflowExecuter) createTaskForUser(
	ctx context.Context,
	masterTask taskmanagementsql.DBGetTaskMasterResponse,
	baseMetadata map[string]interface{},
	user *usersql.DBUserDetailResponse,
	loanApplication *loansql.DBGetLoanApplicationResponse,
	location string,
) error {
	taskClone := masterTask
	metadataMapClone := cloneMetadata(baseMetadata)

	metadataMapClone["name"] = user.Name
	metadataMapClone["gender"] = user.Gender
	metadataMapClone["mobile_number"] = user.Mobile
	metadataMapClone["dob"] = user.DOB
	metadataMapClone["application_id"] = loanApplication.LoanApplicationID
	metadataMapClone["location"] = location

	metadataBytes, err := json.Marshal(metadataMapClone)
	if err != nil {
		logger.WithContext(ctx).Errorf("[MoveCOToBPOFunc] Error marshalling metadata: %v", err)
		return nil
	}

	taskClone.Metadata = string(metadataBytes)
	taskParams := event.NewTaskFromMasterTask(ctx, taskClone, "System", loanApplication.LoanApplicationID, "LOAN_APPLICATION_ID")

	if err := taskmanagementsql.DBInsertTasks(ctx, nil, &taskParams); err != nil {
		logger.WithContext(ctx).Errorf("[MoveCOToBPOFunc] Error inserting tasks: %v", err)
		return err
	}

	return nil
}

// Helper function to unmarshal task metadata
func unmarshalTaskMetadata(ctx context.Context, metadata string) (map[string]interface{}, error) {
	var metadataMap map[string]interface{}
	if err := json.Unmarshal([]byte(metadata), &metadataMap); err != nil {
		logger.WithContext(ctx).Errorf("Error unmarshalling metadata: %v", err)
		return nil, err
	}
	return metadataMap, nil
}

// Helper function to clone metadata map
func cloneMetadata(original map[string]interface{}) map[string]interface{} {
	clone := make(map[string]interface{})
	for k, v := range original {
		clone[k] = v
	}
	return clone
}
func (svc *mflLapWorkflowExecuter) MoveCOToROFunc(ctx context.Context, workflowCtx *bpmworkflowmanagement.ActionFuncRefExecReq) (*bpmworkflowmanagement.WorkflowActionResponse, error) {
	return nil, nil
}

func (svc *mflLapWorkflowExecuter) InitiateLegalFunc(ctx context.Context, workflowCtx *bpmworkflowmanagement.ActionFuncRefExecReq) (*bpmworkflowmanagement.WorkflowActionResponse, error) {
	return nil, nil
}

func (svc *mflLapWorkflowExecuter) InitiateRCUFunc(ctx context.Context, workflowCtx *bpmworkflowmanagement.ActionFuncRefExecReq) (*bpmworkflowmanagement.WorkflowActionResponse, error) {
	return nil, nil
}

func (svc *mflLapWorkflowExecuter) InitiateTechnicalFunc(ctx context.Context, workflowCtx *bpmworkflowmanagement.ActionFuncRefExecReq) (*bpmworkflowmanagement.WorkflowActionResponse, error) {
	return nil, nil
}

func (svc *mflLapWorkflowExecuter) MoveBPOToBCMFunc(ctx context.Context, workflowCtx *bpmworkflowmanagement.ActionFuncRefExecReq) (*bpmworkflowmanagement.WorkflowActionResponse, error) {
	return nil, nil
}

func (svc *mflLapWorkflowExecuter) MoveBPOToCOFunc(ctx context.Context, workflowCtx *bpmworkflowmanagement.ActionFuncRefExecReq) (*bpmworkflowmanagement.WorkflowActionResponse, error) {
	return nil, nil
}

func (svc *mflLapWorkflowExecuter) ValidateBCMApproveFunc(ctx context.Context, workflowCtx *bpmworkflowmanagement.ActionFuncRefExecReq) (*bpmworkflowmanagement.WorkflowActionResponse, error) {
	return nil, nil
}

// Main workflow function - now much cleaner
func (svc *mflLapWorkflowExecuter) MoveManualCreditReviewTaskUpwardsFunc(ctx context.Context, workflowCtx *bpmworkflowmanagement.ActionFuncRefExecReq) (*bpmworkflowmanagement.WorkflowActionResponse, error) {

	wfData, err := NewMflLapWorkflowContext(ctx, workflowCtx.FuncRef.Parameters)
	if err != nil {
		logger.WithContext(ctx).Errorf("[MoveManualCreditReviewTaskUpwardsFunc] Err creating context err: %+v, params %+v:", err, workflowCtx.FuncRef.Parameters)
		return nil, fmt.Errorf("failed to create workflow context: %v", err)
	}

	// Fetch loan application details
	var loanParam loansql.DBGetLoanApplicationParam
	loanParam.LoanApplicationID = wfData.LoanApplicationID

	loanRepo := loansql.NewLoanDBRepository(psql.Database)
	loanInfo, err := loanRepo.DBGetLoanApplicationByParams(ctx, loanParam)
	if err != nil {
		logger.WithContext(ctx).Errorf("[MoveManualCreditReviewTaskUpwardsFunc] failed to fetch loan application. err: %v, loanInfoParam: %+v", err, loanParam)
		return nil, fmt.Errorf("failed to fetch loan application")
	}

	// Validate all required tasks are resolved
	requiredTasks := []mdashboard.TaskType{
		mdashboard.BCM_Verification,
		mdashboard.BCM_TASKS,
	}
	if err := validateTasksResolved(ctx, wfData, requiredTasks); err != nil {
		return nil, err
	}

	// Validate all deviations are approved
	if err := validateDeviationsResolved(ctx, wfData); err != nil {
		return nil, err
	}

	// Validate CAM report exists
	if err := validateCAMReportExists(ctx, wfData); err != nil {
		return nil, err
	}

	// Validate co-applicant exists
	if err := validateCoApplicantExists(ctx, &loanInfo); err != nil {
		return nil, err
	}

	// Validate loan offer details
	if err := validateLoanOfferDetails(ctx, &loanInfo, wfData); err != nil {
		return nil, err
	}

	// Query user hierarchy and get email
	email, err := queryUserHierarchyAndGetEmail(ctx, &loanInfo, wfData)
	if err != nil {
		return nil, fmt.Errorf(constants.GenericInternalIssuesMessage)
	}

	err = svc.updateTaskStatusByTaskType(ctx, wfData, mdashboard.MANUAL_CREDIT_REVIEW, mdashboard.Started)
	if err != nil {
		logger.WithContext(ctx).Errorf("[mflLapWorkflowExecuter - RejectManualCreditReviewFunc] Failed to update task err: %v", err)
		return nil, err
	}

	response := map[string]interface{}{
		"AssignedEmail": email,
	}

	logger.WithContext(ctx).Infof("[MoveManualCreditReviewTaskUpwardsFunc] All validations passed successfully")
	return &bpmworkflowmanagement.WorkflowActionResponse{
		Data: response,
	}, nil
}

func (svc *mflLapWorkflowExecuter) MoveManualCreditReviewTaskBelowFunc(ctx context.Context, workflowCtx *bpmworkflowmanagement.ActionFuncRefExecReq) (*bpmworkflowmanagement.WorkflowActionResponse, error) {

	wfData, err := NewMflLapWorkflowContext(ctx, workflowCtx.FuncRef.Parameters)
	if err != nil {
		logger.WithContext(ctx).Errorf("[MoveManualCreditReviewTaskUpwardsFunc] Err creating context err: %+v", err)
		return nil, fmt.Errorf("failed to create workflow context: %v", err)
	}

	// Fetch loan application details
	var loanParam loansql.DBGetLoanApplicationParam
	loanParam.LoanApplicationID = wfData.LoanApplicationID

	loanRepo := loansql.NewLoanDBRepository(psql.Database)
	loanInfo, err := loanRepo.DBGetLoanApplicationByParams(ctx, loanParam)
	if err != nil {
		logger.WithContext(ctx).Errorf("[MoveManualCreditReviewTaskUpwardsFunc] failed to fetch loan application. err: %v, loanInfoParam: %+v", err, loanParam)
		return nil, fmt.Errorf("failed to fetch loan application")
	}

	// Query user hierarchy and get email
	email, err := queryUserHierarchyAndGetEmail(ctx, &loanInfo, wfData)
	if err != nil {
		return nil, fmt.Errorf(constants.GenericInternalIssuesMessage)
	}

	response := map[string]interface{}{
		"AssignedEmail": email,
	}

	logger.WithContext(ctx).Infof("[MoveManualCreditReviewTaskUpwardsFunc] All validations passed successfully")
	return &bpmworkflowmanagement.WorkflowActionResponse{
		Data: response,
	}, nil
}

func (svc *mflLapWorkflowExecuter) RejectManualCreditReviewFunc(ctx context.Context, workflowCtx *bpmworkflowmanagement.ActionFuncRefExecReq) (*bpmworkflowmanagement.WorkflowActionResponse, error) {

	wfData, err := NewMflLapWorkflowContext(ctx, workflowCtx.FuncRef.Parameters)
	if err != nil {
		logger.WithContext(ctx).Errorf("[RejectManualCreditReviewFunc] Err creating context err: %+v", err)
		return nil, fmt.Errorf("failed to create workflow context: %v", err)
	}

	err = svc.updateTaskStatusByTaskType(ctx, wfData, mdashboard.MANUAL_CREDIT_REVIEW, mdashboard.Completed)
	if err != nil {
		logger.WithContext(ctx).Errorf("[mflLapWorkflowExecuter - RejectManualCreditReviewFunc] Failed to update task err: %v", err)
		return nil, err
	}

	return nil, nil
}

func (svc *mflLapWorkflowExecuter) ApproveManualCreditReviewFunc(ctx context.Context, workflowCtx *bpmworkflowmanagement.ActionFuncRefExecReq) (*bpmworkflowmanagement.WorkflowActionResponse, error) {
	wfData, err := NewMflLapWorkflowContext(ctx, workflowCtx.FuncRef.Parameters)
	if err != nil {
		logger.WithContext(ctx).Errorf("[MoveManualCreditReviewTaskUpwardsFunc] Err creating context err: %+v", err)
		return nil, fmt.Errorf("failed to create workflow context: %v", err)
	}

	// Fetch loan application details
	var loanParam loansql.DBGetLoanApplicationParam
	loanParam.LoanApplicationID = wfData.LoanApplicationID

	loanRepo := loansql.NewLoanDBRepository(psql.Database)
	loanInfo, err := loanRepo.DBGetLoanApplicationByParams(ctx, loanParam)
	if err != nil {
		logger.WithContext(ctx).Errorf("[MoveManualCreditReviewTaskUpwardsFunc] failed to fetch loan application. err: %v, loanInfoParam: %+v", err, loanParam)
		return nil, fmt.Errorf("failed to fetch loan application")
	}

	// Validate all required tasks are resolved
	requiredTasks := []mdashboard.TaskType{
		mdashboard.BCM_Verification,
		mdashboard.BCM_TASKS,
	}
	if err := validateTasksResolved(ctx, wfData, requiredTasks); err != nil {
		return nil, err
	}

	// Validate all deviations are approved
	if err := validateDeviationsResolved(ctx, wfData); err != nil {
		return nil, err
	}

	// Validate CAM report exists
	if err := validateCAMReportExists(ctx, wfData); err != nil {
		return nil, err
	}

	// Validate co-applicant exists
	if err := validateCoApplicantExists(ctx, &loanInfo); err != nil {
		return nil, err
	}

	// Validate loan offer details
	if err := validateLoanOfferDetails(ctx, &loanInfo, wfData); err != nil {
		return nil, err
	}

	if err := validateCreditApprovalLimit(ctx, &loanInfo, wfData); err != nil {
		return nil, err
	}

	logger.WithContext(ctx).Infof("[MoveManualCreditReviewTaskUpwardsFunc] All validations passed successfully")

	err = svc.updateTaskStatusByTaskType(ctx, wfData, mdashboard.MANUAL_CREDIT_REVIEW, mdashboard.Completed)
	if err != nil {
		logger.WithContext(ctx).Errorf("[mflLapWorkflowExecuter - ApproveManualCreditReviewFunc] Failed to update task err: %v", err)
	}

	return nil, nil
}

func isTasksResolved(ctx context.Context, taskTypeNames []mdashboard.TaskType, identifierType mdashboard.IdentifierType, identifierID string) (bool, error) {
	taskmanagementRepo := taskmanagementsql.NewTaskManagementDBRepository(db.GetDB())

	// Get all task types in one call using NameList
	taskTypes, err := taskmanagementRepo.DBListTaskTypesByParams(ctx, &taskmanagementsql.DBGetTaskTypesParam{NameList: &taskTypeNames}, nil, nil)
	if err != nil {
		logger.WithContext(ctx).Errorf("[isTasksResolved] Failed to get task types: %v", err)
		return false, err
	}

	if len(taskTypes) == 0 {
		logger.WithContext(ctx).Errorf("[isTasksResolved] No task types found for the given names")
		return false, errors.New("no task types found")
	}

	// Collect all task type IDs
	var taskTypeIDs []string
	for _, taskType := range taskTypes {
		taskTypeIDs = append(taskTypeIDs, taskType.Id)
	}

	// Get status list for all task types in one call using TaskTypeList
	statusList, err := taskmanagementRepo.DBListTaskStatusByParams(ctx, &taskmanagementsql.DBGetTaskStatusParam{TaskTypeList: taskTypeIDs}, nil, nil)
	if err != nil {
		logger.WithContext(ctx).Errorf("[isTasksResolved] Failed to get task status list: %v", err)
		return false, err
	}

	// Get all tasks for all task types
	idType := identifierType.String()
	var allTasks []taskmanagementsql.DBGetTasksResponse
	for _, taskTypeID := range taskTypeIDs {
		tasks, err := taskmanagementRepo.DBListTasksByParams(ctx, &taskmanagementsql.DBGetTasksListParam{
			TypeID:         &taskTypeID,
			IdentifierID:   &identifierID,
			IdentifierType: &idType,
		}, nil, nil)
		if err != nil {
			logger.WithContext(ctx).Errorf("[isTasksResolved] Failed to get task lists for task type %s: %v", taskTypeID, err)
			return false, err
		}
		allTasks = append(allTasks, tasks...)
	}

	// If no tasks found
	if len(allTasks) == 0 {
		logger.WithContext(ctx).Info("[isTasksResolved] No tasks found for the given criteria")
		return true, nil
	}

	// Create status map
	taskTypeStatusMap := map[string]taskmanagementsql.DBGetTaskStatusResponse{}
	for _, status := range statusList {
		taskTypeStatusMap[status.Id] = status
	}

	// Check if all tasks are resolved
	for _, task := range allTasks {
		status := taskTypeStatusMap[task.StatusId]
		if status.State != mdashboard.Completed && status.State != mdashboard.Cancelled {
			logger.WithContext(ctx).Errorf("[isTasksResolved] Task not resolved Task ID: %v", task.Id)
			return false, errors.New(mdashboard.TaskNotResolved)
		}
	}

	return true, nil
}

func (svc *mflLapWorkflowExecuter) ValidateBCMRejectFunc(ctx context.Context, workflowCtx *bpmworkflowmanagement.ActionFuncRefExecReq) (*bpmworkflowmanagement.WorkflowActionResponse, error) {
	return nil, nil
}

func (svc *mflLapWorkflowExecuter) InitiateROQuickDataEntryFunc(ctx context.Context, workflowCtx *bpmworkflowmanagement.ActionFuncRefExecReq) (*bpmworkflowmanagement.WorkflowActionResponse, error) {
	return nil, nil
}

func (svc *mflLapWorkflowExecuter) updateTaskStatus(ctx context.Context, wfData *mflLapWorkflowContext, targetState mdashboard.TaskState) error {
	// Get workflow instance
	wfInstance, err := svc.wfRepo.DBGetWorkflowInstancesByParams(ctx, &workflowinstancessql.DBGetWorkflowInstancesParam{
		Id: wfData.WorkflowInstanceID,
	}, nil)
	if err != nil {
		logger.WithContext(ctx).Errorf("[updateTaskStatus] Error getting workflow instance: %v", err)
		return err
	}

	// Get task
	task, err := taskmanagementsql.DBGetTasksByParams(ctx, &taskmanagementsql.DBGetTasksParam{
		ID: &wfInstance.IdentifierID,
	}, nil)
	if err != nil {
		logger.WithContext(ctx).Errorf("[updateTaskStatus] Error getting task: %v", err)
		return err
	}

	// Get task statuses
	taskStatus, err := svc.taskRepo.DBListTaskStatusByParams(ctx, &taskmanagementsql.DBGetTaskStatusParam{TaskType: &task.TypeId}, nil, nil)
	if err != nil {
		logger.WithContext(ctx).Errorf("[updateTaskStatus] Error getting task status: %v", err)
		return err
	}

	now := general.GetTimeStampString()
	email := wfData.User.GetEmail()

	for _, status := range taskStatus {
		if status.State == targetState {
			err := taskmanagementsql.DBUpdateTasks(ctx, nil, &taskmanagementsql.DBUpdateTasksParam{
				StatusId:   &status.Id,
				ApprovedAt: &now,
				ApprovedBy: &email,
			}, task.Id)
			logger.WithContext(ctx).Info("Task approved successfully")
			return err
		}
	}

	return nil
}

func (svc *mflLapWorkflowExecuter) updateTaskStatusByTaskType(ctx context.Context, wfData *mflLapWorkflowContext, taskType mdashboard.TaskType, targetState mdashboard.TaskState) error {
	taskTypeData, err := svc.taskRepo.DBGetTaskTypesByParams(ctx, &taskmanagementsql.DBGetTaskTypesParam{
		Name: &taskType,
	}, nil)
	if err != nil {
		logger.WithContext(ctx).Errorf("[updateTaskStatusByTaskType] Error getting for task type: %v, err %v", taskType, err)
		return err
	}

	tasks, err := svc.taskRepo.DBListTasksByParams(ctx, &taskmanagementsql.DBGetTasksListParam{
		IdentifierID: &wfData.LoanApplicationID,
		TypeID:       &taskTypeData.Id,
	}, nil, nil)

	// Get task statuses
	taskStatus, err := svc.taskRepo.DBListTaskStatusByParams(ctx, &taskmanagementsql.DBGetTaskStatusParam{TaskType: &taskTypeData.Id}, nil, nil)
	if err != nil {
		logger.WithContext(ctx).Errorf("[updateTaskStatus] Error getting task status for tasktype id: %v, err %v", taskTypeData.Id, err)
		return err
	}

	now := general.GetTimeStampString()

	for _, task := range tasks {
		for _, status := range taskStatus {
			if status.State == targetState {
				updateTaskParams := &taskmanagementsql.DBUpdateTasksParam{
					StatusId:  &status.Id,
					UpdatedAt: &now,
				}
				err := taskmanagementsql.DBUpdateTasks(ctx, nil, updateTaskParams, task.Id)
				logger.WithContext(ctx).Info("Task approved successfully")
				return err
			}
		}
	}

	return nil
}

func (svc *mflLapWorkflowExecuter) RecommendedFunc(ctx context.Context, workflowCtx *bpmworkflowmanagement.ActionFuncRefExecReq) (*bpmworkflowmanagement.WorkflowActionResponse, error) {
	wfData, err := NewMflLapWorkflowContext(ctx, workflowCtx.FuncRef.Parameters)
	if err != nil {
		logger.WithContext(ctx).Errorf("[RecommendedFunc] Err creating context err: %+v", err)
	}

	err = svc.updateTaskStatus(ctx, wfData, mdashboard.Completed)
	return nil, err
}

func (svc *mflLapWorkflowExecuter) InitiateTechnicalBCMFunc(ctx context.Context, workflowCtx *bpmworkflowmanagement.ActionFuncRefExecReq) (*bpmworkflowmanagement.WorkflowActionResponse, error) {
	wfData, err := NewMflLapWorkflowContext(ctx, workflowCtx.FuncRef.Parameters)
	if err != nil {
		logger.WithContext(ctx).Errorf("[InitiateTechnicalBCMFunc] Err creating context err: %+v", err)
	}

	err = svc.getTaskAttachment(ctx, wfData.TaskID)
	if err != nil {
		logger.WithContext(ctx).Errorf("[InitiateTechnicalBCMFunc] error in getting attachement for taskID:%s, err:%v", wfData.TaskID, err)
		return nil, err
	}

	err = svc.updateTaskStatus(ctx, wfData, mdashboard.Started)
	if err != nil {
		logger.WithContext(ctx).Errorf("[InitiateTechnicalBCMFunc]error in updating task for taksID:%s, err:%v", wfData.TaskID, err)
		return nil, err
	}
	return nil, err
}

func (svc *mflLapWorkflowExecuter) InitiateLegalBCMFunc(ctx context.Context, workflowCtx *bpmworkflowmanagement.ActionFuncRefExecReq) (*bpmworkflowmanagement.WorkflowActionResponse, error) {
	wfData, err := NewMflLapWorkflowContext(ctx, workflowCtx.FuncRef.Parameters)
	if err != nil {
		logger.WithContext(ctx).Errorf("[InitiateLegalBCMFunc] error creating context err: %+v", err)
		return nil, err
	}

	err = svc.getTaskAttachment(ctx, wfData.TaskID)
	if err != nil {
		logger.WithContext(ctx).Errorf("[InitiateLegalBCMFunc] error in getting attachement for taskID:%s, err:%v", wfData.TaskID, err)
		return nil, err
	}

	err = svc.updateTaskStatus(ctx, wfData, mdashboard.Started)
	if err != nil {
		logger.WithContext(ctx).Errorf("[InitiateLegalBCMFunc]error in updating task for taksID:%s, err:%v", wfData.TaskID, err)
		return nil, err
	}
	return nil, err
}

func (svc *mflLapWorkflowExecuter) InitiateRCUBCMFunc(ctx context.Context, workflowCtx *bpmworkflowmanagement.ActionFuncRefExecReq) (*bpmworkflowmanagement.WorkflowActionResponse, error) {
	wfData, err := NewMflLapWorkflowContext(ctx, workflowCtx.FuncRef.Parameters)
	if err != nil {
		logger.WithContext(ctx).Errorf("[InitiateRCUBCMFunc] Err creating context err: %+v", err)
	}

	err = svc.updateTaskStatus(ctx, wfData, mdashboard.Started)
	if err != nil {
		logger.WithContext(ctx).Errorf("[InitiateRCUBCMFunc]error in updating task for taskID:%s, err:%v", wfData.TaskID, err)
		return nil, err
	}
	return nil, err
}

func (svc *mflLapWorkflowExecuter) getTaskAttachment(ctx context.Context, taskID string) error {
	attachment, err := taskmanagementsql.DBGetTaskAttachmentsByParams(ctx, &taskmanagementsql.DBGetTaskAttachmentsParam{
		TaskID: &taskID,
	}, nil)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		logger.WithContext(ctx).Errorf("[getTaskAttachment] error in getting task attachment for taksID:%s, err:%v", taskID, err)
		return err
	}

	if attachment != nil {
		return nil
	}

	return fmt.Errorf("documents not attached")
}

func (svc *mflLapWorkflowExecuter) CheckForDocumentScreening(ctx context.Context, workflowCtx *bpmworkflowmanagement.ActionFuncRefExecReq) (*bpmworkflowmanagement.WorkflowActionResponse, error) {
	resp := &bpmworkflowmanagement.WorkflowActionResponse{}

	wfData, err := NewMflLapWorkflowContext(ctx, workflowCtx.FuncRef.Parameters)
	if err != nil {
		logger.WithContext(ctx).Errorf("[CheckForDocumentScreening] Err creating context err: %+v", err)
		return nil, err
	}

	reviewStatus, err := getDocumentReviewStatus(ctx, wfData.LoanApplicationID)
	if err != nil {
		logger.WithContext(ctx).Errorf("[CheckForDocumentScreening] Err getting review status err: %+v", err)
		return nil, err
	}

	m := make(map[string]interface{})
	switch reviewStatus {
	case documentsql.DOC_STATUS_SAMPLED:
		m["stateName"] = mdashboard.RCUManagerVerification
		m["groupName"] = mdashboard.RCUManagerGroup
	case documentsql.DOC_STATUS_SCREENED:
		err = svc.updateTaskStatus(ctx, wfData, mdashboard.Completed)
		if err != nil {
			logger.WithContext(ctx).Errorf("[CheckForDocumentScreening]err updating task status for workflowInstanceID:%s, err:%v", wfData.WorkflowInstanceID, err)
		}
	}

	resp.Data = m

	return resp, err
}

func (svc *mflLapWorkflowExecuter) MoveRCUTORCUM(ctx context.Context, workflowCtx *bpmworkflowmanagement.ActionFuncRefExecReq) (*bpmworkflowmanagement.WorkflowActionResponse, error) {
	wfData, err := NewMflLapWorkflowContext(ctx, workflowCtx.FuncRef.Parameters)
	if err != nil {
		logger.WithContext(ctx).Errorf("[MoveRCUTORCUM] failed to create workflow context for workflowInstanceID:%s, err:%v", wfData.WorkflowInstanceID, err)
		return nil, err
	}

	_, err = getDocumentReviewStatus(ctx, wfData.LoanApplicationID)
	if err != nil {
		logger.WithContext(ctx).Errorf("[MoveRCUTORCUM] failed to fetch document review status for loanApplicationID:%s, err:%v", wfData.LoanApplicationID, err)
		return nil, err
	}

	return nil, nil
}

// getDocumentReviewStatus determines the review status of documents for a given loan application ID.
// It fetches documents linked to the loan application and evaluates their review states.
// Returns the review status code or error if some documents are not reviewed.
// It evaluates all documents associated with the loan application according to the following priority:
// 1. If any document is under review or on hold -> returns UNDER_REVIEW/HOLD status
// 2. If any document is sampled -> returns SAMPLED status
// 3. Otherwise -> returns SCREENED status

func getDocumentReviewStatus(ctx context.Context, loanApplicationID string) (int, error) {
	documents, err := documentsql.DBGetDashboardDocumentsWithMedia(ctx, &documentsql.DBGetDashboardDocumentsParam{
		LoanApplicationID: loanApplicationID,
	})
	if err != nil {
		logger.WithContext(ctx).Errorf("[getDocumentReviewStatus] err getting documents for loanApplicationId:%s, err: %v", loanApplicationID, err)
		return 0, fmt.Errorf("err getting documents err: %v", err)
	}

	flag := false
	for _, document := range documents {
		if document.ReviewStatus == documentsql.DOC_STATUS_UNDER_REVIEW || document.ReviewStatus == documentsql.DOC_SAMPLED_HOLD {
			return document.ReviewStatus, fmt.Errorf("all the docs are not reviewed")
		}
		if document.ReviewStatus == documentsql.DOC_STATUS_SAMPLED {
			flag = true
		}
	}

	if flag {
		return documentsql.DOC_STATUS_SAMPLED, nil
	}
	return documentsql.DOC_STATUS_SCREENED, nil
}
