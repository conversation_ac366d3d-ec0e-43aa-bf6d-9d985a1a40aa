package dynamicmyqueue

import (
	"context"
	"crypto/md5"
	"fmt"
	"time"

	"finbox/go-api/functions/logger"
	"finbox/go-api/internal/service/dynamicmyqueue/factory"
)

// DynamicQueueServiceImpl implements the DynamicQueueService interface
type DynamicQueueServiceImpl struct {
	configRepo         ConfigRepository
	builderFactory     QueueBuilderFactory
	transformerFactory TransformerFactory
	strategyRegistry   *factory.ClientStrategyRegistry
	cacheManager       CacheManager
	eventPublisher     EventPublisher
	metricsCollector   MetricsCollector
}

// NewDynamicQueueServiceImpl creates a new instance of DynamicQueueServiceImpl
func NewDynamicQueueServiceImpl(config *factory.DynamicQueueServiceConfig) *DynamicQueueServiceImpl {
	if err := config.Validate(); err != nil {
		panic(fmt.Sprintf("invalid service configuration: %v", err))
	}

	return &DynamicQueueServiceImpl{
		configRepo:         config.ConfigRepository,
		builderFactory:     config.BuilderFactory,
		transformerFactory: config.TransformerFactory,
		strategyRegistry:   config.StrategyRegistry,
		cacheManager:       config.CacheManager,
		eventPublisher:     config.EventPublisher,
		metricsCollector:   config.MetricsCollector,
	}
}

// GetTaskCases retrieves task cases based on the configuration and request parameters
func (s *DynamicQueueServiceImpl) GetTaskCases(ctx context.Context, request *TaskCasesRequest) (*TaskCasesResponse, error) {
	if request == nil {
		return nil, fmt.Errorf("request cannot be nil")
	}

	startTime := time.Now()

	// Get configuration
	config, err := s.getConfig(ctx, request.ClientID, request.ConfigName)
	if err != nil {
		return nil, fmt.Errorf("failed to get config: %w", err)
	}

	// Generate cache key
	cacheKey := s.generateCacheKey(request)

	// Try cache first
	if s.cacheManager != nil {
		if cachedResult, err := s.cacheManager.GetQueryResult(ctx, cacheKey); err == nil {
			return cachedResult, nil
		}
	}

	// Build query
	queryBuilder, err := s.builderFactory.CreateBuilder(request.ClientID)
	if err != nil {
		return nil, fmt.Errorf("failed to create query builder: %w", err)
	}

	// Build main query
	queryResult, err := queryBuilder.BuildQuery(ctx, config, request)
	if err != nil {
		return nil, fmt.Errorf("failed to build query: %w", err)
	}

	// Build count query
	countQueryResult, err := queryBuilder.BuildCountQuery(ctx, config, request)
	if err != nil {
		return nil, fmt.Errorf("failed to build count query: %w", err)
	}

	// Execute queries (this would be implemented with actual database execution)
	rawData, totalCount, err := s.executeQueries(ctx, queryResult, countQueryResult)
	if err != nil {
		return nil, fmt.Errorf("failed to execute queries: %w", err)
	}

	// Transform data
	transformer, err := s.transformerFactory.CreateTransformer("default")
	if err != nil {
		return nil, fmt.Errorf("failed to create transformer: %w", err)
	}

	transformedData, err := transformer.Transform(ctx, rawData, config.FieldMappings)
	if err != nil {
		return nil, fmt.Errorf("failed to transform data: %w", err)
	}

	// Use transformed data as final data (no business rules)
	finalData := transformedData

	// Apply client-specific post-processing
	strategy := s.strategyRegistry.GetStrategy(request.ClientID)
	finalData, err = strategy.PostProcessData(finalData, config)
	if err != nil {
		return nil, fmt.Errorf("failed to post-process data: %w", err)
	}

	// Build response
	response := &TaskCasesResponse{
		Data:            finalData,
		TotalCount:      totalCount,
		Page:            request.Page,
		PageSize:        request.PageSize,
		HasNextPage:     s.calculateHasNextPage(request.Page, request.PageSize, totalCount),
		HasPreviousPage: request.Page > 1,
		Metadata: map[string]interface{}{
			"executionTime": time.Since(startTime).Milliseconds(),
			"queryHash":     s.generateQueryHash(queryResult.Query),
		},
	}

	// Cache the result
	if s.cacheManager != nil {
		if err := s.cacheManager.SetQueryResult(ctx, cacheKey, response, 300); err != nil {
			logger.WithContext(ctx).Warnf("Failed to cache query result: %v", err)
		}
	}

	// Record metrics
	if s.metricsCollector != nil {
		s.metricsCollector.RecordQueryExecution(
			request.ClientID,
			request.ConfigName,
			time.Since(startTime).Milliseconds(),
			totalCount,
		)
	}

	// Publish event
	if s.eventPublisher != nil {
		if err := s.eventPublisher.PublishQueryExecuted(ctx, request.ClientID, request.ConfigName, time.Since(startTime).Milliseconds()); err != nil {
			logger.WithContext(ctx).Warnf("Failed to publish query executed event: %v", err)
		}
	}

	return response, nil
}

// GetTaskCaseDetails retrieves detailed information for a specific task case
func (s *DynamicQueueServiceImpl) GetTaskCaseDetails(ctx context.Context, clientID, configName, taskID string) (*TaskCaseDetails, error) {
	// This would be implemented with actual database queries to get task details
	// For now, return a placeholder
	return &TaskCaseDetails{
		ID:   taskID,
		Data: map[string]interface{}{},
		Metadata: map[string]interface{}{
			"clientID":   clientID,
			"configName": configName,
		},
	}, nil
}

// ValidateConfig validates a configuration
func (s *DynamicQueueServiceImpl) ValidateConfig(ctx context.Context, config *MetadataConfig) (*ValidationResult, error) {
	if config == nil {
		return &ValidationResult{
			IsValid: false,
			Errors: []ValidationError{
				{Field: "config", Message: "config cannot be nil", Code: "REQUIRED"},
			},
		}, nil
	}

	strategy := s.strategyRegistry.GetStrategy(config.ClientID)
	if err := strategy.ValidateConfig(config); err != nil {
		return &ValidationResult{
			IsValid: false,
			Errors: []ValidationError{
				{Field: "config", Message: err.Error(), Code: "VALIDATION_FAILED"},
			},
		}, nil
	}

	return &ValidationResult{IsValid: true}, nil
}

// TestQuery tests a query configuration
func (s *DynamicQueueServiceImpl) TestQuery(ctx context.Context, clientID, configName string, filters map[string]interface{}) (*QueryTestResult, error) {
	config, err := s.getConfig(ctx, clientID, configName)
	if err != nil {
		return nil, fmt.Errorf("failed to get config: %w", err)
	}

	queryBuilder, err := s.builderFactory.CreateBuilder(clientID)
	if err != nil {
		return nil, fmt.Errorf("failed to create query builder: %w", err)
	}

	request := &TaskCasesRequest{
		ClientID:   clientID,
		ConfigName: configName,
		Filters:    filters,
		Page:       1,
		PageSize:   10,
	}

	startTime := time.Now()
	queryResult, err := queryBuilder.BuildQuery(ctx, config, request)
	if err != nil {
		return nil, fmt.Errorf("failed to build query: %w", err)
	}

	return &QueryTestResult{
		Query:         queryResult.Query,
		Parameters:    queryResult.Parameters,
		SampleData:    []map[string]interface{}{}, // Would be populated with actual test data
		ExecutionTime: time.Since(startTime).Milliseconds(),
		RowCount:      0, // Would be populated with actual row count
	}, nil
}

// Helper methods
func (s *DynamicQueueServiceImpl) generateCacheKey(request *TaskCasesRequest) string {
	return fmt.Sprintf("task_cases:%s:%s:%x", request.ClientID, request.ConfigName, s.hashRequest(request))
}

func (s *DynamicQueueServiceImpl) generateQueryHash(query string) string {
	return fmt.Sprintf("%x", md5.Sum([]byte(query)))
}

func (s *DynamicQueueServiceImpl) hashRequest(request *TaskCasesRequest) string {
	data := fmt.Sprintf("%v:%v:%d:%d:%s", request.Filters, request.Sort, request.Page, request.PageSize, request.SearchQuery)
	return fmt.Sprintf("%x", md5.Sum([]byte(data)))
}

func (s *DynamicQueueServiceImpl) calculateHasNextPage(page, pageSize int, totalCount int64) bool {
	return int64(page*pageSize) < totalCount
}

func (s *DynamicQueueServiceImpl) executeQueries(ctx context.Context, queryResult, countQueryResult *QueryResult) ([]map[string]interface{}, int64, error) {
	// This would be implemented with actual database execution
	// For now, return placeholder data
	return []map[string]interface{}{}, 0, nil
}

// getConfig is a private helper method to retrieve configuration
func (s *DynamicQueueServiceImpl) getConfig(ctx context.Context, clientID, configName string) (*MetadataConfig, error) {
	// Try cache first
	if s.cacheManager != nil {
		if config, err := s.cacheManager.GetConfig(ctx, clientID, configName); err == nil {
			return config, nil
		}
	}

	// Get from repository
	config, err := s.configRepo.GetByClientAndName(ctx, clientID, configName)
	if err != nil {
		return nil, fmt.Errorf("failed to get config: %w", err)
	}

	// Cache the result
	if s.cacheManager != nil {
		if err := s.cacheManager.SetConfig(ctx, clientID, configName, config); err != nil {
			logger.WithContext(ctx).Warnf("Failed to cache config: %v", err)
		}
	}

	return config, nil
}
