package factory

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"finbox/go-api/functions/logger"
	"finbox/go-api/internal/service/dynamicmyqueue"
)

// DefaultClientStrategy provides default implementation for client strategy
type DefaultClientStrategy struct{}

// NewDefaultClientStrategy creates a new default client strategy
func NewDefaultClientStrategy() *DefaultClientStrategy {
	return &DefaultClientStrategy{}
}

// GetDefaultConfig returns the default configuration for a client
func (s *DefaultClientStrategy) GetDefaultConfig() *dynamicmyqueue.MetadataConfig {
	return &dynamicmyqueue.MetadataConfig{
		Version:  "1.0",
		IsActive: true,
		TableJoins: []dynamicmyqueue.TableJoinConfig{
			{
				TableName:     "loan_application",
				Alias:         "la",
				JoinType:      dynamicmyqueue.InnerJoin,
				JoinCondition: "",
				IsRequired:    true,
			},
		},
		FieldMappings: []dynamicmyqueue.FieldMapping{
			{
				SourceField:  "la.loan_application_id",
				TargetField:  "loanApplicationId",
				DataType:     dynamicmyqueue.StringType,
				IsVisible:    true,
				IsSearchable: true,
				IsSortable:   true,
			},
			{
				SourceField:  "la.created_at",
				TargetField:  "createdAt",
				DataType:     dynamicmyqueue.DateTimeType,
				IsVisible:    true,
				IsSearchable: false,
				IsSortable:   true,
			},
		},
		SortingConfig: dynamicmyqueue.SortingConfig{
			DefaultSort: []dynamicmyqueue.SortField{
				{
					FieldName: "createdAt",
					Direction: dynamicmyqueue.Descending,
				},
			},
		},
		PaginationConfig: dynamicmyqueue.PaginationConfig{
			DefaultPageSize: 20,
			MaxPageSize:     100,
			AllowedSizes:    []int{10, 20, 50, 100},
		},
	}
}

// ValidateConfig validates the configuration for the client
func (s *DefaultClientStrategy) ValidateConfig(config *dynamicmyqueue.MetadataConfig) error {
	if config == nil {
		return fmt.Errorf("config cannot be nil")
	}

	if config.ClientID == "" {
		return fmt.Errorf("client ID is required")
	}

	if config.ConfigName == "" {
		return fmt.Errorf("config name is required")
	}

	if len(config.TableJoins) == 0 {
		return fmt.Errorf("at least one table join is required")
	}

	if len(config.FieldMappings) == 0 {
		return fmt.Errorf("at least one field mapping is required")
	}

	// Validate table joins
	for i, join := range config.TableJoins {
		if join.TableName == "" {
			return fmt.Errorf("table name is required for join %d", i)
		}
		if join.Alias == "" {
			return fmt.Errorf("alias is required for join %d", i)
		}
	}

	// Validate field mappings
	for i, mapping := range config.FieldMappings {
		if mapping.SourceField == "" {
			return fmt.Errorf("source field is required for mapping %d", i)
		}
		if mapping.TargetField == "" {
			return fmt.Errorf("target field is required for mapping %d", i)
		}
	}

	return nil
}

// CustomizeQuery allows client-specific query customization
func (s *DefaultClientStrategy) CustomizeQuery(query *dynamicmyqueue.QueryResult, config *dynamicmyqueue.MetadataConfig) (*dynamicmyqueue.QueryResult, error) {
	// Default implementation doesn't modify the query
	return query, nil
}

// PostProcessData allows client-specific data post-processing
func (s *DefaultClientStrategy) PostProcessData(data []map[string]interface{}, config *dynamicmyqueue.MetadataConfig) ([]map[string]interface{}, error) {
	// Default implementation doesn't modify the data
	return data, nil
}

// DefaultDataTransformer provides default data transformation capabilities
type DefaultDataTransformer struct{}

// NewDefaultDataTransformer creates a new default data transformer
func NewDefaultDataTransformer() *DefaultDataTransformer {
	return &DefaultDataTransformer{}
}

// Transform applies field mappings to raw data
func (t *DefaultDataTransformer) Transform(ctx context.Context, rawData []map[string]interface{}, fieldMappings []dynamicmyqueue.FieldMapping) ([]map[string]interface{}, error) {
	if len(rawData) == 0 {
		return rawData, nil
	}

	transformedData := make([]map[string]interface{}, len(rawData))

	for i, row := range rawData {
		transformedRow := make(map[string]interface{})

		for _, mapping := range fieldMappings {
			if !mapping.IsVisible {
				continue
			}

			value, exists := row[mapping.SourceField]
			if !exists {
				continue
			}

			// Apply transformation based on data type
			transformedValue, err := t.transformValue(value, mapping)
			if err != nil {
				logger.WithContext(ctx).Warnf("Failed to transform value for field %s: %v", mapping.SourceField, err)
				transformedValue = value // Use original value if transformation fails
			}

			transformedRow[mapping.TargetField] = transformedValue
		}

		transformedData[i] = transformedRow
	}

	return transformedData, nil
}

// ValidateData validates data against validation rules
func (t *DefaultDataTransformer) ValidateData(ctx context.Context, data []map[string]interface{}, validationRules []dynamicmyqueue.ValidationRule) error {
	// Basic validation implementation
	for _, row := range data {
		for _, rule := range validationRules {
			if err := t.validateRow(row, rule); err != nil {
				return err
			}
		}
	}
	return nil
}

// transformValue transforms a value based on the field mapping
func (t *DefaultDataTransformer) transformValue(value interface{}, mapping dynamicmyqueue.FieldMapping) (interface{}, error) {
	if value == nil {
		return nil, nil
	}

	switch mapping.DataType {
	case dynamicmyqueue.StringType:
		return fmt.Sprintf("%v", value), nil
	case dynamicmyqueue.IntegerType:
		if str, ok := value.(string); ok {
			return strconv.Atoi(str)
		}
		return value, nil
	case dynamicmyqueue.FloatType:
		if str, ok := value.(string); ok {
			return strconv.ParseFloat(str, 64)
		}
		return value, nil
	case dynamicmyqueue.BooleanType:
		if str, ok := value.(string); ok {
			return strconv.ParseBool(str)
		}
		return value, nil
	case dynamicmyqueue.DateTimeType:
		if str, ok := value.(string); ok {
			return time.Parse(time.RFC3339, str)
		}
		return value, nil
	case dynamicmyqueue.JSONType:
		if str, ok := value.(string); ok {
			var jsonValue interface{}
			if err := json.Unmarshal([]byte(str), &jsonValue); err != nil {
				return str, nil // Return as string if JSON parsing fails
			}
			return jsonValue, nil
		}
		return value, nil
	default:
		return value, nil
	}
}

// validateRow validates a single row against a validation rule
func (t *DefaultDataTransformer) validateRow(row map[string]interface{}, rule dynamicmyqueue.ValidationRule) error {
	// Basic validation implementation
	switch rule.Type {
	case "required":
		if fields, ok := rule.Value.([]string); ok {
			for _, field := range fields {
				if _, exists := row[field]; !exists {
					return fmt.Errorf("required field %s is missing", field)
				}
			}
		}
	case "not_empty":
		if fields, ok := rule.Value.([]string); ok {
			for _, field := range fields {
				if value, exists := row[field]; exists {
					if str, ok := value.(string); ok && strings.TrimSpace(str) == "" {
						return fmt.Errorf("field %s cannot be empty", field)
					}
				}
			}
		}
	}
	return nil
}

// JSONDataTransformer handles JSON-specific transformations
type JSONDataTransformer struct {
	*DefaultDataTransformer
}

// NewJSONDataTransformer creates a new JSON data transformer
func NewJSONDataTransformer() *JSONDataTransformer {
	return &JSONDataTransformer{
		DefaultDataTransformer: NewDefaultDataTransformer(),
	}
}

// DateDataTransformer handles date-specific transformations
type DateDataTransformer struct {
	*DefaultDataTransformer
}

// NewDateDataTransformer creates a new date data transformer
func NewDateDataTransformer() *DateDataTransformer {
	return &DateDataTransformer{
		DefaultDataTransformer: NewDefaultDataTransformer(),
	}
}

// CurrencyDataTransformer handles currency-specific transformations
type CurrencyDataTransformer struct {
	*DefaultDataTransformer
}

// NewCurrencyDataTransformer creates a new currency data transformer
func NewCurrencyDataTransformer() *CurrencyDataTransformer {
	return &CurrencyDataTransformer{
		DefaultDataTransformer: NewDefaultDataTransformer(),
	}
}
