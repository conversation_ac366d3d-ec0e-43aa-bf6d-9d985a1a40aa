package factory

import (
	"fmt"
	"sync"

	"finbox/go-api/internal/service/dynamicmyqueue"
)

// QueueBuilderFactoryImpl implements the QueueBuilderFactory interface
type QueueBuilderFactoryImpl struct {
	builders map[string]dynamicmyqueue.QueryBuilder
	mutex    sync.RWMutex
}

// NewQueueBuilderFactory creates a new instance of QueueBuilderFactory
func NewQueueBuilderFactory() *QueueBuilderFactoryImpl {
	return &QueueBuilderFactoryImpl{
		builders: make(map[string]dynamicmyqueue.QueryBuilder),
	}
}

// CreateBuilder creates a query builder for the specified client
func (f *QueueBuilderFactoryImpl) CreateBuilder(clientID string) (dynamicmyqueue.QueryBuilder, error) {
	f.mutex.RLock()
	defer f.mutex.RUnlock()

	if builder, exists := f.builders[clientID]; exists {
		return builder, nil
	}

	// Return default builder if no specific builder is registered
	return NewDefaultQueryBuilder(), nil
}

// RegisterBuilder registers a custom query builder for a specific client
func (f *QueueBuilderFactoryImpl) RegisterBuilder(clientID string, builder dynamicmyqueue.QueryBuilder) {
	f.mutex.Lock()
	defer f.mutex.Unlock()
	f.builders[clientID] = builder
}

// GetSupportedClients returns a list of clients with registered builders
func (f *QueueBuilderFactoryImpl) GetSupportedClients() []string {
	f.mutex.RLock()
	defer f.mutex.RUnlock()

	clients := make([]string, 0, len(f.builders))
	for clientID := range f.builders {
		clients = append(clients, clientID)
	}
	return clients
}

// TransformerFactoryImpl implements the TransformerFactory interface
type TransformerFactoryImpl struct {
	transformers map[string]dynamicmyqueue.DataTransformer
	mutex        sync.RWMutex
}

// NewTransformerFactory creates a new instance of TransformerFactory
func NewTransformerFactory() *TransformerFactoryImpl {
	factory := &TransformerFactoryImpl{
		transformers: make(map[string]dynamicmyqueue.DataTransformer),
	}

	// Register default transformers
	factory.RegisterTransformer("default", NewDefaultDataTransformer())
	factory.RegisterTransformer("json", NewJSONDataTransformer())
	factory.RegisterTransformer("date", NewDateDataTransformer())
	factory.RegisterTransformer("currency", NewCurrencyDataTransformer())

	return factory
}

// CreateTransformer creates a data transformer for the specified type
func (f *TransformerFactoryImpl) CreateTransformer(transformationType string) (dynamicmyqueue.DataTransformer, error) {
	f.mutex.RLock()
	defer f.mutex.RUnlock()

	if transformer, exists := f.transformers[transformationType]; exists {
		return transformer, nil
	}

	return nil, fmt.Errorf("transformer type '%s' not supported", transformationType)
}

// RegisterTransformer registers a custom data transformer
func (f *TransformerFactoryImpl) RegisterTransformer(transformationType string, transformer dynamicmyqueue.DataTransformer) {
	f.mutex.Lock()
	defer f.mutex.Unlock()
	f.transformers[transformationType] = transformer
}

// GetSupportedTransformations returns a list of supported transformation types
func (f *TransformerFactoryImpl) GetSupportedTransformations() []string {
	f.mutex.RLock()
	defer f.mutex.RUnlock()

	types := make([]string, 0, len(f.transformers))
	for transformationType := range f.transformers {
		types = append(types, transformationType)
	}
	return types
}

// ClientStrategyRegistry manages client-specific strategies
type ClientStrategyRegistry struct {
	strategies map[string]dynamicmyqueue.ClientStrategy
	mutex      sync.RWMutex
}

// NewClientStrategyRegistry creates a new client strategy registry
func NewClientStrategyRegistry() *ClientStrategyRegistry {
	registry := &ClientStrategyRegistry{
		strategies: make(map[string]dynamicmyqueue.ClientStrategy),
	}

	// Register default strategy
	registry.RegisterStrategy("default", NewDefaultClientStrategy())

	return registry
}

// GetStrategy returns the strategy for a specific client
func (r *ClientStrategyRegistry) GetStrategy(clientID string) dynamicmyqueue.ClientStrategy {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	if strategy, exists := r.strategies[clientID]; exists {
		return strategy
	}

	// Return default strategy if no specific strategy is registered
	return r.strategies["default"]
}

// RegisterStrategy registers a strategy for a specific client
func (r *ClientStrategyRegistry) RegisterStrategy(clientID string, strategy dynamicmyqueue.ClientStrategy) {
	r.mutex.Lock()
	defer r.mutex.Unlock()
	r.strategies[clientID] = strategy
}

// GetSupportedClients returns a list of clients with registered strategies
func (r *ClientStrategyRegistry) GetSupportedClients() []string {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	clients := make([]string, 0, len(r.strategies))
	for clientID := range r.strategies {
		if clientID != "default" {
			clients = append(clients, clientID)
		}
	}
	return clients
}

// ServiceFactory creates and configures the main dynamic queue service
type ServiceFactory struct {
	builderFactory     *QueueBuilderFactoryImpl
	transformerFactory *TransformerFactoryImpl
	strategyRegistry   *ClientStrategyRegistry
}

// NewServiceFactory creates a new service factory with all components
func NewServiceFactory() *ServiceFactory {
	return &ServiceFactory{
		builderFactory:     NewQueueBuilderFactory(),
		transformerFactory: NewTransformerFactory(),
		strategyRegistry:   NewClientStrategyRegistry(),
	}
}

// CreateDynamicQueueService creates a fully configured dynamic queue service
func (sf *ServiceFactory) CreateDynamicQueueService() dynamicmyqueue.DynamicQueueService {
	return dynamicmyqueue.NewDynamicQueueServiceImpl()
}

// GetBuilderFactory returns the query builder factory
func (sf *ServiceFactory) GetBuilderFactory() *QueueBuilderFactoryImpl {
	return sf.builderFactory
}

// GetTransformerFactory returns the data transformer factory
func (sf *ServiceFactory) GetTransformerFactory() *TransformerFactoryImpl {
	return sf.transformerFactory
}

// GetStrategyRegistry returns the client strategy registry
func (sf *ServiceFactory) GetStrategyRegistry() *ClientStrategyRegistry {
	return sf.strategyRegistry
}
