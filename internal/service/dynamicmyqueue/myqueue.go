package dynamicmyqueue

import (
	"time"
)

// MetadataConfig represents the core configuration structure for dynamic task cases
type MetadataConfig struct {
	ClientID         string            `json:"clientId" db:"client_id"`
	ConfigName       string            `json:"configName" db:"config_name"`
	Version          string            `json:"version" db:"version"`
	IsActive         bool              `json:"isActive" db:"is_active"`
	TableJoins       []TableJoinConfig `json:"tableJoins" db:"table_joins"`
	FieldMappings    []FieldMapping    `json:"fieldMappings" db:"field_mappings"`
	FilterConditions []FilterCondition `json:"filterConditions" db:"filter_conditions"`
	SortingConfig    SortingConfig     `json:"sortingConfig" db:"sorting_config"`
	PaginationConfig PaginationConfig  `json:"paginationConfig" db:"pagination_config"`
	CreatedAt        time.Time         `json:"createdAt" db:"created_at"`
	UpdatedAt        time.Time         `json:"updatedAt" db:"updated_at"`
	CreatedBy        string            `json:"createdBy" db:"created_by"`
	UpdatedBy        string            `json:"updatedBy" db:"updated_by"`
}

// TableJoinConfig defines how tables should be joined
type TableJoinConfig struct {
	TableName     string            `json:"tableName"`
	Alias         string            `json:"alias"`
	JoinType      JoinType          `json:"joinType"`
	JoinCondition string            `json:"joinCondition"`
	IsRequired    bool              `json:"isRequired"`
	Metadata      map[string]string `json:"metadata"`
}

// FieldMapping defines how database fields map to API response fields
type FieldMapping struct {
	SourceField     string            `json:"sourceField"`
	TargetField     string            `json:"targetField"`
	DataType        DataType          `json:"dataType"`
	IsVisible       bool              `json:"isVisible"`
	IsSearchable    bool              `json:"isSearchable"`
	IsSortable      bool              `json:"isSortable"`
	Transformation  string            `json:"transformation"`
	ValidationRules []ValidationRule  `json:"validationRules"`
	Metadata        map[string]string `json:"metadata"`
}

// FilterCondition defines dynamic filtering capabilities
type FilterCondition struct {
	FieldName     string            `json:"fieldName"`
	Operator      FilterOperator    `json:"operator"`
	Value         interface{}       `json:"value"`
	IsUserDefined bool              `json:"isUserDefined"`
	IsRequired    bool              `json:"isRequired"`
	Metadata      map[string]string `json:"metadata"`
}

// SortingConfig defines default and available sorting options
type SortingConfig struct {
	DefaultSort    []SortField       `json:"defaultSort"`
	AvailableSorts []SortField       `json:"availableSorts"`
	Metadata       map[string]string `json:"metadata"`
}

// PaginationConfig defines pagination behavior
type PaginationConfig struct {
	DefaultPageSize int               `json:"defaultPageSize"`
	MaxPageSize     int               `json:"maxPageSize"`
	AllowedSizes    []int             `json:"allowedSizes"`
	Metadata        map[string]string `json:"metadata"`
}

// Supporting types and enums
type JoinType string

const (
	InnerJoin JoinType = "INNER"
	LeftJoin  JoinType = "LEFT"
	RightJoin JoinType = "RIGHT"
	FullJoin  JoinType = "FULL"
)

type DataType string

const (
	StringType   DataType = "string"
	IntegerType  DataType = "integer"
	FloatType    DataType = "float"
	BooleanType  DataType = "boolean"
	DateTimeType DataType = "datetime"
	JSONType     DataType = "json"
)

type FilterOperator string

const (
	Equal              FilterOperator = "eq"
	NotEqual           FilterOperator = "ne"
	GreaterThan        FilterOperator = "gt"
	GreaterThanOrEqual FilterOperator = "gte"
	LessThan           FilterOperator = "lt"
	LessThanOrEqual    FilterOperator = "lte"
	Like               FilterOperator = "like"
	In                 FilterOperator = "in"
	NotIn              FilterOperator = "not_in"
	IsNull             FilterOperator = "is_null"
	IsNotNull          FilterOperator = "is_not_null"
	Between            FilterOperator = "between"
)

type ValidationRule struct {
	Type         string      `json:"type"`
	Value        interface{} `json:"value"`
	ErrorMessage string      `json:"errorMessage"`
}

type SortField struct {
	FieldName string    `json:"fieldName"`
	Direction SortOrder `json:"direction"`
}

type SortOrder string

const (
	Ascending  SortOrder = "ASC"
	Descending SortOrder = "DESC"
)
