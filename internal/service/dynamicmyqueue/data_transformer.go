package dynamicmyqueue

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"
)

// SimpleDataTransformer handles data transformation based on field mappings
type SimpleDataTransformer struct{}

// NewSimpleDataTransformer creates a new data transformer
func NewSimpleDataTransformer() *SimpleDataTransformer {
	return &SimpleDataTransformer{}
}

// Transform applies field mappings to raw data and performs data type conversions
func (t *SimpleDataTransformer) Transform(ctx context.Context, rawData []map[string]interface{}, fieldMappings []FieldMapping) ([]map[string]interface{}, error) {
	if len(rawData) == 0 {
		return rawData, nil
	}

	transformedData := make([]map[string]interface{}, 0, len(rawData))

	for _, row := range rawData {
		transformedRow := make(map[string]interface{})
		
		for _, mapping := range fieldMappings {
			if !mapping.IsVisible {
				continue
			}
			
			// Extract source field value
			sourceValue := t.extractFieldValue(row, mapping.SourceField)
			
			// Transform the value based on data type and metadata
			transformedValue, err := t.transformValue(sourceValue, mapping)
			if err != nil {
				return nil, fmt.Errorf("failed to transform field %s: %w", mapping.SourceField, err)
			}
			
			// Set the transformed value with the target field name
			transformedRow[mapping.TargetField] = transformedValue
		}
		
		transformedData = append(transformedData, transformedRow)
	}

	return transformedData, nil
}

// extractFieldValue extracts value from a row using dot notation (e.g., "la.loan_application_id")
func (t *SimpleDataTransformer) extractFieldValue(row map[string]interface{}, sourceField string) interface{} {
	// Handle simple field names and aliased field names
	// For now, we'll use the full field name as the key
	// In a real implementation, you might need to handle the alias mapping
	
	// Try the full field name first
	if value, exists := row[sourceField]; exists {
		return value
	}
	
	// Try without the alias (e.g., "loan_application_id" from "la.loan_application_id")
	parts := strings.Split(sourceField, ".")
	if len(parts) > 1 {
		fieldName := parts[len(parts)-1]
		if value, exists := row[fieldName]; exists {
			return value
		}
	}
	
	return nil
}

// transformValue applies data type conversion and formatting based on field mapping
func (t *SimpleDataTransformer) transformValue(value interface{}, mapping FieldMapping) (interface{}, error) {
	if value == nil {
		return nil, nil
	}

	// Apply data type conversion
	convertedValue, err := t.convertDataType(value, mapping.DataType)
	if err != nil {
		return nil, err
	}

	// Apply formatting based on metadata
	formattedValue := t.applyFormatting(convertedValue, mapping.Metadata)
	
	return formattedValue, nil
}

// convertDataType converts value to the specified data type
func (t *SimpleDataTransformer) convertDataType(value interface{}, dataType DataType) (interface{}, error) {
	if value == nil {
		return nil, nil
	}

	switch dataType {
	case StringType:
		return t.toString(value), nil
	case IntType:
		return t.toInt(value)
	case FloatType:
		return t.toFloat(value)
	case BoolType:
		return t.toBool(value)
	case DateTimeType:
		return t.toDateTime(value)
	case DateType:
		return t.toDate(value)
	default:
		return value, nil
	}
}

// toString converts value to string
func (t *SimpleDataTransformer) toString(value interface{}) string {
	if value == nil {
		return ""
	}
	return fmt.Sprintf("%v", value)
}

// toInt converts value to integer
func (t *SimpleDataTransformer) toInt(value interface{}) (int64, error) {
	switch v := value.(type) {
	case int:
		return int64(v), nil
	case int32:
		return int64(v), nil
	case int64:
		return v, nil
	case float32:
		return int64(v), nil
	case float64:
		return int64(v), nil
	case string:
		return strconv.ParseInt(v, 10, 64)
	default:
		return 0, fmt.Errorf("cannot convert %T to int", value)
	}
}

// toFloat converts value to float
func (t *SimpleDataTransformer) toFloat(value interface{}) (float64, error) {
	switch v := value.(type) {
	case int:
		return float64(v), nil
	case int32:
		return float64(v), nil
	case int64:
		return float64(v), nil
	case float32:
		return float64(v), nil
	case float64:
		return v, nil
	case string:
		return strconv.ParseFloat(v, 64)
	default:
		return 0, fmt.Errorf("cannot convert %T to float", value)
	}
}

// toBool converts value to boolean
func (t *SimpleDataTransformer) toBool(value interface{}) (bool, error) {
	switch v := value.(type) {
	case bool:
		return v, nil
	case string:
		return strconv.ParseBool(v)
	case int, int32, int64:
		return v != 0, nil
	default:
		return false, fmt.Errorf("cannot convert %T to bool", value)
	}
}

// toDateTime converts value to datetime string
func (t *SimpleDataTransformer) toDateTime(value interface{}) (string, error) {
	switch v := value.(type) {
	case time.Time:
		return v.Format("2006-01-02 15:04:05"), nil
	case string:
		// Try to parse and reformat
		if parsedTime, err := time.Parse(time.RFC3339, v); err == nil {
			return parsedTime.Format("2006-01-02 15:04:05"), nil
		}
		return v, nil
	default:
		return fmt.Sprintf("%v", value), nil
	}
}

// toDate converts value to date string
func (t *SimpleDataTransformer) toDate(value interface{}) (string, error) {
	switch v := value.(type) {
	case time.Time:
		return v.Format("2006-01-02"), nil
	case string:
		// Try to parse and reformat
		if parsedTime, err := time.Parse(time.RFC3339, v); err == nil {
			return parsedTime.Format("2006-01-02"), nil
		}
		return v, nil
	default:
		return fmt.Sprintf("%v", value), nil
	}
}

// applyFormatting applies formatting based on metadata
func (t *SimpleDataTransformer) applyFormatting(value interface{}, metadata map[string]string) interface{} {
	if value == nil || metadata == nil {
		return value
	}

	format, exists := metadata["format"]
	if !exists {
		return value
	}

	switch format {
	case "phone":
		return t.formatPhone(value)
	case "status":
		return t.formatStatus(value)
	case "currency":
		return t.formatCurrency(value)
	case "percentage":
		return t.formatPercentage(value)
	default:
		return value
	}
}

// formatPhone formats phone numbers
func (t *SimpleDataTransformer) formatPhone(value interface{}) string {
	phone := t.toString(value)
	if len(phone) == 10 {
		return fmt.Sprintf("+91-%s-%s", phone[:5], phone[5:])
	}
	return phone
}

// formatStatus formats status values
func (t *SimpleDataTransformer) formatStatus(value interface{}) string {
	status := t.toString(value)
	return strings.Title(strings.ToLower(status))
}

// formatCurrency formats currency values
func (t *SimpleDataTransformer) formatCurrency(value interface{}) string {
	if floatVal, err := t.toFloat(value); err == nil {
		return fmt.Sprintf("₹%.2f", floatVal)
	}
	return t.toString(value)
}

// formatPercentage formats percentage values
func (t *SimpleDataTransformer) formatPercentage(value interface{}) string {
	if floatVal, err := t.toFloat(value); err == nil {
		return fmt.Sprintf("%.2f%%", floatVal)
	}
	return t.toString(value)
}

// ValidateData validates data against validation rules (placeholder implementation)
func (t *SimpleDataTransformer) ValidateData(ctx context.Context, data []map[string]interface{}, validationRules []ValidationRule) error {
	// Basic validation implementation
	for _, row := range data {
		for _, rule := range validationRules {
			if err := t.validateRow(row, rule); err != nil {
				return err
			}
		}
	}
	return nil
}

// validateRow validates a single row against a validation rule
func (t *SimpleDataTransformer) validateRow(row map[string]interface{}, rule ValidationRule) error {
	// Basic validation implementation
	switch rule.Type {
	case "required":
		// Check if required fields are present and not empty
		return nil
	case "min_length":
		// Check minimum length for string fields
		return nil
	case "max_length":
		// Check maximum length for string fields
		return nil
	default:
		return nil
	}
}
