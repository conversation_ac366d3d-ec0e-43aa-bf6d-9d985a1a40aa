package examples

import (
	"finbox/go-api/internal/service/dynamicmyqueue"
)

// GetDefaultTaskCasesConfig returns a default configuration for task cases
func GetDefaultTaskCasesConfig() *dynamicmyqueue.MetadataConfig {
	return &dynamicmyqueue.MetadataConfig{
		ClientID:   "default",
		ConfigName: "task_cases_default",
		Version:    "1.0",
		IsActive:   true,
		TableJoins: []dynamicmyqueue.TableJoinConfig{
			{
				TableName:     "loan_application",
				Alias:         "la",
				JoinType:      dynamicmyqueue.InnerJoin,
				JoinCondition: "",
				IsRequired:    true,
			},
			{
				TableName:     "users",
				Alias:         "u",
				JoinType:      dynamicmyqueue.LeftJoin,
				JoinCondition: "u.user_id = la.user_id",
				IsRequired:    false,
			},
		},
		FieldMappings: []dynamicmyqueue.FieldMapping{
			{
				SourceField:  "la.loan_application_id",
				TargetField:  "loanApplicationId",
				DataType:     dynamicmyqueue.StringType,
				IsVisible:    true,
				IsSearchable: true,
				IsSortable:   true,
			},
			{
				SourceField:  "u.name",
				TargetField:  "userName",
				DataType:     dynamicmyqueue.StringType,
				IsVisible:    true,
				IsSearchable: true,
				IsSortable:   true,
			},
			{
				SourceField:  "la.status",
				TargetField:  "status",
				DataType:     dynamicmyqueue.StringType,
				IsVisible:    true,
				IsSearchable: true,
				IsSortable:   true,
			},
			{
				SourceField:  "la.created_at",
				TargetField:  "createdAt",
				DataType:     dynamicmyqueue.DateTimeType,
				IsVisible:    true,
				IsSearchable: false,
				IsSortable:   true,
			},
		},
		FilterConditions: []dynamicmyqueue.FilterCondition{
			{
				FieldName:     "la.status",
				Operator:      dynamicmyqueue.Equal,
				IsUserDefined: true,
				IsRequired:    false,
			},
			{
				FieldName:     "la.created_at",
				Operator:      dynamicmyqueue.GreaterThanOrEqual,
				IsUserDefined: true,
				IsRequired:    false,
			},
		},
		SortingConfig: dynamicmyqueue.SortingConfig{
			DefaultSort: []dynamicmyqueue.SortField{
				{
					FieldName: "la.created_at",
					Direction: dynamicmyqueue.Descending,
				},
			},
		},
		PaginationConfig: dynamicmyqueue.PaginationConfig{
			DefaultPageSize: 20,
			MaxPageSize:     100,
			AllowedSizes:    []int{10, 20, 50, 100},
		},
		CreatedBy: "system",
		UpdatedBy: "system",
	}
}

// GetTaskWorkflowConfig returns a configuration for the task workflow query
func GetTaskWorkflowConfig() *dynamicmyqueue.MetadataConfig {
	return &dynamicmyqueue.MetadataConfig{
		ClientID:   "mfl",
		ConfigName: "task_workflow_cases",
		Version:    "1.0",
		IsActive:   true,
		TableJoins: []dynamicmyqueue.TableJoinConfig{
			{
				TableName:     "loan_application",
				Alias:         "la",
				JoinType:      dynamicmyqueue.InnerJoin,
				JoinCondition: "",
				IsRequired:    true,
				Metadata:      map[string]string{"description": "Main loan application table"},
			},
			{
				TableName:     "users",
				Alias:         "u",
				JoinType:      dynamicmyqueue.InnerJoin,
				JoinCondition: "la.user_id = u.user_id",
				IsRequired:    true,
				Metadata:      map[string]string{"description": "User information"},
			},
			{
				TableName:     "tasks",
				Alias:         "t",
				JoinType:      dynamicmyqueue.LeftJoin,
				JoinCondition: "t.identifier_id = la.loan_application_id",
				IsRequired:    false,
				Metadata:      map[string]string{"description": "Task information"},
			},
			{
				TableName:     "task_types",
				Alias:         "tt",
				JoinType:      dynamicmyqueue.LeftJoin,
				JoinCondition: "t.type_id = tt.id",
				IsRequired:    false,
				Metadata:      map[string]string{"description": "Task type information"},
			},
			{
				TableName:     "workflow_instances",
				Alias:         "wi",
				JoinType:      dynamicmyqueue.LeftJoin,
				JoinCondition: "wi.identifier_id = t.id",
				IsRequired:    false,
				Metadata:      map[string]string{"description": "Workflow instance information"},
			},
		},
		FieldMappings: []dynamicmyqueue.FieldMapping{
			{
				SourceField:  "la.loan_application_id",
				TargetField:  "loanApplicationId",
				DataType:     dynamicmyqueue.StringType,
				IsVisible:    true,
				IsSearchable: true,
				IsSortable:   true,
				Metadata:     map[string]string{"label": "Loan Application ID", "format": "text"},
			},
			{
				SourceField:  "tt.type",
				TargetField:  "taskType",
				DataType:     dynamicmyqueue.StringType,
				IsVisible:    true,
				IsSearchable: true,
				IsSortable:   true,
				Metadata:     map[string]string{"label": "Task Type", "format": "text"},
			},
			{
				SourceField:  "u.name",
				TargetField:  "userName",
				DataType:     dynamicmyqueue.StringType,
				IsVisible:    true,
				IsSearchable: true,
				IsSortable:   true,
				Metadata:     map[string]string{"label": "User Name", "format": "text"},
			},
			{
				SourceField:  "u.mobile",
				TargetField:  "userMobile",
				DataType:     dynamicmyqueue.StringType,
				IsVisible:    true,
				IsSearchable: true,
				IsSortable:   false,
				Metadata:     map[string]string{"label": "Mobile Number", "format": "phone"},
			},
			{
				SourceField:  "t.status",
				TargetField:  "taskStatus",
				DataType:     dynamicmyqueue.StringType,
				IsVisible:    true,
				IsSearchable: true,
				IsSortable:   true,
				Metadata:     map[string]string{"label": "Task Status", "format": "status"},
			},
			{
				SourceField:  "wi.status",
				TargetField:  "workflowStatus",
				DataType:     dynamicmyqueue.StringType,
				IsVisible:    true,
				IsSearchable: true,
				IsSortable:   true,
				Metadata:     map[string]string{"label": "Workflow Status", "format": "status"},
			},
			{
				SourceField:  "la.created_at",
				TargetField:  "createdAt",
				DataType:     dynamicmyqueue.DateTimeType,
				IsVisible:    true,
				IsSearchable: false,
				IsSortable:   true,
				Metadata:     map[string]string{"label": "Created At", "format": "datetime"},
			},
			{
				SourceField:  "t.created_at",
				TargetField:  "taskCreatedAt",
				DataType:     dynamicmyqueue.DateTimeType,
				IsVisible:    true,
				IsSearchable: false,
				IsSortable:   true,
				Metadata:     map[string]string{"label": "Task Created At", "format": "datetime"},
			},
		},
		FilterConditions: []dynamicmyqueue.FilterCondition{
			{
				FieldName:     "la.loan_application_id",
				Operator:      dynamicmyqueue.Equal,
				IsUserDefined: true,
				IsRequired:    false,
				Metadata:      map[string]string{"label": "Loan Application ID", "type": "text"},
			},
			{
				FieldName:     "tt.type",
				Operator:      dynamicmyqueue.Equal,
				IsUserDefined: true,
				IsRequired:    false,
				Metadata:      map[string]string{"label": "Task Type", "type": "select"},
			},
			{
				FieldName:     "t.status",
				Operator:      dynamicmyqueue.Equal,
				IsUserDefined: true,
				IsRequired:    false,
				Metadata:      map[string]string{"label": "Task Status", "type": "select"},
			},
			{
				FieldName:     "wi.status",
				Operator:      dynamicmyqueue.Equal,
				IsUserDefined: true,
				IsRequired:    false,
				Metadata:      map[string]string{"label": "Workflow Status", "type": "select"},
			},
			{
				FieldName:     "u.mobile",
				Operator:      dynamicmyqueue.Equal,
				IsUserDefined: true,
				IsRequired:    false,
				Metadata:      map[string]string{"label": "Mobile Number", "type": "text"},
			},
			{
				FieldName:     "la.created_at",
				Operator:      dynamicmyqueue.GreaterThanOrEqual,
				IsUserDefined: true,
				IsRequired:    false,
				Metadata:      map[string]string{"label": "Created After", "type": "date"},
			},
			{
				FieldName:     "la.created_at",
				Operator:      dynamicmyqueue.LessThanOrEqual,
				IsUserDefined: true,
				IsRequired:    false,
				Metadata:      map[string]string{"label": "Created Before", "type": "date"},
			},
		},
		SortingConfig: dynamicmyqueue.SortingConfig{
			DefaultSort: []dynamicmyqueue.SortField{
				{
					FieldName: "la.created_at",
					Direction: dynamicmyqueue.Descending,
				},
			},
			AvailableSorts: []dynamicmyqueue.SortField{
				{
					FieldName: "la.created_at",
					Direction: dynamicmyqueue.Descending,
				},
				{
					FieldName: "la.created_at",
					Direction: dynamicmyqueue.Ascending,
				},
				{
					FieldName: "tt.type",
					Direction: dynamicmyqueue.Ascending,
				},
				{
					FieldName: "t.status",
					Direction: dynamicmyqueue.Ascending,
				},
			},
		},
		PaginationConfig: dynamicmyqueue.PaginationConfig{
			DefaultPageSize: 20,
			MaxPageSize:     100,
			AllowedSizes:    []int{10, 20, 50, 100},
		},
		CreatedBy: "system",
		UpdatedBy: "system",
	}
}

// GetMinimalConfig returns a minimal configuration for testing
func GetMinimalConfig() *dynamicmyqueue.MetadataConfig {
	return &dynamicmyqueue.MetadataConfig{
		ClientID:   "test",
		ConfigName: "minimal_test",
		Version:    "1.0",
		IsActive:   true,
		TableJoins: []dynamicmyqueue.TableJoinConfig{
			{
				TableName:     "loan_application",
				Alias:         "la",
				JoinType:      dynamicmyqueue.InnerJoin,
				JoinCondition: "",
				IsRequired:    true,
			},
		},
		FieldMappings: []dynamicmyqueue.FieldMapping{
			{
				SourceField:  "la.loan_application_id",
				TargetField:  "id",
				DataType:     dynamicmyqueue.StringType,
				IsVisible:    true,
				IsSearchable: true,
				IsSortable:   true,
			},
		},
		SortingConfig: dynamicmyqueue.SortingConfig{
			DefaultSort: []dynamicmyqueue.SortField{
				{FieldName: "la.created_at", Direction: dynamicmyqueue.Descending},
			},
		},
		PaginationConfig: dynamicmyqueue.PaginationConfig{
			DefaultPageSize: 10,
			MaxPageSize:     50,
			AllowedSizes:    []int{10, 25, 50},
		},
		CreatedBy: "system",
		UpdatedBy: "system",
	}
}
