package examples

import (
	"finbox/go-api/internal/service/dynamicmyqueue"
)

// GetDefaultTaskCasesConfig returns a default configuration for task cases
func GetDefaultTaskCasesConfig() *dynamicmyqueue.MetadataConfig {
	return &dynamicmyqueue.MetadataConfig{
		ClientID:   "default",
		ConfigName: "task_cases_default",
		Version:    "1.0",
		IsActive:   true,
		TableJoins: []dynamicmyqueue.TableJoinConfig{
			{
				TableName:     "loan_application",
				Alias:         "la",
				JoinType:      dynamicmyqueue.InnerJoin,
				JoinCondition: "",
				IsRequired:    true,
			},
			{
				TableName:     "users",
				Alias:         "u",
				JoinType:      dynamicmyqueue.LeftJoin,
				JoinCondition: "u.user_id = la.user_id",
				IsRequired:    false,
			},
		},
		FieldMappings: []dynamicmyqueue.FieldMapping{
			{
				SourceField:  "la.loan_application_id",
				TargetField:  "loanApplicationId",
				DataType:     dynamicmyqueue.StringType,
				IsVisible:    true,
				IsSearchable: true,
				IsSortable:   true,
			},
			{
				SourceField:  "u.name",
				TargetField:  "userName",
				DataType:     dynamicmyqueue.StringType,
				IsVisible:    true,
				IsSearchable: true,
				IsSortable:   true,
			},
			{
				SourceField:  "la.status",
				TargetField:  "status",
				DataType:     dynamicmyqueue.StringType,
				IsVisible:    true,
				IsSearchable: true,
				IsSortable:   true,
			},
			{
				SourceField:  "la.created_at",
				TargetField:  "createdAt",
				DataType:     dynamicmyqueue.DateTimeType,
				IsVisible:    true,
				IsSearchable: false,
				IsSortable:   true,
			},
		},
		FilterConditions: []dynamicmyqueue.FilterCondition{
			{
				FieldName:     "la.status",
				Operator:      dynamicmyqueue.Equal,
				IsUserDefined: true,
				IsRequired:    false,
			},
			{
				FieldName:     "la.created_at",
				Operator:      dynamicmyqueue.GreaterThanOrEqual,
				IsUserDefined: true,
				IsRequired:    false,
			},
		},
		SortingConfig: dynamicmyqueue.SortingConfig{
			DefaultSort: []dynamicmyqueue.SortField{
				{
					FieldName: "la.created_at",
					Direction: dynamicmyqueue.Descending,
				},
			},
		},
		PaginationConfig: dynamicmyqueue.PaginationConfig{
			DefaultPageSize: 20,
			MaxPageSize:     100,
			AllowedSizes:    []int{10, 20, 50, 100},
		},
		CreatedBy: "system",
		UpdatedBy: "system",
	}
}

// GetMinimalConfig returns a minimal configuration for testing
func GetMinimalConfig() *dynamicmyqueue.MetadataConfig {
	return &dynamicmyqueue.MetadataConfig{
		ClientID:   "test",
		ConfigName: "minimal_test",
		Version:    "1.0",
		IsActive:   true,
		TableJoins: []dynamicmyqueue.TableJoinConfig{
			{
				TableName:     "loan_application",
				Alias:         "la",
				JoinType:      dynamicmyqueue.InnerJoin,
				JoinCondition: "",
				IsRequired:    true,
			},
		},
		FieldMappings: []dynamicmyqueue.FieldMapping{
			{
				SourceField:  "la.loan_application_id",
				TargetField:  "id",
				DataType:     dynamicmyqueue.StringType,
				IsVisible:    true,
				IsSearchable: true,
				IsSortable:   true,
			},
		},
		SortingConfig: dynamicmyqueue.SortingConfig{
			DefaultSort: []dynamicmyqueue.SortField{
				{FieldName: "la.created_at", Direction: dynamicmyqueue.Descending},
			},
		},
		PaginationConfig: dynamicmyqueue.PaginationConfig{
			DefaultPageSize: 10,
			MaxPageSize:     50,
			AllowedSizes:    []int{10, 25, 50},
		},
		CreatedBy: "system",
		UpdatedBy: "system",
	}
}
