package examples

import (
	"context"
	"fmt"
	"log"

	"finbox/go-api/internal/service/dynamicmyqueue"
)

// DemoQueryGeneration demonstrates how the dynamic query system works
func DemoQueryGeneration() {
	fmt.Println("=== Dynamic Queue Query Generation Demo ===\n")

	// Get the task workflow configuration
	config := GetTaskWorkflowConfig()
	fmt.Printf("Configuration: %s for client: %s\n", config.ConfigName, config.ClientID)
	fmt.Printf("Tables involved: %d\n", len(config.TableJoins))
	fmt.Printf("Field mappings: %d\n", len(config.FieldMappings))
	fmt.Printf("Filter conditions: %d\n\n", len(config.FilterConditions))

	// Create query builder and transformer
	queryBuilder := dynamicmyqueue.NewSimpleQueryBuilder()
	dataTransformer := dynamicmyqueue.NewSimpleDataTransformer()
	ctx := context.Background()

	// Demo 1: Basic query without filters
	fmt.Println("--- Demo 1: Basic Query (No Filters) ---")
	basicRequest := &dynamicmyqueue.TaskCasesRequest{
		ClientID:   "mfl",
		ConfigName: "task_workflow_cases",
		Filters:    map[string]interface{}{},
		Page:       1,
		PageSize:   20,
	}

	basicQuery, err := queryBuilder.BuildQuery(ctx, config, basicRequest)
	if err != nil {
		log.Printf("Error building basic query: %v", err)
		return
	}

	fmt.Printf("Generated SQL:\n%s\n", basicQuery.Query)
	fmt.Printf("Parameters: %v\n\n", basicQuery.Parameters)

	// Demo 2: Query with single filter
	fmt.Println("--- Demo 2: Query with Task Type Filter ---")
	filteredRequest := &dynamicmyqueue.TaskCasesRequest{
		ClientID:   "mfl",
		ConfigName: "task_workflow_cases",
		Filters: map[string]interface{}{
			"tt.type": "DOCUMENT_VERIFICATION",
		},
		Page:     1,
		PageSize: 20,
	}

	filteredQuery, err := queryBuilder.BuildQuery(ctx, config, filteredRequest)
	if err != nil {
		log.Printf("Error building filtered query: %v", err)
		return
	}

	fmt.Printf("Generated SQL:\n%s\n", filteredQuery.Query)
	fmt.Printf("Parameters: %v\n\n", filteredQuery.Parameters)

	// Demo 3: Query with multiple filters
	fmt.Println("--- Demo 3: Query with Multiple Filters ---")
	multiFilterRequest := &dynamicmyqueue.TaskCasesRequest{
		ClientID:   "mfl",
		ConfigName: "task_workflow_cases",
		Filters: map[string]interface{}{
			"tt.type":    "DOCUMENT_VERIFICATION",
			"t.status":   "PENDING",
			"u.mobile":   "9876543210",
		},
		Sort: []dynamicmyqueue.SortField{
			{FieldName: "tt.type", Direction: dynamicmyqueue.Ascending},
			{FieldName: "la.created_at", Direction: dynamicmyqueue.Descending},
		},
		Page:     2,
		PageSize: 10,
	}

	multiFilterQuery, err := queryBuilder.BuildQuery(ctx, config, multiFilterRequest)
	if err != nil {
		log.Printf("Error building multi-filter query: %v", err)
		return
	}

	fmt.Printf("Generated SQL:\n%s\n", multiFilterQuery.Query)
	fmt.Printf("Parameters: %v\n\n", multiFilterQuery.Parameters)

	// Demo 4: Count query
	fmt.Println("--- Demo 4: Count Query ---")
	countQuery, err := queryBuilder.BuildCountQuery(ctx, config, multiFilterRequest)
	if err != nil {
		log.Printf("Error building count query: %v", err)
		return
	}

	fmt.Printf("Generated Count SQL:\n%s\n", countQuery.Query)
	fmt.Printf("Parameters: %v\n\n", countQuery.Parameters)

	// Demo 5: Data transformation
	fmt.Println("--- Demo 5: Data Transformation ---")
	
	// Mock raw data as it would come from the database
	rawData := []map[string]interface{}{
		{
			"loan_application_id": "LA123456",
			"type":               "DOCUMENT_VERIFICATION",
			"name":               "john doe",
			"mobile":             "9876543210",
			"status":             "pending",
			"created_at":         "2024-01-15T10:30:00Z",
			"task_created_at":    "2024-01-15T11:00:00Z",
		},
		{
			"loan_application_id": "LA789012",
			"type":               "CREDIT_CHECK",
			"name":               "jane smith",
			"mobile":             "8765432109",
			"status":             "completed",
			"created_at":         "2024-01-14T09:15:00Z",
			"task_created_at":    "2024-01-14T10:00:00Z",
		},
	}

	fmt.Printf("Raw Data (before transformation):\n")
	for i, row := range rawData {
		fmt.Printf("Row %d: %v\n", i+1, row)
	}

	transformedData, err := dataTransformer.Transform(ctx, rawData, config.FieldMappings)
	if err != nil {
		log.Printf("Error transforming data: %v", err)
		return
	}

	fmt.Printf("\nTransformed Data (after field mapping and formatting):\n")
	for i, row := range transformedData {
		fmt.Printf("Row %d: %v\n", i+1, row)
	}

	fmt.Println("\n=== Demo Complete ===")
}

// DemoFilterTypes demonstrates different filter operators
func DemoFilterTypes() {
	fmt.Println("\n=== Filter Types Demo ===\n")

	config := GetTaskWorkflowConfig()
	queryBuilder := dynamicmyqueue.NewSimpleQueryBuilder()
	ctx := context.Background()

	filterExamples := []struct {
		name    string
		filters map[string]interface{}
		desc    string
	}{
		{
			name: "Equality Filter",
			filters: map[string]interface{}{
				"tt.type": "DOCUMENT_VERIFICATION",
			},
			desc: "Filter tasks by specific type",
		},
		{
			name: "Multiple Equality Filters",
			filters: map[string]interface{}{
				"tt.type":  "DOCUMENT_VERIFICATION",
				"t.status": "PENDING",
			},
			desc: "Filter by task type AND status",
		},
		{
			name: "Date Range Filter",
			filters: map[string]interface{}{
				"la.created_at": "2024-01-01",
			},
			desc: "Filter by creation date",
		},
		{
			name: "Mobile Number Filter",
			filters: map[string]interface{}{
				"u.mobile": "9876543210",
			},
			desc: "Filter by user mobile number",
		},
		{
			name: "Complex Multi-Field Filter",
			filters: map[string]interface{}{
				"tt.type":    "CREDIT_CHECK",
				"t.status":   "COMPLETED",
				"wi.status":  "APPROVED",
				"u.mobile":   "9876543210",
			},
			desc: "Complex filter with multiple conditions",
		},
	}

	for _, example := range filterExamples {
		fmt.Printf("--- %s ---\n", example.name)
		fmt.Printf("Description: %s\n", example.desc)

		request := &dynamicmyqueue.TaskCasesRequest{
			ClientID:   "mfl",
			ConfigName: "task_workflow_cases",
			Filters:    example.filters,
			Page:       1,
			PageSize:   20,
		}

		query, err := queryBuilder.BuildQuery(ctx, config, request)
		if err != nil {
			log.Printf("Error: %v", err)
			continue
		}

		fmt.Printf("Filters Applied: %v\n", example.filters)
		fmt.Printf("Generated WHERE clause:\n")
		
		// Extract WHERE clause from the full query
		lines := splitLines(query.Query)
		for _, line := range lines {
			if contains(line, "WHERE") || contains(line, "AND") || contains(line, "=") {
				fmt.Printf("  %s\n", line)
			}
		}
		
		fmt.Printf("Parameters: %v\n\n", query.Parameters)
	}
}

// Helper functions
func splitLines(text string) []string {
	lines := []string{}
	current := ""
	for _, char := range text {
		if char == '\n' {
			if current != "" {
				lines = append(lines, current)
				current = ""
			}
		} else {
			current += string(char)
		}
	}
	if current != "" {
		lines = append(lines, current)
	}
	return lines
}

func contains(text, substr string) bool {
	return len(text) >= len(substr) && findSubstring(text, substr) >= 0
}

func findSubstring(text, substr string) int {
	if len(substr) == 0 {
		return 0
	}
	if len(text) < len(substr) {
		return -1
	}
	
	for i := 0; i <= len(text)-len(substr); i++ {
		match := true
		for j := 0; j < len(substr); j++ {
			if text[i+j] != substr[j] {
				match = false
				break
			}
		}
		if match {
			return i
		}
	}
	return -1
}
