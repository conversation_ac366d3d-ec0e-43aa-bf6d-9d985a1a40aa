package dynamicmyqueue

import (
	"context"
)

// DynamicQueueService defines the main service interface for metadata-driven task cases
type DynamicQueueService interface {
	// Configuration management
	CreateConfig(ctx context.Context, config *MetadataConfig) error
	UpdateConfig(ctx context.Context, clientID, configName string, config *MetadataConfig) error
	GetConfig(ctx context.Context, clientID, configName string) (*MetadataConfig, error)
	ListConfigs(ctx context.Context, clientID string) ([]*MetadataConfig, error)
	DeleteConfig(ctx context.Context, clientID, configName string) error

	// Data retrieval
	GetTaskCases(ctx context.Context, request *TaskCasesRequest) (*TaskCasesResponse, error)
	GetTaskCaseDetails(ctx context.Context, clientID, configName, taskID string) (*TaskCaseDetails, error)

	// Validation and testing
	ValidateConfig(ctx context.Context, config *MetadataConfig) (*ValidationResult, error)
	TestQuery(ctx context.Context, clientID, configName string, filters map[string]interface{}) (*QueryTestResult, error)
}

// ConfigRepository defines the data access interface for metadata configurations
type ConfigRepository interface {
	Create(ctx context.Context, config *MetadataConfig) error
	Update(ctx context.Context, clientID, configName string, config *MetadataConfig) error
	GetByClientAndName(ctx context.Context, clientID, configName string) (*MetadataConfig, error)
	ListByClient(ctx context.Context, clientID string) ([]*MetadataConfig, error)
	Delete(ctx context.Context, clientID, configName string) error
	GetActiveConfig(ctx context.Context, clientID string) (*MetadataConfig, error)
}

// QueryBuilder defines the interface for building dynamic SQL queries
type QueryBuilder interface {
	BuildQuery(ctx context.Context, config *MetadataConfig, request *TaskCasesRequest) (*QueryResult, error)
	BuildCountQuery(ctx context.Context, config *MetadataConfig, request *TaskCasesRequest) (*QueryResult, error)
	ValidateQuery(ctx context.Context, config *MetadataConfig) error
}

// DataTransformer defines the interface for transforming raw data based on field mappings
type DataTransformer interface {
	Transform(ctx context.Context, rawData []map[string]interface{}, fieldMappings []FieldMapping) ([]map[string]interface{}, error)
	ValidateData(ctx context.Context, data []map[string]interface{}, validationRules []ValidationRule) error
}

// FilterProcessor defines the interface for processing dynamic filters
type FilterProcessor interface {
	ProcessFilters(ctx context.Context, filters map[string]interface{}, filterConditions []FilterCondition) ([]ProcessedFilter, error)
	ValidateFilters(ctx context.Context, filters map[string]interface{}, filterConditions []FilterCondition) error
}

// CacheManager defines the interface for caching configurations and query results
type CacheManager interface {
	GetConfig(ctx context.Context, clientID, configName string) (*MetadataConfig, error)
	SetConfig(ctx context.Context, clientID, configName string, config *MetadataConfig) error
	InvalidateConfig(ctx context.Context, clientID, configName string) error
	GetQueryResult(ctx context.Context, queryHash string) (*TaskCasesResponse, error)
	SetQueryResult(ctx context.Context, queryHash string, result *TaskCasesResponse, ttl int) error
}

// Request and Response types
type TaskCasesRequest struct {
	ClientID    string                 `json:"clientId"`
	ConfigName  string                 `json:"configName"`
	Filters     map[string]interface{} `json:"filters"`
	Sort        []SortField            `json:"sort"`
	Page        int                    `json:"page"`
	PageSize    int                    `json:"pageSize"`
	SearchQuery string                 `json:"searchQuery"`
	ExtraParams map[string]interface{} `json:"extraParams"`
}

type TaskCasesResponse struct {
	Data            []map[string]interface{} `json:"data"`
	TotalCount      int64                    `json:"totalCount"`
	Page            int                      `json:"page"`
	PageSize        int                      `json:"pageSize"`
	HasNextPage     bool                     `json:"hasNextPage"`
	HasPreviousPage bool                     `json:"hasPreviousPage"`
	UIConfig        UIConfiguration          `json:"uiConfig"`
	Metadata        map[string]interface{}   `json:"metadata"`
}

type TaskCaseDetails struct {
	ID       string                 `json:"id"`
	Data     map[string]interface{} `json:"data"`
	Actions  []ActionConfig         `json:"actions"`
	Metadata map[string]interface{} `json:"metadata"`
}

type ValidationResult struct {
	IsValid  bool                `json:"isValid"`
	Errors   []ValidationError   `json:"errors"`
	Warnings []ValidationWarning `json:"warnings"`
}

type ValidationError struct {
	Field   string `json:"field"`
	Message string `json:"message"`
	Code    string `json:"code"`
}

type ValidationWarning struct {
	Field   string `json:"field"`
	Message string `json:"message"`
	Code    string `json:"code"`
}

type QueryTestResult struct {
	Query         string                   `json:"query"`
	Parameters    map[string]interface{}   `json:"parameters"`
	SampleData    []map[string]interface{} `json:"sampleData"`
	ExecutionTime int64                    `json:"executionTime"`
	RowCount      int64                    `json:"rowCount"`
}

type QueryResult struct {
	Query      string                 `json:"query"`
	Parameters map[string]interface{} `json:"parameters"`
}

type ProcessedFilter struct {
	Field     string      `json:"field"`
	Operator  string      `json:"operator"`
	Value     interface{} `json:"value"`
	SQLClause string      `json:"sqlClause"`
}

// Factory interfaces for extensibility
type QueueBuilderFactory interface {
	CreateBuilder(clientID string) (QueryBuilder, error)
	RegisterBuilder(clientID string, builder QueryBuilder)
	GetSupportedClients() []string
}

type TransformerFactory interface {
	CreateTransformer(transformationType string) (DataTransformer, error)
	RegisterTransformer(transformationType string, transformer DataTransformer)
	GetSupportedTransformations() []string
}

// Strategy interfaces for different client implementations
type ClientStrategy interface {
	GetDefaultConfig() *MetadataConfig
	ValidateConfig(config *MetadataConfig) error
	CustomizeQuery(query *QueryResult, config *MetadataConfig) (*QueryResult, error)
	PostProcessData(data []map[string]interface{}, config *MetadataConfig) ([]map[string]interface{}, error)
}

// Event interfaces for extensibility and monitoring
type EventPublisher interface {
	PublishConfigCreated(ctx context.Context, config *MetadataConfig) error
	PublishConfigUpdated(ctx context.Context, oldConfig, newConfig *MetadataConfig) error
	PublishQueryExecuted(ctx context.Context, clientID, configName string, executionTime int64) error
}

type MetricsCollector interface {
	RecordQueryExecution(clientID, configName string, executionTime int64, rowCount int64)
	RecordConfigUsage(clientID, configName string)
	RecordError(clientID, configName, errorType string)
}
