package dynamicmyqueue

import (
	"context"
	"strings"
	"testing"

	"finbox/go-api/internal/service/dynamicmyqueue/examples"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestQueryBuilder_BuildQuery(t *testing.T) {
	builder := NewSimpleQueryBuilder()
	config := examples.GetTaskWorkflowConfig()
	ctx := context.Background()

	tests := []struct {
		name           string
		request        *TaskCasesRequest
		expectedSQL    string
		expectedParams map[string]interface{}
	}{
		{
			name: "Basic query without filters",
			request: &TaskCasesRequest{
				ClientID:   "mfl",
				ConfigName: "task_workflow_cases",
				Filters:    map[string]interface{}{},
				Page:       1,
				PageSize:   20,
			},
			expectedSQL: `SELECT DISTINCT la.loan_application_id, tt.type, u.name, u.mobile, t.status, wi.status, la.created_at, t.created_at
FROM loan_application la 
JOIN users u 
	ON la.user_id = u.user_id 
LEFT JOIN tasks t 
	ON t.identifier_id = la.loan_application_id 
LEFT JOIN task_types tt 
	ON t.type_id = tt.id 
LEFT JOIN workflow_instances wi 
	ON wi.identifier_id = t.id
ORDER BY la.created_at DESC
LIMIT 20 OFFSET 0`,
			expectedParams: map[string]interface{}{},
		},
		{
			name: "Query with task type filter",
			request: &TaskCasesRequest{
				ClientID:   "mfl",
				ConfigName: "task_workflow_cases",
				Filters: map[string]interface{}{
					"tt.type": "DOCUMENT_VERIFICATION",
				},
				Page:     1,
				PageSize: 20,
			},
			expectedSQL: `SELECT DISTINCT la.loan_application_id, tt.type, u.name, u.mobile, t.status, wi.status, la.created_at, t.created_at
FROM loan_application la 
JOIN users u 
	ON la.user_id = u.user_id 
LEFT JOIN tasks t 
	ON t.identifier_id = la.loan_application_id 
LEFT JOIN task_types tt 
	ON t.type_id = tt.id 
LEFT JOIN workflow_instances wi 
	ON wi.identifier_id = t.id
WHERE tt.type = $param1
ORDER BY la.created_at DESC
LIMIT 20 OFFSET 0`,
			expectedParams: map[string]interface{}{
				"param1": "DOCUMENT_VERIFICATION",
			},
		},
		{
			name: "Query with multiple filters",
			request: &TaskCasesRequest{
				ClientID:   "mfl",
				ConfigName: "task_workflow_cases",
				Filters: map[string]interface{}{
					"tt.type":  "DOCUMENT_VERIFICATION",
					"t.status": "PENDING",
					"u.mobile": "9876543210",
				},
				Page:     2,
				PageSize: 10,
			},
			expectedSQL: `SELECT DISTINCT la.loan_application_id, tt.type, u.name, u.mobile, t.status, wi.status, la.created_at, t.created_at
FROM loan_application la 
JOIN users u 
	ON la.user_id = u.user_id 
LEFT JOIN tasks t 
	ON t.identifier_id = la.loan_application_id 
LEFT JOIN task_types tt 
	ON t.type_id = tt.id 
LEFT JOIN workflow_instances wi 
	ON wi.identifier_id = t.id
WHERE la.loan_application_id = $param1 AND tt.type = $param2 AND t.status = $param3 AND wi.status = $param4 AND u.mobile = $param5
ORDER BY la.created_at DESC
LIMIT 10 OFFSET 10`,
			expectedParams: map[string]interface{}{
				"param2": "DOCUMENT_VERIFICATION",
				"param3": "PENDING",
				"param5": "9876543210",
			},
		},
		{
			name: "Query with date range filters",
			request: &TaskCasesRequest{
				ClientID:   "mfl",
				ConfigName: "task_workflow_cases",
				Filters: map[string]interface{}{
					"la.created_at": "2024-01-01",
				},
				Sort: []SortField{
					{FieldName: "tt.type", Direction: Ascending},
				},
				Page:     1,
				PageSize: 50,
			},
			expectedSQL: `SELECT DISTINCT la.loan_application_id, tt.type, u.name, u.mobile, t.status, wi.status, la.created_at, t.created_at
FROM loan_application la 
JOIN users u 
	ON la.user_id = u.user_id 
LEFT JOIN tasks t 
	ON t.identifier_id = la.loan_application_id 
LEFT JOIN task_types tt 
	ON t.type_id = tt.id 
LEFT JOIN workflow_instances wi 
	ON wi.identifier_id = t.id
WHERE la.created_at = $param1
ORDER BY tt.type ASC
LIMIT 50 OFFSET 0`,
			expectedParams: map[string]interface{}{
				"param1": "2024-01-01",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := builder.BuildQuery(ctx, config, tt.request)
			require.NoError(t, err)
			require.NotNil(t, result)

			// Normalize whitespace for comparison
			actualSQL := normalizeSQL(result.Query)
			expectedSQL := normalizeSQL(tt.expectedSQL)

			assert.Equal(t, expectedSQL, actualSQL, "SQL query should match expected")

			// Check that all expected parameters are present
			for key, expectedValue := range tt.expectedParams {
				actualValue, exists := result.Parameters[key]
				assert.True(t, exists, "Parameter %s should exist", key)
				assert.Equal(t, expectedValue, actualValue, "Parameter %s should have correct value", key)
			}
		})
	}
}

func TestQueryBuilder_BuildCountQuery(t *testing.T) {
	builder := NewSimpleQueryBuilder()
	config := examples.GetTaskWorkflowConfig()
	ctx := context.Background()

	request := &TaskCasesRequest{
		ClientID:   "mfl",
		ConfigName: "task_workflow_cases",
		Filters: map[string]interface{}{
			"tt.type": "DOCUMENT_VERIFICATION",
		},
	}

	result, err := builder.BuildCountQuery(ctx, config, request)
	require.NoError(t, err)
	require.NotNil(t, result)

	expectedSQL := `SELECT COUNT(DISTINCT la.loan_application_id) as total_count
FROM loan_application la 
JOIN users u 
	ON la.user_id = u.user_id 
LEFT JOIN tasks t 
	ON t.identifier_id = la.loan_application_id 
LEFT JOIN task_types tt 
	ON t.type_id = tt.id 
LEFT JOIN workflow_instances wi 
	ON wi.identifier_id = t.id
WHERE tt.type = $param1`

	actualSQL := normalizeSQL(result.Query)
	expectedSQL = normalizeSQL(expectedSQL)

	assert.Equal(t, expectedSQL, actualSQL)
	assert.Equal(t, "DOCUMENT_VERIFICATION", result.Parameters["param1"])
}

func TestDataTransformer_Transform(t *testing.T) {
	transformer := NewSimpleDataTransformer()
	config := examples.GetTaskWorkflowConfig()
	ctx := context.Background()

	// Mock raw data as it would come from database
	rawData := []map[string]interface{}{
		{
			"loan_application_id": "LA123456",
			"type":                "DOCUMENT_VERIFICATION",
			"name":                "john doe",
			"mobile":              "9876543210",
			"status":              "pending",
			"created_at":          "2024-01-15T10:30:00Z",
		},
		{
			"loan_application_id": "LA789012",
			"type":                "CREDIT_CHECK",
			"name":                "jane smith",
			"mobile":              "8765432109",
			"status":              "completed",
			"created_at":          "2024-01-14T09:15:00Z",
		},
	}

	result, err := transformer.Transform(ctx, rawData, config.FieldMappings)
	require.NoError(t, err)
	require.Len(t, result, 2)

	// Check first row transformation
	firstRow := result[0]
	assert.Equal(t, "LA123456", firstRow["loanApplicationId"])
	assert.Equal(t, "DOCUMENT_VERIFICATION", firstRow["taskType"])
	assert.Equal(t, "john doe", firstRow["userName"])
	assert.Equal(t, "+91-98765-43210", firstRow["userMobile"]) // Phone formatting
	assert.Equal(t, "Pending", firstRow["taskStatus"])         // Status formatting

	// Check second row transformation
	secondRow := result[1]
	assert.Equal(t, "LA789012", secondRow["loanApplicationId"])
	assert.Equal(t, "CREDIT_CHECK", secondRow["taskType"])
	assert.Equal(t, "jane smith", secondRow["userName"])
	assert.Equal(t, "+91-87654-32109", secondRow["userMobile"]) // Phone formatting
	assert.Equal(t, "Completed", secondRow["taskStatus"])       // Status formatting
}

func TestDynamicFilters(t *testing.T) {
	builder := NewSimpleQueryBuilder()
	config := examples.GetTaskWorkflowConfig()
	ctx := context.Background()

	tests := []struct {
		name            string
		filters         map[string]interface{}
		expectedFilters []string
	}{
		{
			name:            "No filters",
			filters:         map[string]interface{}{},
			expectedFilters: []string{},
		},
		{
			name: "Single filter",
			filters: map[string]interface{}{
				"tt.type": "DOCUMENT_VERIFICATION",
			},
			expectedFilters: []string{"tt.type = $param1"},
		},
		{
			name: "Multiple filters",
			filters: map[string]interface{}{
				"tt.type":  "DOCUMENT_VERIFICATION",
				"t.status": "PENDING",
				"u.mobile": "9876543210",
			},
			expectedFilters: []string{
				"tt.type = $param2",
				"t.status = $param3",
				"u.mobile = $param5",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			request := &TaskCasesRequest{
				ClientID:   "mfl",
				ConfigName: "task_workflow_cases",
				Filters:    tt.filters,
				Page:       1,
				PageSize:   20,
			}

			result, err := builder.BuildQuery(ctx, config, request)
			require.NoError(t, err)

			if len(tt.expectedFilters) == 0 {
				assert.NotContains(t, result.Query, "WHERE")
			} else {
				assert.Contains(t, result.Query, "WHERE")
				// Check that we have the right number of filter conditions
				assert.Contains(t, result.Query, "=")
			}

			// Verify parameter count matches filter count
			assert.Len(t, result.Parameters, len(tt.filters))
		})
	}
}

// Helper function to normalize SQL for comparison
func normalizeSQL(sql string) string {
	// Remove extra whitespace and normalize line breaks
	lines := strings.Split(sql, "\n")
	var normalizedLines []string
	for _, line := range lines {
		trimmed := strings.TrimSpace(line)
		if trimmed != "" {
			normalizedLines = append(normalizedLines, trimmed)
		}
	}
	return strings.Join(normalizedLines, "\n")
}
