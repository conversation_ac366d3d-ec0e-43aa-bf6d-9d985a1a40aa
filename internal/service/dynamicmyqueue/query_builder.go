package dynamicmyqueue

import (
	"context"
	"fmt"
	"strings"
)

// SimpleQueryBuilder builds SQL queries from metadata configuration
type SimpleQueryBuilder struct{}

// NewSimpleQueryBuilder creates a new query builder
func NewSimpleQueryBuilder() *SimpleQueryBuilder {
	return &SimpleQueryBuilder{}
}

// BuildQuery constructs the main SQL query based on configuration and request
func (qb *SimpleQueryBuilder) BuildQuery(ctx context.Context, config *MetadataConfig, request *TaskCasesRequest) (*QueryResult, error) {
	if config == nil {
		return nil, fmt.Errorf("config cannot be nil")
	}
	if request == nil {
		return nil, fmt.Errorf("request cannot be nil")
	}

	// Build SELECT clause
	selectClause := qb.buildSelectClause(config.FieldMappings)
	
	// Build FROM clause with JOINs
	fromClause := qb.buildFromClause(config.TableJoins)
	
	// Build WHERE clause with filters
	whereClause, parameters, err := qb.buildWhereClause(config.FilterConditions, request.Filters)
	if err != nil {
		return nil, fmt.Errorf("failed to build WHERE clause: %w", err)
	}
	
	// Build ORDER BY clause
	orderByClause := qb.buildOrderByClause(request.Sort, config.SortingConfig)
	
	// Build LIMIT and OFFSET clause
	limitClause := qb.buildLimitClause(request.Page, request.PageSize)
	
	// Combine all parts
	query := fmt.Sprintf("SELECT DISTINCT %s\nFROM %s", selectClause, fromClause)
	
	if whereClause != "" {
		query += fmt.Sprintf("\nWHERE %s", whereClause)
	}
	
	if orderByClause != "" {
		query += fmt.Sprintf("\nORDER BY %s", orderByClause)
	}
	
	if limitClause != "" {
		query += fmt.Sprintf("\n%s", limitClause)
	}

	return &QueryResult{
		Query:      query,
		Parameters: parameters,
	}, nil
}

// BuildCountQuery constructs a count query for pagination
func (qb *SimpleQueryBuilder) BuildCountQuery(ctx context.Context, config *MetadataConfig, request *TaskCasesRequest) (*QueryResult, error) {
	if config == nil {
		return nil, fmt.Errorf("config cannot be nil")
	}
	if request == nil {
		return nil, fmt.Errorf("request cannot be nil")
	}

	// Build FROM clause with JOINs
	fromClause := qb.buildFromClause(config.TableJoins)
	
	// Build WHERE clause with filters
	whereClause, parameters, err := qb.buildWhereClause(config.FilterConditions, request.Filters)
	if err != nil {
		return nil, fmt.Errorf("failed to build WHERE clause: %w", err)
	}
	
	// Build count query
	query := fmt.Sprintf("SELECT COUNT(DISTINCT %s) as total_count\nFROM %s", 
		config.FieldMappings[0].SourceField, fromClause)
	
	if whereClause != "" {
		query += fmt.Sprintf("\nWHERE %s", whereClause)
	}

	return &QueryResult{
		Query:      query,
		Parameters: parameters,
	}, nil
}

// buildSelectClause constructs the SELECT part of the query
func (qb *SimpleQueryBuilder) buildSelectClause(fieldMappings []FieldMapping) string {
	var fields []string
	for _, mapping := range fieldMappings {
		if mapping.IsVisible {
			fields = append(fields, fmt.Sprintf("%s", mapping.SourceField))
		}
	}
	return strings.Join(fields, ", ")
}

// buildFromClause constructs the FROM part with JOINs
func (qb *SimpleQueryBuilder) buildFromClause(tableJoins []TableJoinConfig) string {
	if len(tableJoins) == 0 {
		return ""
	}

	var parts []string
	
	// Start with the first table (main table)
	mainTable := tableJoins[0]
	parts = append(parts, fmt.Sprintf("%s %s", mainTable.TableName, mainTable.Alias))
	
	// Add JOIN clauses for remaining tables
	for i := 1; i < len(tableJoins); i++ {
		join := tableJoins[i]
		joinType := qb.getJoinTypeString(join.JoinType)
		
		if join.JoinCondition != "" {
			parts = append(parts, fmt.Sprintf("\n%s %s %s \n\tON %s", 
				joinType, join.TableName, join.Alias, join.JoinCondition))
		} else {
			parts = append(parts, fmt.Sprintf("\n%s %s %s", 
				joinType, join.TableName, join.Alias))
		}
	}
	
	return strings.Join(parts, " ")
}

// buildWhereClause constructs the WHERE part with dynamic filters
func (qb *SimpleQueryBuilder) buildWhereClause(filterConditions []FilterCondition, requestFilters map[string]interface{}) (string, map[string]interface{}, error) {
	var whereParts []string
	parameters := make(map[string]interface{})
	paramCounter := 1

	// Apply configured filter conditions that have values in the request
	for _, condition := range filterConditions {
		if condition.IsUserDefined {
			// Check if this filter has a value in the request
			if value, exists := requestFilters[condition.FieldName]; exists && value != nil {
				paramName := fmt.Sprintf("param%d", paramCounter)
				sqlClause := qb.buildFilterClause(condition.FieldName, condition.Operator, paramName)
				whereParts = append(whereParts, sqlClause)
				parameters[paramName] = value
				paramCounter++
			}
		} else if condition.IsRequired {
			// Required filters must have values
			if condition.Value != nil {
				paramName := fmt.Sprintf("param%d", paramCounter)
				sqlClause := qb.buildFilterClause(condition.FieldName, condition.Operator, paramName)
				whereParts = append(whereParts, sqlClause)
				parameters[paramName] = condition.Value
				paramCounter++
			}
		}
	}

	if len(whereParts) == 0 {
		return "", parameters, nil
	}

	return strings.Join(whereParts, " AND "), parameters, nil
}

// buildFilterClause creates a SQL filter clause based on operator
func (qb *SimpleQueryBuilder) buildFilterClause(fieldName string, operator FilterOperator, paramName string) string {
	switch operator {
	case Equal:
		return fmt.Sprintf("%s = $%s", fieldName, paramName)
	case NotEqual:
		return fmt.Sprintf("%s != $%s", fieldName, paramName)
	case GreaterThan:
		return fmt.Sprintf("%s > $%s", fieldName, paramName)
	case GreaterThanOrEqual:
		return fmt.Sprintf("%s >= $%s", fieldName, paramName)
	case LessThan:
		return fmt.Sprintf("%s < $%s", fieldName, paramName)
	case LessThanOrEqual:
		return fmt.Sprintf("%s <= $%s", fieldName, paramName)
	case Like:
		return fmt.Sprintf("%s LIKE $%s", fieldName, paramName)
	case In:
		return fmt.Sprintf("%s IN ($%s)", fieldName, paramName)
	case NotIn:
		return fmt.Sprintf("%s NOT IN ($%s)", fieldName, paramName)
	case IsNull:
		return fmt.Sprintf("%s IS NULL", fieldName)
	case IsNotNull:
		return fmt.Sprintf("%s IS NOT NULL", fieldName)
	default:
		return fmt.Sprintf("%s = $%s", fieldName, paramName)
	}
}

// buildOrderByClause constructs the ORDER BY part
func (qb *SimpleQueryBuilder) buildOrderByClause(requestSort []SortField, sortingConfig SortingConfig) string {
	var sortParts []string
	
	// Use request sort if provided
	if len(requestSort) > 0 {
		for _, sort := range requestSort {
			direction := "ASC"
			if sort.Direction == Descending {
				direction = "DESC"
			}
			sortParts = append(sortParts, fmt.Sprintf("%s %s", sort.FieldName, direction))
		}
	} else if len(sortingConfig.DefaultSort) > 0 {
		// Use default sort from config
		for _, sort := range sortingConfig.DefaultSort {
			direction := "ASC"
			if sort.Direction == Descending {
				direction = "DESC"
			}
			sortParts = append(sortParts, fmt.Sprintf("%s %s", sort.FieldName, direction))
		}
	}
	
	return strings.Join(sortParts, ", ")
}

// buildLimitClause constructs the LIMIT and OFFSET part
func (qb *SimpleQueryBuilder) buildLimitClause(page, pageSize int) string {
	if pageSize <= 0 {
		pageSize = 20 // default page size
	}
	if page <= 0 {
		page = 1 // default to first page
	}
	
	offset := (page - 1) * pageSize
	return fmt.Sprintf("LIMIT %d OFFSET %d", pageSize, offset)
}

// getJoinTypeString converts JoinType enum to SQL string
func (qb *SimpleQueryBuilder) getJoinTypeString(joinType JoinType) string {
	switch joinType {
	case InnerJoin:
		return "JOIN"
	case LeftJoin:
		return "LEFT JOIN"
	case RightJoin:
		return "RIGHT JOIN"
	case FullJoin:
		return "FULL JOIN"
	default:
		return "JOIN"
	}
}
