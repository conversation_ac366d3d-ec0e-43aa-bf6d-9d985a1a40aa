package tags

type TagKeyType int

const (
	HTTPErrorResponseData TagKeyType = iota
	HTTPSuccessResponseData
	HTTPRequestPayload
	FinalResponseData
	ApiStackStatusCode
	ServerAPIKey
	XClientID // XCLIENTID is used to get the client ID from the request header
	OrganizationID
	OrgEnabled
	SourceEntityID
	CustomerID
	UserObject
	WorkflowStep
	IsAPICalledBefore
	URLBasedClient // URLBasedClient is used to get the client based on the URL
	RequestURI
	RequestMethod

	// Graphql Tags
	TaskTriggersMasterTaskIDs
	TestTag
	// Used to send API processing info to next middleware, If API request is processed
	// the next middleware will not process the request again
	APIRequestProcessed
	ExtraResponseData
)
