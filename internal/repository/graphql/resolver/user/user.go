package user

import (
	"database/sql"
	"errors"
	"finbox/go-api/functions/logger"
	"finbox/go-api/infra/db"
	gqlresolver "finbox/go-api/internal/repository/graphql/resolver"
	"finbox/go-api/internal/repository/graphql/resolver/loan"
	"finbox/go-api/internal/repository/psql"
	usersql "finbox/go-api/internal/repository/psql/user"

	"github.com/graphql-go/graphql"
)

// UserResolver fetches user information from the database and returns it as a User model object.
func UserResolver(p graphql.ResolveParams) (interface{}, error) {
	var (
		res      User
		req      GetUserParams
		dbParams usersql.DBGetUserInfoParam
		ctx      = p.Context
	)

	decodeConfig := gqlresolver.CommonDecorderConf(&req)
	parsedParams, err := gqlresolver.ParseGQLRequest(p, &decodeConfig)
	if err != nil {
		return res, err
	}

	dbParams, ok := parsedParams.(usersql.DBGetUserInfoParam)
	if !ok {
		return res, gqlresolver.ErrInvalidGQLRequest
	}

	userRepo := usersql.NewUserDBRepository(db.GetDB())

	userDBObj, err := userRepo.DBGetUserByParamsV2(ctx, dbParams)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return res, err
	}

	res = NewUser(userDBObj)

	return res, nil
}

func UserBankDetailsResolver(p graphql.ResolveParams) (interface{}, error) {
	var (
		res      UserBankDetails
		req      GetUserBankDetailsParam
		dbParams usersql.DBGetUserBankDetailsParam
		ctx      = p.Context
	)

	switch p.Source.(type) {
	case loan.LoanApplication:
		// Extract UserBankDetailsID from LoanApplication
		req.UserBankDetailsID = p.Source.(loan.LoanApplication).UserBankDetailsID
		req.UserID = p.Source.(loan.LoanApplication).UserID
		decodeConfig := gqlresolver.CommonDecorderConf(&req)
		parsedParams, err := gqlresolver.ParseGQLRequestFromArgs(p, &decodeConfig)
		if err != nil {
			logger.WithContext(ctx).Errorf("[UserBankDetailsResolver] unable to parse graphql params. req: %+v, err: %+v", req, err)
			return res, err
		}
		var ok bool
		dbParams, ok = parsedParams.(usersql.DBGetUserBankDetailsParam)
		if !ok {
			return res, gqlresolver.ErrInvalidGQLRequest
		}
		dbParams.UserBankDetailsID = p.Source.(loan.LoanApplication).UserBankDetailsID
		dbParams.UserID = p.Source.(loan.LoanApplication).UserID
	default:
		// Handle decoding for a standalone query
		decodeConfig := gqlresolver.CommonDecorderConf(&req)
		parsedParams, err := gqlresolver.ParseGQLRequest(p, &decodeConfig)
		if err != nil {
			logger.WithContext(ctx).Errorf("[UserBankDetailsResolver] unable to parse graphql params. req: %+v, err: %+v", req, err)
			return res, err
		}
		var ok bool
		dbParams, ok = parsedParams.(usersql.DBGetUserBankDetailsParam)
		if !ok {
			return res, gqlresolver.ErrInvalidGQLRequest
		}
	}

	// Fetch UserBankDetails from the database
	userBankDetailsDBObj, err := usersql.DBGetUserBankDetailsByParam(ctx, dbParams)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		logger.WithContext(ctx).Errorf("[UserBankDetailsResolver] unable to fetch userBankDetailsDBObj. req: %+v, err: %+v", req, err)
		return res, err
	}

	// Convert the DB object to the API response model
	res = NewUserBankDetails(userBankDetailsDBObj)
	return res, nil
}

// UserBusinessResolver fetches user business information from the database and returns it as a UserBusiness model object.
func UserBusinessResolver(p graphql.ResolveParams) (interface{}, error) {
	var (
		res      UserBusiness
		req      GetUserBusinessParam
		dbParams usersql.DBGetUserBusinessParam
		ctx      = p.Context
	)

	decodeConfig := gqlresolver.CommonDecorderConf(&req)
	parsedParams, err := gqlresolver.ParseGQLRequest(p, &decodeConfig)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return res, err
	}

	dbParams, ok := parsedParams.(usersql.DBGetUserBusinessParam)
	if !ok {
		return res, gqlresolver.ErrInvalidGQLRequest
	}

	userBusinessDBObj, err := usersql.DBGetUserBusinessByParams(ctx, dbParams)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return res, err
	}

	res = NewUserBusiness(userBusinessDBObj)
	return res, nil
}

// UserBusinessGSTResolver fetches user business GST information from the database and returns it as a UserBusinessGST model object.
func UserBusinessGSTResolver(p graphql.ResolveParams) (interface{}, error) {
	var (
		res      UserBusinessGST
		req      GetUserBusinessGSTParam
		dbParams usersql.DBGetUserBusinessGSTParam
		ctx      = p.Context
	)

	decodeConfig := gqlresolver.CommonDecorderConf(&req)
	parsedParams, err := gqlresolver.ParseGQLRequest(p, &decodeConfig)
	if err != nil {
		return res, err
	}

	dbParams, ok := parsedParams.(usersql.DBGetUserBusinessGSTParam)
	if !ok {
		return res, gqlresolver.ErrInvalidGQLRequest
	}

	userBusinessGSTDBObj, err := usersql.DBGetUserBusinessGSTByParam(ctx, dbParams)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return res, err
	}

	res = NewUserBusinessGST(userBusinessGSTDBObj)
	return res, nil
}

// UserBusinessUANResolver fetches user business UAN information from the database and returns it as a UserBusinessUAN model object.
func UserBusinessUANResolver(p graphql.ResolveParams) (interface{}, error) {
	var (
		res      UserBusinessUAN
		req      GetUserBusinessUANParam
		dbParams usersql.DBGetUserBusinessUANParam
		ctx      = p.Context
	)

	decodeConfig := gqlresolver.CommonDecorderConf(&req)
	parsedParams, err := gqlresolver.ParseGQLRequest(p, &decodeConfig)
	if err != nil {
		return res, err
	}

	dbParams, ok := parsedParams.(usersql.DBGetUserBusinessUANParam)
	if !ok {
		return res, gqlresolver.ErrInvalidGQLRequest
	}

	userDBRepo := usersql.NewUserDBRepository(psql.Database)

	userBusinessUANDBObj, err := userDBRepo.DBGetUserBusinessUANByParam(ctx, dbParams)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return res, err
	}

	res = NewUserBusinessUAN(userBusinessUANDBObj)
	return res, nil
}

// UserLoanDetailResolver fetches user loan details from the database and returns it as a UserLoanDetails model object.
func UserLoanDetailResolver(p graphql.ResolveParams) (interface{}, error) {
	var (
		res      UserLoanDetails
		req      GetUserLoanDetailParam
		dbParams usersql.DBGetUserLoanDetailParam
		ctx      = p.Context
	)

	decodeConfig := gqlresolver.CommonDecorderConf(&req)
	parsedParams, err := gqlresolver.ParseGQLRequest(p, &decodeConfig)
	if err != nil {
		return res, err
	}

	dbParams, ok := parsedParams.(usersql.DBGetUserLoanDetailParam)
	if !ok {
		return res, gqlresolver.ErrInvalidGQLRequest
	}

	loanDetailsDBObj, err := usersql.DBGetUserLoanDetailsByParams(ctx, &dbParams)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return res, err
	}

	res = NewUserLoanDetails(loanDetailsDBObj)
	return res, nil
}

func UserLoanDetailListResolver(p graphql.ResolveParams) (interface{}, error) {
	var (
		res      []UserLoanDetails
		req      GetUserLoanDetailParam
		dbParams usersql.DBGetUserLoanDetailParam
		ctx      = p.Context
	)

	decodeConfig := gqlresolver.CommonDecorderConf(&req)
	parsedParams, err := gqlresolver.ParseGQLRequest(p, &decodeConfig)
	if err != nil {
		return res, err
	}

	dbParams, ok := parsedParams.(usersql.DBGetUserLoanDetailParam)
	if !ok {
		return res, gqlresolver.ErrInvalidGQLRequest
	}

	loanDetailsDBObj, err := usersql.DBGetUserLoanDetailsListByParams(ctx, &dbParams)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return res, err
	}

	res = NewUserLoanDetailsList(loanDetailsDBObj)
	return res, nil
}

// UserWorkflowsResolver fetches user workflows from the database and returns it as a UserWorkflows model object.
func UserWorkflowsResolver(p graphql.ResolveParams) (interface{}, error) {
	var (
		res      UserWorkflows
		req      GetUserWorkflowsParam
		dbParams usersql.DBGetUserWorkflowsParam
		ctx      = p.Context
	)

	// Decode the GraphQL request into the request object
	decodeConfig := gqlresolver.CommonDecorderConf(&req)
	parsedParams, err := gqlresolver.ParseGQLRequest(p, &decodeConfig)
	if err != nil {
		return res, err
	}

	// Type assertion from parsedParams to DBGetUserWorkflowsParam
	dbParams, ok := parsedParams.(usersql.DBGetUserWorkflowsParam)
	if !ok {
		return res, gqlresolver.ErrInvalidGQLRequest
	}

	// Fetch data from the database
	userWorkflowsDBObj, err := usersql.DBGetUserWorkflowsByParam(ctx, dbParams)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return res, err
	}

	// Convert the database object into the API response model
	res = NewUserWorkflows(userWorkflowsDBObj)
	return res, nil
}

// MultiUserLoanRelationsResolver fetches multi-user loan relations from the database and returns it as a MultiUserLoanRelations model object.
func MultiUserLoanRelationsResolver(p graphql.ResolveParams) (interface{}, error) {
	var (
		res      []MultiUserLoanRelations
		req      GetMultiUserLoanRelationsParam
		dbParams usersql.DBGetMultiUserLoanRelationsParam
		ctx      = p.Context
	)

	// Todo: handle multi-user loan relation generically like other
	switch p.Source.(type) {
	case loan.LoanApplication:
		req.ParentUserID = p.Source.(loan.LoanApplication).UserID
		decodeConfig := gqlresolver.CommonDecorderConf(&req)
		parsedParams, err := gqlresolver.ParseGQLRequestFromArgs(p, &decodeConfig)
		if err != nil {
			return res, err
		}
		var ok bool
		dbParams, ok = parsedParams.(usersql.DBGetMultiUserLoanRelationsParam)
		if !ok {
			return res, gqlresolver.ErrInvalidGQLRequest
		}
		dbParams.ParentUserID = p.Source.(loan.LoanApplication).UserID
	default:
		// Decode the GraphQL request into the request object
		decodeConfig := gqlresolver.CommonDecorderConf(&req)
		parsedParams, err := gqlresolver.ParseGQLRequest(p, &decodeConfig)
		if err != nil && !errors.Is(err, sql.ErrNoRows) {
			return res, err
		}
		var ok bool
		dbParams, ok = parsedParams.(usersql.DBGetMultiUserLoanRelationsParam)
		if !ok {
			return res, gqlresolver.ErrInvalidGQLRequest
		}
	}

	// Fetch data from the database
	multiUserLoanRelationsDBObj, err := usersql.DBGetMultiUserLoanRelationsListByParam(ctx, dbParams)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return res, err
	}

	// Convert the database object into the API response model
	res = NewMultiUserLoanRelationsList(multiUserLoanRelationsDBObj)
	return res, nil
}
