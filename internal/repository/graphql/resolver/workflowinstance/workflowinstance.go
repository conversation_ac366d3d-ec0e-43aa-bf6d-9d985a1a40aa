package workflowinstance

import (
	"database/sql"
	"errors"
	"finbox/go-api/infra/db"
	gqlresolver "finbox/go-api/internal/repository/graphql/resolver"
	workflowinstancesql "finbox/go-api/internal/repository/psql/workflowinstance"

	"github.com/graphql-go/graphql"
)

func WorkflowInstanceResolver(p graphql.ResolveParams) (interface{}, error) {
	var (
		res      WorkflowInstance
		req      GetWorkflowInstanceParams
		dbParams workflowinstancesql.DBGetWorkflowInstancesParam
		ctx      = p.Context
	)

	decodeConfig := gqlresolver.CommonDecorderConf(&req)
	parsedParams, err := gqlresolver.ParseGQLRequest(p, &decodeConfig)
	if err != nil {
		return res, err
	}

	dbParams, ok := parsedParams.(workflowinstancesql.DBGetWorkflowInstancesParam)
	if !ok {
		return res, gqlresolver.ErrInvalidGQLRequest
	}

	workflowRepo := workflowinstancesql.NewWorkflowInstancesRepository(db.GetDB())

	workflowInstance, err := workflowRepo.DBGetWorkflowInstancesByParams(ctx, &dbParams, nil)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return res, err
	}

	res = NewWorkflowInstance(workflowInstance)
	return res, nil
}

// ListWorkflowInstanceHistoryResolver fetches workflow instances information from the database.
func ListWorkflowInstanceHistoryResolver(p graphql.ResolveParams) (interface{}, error) {
	var (
		res      []WorkflowInstanceHistory
		req      GetListWorkflowInstanceHistoryParams
		dbParams workflowinstancesql.DBListWorkflowInstanceHistoryParam
		ctx      = p.Context
	)

	decodeConfig := gqlresolver.CommonDecorderConf(&req)
	parsedParams, err := gqlresolver.ParseGQLRequest(p, &decodeConfig)
	if err != nil {
		return res, err
	}

	dbParams, ok := parsedParams.(workflowinstancesql.DBListWorkflowInstanceHistoryParam)
	if !ok {
		return res, gqlresolver.ErrInvalidGQLRequest
	}

	workflowRepo := workflowinstancesql.NewWorkflowInstancesRepository(db.GetDB())

	workflowInstanceList, err := workflowRepo.DBListWorkflowInstanceHistoryByParams(ctx, &dbParams, nil)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return res, err
	}

	res = NewWorkflowInstanceHistoryList(workflowInstanceList)
	return res, nil
}
