// Package documentsql ...
package documentsql

import (
	"context"
	"errors"
	"finbox/go-api/constants"
	dashboard "finbox/go-api/models/dashboard"
	"finbox/go-api/models/userbusinessuan"
	"fmt"
	"strings"

	"finbox/go-api/functions/logger"
	"finbox/go-api/internal/repository/psql"

	"github.com/jmoiron/sqlx"
)

func DBGetDashboardCommentsByParams(ctx context.Context, param *DBGetDashboardCommentsParam, fields *DBGetDashboardCommentsFields) (resp *DBGetDashboardCommentsResponse, err error) {
	resp = &DBGetDashboardCommentsResponse{}
	if param == nil || *param == (DBGetDashboardCommentsParam{}) {
		err = errors.New("invalid or empty parameters")
		logger.WithContext(ctx).Errorf("[DBGetDashboardCommentsByParams] error validating params getting err : %v, req : %v", err, *param)
		return nil, err
	}
	if fields == nil || *fields == (DBGetDashboardCommentsFields{}) {
		fields = NewDBGetDashboardCommentsFields(true)
	}
	query := "SELECT "

	if fields.DocId {
		query += "COALESCE(doc_id::TEXT, '') AS doc_id, "
	}
	if fields.Comment {
		query += "COALESCE(comment::TEXT, '') AS comment, "
	}
	if fields.CreatedAt {
		query += "COALESCE(to_char(created_at, 'YYYY-MM-DD HH24:MI:SS'), '') AS created_at, "
	}
	if fields.EntityType {
		query += "COALESCE(entity_type::TEXT, '') AS entity_type, "
	}
	if fields.Status {
		query += "COALESCE(status, false) AS status, "
	}
	if fields.CommentId {
		query += "COALESCE(comment_id::TEXT, '') AS comment_id, "
	}
	query = query[0 : len(query)-2]
	query += " FROM dashboard_comments WHERE "
	var conditions []string

	var values []interface{}

	if param.CommentId != nil {
		conditions = append(conditions, "comment_id = ?")
		values = append(values, param.CommentId)
	}
	query += strings.Join(conditions, " AND ")
	query = psql.Database.Rebind(query)
	err = psql.Database.GetContext(ctx, resp, query, values...)
	if err != nil {
		logger.WithContext(ctx).Errorf("[DBGetDashboardCommentsByParams] error executing query getting err : %v, req : %v", err, *param)
		return nil, err
	}
	return resp, nil
}

func DBUpdateDashboardComments(ctx context.Context, tx *sqlx.Tx, param *DBUpdateDashboardCommentsParam, comment_id string) (err error) {
	if param == nil || *param == (DBUpdateDashboardCommentsParam{}) {
		err = errors.New("invalid or empty parameters")
		logger.WithContext(ctx).Errorf("[DBUpdateDashboardComments] error validating params getting err : %v, req : %v", err, *param)
		return err
	}
	query := "UPDATE dashboard_comments SET "

	var conditions []string

	var values []interface{}

	if param.Comment != nil {
		conditions = append(conditions, "comment = ?")
		values = append(values, param.Comment)
	}
	if param.CreatedAt != nil {
		conditions = append(conditions, "created_at = ?")
		values = append(values, param.CreatedAt)
	}
	if param.EntityType != nil {
		conditions = append(conditions, "entity_type = ?")
		values = append(values, param.EntityType)
	}
	if param.Status != nil {
		conditions = append(conditions, "status = ?")
		values = append(values, param.Status)
	}
	query += strings.Join(conditions, " , ")
	query += " WHERE comment_id = ?"
	values = append(values, comment_id)
	query = psql.Database.Rebind(query)
	switch {
	case tx != nil:
		_, err = tx.Exec(query, values...)
	default:
		_, err = psql.Database.Exec(query, values...)
	}
	if err != nil {
		logger.WithContext(ctx).Errorf("[DBUpdateDashboardComments] error executing query getting err : %v, req : %v", err, *param)
		return err
	}
	return nil
}

func DBListDashboardCommentsByParams(ctx context.Context, param *DBGetDashboardCommentsParam, fields *DBGetDashboardCommentsFields, pagination *psql.Pagination) (resp []DBGetDashboardCommentsResponse, err error) {
	if param == nil || *param == (DBGetDashboardCommentsParam{}) {
		err = errors.New("invalid or empty parameters")
		logger.WithContext(ctx).Errorf("[DBListDashboardCommentsByParams] error validating params getting err : %v, req : %v", err, *param)
		return nil, err
	}
	if fields == nil || *fields == (DBGetDashboardCommentsFields{}) {
		fields = NewDBGetDashboardCommentsFields(true)
	}
	query := "SELECT "

	if fields.DocId {
		query += "COALESCE(doc_id::TEXT, '') AS doc_id, "
	}
	if fields.Comment {
		query += "COALESCE(comment::TEXT, '') AS comment, "
	}
	if fields.CreatedAt {
		query += "COALESCE(to_char(created_at, 'YYYY-MM-DD HH24:MI:SS'), '') AS created_at, "
	}
	if fields.EntityType {
		query += "COALESCE(entity_type::TEXT, '') AS entity_type, "
	}
	if fields.Status {
		query += "COALESCE(status, false) AS status, "
	}
	if fields.CommentId {
		query += "COALESCE(comment_id::TEXT, '') AS comment_id, "
	}
	query = query[0 : len(query)-2]
	query += " FROM dashboard_comments WHERE "
	var conditions []string

	var values []interface{}

	if param.DocId != nil {
		conditions = append(conditions, "doc_id = ?")
		values = append(values, param.DocId)
	}
	if param.CommentId != nil {
		conditions = append(conditions, "comment_id = ?")
		values = append(values, param.CommentId)
	}

	query += strings.Join(conditions, " AND ")
	if pagination != nil {
		query += " LIMIT ? OFFSET ? "
		values = append(values, pagination.Limit)
		values = append(values, pagination.Offset)
	}
	query = psql.Database.Rebind(query)
	err = psql.Database.SelectContext(ctx, &resp, query, values...)
	if err != nil {
		logger.WithContext(ctx).Errorf("[DBListDashboardCommentsByParams] error executing query getting err : %v, req : %v", err, *param)
		return nil, err
	}
	return resp, nil
}

// DBListDashboardDocsByParams retrieves the dashboard documents from the database
func DBListDashboardDocsByParams(ctx context.Context, param *DBGetDashboardDocsParams) ([]DBGetDashboardDocsResponse, error) {

	var resp []DBGetDashboardDocsResponse

	if param == nil {
		logger.WithContext(ctx).Errorf("[DBListDashboardDocsByParams] empty params. param: %+v", param)
		return resp, errors.New("[DBListDashboardDocsByParams] empty params")
	}

	query := `
	SELECT 
		coalesce(dd.doc_id::TEXT, '') as doc_id,
		coalesce(dd.loan_application_id::TEXT, '') as loan_application_id,
		coalesce(dd.media_id::TEXT, '') as media_id,
		coalesce(to_char(dd.created_at, 'YYYY-MM-DD HH24:MI:SS'), '') as created_at,
		coalesce(dd.created_by, '') as created_by,
		coalesce(dd.entity_type, '') as entity_type,
		coalesce(dd.status, false) as status,
		coalesce(dd.review_status, 0) as review_status,
		coalesce(dd.document_id::TEXT, '') as document_id
	FROM 
		dashboard_docs dd
	WHERE 
	`

	var conditions []string
	var values []interface{}

	// Add conditions based on the provided parameters
	if param.DocID != "" {
		conditions = append(conditions, " doc_id = ?")
		values = append(values, param.DocID)
	}

	if param.DocumentID != "" {
		conditions = append(conditions, " document_id = ?")
		values = append(values, param.DocumentID)
	}

	if param.LoanApplicationID != "" {
		conditions = append(conditions, " loan_application_id = ?")
		values = append(values, param.LoanApplicationID)
	}
	if param.MediaID != "" {
		conditions = append(conditions, " media_id = ?")
		values = append(values, param.MediaID)
	}

	// Construct the final WHERE clause
	if len(conditions) > 0 {
		query += strings.Join(conditions, " AND ")
	}

	query = psql.Database.Rebind(query)

	err := psql.Database.Select(&resp, query, values...)
	if err != nil {
		logger.WithContext(ctx).Errorf("[DBGetDashboardDocsByParams] failed to get dashboard docs by params. err: %v, param: %+v", err, param)
		return nil, err
	}

	return resp, nil
}

// DBGetDocumentByParams fetches a document from the database based on the document ID
func DBGetDocumentByParams(ctx context.Context, param *DBGetDocumentParam) (DBGetDocumentResponse, error) {

	var resp DBGetDocumentResponse

	if param == nil {
		logger.WithContext(ctx).Errorf("[DBGetDocumentByParams] empty params. param: %+v", param)
		return resp, errors.New("empty params")
	}

	query := `
	SELECT 
		coalesce(d.document_id::TEXT, '') as document_id,
		coalesce(d.document_name, '') as document_name,
		coalesce(d.document_category, '') as document_category,
		coalesce(d.status, 0) as status,
		coalesce(to_char(d.created_at, 'YYYY-MM-DD HH24:MI:SS'), '') as created_at,
		coalesce(d.created_by, '') as created_by,
		coalesce(to_char(d.updated_at, 'YYYY-MM-DD HH24:MI:SS'), '') as updated_at,
		coalesce(d.updated_by, '') as updated_by,
		coalesce(d.document_title, '') as document_title,
		coalesce(d.both_sides, false) as both_sides,
		coalesce(d.allow_upload, false) as allow_upload,
		coalesce(d.icon_sub_url, '') as icon_sub_url
	FROM 
		documents d
	WHERE 
	`

	var conditions []string
	var values []interface{}

	// Add conditions based on the provided parameters
	if param.DocumentID != "" {
		conditions = append(conditions, " document_id = ?")
		values = append(values, param.DocumentID)
	}

	// Construct the final WHERE clause
	if len(conditions) > 0 {
		query += strings.Join(conditions, " AND ")
	}

	query = psql.Database.Rebind(query)

	err := psql.Database.Get(&resp, query, values...)
	if err != nil {
		logger.WithContext(ctx).Errorf("[DBGetDocumentByParams] failed to get document by params. err: %v, param: %+v", err, param)
		return resp, err
	}

	return resp, nil
}

// DBGetPanDetailsByParams fetches PAN details from the database based on the PanDetailsID or UserID
func DBGetPanDetailsByParams(ctx context.Context, param *DBGetPanDetailsParam) (DBGetPanDetailsResponse, error) {
	var resp DBGetPanDetailsResponse

	if param == nil {
		logger.WithContext(ctx).Errorf("[DBGetPanDetailsByParams] empty params. param: %+v", param)
		return resp, errors.New("empty params")
	}

	query := `
	SELECT 
		coalesce(pan_details_id::TEXT, '') as pan_details_id,
		coalesce(user_id::TEXT, '') as user_id,
		coalesce(external_service_id::TEXT, '') as external_service_id,
		coalesce(name, '') as name,
		coalesce(pan_number, '') as pan_number,
		coalesce(status, 0) as status,
		coalesce(gender, 0) as gender,
		coalesce(first_name, '') as first_name,
		coalesce(middle_name, '') as middle_name,
		coalesce(last_name, '') as last_name,
		coalesce(to_char(dob, 'YYYY-MM-DD'), '') as dob,
		coalesce(address, '') as address,
		coalesce(aadhaar_linked::TEXT, '') as aadhaar_linked,
		coalesce(pincode, '') as pincode,
		coalesce(dynamic_pan_info::TEXT, '') as dynamic_pan_info
	FROM 
		pan_details
	WHERE 
	`

	var conditions []string
	var values []interface{}

	// Add conditions based on the provided parameters
	if param.PanDetailsID != "" {
		conditions = append(conditions, "pan_details_id = ?")
		values = append(values, param.PanDetailsID)
	}
	if param.UserID != "" {
		conditions = append(conditions, "user_id = ?")
		values = append(values, param.UserID)
	}

	// Construct the final WHERE clause
	if len(conditions) > 0 {
		query += strings.Join(conditions, " AND ")
	}

	// Rebind the query for the specific database driver
	query = psql.Database.Rebind(query)

	// Execute the query and scan the result into the response struct
	err := psql.Database.Get(&resp, query, values...)
	if err != nil {
		logger.WithContext(ctx).Errorf("[DBGetPanDetailsByParams] failed to get PAN details by params. err: %v, param: %+v", err, param)
		return resp, err
	}

	return resp, nil
}

func DBGetAdditionalDashboardDocuments(ctx context.Context, param *DBGetDashboardDocsParam) ([]dashboard.DBDocsInfo, error) {
	docsInfo := []dashboard.DBDocsInfo{}

	if param == nil || param.LoanApplicationID == "" {
		logger.WithContext(ctx).Errorf("[DBGetAdditionalDashboardDocuments] empty params. param: %+v", param)
		return nil, errors.New("empty params")
	}

	query := `
    SELECT 
        coalesce(d.doc_id::TEXT, '') as docid, 
        m.media_id as mediaid, 
        coalesce(m.media_type) as doctype,
        coalesce(m.path, '') as mediapath, 
        coalesce(c.comment, '') as comment,
        coalesce(d.review_status, 0) as reviewstatus
    FROM 
        dashboard_docs d
    JOIN 
        media m ON d.media_id = m.media_id AND d.status = TRUE
    LEFT JOIN 
        dashboard_comments c ON c.doc_id = d.doc_id
    WHERE 
        d.loan_application_id = ? AND 
    	(d.document_id IS NULL or d.document_id = (?)) AND
        d.entity_type IN (?)
    ORDER BY 
        m.created_at DESC`

	// Use sqlx.In to handle the IN clause
	query, args, err := sqlx.In(query, param.LoanApplicationID, constants.AdditionalDocCategoryDocumentID, param.EntityTypes)
	if err != nil {
		logger.WithContext(ctx).Errorf("[DBGetAdditionalDashboardDocuments] Error in forming the query err: %v, query: %v", err, query)
		return nil, err
	}

	// Rebind the query for the correct dialect
	query = psql.Database.Rebind(query)

	// Execute the query
	err = psql.Database.Select(&docsInfo, query, args...)
	if err != nil {
		logger.WithContext(ctx).Errorf("[DBGetAdditionalDashboardDocuments] Error in getting docs info err: %v, param: %+v", err, param)
		return nil, err
	}

	return docsInfo, nil
}

func GetAdditionalKYCDocs(ctx context.Context, loanApplicationID, lenderID, userID string) ([]dashboard.AdditionalKYCDoc, error) {

	var additionalDocs []dashboard.AdditionalKYCDoc

	query := `select coalesce(d.doc_id::text, '') as doc_id, docs.document_category, docs.document_name, m.path, coalesce(d.review_status, 0) as review_status, coalesce(to_char(d.created_at, 'YYYY-MM-DD HH24:MI:SS'), '') as created_at
				from dashboard_docs d
				join media m on m.media_id=d.media_id and d.status=true
				join documents docs on docs.document_id=m.document_id
				where d.loan_application_id=$1 order by m.created_at desc`

	err := psql.Database.SelectContext(ctx, &additionalDocs, query, loanApplicationID)
	if err != nil {
		logger.WithContext(ctx).Errorf("[GetAdditionalKYCDocs] failed to get additional kyc docs. err: %v, loanApplication ID: %+v", err, loanApplicationID)
		return additionalDocs, err
	}

	if lenderID == constants.ABFLPLID {
		uanDetails, err := userbusinessuan.GetUANDataFromUserID(userID)
		if err != nil {
			logger.WithContext(ctx).Errorf("[GetAdditionalKYCDocs] failed to get uan details. err: %v, loanApplication ID: %+v", err, loanApplicationID)
		} else {
			additionalDocs = append(additionalDocs, dashboard.AdditionalKYCDoc{
				DocumentCategory: "",
				DocumentName:     constants.DocumentNameAdditionalDocUANFromAPI,
				DocumentID:       "",
				Password:         "",
				Path:             fmt.Sprintf("%s/uan/%s.pdf", userID, uanDetails.UAN),
				ReviewStatus:     0,
			})
		}
	}

	return additionalDocs, nil
}

func DBGetDashboardDocumentsWithMedia(ctx context.Context, param *DBGetDashboardDocumentsParam) ([]DBGetUserDocumentsResponse, error) {
	var resp []DBGetUserDocumentsResponse

	if param == nil {
		logger.WithContext(ctx).Errorf("[DBGetDashboardDocumentsWithMedia] empty params. param: %+v", param)
		return resp, errors.New("empty params")
	}

	if param.LoanApplicationID == "" {
		logger.WithContext(ctx).Errorf("[DBGetDashboardDocumentsWithMedia] no loan application ID provided. param: %+v", param)
		return resp, errors.New("loan application ID is required")
	}

	query := `
	SELECT 
		coalesce(d.doc_id::TEXT, '') as doc_id,
		coalesce(d.document_id::TEXT, '') as document_id, 
		coalesce(docs.document_category, '') as document_category, 
		coalesce(docs.document_name, '') as document_name, 
		coalesce(m.path, '') as path, 
		coalesce(d.review_status, 0) as review_status,
		coalesce(m.password, '') as password,
		coalesce(to_char(d.created_at, 'YYYY-MM-DD HH24:MI:SS'), '') as created_at,
		coalesce(m.user_id::TEXT, '') as user_id
	FROM 
		dashboard_docs d
	JOIN 
		media m ON m.media_id = d.media_id AND d.status = true
	LEFT JOIN 
		documents docs ON docs.document_id::text = d.document_id
	WHERE 
		d.loan_application_id = ?
	ORDER BY 
		m.created_at DESC
	`

	// Prepare args for sqlx.In
	args := []interface{}{param.LoanApplicationID}

	// Rebind the query to match the database driver
	query = psql.Database.Rebind(query)

	err := psql.Database.SelectContext(ctx, &resp, query, args...)
	if err != nil {
		logger.WithContext(ctx).Errorf("[DBGetDashboardDocumentsWithMedia] failed to get dashboard documents with media. err: %v, param: %+v", err, param)
		return resp, err
	}

	return resp, nil
}

func DBGetUserDocumentsWithMedia(ctx context.Context, param *DBGetUserDocumentsParam) ([]DBGetUserDocumentsResponse, error) {
	var resp []DBGetUserDocumentsResponse

	if param == nil {
		logger.WithContext(ctx).Errorf("[DBGetUserDocumentsWithMedia] empty params. param: %+v", param)
		return resp, errors.New("empty params")
	}

	query := `
    SELECT 
        coalesce(ud.document_id::TEXT, '') as document_id,
        coalesce(ud.document_type, '') as document_category,
        coalesce(ud.document_name, '') as document_name,
        coalesce(m.password, '') as password,
        coalesce(m.path, '') as path,
        coalesce(ud.status, 0) as review_status,
        coalesce(to_char(ud.created_at, 'YYYY-MM-DD HH24:MI:SS'), '') as created_at
    FROM 
        user_documents ud
    LEFT JOIN 
        media m ON ud.document_id::uuid = m.document_id
    WHERE 
    `

	var conditions []string
	var values []interface{}

	// Add conditions based on the provided parameters
	if param.UserID != "" {
		conditions = append(conditions, "ud.user_id = ?")
		values = append(values, param.UserID)
	}
	if param.DocumentID != "" {
		conditions = append(conditions, "ud.document_id = ?")
		values = append(values, param.DocumentID)
	}

	// Construct the final WHERE clause
	if len(conditions) > 0 {
		query += strings.Join(conditions, " AND ")
	} else {
		// If no conditions are provided, return an error
		logger.WithContext(ctx).Errorf("[DBGetUserDocumentsWithMedia] no search criteria provided. param: %+v", param)
		return resp, errors.New("no search criteria provided")
	}

	// Rebind the query for the specific database driver
	query = psql.Database.Rebind(query)

	// Execute the query and scan the results into the response structs
	err := psql.Database.Select(&resp, query, values...)
	if err != nil {
		logger.WithContext(ctx).Errorf("[DBGetUserDocumentsWithMedia] failed to get user documents with media. err: %v, param: %+v", err, param)
		return resp, err
	}

	return resp, nil
}
