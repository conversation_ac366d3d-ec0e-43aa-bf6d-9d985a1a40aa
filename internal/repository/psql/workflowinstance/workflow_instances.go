package workflowinstancessql

import (
	"context"
	"errors"
	"finbox/go-api/utils/general"
	"strings"

	"finbox/go-api/functions/logger"

	"github.com/jmoiron/sqlx"
)

type workflowInstancesRepository struct {
	db *sqlx.DB
}

func NewWorkflowInstancesRepository(db *sqlx.DB) *workflowInstancesRepository {
	return &workflowInstancesRepository{db: db}
}

func (repo *workflowInstancesRepository) DBInsertWorkflowInstances(ctx context.Context, tx *sqlx.Tx, param *DBInsertWorkflowInstancesParams) (string, error) {
	var (
		err                error
		workflowInstanceID = general.GetUUID()
	)
	if param == nil || *param == (DBInsertWorkflowInstancesParams{}) {
		err = errors.New("invalid or empty parameters")
		logger.WithContext(ctx).Errorf("[DBInsertWorkflowInstances] error validating params getting err : %v, req : %v", err, param)
		return workflowInstanceID, err
	}
	query := "INSERT INTO workflow_instances ("

	var (
		fields []string
		values []interface{}
	)

	placeholders := make([]string, 0)

	param.ID = workflowInstanceID

	if param.ID != "" {
		fields = append(fields, "id")
		values = append(values, param.ID)
		placeholders = append(placeholders, "?")
	}

	if param.WorkflowId != 0 {
		fields = append(fields, "workflow_id")
		values = append(values, param.WorkflowId)
		placeholders = append(placeholders, "?")
	}
	if param.IdentifierId != "" {
		fields = append(fields, "identifier_id")
		values = append(values, param.IdentifierId)
		placeholders = append(placeholders, "?")
	}
	if param.IdentifierType != "" {
		fields = append(fields, "identifier_type")
		values = append(values, param.IdentifierType)
		placeholders = append(placeholders, "?")
	}
	if param.CurrentState != "" {
		fields = append(fields, "current_state")
		values = append(values, param.CurrentState)
		placeholders = append(placeholders, "?")
	}
	if param.AssignedGroup != "" {
		fields = append(fields, "assigned_group")
		values = append(values, param.AssignedGroup)
		placeholders = append(placeholders, "?")
	}
	if param.AssignedTo != "" {
		fields = append(fields, "assigned_to")
		values = append(values, param.AssignedTo)
		placeholders = append(placeholders, "?")
	}

	query += strings.Join(fields, ", ")
	query += ") VALUES ("
	query += strings.Join(placeholders, ", ")
	query += ")"

	query = repo.db.Rebind(query)
	switch {
	case tx != nil:
		_, err = tx.Exec(query, values...)
	default:
		_, err = repo.db.Exec(query, values...)
	}
	if err != nil {
		logger.WithContext(ctx).Errorf("[DBInsertWorkflowInstances] error executing query getting err : %v, req : %v", err, param)
		return workflowInstanceID, err
	}
	return workflowInstanceID, nil
}

func (repo *workflowInstancesRepository) DBGetWorkflowInstancesByParams(ctx context.Context, param *DBGetWorkflowInstancesParam, fields *DBGetWorkflowInstancesFields) (resp *DBGetWorkflowInstancesResponse, err error) {
	resp = &DBGetWorkflowInstancesResponse{}
	if param == nil || *param == (DBGetWorkflowInstancesParam{}) {
		err = errors.New("invalid or empty parameters")
		logger.WithContext(ctx).Errorf("[DBGetWorkflowInstancesByParams] error validating params getting err : %v, req : %v", err, param)
		return nil, err
	}

	if fields == nil || *fields == (DBGetWorkflowInstancesFields{}) {
		fields = NewDBGetWorkflowInstancesFields(true)
	}

	query := "SELECT "

	if fields.Id {
		query += "COALESCE(id::TEXT, '') AS id, "
	}

	if fields.WorkflowId {
		query += "COALESCE(workflow_id, -1) AS workflow_id, "
	}

	if fields.IdentifierId {
		query += "COALESCE(identifier_id::TEXT, '') AS identifier_id, "
	}
	if fields.IdentifierType {
		query += "COALESCE(identifier_type::TEXT, '') AS identifier_type, "
	}
	if fields.CurrentState {
		query += "COALESCE(current_state::TEXT, '') AS current_state, "
	}
	if fields.AssignedGroup {
		query += "COALESCE(assigned_group::TEXT, '') AS assigned_group, "
	}
	if fields.AssignedTo {
		query += "COALESCE(assigned_to::TEXT, '') AS assigned_to, "
	}
	if fields.Metadata {
		query += "COALESCE(metadata::TEXT, '') AS metadata, "
	}
	if fields.CreatedAt {
		query += "COALESCE(to_char(created_at, 'YYYY-MM-DD HH24:MI:SS'), '') AS created_at, "
	}
	if fields.UpdatedAt {
		query += "COALESCE(to_char(updated_at, 'YYYY-MM-DD HH24:MI:SS'), '') AS updated_at, "
	}
	if fields.DeletedAt {
		query += "COALESCE(to_char(deleted_at, 'YYYY-MM-DD HH24:MI:SS'), '') AS deleted_at, "
	}

	query = query[0 : len(query)-2]
	query += " FROM workflow_instances WHERE "
	var conditions []string

	var values []interface{}

	if param.Id != "" {
		conditions = append(conditions, "id = ?")
		values = append(values, param.Id)
	}

	if param.IdentifierID != "" {
		conditions = append(conditions, "identifier_id = ?")
		values = append(values, param.IdentifierID)

		if param.IdentifierType != "" {
			conditions = append(conditions, "identifier_type = ?")
			values = append(values, param.IdentifierID)
		}
	}

	query += strings.Join(conditions, " AND ")
	query = repo.db.Rebind(query)
	err = repo.db.GetContext(ctx, resp, query, values...)
	if err != nil {
		logger.WithContext(ctx).Errorf("[DBGetWorkflowInstancesByParams] error executing query getting err : %v, req : %v", err, param)
		return nil, err
	}

	return resp, nil
}
func (repo *workflowInstancesRepository) DBUpdateWorkflowInstances(ctx context.Context, tx *sqlx.Tx, param *DBUpdateWorkflowInstancesParam) (err error) {
	if param == nil || *param == (DBUpdateWorkflowInstancesParam{}) {
		err = errors.New("invalid or empty parameters")
		logger.WithContext(ctx).Errorf("[DBUpdateWorkflowInstances] error validating params getting err : %v, req : %v", err, param)
		return err
	}

	query := "UPDATE workflow_instances SET "

	var (
		conditions []string
		values     []interface{}
	)

	if param.IdentifierType != "" {
		conditions = append(conditions, "identifier_type = ?")
		values = append(values, param.IdentifierType)
	}

	if param.CurrentState != "" {
		conditions = append(conditions, "current_state = ?")
		values = append(values, param.CurrentState)
	}

	if param.AssignedGroup != "" {
		conditions = append(conditions, "assigned_group = ?")
		values = append(values, param.AssignedGroup)
	}

	if param.Metadata != nil {
		conditions = append(conditions, "metadata = ?")
		values = append(values, param.Metadata)
	}

	if param.AssignedTo != "" {
		conditions = append(conditions, "assigned_to = ?")
		values = append(values, param.AssignedTo)
	}

	query += strings.Join(conditions, " , ")

	if param.WorkflowInstanceID == "" {
		err = errors.New("workflowInstanceID is required")
		logger.WithContext(ctx).Errorf("[DBUpdateWorkflowInstances] invalid update request err : %v, req : %v", err, param)
		return err
	}

	query += ", updated_at = NOW() WHERE id = ?"
	values = append(values, param.WorkflowInstanceID)

	query = repo.db.Rebind(query)

	switch {
	case tx != nil:
		_, err = tx.Exec(query, values...)
	default:
		_, err = repo.db.Exec(query, values...)
	}

	if err != nil {
		logger.WithContext(ctx).Errorf("[DBUpdateWorkflowInstances] error executing query getting err : %v, req : %v", err, param)
		return err
	}

	return nil
}
func (repo *workflowInstancesRepository) DBListWorkflowInstancesByParams(ctx context.Context, param *DBListWorkflowInstancesParam, fields *DBGetWorkflowInstancesFields) (resp []DBGetWorkflowInstancesResponse, err error) {
	if param == nil || (len(param.WorkflowInstanceIds) == 0 && len(param.IdentifierIds) == 0) {
		err = errors.New("invalid or empty parameters")
		logger.WithContext(ctx).Errorf("[DBListWorkflowInstancesByParams] error validating params getting err : %v, req : %v", err, param)
		return nil, err
	}

	if fields == nil || *fields == (DBGetWorkflowInstancesFields{}) {
		fields = NewDBGetWorkflowInstancesFields(true)
	}

	query := "SELECT "

	if fields.Id {
		query += "COALESCE(id::TEXT, '') AS id, "
	}

	if fields.WorkflowId {
		query += "COALESCE(workflow_id, 0) AS workflow_id, "
	}
	if fields.IdentifierId {
		query += "COALESCE(identifier_id::TEXT, '') AS identifier_id, "
	}
	if fields.IdentifierType {
		query += "COALESCE(identifier_type::TEXT, '') AS identifier_type, "
	}
	if fields.CurrentState {
		query += "COALESCE(current_state::TEXT, '') AS current_state, "
	}
	if fields.AssignedGroup {
		query += "COALESCE(assigned_group::TEXT, '') AS assigned_group, "
	}
	if fields.AssignedTo {
		query += "COALESCE(assigned_to::TEXT, '') AS assigned_to, "
	}
	if fields.CreatedAt {
		query += "COALESCE(to_char(created_at, 'YYYY-MM-DD HH24:MI:SS'), '') AS created_at, "
	}
	if fields.UpdatedAt {
		query += "COALESCE(to_char(updated_at, 'YYYY-MM-DD HH24:MI:SS'), '') AS updated_at, "
	}
	if fields.DeletedAt {
		query += "COALESCE(to_char(deleted_at, 'YYYY-MM-DD HH24:MI:SS'), '') AS deleted_at, "
	}

	query = query[0 : len(query)-2]
	query += " FROM workflow_instances WHERE "
	var conditions []string

	var values []interface{}

	if len(param.WorkflowInstanceIds) > 0 {
		tempQuery, tempArgs, err := sqlx.In(" id IN (?)", param.WorkflowInstanceIds)
		if err != nil {
			logger.WithContext(ctx).Errorf("[DBListWorkflowInstancesByParams] failed to make sqlx In query for workflowIds. err: %v", err)
			return nil, err
		}

		conditions = append(conditions, tempQuery)
		values = append(values, tempArgs...)
	}

	if len(param.IdentifierIds) > 0 {
		tempQuery, tempArgs, err := sqlx.In(" identifier_id IN (?)", param.IdentifierIds)
		if err != nil {
			logger.WithContext(ctx).Errorf("[DBListWorkflowInstancesByParams] failed to make sqlx In query for workflow indentifierIds. err: %v", err)
			return nil, err
		}

		conditions = append(conditions, tempQuery)
		values = append(values, tempArgs...)
	}

	query += strings.Join(conditions, " AND ")
	if param.Pagination != nil {
		query += " LIMIT ? OFFSET ? "
		values = append(values, param.Pagination.Limit)
		values = append(values, param.Pagination.Offset)
	}

	query = repo.db.Rebind(query)

	err = repo.db.SelectContext(ctx, &resp, query, values...)
	if err != nil {
		logger.WithContext(ctx).Errorf("[DBListWorkflowInstancesByParams] error executing query getting err : %v, req : %v", err, param)
		return nil, err
	}

	return resp, nil
}
