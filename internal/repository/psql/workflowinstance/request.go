package workflowinstancessql

import (
	"encoding/json"
	"finbox/go-api/internal/repository/psql"
	"time"
)

type DBUpdateWorkflowInstancesParam struct {
	WorkflowInstanceID string
	IdentifierType     string
	CurrentState       string
	AssignedGroup      string
	AssignedTo         string
	Metadata           *json.RawMessage
	UpdatedAt          time.Time
}
type DBGetWorkflowInstancesParam struct {
	Id             string
	IdentifierID   string
	IdentifierType string
}
type DBGetWorkflowInstancesFields struct {
	Id             bool
	WorkflowId     bool
	IdentifierId   bool
	IdentifierType bool
	CurrentState   bool
	AssignedGroup  bool
	AssignedTo     bool
	CreatedAt      bool
	UpdatedAt      bool
	DeletedAt      bool
	Metadata       bool
}

func NewDBGetWorkflowInstancesFields(_default bool) *DBGetWorkflowInstancesFields {
	return &DBGetWorkflowInstancesFields{
		Id:             _default,
		WorkflowId:     _default,
		IdentifierId:   _default,
		IdentifierType: _default,
		CurrentState:   _default,
		AssignedGroup:  _default,
		AssignedTo:     _default,
		CreatedAt:      _default,
		UpdatedAt:      _default,
		DeletedAt:      _default,
	}
}

type DBInsertWorkflowInstancesParams struct {
	ID             string
	WorkflowId     int
	IdentifierId   string
	IdentifierType string
	CurrentState   string
	AssignedGroup  string
	AssignedTo     string
}
type DBListWorkflowInstancesParam struct {
	WorkflowInstanceIds []string
	IdentifierIds       []string
	Pagination          *psql.Pagination
}

type DBUpdateWorkflowInstanceHistoryParam struct {
	PreviousState string
	CurrentState  string
	Action        string
	ActionBy      string
	AssignedGroup string
	AssignedTo    string
	CreatedAt     string
	UpdatedAt     string
	DeletedAt     string
}
type DBGetWorkflowInstanceHistoryParam struct {
	Id                 string
	WorkflowInstanceId string
}
type DBGetWorkflowInstanceHistoryFields struct {
	Id                 bool
	WorkflowInstanceId bool
	PreviousState      bool
	CurrentState       bool
	Action             bool
	ActionBy           bool
	AssignedGroup      bool
	AssignedTo         bool
	CreatedAt          bool
	UpdatedAt          bool
	DeletedAt          bool
}

func NewDBGetWorkflowInstanceHistoryFields(_default bool) *DBGetWorkflowInstanceHistoryFields {
	return &DBGetWorkflowInstanceHistoryFields{
		Id:                 _default,
		WorkflowInstanceId: _default,
		PreviousState:      _default,
		CurrentState:       _default,
		Action:             _default,
		ActionBy:           _default,
		AssignedGroup:      _default,
		AssignedTo:         _default,
		CreatedAt:          _default,
		UpdatedAt:          _default,
		DeletedAt:          _default,
	}
}

type DBInsertWorkflowInstanceHistoryParams struct {
	WorkflowInstanceID string
	PreviousState      string
	CurrentState       string
	Action             string
	ActionBy           string
	AssignedGroup      string
	AssignedTo         string
	Remarks            string
}
type DBListWorkflowInstanceHistoryParam struct {
	DBGetWorkflowInstanceHistoryParam
	Pagination         *psql.Pagination
	OrderByCreatedDate string
}

const (
	IdentifierType_Task = "task"
)
