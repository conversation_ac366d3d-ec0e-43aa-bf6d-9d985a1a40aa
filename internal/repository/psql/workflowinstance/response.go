package workflowinstancessql

type DBGetWorkflowInstancesResponse struct {
	ID             string `db:"id"`
	WorkflowID     int    `db:"workflow_id"`
	IdentifierID   string `db:"identifier_id"`
	IdentifierType string `db:"identifier_type"`
	CurrentState   string `db:"current_state"`
	AssignedGroup  string `db:"assigned_group"`
	AssignedTo     string `db:"assigned_to"`
	CreatedAt      string `db:"created_at"`
	UpdatedAt      string `db:"updated_at"`
	Metadata       string `db:"metadata"`
	DeletedAt      string `db:"deleted_at"`
}

type DBGetWorkflowInstanceHistoryResponse struct {
	ID                 string `db:"id"`
	WorkflowInstanceID string `db:"workflow_instance_id"`
	PreviousState      string `db:"previous_state"`
	CurrentState       string `db:"current_state"`
	Action             string `db:"action"`
	ActionBy           string `db:"action_by"`
	AssignedGroup      string `db:"assigned_group"`
	AssignedTo         string `db:"assigned_to"`
	Remarks            string `db:"remarks"`
	CreatedAt          string `db:"created_at"`
	UpdatedAt          string `db:"updated_at"`
	DeletedAt          string `db:"deleted_at"`
}
