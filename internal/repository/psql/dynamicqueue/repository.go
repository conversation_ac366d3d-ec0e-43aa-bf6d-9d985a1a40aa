package dynamicqueue

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"time"

	"finbox/go-api/functions/logger"
	"finbox/go-api/internal/service/dynamicmyqueue"

	"github.com/jmoiron/sqlx"
)

// ConfigRepository implements the dynamicmyqueue.ConfigRepository interface
type ConfigRepository struct {
	db *sqlx.DB
}

// NewConfigRepository creates a new instance of ConfigRepository
func NewConfigRepository(db *sqlx.DB) *ConfigRepository {
	return &ConfigRepository{
		db: db,
	}
}

// Create creates a new metadata configuration
func (r *ConfigRepository) Create(ctx context.Context, config *dynamicmyqueue.MetadataConfig) error {
	if config == nil {
		return fmt.Errorf("config cannot be nil")
	}

	// Marshal JSON fields
	tableJoinsJSON, err := json.Marshal(config.TableJoins)
	if err != nil {
		return fmt.Errorf("failed to marshal table joins: %w", err)
	}

	fieldMappingsJSON, err := json.Marshal(config.FieldMappings)
	if err != nil {
		return fmt.Errorf("failed to marshal field mappings: %w", err)
	}

	filterConditionsJSON, err := json.Marshal(config.FilterConditions)
	if err != nil {
		return fmt.Errorf("failed to marshal filter conditions: %w", err)
	}

	sortingConfigJSON, err := json.Marshal(config.SortingConfig)
	if err != nil {
		return fmt.Errorf("failed to marshal sorting config: %w", err)
	}

	paginationConfigJSON, err := json.Marshal(config.PaginationConfig)
	if err != nil {
		return fmt.Errorf("failed to marshal pagination config: %w", err)
	}

	query := `
		INSERT INTO dynamic_queue_metadata_config (
			client_id, config_name, version, is_active,
			table_joins, field_mappings, filter_conditions,
			sorting_config, pagination_config,
			created_by, updated_by
		) VALUES (
			$1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11
		)`

	_, err = r.db.ExecContext(ctx, query,
		config.ClientID, config.ConfigName, config.Version, config.IsActive,
		tableJoinsJSON, fieldMappingsJSON, filterConditionsJSON,
		sortingConfigJSON, paginationConfigJSON,
		config.CreatedBy, config.UpdatedBy,
	)

	if err != nil {
		logger.WithContext(ctx).Errorf("Failed to create config: %v", err)
		return fmt.Errorf("failed to create config: %w", err)
	}

	logger.WithContext(ctx).Infof("Created config %s for client %s", config.ConfigName, config.ClientID)
	return nil
}

// Update updates an existing metadata configuration
func (r *ConfigRepository) Update(ctx context.Context, clientID, configName string, config *dynamicmyqueue.MetadataConfig) error {
	if config == nil {
		return fmt.Errorf("config cannot be nil")
	}

	// Marshal JSON fields
	tableJoinsJSON, err := json.Marshal(config.TableJoins)
	if err != nil {
		return fmt.Errorf("failed to marshal table joins: %w", err)
	}

	fieldMappingsJSON, err := json.Marshal(config.FieldMappings)
	if err != nil {
		return fmt.Errorf("failed to marshal field mappings: %w", err)
	}

	filterConditionsJSON, err := json.Marshal(config.FilterConditions)
	if err != nil {
		return fmt.Errorf("failed to marshal filter conditions: %w", err)
	}

	sortingConfigJSON, err := json.Marshal(config.SortingConfig)
	if err != nil {
		return fmt.Errorf("failed to marshal sorting config: %w", err)
	}

	paginationConfigJSON, err := json.Marshal(config.PaginationConfig)
	if err != nil {
		return fmt.Errorf("failed to marshal pagination config: %w", err)
	}

	query := `
		UPDATE dynamic_queue_metadata_config SET
			version = $3, is_active = $4,
			table_joins = $5, field_mappings = $6, filter_conditions = $7,
			sorting_config = $8, pagination_config = $9, updated_by = $10, updated_at = NOW()
		WHERE client_id = $1 AND config_name = $2`

	result, err := r.db.ExecContext(ctx, query,
		clientID, configName, config.Version, config.IsActive,
		tableJoinsJSON, fieldMappingsJSON, filterConditionsJSON,
		sortingConfigJSON, paginationConfigJSON,
		config.UpdatedBy,
	)

	if err != nil {
		logger.WithContext(ctx).Errorf("Failed to update config: %v", err)
		return fmt.Errorf("failed to update config: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("config not found: client_id=%s, config_name=%s", clientID, configName)
	}

	logger.WithContext(ctx).Infof("Updated config %s for client %s", configName, clientID)
	return nil
}

// GetByClientAndName retrieves a configuration by client ID and config name
func (r *ConfigRepository) GetByClientAndName(ctx context.Context, clientID, configName string) (*dynamicmyqueue.MetadataConfig, error) {
	query := `
		SELECT client_id, config_name, version, is_active,
			   table_joins, field_mappings, filter_conditions,
			   sorting_config, pagination_config,
			   created_at, updated_at, created_by, updated_by
		FROM dynamic_queue_metadata_config
		WHERE client_id = $1 AND config_name = $2`

	var dbConfig DBMetadataConfig
	err := r.db.GetContext(ctx, &dbConfig, query, clientID, configName)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("config not found: client_id=%s, config_name=%s", clientID, configName)
		}
		logger.WithContext(ctx).Errorf("Failed to get config: %v", err)
		return nil, fmt.Errorf("failed to get config: %w", err)
	}

	config, err := r.dbConfigToMetadataConfig(&dbConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to convert db config: %w", err)
	}

	return config, nil
}

// ListByClient retrieves all configurations for a client
func (r *ConfigRepository) ListByClient(ctx context.Context, clientID string) ([]*dynamicmyqueue.MetadataConfig, error) {
	query := `
		SELECT client_id, config_name, version, is_active,
			   table_joins, field_mappings, filter_conditions,
			   sorting_config, pagination_config,
			   created_at, updated_at, created_by, updated_by
		FROM dynamic_queue_metadata_config
		WHERE client_id = $1
		ORDER BY created_at DESC`

	var dbConfigs []DBMetadataConfig
	err := r.db.SelectContext(ctx, &dbConfigs, query, clientID)
	if err != nil {
		logger.WithContext(ctx).Errorf("Failed to list configs: %v", err)
		return nil, fmt.Errorf("failed to list configs: %w", err)
	}

	configs := make([]*dynamicmyqueue.MetadataConfig, len(dbConfigs))
	for i, dbConfig := range dbConfigs {
		config, err := r.dbConfigToMetadataConfig(&dbConfig)
		if err != nil {
			logger.WithContext(ctx).Warnf("Failed to convert config %s: %v", dbConfig.ConfigName, err)
			continue
		}
		configs[i] = config
	}

	return configs, nil
}

// Delete deletes a configuration
func (r *ConfigRepository) Delete(ctx context.Context, clientID, configName string) error {
	query := `DELETE FROM dynamic_queue_metadata_config WHERE client_id = $1 AND config_name = $2`

	result, err := r.db.ExecContext(ctx, query, clientID, configName)
	if err != nil {
		logger.WithContext(ctx).Errorf("Failed to delete config: %v", err)
		return fmt.Errorf("failed to delete config: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("config not found: client_id=%s, config_name=%s", clientID, configName)
	}

	logger.WithContext(ctx).Infof("Deleted config %s for client %s", configName, clientID)
	return nil
}

// GetActiveConfig retrieves the active configuration for a client
func (r *ConfigRepository) GetActiveConfig(ctx context.Context, clientID string) (*dynamicmyqueue.MetadataConfig, error) {
	query := `
		SELECT client_id, config_name, version, is_active,
			   table_joins, field_mappings, filter_conditions,
			   sorting_config, pagination_config,
			   created_at, updated_at, created_by, updated_by
		FROM dynamic_queue_metadata_config
		WHERE client_id = $1 AND is_active = true
		LIMIT 1`

	var dbConfig DBMetadataConfig
	err := r.db.GetContext(ctx, &dbConfig, query, clientID)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("no active config found for client: %s", clientID)
		}
		logger.WithContext(ctx).Errorf("Failed to get active config: %v", err)
		return nil, fmt.Errorf("failed to get active config: %w", err)
	}

	config, err := r.dbConfigToMetadataConfig(&dbConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to convert db config: %w", err)
	}

	return config, nil
}

// dbConfigToMetadataConfig converts database model to domain model
func (r *ConfigRepository) dbConfigToMetadataConfig(dbConfig *DBMetadataConfig) (*dynamicmyqueue.MetadataConfig, error) {
	config := &dynamicmyqueue.MetadataConfig{
		ClientID:   dbConfig.ClientID,
		ConfigName: dbConfig.ConfigName,
		Version:    dbConfig.Version,
		IsActive:   dbConfig.IsActive,
		CreatedAt:  dbConfig.CreatedAt,
		UpdatedAt:  dbConfig.UpdatedAt,
		CreatedBy:  dbConfig.CreatedBy,
		UpdatedBy:  dbConfig.UpdatedBy,
	}

	// Unmarshal JSON fields
	if err := json.Unmarshal(dbConfig.TableJoins, &config.TableJoins); err != nil {
		return nil, fmt.Errorf("failed to unmarshal table joins: %w", err)
	}

	if err := json.Unmarshal(dbConfig.FieldMappings, &config.FieldMappings); err != nil {
		return nil, fmt.Errorf("failed to unmarshal field mappings: %w", err)
	}

	if err := json.Unmarshal(dbConfig.FilterConditions, &config.FilterConditions); err != nil {
		return nil, fmt.Errorf("failed to unmarshal filter conditions: %w", err)
	}

	if err := json.Unmarshal(dbConfig.SortingConfig, &config.SortingConfig); err != nil {
		return nil, fmt.Errorf("failed to unmarshal sorting config: %w", err)
	}

	if err := json.Unmarshal(dbConfig.PaginationConfig, &config.PaginationConfig); err != nil {
		return nil, fmt.Errorf("failed to unmarshal pagination config: %w", err)
	}

	return config, nil
}

// DBMetadataConfig represents the database model for metadata configuration
type DBMetadataConfig struct {
	ClientID         string          `db:"client_id"`
	ConfigName       string          `db:"config_name"`
	Version          string          `db:"version"`
	IsActive         bool            `db:"is_active"`
	TableJoins       json.RawMessage `db:"table_joins"`
	FieldMappings    json.RawMessage `db:"field_mappings"`
	FilterConditions json.RawMessage `db:"filter_conditions"`
	SortingConfig    json.RawMessage `db:"sorting_config"`
	PaginationConfig json.RawMessage `db:"pagination_config"`
	CreatedAt        time.Time       `db:"created_at"`
	UpdatedAt        time.Time       `db:"updated_at"`
	CreatedBy        string          `db:"created_by"`
	UpdatedBy        string          `db:"updated_by"`
}

// DBQueryLog represents the database model for query execution logs
type DBQueryLog struct {
	ID              string          `db:"id"`
	ClientID        string          `db:"client_id"`
	ConfigName      string          `db:"config_name"`
	QueryHash       string          `db:"query_hash"`
	QueryText       string          `db:"query_text"`
	Parameters      json.RawMessage `db:"parameters"`
	ExecutionTimeMs int             `db:"execution_time_ms"`
	RowCount        int             `db:"row_count"`
	Status          string          `db:"status"`
	ErrorMessage    *string         `db:"error_message"`
	UserID          *string         `db:"user_id"`
	RequestID       *string         `db:"request_id"`
	CreatedAt       time.Time       `db:"created_at"`
}

// DBCacheEntry represents the database model for cached query results
type DBCacheEntry struct {
	ID         string          `db:"id"`
	CacheKey   string          `db:"cache_key"`
	ClientID   string          `db:"client_id"`
	ConfigName string          `db:"config_name"`
	QueryHash  string          `db:"query_hash"`
	CachedData json.RawMessage `db:"cached_data"`
	ExpiresAt  time.Time       `db:"expires_at"`
	CreatedAt  time.Time       `db:"created_at"`
	UpdatedAt  time.Time       `db:"updated_at"`
}
