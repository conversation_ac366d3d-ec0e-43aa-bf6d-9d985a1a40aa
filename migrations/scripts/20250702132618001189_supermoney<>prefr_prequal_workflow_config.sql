-- Disable existing Prequalification workflow for Prefr<>SuperMoney
UPDATE workflow_config
SET is_active = FALSE
WHERE source_entity_id = 'f06cf417-f415-4d93-b733-a45e885a4d36'
AND module_name = 'PREQUALIFICATION';


-- Create new Prequalification workflow for Prefr<>SuperMoney
INSERT INTO workflow_config (
  config_id,
  source_entity_id,
  organization_id,
  program_name,
  lender_id,
  module_name,
  created_by,
  is_active,
  workflow_definition
 )
VALUES ( 
  uuid_generate_v4(),
  'f06cf417-f415-4d93-b733-a45e885a4d36',
  '8af96add-e7eb-4583-8ded-0cebe42121d8',
  'PERSONAL_LOAN',
  '9c8551e3-fd00-46f0-b61b-64d0011f3bbd',
  'PREQUALIFICATION',
  'ADMIN',
  TRUE,
'{
  "retries": [
    {
      "delay": "PT1M",
      "maxAttempts": 23,
      "name": "ServicesNotAvailableRetryStrategy",
      "multiplier": 1.5
    }
  ],
  "name": "Prefr<>SuperMoney prequalication and offer qualification workflow",
  "states": [
    {
      "transition": "IsDuplicateApplication",
      "type": "operation",
      "name": "LenderDedupe",
      "actions": [
        {
          "retryRef": "ServicesNotAvailableRetryStrategy",
          "functionRef": {
            "refName": "LisaDedupeCheckV2",
            "arguments": {
              "lenderID": "${ .lenderID }"
            }
          }
        }
      ],
      "onErrors": [
        {
          "transition": "HandleInternalFailure",
          "errorRef": "DefaultErrorRef"
        }
      ]
    },
    {
      "dataConditions": [
        {
          "transition": "DisqualifyUser",
          "condition": ".isLenderDedupe == true"
        }
      ],
      "defaultCondition": {
        "transition": "CreateApplicant"
      },
      "type": "switch",
      "name": "IsDuplicateApplication"
    },
    {
      "transition": "CompleteWorkflow",
      "type": "operation",
      "name": "DisqualifyUser",
      "actions": [
        {
          "retryRef": "ServicesNotAvailableRetryStrategy",
          "functionRef": {
            "refName": "DisqualifyUser",
            "arguments": {
              "rejectReason": "${ .rejectReason }"
            }
          }
        }
      ],
      "onErrors": [
        {
          "transition": "HandleInternalFailure",
          "errorRef": "DefaultErrorRef"
        }
      ]
    },
    {
      "transition": "GetOffersFromLender",
      "type": "operation",
      "name": "CreateApplicant",
      "actions": [
        {
          "retryRef": "ServicesNotAvailableRetryStrategy",
          "functionRef": {
            "refName": "LisaCreateApplicantV2",
            "arguments": {
              "loanApplicationID": "",
              "toUpdateLenderVariables": "true",
              "lenderID": "${ .lenderID }",
              "toUpdateLSQLead": "false",
              "toSkipIfUserCRMExists": "false",
              "toUpdateUserCRM": "false",
              "toSkipLoanCheck": "true"
            }
          }
        }
      ],
      "onErrors": [
        {
          "transition": "HandleInternalFailure",
          "errorRef": "DefaultErrorRef"
        }
      ]
    },
    {
      "transition": "checkOfferResponseStatus",
      "type": "operation",
      "name": "GetOffersFromLender",
      "actions": [
        {
          "retryRef": "ServicesNotAvailableRetryStrategy",
          "functionRef": {
            "refName": "LisaGetOfferV2",
            "arguments": {
              "intent": "FINAL",
              "lenderID": "${ .lenderID }"
            }
          }
        }
      ],
      "onErrors": [
        {
          "transition": "HandleInternalFailure",
          "errorRef": "DefaultErrorRef"
        }
      ]
    },
    {
      "dataConditions": [
        {
          "transition": "DisqualifyUser",
          "condition": ".response.segment | IN(\"pan_rejected\",\"age_rejected\",\"income_rejected\",\"pincode_rejected\",\"dedupe_rejected\",\"bureau_rejected\",\"policy_rejected\",\"lender_rejected\")"
        },
        {
          "transition": "SaveRedirectionURLInLenderVariables",
          "condition": ".response.segment | IN(\"policy_approved\",\"policy_approved_tentative\")"
        }
      ],
      "defaultCondition": {
        "transition": "HandleInternalFailure"
      },
      "type": "switch",
      "name": "checkOfferResponseStatus"
    },
    {
      "transition": "StartOfferWorkflow",
      "type": "operation",
      "name": "SaveRedirectionURLInLenderVariables",
      "actions": [
        {
          "retryRef": "ServicesNotAvailableRetryStrategy",
          "functionRef": {
            "refName": "UpsertLenderDynamicVariables",
            "arguments": {
              "lenderID": "${.lenderID}",
              "updateLenderDynamicVariables": {
                "redirectionURL": ".response.redirectionUrl"
              }
            }
          }
        }
      ]
    },
    {
      "transition": "CompleteWorkflow",
      "type": "operation",
      "name": "StartOfferWorkflow",
      "actions": [
        {
          "retryRef": "ServicesNotAvailableRetryStrategy",
          "functionRef": {
            "refName": "StartWorkflowFromConfig",
            "arguments": {
              "data": "${{ \"userObj\": .userObj, \"lisaOfferResponse\" : .response, \"lenderID\" : .lenderID }}",
              "sourceEntityID": "${ .userObj.sourceEntityID }",
              "userID": "${ .userObj.userID }",
              "lenderID": "${ .lenderID }",
              "workflowType": "OFFER"
            }
          }
        }
      ],
      "onErrors": [
        {
          "transition": "HandleInternalFailure",
          "errorRef": "DefaultErrorRef"
        }
      ]
    },
    {
      "end": true,
      "type": "operation",
      "name": "HandleInternalFailure",
      "actions": [
        {
          "retryRef": "ServicesNotAvailableRetryStrategy",
          "functionRef": {
            "refName": "UpdateUserWorkflowStatus",
            "arguments": {
              "errorType": "ERR_WF_FAILED",
              "workflowStatus": "FAILED",
              "failureReason": "INTERNAL_FAILURE"
            }
          }
        }
      ]
    },
    {
      "end": true,
      "type": "operation",
      "name": "CompleteWorkflow",
      "actions": [
        {
          "retryRef": "ServicesNotAvailableRetryStrategy",
          "functionRef": {
            "refName": "UpdateUserWorkflowStatus",
            "arguments": {
              "workflowStatus": "SUCCESS"
            }
          }
        },
        {
          "retryRef": "ServicesNotAvailableRetryStrategy",
          "functionRef": {
            "refName": "IncrementUserAPIModuleMapping",
            "arguments": {
              "moduleName": "PREQUALIFICATION"
            }
          }
        }
      ]
    }
  ],
  "start": "LenderDedupe",
  "version": "1.0.0",
  "specVersion": "0.8.0",
  "id": "a64baad2-8b3a-4605-a13c-111aade75e17"
}');