-- Create source entity for Prefr<>SuperMoney
insert into source_entity (source_entity_id, status, source_entity_name, created_at, created_by, server_api_key, 
pre_loan_schema, legal_name, show_assisted, 
credit_link_config, partner_code, organization_id, org_enabled, program_name)
values ('f06cf417-f415-4d93-b733-a45e885a4d36', 1, 'Prefr<>SuperMoney', now(), 'ADMIN', '956d29ab-cce0-4ddd-a768-d579a49ce8f5',
 '[]','SuperMoneyPrefr', false,
'{}', 'SPRFR', '8af96add-e7eb-4583-8ded-0cebe42121d8', false, 'PERSONAL_LOAN');

-- Create lender for Prefr<>SuperMoney
INSERT INTO lender (lender_id, organization_id, lender_name, status, created_at,
                    privacy_policy_url, tnc_url, lender_logo_url, lender_square_logo_url, created_by)
VALUES ('9c8551e3-fd00-46f0-b61b-64d0011f3bbd', '8af96add-e7eb-4583-8ded-0cebe42121d8', 'Prefr<>SuperMoney', 1, NOW(),
        '', '',
        'https://finbox-cdn.s3.ap-south-1.amazonaws.com/assets/prefr-logo.png', 'https://finbox-cdn.s3.ap-south-1.amazonaws.com/assets/prefr-logo.png', 'ADMIN') ON CONFLICT (lender_id) DO NOTHING;

-- Add active lender for Prefr<>SuperMoney
INSERT INTO platform_active_lenders (lender_id, source_entity_id, active, created_at, priority)
VALUES ('9c8551e3-fd00-46f0-b61b-64d0011f3bbd','f06cf417-f415-4d93-b733-a45e885a4d36', TRUE, NOW(), 1);


-- Create partner user data schema for Prefr<>SuperMoney
INSERT into partner_user_data_schemas(id, source_entity_id, data_schema_id, schema_type, version, invocation, created_at) values
('618fde69-4757-44c0-930a-801ac2f3ad28', 'f06cf417-f415-4d93-b733-a45e885a4d36', '1abab562-5e1d-474e-a9b1-47e3a4058457', 'USER_DETAILS', 1, 1, now())
on conflict (id) do update set (source_entity_id, data_schema_id, schema_type, created_at, version, invocation) =
(excluded.source_entity_id, excluded.data_schema_id, excluded.schema_type, excluded.created_at, excluded.version, excluded.invocation);

-- Create workflow for Prefr<>SuperMoney
INSERT INTO apistack_workflow (workflow_id, workflow_name, source_entity_id, status, workflow_data, created_at, created_by) VALUES
    (uuid_generate_v4(), 'Prefr<>SuperMoney', 'f06cf417-f415-4d93-b733-a45e885a4d36', 1, 
'{
  "1": {
    "endpoint": "/custom/prefr/lender/2.0/createApplication",
    "optional": false,
    "method": "POST",
    "metadata": {
      "moduleName": "CREATE_USER"
    }
  },
  "2": {
    "endpoint": "/custom/prefr/lender/2.0/createApplication",
    "optional": false,
    "method": "POST",
    "metadata": {
      "invocation": 1,
      "version": 1,
      "moduleName": "UPDATE_PROFILE"
    }
  },
  "3": {
    "endpoint": "/custom/prefr/lender/2.0/createApplication",
    "optional": false,
    "method": "POST",
    "metadata": {
      "moduleName": "PREQUALIFICATION"
    }
  }
}', now(), 'ADMIN') ON CONFLICT (workflow_name) DO UPDATE SET workflow_data = EXCLUDED.workflow_data;


INSERT INTO source_entity_webhook_data (webhook_data_id, source_entity_id, program_id, service_name, webhook_url, http_method, header_value_pair,
created_at, created_by, events, is_active, selection_mode) values (
  '770951ea-d968-49e2-9e84-bf6f45ae6183', 
  'f06cf417-f415-4d93-b733-a45e885a4d36', 
  '681392ab-b55c-44aa-9238-5de170e729d7', 
  'generic_webhook_service', 
  'https://www.sm-pandora.flipkart.net/pandora/loan/SME/V2/lsp/2.0/applicationStateEvent', -- UAT: https://www.sm-pandora-preprod.flipkart.net/pandora/loan/SME/V2, https://www.sm-pandora-preprod.flipkart.net/pandora/loan/SME/V2/lsp/2.0/applicationStateEvent
  'POST', 
  '[
    {
      "header": "X-Client-ID",
      "value": "114"
    }
  ]', 
  now(), 
  'ADMIN', 
  '[
      "user_created",
      "offer_generation_initiated",
      "offer_generated",
      "offer_generation_failed",
      "account_aggregator_started",
      "account_aggregator_completed",
      "account_aggregator_failed",
      "offer_final_generation_initiated",
      "offer_final_generated",
      "offer_final_generation_failed",
      "offer_acceptance_initiated",
      "offer_accepted",
      "pre_loan_data_submitted",
      "kyc_initiated",
      "kyc_verified",
      "kyc_failed",
      "bank_details_added",
      "bank_details_verified",
      "enach_started",
      "enach_completed",
      "enach_failed",
      "bank_verification_failed",
      "loan_esigned",
      "loan_esign_failed",
      "loan_disbursed",
      "loan_rejected",
      "user_disqualified",
      "loan_cancelled"
  ]', 
  true, 
  'WHITELIST'
) ON CONFLICT (webhook_data_id) DO UPDATE SET (source_entity_id, program_id, service_name, webhook_url, http_method, header_value_pair,
created_at, created_by, events, is_active, selection_mode) =
(excluded.source_entity_id, excluded.program_id, excluded.service_name, excluded.webhook_url, excluded.http_method, excluded.header_value_pair,
excluded.created_at, excluded.created_by, excluded.events, excluded.is_active, excluded.selection_mode);


    