---- BUREAU MODDULE - PFL SEGMENT STAMPING ----
UPDATE workflow_config SET is_active = false
WHERE
    module_name = 'BUREAU'
    AND source_entity_id = 'e023bd9e-46e2-4964-a8e7-d3e73e62421c'
    AND is_active = true;

INSERT INTO
workflow_config (
    config_id,
    source_entity_id,
    lender_id,
    organization_id,
    program_name,
    version,
    module_name,
    created_by,
    is_active,
    workflow_definition
) VALUES (
    uuid_generate_v4(),
    'e023bd9e-46e2-4964-a8e7-d3e73e62421c',
    'e023bd9e-46e2-4964-a8e7-d3e73e62421c',
    'db5fcba9-70cb-42b3-aaf1-d0afc0903e79',
    'PERSONAL_LOAN',
    17,
    'BUREAU',
    'ADMIN',
    true,
    '{
  "retries": [
    {
      "delay": "PT1S",
      "maxAttempts": 7,
      "name": "polling-retry",
      "multiplier": 3
    }
  ],
  "errors": [
    {
      "code": "500",
      "name": "LenderError",
      "description": "Handles lender api error - registers activity"
    }
  ],
  "name": "pfl_bureau",
  "states": [
    {
      "transition": "CheckFailedWorkflowAttemptsSwitch",
      "type": "operation",
      "name": "CheckWorkflowAttemptsByStatus",
      "actions": [
        {
          "retryRef": "RetryHTTP",
          "functionRef": {
            "refName": "CheckWorkflowAttemptsByStatus",
            "arguments": {
              "status": "FAILED",
              "moduleName": "BUREAU"
            }
          }
        }
      ],
      "metadata": {
        "subModuleNameTag": "BUREAU_PROGRESS",
        "moduleNameTag": "BUREAU"
      }
    },
    {
      "dataConditions": [
        {
          "transition": "WaitForRetrySignal",
          "condition": ".retryCount >= 3"
        }
      ],
      "defaultCondition": {
        "transition": "BREFlowDecide"
      },
      "type": "switch",
      "name": "CheckFailedWorkflowAttemptsSwitch"
    },
    {
      "onEvents": [
        {
          "eventRefs": [
            "retry_bureau"
          ]
        }
      ],
      "transition": "BREFlowDecide",
      "type": "event",
      "name": "WaitForRetrySignal",
      "metadata": {
        "subModuleNameTag": "RETRY_WAIT",
        "moduleNameTag": "BUREAU"
      }
    },
    {
      "transition": "BREFlowDecideSwitch",
      "type": "operation",
      "name": "BREFlowDecide",
      "actions": [
        {
          "retryRef": "no-retry",
          "functionRef": {
            "refName": "GetFeatureFlagValue",
            "arguments": {
              "flag": "bre_flow_v3",
              "key": "${.userObj.userID}"
            }
          }
        }
      ],
      "metadata": {
        "subModuleNameTag": "BUREAU_PROGRESS",
        "moduleNameTag": "BUREAU"
      }
    },
    {
      "end": true,
      "type": "operation",
      "name": "DisqualifyUser",
      "actions": [
        {
          "retryRef": "RetryHTTP",
          "functionRef": {
            "refName": "DisqualifyUser",
            "arguments": {
              "rejectionReason": "${if (.rejectReason != null and .rejectReason != \"\") then .rejectReason elif (.response.offers[0].message != null and .response.offers[0].message != \"\") then .response.offers[0].message else \"active loan exists at lender''s end\" end}"
            }
          }
        }
      ],
      "metadata": {
        "subModuleNameTag": "BUREAU_PROGRESS",
        "moduleNameTag": "BUREAU"
      }
    },
    {
      "dataConditions": [
        {
          "transition": "StartPreQualification",
          "condition": ".featureFlagValue == true or .userObj.dynamicUserInfoMap.segment == \"PRIME\" or .userObj.dynamicUserInfoMap.journeyType == \"preApproved\" or .userObj.dynamicUserInfoMap.journeyType == \"topUp\""
        }
      ],
      "defaultCondition": {
        "transition": "RuleEngineV1"
      },
      "type": "switch",
      "name": "BREFlowDecideSwitch"
    },
    {
      "end": true,
      "type": "operation",
      "name": "RuleEngineV1",
      "actions": [
        {
          "retryRef": "no-retry",
          "functionRef": {
            "refName": "CallBRE"
          }
        }
      ],
      "metadata": {
        "subModuleNameTag": "BUREAU_PROGRESS",
        "moduleNameTag": "BUREAU"
      }
    },
    {
      "transition": "BlackListedUserStatusSwitch",
      "type": "operation",
      "name": "StartPreQualification",
      "actions": [
        {
          "retryRef": "no-retry",
          "functionRef": {
            "refName": "CheckIfBlackListedUser"
          }
        }
      ],
      "metadata": {
        "subModuleNameTag": "BUREAU_PROGRESS",
        "moduleNameTag": "BUREAU"
      }
    },
    {
      "dataConditions": [
        {
          "transition": "DisqualifyUser",
          "condition": ".userStatus == 0"
        }
      ],
      "defaultCondition": {
        "transition": "AgeCheck"
      },
      "type": "switch",
      "name": "BlackListedUserStatusSwitch"
    },
    {
      "transition": "AgeCheckSwitch",
      "type": "operation",
      "name": "AgeCheck",
      "actions": [
        {
          "retryRef": "RetryHTTP",
          "functionRef": {
            "refName": "AgeCheckV2",
            "arguments": {
              "dob": "${.userObj.dob}",
              "lowerAgeLimit": 21,
              "upperAgeLimit": 55
            }
          }
        }
      ],
      "metadata": {
        "subModuleNameTag": "BUREAU_PROGRESS",
        "moduleNameTag": "BUREAU"
      }
    },
    {
      "dataConditions": [
        {
          "transition": "DisqualifyUser",
          "condition": ".userStatus == 0"
        }
      ],
      "defaultCondition": {
        "transition": "WaitForDeviceConnect"
      },
      "type": "switch",
      "name": "AgeCheckSwitch"
    },
    {
      "transition": "JourneyFlowSwitch",
      "type": "operation",
      "name": "WaitForDeviceConnect",
      "actions": [
        {
          "retryRef": "RetryHTTP",
          "functionRef": {
            "refName": "WaitForDC",
            "arguments": {
              "attempt": 65
            }
          }
        }
      ],
      "metadata": {
        "subModuleNameTag": "BUREAU_PROGRESS",
        "moduleNameTag": "BUREAU"
      }
    },
    {
      "name": "GetLisaOffer",
      "transition": "PflOfferEligibility",
      "actions": [
        {
          "functionRef": {
            "refName": "LisaGetOfferV2",
            "arguments": {
              "intent": "soft",
              "loanType": "personal_loan",
              "journeyType": "${.userObj.dynamicUserInfoMap.segment}"
            }
          }
        }
      ],
      "onErrors": [
        {
          "transition": "RegisterFailureEvent",
          "errorRef": "DefaultErrorRef"
        }
      ],
      "type": "operation",
      "metadata": {
        "subModuleNameTag": "BUREAU_PROGRESS",
        "moduleNameTag": "BUREAU"
      }
    },
    {
      "dataConditions": [
        {
          "transition": "DedupeCall",
          "condition": ".userObj.dynamicUserInfoMap.segment == \"PRIME\" or .userObj.dynamicUserInfoMap.journeyType == \"preApproved\" or .userObj.dynamicUserInfoMap.journeyType == \"topUp\""
        }
      ],
      "defaultCondition": {
        "transition": "GetLisaOffer"
      },
      "type": "switch",
      "name": "JourneyFlowSwitch"
    },
    {
      "dataConditions": [
        {
          "transition": "is-prime-flow",
          "condition": ".response.offers[0].offerEligibility != \"YES\""
        }
      ],
      "defaultCondition": {
        "transition": "CappedCheck"
      },
      "type": "switch",
      "name": "PflOfferEligibility"
    },
    {
      "name": "DedupeCall",
      "transition": "DedupeResultCheck",
      "actions": [
        {
          "functionRef": {
            "refName": "LisaDedupeCheckV2",
            "arguments": {
              "intent": "${if (.userObj.dynamicUserInfoMap.journeyType == \"preApproved\" or .userObj.dynamicUserInfoMap.journeyType == \"topUp\") then \"RT4\" else \"EXPERIAN\" end}",
              "lenderID": "e023bd9e-46e2-4964-a8e7-d3e73e62421c"
            }
          }
        }
      ],
      "onErrors": [
        {
          "transition": "RegisterDedupeFailureEvent",
          "errorRef": "DefaultErrorRef"
        }
      ],
      "type": "operation",
      "metadata": {
        "subModuleNameTag": "BUREAU_PROGRESS",
        "moduleNameTag": "BUREAU"
      }
    },
    {
      "transition": "FailWorkflow",
      "type": "operation",
      "name": "RegisterDedupeFailureEvent",
      "actions": [
        {
          "functionRef": {
            "refName": "RegisterActivityEvent",
            "arguments": {
              "eventType": "${if .userObj.dynamicUserInfoMap.journeyType == \"preApproved\" or .userObj.dynamicUserInfoMap.journeyType == \"topUp\" then \"rt4_api_failed\" else \"experian_api_failed\" end}",
              "entityType": "system",
              "description": ""
            }
          }
        }
      ],
      "metadata": {
        "subModuleNameTag": "BUREAU_PROGRESS",
        "moduleNameTag": "BUREAU"
      }
    },
    {
      "dataConditions": [
        {
          "transition": "DisqualifyUser",
          "condition": ".isLenderDedupe == true"
        },
        {
          "transition": "RegisterRT4PassEvent",
          "condition": ".userObj.dynamicUserInfoMap.journeyType == \"preApproved\" or .userObj.dynamicUserInfoMap.journeyType == \"topUp\""
        }
      ],
      "defaultCondition": {
        "transition": "RegisterExperianPassEvent"
      },
      "type": "switch",
      "name": "DedupeResultCheck"
    },
    {
      "transition": "TransposeOffer",
      "type": "operation",
      "name": "GetPFLOfferFromPartnerData",
      "actions": [
        {
          "functionRef": {
            "refName": "GetPFLOfferFromPartnerData",
            "arguments": {}
          }
        }
      ],
      "metadata": {
        "subModuleNameTag": "BUREAU_PROGRESS",
        "moduleNameTag": "BUREAU"
      }
    },
    {
      "transition": "FailWorkflow",
      "type": "operation",
      "name": "RegisterFailureEvent",
      "actions": [
        {
          "functionRef": {
            "refName": "RegisterActivityEvent",
            "arguments": {
              "eventType": "offer_generation_failed",
              "entityType": "customer",
              "description": ""
            }
          }
        }
      ],
      "metadata": {
        "subModuleNameTag": "WAIT",
        "moduleNameTag": "BUREAU"
      }
    },
    {
      "transition": "RuleEngine_EVALIDATION",
      "type": "operation",
      "name": "RegisterExperianPassEvent",
      "actions": [
        {
          "functionRef": {
            "refName": "RegisterActivityEvent",
            "arguments": {
              "eventType": "experian_passed",
              "entityType": "system",
              "description": ""
            }
          }
        }
      ],
      "metadata": {
        "subModuleNameTag": "WAIT",
        "moduleNameTag": "BUREAU"
      }
    },
    {
      "transition": "GetPFLOfferFromPartnerData",
      "type": "operation",
      "name": "RegisterRT4PassEvent",
      "actions": [
        {
          "functionRef": {
            "refName": "RegisterActivityEvent",
            "arguments": {
              "eventType": "rt4_passed",
              "entityType": "system",
              "description": ""
            }
          }
        }
      ],
      "metadata": {
        "subModuleNameTag": "BUREAU_PROGRESS",
        "moduleNameTag": "BUREAU"
      }
    },
    {
      "end": true,
      "type": "operation",
      "name": "FailWorkflow",
      "actions": [
        {
          "functionRef": {
            "refName": "ErrorStateActivity",
            "arguments": {
              "errorMessage": "Lender Api Failed"
            }
          }
        }
      ],
      "metadata": {
        "subModuleNameTag": "BUREAU_PROGRESS",
        "moduleNameTag": "BUREAU"
      }
    },
    {
      "transition": "TransposeOffer",
      "type": "operation",
      "name": "CappedCheck",
      "actions": [
        {
          "functionRef": {
            "refName": "PflCappedCheck",
            "arguments": {
              "response": "${.response}"
            }
          }
        }
      ],
      "metadata": {
        "subModuleNameTag": "BUREAU_PROGRESS",
        "moduleNameTag": "BUREAU"
      }
    },
    {
      "transition": "SavePLOffer",
      "type": "operation",
      "name": "TransposeOffer",
      "actions": [
        {
          "functionRef": {
            "refName": "TransposeOffer",
            "arguments": {
              "offers": "${.offers}",
              "lenderID": "e023bd9e-46e2-4964-a8e7-d3e73e62421c",
              "programName": "personal_loan"
            }
          }
        }
      ],
      "metadata": {
        "subModuleNameTag": "BUREAU_PROGRESS",
        "moduleNameTag": "BUREAU"
      }
    },
    {
      "transition": "RegisterPlEvent",
      "type": "operation",
      "name": "SavePLOffer",
      "actions": [
        {
          "functionRef": {
            "refName": "SavePLOffer",
            "arguments": {
              "status": 1,
              "offerMetadataBytes": "${.offerMetadataBytes}",
              "processingFeeType": "PERC",
              "maxEMI": -1,
              "minTenure": 0,
              "interest": "${.offers[0].interestRate}",
              "minAmount": 0,
              "lenderID": "e023bd9e-46e2-4964-a8e7-d3e73e62421c",
              "method": "rb",
              "maxTenure": "${.offers[0].tenure}",
              "processingFee": "${.offers[0].processingFee}",
              "offerType": "soft",
              "subProductType": "${.offers[0].offerType}",
              "maxAmount": "${.offers[0].amount}"
            }
          }
        }
      ],
      "metadata": {
        "subModuleNameTag": "BUREAU_PROGRESS",
        "moduleNameTag": "BUREAU"
      }
    },
    {
      "transition": "OfferExpiry",
      "type": "operation",
      "name": "RegisterPlEvent",
      "actions": [
        {
          "functionRef": {
            "refName": "RegisterActivityEvent",
            "arguments": {
              "eventType": "offer_generated",
              "entityType": "customer",
              "description": ""
            }
          }
        }
      ],
      "metadata": {
        "subModuleNameTag": "WAIT",
        "moduleNameTag": "BUREAU"
      }
    },
    {
      "transition": "is-prime-flow",
      "type": "operation",
      "name": "OfferExpiry",
      "actions": [
        {
          "functionRef": {
            "refName": "Mapper",
            "arguments": {
              "config": {
                "offerValidityTimeData": "{\"validityDays\":0, \"validityYears\":0, \"validityMonths\":0}",
                "offerValidityData": "{\"timeFormat\": \"01/02/2006\", \"offerValidity\": .offers[0].offerValidity}"
              }
            }
          }
        },
        {
          "functionRef": {
            "refName": "OfferExpiry",
            "arguments": {
              "offerValidityStruct": "${.offerValidityData}",
              "offerValidityTimeStruct": "${.offerValidityTimeData}"
            }
          }
        }
      ],
      "metadata": {
        "subModuleNameTag": "BUREAU_PROGRESS",
        "moduleNameTag": "BUREAU"
      }
    },
    {
      "transition": "FetchEvaluation",
      "type": "operation",
      "name": "RuleEngineV2",
      "actions": [
        {
          "retryRef": "no-retry",
          "functionRef": {
            "refName": "SentinelDumpAndTriggerEvaluation",
            "arguments": {
              "source": "bureau",
              "evaluationType": "bureau",
              "ruleVersion": "",
              "lenderID": "e023bd9e-46e2-4964-a8e7-d3e73e62421c",
              "updateData": {
                "AdditionalParams.Segment": ".response.segment",
                "AdditionalParams.BankConnectFlag": ".response.bcFlag",
                "LatestPersonalLoanOffer.EligibleAmount": ".response.offers[0].amount",
                "AdditionalParams.PFLOfferEligibile": ".response.offers[0].offerEligibility"
              },
              "additionalDataSources": "${[\"sentinelJQConfig\"]}"
            }
          }
        }
      ],
      "metadata": {
        "subModuleNameTag": "WAIT",
        "moduleNameTag": "BUREAU"
      }
    },
    {
      "transition": "FetchEvaluation",
      "type": "operation",
      "name": "RuleEngineV2-prime",
      "actions": [
        {
          "retryRef": "no-retry",
          "functionRef": {
            "refName": "SentinelDumpAndTriggerEvaluation",
            "arguments": {
              "source": "bureau",
              "evaluationType": "bureau",
              "ruleVersion": "",
              "lenderID": "e023bd9e-46e2-4964-a8e7-d3e73e62421c",
              "updateData": {
                "AdditionalParams.Segment": ".response.segment",
                "AdditionalParams.BankConnectFlag": ".response.bcFlag",
                "LatestPersonalLoanOffer.EligibleAmount": ".response.offers[0].amount",
                "AdditionalParams.PFLOfferEligibile": ".response.offers[0].offerEligibility"
              },
              "additionalDataSources": "${[\"sentinelJQConfig\"]}"
            }
          }
        }
      ],
      "metadata": {
        "subModuleNameTag": "WAIT",
        "moduleNameTag": "BUREAU"
      }
    },
    {
      "transition": "DecisionCheckSwitch",
      "type": "operation",
      "name": "FetchEvaluation",
      "actions": [
        {
          "retryRef": "polling-retry",
          "functionRef": {
            "refName": "FetchEvaluation",
            "arguments": {
              "sentinelEvaluationID": "${.sentinelEvaluationID}",
              "maxAttempts": 7,
              "evaluationCode": "${.evaluationCode}",
              "lenderID": "e023bd9e-46e2-4964-a8e7-d3e73e62421c",
              "dumpID": "${.dumpID}"
            }
          }
        }
      ],
      "metadata": {
        "subModuleNameTag": "WAIT",
        "moduleNameTag": "BUREAU"
      }
    },
    {
      "dataConditions": [
        {
          "transition": "DisqualifyUser",
          "condition": ".decision == \"reject\""
        },
        {
          "transition": "SwitchWorkflow",
          "condition": ".decision == \"skip_bankconnect\""
        }
      ],
      "defaultCondition": {
        "transition": "DistanceCheck"
      },
      "type": "switch",
      "name": "DecisionCheckSwitch"
    },
    {
      "transition": "QualifyUser",
      "type": "operation",
      "name": "DistanceCheck",
      "actions": [
        {
          "functionRef": {
            "refName": "PflDistanceCheck",
            "arguments": {
              "riskSegment": "${ .response.segment }",
              "sentinelResponse": "${.sentinelResponse}",
              "disableDistanceCheck": "${if (.userObj.dynamicUserInfoMap.segment == \"PRIME\" or .userObj.dynamicUserInfoMap.journeyType == \"topUp\") then true else false end}"
            }
          }
        }
      ],
      "metadata": {
        "subModuleNameTag": "WAIT",
        "moduleNameTag": "BUREAU"
      }
    },
    {
      "transition": "DistanceCheck",
      "type": "operation",
      "name": "SwitchWorkflow",
      "actions": [
        {
          "functionRef": {
            "refName": "SwitchCurrentModulesWorkflow",
            "arguments": {
              "workflowID": "${if .userObj.dynamicUserInfoMap.journeyType == \"topUp\" then \"3872f80d-afa9-479b-8e31-12ac0faac385\" else \"40568d3d-b2fb-41a3-93e4-6e88467140db\" end}"
            }
          }
        }
      ]
    },
    {
      "transition": "RegisterActivity",
      "type": "operation",
      "name": "QualifyUser",
      "actions": [
        {
          "retryRef": "no-retry",
          "functionRef": {
            "refName": "Mapper",
            "arguments": {
              "config": {
                "updateObj": {
                  "status": "4",
                  "userID": ".userObj.userID"
                }
              }
            }
          }
        },
        {
          "retryRef": "no-retry",
          "functionRef": {
            "refName": "UpdateUserDetails",
            "arguments": {
              "updateObj": "${ .updateObj }"
            }
          }
        }
      ],
      "metadata": {
        "subModuleNameTag": "WAIT",
        "moduleNameTag": "BUREAU"
      }
    },
    {
      "name": "RuleEngine_EVALIDATION",
      "transition": "FetchEvaluation_EVALIDATION",
      "actions": [
        {
          "retryRef": "no-retry",
          "functionRef": {
            "refName": "SentinelDumpAndTriggerEvaluation",
            "arguments": {
              "evaluationType": "e_validation",
              "source": "e_validation",
              "lenderID": "e023bd9e-46e2-4964-a8e7-d3e73e62421c",
              "additionalDataSources": "${[\"sentinelJQConfig\"]}",
              "ruleVersion": ""
            }
          }
        }
      ],
      "onErrors": [
        {
          "transition": "register-epfo-api-failure-event",
          "errorRef": "DefaultErrorRef"
        }
      ],
      "type": "operation",
      "metadata": {
        "subModuleNameTag": "WAIT",
        "moduleNameTag": "BUREAU"
      }
    },
    {
      "name": "FetchEvaluation_EVALIDATION",
      "transition": "EValidationDecisionCheckSwitch",
      "actions": [
        {
          "retryRef": "polling-retry",
          "functionRef": {
            "refName": "FetchEvaluation",
            "arguments": {
              "sentinelEvaluationID": "${.sentinelEvaluationID}",
              "maxAttempts": 7,
              "evaluationCode": "${.evaluationCode}",
              "lenderID": "e023bd9e-46e2-4964-a8e7-d3e73e62421c",
              "dumpID": "${.dumpID}"
            }
          }
        },
        {
          "functionRef": {
            "refName": "Mapper",
            "arguments": {
              "config": {
                "epfoValidationData": "{\"epfoValidationStatus\": (if .decision == \"reject\" then \"not-verified\" else \"verified\" end)}"
              }
            }
          }
        },
        {
          "retryRef": "NoRetry",
          "functionRef": {
            "refName": "UpdateDynamicUserInfoV2",
            "arguments": {
              "userID": "${.userObj.UserID}",
              "dynamicUserInfo": "${.epfoValidationData}"
            }
          }
        }
      ],
      "onErrors": [
        {
          "transition": "register-epfo-api-failure-event",
          "errorRef": "DefaultErrorRef"
        }
      ],
      "type": "operation",
      "metadata": {
        "subModuleNameTag": "WAIT",
        "moduleNameTag": "BUREAU"
      }
    },
    {
      "name": "register-epfo-api-failure-event",
      "transition": "AddDomains",
      "actions": [
        {
          "functionRef": {
            "refName": "RegisterActivityEvent",
            "arguments": {
              "eventType": "epfo_api_failed",
              "entityType": "system",
              "entityRef": "${.userObj.UserID}",
              "description": ""
            }
          }
        },
        {
          "functionRef": {
            "refName": "Mapper",
            "arguments": {
              "config": {
                "applicationReq": {
                  "type": "\"PRIME\"",
                  "sourceEntityID": "\"e023bd9e-46e2-4964-a8e7-d3e73e62421c\"",
                  "userID": ".userObj.userID",
                  "lenderID": "\"e023bd9e-46e2-4964-a8e7-d3e73e62421c\""
                }
              }
            }
          }
        },
        {
          "functionRef": {
            "refName": "LisaUpdateApplicationV2",
            "arguments": {
              "intent": "epfo_failed",
              "applicationReq": "${.applicationReq}"
            }
          },
          "actionDataFilter": {
            "useResults": false
          }
        }
      ],
      "onErrors": [
        {
          "transition": "AddDomains",
          "errorRef": "LenderError"
        },
        {
          "transition": "AddDomains",
          "errorRef": "DefaultErrorRef"
        }
      ],
      "type": "operation",
      "metadata": {
        "subModuleNameTag": "WAIT",
        "moduleNameTag": "BUREAU"
      }
    },
    {
      "dataConditions": [
        {
          "transition": "register-epfo-reject-event",
          "condition": ".decision == \"reject\""
        }
      ],
      "defaultCondition": {
        "transition": "register-epfo-success-event"
      },
      "type": "switch",
      "name": "EValidationDecisionCheckSwitch"
    },
    {
      "name": "register-epfo-reject-event",
      "transition": "AddDomains",
      "actions": [
        {
          "functionRef": {
            "refName": "RegisterActivityEvent",
            "arguments": {
              "eventType": "epfo_verification_failed",
              "entityType": "system",
              "entityRef": "${.userObj.UserID}",
              "description": "${ \"segment: \" + (if (.userObj.dynamicUserInfoMap.segment | tostring) == \"\" then \"STPL\" else (.userObj.dynamicUserInfoMap.segment | tostring) end) }"
            }
          }
        },
        {
          "functionRef": {
            "refName": "Mapper",
            "arguments": {
              "config": {
                "applicationReq": {
                  "type": "\"PRIME\"",
                  "sourceEntityID": "\"e023bd9e-46e2-4964-a8e7-d3e73e62421c\"",
                  "userID": ".userObj.userID",
                  "lenderID": "\"e023bd9e-46e2-4964-a8e7-d3e73e62421c\""
                }
              }
            }
          }
        },
        {
          "functionRef": {
            "refName": "LisaUpdateApplicationV2",
            "arguments": {
              "intent": "epfo_failed",
              "applicationReq": "${.applicationReq}"
            }
          },
          "actionDataFilter": {
            "useResults": false
          }
        }
      ],
      "onErrors": [
        {
          "transition": "AddDomains",
          "errorRef": "LenderError"
        },
        {
          "transition": "AddDomains",
          "errorRef": "DefaultErrorRef"
        }
      ],
      "type": "operation",
      "metadata": {
        "subModuleNameTag": "WAIT",
        "moduleNameTag": "BUREAU"
      }
    },
    {
      "transition": "GetLisaOffer",
      "type": "operation",
      "name": "register-epfo-success-event",
      "actions": [
        {
          "functionRef": {
            "refName": "RegisterActivityEvent",
            "arguments": {
              "eventType": "epfo_verified",
              "entityType": "system",
              "entityRef": "${.userObj.UserID}",
              "description": "${ \"segment: \" + (if (.userObj.dynamicUserInfoMap.segment | tostring) == \"\" then \"STPL\" else (.userObj.dynamicUserInfoMap.segment | tostring) end) }"
            }
          }
        }
      ],
      "metadata": {
        "subModuleNameTag": "WAIT",
        "moduleNameTag": "BUREAU"
      }
    },
    {
      "transition": "GetLisaOffer",
      "type": "operation",
      "name": "redirection-to-non-prime-flow",
      "actions": [
        {
          "retryRef": "no-retry",
          "functionRef": {
            "refName": "UpdateDynamicUserInfo",
            "arguments": {
              "dynamicUserInfo": {
                "segment": ""
              }
            }
          }
        },
        {
          "functionRef": {
            "refName": "RegisterActivityEvent",
            "arguments": {
              "eventType": "user_redirected",
              "entityType": "customer",
              "entityRef": "${.userObj.UserID}",
              "description": "${ \"segment: \" + (if (.userObj.dynamicUserInfoMap.segment | tostring) == \"\" then \"STPL\" else (.userObj.dynamicUserInfoMap.segment | tostring) end) + \", user redirected from prime to stpl flow\"}"
            }
          }
        }
      ],
      "metadata": {
        "subModuleNameTag": "WAIT",
        "moduleNameTag": "BUREAU"
      }
    },
    {
      "dataConditions": [
        {
          "transition": "RuleEngineV2-prime",
          "condition": ".userObj.dynamicUserInfoMap.segment == \"PRIME\""
        }
      ],
      "defaultCondition": {
        "transition": "RuleEngineV2"
      },
      "type": "switch",
      "name": "is-prime-flow",
      "metadata": {
        "subModuleNameTag": "",
        "moduleNameTag": "BUREAU"
      }
    },
    {
      "end": true,
      "type": "operation",
      "name": "RegisterActivity",
      "actions": [
        {
          "functionRef": {
            "refName": "RegisterActivityEvent",
            "arguments": {
              "eventType": "user_qualified",
              "description": "${.description}"
            }
          }
        }
      ],
      "metadata": {
        "subModuleNameTag": "WAIT",
        "moduleNameTag": "BUREAU"
      }
    },
    {
      "transition": "SendOtp",
      "type": "operation",
      "name": "AddDomains",
      "actions": [
        {
          "retryRef": "no-retry",
          "functionRef": {
            "refName": "Mapper",
            "arguments": {
              "config": {
                "resendCounter": "0"
              }
            }
          }
        },
        {
          "retryRef": "no-retry",
          "functionRef": {
            "refName": "OutputMapper",
            "arguments": {
              "config": {
                "domains": ".userObj.dynamicUserInfoMap.companyDomain",
                "disableResend": {
                  "redirect": "false",
                  "disable": "false"
                },
                "isPrime": "if .userObj.dynamicUserInfoMap.segment == \"PRIME\" then true else false end",
                "showSkipButton": "true",
                "reject": "false"
              }
            }
          }
        }
      ],
      "metadata": {
        "subModuleNameTag": "WAIT",
        "moduleNameTag": "BUREAU"
      }
    },
    {
      "eventConditions": [
        {
          "transition": "HandleSkipEmailVerification",
          "eventRef": "skip_email_verification"
        },
        {
          "transition": "EmailChangeMapper",
          "eventRef": "send_otp"
        }
      ],
      "defaultCondition": {
        "transition": "SendOtp"
      },
      "name": "SendOtp",
      "timeouts": {
        "eventTimeout": "PT24H"
      },
      "type": "switch",
      "metadata": {
        "subModuleNameTag": "EMAIL_VERIFICATION",
        "moduleNameTag": "BUREAU"
      }
    },
    {
      "transition": "ResendMapper",
      "type": "operation",
      "name": "EmailChangeMapper",
      "actions": [
        {
          "retryRef": "no-retry",
          "functionRef": {
            "refName": "Mapper",
            "arguments": {
              "config": {
                "emailCounter": "0"
              }
            }
          }
        }
      ],
      "metadata": {
        "subModuleNameTag": "WAIT",
        "moduleNameTag": "BUREAU"
      }
    },
    {
      "transition": "GenerateOTP",
      "type": "operation",
      "name": "ResendMapper",
      "actions": [
        {
          "retryRef": "no-retry",
          "functionRef": {
            "refName": "Mapper",
            "arguments": {
              "config": {
                "dummy": "0"
              }
            }
          }
        }
      ],
      "metadata": {
        "subModuleNameTag": "WAIT",
        "moduleNameTag": "BUREAU"
      }
    },
    {
      "transition": "OtpCheck",
      "type": "operation",
      "name": "GenerateOTP",
      "actions": [
        {
          "retryRef": "RetryHTTP",
          "functionRef": {
            "refName": "GenerateOTPV3",
            "arguments": {
              "authID": "${.signal_data_send_otp.email}"
            }
          }
        }
      ],
      "metadata": {
        "subModuleNameTag": "WAIT",
        "moduleNameTag": "BUREAU"
      }
    },
    {
      "dataConditions": [
        {
          "transition": "LenderRejectionMapper",
          "condition": ".otp == 0 "
        }
      ],
      "defaultCondition": {
        "transition": "SendEmail"
      },
      "type": "switch",
      "name": "OtpCheck",
      "metadata": {
        "subModuleNameTag": "WAIT",
        "moduleNameTag": "BUREAU"
      }
    },
    {
      "name": "SendEmail",
      "transition": "SubmitOtp",
      "actions": [
        {
          "functionRef": {
            "refName": "Mapper",
            "arguments": {
              "config": {
                "emailConfig": {
                  "OTPCode": ".otp",
                  "CustomerName": ".userObj.name",
                  "ProductName": "\"Personal_Loan\""
                },
                "email": ".signal_data_send_otp.email"
              }
            }
          }
        },
        {
          "retryRef": "RetryHTTP",
          "functionRef": {
            "refName": "SendSMTPEmail",
            "arguments": {
              "username": "",
              "recipientEmailID": "${ .signal_data_send_otp.email}",
              "emailConfig": "${ .emailConfig }",
              "emailSubjectKey": "opt_verification_email_subject_pfl",
              "senderEmailID": "<EMAIL>",
              "port": 587,
              "recipientDisplayName": "${ .userObj.name }",
              "host": "smtp.poonawallafincorp.com",
              "password": "",
              "emailBodyKey": "opt_verification_email_body_pfl",
              "senderDisplayName": "PoonawallaFincorpLimited"
            }
          }
        }
      ],
      "onErrors": [
        {
          "transition": "EmailChangeCheck",
          "errorRef": "DefaultErrorRef"
        }
      ],
      "type": "operation",
      "metadata": {
        "subModuleNameTag": "WAIT",
        "moduleNameTag": "BUREAU"
      }
    },
    {
      "eventConditions": [
        {
          "transition": "VerifyOtp",
          "eventRef": "submit_otp"
        },
        {
          "transition": "EmailChangeCheck",
          "eventRef": "send_otp"
        },
        {
          "transition": "redirection-to-non-prime-flow",
          "eventRef": "skip_email_verification"
        }
      ],
      "defaultCondition": {
        "transition": "SubmitOtp"
      },
      "name": "SubmitOtp",
      "timeouts": {
        "eventTimeout": "PT24H"
      },
      "type": "switch",
      "metadata": {
        "subModuleNameTag": "EMAIL_VERIFICATION",
        "moduleNameTag": "BUREAU"
      }
    },
    {
      "dataConditions": [
        {
          "transition": "Increase-Resend-Counter",
          "condition": ".email == .signal_data_send_otp.email"
        }
      ],
      "defaultCondition": {
        "transition": "Increase-Email-Change-Counter"
      },
      "type": "switch",
      "name": "EmailChangeCheck",
      "metadata": {
        "subModuleNameTag": "WAIT",
        "moduleNameTag": "BUREAU"
      }
    },
    {
      "transition": "EmailCounterCheck",
      "type": "operation",
      "name": "Increase-Email-Change-Counter",
      "actions": [
        {
          "retryRef": "RetryHTTP",
          "functionRef": {
            "refName": "IncrementValue",
            "arguments": {
              "counterVariable": "emailCounter"
            }
          }
        }
      ],
      "metadata": {
        "subModuleNameTag": "WAIT",
        "moduleNameTag": "BUREAU"
      }
    },
    {
      "dataConditions": [
        {
          "transition": "PrepareForRedirection",
          "condition": ".emailCounter >= 3"
        }
      ],
      "defaultCondition": {
        "transition": "ResendMapper"
      },
      "type": "switch",
      "name": "EmailCounterCheck",
      "metadata": {
        "subModuleNameTag": "WAIT",
        "moduleNameTag": "BUREAU"
      }
    },
    {
      "transition": "ResendCounterCheck",
      "type": "operation",
      "name": "Increase-Resend-Counter",
      "actions": [
        {
          "functionRef": {
            "refName": "Mapper",
            "arguments": {
              "config": {
                "resendCounterInit": "0"
              }
            }
          }
        },
        {
          "retryRef": "RetryHTTP",
          "functionRef": {
            "refName": "IncrementValue",
            "arguments": {
              "initialValue": "${.resendCounterInit}",
              "counterVariable": "resendCounter"
            }
          }
        }
      ],
      "metadata": {
        "subModuleNameTag": "WAIT",
        "moduleNameTag": "BUREAU"
      }
    },
    {
      "transition": "LenderRejectionMapper",
      "type": "operation",
      "name": "PrepareForRedirection",
      "actions": [
        {
          "retryRef": "no-retry",
          "functionRef": {
            "refName": "OutputMapper",
            "arguments": {
              "config": {
                "disableResend": {
                  "redirect": "true",
                  "disable": "true"
                }
              }
            }
          }
        }
      ],
      "metadata": {
        "subModuleNameTag": "WAIT",
        "moduleNameTag": "BUREAU"
      }
    },
    {
      "dataConditions": [
        {
          "transition": "PrepareForRedirection",
          "condition": ".resendCounter >= 3"
        }
      ],
      "defaultCondition": {
        "transition": "GenerateOTP"
      },
      "type": "switch",
      "name": "ResendCounterCheck",
      "metadata": {
        "subModuleNameTag": "WAIT",
        "moduleNameTag": "BUREAU"
      }
    },
    {
      "transition": "ResendDisablePolling",
      "type": "operation",
      "name": "DisableResend",
      "actions": [
        {
          "retryRef": "no-retry",
          "functionRef": {
            "refName": "OutputMapper",
            "arguments": {
              "config": {
                "disableResend": {
                  "redirect": "true",
                  "disable": "true"
                }
              }
            }
          }
        }
      ],
      "metadata": {
        "subModuleNameTag": "WAIT",
        "moduleNameTag": "BUREAU"
      }
    },
    {
      "duration": "PT5M",
      "transition": "EnableResend",
      "type": "sleep",
      "name": "ResendDisablePolling"
    },
    {
      "transition": "SubmitOtp",
      "type": "operation",
      "name": "EnableResend",
      "actions": [
        {
          "retryRef": "no-retry",
          "functionRef": {
            "refName": "OutputMapper",
            "arguments": {
              "config": {
                "disableResend": {
                  "redirect": "false",
                  "disable": "false"
                }
              }
            }
          }
        }
      ],
      "metadata": {
        "subModuleNameTag": "WAIT",
        "moduleNameTag": "BUREAU"
      }
    },
    {
      "transition": "VerifyCheck",
      "type": "operation",
      "name": "VerifyOtp",
      "actions": [
        {
          "retryRef": "RetryHTTP",
          "functionRef": {
            "refName": "OutputMapper",
            "arguments": {
              "config": {
                "otp_verification_status": {
                  "status": "pending"
                }
              }
            }
          }
        },
        {
          "retryRef": "RetryHTTP",
          "functionRef": {
            "refName": "VerifyOTPV3",
            "arguments": {
              "mobile": "${ .signal_data_submit_otp.email }",
              "otpType": "pfl_email_verification",
              "otpNumber": "${ .signal_data_submit_otp.otp_number }"
            }
          }
        }
      ],
      "metadata": {
        "subModuleNameTag": "WAIT",
        "moduleNameTag": "BUREAU"
      }
    },
    {
      "dataConditions": [
        {
          "transition": "Otp-verified-activity",
          "condition": ".otpVerified == true "
        },
        {
          "transition": "LenderRejectionMapper",
          "condition": ".otpErrorMessage == \"Max failed attempts reached, try again after sometime\""
        }
      ],
      "defaultCondition": {
        "transition": "SubmitOtp"
      },
      "type": "switch",
      "name": "VerifyCheck",
      "metadata": {
        "subModuleNameTag": "WAIT",
        "moduleNameTag": "BUREAU"
      }
    },
    {
      "transition": "redirection-to-non-prime-flow",
      "type": "operation",
      "name": "LenderRejectionMapper",
      "actions": [
        {
          "functionRef": {
            "refName": "RegisterActivityEvent",
            "arguments": {
              "eventType": "email_verification_failed",
              "entityType": "system",
              "entityRef": "${.userObj.UserID}",
              "description": "email verification failed after multiple attempts"
            }
          }
        }
      ],
      "metadata": {
        "subModuleNameTag": "WAIT",
        "moduleNameTag": "BUREAU"
      }
    },
    {
      "transition": "redirection-to-non-prime-flow",
      "type": "operation",
      "name": "HandleSkipEmailVerification",
      "actions": [
        {
          "retryRef": "NoRetry",
          "functionRef": {
            "refName": "UpdateDynamicUserInfoV2",
            "arguments": {
              "userID": "${.currentDirectorInfo.userID}",
              "dynamicUserInfo": {
                "userSkippedEmail": "true"
              }
            }
          }
        },
        {
          "functionRef": {
            "refName": "RegisterActivityEvent",
            "arguments": {
              "eventType": "email_verification_skipped",
              "entityType": "system",
              "entityRef": "${.userObj.UserID}",
              "description": "${ \"segment: \" + (if (.userObj.dynamicUserInfoMap.segment | tostring) == \"\" then \"STPL\" else (.userObj.dynamicUserInfoMap.segment | tostring) end) + \", skipped official email verification\"}"
            }
          }
        }
      ],
      "metadata": {
        "subModuleNameTag": "WAIT",
        "moduleNameTag": "BUREAU"
      }
    },
    {
      "transition": "Add-email-field",
      "type": "operation",
      "name": "Otp-verified-activity",
      "actions": [
        {
          "functionRef": {
            "refName": "RegisterActivityEvent",
            "arguments": {
              "eventType": "official_email_verified",
              "entityType": "system",
              "entityRef": "${.userObj.UserID}",
              "description": "${ \"segment: \" + (if (.userObj.dynamicUserInfoMap.segment | tostring) == \"\" then \"STPL\" else (.userObj.dynamicUserInfoMap.segment | tostring) end)}"
            }
          }
        }
      ],
      "metadata": {
        "subModuleNameTag": "WAIT",
        "moduleNameTag": "BUREAU"
      }
    },
    {
      "transition": "GetLisaOffer",
      "type": "operation",
      "name": "Add-email-field",
      "actions": [
        {
          "retryRef": "no-retry",
          "functionRef": {
            "refName": "Mapper",
            "arguments": {
              "config": {
                "additionalFields": {
                  "officialEmail": ".signal_data_submit_otp.email"
                }
              }
            }
          }
        },
        {
          "retryRef": "no-retry",
          "functionRef": {
            "refName": "AppendKeysToDynamicUserInfo",
            "arguments": {
              "additionalFields": "${ .additionalFields }"
            }
          }
        }
      ],
      "metadata": {
        "subModuleNameTag": "WAIT",
        "moduleNameTag": "BUREAU"
      }
    }
  ],
  "start": "CheckWorkflowAttemptsByStatus",
  "version": "1.1.0",
  "specVersion": "0.0.1",
  "id": "fa3790d6-285a-44a7-8815-6186abe1930d"
}'
) ON CONFLICT (config_id)
DO UPDATE SET (
    source_entity_id,
    lender_id,
    organization_id,
    program_name,
    version,
    module_name,
    created_by,
    is_active,
    workflow_definition
) = (
    excluded.source_entity_id,
    excluded.lender_id,
    excluded.organization_id,
    excluded.program_name,
    excluded.version,
    excluded.module_name,
    excluded.created_by,
    excluded.is_active,
    excluded.workflow_definition
);