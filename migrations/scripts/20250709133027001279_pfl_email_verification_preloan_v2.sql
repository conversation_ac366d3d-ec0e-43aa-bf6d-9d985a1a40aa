UPDATE workflow_config SET is_active = false
WHERE
    module_name = 'PRELOAN'
    AND source_entity_id = 'e023bd9e-46e2-4964-a8e7-d3e73e62421c'
    AND is_active = true;

INSERT INTO
workflow_config (
    config_id,
    source_entity_id,
    lender_id,
    organization_id,
    program_name,
    version,
    module_name,
    created_by,
    is_active,
    workflow_definition
) VALUES (
    uuid_generate_v4(),
    'e023bd9e-46e2-4964-a8e7-d3e73e62421c',
    'e023bd9e-46e2-4964-a8e7-d3e73e62421c',
    'db5fcba9-70cb-42b3-aaf1-d0afc0903e79',
    'PERSONAL_LOAN',
    8,
    'PRELOAN',
    'ADMIN',
    true,
    '{
  "id": "c03fe29c-7aee-42da-89de-ee38deccd366",
  "name": "PFL Pre Loan",
  "start": "prime-check",
  "states": [
    {
      "name": "prime-check",
      "type": "operation",
      "actions": [
        {
          "retryRef": "no-retry",
          "functionRef": {
            "refName": "OutputMapperV2",
            "arguments": {
              "config": {
                "reject": "false",
                "isPrime": "if .userObj.dynamicUserInfoMap.segment == \"PRIME\" then true else false end",
                "disableResend": {
                  "disable": "false",
                  "redirect": "false"
                },
                "isEpfoVerified": "if .userObj.dynamicUserInfoMap.epfoValidationStatus == \"verified\" then true else false end"
              }
            }
          }
        },
        {
          "retryRef": "no-retry",
          "functionRef": {
            "refName": "OutputMapperV2",
            "arguments": {
              "config": {
                "otpVerification": {}
              }
            }
          }
        }
      ],
      "metadata": {
        "moduleNameTag": "PRELOAN",
        "subModuleNameTag": "PRE_LOAN_DETAILS"
      },
      "transition": "preloan-submit"
    },
    {
      "name": "preloan-submit",
      "type": "event",
      "metadata": {
        "moduleNameTag": "PRELOAN",
        "subModuleNameTag": "PRE_LOAN_DETAILS"
      },
      "onEvents": [
        {
          "eventRefs": [
            "pre_loan_submitted"
          ]
        }
      ],
      "transition": "ValidatePreLoan"
    },
    {
      "name": "ValidatePreLoan",
      "type": "operation",
      "actions": [
        {
          "functionRef": {
            "refName": "ValidatePreLoan",
            "arguments": {
              "preLoanData": "${ .signal_data_pre_loan_submitted.preLoanData }",
              "loanApplicationID": "${ .signal_data_pre_loan_submitted.loanApplicationID }"
            }
          }
        }
      ],
      "metadata": {
        "moduleNameTag": "PRELOAN",
        "subModuleNameTag": "WAIT"
      },
      "transition": "ErrorCheckForPreLoan"
    },
    {
      "name": "ErrorCheckForPreLoan",
      "type": "switch",
      "metadata": {
        "moduleNameTag": "PRELOAN",
        "subModuleNameTag": "WAIT"
      },
      "dataConditions": [
        {
          "condition": ".isError == true ",
          "transition": "preloan-submit"
        }
      ],
      "defaultCondition": {
        "transition": "PreLoanSuccessState"
      }
    },
    {
      "name": "PreLoanSuccessState",
      "type": "operation",
      "actions": [
        {
          "functionRef": {
            "refName": "PreLoanSuccess",
            "arguments": {
              "userMap": "${ .userMap}",
              "entityRef": "${ .signal_data_pre_loan_submitted.entityRef }",
              "entityType": "${ .signal_data_pre_loan_submitted.entityType }",
              "loanApplicationID": "${ .signal_data_pre_loan_submitted.loanApplicationID }",
              "userLoanDetailsMap": "${.userLoanDetailsMap}"
            }
          }
        },
        {
          "retryRef": "no-retry",
          "functionRef": {
            "refName": "Mapper",
            "arguments": {
              "config": {
                "isSTPL": "if .userObj.dynamicUserInfoMap.segment == \"\" then true else false end",
                "isPrime": "if .userObj.dynamicUserInfoMap.segment == \"PRIME\" then true else false end",
                "isTopUP": "if .userObj.dynamicUserInfoMap.riskSegment == \"topUp\" then true else false end"
              }
            }
          }
        },
        {
          "functionRef": {
            "refName": "Mapper",
            "arguments": {
              "config": {
                "applicationReq": {
                  "type": "if .isPrime then \"PRIME\" elif .isTopUP then \"topUp\" elif .isSTPL then \"\" end",
                  "userID": ".userObj.userID",
                  "lenderID": "\"e023bd9e-46e2-4964-a8e7-d3e73e62421c\"",
                  "sourceEntityID": "\"e023bd9e-46e2-4964-a8e7-d3e73e62421c\""
                }
              }
            }
          }
        }
      ],
      "metadata": {
        "moduleNameTag": "PRELOAN",
        "subModuleNameTag": "WAIT"
      },
      "transition": "SetInitResendCounter"
    },
    {
      "name": "SetInitResendCounter",
      "type": "operation",
      "actions": [
        {
          "retryRef": "no-retry",
          "functionRef": {
            "refName": "Mapper",
            "arguments": {
              "config": {
                "totalAttempts": "0"
              }
            }
          }
        },
        {
          "retryRef": "no-retry",
          "functionRef": {
            "refName": "OutputMapperV2",
            "arguments": {
              "config": {
                "otpVerification": {
                  "errorMsg": "\" \"",
                  "putToHold": "false"
                }
              }
            }
          }
        }
      ],
      "metadata": {
        "moduleNameTag": "PRELOAN",
        "subModuleNameTag": "WAIT"
      },
      "transition": "SendOtp"
    },
    {
      "name": "SendOtp",
      "type": "event",
      "metadata": {
        "moduleNameTag": "PRELOAN",
        "subModuleNameTag": "EMAIL_VERIFICATION"
      },
      "onEvents": [
        {
          "eventRefs": [
            "send_otp"
          ]
        }
      ],
      "timeouts": {
        "eventTimeout": "PT24H"
      },
      "transition": "SetDefaultStatus"
    },
    {
      "name": "SetDefaultStatus",
      "type": "operation",
      "actions": [
        {
          "retryRef": "no-retry",
          "functionRef": {
            "refName": "Mapper",
            "arguments": {
              "config": {
                "email": ".signal_data_send_otp.email"
              }
            }
          }
        },
        {
          "retryRef": "no-retry",
          "functionRef": {
            "refName": "OutputMapperV2",
            "arguments": {
              "config": {
                "otpVerification": {}
              }
            }
          }
        }
      ],
      "metadata": {
        "moduleNameTag": "PRELOAN",
        "subModuleNameTag": "WAIT"
      },
      "transition": "GenerateOTP"
    },
    {
      "name": "GenerateOTP",
      "type": "operation",
      "actions": [
        {
          "retryRef": "RetryHTTP",
          "functionRef": {
            "refName": "GenerateOTPV4",
            "arguments": {
              "authID": "${.signal_data_send_otp.email}",
              "otpType": "pfl_personal_email_verification"
            }
          }
        }
      ],
      "metadata": {
        "moduleNameTag": "PRELOAN",
        "subModuleNameTag": "EMAIL_VERIFICATION"
      },
      "onErrors": [
        {
          "errorRef": "DefaultErrorRef",
          "transition": "SendEmailFailure"
        }
      ],
      "transition": "SendEmail"
    },
    {
      "name": "SendEmailFailure",
      "type": "operation",
      "actions": [
        {
          "retryRef": "no-retry",
          "functionRef": {
            "refName": "Mapper",
            "arguments": {
              "config": {
                "totalAttempts": "(.totalAttempts // 0) + 1"
              }
            }
          }
        },
        {
          "retryRef": "no-retry",
          "functionRef": {
            "refName": "OutputMapperV2",
            "arguments": {
              "config": {
                "otpVerification": {
                  "errorMsg": "\"Please enter a valid email address\"",
                  "putToHold": "false",
                  "statusText": "\"failure\""
                }
              }
            }
          }
        }
      ],
      "metadata": {
        "moduleNameTag": "PRELOAN",
        "subModuleNameTag": "EMAIL_VERIFICATION"
      },
      "transition": "SendOtpError"
    },
    {
      "name": "SendOtpError",
      "type": "switch",
      "metadata": {
        "moduleNameTag": "PRELOAN",
        "subModuleNameTag": "EMAIL_VERIFICATION"
      },
      "timeouts": {
        "eventTimeout": "PT24H"
      },
      "eventConditions": [
        {
          "eventRef": "send_otp",
          "transition": "SetDefaultStatus1"
        },
        {
          "eventRef": "edit_email",
          "transition": "HandleEmailEdit"
        },
        {
          "eventRef": "submit_otp",
          "transition": "HandleInvalidOtpSubmit"
        }
      ],
      "defaultCondition": {
        "transition": "SendOtpError"
      }
    },
    {
      "name": "HandleInvalidOtpSubmit",
      "type": "operation",
      "actions": [
        {
          "retryRef": "no-retry",
          "functionRef": {
            "refName": "Mapper",
            "arguments": {
              "config": {
                "totalAttempts": "(.totalAttempts // 0) + 1"
              }
            }
          }
        },
        {
          "retryRef": "no-retry",
          "functionRef": {
            "refName": "OutputMapperV2",
            "arguments": {
              "config": {
                "otpVerification": {
                  "errorMsg": "\"Please enter a valid email address\"",
                  "putToHold": "false",
                  "statusText": "\"failure\""
                }
              }
            }
          }
        }
      ],
      "metadata": {
        "moduleNameTag": "PRELOAN",
        "subModuleNameTag": "EMAIL_VERIFICATION"
      },
      "transition": "CheckTotalAttemptsAfterInvalidSubmit"
    },
    {
      "name": "CheckTotalAttemptsAfterInvalidSubmit",
      "type": "switch",
      "metadata": {
        "moduleNameTag": "PRELOAN",
        "subModuleNameTag": "WAIT"
      },
      "dataConditions": [
        {
          "condition": ".totalAttempts >= 4",
          "transition": "PutUserToHold"
        }
      ],
      "defaultCondition": {
        "transition": "SendOtpError"
      }
    },
    {
      "name": "SendEmail",
      "type": "operation",
      "actions": [
        {
          "functionRef": {
            "refName": "Mapper",
            "arguments": {
              "config": {
                "email": ".signal_data_send_otp.email",
                "emailConfig": {
                  "OTPCode": ".otp",
                  "ProductName": "\"Personal_Loan\"",
                  "CustomerName": ".userObj.name"
                }
              }
            }
          }
        },
        {
          "retryRef": "RetryHTTP",
          "functionRef": {
            "refName": "SendSMTPEmail",
            "arguments": {
              "host": "smtp.poonawallafincorp.com",
              "port": 587,
              "password": "",
              "username": "",
              "emailConfig": "${ .emailConfig }",
              "emailBodyKey": "otp_verification_email_body_pfl_personal_email",
              "senderEmailID": "<EMAIL>",
              "emailSubjectKey": "opt_verification_email_subject_pfl",
              "recipientEmailID": "${ .signal_data_send_otp.email}",
              "senderDisplayName": "PoonawallaFincorpLimited",
              "recipientDisplayName": "${ .userObj.name }"
            }
          }
        }
      ],
      "metadata": {
        "moduleNameTag": "PRELOAN",
        "subModuleNameTag": "WAIT"
      },
      "onErrors": [
        {
          "errorRef": "DefaultErrorRef",
          "transition": "SendEmailFailure"
        }
      ],
      "transition": "SendEmailSuccess"
    },
    {
      "name": "SendEmailSuccess",
      "type": "operation",
      "actions": [
        {
          "retryRef": "no-retry",
          "functionRef": {
            "refName": "OutputMapperV2",
            "arguments": {
              "config": {
                "otpVerification": {
                  "errorMsg": "\"OTP sent successfully\"",
                  "putToHold": "false",
                  "statusText": "\"send_email_success\""
                }
              }
            }
          }
        }
      ],
      "metadata": {
        "moduleNameTag": "PRELOAN",
        "subModuleNameTag": "EMAIL_VERIFICATION"
      },
      "transition": "SubmitOtp"
    },
    {
      "name": "SubmitOtp",
      "type": "switch",
      "metadata": {
        "moduleNameTag": "PRELOAN",
        "subModuleNameTag": "EMAIL_VERIFICATION"
      },
      "timeouts": {
        "eventTimeout": "PT24H"
      },
      "eventConditions": [
        {
          "eventRef": "edit_email",
          "transition": "HandleEmailEdit"
        },
        {
          "eventRef": "submit_otp",
          "transition": "VerifyOtp"
        },
        {
          "eventRef": "send_otp",
          "transition": "SetDefaultStatus1"
        }
      ],
      "defaultCondition": {
        "transition": "SubmitOtp"
      }
    },
    {
      "name": "SetDefaultStatus1",
      "type": "operation",
      "actions": [
        {
          "retryRef": "no-retry",
          "functionRef": {
            "refName": "Mapper",
            "arguments": {
              "config": {
                "email": ".signal_data_send_otp.email"
              }
            }
          }
        },
        {
          "retryRef": "no-retry",
          "functionRef": {
            "refName": "OutputMapperV2",
            "arguments": {
              "config": {
                "otpVerification": {}
              }
            }
          }
        }
      ],
      "metadata": {
        "moduleNameTag": "PRELOAN",
        "subModuleNameTag": "WAIT"
      },
      "transition": "IncreaseResendCounter"
    },
    {
      "name": "HandleEmailEdit",
      "type": "operation",
      "actions": [
        {
          "retryRef": "no-retry",
          "functionRef": {
            "refName": "ClearOTPAttemptsV1",
            "arguments": {
              "otpType": "pfl_personal_email_verification",
              "otpDataKey": "",
              "entityToClear": ".email"
            }
          }
        },
        {
          "retryRef": "no-retry",
          "functionRef": {
            "refName": "Mapper",
            "arguments": {
              "config": {
                "totalAttempts": "0"
              }
            }
          }
        },
        {
          "retryRef": "no-retry",
          "functionRef": {
            "refName": "OutputMapperV2",
            "arguments": {
              "config": {
                "otpVerification": {
                  "putToHold": "false",
                  "statusText": "\"\""
                }
              }
            }
          }
        }
      ],
      "metadata": {
        "moduleNameTag": "PRELOAN",
        "subModuleNameTag": "EMAIL_VERIFICATION"
      },
      "transition": "SetInitResendCounter"
    },
    {
      "name": "IncreaseResendCounter",
      "type": "operation",
      "actions": [
        {
          "retryRef": "no-retry",
          "functionRef": {
            "refName": "Mapper",
            "arguments": {
              "config": {
                "totalAttempts": "(.totalAttempts // 0) + 1"
              }
            }
          }
        }
      ],
      "metadata": {
        "moduleNameTag": "PRELOAN",
        "subModuleNameTag": "WAIT"
      },
      "transition": "CheckResendLimit"
    },
    {
      "name": "CheckResendLimit",
      "type": "switch",
      "metadata": {
        "moduleNameTag": "PRELOAN",
        "subModuleNameTag": "WAIT"
      },
      "dataConditions": [
        {
          "condition": ".totalAttempts >= 4",
          "transition": "PutUserToHold"
        }
      ],
      "defaultCondition": {
        "transition": "GenerateOTP"
      }
    },
    {
      "name": "PutUserToHold",
      "type": "operation",
      "actions": [
        {
          "retryRef": "no-retry",
          "functionRef": {
            "refName": "OutputMapperV2",
            "arguments": {
              "config": {
                "otpVerification": {
                  "poll": "true",
                  "errorMsg": "\"Number of attempts has been exceeded. Please try again after 2 minutes.\"",
                  "putToHold": "true",
                  "statusText": "\"failure\""
                }
              }
            }
          }
        }
      ],
      "metadata": {
        "moduleNameTag": "PRELOAN",
        "subModuleNameTag": "WAIT"
      },
      "transition": "PutUserTo2MWait"
    },
    {
      "name": "PutUserTo2MWait",
      "type": "sleep",
      "duration": "PT2M",
      "metadata": {
        "moduleNameTag": "PRELOAN",
        "subModuleNameTag": "EMAIL_VERIFICATION"
      },
      "transition": "SetInitResendCounter"
    },
    {
      "name": "VerifyOtp",
      "type": "operation",
      "actions": [
        {
          "retryRef": "no-retry",
          "functionRef": {
            "refName": "OutputMapperV2",
            "arguments": {
              "config": {
                "otpVerification": {}
              }
            }
          }
        },
        {
          "retryRef": "RetryHTTP",
          "functionRef": {
            "refName": "VerifyOTPV3",
            "arguments": {
              "mobile": "${ .signal_data_submit_otp.email }",
              "otpType": "pfl_personal_email_verification",
              "otpNumber": "${ .signal_data_submit_otp.otp_number }"
            }
          }
        }
      ],
      "metadata": {
        "moduleNameTag": "PRELOAN",
        "subModuleNameTag": "WAIT"
      },
      "transition": "VerifyCheck"
    },
    {
      "name": "VerifyOTPSuccess",
      "type": "operation",
      "actions": [
        {
          "retryRef": "no-retry",
          "functionRef": {
            "refName": "OutputMapperV2",
            "arguments": {
              "config": {
                "otpVerification": {
                  "errorMsg": "\".\"",
                  "putToHold": "false",
                  "statusText": "\"verify_otp_success\""
                }
              }
            }
          }
        }
      ],
      "metadata": {
        "moduleNameTag": "PRELOAN",
        "subModuleNameTag": "WAIT"
      },
      "transition": "OtpVerifiedActivity"
    },
    {
      "name": "VerifyOTPFailed",
      "type": "operation",
      "actions": [
        {
          "retryRef": "no-retry",
          "functionRef": {
            "refName": "Mapper",
            "arguments": {
              "config": {
                "totalAttempts": "(.totalAttempts // 0) + 1"
              }
            }
          }
        },
        {
          "retryRef": "no-retry",
          "functionRef": {
            "refName": "OutputMapperV2",
            "arguments": {
              "config": {
                "otpVerification": {
                  "errorMsg": "if .otpErrorMessage == \"OTP Expired\" then \"OTP has expired. Please request a new one.\" else .otpErrorMessage end",
                  "putToHold": "false",
                  "statusText": "\"failure\""
                }
              }
            }
          }
        }
      ],
      "metadata": {
        "moduleNameTag": "PRELOAN",
        "subModuleNameTag": "WAIT"
      },
      "transition": "CheckTotalAttemptsAfterVerifyFail"
    },
    {
      "name": "CheckTotalAttemptsAfterVerifyFail",
      "type": "switch",
      "metadata": {
        "moduleNameTag": "PRELOAN",
        "subModuleNameTag": "WAIT"
      },
      "dataConditions": [
        {
          "condition": ".totalAttempts >= 4",
          "transition": "PutUserToHold"
        }
      ],
      "defaultCondition": {
        "transition": "SubmitOtp"
      }
    },
    {
      "name": "VerifyCheck",
      "type": "switch",
      "metadata": {
        "moduleNameTag": "PRELOAN",
        "subModuleNameTag": "WAIT"
      },
      "dataConditions": [
        {
          "condition": ".otpVerified == true ",
          "transition": "VerifyOTPSuccess"
        },
        {
          "condition": ".otpErrorMessage == \"Max failed attempts reached, try again after sometime\"",
          "transition": "PutUserToHold"
        }
      ],
      "defaultCondition": {
        "transition": "VerifyOTPFailed"
      }
    },
    {
      "name": "OtpVerifiedActivity",
      "type": "operation",
      "actions": [
        {
          "functionRef": {
            "refName": "RegisterActivityEvent",
            "arguments": {
              "entityRef": "${.userObj.UserID}",
              "eventType": "personal_email_verified",
              "entityType": "system",
              "description": "personal email verified"
            }
          }
        }
      ],
      "metadata": {
        "moduleNameTag": "PRELOAN",
        "subModuleNameTag": "WAIT"
      },
      "transition": "AddEmailField"
    },
    {
      "name": "AddEmailField",
      "type": "operation",
      "actions": [
        {
          "retryRef": "no-retry",
          "functionRef": {
            "refName": "Mapper",
            "arguments": {
              "config": {
                "additionalFields": {
                  "verifiedPersonalEmail": ".signal_data_send_otp.email"
                }
              }
            }
          }
        },
        {
          "retryRef": "no-retry",
          "functionRef": {
            "refName": "AppendKeysToDynamicUserInfo",
            "arguments": {
              "additionalFields": "${ .additionalFields }"
            }
          }
        }
      ],
      "metadata": {
        "moduleNameTag": "PRELOAN",
        "subModuleNameTag": "WAIT"
      },
      "transition": "FinalSuccessState"
    },
    {
      "end": true,
      "name": "FinalSuccessState",
      "type": "operation",
      "actions": [
        {
          "retryRef": "no-retry",
          "functionRef": {
            "refName": "UpdateDynamicUserInfoV2",
            "arguments": {
              "userID": "${.userObj.UserID}",
              "dynamicUserInfo": {
                "userVerifiedPersonalEmail": "true"
              }
            }
          }
        },
        {
          "functionRef": {
            "refName": "LisaUpdateApplicationV2",
            "arguments": {
              "intent": "kyc_initiated",
              "applicationReq": "${.applicationReq}"
            }
          }
        }
      ],
      "metadata": {
        "moduleNameTag": "PRELOAN",
        "subModuleNameTag": "WAIT"
      }
    }
  ],
  "retries": [
    {
      "name": "no-retry",
      "delay": "PT1S",
      "multiplier": 1,
      "maxAttempts": 1
    },
    {
      "name": "polling-retry",
      "delay": "PT1S",
      "multiplier": 3,
      "maxAttempts": 7
    }
  ],
  "version": "1.1",
  "description": "Workflow for pre loan submission",
  "specVersion": "0.8"
}'
) ON CONFLICT (config_id)
DO UPDATE SET (
    source_entity_id,
    lender_id,
    organization_id,
    program_name,
    version,
    module_name,
    created_by,
    is_active,
    workflow_definition
) = (
    excluded.source_entity_id,
    excluded.lender_id,
    excluded.organization_id,
    excluded.program_name,
    excluded.version,
    excluded.module_name,
    excluded.created_by,
    excluded.is_active,
    excluded.workflow_definition
);