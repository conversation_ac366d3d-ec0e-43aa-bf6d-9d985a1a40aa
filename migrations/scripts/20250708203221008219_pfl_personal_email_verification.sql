----- <PERSON><PERSON><PERSON><PERSON> PERSONAL EMAIL VERIFICATION WORKFLOW CONFIGURATION -----
UPDATE workflow_config SET is_active = false
WHERE
    module_name = 'PRELOAN'
    AND source_entity_id = 'e023bd9e-46e2-4964-a8e7-d3e73e62421c'
    AND is_active = true;

INSERT INTO
workflow_config (
    config_id,
    source_entity_id,
    lender_id,
    organization_id,
    program_name,
    version,
    module_name,
    created_by,
    is_active,
    workflow_definition
) VALUES (
    uuid_generate_v4(),
    'e023bd9e-46e2-4964-a8e7-d3e73e62421c',
    'e023bd9e-46e2-4964-a8e7-d3e73e62421c',
    'db5fcba9-70cb-42b3-aaf1-d0afc0903e79',
    'PERSONAL_LOAN',
    7,
    'PRELOAN',
    'ADMIN',
    true,
    '{
  "retries": [
    {
      "delay": "PT1S",
      "maxAttempts": 1,
      "name": "no-retry",
      "multiplier": 1
    },
    {
      "delay": "PT1S",
      "maxAttempts": 7,
      "name": "polling-retry",
      "multiplier": 3
    }
  ],
  "name": "PFL Pre Loan",
  "states": [
    {
      "transition": "preloan-submit",
      "type": "operation",
      "name": "prime-check",
      "actions": [
        {
          "retryRef": "no-retry",
          "functionRef": {
            "refName": "OutputMapperV2",
            "arguments": {
              "config": {
                "disableResend": {
                  "redirect": "false",
                  "disable": "false"
                },
                "isEpfoVerified": "if .userObj.dynamicUserInfoMap.epfoValidationStatus == \"verified\" then true else false end",
                "isPrime": "if .userObj.dynamicUserInfoMap.segment == \"PRIME\" then true else false end",
                "reject": "false"
              }
            }
          }
        },
        {
          "retryRef": "no-retry",
          "functionRef": {
            "refName": "OutputMapperV2",
            "arguments": {
              "config": {
                "otpVerification": {}
              }
            }
          }
        }
      ],
      "metadata": {
        "subModuleNameTag": "PRE_LOAN_DETAILS",
        "moduleNameTag": "PRELOAN"
      }
    },
    {
      "onEvents": [
        {
          "eventRefs": [
            "pre_loan_submitted"
          ]
        }
      ],
      "transition": "ValidatePreLoan",
      "type": "event",
      "name": "preloan-submit",
      "metadata": {
        "subModuleNameTag": "PRE_LOAN_DETAILS",
        "moduleNameTag": "PRELOAN"
      }
    },
    {
      "transition": "ErrorCheckForPreLoan",
      "type": "operation",
      "name": "ValidatePreLoan",
      "actions": [
        {
          "functionRef": {
            "refName": "ValidatePreLoan",
            "arguments": {
              "preLoanData": "${ .signal_data_pre_loan_submitted.preLoanData }",
              "loanApplicationID": "${ .signal_data_pre_loan_submitted.loanApplicationID }"
            }
          }
        }
      ],
      "metadata": {
        "subModuleNameTag": "WAIT",
        "moduleNameTag": "PRELOAN"
      }
    },
    {
      "dataConditions": [
        {
          "transition": "preloan-submit",
          "condition": ".isError == true "
        }
      ],
      "defaultCondition": {
        "transition": "PreLoanSuccessState"
      },
      "type": "switch",
      "name": "ErrorCheckForPreLoan",
      "metadata": {
        "subModuleNameTag": "WAIT",
        "moduleNameTag": "PRELOAN"
      }
    },
    {
      "transition": "SetInitResendCounter",
      "type": "operation",
      "name": "PreLoanSuccessState",
      "actions": [
        {
          "functionRef": {
            "refName": "PreLoanSuccess",
            "arguments": {
              "loanApplicationID": "${ .signal_data_pre_loan_submitted.loanApplicationID }",
              "entityType": "${ .signal_data_pre_loan_submitted.entityType }",
              "userLoanDetailsMap": "${.userLoanDetailsMap}",
              "userMap": "${ .userMap}",
              "entityRef": "${ .signal_data_pre_loan_submitted.entityRef }"
            }
          }
        },
        {
          "retryRef": "no-retry",
          "functionRef": {
            "refName": "Mapper",
            "arguments": {
              "config": {
                "isPrime": "if .userObj.dynamicUserInfoMap.segment == \"PRIME\" then true else false end",
                "isTopUP": "if .userObj.dynamicUserInfoMap.riskSegment == \"topUp\" then true else false end",
                "isSTPL": "if .userObj.dynamicUserInfoMap.segment == \"\" then true else false end"
              }
            }
          }
        },
        {
          "functionRef": {
            "refName": "Mapper",
            "arguments": {
              "config": {
                "applicationReq": {
                  "type": "if .isPrime then \"PRIME\" elif .isTopUP then \"topUp\" elif .isSTPL then \"\" end",
                  "sourceEntityID": "\"e023bd9e-46e2-4964-a8e7-d3e73e62421c\"",
                  "userID": ".userObj.userID",
                  "lenderID": "\"e023bd9e-46e2-4964-a8e7-d3e73e62421c\""
                }
              }
            }
          }
        }
      ],
      "metadata": {
        "subModuleNameTag": "WAIT",
        "moduleNameTag": "PRELOAN"
      }
    },
    {
      "transition": "SendOtp",
      "type": "operation",
      "name": "SetInitResendCounter",
      "actions": [
        {
          "retryRef": "no-retry",
          "functionRef": {
            "refName": "Mapper",
            "arguments": {
              "config": {
                "totalAttempts": "0"
              }
            }
          }
        },
        {
          "retryRef": "no-retry",
          "functionRef": {
            "refName": "OutputMapperV2",
            "arguments": {
              "config": {
                "otpVerification": {
                  "errorMsg": "\" \"",
                  "putToHold": "false"
                }
              }
            }
          }
        }
      ],
      "metadata": {
        "subModuleNameTag": "WAIT",
        "moduleNameTag": "PRELOAN"
      }
    },
    {
      "name": "SendOtp",
      "onEvents": [
        {
          "eventRefs": [
            "send_otp"
          ]
        }
      ],
      "transition": "SetDefaultStatus",
      "timeouts": {
        "eventTimeout": "PT24H"
      },
      "type": "event",
      "metadata": {
        "subModuleNameTag": "EMAIL_VERIFICATION",
        "moduleNameTag": "PRELOAN"
      }
    },
    {
      "transition": "GenerateOTP",
      "type": "operation",
      "name": "SetDefaultStatus",
      "actions": [
        {
          "retryRef": "no-retry",
          "functionRef": {
            "refName": "Mapper",
            "arguments": {
              "config": {
                "email": ".signal_data_send_otp.email"
              }
            }
          }
        },
        {
          "retryRef": "no-retry",
          "functionRef": {
            "refName": "OutputMapperV2",
            "arguments": {
              "config": {
                "otpVerification": {}
              }
            }
          }
        }
      ],
      "metadata": {
        "subModuleNameTag": "WAIT",
        "moduleNameTag": "PRELOAN"
      }
    },
    {
      "name": "GenerateOTP",
      "transition": "SendEmail",
      "actions": [
        {
          "retryRef": "RetryHTTP",
          "functionRef": {
            "refName": "GenerateOTPV4",
            "arguments": {
              "otpType": "pfl_personal_email_verification",
              "authID": "${.signal_data_send_otp.email}"
            }
          }
        }
      ],
      "onErrors": [
        {
          "transition": "SendEmailFailure",
          "errorRef": "DefaultErrorRef"
        }
      ],
      "type": "operation",
      "metadata": {
        "subModuleNameTag": "EMAIL_VERIFICATION",
        "moduleNameTag": "PRELOAN"
      }
    },
    {
      "transition": "SendOtpError",
      "type": "operation",
      "name": "SendEmailFailure",
      "actions": [
        {
          "retryRef": "no-retry",
          "functionRef": {
            "refName": "Mapper",
            "arguments": {
              "config": {
                "totalAttempts": "(.totalAttempts // 0) + 1"
              }
            }
          }
        },
        {
          "retryRef": "no-retry",
          "functionRef": {
            "refName": "OutputMapperV2",
            "arguments": {
              "config": {
                "otpVerification": {
                  "errorMsg": "\"Please enter a valid email address\"",
                  "putToHold": "false",
                  "statusText": "\"failure\""
                }
              }
            }
          }
        }
      ],
      "metadata": {
        "subModuleNameTag": "EMAIL_VERIFICATION",
        "moduleNameTag": "PRELOAN"
      }
    },
    {
      "eventConditions": [
        {
          "transition": "SetDefaultStatus1",
          "eventRef": "send_otp"
        },
        {
          "transition": "HandleEmailEdit",
          "eventRef": "edit_email"
        },
        {
          "transition": "HandleInvalidOtpSubmit",
          "eventRef": "submit_otp"
        }
      ],
      "defaultCondition": {
        "transition": "SendOtpError"
      },
      "name": "SendOtpError",
      "timeouts": {
        "eventTimeout": "PT24H"
      },
      "type": "switch",
      "metadata": {
        "subModuleNameTag": "EMAIL_VERIFICATION",
        "moduleNameTag": "PRELOAN"
      }
    },
    {
      "transition": "CheckTotalAttemptsAfterInvalidSubmit",
      "type": "operation",
      "name": "HandleInvalidOtpSubmit",
      "actions": [
        {
          "retryRef": "no-retry",
          "functionRef": {
            "refName": "Mapper",
            "arguments": {
              "config": {
                "totalAttempts": "(.totalAttempts // 0) + 1"
              }
            }
          }
        },
        {
          "retryRef": "no-retry",
          "functionRef": {
            "refName": "OutputMapperV2",
            "arguments": {
              "config": {
                "otpVerification": {
                  "errorMsg": "\"Please enter a valid email address\"",
                  "putToHold": "false",
                  "statusText": "\"failure\""
                }
              }
            }
          }
        }
      ],
      "metadata": {
        "subModuleNameTag": "EMAIL_VERIFICATION",
        "moduleNameTag": "PRELOAN"
      }
    },
    {
      "dataConditions": [
        {
          "transition": "PutUserToHold",
          "condition": ".totalAttempts >= 4"
        }
      ],
      "defaultCondition": {
        "transition": "SendOtpError"
      },
      "type": "switch",
      "name": "CheckTotalAttemptsAfterInvalidSubmit",
      "metadata": {
        "subModuleNameTag": "WAIT",
        "moduleNameTag": "PRELOAN"
      }
    },
    {
      "name": "SendEmail",
      "transition": "SendEmailSuccess",
      "actions": [
        {
          "functionRef": {
            "refName": "Mapper",
            "arguments": {
              "config": {
                "emailConfig": {
                  "OTPCode": ".otp",
                  "CustomerName": ".userObj.name",
                  "ProductName": "\"Personal_Loan\""
                },
                "email": ".signal_data_send_otp.email"
              }
            }
          }
        },
        {
          "retryRef": "RetryHTTP",
          "functionRef": {
            "refName": "SendSMTPEmail",
            "arguments": {
              "username": "",
              "recipientEmailID": "${ .signal_data_send_otp.email}",
              "emailConfig": "${ .emailConfig }",
              "emailSubjectKey": "opt_verification_email_subject_pfl",
              "senderEmailID": "<EMAIL>",
              "port": 587,
              "recipientDisplayName": "${ .userObj.name }",
              "host": "smtp.poonawallafincorp.com",
              "password": "",
              "emailBodyKey": "otp_verification_email_body_pfl_personal_email",
              "senderDisplayName": "PoonawallaFincorpLimited"
            }
          }
        }
      ],
      "onErrors": [
        {
          "transition": "SendEmailFailure",
          "errorRef": "DefaultErrorRef"
        }
      ],
      "type": "operation",
      "metadata": {
        "subModuleNameTag": "WAIT",
        "moduleNameTag": "PRELOAN"
      }
    },
    {
      "transition": "SubmitOtp",
      "type": "operation",
      "name": "SendEmailSuccess",
      "actions": [
        {
          "retryRef": "no-retry",
          "functionRef": {
            "refName": "OutputMapperV2",
            "arguments": {
              "config": {
                "otpVerification": {
                  "errorMsg": "\"OTP sent successfully\"",
                  "putToHold": "false",
                  "statusText": "\"send_email_success\""
                }
              }
            }
          }
        }
      ],
      "metadata": {
        "subModuleNameTag": "EMAIL_VERIFICATION",
        "moduleNameTag": "PRELOAN"
      }
    },
    {
      "eventConditions": [
        {
          "transition": "HandleEmailEdit",
          "eventRef": "edit_email"
        },
        {
          "transition": "VerifyOtp",
          "eventRef": "submit_otp"
        },
        {
          "transition": "SetDefaultStatus1",
          "eventRef": "send_otp"
        }
      ],
      "defaultCondition": {
        "transition": "SubmitOtp"
      },
      "name": "SubmitOtp",
      "timeouts": {
        "eventTimeout": "PT24H"
      },
      "type": "switch",
      "metadata": {
        "subModuleNameTag": "EMAIL_VERIFICATION",
        "moduleNameTag": "PRELOAN"
      }
    },
    {
      "transition": "IncreaseResendCounter",
      "type": "operation",
      "name": "SetDefaultStatus1",
      "actions": [
        {
          "retryRef": "no-retry",
          "functionRef": {
            "refName": "Mapper",
            "arguments": {
              "config": {
                "email": ".signal_data_send_otp.email"
              }
            }
          }
        },
        {
          "retryRef": "no-retry",
          "functionRef": {
            "refName": "OutputMapperV2",
            "arguments": {
              "config": {
                "otpVerification": {}
              }
            }
          }
        }
      ],
      "metadata": {
        "subModuleNameTag": "WAIT",
        "moduleNameTag": "PRELOAN"
      }
    },
    {
      "transition": "SetInitResendCounter",
      "type": "operation",
      "name": "HandleEmailEdit",
      "actions": [
        {
          "retryRef": "no-retry",
          "functionRef": {
            "refName": "ClearOTPAttemptsV1",
            "arguments": {
              "otpDataKey": "",
              "otpType": "pfl_personal_email_verification",
              "emailToClear": ".email"
            }
          }
        },
        {
          "retryRef": "no-retry",
          "functionRef": {
            "refName": "Mapper",
            "arguments": {
              "config": {
                "totalAttempts": "0"
              }
            }
          }
        },
        {
          "retryRef": "no-retry",
          "functionRef": {
            "refName": "OutputMapperV2",
            "arguments": {
              "config": {
                "otpVerification": {
                  "statusText": "\"\"",
                  "putToHold": "false"
                }
              }
            }
          }
        }
      ],
      "metadata": {
        "subModuleNameTag": "EMAIL_VERIFICATION",
        "moduleNameTag": "PRELOAN"
      }
    },
    {
      "transition": "CheckResendLimit",
      "type": "operation",
      "name": "IncreaseResendCounter",
      "actions": [
        {
          "retryRef": "no-retry",
          "functionRef": {
            "refName": "Mapper",
            "arguments": {
              "config": {
                "totalAttempts": "(.totalAttempts // 0) + 1"
              }
            }
          }
        }
      ],
      "metadata": {
        "subModuleNameTag": "WAIT",
        "moduleNameTag": "PRELOAN"
      }
    },
    {
      "dataConditions": [
        {
          "transition": "PutUserToHold",
          "condition": ".totalAttempts >= 4"
        }
      ],
      "defaultCondition": {
        "transition": "GenerateOTP"
      },
      "type": "switch",
      "name": "CheckResendLimit",
      "metadata": {
        "subModuleNameTag": "WAIT",
        "moduleNameTag": "PRELOAN"
      }
    },
    {
      "transition": "PutUserTo2MWait",
      "type": "operation",
      "name": "PutUserToHold",
      "actions": [
        {
          "retryRef": "no-retry",
          "functionRef": {
            "refName": "OutputMapperV2",
            "arguments": {
              "config": {
                "otpVerification": {
                  "errorMsg": "\"Number of attempts has been exceeded. Please try again after 2 minutes.\"",
                  "poll": "true",
                  "putToHold": "true",
                  "statusText": "\"failure\""
                }
              }
            }
          }
        }
      ],
      "metadata": {
        "subModuleNameTag": "WAIT",
        "moduleNameTag": "PRELOAN"
      }
    },
    {
      "duration": "PT2M",
      "transition": "SetInitResendCounter",
      "type": "sleep",
      "name": "PutUserTo2MWait",
      "metadata": {
        "subModuleNameTag": "EMAIL_VERIFICATION",
        "moduleNameTag": "PRELOAN"
      }
    },
    {
      "transition": "VerifyCheck",
      "type": "operation",
      "name": "VerifyOtp",
      "actions": [
        {
          "retryRef": "no-retry",
          "functionRef": {
            "refName": "OutputMapperV2",
            "arguments": {
              "config": {
                "otpVerification": {}
              }
            }
          }
        },
        {
          "retryRef": "RetryHTTP",
          "functionRef": {
            "refName": "VerifyOTPV3",
            "arguments": {
              "mobile": "${ .signal_data_submit_otp.email }",
              "otpType": "pfl_personal_email_verification",
              "otpNumber": "${ .signal_data_submit_otp.otp_number }"
            }
          }
        }
      ],
      "metadata": {
        "subModuleNameTag": "WAIT",
        "moduleNameTag": "PRELOAN"
      }
    },
    {
      "transition": "OtpVerifiedActivity",
      "type": "operation",
      "name": "VerifyOTPSuccess",
      "actions": [
        {
          "retryRef": "no-retry",
          "functionRef": {
            "refName": "OutputMapperV2",
            "arguments": {
              "config": {
                "otpVerification": {
                  "errorMsg": "\".\"",
                  "putToHold": "false",
                  "statusText": "\"verify_otp_success\""
                }
              }
            }
          }
        }
      ],
      "metadata": {
        "subModuleNameTag": "WAIT",
        "moduleNameTag": "PRELOAN"
      }
    },
    {
      "transition": "CheckTotalAttemptsAfterVerifyFail",
      "type": "operation",
      "name": "VerifyOTPFailed",
      "actions": [
        {
          "retryRef": "no-retry",
          "functionRef": {
            "refName": "Mapper",
            "arguments": {
              "config": {
                "totalAttempts": "(.totalAttempts // 0) + 1"
              }
            }
          }
        },
        {
          "retryRef": "no-retry",
          "functionRef": {
            "refName": "OutputMapperV2",
            "arguments": {
              "config": {
                "otpVerification": {
                  "errorMsg": "if .otpErrorMessage == \"OTP Expired\" then \"OTP has expired. Please request a new one.\" else .otpErrorMessage end",
                  "putToHold": "false",
                  "statusText": "\"failure\""
                }
              }
            }
          }
        }
      ],
      "metadata": {
        "subModuleNameTag": "WAIT",
        "moduleNameTag": "PRELOAN"
      }
    },
    {
      "dataConditions": [
        {
          "transition": "PutUserToHold",
          "condition": ".totalAttempts >= 4"
        }
      ],
      "defaultCondition": {
        "transition": "SubmitOtp"
      },
      "type": "switch",
      "name": "CheckTotalAttemptsAfterVerifyFail",
      "metadata": {
        "subModuleNameTag": "WAIT",
        "moduleNameTag": "PRELOAN"
      }
    },
    {
      "dataConditions": [
        {
          "transition": "VerifyOTPSuccess",
          "condition": ".otpVerified == true "
        },
        {
          "transition": "PutUserToHold",
          "condition": ".otpErrorMessage == \"Max failed attempts reached, try again after sometime\""
        }
      ],
      "defaultCondition": {
        "transition": "VerifyOTPFailed"
      },
      "type": "switch",
      "name": "VerifyCheck",
      "metadata": {
        "subModuleNameTag": "WAIT",
        "moduleNameTag": "PRELOAN"
      }
    },
    {
      "transition": "AddEmailField",
      "type": "operation",
      "name": "OtpVerifiedActivity",
      "actions": [
        {
          "functionRef": {
            "refName": "RegisterActivityEvent",
            "arguments": {
              "eventType": "personal_email_verified",
              "entityType": "system",
              "entityRef": "${.userObj.UserID}",
              "description": "personal email verified"
            }
          }
        }
      ],
      "metadata": {
        "subModuleNameTag": "WAIT",
        "moduleNameTag": "PRELOAN"
      }
    },
    {
      "transition": "FinalSuccessState",
      "type": "operation",
      "name": "AddEmailField",
      "actions": [
        {
          "retryRef": "no-retry",
          "functionRef": {
            "refName": "Mapper",
            "arguments": {
              "config": {
                "additionalFields": {
                  "verifiedPersonalEmail": ".signal_data_send_otp.email"
                }
              }
            }
          }
        },
        {
          "retryRef": "no-retry",
          "functionRef": {
            "refName": "AppendKeysToDynamicUserInfo",
            "arguments": {
              "additionalFields": "${ .additionalFields }"
            }
          }
        }
      ],
      "metadata": {
        "subModuleNameTag": "WAIT",
        "moduleNameTag": "PRELOAN"
      }
    },
    {
      "end": true,
      "type": "operation",
      "name": "FinalSuccessState",
      "actions": [
        {
          "retryRef": "no-retry",
          "functionRef": {
            "refName": "UpdateDynamicUserInfoV2",
            "arguments": {
              "userID": "${.userObj.UserID}",
              "dynamicUserInfo": {
                "userVerifiedPersonalEmail": "true"
              }
            }
          }
        },
        {
          "functionRef": {
            "refName": "LisaUpdateApplicationV2",
            "arguments": {
              "intent": "kyc_initiated",
              "applicationReq": "${.applicationReq}"
            }
          }
        }
      ],
      "metadata": {
        "subModuleNameTag": "WAIT",
        "moduleNameTag": "PRELOAN"
      }
    }
  ],
  "start": "prime-check",
  "version": "1.1",
  "specVersion": "0.8",
  "id": "c03fe29c-7aee-42da-89de-ee38deccd366",
  "description": "Workflow for pre loan submission"
}'
) ON CONFLICT (config_id)
DO UPDATE SET (
    source_entity_id,
    lender_id,
    organization_id,
    program_name,
    version,
    module_name,
    created_by,
    is_active,
    workflow_definition
) = (
    excluded.source_entity_id,
    excluded.lender_id,
    excluded.organization_id,
    excluded.program_name,
    excluded.version,
    excluded.module_name,
    excluded.created_by,
    excluded.is_active,
    excluded.workflow_definition
);

---- PRELOAN PERSONAL EMAIL VERIFICATION SECTION CONFIGURATION ----
UPDATE section_config SET is_active = false
WHERE
    source_entity_id = 'e023bd9e-46e2-4964-a8e7-d3e73e62421c'
    AND module_name = 'PRELOAN'
    AND sub_module_name = 'EMAIL_VERIFICATION'
    AND is_active = true;

INSERT INTO section_config (
    id,
    source_entity_id,
    module_name,
    sub_module_name,
    created_at,
    created_by,
    is_active,
    program_name,
    organization_id,
    version,
    section_data
) VALUES (
    uuid_generate_v4(),
    'e023bd9e-46e2-4964-a8e7-d3e73e62421c',
    'PRELOAN',
    'EMAIL_VERIFICATION',
    now(),
    'ADMIN',
    true,
    'PERSONAL_LOAN',
    'db5fcba9-70cb-42b3-aaf1-d0afc0903e79',
    2,
    '{
  "pollKey": "emailVerification",
  "sections": [
    {
      "onLoad": [
        {
          "url": "/loan/getWorkflowKey?key=otpVerification&moduleName=${currentModule}",
          "type": "get",
          "initialDelay": 2000,
          "key": "otpVerification",
          "sequence": 0
        }
      ],
      "key": "PRE_LOAN_INFO",
      "title": "Tell us more!",
      "calculatedOfferVariables": [
        {
          "key": "disableSendOtpButton",
          "arguments": [],
          "value": "''${submitOtpApi.msg}''===''workflow signalled successfully''? true : false",
          "sequence": 0
        }
      ],
      "sequence": 0,
      "components": [
        {
          "fields": [
            {
              "hideCollapse": true,
              "key": "additionalInfo",
              "containerStyle": {
                "marginTop": "1rem"
              },
              "title": "",
              "hideTitle": true,
              "components": [
                {
                  "fields": [
                    {
                      "inputRegex": "^$|^(?!.*[+@._-]{2})(?!.*\\.\\.)(?!.*@@)[a-zA-Z0-9][a-zA-Z0-9+@._-]*$",
                      "type": "input",
                      "value": "${user.email}",
                      "label": "Enter your personal email",
                      "readOnly": "fn(${dynamicFunctionResponses.disableSendOtpButton} || ''${otpVerification.putToHold}'' === ''true'' ? true : false || ''${verifyOtpApi.putToHold}'' === ''true'' ? true : false)",
                      "key": "email",
                      "validation": {
                        "type": "regex",
                        "required": true,
                        "params": [
                          "^[\\w.%+-]+@(?:[\\w-]+\\.)+[\\w]{2,}$"
                        ],
                        "errorText": "Enter a valid email id",
                        "maxLength": 200
                      },
                      "placeholder": "Enter your Personal Email id",
                      "inputType": "text"
                    },
                    {
                      "inputType": "buttonGroup",
                      "containerStyle": {
                        "marginTop": "1rem"
                      },
                      "buttonMap": [
                        {
                          "containerStyle": {
                            "marginTop": "1rem"
                          },
                          "reloadDynamicFunctions": true,
                          "buttonType": "primaryButton",
                          "label": "Get otp",
                          "disabled": "fn(${dynamicFunctionResponses.disableSendOtpButton} || ''${otpVerification.putToHold}'' === ''true'' ? true : false || ''${verifyOtpApi.putToHold}'' === ''true'' ? true : false)",
                          "key": "submit",
                          "action": [
                            {
                              "requestPayload": {
                                "moduleName": "${currentModule}",
                                "data": {
                                  "email": "fn(${pre_loan_form.email}.toLowerCase())"
                                },
                                "signalName": "send_otp"
                              },
                              "sequence": 0,
                              "url": "/loan/signalWorkflow",
                              "navigate": false,
                              "key": "submitOtpApi",
                              "type": "post"
                            }
                          ],
                          "type": "submit"
                        },
                        {
                          "containerStyle": {
                            "marginTop": "1rem"
                          },
                          "isValidationOptional": true,
                          "buttonType": "secondaryButton",
                          "label": "Edit email",
                          "disabled": "fn(!${dynamicFunctionResponses.disableSendOtpButton} || ''${otpVerification.putToHold}'' === ''true'' ? true : false || ''${verifyOtpApi.putToHold}'' === ''true'' ? true : false)",
                          "key": "openEditPan",
                          "action": [
                            {
                              "requestPayload": {
                                "moduleName": "${currentModule}",
                                "signalName": "edit_email"
                              },
                              "sequence": 0,
                              "url": "/loan/signalWorkflow",
                              "navigate": {
                                "url": "",
                                "type": "reload"
                              },
                              "key": "submitOtpApi",
                              "type": "post"
                            }
                          ],
                          "type": "submit"
                        }
                      ]
                    },
                    {
                      "key": "otpVerify",
                      "timerDuration": 15,
                      "reloadDynamicFunctions": true,
                      "title": "OTP sent to ${pre_loan_form.email}",
                      "disabled": "fn(''${resendOtpVerification.putToHold}'' === ''true'' ? true : false || ''${otpVerification.putToHold}'' === ''true'' ? true : false || ''${verifyOtpApi.putToHold}'' === ''true'' ? true : false)",
                      "isAutoSubmit": true,
                      "dynamicCondition": {
                        "type": "and",
                        "conditions": [
                          {
                            "is": "workflow signalled successfully",
                            "when": "submitOtpApi.msg"
                          }
                        ]
                      },
                      "maxLength": 6,
                      "validation": {
                        "required": true
                      },
                      "inputType": "otp",
                      "resend": {
                        "validationOptional": true,
                        "key": "otpVerify",
                        "reloadDynamicFunctions": true,
                        "timeOut": 15,
                        "action": [
                          {
                            "url": "/loan/signalWorkflow",
                            "navigate": false,
                            "type": "post",
                            "requestPayload": {
                              "moduleName": "${currentModule}",
                              "data": {
                                "email": "${pre_loan_form.email}"
                              },
                              "signalName": "send_otp"
                            },
                            "sequence": 0
                          },
                          {
                            "retries": {
                              "delay": 3000,
                              "max": 3
                            },
                            "sequence": 1,
                            "url": "/loan/getWorkflowKey?key=otpVerification&moduleName=${currentModule}",
                            "initialDelay": 1000,
                            "expectedResponse": {
                              "responseDataPath": "statusText",
                              "errorMessagePath": "",
                              "expectedValues": [
                                "send_email_success",
                                "verify_otp_success",
                                "failure"
                              ]
                            },
                            "key": "verifyOtpApi",
                            "type": "get"
                          }
                        ],
                        "type": "submit"
                      }
                    }
                  ],
                  "type": "form",
                  "key": "pre_loan_form"
                }
              ],
              "open": true,
              "inputType": "accordion"
            },
            {
              "key": "errorText",
              "containerStyle": {
                "margin": "1rem 0",
                "textAlign": "center"
              },
              "label": "fn(''${verifyOtpApi.errorMsg}'')",
              "dynamicCondition": {
                "type": "and",
                "conditions": [
                  {
                    "notEqualsLength": 0,
                    "when": "verifyOtpApi.errorMsg"
                  }
                ]
              },
              "data": {
                "style": {
                  "color": "red",
                  "fontSize": "1rem"
                },
                "face": "SubText"
              },
              "inputType": "typography"
            },
            {
              "key": "errorText",
              "containerStyle": {
                "margin": "1rem 0",
                "textAlign": "center"
              },
              "label": "fn(''${otpVerification.errorMsg}'')",
              "dynamicCondition": {
                "type": "and",
                "conditions": [
                  {
                    "is": true,
                    "when": "otpVerification.putToHold"
                  }
                ]
              },
              "data": {
                "style": {
                  "color": "red",
                  "fontSize": "1rem"
                },
                "face": "SubText"
              },
              "inputType": "typography"
            },
            {
              "validationOptional": true,
              "dynamicCondition": {
                "type": "or",
                "conditions": [
                  {
                    "is": true,
                    "when": "otpVerification.putToHold"
                  },
                  {
                    "is": true,
                    "when": "verifyOtpApi.putToHold"
                  }
                ]
              },
              "containerStyle": {
                "marginTop": "1rem",
                "marginBottom": "1rem"
              },
              "label": "Reload",
              "disabled": "",
              "key": "submit",
              "action": [
                {
                  "sequence": 0,
                  "url": "/user/getModules?sdkVersion=${context.sdkVersion}",
                  "navigate": {
                    "url": "/",
                    "type": "module"
                  },
                  "initialDelay": 2000,
                  "key": "getModules",
                  "type": "get"
                }
              ],
              "type": "reload",
              "inputType": "primaryButton"
            },
            {
              "containerStyle": {
                "marginTop": "auto"
              },
              "label": "Submit OTP",
              "disabled": "fn(''${otpVerification.putToHold}'' === ''true'' ? true : false || ''${verifyOtpApi.putToHold}'' === ''true'' ? true : false || ''${pre_loan_form.otpVerify}''.length === 6 ? false : true)",
              "key": "submit",
              "action": [
                {
                  "url": "/loan/signalWorkflow",
                  "navigate": false,
                  "type": "post",
                  "requestPayload": {
                    "moduleName": "${currentModule}",
                    "data": {
                      "email": "${pre_loan_form.email}",
                      "otp_number": "fn(Number(${pre_loan_form.otpVerify}))"
                    },
                    "signalName": "submit_otp"
                  },
                  "sequence": 0
                },
                {
                  "retries": {
                    "delay": 3000,
                    "max": 3
                  },
                  "sequence": 1,
                  "url": "/loan/getWorkflowKey?key=otpVerification&moduleName=${currentModule}",
                  "navigate": false,
                  "initialDelay": 1000,
                  "expectedResponse": {
                    "responseDataPath": "statusText",
                    "errorMessagePath": "",
                    "expectedValues": [
                      "send_email_success",
                      "verify_otp_success",
                      "failure"
                    ]
                  },
                  "key": "verifyOtpApi",
                  "type": "get"
                },
                {
                  "url": "/user/getModules?sdkVersion=${context.sdkVersion}",
                  "navigate": {
                    "url": "/",
                    "type": "reload"
                  },
                  "dependencyOnApi": {
                    "key": "${verifyOtpApi.statusText}",
                    "value": "verify_otp_success"
                  },
                  "type": "get",
                  "sequence": 2
                }
              ],
              "type": "submit",
              "inputType": "primaryButton"
            }
          ],
          "type": "form",
          "key": "pre_loan_form"
        }
      ],
      "type": "page"
    }
  ],
  "key": "EMAIL_VERIFICATION"
}'
) ON CONFLICT (id)
DO UPDATE SET (
    source_entity_id,
    module_name,
    sub_module_name,
    created_by,
    is_active,
    section_data,
    updated_at
) = (
    excluded.source_entity_id,
    excluded.module_name,
    excluded.sub_module_name,
    excluded.created_by,
    excluded.is_active,
    excluded.section_data,
    now()
);


---- PRELOAN DETAILS SECTION CONFIGURATION ----
UPDATE section_config SET is_active = false
WHERE
    source_entity_id = 'e023bd9e-46e2-4964-a8e7-d3e73e62421c'
    AND module_name = 'PRELOAN'
    AND sub_module_name = 'PRE_LOAN_DETAILS'
    AND is_active = true;

INSERT INTO section_config (
    id,
    source_entity_id,
    module_name,
    sub_module_name,
    created_at,
    created_by,
    is_active,
    program_name,
    organization_id,
    version,
    section_data
) VALUES (
    uuid_generate_v4(),
    'e023bd9e-46e2-4964-a8e7-d3e73e62421c',
    'PRELOAN',
    'PRE_LOAN_DETAILS',
    now(),
    'ADMIN',
    true,
    'PERSONAL_LOAN',
    'db5fcba9-70cb-42b3-aaf1-d0afc0903e79',
    4,
    '{
  "configID": "20a31fc7-f6ff-4104-b120-4defac856b97",
  "sections": [
    {
      "onLoad": [
        {
          "url": "/loan/getWorkflowKey?key=isPrime&moduleName=${currentModule}",
          "type": "get",
          "key": "getPrimeKey",
          "sequence": 0
        }
      ],
      "key": "PRE_LOAN_INFO",
      "title": "Tell us more!",
      "sequence": 0,
      "components": [
        {
          "fields": [
            {
              "hideCollapse": true,
              "key": "additionalInfo",
              "containerStyle": {
                "marginTop": "1rem"
              },
              "title": "Additional information",
              "hideTitle": true,
              "components": [
                {
                  "fields": [
                    {
                      "containerStyle": {
                        "marginTop": "-1rem"
                      },
                      "dataType": "text",
                      "label": "Loan Reason",
                      "key": "loanPurpose",
                      "inputType": "dropdown",
                      "validation": {
                        "type": "",
                        "required": true,
                        "params": [],
                        "errorText": "Required"
                      },
                      "placeholder": "Select loan reason",
                      "options": "fn(''${getPrimeKey}'' === ''true'' ? [\"Personal Use\", \"Education purpose\", \"Home Renovation\", \"Purchasing Home\", \"Tourism\", \"Wedding\"] : [\"Personal Use\", \"Balance Transfer\", \"Buy First House\", \"Family Celebration\", \"Health and Wellness\", \"Loan Consolidation\", \"Medical Treatment\", \"Multipurpose\", \"Vacation\", \"Wedding Loan\"])"
                    },
                    {
                      "containerStyle": {
                        "marginTop": "24px"
                      },
                      "dataType": "text",
                      "label": "Highest Education Qualification",
                      "key": "educationLevel",
                      "inputType": "dropdown",
                      "validation": {
                        "type": "",
                        "required": true,
                        "params": [],
                        "errorText": "Required"
                      },
                      "placeholder": "Select max education qualification",
                      "options": [
                        "Under Graduate",
                        "Graduate",
                        "Post Graduate",
                        "PhD",
                        "Chartered Accountant",
                        "Company Secretary",
                        "Doctor",
                        "Cost Accountant"
                      ]
                    },
                    {
                      "containerStyle": {
                        "marginTop": "24px"
                      },
                      "dataType": "text",
                      "label": "Father Name",
                      "key": "fathersName",
                      "inputType": "text",
                      "validation": {
                        "type": "regex",
                        "required": true,
                        "params": [
                          "^([a-zA-Z]{1,})+[a-zA-Z\\s]*$"
                        ],
                        "errorText": "min 3 chars and only alphabets/space allowed"
                      },
                      "placeholder": "Enter your father full name",
                      "options": []
                    },
                    {
                      "inputRegex": "",
                      "containerStyle": {
                        "marginTop": "24px"
                      },
                      "dataType": "number",
                      "label": "Marital Status",
                      "key": "maritalStatus",
                      "inputType": "radioButtonGroup",
                      "validation": {
                        "type": "",
                        "required": true,
                        "params": [],
                        "errorText": "Required"
                      },
                      "placeholder": "Select marital status",
                      "options": [
                        {
                          "icon": "https://finbox-cdn.s3.ap-south-1.amazonaws.com/assets/builder/PFL/single.svg",
                          "label": "Single"
                        },
                        {
                          "icon": "https://finbox-cdn.s3.ap-south-1.amazonaws.com/assets/builder/PFL/married.svg",
                          "label": "Married"
                        }
                      ]
                    }
                  ],
                  "type": "form",
                  "key": "pre_loan_form_card"
                }
              ],
              "open": true,
              "inputType": "accordion"
            },
            {
              "containerStyle": {
                "marginTop": "auto"
              },
              "label": "Continue",
              "disabled": false,
              "key": "submit",
              "action": [
                {
                  "requestPayload": {
                    "preLoanData": {
                      "educationLevel": "${pre_loan_form.educationLevel}",
                      "maritalStatus": "${pre_loan_form.maritalStatus}",
                      "loanPurpose": "${pre_loan_form.loanPurpose}",
                      "fathersName": "${pre_loan_form.fathersName}"
                    },
                    "loanApplicationID": "${currentLoan.loanApplicationID}",
                    "signalName": "pre_loan_submitted"
                  },
                  "sequence": 0,
                  "url": "/loan/submitPreloanV2",
                  "navigate": {
                    "url": "",
                    "type": "reload"
                  },
                  "key": "submitDetails",
                  "type": "post"
                }
              ],
              "type": "submit",
              "inputType": "primaryButton"
            }
          ],
          "type": "form",
          "key": "pre_loan_form"
        }
      ],
      "type": "page"
    }
  ],
  "key": "PRE_LOAN_DETAILS_PAGE",
  "subModuleNameTag": "PRE_LOAN_DETAILS"
}'
) ON CONFLICT (id)
DO UPDATE SET (
    source_entity_id,
    module_name,
    sub_module_name,
    created_by,
    is_active,
    section_data,
    updated_at
) = (
    excluded.source_entity_id,
    excluded.module_name,
    excluded.sub_module_name,
    excluded.created_by,
    excluded.is_active,
    excluded.section_data,
    now()
);
