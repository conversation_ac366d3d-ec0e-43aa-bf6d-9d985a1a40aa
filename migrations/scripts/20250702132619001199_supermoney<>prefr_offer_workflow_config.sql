-- Disable existing Offer workflow for Prefr<>SuperMoney
UPDATE workflow_config
SET is_active = FALSE
WHERE source_entity_id = 'f06cf417-f415-4d93-b733-a45e885a4d36'
AND module_name = 'OFFER';


-- Create new Offer workflow for Prefr<>SuperMoney
INSERT INTO workflow_config(
  config_id,
  source_entity_id,
  organization_id,
  program_name,
  lender_id,
  module_name,
  created_by,
  is_active,
  workflow_definition
) VALUES (
  uuid_generate_v4(),
  'f06cf417-f415-4d93-b733-a45e885a4d36',
  '8af96add-e7eb-4583-8ded-0cebe42121d8',
  'PERSONAL_LOAN',
  '9c8551e3-fd00-46f0-b61b-64d0011f3bbd',
  'OFFER',
  'ADMIN',
  TRUE,
'{
  "retries": [
    {
      "delay": "PT1M",
      "maxAttempts": 23,
      "name": "ServicesNotAvailableRetryStrategy",
      "multiplier": 1.5
    }
  ],
  "name": "Prefr<>SuperMoney offer and loan generation workflow",
  "states": [
    {
      "dataConditions": [
        {
          "transition": "SavePLOffer",
          "condition": ".lisaOfferResponse.segment == \"policy_approved\""
        },
        {
          "transition": "WaitForPolicyStatusSignal",
          "condition": ".lisaOfferResponse.segment == \"policy_approved_tentative\""
        }
      ],
      "defaultCondition": {
        "transition": "DisqualifyUser"
      },
      "type": "switch",
      "name": "checkOfferResponseStatus"
    },
    {
      "timeouts": {
        "eventTimeout": "PT168H"
      },
      "eventConditions": [
        {
          "transition": "SavePLOffer",
          "eventRef": "offer_final_generated"
        },
        {
          "transition": "DisqualifyUser",
          "eventRef": "user_disqualified"
        }
      ],
      "defaultCondition": {
        "transition": "WaitForPolicyStatusSignal"
      },
      "type": "switch",
      "name": "WaitForPolicyStatusSignal"
    },
    {
      "transition": "WaitForAcceptOfferSignal",
      "type": "operation",
      "name": "SavePLOffer",
      "actions": [
        {
          "retryRef": "ServicesNotAvailableRetryStrategy",
          "functionRef": {
            "refName": "SavePLOffer",
            "arguments": {
              "status": 1,
              "offerMetadataBytes": "${.lisaOfferResponse | tostring}",
              "processingFeeType": "PERC",
              "maxEMI": "${.lisaOfferResponse.offers[0].maxEmi}",
              "minTenure": "${.lisaOfferResponse.offers[0].minTenure}",
              "interest": "${.lisaOfferResponse.offers[0].interestRate}",
              "minAmount": "${.lisaOfferResponse.offers[0].minAmount}",
              "lenderID": "${.lenderID}",
              "method": "rb",
              "maxTenure": "${.lisaOfferResponse.offers[0].maxTenure}",
              "processingFee": "${.lisaOfferResponse.offers[0].processingFee}",
              "offerType": "${.lisaOfferResponse.offers[0].offerType}",
              "subProductType": "${.lisaOfferResponse.offers[0].subProductType}",
              "maxAmount": "${.lisaOfferResponse.offers[0].maxAmount}"
            }
          }
        }
      ],
      "onErrors": [
        {
          "transition": "HandleInternalFailure",
          "errorRef": "DefaultErrorRef"
        }
      ]
    },
    {
      "end": true,
      "type": "operation",
      "name": "CompleteWorkflow",
      "actions": [
        {
          "retryRef": "ServicesNotAvailableRetryStrategy",
          "functionRef": {
            "refName": "UpdateUserWorkflowStatus",
            "arguments": {
              "workflowStatus": "SUCCESS"
            }
          }
        }
      ]
    },
    {
      "end": true,
      "type": "operation",
      "name": "HandleInternalFailure",
      "actions": [
        {
          "retryRef": "ServicesNotAvailableRetryStrategy",
          "functionRef": {
            "refName": "UpdateUserWorkflowStatus",
            "arguments": {
              "errorType": "ERR_WF_FAILED",
              "workflowStatus": "FAILED",
              "failureReason": "INTERNAL_FAILURE"
            }
          }
        }
      ]
    },
    {
      "timeouts": {
        "eventTimeout": "PT168H"
      },
      "eventConditions": [
        {
          "transition": "CreateLoanApplication",
          "eventRef": "offer_accepted"
        }
      ],
      "defaultCondition": {
        "transition": "WaitForAcceptOfferSignal"
      },
      "type": "switch",
      "name": "WaitForAcceptOfferSignal"
    },
    {
      "transition": "CompleteWorkflow",
      "type": "operation",
      "name": "DisqualifyUser",
      "actions": [
        {
          "retryRef": "ServicesNotAvailableRetryStrategy",
          "functionRef": {
            "refName": "DisqualifyUser",
            "arguments": {
              "rejectReason": "${ .rejectReason }"
            }
          }
        }
      ],
      "onErrors": [
        {
          "transition": "HandleInternalFailure",
          "errorRef": "DefaultErrorRef"
        }
      ]
    },
    {
      "transition": "CompleteWorkflow",
      "type": "operation",
      "name": "CreateLoanApplication",
      "actions": [
        {
          "retryRef": "ServicesNotAvailableRetryStrategy",
          "functionRef": {
            "refName": "AcceptGeneratedPLLoanOffer",
            "arguments": {
              "skipCalcProcessingFee": "true",
              "toSkipLoanOfferTemplate": "true",
              "offerID": "${ .loanOfferID }",
              "skipOfferStatusUpdate": "false"
            }
          }
        },
        {
          "retryRef": "ServicesNotAvailableRetryStrategy",
          "functionRef": {
            "refName": "UpdateLoanApplicationV2",
            "arguments": {
              "amount": "${ .signal_data_offer_accepted.amount }",
              "interest": "${ .signal_data_offer_accepted.interest }",
              "processingFee": "${ .signal_data_offer_accepted.processingFee }",
              "loanApplicationID": "${ .loanApplicationID }",
              "tenure": "${ .signal_data_offer_accepted.tenure}"
            }
          }
        }
      ]
    }
  ],
  "start": "checkOfferResponseStatus",
  "version": "1.0.0",
  "specVersion": "0.8.0",
  "id": "a64baad2-8b3a-4605-a13c-111aade75e17"
}');