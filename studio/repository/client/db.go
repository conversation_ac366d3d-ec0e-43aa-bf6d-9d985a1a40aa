package clientrepository

import (
	"context"
	"finbox/go-api/functions/logger"
	"finbox/go-api/studio/models"

	"github.com/jmoiron/sqlx"
)

type ClientRepository struct {
	DatabaseClient *sqlx.DB
}

func New(db *sqlx.DB) *ClientRepository {
	return &ClientRepository{
		DatabaseClient: db,
	}
}

func (c *ClientRepository) CheckOrgExists(ctx context.Context, organizationID string) bool {

	count := 0
	query := `SELECT count(*) FROM organization WHERE organization_id = $1`
	_ = c.DatabaseClient.GetContext(ctx, &count, query, organizationID)
	return count > 0

}

func (c *ClientRepository) GetDeviceConnectCredentials(ctx context.Context, keyID string) (models.DeviceConnectCredentails, error) {

	var creds models.DeviceConnectCredentails
	query := `SELECT key_id, api_key, connect_server_hash, coalesce(client_id, 0) as client_id FROM device_connect_keys WHERE key_id = $1`
	err := c.DatabaseClient.GetContext(ctx, &creds, query, keyID)
	if err != nil {
		logger.Log.WithContext(ctx).Errorln(err)
		return models.DeviceConnectCredentails{}, err
	}
	return creds, nil
}
