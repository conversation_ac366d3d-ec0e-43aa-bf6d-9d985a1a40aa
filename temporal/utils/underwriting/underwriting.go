package underwritingutils

import (
	"context"
	"errors"
	"finbox/go-api/constants"
	"finbox/go-api/errorHandler"
	"finbox/go-api/functions/journey"
	"finbox/go-api/functions/logger"
	"finbox/go-api/infra/redis"
	"finbox/go-api/infra/temporalclient"
	"finbox/go-api/models/temporalsignallogging"
	"finbox/go-api/models/users"
	"finbox/go-api/models/userworkflows"
	"finbox/go-api/temporal/temporalutility"
	"finbox/go-api/temporal/workflows"
	"finbox/go-api/utils/builderutils"
	"finbox/go-api/utils/general"
	"finbox/go-api/utils/journeyutils"

	"fmt"
	"time"

	redisclient "github.com/go-redis/redis/v8"
	"go.temporal.io/sdk/client"
)

// DashboardUtility - creates new workflow if no active workflows are found
// sends signal if active workflow is present
func (uw *UnderwritingStruct) DashboardUtility() error {

	if uw == nil {
		err := fmt.Errorf("bd is nil")
		logger.Log.Errorln(err)
		return err
	}

	var (
		workflowFailedAbruptly bool
		errorCode              string
		failureReason          string
	)

	wf, err := userworkflows.Get(uw.UserID, uw.ModuleInfo.ModuleName)
	if err != nil {
		logger.WithUser(uw.UserID).Errorln(err)
		return err
	}

	// checks for temporal status - factors in temporal workflow failures/ timeout

	workflowFailedAbruptly, errorCode, failureReason, err = temporalutility.RunSanityChecksOnWorkflowID(context.Background(), uw.UserID, wf.WorkflowID, wf.RunID)
	if err != nil {
		logger.WithUser(uw.UserID).Errorf("[UnderwritingStruct.DashboardUtility] failed to get workflow health, err: %v, userID: %s, workflowID: %s, runID: %s", err, uw.UserID, wf.WorkflowID, wf.RunID)
		return err
	}
	if workflowFailedAbruptly {
		errWfUpdate := userworkflows.UpdateStatus(nil, uw.UserID, constants.TemporalStatusFailed, wf.WorkflowID, wf.RunID, errorCode, failureReason)
		if errWfUpdate != nil {
			errorHandler.ReportToSentryWithoutRequest(errWfUpdate)
			return err
		}
	}

	// spawn new workflow if the workflow has reached terminal state or if temporal workflow has failed abruptly or if no workflows are found
	createNewWorkflow := workflowFailedAbruptly || wf.IsTerminalStatus()

	if createNewWorkflow {
		uw.MarkModuleMapping = true
		if wf.IsSuccess() || wf.IsArchived() {
			uw.MarkModuleMapping = false
		}
		if err = uw.SpawnNewWorkflow(); err != nil {
			logger.WithUser(uw.UserID).Errorln(err)
			return err
		}
	} else {

		subModule, err := journeyutils.GetCurrentSubmodule(uw.UserID, wf.WorkflowID, wf.RunID)
		if err != nil {
			logger.WithUser(uw.UserID).Errorln(err)
			errorHandler.ReportToSentryWithFields(map[string]interface{}{"user_id": uw.UserID}, err)
			return err
		}

		if !general.InArr(subModule, uw.ModuleInfo.SubModuleName) {
			err = fmt.Errorf("invalid submodule %s for retrigger bre signal for user: %s", subModule, uw.UserID)
			logger.WithUser(uw.UserID).Errorln(err)
			errorHandler.ReportToSentryWithFields(map[string]interface{}{"user_id": uw.UserID}, err)
			return err
		}
		// send signal to the running workflow
		if err = uw.SignalWorkflow(wf); err != nil {
			logger.WithUser(uw.UserID).Error(err)
			return err
		}
	}

	return err
}

// SpawnNewWorkflow - creates new bureau workflow
func (uw *UnderwritingStruct) SpawnNewWorkflow() (err error) {
	// get user data
	user, err := users.Get(uw.UserID)
	if err != nil {
		logger.WithUser(uw.UserID).Errorln(err)
		return err
	}

	var parentSourceEntityID = user.SourceEntityID
	journey.GetParentSourceEntityID(uw.UserID, &parentSourceEntityID)
	isDSA := false
	if parentSourceEntityID != user.SourceEntityID {
		isDSA = true
	}

	workflowConfig, err := builderutils.FetchWorkflowConfig(context.Background(), uw.UserID, user.SourceEntityID, uw.ModuleInfo.ModuleName, uw.LenderID, parentSourceEntityID, isDSA, false)
	if err != nil {
		logger.WithUser(uw.UserID).Error(err)
		return err
	}

	key := journey.TemporalKey(fmt.Sprintf("dashboard_%s", uw.UserID))
	err = redis.SetArgs(context.Background(), key, "1", redisclient.SetArgs{Mode: "NX", TTL: 1 * time.Minute})
	if err != nil {
		logger.WithUser(uw.UserID).Error(err)
		return errors.New(constants.ErrStrPreviousWorkflowRunning)
	}

	var workflowType = workflows.DashboardWorkflowV0
	if uw.MarkModuleMapping {
		workflowType = workflows.GenericWorkflowV4
	}

	run, err := temporalclient.Client.ExecuteWorkflow(
		context.Background(),
		client.StartWorkflowOptions{
			TaskQueue:                journey.GetClientQueue(user.SourceEntityID),
			WorkflowExecutionTimeout: journey.GetWorkflowTimeout(uw.ModuleInfo.ModuleName),
		},
		workflowType,
		uw.ModuleInfo.ModuleName,
		workflowConfig.WorkflowDefinition,
		map[string]interface{}{
			"userObj":       user,
			"triggerSource": uw.EntityType,
			"triggerType":   uw.TriggerType,
			"initData": map[string]interface{}{
				"entityType": uw.EntityType,
				"entityRef":  uw.EntityRef,
			},
		},
	)
	if err != nil {
		logger.WithUser(uw.UserID).Errorln(err)
		// delete redis key if workflow execution fails
		errRedis := redis.Delete(context.Background(), key)
		if errRedis != nil {
			logger.WithUser(uw.UserID).Error(errRedis)
			errorHandler.ReportToSentryWithFields(map[string]interface{}{
				"userID": uw.UserID,
				"error":  "unable to release redis lock",
			}, errRedis)
		}
		return err
	}

	runID := run.GetRunID()
	workflowID := run.GetID()

	if insErr := userworkflows.InsertV2(uw.UserID, workflowConfig.ConfigID.String(), workflowID, runID, uw.ModuleInfo.ModuleName, constants.TemporalStatusRunning, uw.EntityType); insErr != nil {
		logger.WithUser(uw.UserID).Errorln(insErr)
		return insErr
	}

	return nil
}

// SignalWorkflow - sends signal to bureau workflow
func (uw *UnderwritingStruct) SignalWorkflow(wf userworkflows.UserWorkflow) (err error) {

	var data = map[string]interface{}{
		"entityType": uw.EntityType,
		"entityRef":  uw.EntityRef,
	}
	err = temporalclient.Client.SignalWorkflow(context.Background(), wf.WorkflowID, wf.RunID, uw.SignalName, data)
	if err != nil {
		logger.WithUser(uw.UserID).Error(err)
		return err
	}

	if err = temporalsignallogging.InsertV3(uw.SignalName, uw.UserID, wf.WorkflowID, uw.EntityType, data); err != nil {
		logger.WithUser(uw.UserID).Errorln(err)
	}
	return err
}
