// Package activities to write temporal activities
package activities

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"finbox/go-api/conf"
	"finbox/go-api/constants"
	"finbox/go-api/errorHandler"
	"finbox/go-api/functions/journey"
	"finbox/go-api/functions/legallogs"
	"finbox/go-api/functions/lenderservice"
	"finbox/go-api/functions/logger"
	"finbox/go-api/functions/retry"
	"finbox/go-api/functions/services/tdl"
	"finbox/go-api/functions/underwriting"
	"finbox/go-api/infra/s3"
	"finbox/go-api/models/documents"
	"finbox/go-api/models/expiry"
	"finbox/go-api/models/lendervariables"
	"finbox/go-api/models/loanapplication"
	"finbox/go-api/models/loankycdetails"
	"finbox/go-api/models/media"
	"finbox/go-api/models/misc"
	"finbox/go-api/models/personalloanoffer"
	"finbox/go-api/models/userloandetails"
	"finbox/go-api/models/users"
	"finbox/go-api/temporal/temporaltracer"
	"finbox/go-api/utils/calc"
	"finbox/go-api/utils/general"
	"finbox/go-api/utils/lenderutil"
	"finbox/go-api/utils/mapper"
	"fmt"
	"math"
	"strconv"
	"strings"
	"time"

	"github.com/spf13/cast"

	finboxEvents "finbox/go-api/functions/activity"

	runnerconstants "github.com/finbox-in/road-runner/constants"
	"github.com/finbox-in/road-runner/runner"
	"github.com/finbox-in/road-runner/types/argument"
	"go.temporal.io/sdk/activity"
)

// LisaApplicationStatus returns the status of the ongoing application
// For KreditBee this also controls the journey modules
func (a *Activity) LisaApplicationStatus(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	var arguments struct {
		ToAddRespInData string `arg:"toAddRespInData"`
	}
	err = GetArgsV2(&arguments, data)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithFields(errorHandler.ErrorFields{"workflowID": activityInfo.WorkflowExecution.ID, "runID": activityInfo.WorkflowExecution.RunID}, err)
		// return ars, err
	}

	// check if we need resp in data map
	toAddRespInData := arguments.ToAddRespInData == "true"

	lenderID, exists := data["lenderID"].(string)
	if !exists {
		lenderID, _ = underwriting.GetLenderID(userObj.SourceEntityID, userObj.ID, "")
		data["lenderID"] = lenderID
	}
	var loanObj personalloanoffer.PersonalLoanOffer
	loanObj, err = personalloanoffer.GetByLender(userObj.ID, lenderID)
	reqData := lenderservice.ApplicationStatusReq{
		ApplicationReq: lenderservice.ApplicationReq{
			UserID:   userObj.ID,
			LenderID: lenderID,
		},
	}
	res, err := lenderservice.ApplicationStatus(ctx, &reqData, lenderservice.ApplicationStatusResource)
	if err != nil {
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		description := fmt.Sprintf(`{"lender": "%s", "rejectReason":"%s"}`, constants.LenderNamesMap[lenderID], constants.ErrorLenderAPIFailed)
		fmt.Println("Added lender reject and bring back", description)
		_, rejectionErr := tdl.RejectLenderAndBringBack(tdl.LenderReject{
			UserID:         userObj.ID,
			SourceEntityID: userObj.SourceEntityID,
			OfferID:        loanObj.PersonalLoanOfferID,
			LenderID:       lenderID,
			Description:    description,
			RejectReason:   constants.ErrorLenderAPIFailed,
		})
		if rejectionErr != nil {
			logger.WithUser(userObj.ID).Error(rejectionErr)
			return ars, rejectionErr
		}
		return ars, err
	}

	higherLimitConsent, highLimitConsentExists := res.LenderData["higherLimitConsent"].(map[string]interface{})
	if highLimitConsentExists {
		higherLimitConsentID, ok2 := higherLimitConsent["consentId"].(float64)
		eligible, ok3 := higherLimitConsent["eligible"].(bool)
		if ok2 && ok3 && eligible {
			logger.WithUser(userObj.ID).Debugf("higher consent found as expected")
			data["higherLimitConsentID"] = strconv.FormatInt(int64(higherLimitConsentID), 10)
		} else if !ok2 && ok3 {
			logger.WithUser(userObj.ID).Errorln("values for ok, ok2, ok3, eligible:", highLimitConsentExists, ok2, ok3, eligible, " are out of sync for higherConsent")
		}
	}

	_, isVKYCCompletedExists := data["isVKYCCompleted"]
	if !isVKYCCompletedExists {
		data["isVKYCCompleted"] = false
	}

	_, isHardOfferShown := data["isHardOfferShown"]
	if !isHardOfferShown {
		data["isHardOfferShown"] = false
	}

	cnt, err := loanapplication.GetActiveLoanCount(userObj.ID)
	if err != nil {
		logger.WithUser(userObj.ID).Errorln(err)
		return ars, err
	}

	data["activeLoanExists"] = cnt > 0

	var fathersName bool
	var email bool
	var errorToast string
	if res.LenderData != nil {
		data["lenderData"] = res.LenderData
		val, isReferenceRequired := res.LenderData["isReferenceRequired"]
		if isReferenceRequired {
			fathersName = val.(string) == "yes"
		}
		val, emailRequired := res.LenderData["email"]
		if emailRequired {
			email = val.(string) == "no"
		}
		val, emailErrorExists := data["emailError"]
		if emailErrorExists {
			errorToast = val.(string)
		}
	}
	output["preLoanDataRequired"] = map[string]interface{}{
		"fathersName": fathersName,
		"email":       email,
		"errorToast":  errorToast,
	}

	if toAddRespInData {
		data["appStatusRes"] = res
	}

	return ars, nil
}

func (a *Activity) LisaApplicationStatusV2(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	var arguments struct {
		ToAddRespInData string `arg:"toAddRespInData"`
		Intent          string `arg:"intent"`
		Poll            string `arg:"poll"`
		PollKey         string `arg:"pollKey"`
	}
	err = GetArgsV2(&arguments, data)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithFields(errorHandler.ErrorFields{"workflowID": activityInfo.WorkflowExecution.ID, "runID": activityInfo.WorkflowExecution.RunID}, err)
		// return ars, err
	}
	poll := arguments.Poll == "true"

	// check if we need resp in data map
	toAddRespInData := arguments.ToAddRespInData == "true"

	lenderID, exists := data["lenderID"].(string)
	if !exists {
		lenderID, _ = underwriting.GetLenderID(userObj.SourceEntityID, userObj.ID, "")
		data["lenderID"] = lenderID
	}

	reqData := lenderservice.ApplicationStatusReq{
		Intent: arguments.Intent,
		ApplicationReq: lenderservice.ApplicationReq{
			UserID:   userObj.ID,
			LenderID: lenderID,
		},
	}

	res, err := lenderservice.ApplicationStatus(ctx, &reqData, lenderservice.ApplicationStatusResource)
	if err != nil {
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	higherLimitConsent, highLimitConsentExists := res.LenderData["higherLimitConsent"].(map[string]interface{})
	if highLimitConsentExists {
		higherLimitConsentID, ok2 := higherLimitConsent["consentId"].(float64)
		eligible, ok3 := higherLimitConsent["eligible"].(bool)
		if ok2 && ok3 && eligible {
			logger.WithUser(userObj.ID).Debugf("higher consent found as expected")
			data["higherLimitConsentID"] = strconv.FormatInt(int64(higherLimitConsentID), 10)
		} else if !ok2 && ok3 {
			logger.WithUser(userObj.ID).Errorln("values for ok, ok2, ok3, eligible:", highLimitConsentExists, ok2, ok3, eligible, " are out of sync for higherConsent")
		}
	}

	_, isVKYCCompletedExists := data["isVKYCCompleted"]
	if !isVKYCCompletedExists {
		data["isVKYCCompleted"] = false
	}

	_, isHardOfferShown := data["isHardOfferShown"]
	if !isHardOfferShown {
		data["isHardOfferShown"] = false
	}

	cnt, err := loanapplication.GetActiveLoanCount(userObj.ID)
	if err != nil {
		logger.WithUser(userObj.ID).Errorln(err)
		return ars, err
	}

	data["activeLoanExists"] = cnt > 0

	var fathersName bool
	var email bool
	var errorToast string
	if res.LenderData != nil {
		data["lenderData"] = res.LenderData
		val, isReferenceRequired := res.LenderData["isReferenceRequired"]
		if isReferenceRequired {
			fathersName = val.(string) == "yes"
		}
		val, emailRequired := res.LenderData["email"]
		if emailRequired {
			email = val.(string) == "no"
		}
		val, emailErrorExists := data["emailError"]
		if emailErrorExists {
			errorToast = val.(string)
		}
	}
	output["preLoanDataRequired"] = map[string]interface{}{
		"fathersName": fathersName,
		"email":       email,
		"errorToast":  errorToast,
	}

	if poll {
		if arguments.PollKey == "" {
			data["lisaApplicationStatusCount"] = incrementCounterForKey(data, "lisaApplicationStatusCount")
		} else {
			data[arguments.PollKey] = incrementCounterForKey(data, arguments.PollKey)
		}
	}
	if toAddRespInData {
		data["appStatusRes"] = res
	}

	return ars, nil
}

func (a *Activity) CheckOfferDiff(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	loanObj, err := loanapplication.GetLatestByUser(userObj.ID)
	if err != nil {
		logger.WithUser(userObj.ID).Errorln(err)
		return ars, err
	}

	type argumentStruct struct {
		Intent string `arg:"intent"`
	}

	var args argumentStruct
	err = GetArgsV2(&args, data)
	if err != nil {
		logger.WithUser(userObj.ID).Errorln(err)
		return ars, err
	}

	if args.Intent == "" {
		args.Intent = "hard"
	}

	reqData := lenderservice.GetOfferReq{
		Intent: args.Intent,
		ApplicationReq: lenderservice.ApplicationReq{
			UserID:   userObj.ID,
			LenderID: loanObj.LenderID,
		},
	}

	resp, err := lenderservice.GetOffers(ctx, &reqData)
	if err != nil {
		logger.WithUser(userObj.ID).Errorln(err)
		return ars, err
	}

	areComparableWithDelta := func(a, b, delta float64) bool {
		return math.Abs(a-b) <= delta
	}

	if resp != nil {
		offerDiffExists := true
		for _, offer := range resp.Offers {
			if offer != nil {
				if areComparableWithDelta(loanObj.Amount, offer.MaxAmount, 0.50) &&
					areComparableWithDelta(loanObj.Interest, offer.InterestRate, 0.01) &&
					areComparableWithDelta(float64(loanObj.Tenure), float64(offer.MaxTenure), 0.00) {
					offerDiffExists = false
					break
				}
			}
		}

		if offerDiffExists {
			data["offerDiffExists"] = offerDiffExists
			data["isHardOfferShown"] = false
		}
	}

	return ars, nil
}

// LisaUpdateApplication is used to PUT details for existing application using intent from activcity arguments
func (a *Activity) LisaUpdateApplication(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	lenderID, ok := data["lenderID"].(string)
	if !ok {
		lenderID, _ = underwriting.GetLenderID(userObj.SourceEntityID, userObj.ID, "")
		data["lenderID"] = lenderID
	}

	intents := make([]string, 0)
	preLoanData := make(map[string]interface{})

	type argumentStruct struct {
		FathersName       string `arg:"fathers_name"`
		AlternateEmail    string `arg:"alternateEmail"`
		Intent            string `arg:"intent"`
		LoanApplicationID string `arg:"loanApplicationID"`
		EventType         string `arg:"eventType"`
		Description       string `arg:"description"`
	}

	var args argumentStruct
	err = GetArgsV2(&args, data)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, NewNonRetryableApplicationError(err.Error())
	}

	if args.FathersName != "" {
		intents = append(intents, constants.LisaUpdateApplicationIntentFathersName)
		preLoanData["fathersName"] = args.FathersName
	}

	if args.AlternateEmail != "" {
		intents = append(intents, constants.LisaUpdateApplicationIntentAlternateEmail) // to be verified.
		preLoanData["email"] = args.AlternateEmail
	}

	if args.Intent != "" {
		intents = append(intents, args.Intent)
	}

	var loanApplicationId string
	if args.LoanApplicationID != "" {
		loanApplicationId = args.LoanApplicationID
	} else {
		loanApplicationId, err = loanapplication.GetLoanApplicationID(context.TODO(), userObj.ID, "")
		if err != nil {
			logger.WithUser(userObj.ID).Errorln(err)
		}
		data["loanApplicationID"] = loanApplicationId
	}

	if len(preLoanData) > 0 {
		err = userloandetails.UpdatePreLoanDataAndPreserveExistingKeys(nil, "", userObj.ID, preLoanData)
		if err == sql.ErrNoRows {
			loanObj, err := loanapplication.GetLatestByUser(userObj.ID)
			if err != nil {
				logger.WithUser(userObj.ID).Errorln(err)
				return ars, err
			}

			err = userloandetails.InsertV2(&ctx,
				sql.NullString{String: loanObj.ID.String(), Valid: loanObj.ID.String() != ""},
				userObj.ID,
				userObj.ID,
				nil)
			if err != nil {
				logger.WithUser(userObj.ID).Errorln(err)
				return ars, err
			}

			err = userloandetails.UpdatePreLoanDataAndPreserveExistingKeys(nil, "", userObj.ID, preLoanData)
			if err != nil {
				logger.WithUser(userObj.ID).Errorln(err)
				return ars, err
			}
		} else if err != nil {
			err = fmt.Errorf("error updating pre_loan_data, userID: %s, err: %s", userObj.ID, err.Error())
			errorHandler.ReportToSentryWithoutRequest(err)
			logger.WithUser(userObj.ID).Errorln(err)
			return ars, err
		}
	}

	var resp *lenderservice.ApplicationResp

	// TODO : improve this logic by adding an update pre_loan_data func and using another activity
	for i := 0; i < len(intents); i++ {
		if i > 0 {
			// sleep before a consecutive call
			time.Sleep(1 * time.Second)
		}
		reqData := lenderservice.UpdateApplicationReq{
			Intent: intents[i],
			ApplicationReq: lenderservice.ApplicationReq{
				UserID:            userObj.ID,
				LenderID:          lenderID,
				SourceEntityID:    userObj.SourceEntityID,
				LoanApplicationID: loanApplicationId,
			},
		}

		resp, err = lenderservice.UpdateApplication(ctx, &reqData)
		if err != nil {
			logger.WithUser(userObj.ID).Errorln(err)
			if err.Error() == constants.ErrEmailAlreadyExists {
				data["emailError"] = constants.ErrEmailAlreadyExists
				return ars, nil
			}
			errorHandler.ReportToSentryWithoutRequest(err)
			return ars, err
		}

		eventType := constants.ActivityPreLoanDataSubmitted
		if args.EventType != "" {
			eventType = args.EventType
		}

		description := intents[i]
		if args.Description != "" {
			description = args.Description
		}

		dateTimeNowString := general.GetTimeStampString()
		activityObj := finboxEvents.ActivityEvent{
			UserID:            userObj.ID,
			SourceEntityID:    userObj.SourceEntityID,
			EntityType:        constants.EntityTypeCustomer,
			EventType:         eventType,
			LoanApplicationID: loanApplicationId,
			Description:       description,
		}
		finboxEvents.RegisterEvent(&activityObj, dateTimeNowString)
	}

	data["updateApplicationResponse"] = resp
	output["updateApplicationResponse"] = resp

	return ars, nil
}

// LisaUpdateApplicant is used to PUT details for existing application using intent from activity arguments
func (a *Activity) LisaUpdateApplicant(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	lenderID, ok := data["lenderID"].(string)
	if !ok {
		lenderID, _ = underwriting.GetLenderID(userObj.SourceEntityID, userObj.ID, "")
		data["lenderID"] = lenderID
	}

	var reqBody = lenderservice.ApplicationReq{
		UserID:         userObj.ID,
		LenderID:       lenderID,
		SourceEntityID: userObj.SourceEntityID,
	}

	_, err = lenderservice.UpdateApplicant(ctx, &reqBody)
	if err != nil {
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}
	return ars, nil
}

// LisaBoostOffer is used to start boost attempt of an offer
// It can return a redirectURL to be used for boosting the offer
func (a *Activity) LisaBoostOffer(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	type argumentStruct struct {
		ActivityEventType        string `arg:"activityEventType"`
		ActivityEventDescription string `arg:"activityEventDescription"`
	}

	var args argumentStruct
	err = GetArgsV2(&args, data)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, NewNonRetryableApplicationError(err.Error())
	}

	lenderID, ok := data["lenderID"].(string)
	if !ok {
		lenderID, _ = underwriting.GetLenderID(userObj.SourceEntityID, userObj.ID, "")
		data["lenderID"] = lenderID
	}

	reqData := lenderservice.BoostOfferReq{
		RedirectURL: conf.BaseURL + "/v1/callback/lisa/boostOffer?status={{status}}&transaction_id={{transaction_id}}",
		ApplicationReq: lenderservice.ApplicationReq{
			UserID:         userObj.ID,
			LenderID:       lenderID,
			SourceEntityID: userObj.SourceEntityID,
		},
	}

	delete(output, constants.OutputKeyBoosterURLAndParams)

	resp, err := lenderservice.BoostOffer(ctx, &reqData)
	if err != nil {
		data["errMsg"] = err.Error()
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	// save response in lender variables
	err = lendervariables.InsertOrUpdate(nil, lendervariables.LenderVariablesStructNullable{
		UserID:          userObj.ID,
		LenderID:        lenderID,
		BCTransactionID: sql.NullString{String: resp.TransactionID, Valid: resp.TransactionID != ""},
		Status:          sql.NullInt64{Int64: lendervariables.LenderVariableStatusActive, Valid: true},
	})
	if err != nil {
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	data["lenderBoostTransactionID"] = resp.TransactionID
	data["lenderBoostStatus"] = resp.Status

	// set variables in output to be polled by backend redirection route
	output[constants.OutputKeyBoosterURLAndParams] = map[string]interface{}{
		"boosterURL":     resp.RedirectURL,
		"redirectParams": resp.Params,
	}

	if args.ActivityEventType == "" {
		args.ActivityEventType = constants.ActivityLenderThirdPartyLinkGenerated
	}

	if args.ActivityEventDescription == "" {
		args.ActivityEventDescription = "account_aggregator"
	}

	dateTimeNowString := general.GetTimeStampString()
	activityObj := finboxEvents.ActivityEvent{
		UserID:         userObj.ID,
		SourceEntityID: userObj.SourceEntityID,
		EntityType:     constants.EntityTypeCustomer,
		EventType:      args.ActivityEventType,
		Description:    args.ActivityEventDescription,
	}
	finboxEvents.RegisterEvent(&activityObj, dateTimeNowString)
	data["lisaBoostOfferStatusCount"] = 0
	return ars, nil
}

// KreditbeeErrorHandler is used to handle errors from lender side
func (a *Activity) KreditbeeErrorHandler(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}
	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}
	errorMsg, ok := data["errMsg"].(string)
	if !ok {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error("errorMsg not found in data")
		return ars, errors.New("errorMsg not found in data")
	}

	if errorMsg == constants.ErrStringInconeVerificationProcessing {
		data["boostStatus"] = "reProcessing"
	}
	return ars, nil
}

// LisaFetchNachID is used to fetch nachID from lender side natch setup
func (a *Activity) LisaFetchNachID(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	type argumentStruct struct {
		LoanApplicationID              string  `arg:"loanApplicationID" required:"true"`
		LenderID                       string  `arg:"lenderID" required:"true"`
		SourceEntityID                 string  `arg:"sourceEntityID" required:"true"`
		EMI                            float64 `arg:"emi"`
		CatchErrorStringAsNachNotFound string  `arg:"catchErrorStringAsNachNotFound"` // this argument provides the ability to catch certain errors from the API call and treat them as nachNotFound instead of API failure
	}

	var args argumentStruct

	err = GetArgsV2(&args, data)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, NewNonRetryableApplicationError(err.Error())
	}

	nachID := ""
	nachFound := false

	resp, err := lenderservice.FetchNachID(ctx, &lenderservice.NachInitReq{
		EMI: args.EMI,
		BankAccountReq: lenderservice.BankAccountReq{
			ApplicationReq: lenderservice.ApplicationReq{
				UserID:         userObj.ID,
				LenderID:       args.LenderID,
				SourceEntityID: args.SourceEntityID,
			},
		},
	})

	if err != nil {
		if args.CatchErrorStringAsNachNotFound != "" && strings.Contains(strings.ToLower(err.Error()), strings.ToLower(args.CatchErrorStringAsNachNotFound)) {
			nachFound = false
			nachID = ""
		} else {
			logger.WithUser(userObj.ID).Errorln(err)
			errorHandler.ReportToSentryWithoutRequest(err)
			return ars, err
		}
	} else {
		if resp == nil {
			err = errors.New("lisa nach/fetch/NachID returned nil response")
			logger.WithUser(userObj.ID).Errorln(err)
			errorHandler.ReportToSentryWithoutRequest(err)
			return ars, err
		}
		nachID = resp.NachID
	}

	if nachID != "" {
		nachFound = true
		err = lendervariables.UpdateDynamicVariablesField(userObj.ID, "nachID", fmt.Sprintf(`"%s"`, nachID), args.LenderID, "")
		if err != nil {
			logger.WithUser(userObj.ID).Errorln(err)
			errorHandler.ReportToSentryWithoutRequest(err)
			return ars, err
		}
	}

	data["lisaFetchNachID"] = map[string]interface{}{
		"nachFound": nachFound,
		"nachID":    nachID,
	}

	return ars, nil
}

// LisaNachInit is used to start boost attempt of an offer
// It can return a redirectURL to be used for boosting the offer
func (a *Activity) LisaNachInit(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	type argumentStruct struct {
		LoanApplicationID string  `arg:"loanApplicationID" required:"true"`
		LenderID          string  `arg:"lenderID" required:"true"`
		SourceEntityID    string  `arg:"sourceEntityID" required:"true"`
		EMI               float64 `arg:"emi"`
	}

	var args argumentStruct

	err = GetArgsV2(&args, data)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, NewNonRetryableApplicationError(err.Error())
	}

	delete(output, constants.OutputKeyNachURLAndParams)

	nachTransactionID := general.GetUUID()

	redirectURL := fmt.Sprintf("%s/v1/callback/lisa/nach/%s?status={{status}}&transaction_id={{transaction_id}}", conf.BaseURL, nachTransactionID)
	resp, err := lenderservice.InitiateNach(context.TODO(), &lenderservice.NachInitReq{
		RedirectionURL: redirectURL,
		EMI:            args.EMI,
		BankAccountReq: lenderservice.BankAccountReq{
			ApplicationReq: lenderservice.ApplicationReq{
				RedirectURL:    redirectURL,
				UserID:         userObj.ID,
				LenderID:       args.LenderID,
				SourceEntityID: args.SourceEntityID,
			},
		},
	})

	if err != nil {
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	if resp == nil {
		err = errors.New("lisa nach/init returned nil response")
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	// save response in lender variables
	err = lendervariables.InsertOrUpdate(nil, lendervariables.LenderVariablesStructNullable{
		UserID:            userObj.ID,
		LenderID:          args.LenderID,
		NachTransactionID: sql.NullString{String: resp.TransactionID, Valid: resp.TransactionID != ""},
		Status:            sql.NullInt64{Int64: lendervariables.LenderVariableStatusActive, Valid: true},
	})

	if err != nil {
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}
	data["nachTransactionID"] = nachTransactionID

	// set variables in output to be polled by backend redirection route
	output[constants.OutputKeyNachURLAndParams] = map[string]interface{}{
		"url":            resp.NachURL,
		"redirectParams": resp.Params,
	}

	dateTimeNowString := general.GetTimeStampString()
	activityObj := finboxEvents.ActivityEvent{
		UserID:         userObj.ID,
		SourceEntityID: userObj.SourceEntityID,
		EntityType:     constants.EntityTypeCustomer,
		EventType:      constants.ActivityENachLinkGenerated,
		Description:    "nach_setup_redirection",
	}
	finboxEvents.RegisterEvent(&activityObj, dateTimeNowString)

	return ars, nil
}

// LisaNachStatus is used to poll status of Lisa Nach
func (a *Activity) LisaNachStatus(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	type argumentStruct struct {
		LoanApplicationID string  `arg:"loanApplicationID" required:"true"`
		LenderID          string  `arg:"lenderID" required:"true"`
		SourceEntityID    string  `arg:"sourceEntityID" required:"true"`
		TransactionID     string  `arg:"transactionID"`
		EMI               float64 `arg:"emi"`
		Status            string  `arg:"status"`
		Intent            string  `arg:"intent"`
	}

	var args argumentStruct
	err = GetArgsV2(&args, data)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, NewNonRetryableApplicationError(err.Error())
	}

	resp, err := lenderservice.NachStatus(ctx, &lenderservice.NachInitReq{
		EMI:    args.EMI,
		Intent: args.Intent,
		BankAccountReq: lenderservice.BankAccountReq{
			ApplicationReq: lenderservice.ApplicationReq{
				UserID:         userObj.ID,
				LenderID:       args.LenderID,
				SourceEntityID: args.SourceEntityID,
				TransactionID:  args.TransactionID,
			},
		},
	})

	if err != nil {
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	if resp == nil {
		err = errors.New("lisa nach/status returned nil response")
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	if resp.NachID != "" {
		err = lendervariables.UpdateDynamicVariablesField(userObj.ID, "nachID", fmt.Sprintf(`"%s"`, resp.NachID), args.LenderID, "")
		if err != nil {
			logger.WithUser(userObj.ID).Errorln(err)
			errorHandler.ReportToSentryWithoutRequest(err)
			return ars, err
		}
	}

	data["lisaNachStatus"] = map[string]interface{}{
		"status": resp.Status,
	}
	data["lisaNachTypes"] = resp.NachTypes
	data["lisaNachStatusPollCount"] = incrementCounterForKey(data, "lisaNachStatusPollCount")

	return ars, nil
}

// LisaBoostOfferStatus is used to poll the status of a boost attempt at lender side
func (a *Activity) LisaBoostOfferStatus(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	transactionID, ok := data["lenderBoostTransactionID"].(string)
	if !ok {
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, fmt.Errorf("lenderBoostTransactionID not found in data")
	}

	lenderID, ok := data["lenderID"].(string)
	if !ok {
		lenderID, _ = underwriting.GetLenderID(userObj.SourceEntityID, userObj.ID, "")
		data["lenderID"] = lenderID
	}

	reqData := lenderservice.BoostOfferStatusReq{
		ApplicationReq: lenderservice.ApplicationReq{
			UserID:   userObj.ID,
			LenderID: lenderID,
		},
		TransactionID: transactionID,
	}

	res, err := lenderservice.BoostOfferStatus(ctx, &reqData)
	if err != nil {
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	data["boostStatus"] = res.Status
	data["lisaBoostOfferStatusCount"] = incrementCounterForKey(data, "lisaBoostOfferStatusCount")
	return ars, nil
}

// LisaGetOfferStatus is used to poll the status of a boost attempt at lender side
func (a *Activity) LisaGetOfferStatus(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var arguments struct {
		UserID         string `arg:"userID" required:"true"`
		LenderID       string `arg:"lenderID" required:"true"`
		TransactionID  string `arg:"transactionID" required:"true"`
		SourceEntityID string `arg:"sourceEntityID" required:"true"`
		MaxAttempts    int    `arg:"maxAttempts" required:"true"`
		Intent         string `arg:"intent"`
	}

	err = GetArgsV2(&arguments, data)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	reqData := lenderservice.GetOfferStatusReq{
		ApplicationReq: lenderservice.ApplicationReq{
			UserID:         arguments.UserID,
			LenderID:       arguments.LenderID,
			SourceEntityID: arguments.SourceEntityID,
			Intent:         arguments.Intent,
		},
		TransactionID: arguments.TransactionID,
	}

	res, err := lenderservice.GetOfferStatus(ctx, &reqData)
	if err != nil {
		logger.WithUser(arguments.UserID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	if _, ok := data["pollingMaxAttempts"]; !ok {
		data["pollingMaxAttempts"] = arguments.MaxAttempts
	}

	var count int64
	if _, ok := data["PollAttemptCount"]; !ok {
		data["PollAttemptCount"] = 1
	} else {
		count = cast.ToInt64(data["PollAttemptCount"])
		count++
		data["PollAttemptCount"] = count
	}

	data["lisaGetOfferStatusResponseData"] = res.ResponseData
	return ars, nil
}

// LisaUploadKYC is used to upload documents using intent
// It can also return a redirectURL to be used for external KYC
func (a *Activity) LisaUploadKYC(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	var kycDocs lenderservice.KYCDocumentstructsDetails

	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	lenderID, ok := data["lenderID"].(string)
	if !ok {
		lenderID, _ = underwriting.GetLenderID(userObj.SourceEntityID, userObj.ID, "")
		data["lenderID"] = lenderID
	}

	args := argument.GetActivityArguments(data)
	exists, intent := args.Get("intent", data)
	if !exists {
		err = fmt.Errorf("intent not found in args")
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	loanApplication, _ := loanapplication.GetLatestByUser(userObj.ID)

	reqData := lenderservice.UploadDocumentsReq{
		ApplicationReq: lenderservice.ApplicationReq{
			UserID:   userObj.ID,
			LenderID: lenderID,
		},
		Intent: intent.(string),
	}

	var eventType, eventDescription, kycType, kycTransactionID, vkycTransactionID string

	switch intent {
	case constants.LisaUploadKYCIntentUploadSelfie:
		// fetch documents from loan_kyc_details for selfie intent
		kycDocStatus := constants.KYCDocStatusUploaded
		docs, err := loankycdetails.GetLatestDetailType(loanApplication.ID.String(), constants.DocTypePhoto, &kycDocStatus)
		if err != nil {
			logger.WithUser(userObj.ID).Errorln("error fetching selfie from loan kyc:", err)
			return ars, err
		}

		mediaSelfieDoc, err := media.Get(ctx, docs.MediaID)
		if err != nil {
			logger.WithUser(userObj.ID).Errorln("error fetching selfie media:", err)
			return ars, err
		}

		kycDocs = lenderservice.KYCDocumentstructsDetails{
			LoanKYCDetailsID: docs.UniqueID,
			DocumentType:     docs.DocType,
			Name:             docs.Name,
			Identifier:       docs.Identifier,
			Path:             "",
			MediaID:          docs.MediaID,
			DocumentName:     docs.DocType,
			DocumentURL:      s3.GetPresignedURLS3(mediaSelfieDoc.Path, 300),
		}
		reqData.KycDocuments = []*lenderservice.KYCDocumentstructsDetails{&kycDocs}
		eventType = constants.ActivitySelfieUploaded

	case constants.LisaUploadKYCIntentUploadPAN:
		exists, mediaIDArg := args.Get("mediaID", data)
		if !exists {
			err = fmt.Errorf("mediaID not found in args")
			logger.WithUser(userObj.ID).Errorln(err)
			errorHandler.ReportToSentryWithoutRequest(err)
			return ars, err
		}
		mediaID := mediaIDArg.(string)
		if !general.ValidateUUID(mediaID) {
			err = fmt.Errorf("invalid mediaID passed")
			logger.WithUser(userObj.ID).Errorln(err)
			errorHandler.ReportToSentryWithoutRequest(err)
			return ars, err
		}
		mediaObj, err := media.Get(ctx, mediaID)
		if err != nil {
			err = fmt.Errorf("error getting media found for mediaID: %s err: %s", mediaID, err.Error())
			logger.WithUser(userObj.ID).Errorln(err)
			errorHandler.ReportToSentryWithoutRequest(err)
			return ars, err
		}
		docID, err := documents.GetID(constants.DocumentNamePAN, constants.DocCatPANCard)
		if err != nil {
			err = fmt.Errorf("documentID not found for docName: %s, docCat: %s, err: %s", constants.DocumentNamePAN, constants.DocCatAddressProof, err.Error())
			logger.WithUser(userObj.ID).Errorln(err)
			errorHandler.ReportToSentryWithoutRequest(err)
			return ars, err
		}
		err = loankycdetails.UpdateStatus(nil, loanApplication.ID.String(), constants.KYCDocStatusInactive, []string{constants.DocTypePANCard})
		if err != nil {
			err = fmt.Errorf("error setting documents inactive, loanApplicationID: %s, err: %s", loanApplication.ID.String(), err.Error())
			logger.WithUser(userObj.ID).Errorln(err)
			errorHandler.ReportToSentryWithoutRequest(err)
			return ars, err
		}
		loanKYCDetailsUniqueID := general.GetUUID()
		err = loankycdetails.Insert(nil, loankycdetails.LoanKYCDetailStruct{
			UniqueID:    loanKYCDetailsUniqueID,
			LoanID:      loanApplication.ID.String(),
			MediaID:     mediaObj.MediaID,
			DocType:     constants.DocTypePANCard,
			DocumentID:  docID,
			Status:      constants.KYCDocStatusUploaded,
			CreatedBy:   userObj.ID,
			BackMediaID: "",
			Name:        "",
			Identifier:  "",
		})
		if err != nil {
			err = fmt.Errorf("error inserting docType: %s, mediaID: %s for loanApplicationID: %s, err: %s", constants.DocTypePANCard, mediaID, loanApplication.ID.String(), err.Error())
			logger.WithUser(userObj.ID).Errorln(err)
			errorHandler.ReportToSentryWithoutRequest(err)
			return ars, err
		}
		kycDocs = lenderservice.KYCDocumentstructsDetails{
			LoanKYCDetailsID: loanKYCDetailsUniqueID,
			DocumentType:     constants.DocTypePANCard,
			Name:             "",
			Identifier:       "",
			Path:             "",
			MediaID:          mediaID,
			DocumentName:     constants.DocumentNamePAN,
			DocumentURL:      s3.GetPresignedURLS3(mediaObj.Path, 300),
		}
		reqData.KycDocuments = []*lenderservice.KYCDocumentstructsDetails{&kycDocs}
		eventType = constants.ActivityPanCardUploaded

	case constants.LisaUploadKYCIntentAddressProofRedirection:
		reqData.RedirectionURL = conf.BaseURL + "/v1/callback/lisa/kyc?status={{status}}&transaction_id={{transaction_id}}"
		eventType = constants.ActivityLenderThirdPartyLinkGenerated
		eventDescription = "address_proof"
		kycType = "KYC"
		delete(output, constants.OutputKeyKYCURLAndParams)

	case constants.LisaUploadKYCIntentVKYCRedirection:
		vkycTransactionID = general.GetUUID()
		reqData.RedirectionURL = conf.BaseURL + fmt.Sprintf("/v1/callback/lisa/vkyc/%s", vkycTransactionID)
		eventType = constants.ActivityLenderThirdPartyLinkGenerated
		eventDescription = "vkyc_redirection"
		kycType = "VKYC"
		data["vkycTransactionID"] = vkycTransactionID
		delete(output, constants.OutputKeyKYCURLAndParams)

	case constants.LisaUploadKYCIntentVKYCPRVRedirection:
		reqData.RedirectionURL = conf.BaseURL + "/v1/callback/lisa/vkyc?status={{status}}&transaction_id={{transaction_id}}"
		eventType = constants.ActivityLenderThirdPartyLinkGenerated
		eventDescription = "vkyc_redirection"
		kycType = "VKYC"
		delete(output, constants.OutputKeyKYCURLAndParams)

	default:
		err = fmt.Errorf("unknown intent passed %s", intent.(string))
		logger.WithUser(userObj.ID).Errorln(err)
		return ars, err
	}

	resp, err := lenderservice.UploadKYC(ctx, &reqData)
	if err != nil {
		logger.WithUser(userObj.ID).Errorln(err)
		return ars, err
	}

	switch intent {
	case constants.LisaUploadKYCIntentAddressProofRedirection:
		kycTransactionID = resp.TransactionID
		data["kycTransactionID"] = kycTransactionID
	case constants.LisaUploadKYCIntentVKYCPRVRedirection:
		vkycTransactionID = resp.TransactionID
		data["vkycTransactionID"] = vkycTransactionID
	case constants.LisaUploadKYCIntentVKYCRedirection:
		urlparts := strings.Split(resp.URL, "?")
		baseURL := urlparts[0]
		var params map[string]interface{}
		if len(urlparts) > 1 {
			queryParams := strings.Split(urlparts[1], "=")
			params = map[string]interface{}{
				queryParams[0]: queryParams[1],
			}
		}
		resp.URL = baseURL
		resp.Params = params

	}

	// save response in lender variables
	err = lendervariables.InsertOrUpdate(nil, lendervariables.LenderVariablesStructNullable{
		UserID:            userObj.ID,
		LenderID:          lenderID,
		KYCTransactionID:  sql.NullString{String: kycTransactionID, Valid: kycTransactionID != ""},
		VKYCTransactionID: sql.NullString{String: vkycTransactionID, Valid: vkycTransactionID != ""},
		Status:            sql.NullInt64{Int64: lendervariables.LenderVariableStatusActive, Valid: true},
	})

	if err != nil {
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	// store redirect URL and params in output map to be polled from frontend
	if general.InArr(intent.(string),
		[]string{constants.LisaUploadKYCIntentAddressProofRedirection,
			constants.LisaUploadKYCIntentVKYCPRVRedirection,
			constants.LisaUploadKYCIntentVKYCRedirection}) {

		if resp != nil {
			output[constants.OutputKeyKYCURLAndParams] = map[string]interface{}{
				"kycIntent":      intent.(string),
				"kycURL":         resp.URL,
				"kycType":        kycType,
				"redirectParams": resp.Params,
			}
		}
	}

	dateTimeNowString := general.GetTimeStampString()
	activityObj := finboxEvents.ActivityEvent{
		UserID:            userObj.ID,
		SourceEntityID:    userObj.SourceEntityID,
		LoanApplicationID: loanApplication.ID.String(),
		EntityType:        constants.EntityTypeCustomer,
		EventType:         eventType,
		Description:       eventDescription,
	}
	finboxEvents.RegisterEvent(&activityObj, dateTimeNowString)

	return ars, nil
}

// LisaGetKYCStatus is used to get the status of external KYC triggered via LISA
func (a *Activity) LisaGetKYCStatus(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	lenderID, ok := data["lenderID"].(string)
	if !ok {
		lenderID, _ = underwriting.GetLenderID(userObj.SourceEntityID, userObj.ID, "")
		data["lenderID"] = lenderID
	}

	var intent = "document"
	args := argument.GetActivityArguments(data)
	exists, intentArg := args.Get("intent", data)
	if exists {
		intent = intentArg.(string)
	}

	loanApplication, _ := loanapplication.GetLatestByUser(userObj.ID)
	loanApplicationID := loanApplication.ID.String()

	lendervariablesStruct, err := lendervariables.Get(userObj.ID, lenderID)
	if err != nil {
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	var transactionID string
	switch intent {
	case "prv":
		transactionID = lendervariablesStruct.VKYCTransactionID.String
	}

	var reqBody = lenderservice.ApplicationReq{
		UserID:            userObj.ID,
		LoanApplicationID: loanApplicationID,
		LenderID:          lenderID,
		Intent:            intent,
		TransactionID:     transactionID,
	}

	kycStatus, err := lenderservice.KYCStatus(ctx, &reqBody)
	if err != nil {
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	data["kycStatus"] = kycStatus.Status
	if len(kycStatus.KYCDocumentsStatus) > 0 {
		data["kycErroneousDocument"] = kycStatus.KYCDocumentsStatus[0].DocumentType
		data["kycErroneousDocumentMessage"] = kycStatus.KYCDocumentsStatus[0].ErrorMessage
	}
	if general.InArr(kycStatus.Status, []string{KreditBeeVKYCApproved, KreditBeeVKYCOfferDisabled}) {
		data["isVKYCCompleted"] = true
	}

	pollCountStorageKey := fmt.Sprintf("lisa_get_kyc_status_%s_poll_count", intent)
	data[pollCountStorageKey] = incrementCounterForKey(data, pollCountStorageKey)

	return ars, nil
}

// LisaCreateApplicant is used to create customer at lender's end
func (a *Activity) LisaCreateApplicant(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}
	userID := userObj.ID

	// Parse the arguments for activity
	var arguments struct {
		UpdateLenderVariables string `arg:"toUpdateLenderVariables" validate:"required"`
		UpdateUserCRM         string `arg:"toUpdateUserCRM" validate:"required"`
		SkipIfUserCRMExists   string `arg:"toSkipIfUserCRMExists" validate:"required"`
		LoanApplicationID     string `arg:"loanApplicationID" validate:"required"`
		CheckBusinessDate     string `arg:"checkBusinessDate"`
		Intent                string `arg:"intent"`
		LenderID              string `arg:"lenderID"`
		SkipLoanCheck         string `arg:"toSkipLoanCheck"`
	}
	err = GetArgs(&arguments, data)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithFields(errorHandler.ErrorFields{"workflowID": activityInfo.WorkflowExecution.ID, "runID": activityInfo.WorkflowExecution.RunID}, err)
		return ars, err
	}
	// Get LoanApplicationId from activity argument
	loanApplicationID := arguments.LoanApplicationID
	// Check if we need to update the CRM ID in DB
	toUpdateUserCRM := arguments.UpdateUserCRM == "true"
	// Check if we need to update the CRM ID in lender variables
	toUpdateLenderVariables := arguments.UpdateLenderVariables == "true"
	// Check if we need to skip the Create Applicant at lender end if User CRM is already created (avoids duplicate API call)
	toSkipIfUserCRMExists := arguments.SkipIfUserCRMExists == "true"
	// Check if we need to verify lender system business date
	checkLenderBusinessDate := arguments.CheckBusinessDate == "true"
	// Check if we can continue even if loan application is not found
	toSkipLoanCheck := arguments.SkipLoanCheck == "true"

	// using CRMID blank check as idempotency check for this action
	if toSkipIfUserCRMExists {
		// Check if CRM is already there for User
		crmID, err := users.GetUserCRM(ctx, userID)
		if err != nil {
			logger.WithUser(userID).Error(err)
			return ars, err
		}
		if crmID != "" {
			logger.WithUser(userID).Info("Skipping create applicant as it already exists for the user")
			return ars, nil
		}
	}

	// Check business date
	if checkLenderBusinessDate {
		// if current date is not business date we'll throw error from current execution and retry again
		_, err = lenderservice.CheckBusinessDate(ctx, userID, arguments.LoanApplicationID)
		if err != nil {
			logger.WithUser(userID).Error(err)
			return ars, err
		}
	}

	// Prepare the LISA request for Create Applicant
	applicationReq, err := lenderservice.GetApplicationReqByUserAndLoan(ctx, userID, loanApplicationID, &lenderservice.GetApplicationReqOptions{
		LenderID:      arguments.LenderID,
		SkipLoanCheck: toSkipLoanCheck,
	})
	if err != nil {
		logger.WithUser(userID).Error(err)
		return ars, err
	}

	applicationReq.Intent = arguments.Intent

	createApplicantResp, err := lenderservice.CreateApplicant(ctx, &applicationReq)
	if err != nil {
		logger.WithUser(userID).Error(err)
		if err.Error() == constants.ErrEnumLenderBREReject || err.Error() == constants.ErrEnumCustomerExposureExceeded {
			return ars, NewNonRetryableApplicationError(err.Error())
		}
		return ars, err
	}

	tx, err := database.Beginx()
	if err != nil {
		logger.WithUser(userID).Error(err)
		return ars, err
	}
	defer tx.Rollback()

	// Update CRM Id
	if toUpdateUserCRM {
		err = users.Update(tx, users.User{ID: userID, CrmID: createApplicantResp.CrmID})
		if err != nil {
			logger.WithUser(userID).Error(err)
			errorHandler.ReportToSentryWithoutRequest(fmt.Errorf("[LisaCreateApplicant] failed to update CRMID in Users table: userID: %s, err: %v", userID, err))
			return ars, err
		}
	}

	// Update lender variables
	if toUpdateLenderVariables {
		lenderVariablesNullable := lendervariables.LenderVariablesStructNullable{
			UserID:    userID,
			LenderID:  applicationReq.LenderID,
			LSQLeadID: sql.NullString{Valid: createApplicantResp.CrmID != "", String: createApplicantResp.CrmID},
		}
		err = lendervariables.Update(tx, lenderVariablesNullable)
		if err != nil {
			logger.WithUser(userID).Error(err)
			errorHandler.ReportToSentryWithoutRequest(fmt.Errorf("[LisaCreateApplicant] failed to update CRMID in Lender Variables table: userID: %s, err: %v", userID, err))
			return ars, err
		}
	}

	return ars, tx.Commit()
}

// LisaCreateApplication is used to create loan application at lender's end
func (a *Activity) LisaCreateApplication(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}
	userID := userObj.ID

	// Parse the arguments for activity
	var arguments struct {
		SkipIfOldLANUpdated string      `arg:"toSkipIfOldLANUpdated" required:"true"`
		UpdateUserProspect  string      `arg:"toUpdateUserProspect" required:"true"`
		UpdateLAN           string      `arg:"toUpdateLoanApplicationNumber" required:"true"`
		LoanApplicationID   string      `arg:"loanApplicationID" required:"true"`
		CheckBusinessDate   string      `arg:"checkBusinessDate"`
		UpdateCRMID         string      `arg:"toUpdateCRMID"`
		RejectLead          string      `arg:"rejectLead"`
		LogActivity         string      `arg:"toLogActivity"`
		Intent              string      `arg:"intent"`
		LenderID            string      `arg:"lenderID"`
		AdditionalData      interface{} `arg:"additionalData"`
		ToAddRespInData     string      `arg:"toAddRespInData"`
	}
	err = GetArgsV2(&arguments, data)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithFields(errorHandler.ErrorFields{"workflowID": activityInfo.WorkflowExecution.ID, "runID": activityInfo.WorkflowExecution.RunID}, err)
		return ars, err
	}

	// Get LoanApplicationId from activity argument
	loanApplicationID := arguments.LoanApplicationID
	// Check if we need to update the user prospect in DB
	toUpdateProspectNumber := arguments.UpdateUserProspect == "true"
	// Check if we need to update the loan application number in loan application table
	toUpdateLoanApplicationNumber := arguments.UpdateLAN == "true"
	// Check if we need to skip the Create Applicant at lender end if User CRM is already created (avoids duplicate API call)
	toSkipIfOldLANUpdated := arguments.SkipIfOldLANUpdated == "true"
	// Check if we need to verify lender system business date
	checkLenderBusinessDate := arguments.CheckBusinessDate == "true"
	// Check if we need to update the CRMID in DB
	toUpdateCRMID := arguments.UpdateCRMID == "true"
	// check if we need resp in data map
	toAddRespInData := arguments.ToAddRespInData == "true"

	toLogActivity := arguments.LogActivity == "true"
	// Check if lender name needs to be passed in event description

	toLogLender := arguments.LenderID != ""

	loanApp, err := loanapplication.Get(ctx, loanApplicationID)
	if err != nil {
		logger.WithUser(userID).Error(err)
		return ars, err
	}

	// using OldLoanApplicationNo check as idempotency check for this action
	if toSkipIfOldLANUpdated {
		// Check if old loan application is updated
		if loanApp.OldApplicationNo != "" {

			// Fetch lender variables, omit any errors
			if lv, err := lenderutil.GetLenderVariables(userID, loanApp.LenderID); err == nil {
				data["lenderDynamicVariables"] = lv
			}

			logger.WithUser(userID).Info("Skipping create application as old loan application no already updated ")
			return ars, nil
		}
	}

	// Check business date
	if checkLenderBusinessDate {
		// if current date is not business date we'll throw error from current execution and retry again
		_, err = lenderservice.CheckBusinessDate(ctx, userID, arguments.LoanApplicationID)
		if err != nil {
			logger.WithUser(userID).Error(err)
			return ars, err
		}
	}

	// Prepare the LISA request for Create Application
	applicationReq, err := lenderservice.GetApplicationReqByUserAndLoan(ctx, userID, loanApplicationID, nil)
	if err != nil {
		logger.WithUser(userID).Error(err)
		return ars, err
	}

	applicationReq.Intent = arguments.Intent
	applicationReq.AdditionalData = arguments.AdditionalData

	createApplicationResp, err := lenderservice.CreateApplication(ctx, &applicationReq)
	if err != nil {
		logger.WithUser(userID).Error(err)
		if toLogActivity {
			finboxEvents.ActivityLogger(userID, userObj.SourceEntityID, userID, constants.EntityTypeSystem, constants.ActivityLenderAPIFailed, "create application failed", loanApplicationID, general.GetTimeStampString(), false)
		}
		output["error"] = err
		return ars, err
	}

	tx, err := database.Beginx()
	if err != nil {
		logger.WithUser(userID).Error(err)
		return ars, err
	}
	defer tx.Rollback()

	// Update lender variables, omit any errors
	if lv, err := lenderutil.UpdateLenderVariables(userID, loanApplicationID, applicationReq.LenderID, createApplicationResp); err == nil {
		data["lenderDynamicVariables"] = lv
	}

	if createApplicationResp.CustomerSegment != "" {
		err = users.UpdateDynamicUserInfoFieldV3(userID, "customerSegment", createApplicationResp.CustomerSegment, nil)
		if err != nil {
			err = fmt.Errorf("failed to update trigger_kyc_wf in dynamic user info")
			logger.WithUser(userID).Errorln(err)
		}
	}

	// Update CRM Id
	if toUpdateProspectNumber || toUpdateCRMID {
		userUpdateObj := users.User{ID: userID}
		if toUpdateProspectNumber {
			userUpdateObj.ProspectNo = createApplicationResp.ProspectNo
		}
		if toUpdateCRMID {
			userUpdateObj.CrmID = createApplicationResp.CrmID
		}

		err = users.Update(tx, userUpdateObj)
		if err != nil {
			logger.WithUser(userID).Error(err)
			errorHandler.ReportToSentryWithoutRequest(fmt.Errorf("[LisaCreateApplication] failed to update prospect number in Users, userID: %s, err: %v", userID, err))
			return ars, err
		}
	}

	// Update lender variables
	if toUpdateLoanApplicationNumber {
		err = loanapplication.UpdateApplicationNumber(tx, loanApplicationID, createApplicationResp.LoanApplicationNumber)
		if err != nil {
			logger.WithUser(userID).Error(err)
			errorHandler.ReportToSentryWithoutRequest(fmt.Errorf("[LisaCreateApplication] failed to update loan application number, userID: %s, err: %v", userID, err))
			return ars, err
		}
	}
	var lenderVariablesNullable lendervariables.LenderVariablesStructNullable
	if createApplicationResp.LoanApplicationNumber != "" && applicationReq.LenderID == constants.RingMCID {
		lenderVariablesNullable = lendervariables.LenderVariablesStructNullable{
			UserID:      userID,
			LenderID:    applicationReq.LenderID,
			ReferenceID: sql.NullString{Valid: createApplicationResp.LoanApplicationNumber != "", String: createApplicationResp.LoanApplicationNumber},
		}
		err = lendervariables.Update(tx, lenderVariablesNullable)
		if err != nil {
			logger.WithUser(userID).Error(err)
			errorHandler.ReportToSentryWithoutRequest(fmt.Errorf("[LisaCreateApplication] failed to update Loan Applicantion Number in Lender Variables table: userID: %s, err: %v", userID, err))
			return ars, err
		}
	}
	err = tx.Commit()

	if err != nil {
		logger.WithUser(userID).Error(err)
		return ars, err
	}

	logger.WithUser(userID).Debugf("Create Application Response %+v", createApplicationResp)

	if createApplicationResp.Response.Status == "rejected" && createApplicationResp.Response.Message != "" {
		ars.Data["loan_status"] = "rejected"
		ars.Data["loan_status_remark"] = createApplicationResp.Message
	}

	if toLogActivity {
		description := ""
		if createApplicationResp.CrmID != "" {
			description += fmt.Sprintf(" CRM ID: %s", createApplicationResp.CrmID)
		}
		if createApplicationResp.ProspectNo != "" {
			description += fmt.Sprintf(" Prospect Number: %s", createApplicationResp.ProspectNo)
		}
		if createApplicationResp.LoanApplicationNumber != "" {
			description += fmt.Sprintf(" Loan Application Number: %s", createApplicationResp.LoanApplicationNumber)
		}
		if createApplicationResp.CustomerSegment != "" {
			description += fmt.Sprintf(" customerType: %s", createApplicationResp.CustomerSegment)
		}
		if toLogLender {
			description += fmt.Sprintf("\nLender: %s", constants.LenderNamesMap[arguments.LenderID])
		}
		finboxEvents.ActivityLogger(userID, userObj.SourceEntityID, userID, constants.EntityTypeSystem, constants.ActivityLeadPushedToLender, description, loanApplicationID, general.GetTimeStampString(), false)
	}
	if toAddRespInData {
		data["createAppResp"] = createApplicationResp
	}
	return ars, nil
}

// LisaUploadStandardKYCDocuments is used to upload standard documents to lisa with argument to customize the documents needed to be sent
func (a *Activity) LisaUploadStandardKYCDocuments(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}
	userID := userObj.ID

	// get activity arguments
	var arguments struct {
		LoanApplicationID       string `arg:"loanApplicationID" validate:"required"`
		UploadApprovedDocuments string `arg:"toUploadApprovedDocuments" validate:"required"`
		UploadSignedAgreement   string `arg:"toUploadSignedAgreement"`
		UploadBCDocuments       string `arg:"toUploadBCDocuments" validate:"required"`
		IgnoreUploadError       string `arg:"toIgnoreUploadError" validate:"required"`
		CheckBusinessDate       string `arg:"checkBusinessDate"`
		LogEvent                string `arg:"toLogEvent"`
		LenderID                string `arg:"lenderID"`
	}
	err = GetArgs(&arguments, data)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	// Get LoanApplicationId from activity argument
	loanApplicationID := arguments.LoanApplicationID
	// Check if we need to upload Approved documents
	toUploadApprovedDocuments := arguments.UploadApprovedDocuments == "true"
	// Check if we need to upload bank connect documents to lisa
	toUploadBCDocuments := arguments.UploadBCDocuments == "true"
	//Check if we need to upload signed agreement documents to lisa
	toUploadSignedAgreement := arguments.UploadSignedAgreement == "true"
	// Check if we need to ignore error from lender's Upload KYC API
	// this is needed specifically for Muthoot where lender's Upload Documents API fails unexpectedly
	toIgnoreUploadError := arguments.IgnoreUploadError == "true"
	// Check if we need to verify lender system business date
	checkLenderBusinessDate := arguments.CheckBusinessDate == "true"
	// Check if event needs to be logged
	toLogEvent := arguments.LogEvent == "true"
	// Check if lenderName should be there in description of log
	toLogLender := arguments.LenderID != ""

	// Check business date
	if checkLenderBusinessDate {
		// if current date is not business date we'll throw error from current execution and retry again
		_, err = lenderservice.CheckBusinessDate(ctx, userID, arguments.LoanApplicationID)
		if err != nil {
			logger.WithUser(userID).Error(err)
			return ars, err
		}
	}

	lisaKYCDocs := []*lenderservice.KYCDocumentstructsDetails{}

	if toUploadApprovedDocuments {
		kycDocs, err := misc.GetApprovedKYC(ctx, loanApplicationID, "")
		if err != nil {
			logger.WithUser(userID).Error(err)
			errorHandler.ReportToSentryWithoutRequest(fmt.Errorf("[LisaUploadStandardKYCDocuments] failed to get approved KYC documents, userID: %s, err: %v", userID, err))
			return ars, err
		}
		lisaKYCDocs = append(lisaKYCDocs, kycDocs...)
	}

	if toUploadSignedAgreement {
		signURL, signURLErr := loanapplication.GetSignURL(loanApplicationID)

		if signURLErr != nil || signURL == "" {
			if signURL == "" {
				signURLErr = errors.New("signURL is empty")
			}
			logger.WithLoanApplication(loanApplicationID).Error(signURLErr)
		}

		preSignedURL := s3.GetPresignedURLS3(signURL, 300)

		lisaKYCDocs = append(lisaKYCDocs, &lenderservice.KYCDocumentstructsDetails{DocumentName: "SIGN_AGREEMENT", DocumentURL: preSignedURL, Path: signURL})

	}

	if toUploadBCDocuments {
		bankStatementDoc, err := misc.GetBankConnectStatement(userID)
		if err != nil {
			logger.WithUser(userID).Error(err)
			errorHandler.ReportToSentryWithoutRequest(fmt.Errorf("[LisaUploadStandardKYCDocuments] failed to get BC documents, userID: %s, err: %v", userID, err))
			return ars, err
		}
		lisaKYCDocs = append(lisaKYCDocs, bankStatementDoc...)
	}

	// Prepare the LISA request for Create Application
	applicationReq, err := lenderservice.GetApplicationReqByUserAndLoan(ctx, userID, loanApplicationID, nil)
	if err != nil {
		logger.WithUser(userID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(fmt.Errorf("[LisaUploadStandardKYCDocuments] failed to create lisa Application request, userID: %s, err: %v", userID, err))
		return ars, err
	}

	req := lenderservice.UploadDocumentsReq{
		ApplicationReq: applicationReq,
		KycDocuments:   lisaKYCDocs,
	}
	_, err = lenderservice.UploadKYC(ctx, &req)
	if err != nil {
		logger.WithUser(userID).Error(err)
		if toLogEvent {
			finboxEvents.ActivityLogger(userID, userObj.SourceEntityID, "", constants.EntityTypeSystem, constants.ActivityLenderAPIFailed, "upload docs failed", loanApplicationID, general.GetTimeStampString(), false)
		}
		if !toIgnoreUploadError {
			return ars, err
		}
	}
	description := ""
	if toLogLender {
		description = fmt.Sprintf("Lender: %s", constants.LenderNamesMap[arguments.LenderID])
	}
	finboxEvents.ActivityLogger(userID, userObj.SourceEntityID, "", constants.EntityTypeSystem, constants.ActivityDocsPushedToLender, description, loanApplicationID, general.GetTimeStampString(), false)

	return ars, nil
}

// LisaGetOffer is used to fetch offer for a user for a particular lender
func (a *Activity) LisaGetOffer(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	lenderID, ok := data["lenderID"].(string)
	if !ok {
		lenderID, _ = underwriting.GetLenderID(userObj.SourceEntityID, userObj.ID, "")
		data["lenderID"] = lenderID
	}

	type argumesStruct struct {
		Intent                         string `arg:"intent" required:"true"`
		LoanType                       string `arg:"loanType" required:"true"`
		NullOfferRejection             string `arg:"nullOfferRejection"`
		PersonalLoanOfferType          string `arg:"personalLoanOfferType"`
		PersonalLoanOfferStatus        int    `arg:"personalLoanOfferStatus"`
		EarlyReturnOnPendingOrRejected bool   `arg:"earlyReturnOnPendingOrRejected"`
		EventType                      string `arg:"eventType"`
	}
	var args argumesStruct
	err = GetArgsV2(&args, data)
	if err != nil {
		logger.WithUser(userObj.ID).Errorln(err)
		return ars, err
	}

	eventType := constants.ActivityOfferGenerated
	offerType := constants.OfferTypeFinal
	personalLoanOfferStatus := constants.OfferStatusSelected
	if args.EventType != "" {
		eventType = args.EventType
	}

	reqData := lenderservice.GetOfferReq{
		Intent: args.Intent,
		ApplicationReq: lenderservice.ApplicationReq{
			UserID:         userObj.ID,
			LenderID:       lenderID,
			SourceEntityID: userObj.SourceEntityID,
		},
	}

	res, err := lenderservice.GetOffers(ctx, &reqData)
	if err != nil {
		logger.WithUser(userObj.ID).Errorln(err)
		return ars, err
	}

	data["LisaOfferStatus"] = res.Status
	if args.EarlyReturnOnPendingOrRejected && (res.Status == "pending" || res.Status == "rejected") {
		data["getLisaOfferResponse"] = res
		logger.WithUser(userObj.ID).Infoln("Getting offer message: ", res.Message)
		logger.WithUser(userObj.ID).Infoln("Getting offer status: ", res.Status)
		return ars, nil
	}

	if len(res.Offers) < 1 && args.NullOfferRejection != "" {
		data["nullOfferRejection"] = args.NullOfferRejection
		return ars, nil
	} else if len(res.Offers) < 1 {
		err = fmt.Errorf("no offers found")
		return ars, err
	}

	switch args.LoanType {
	case constants.LoanTypePersonalLoan:
		var metadataStr string
		var metadata map[string]interface{}

		if res.Offers[0].StepperData != nil {
			metadata = map[string]interface{}{
				"stepperData":        res.Offers[0].StepperData,
				"enhancedLoanAmount": res.Offers[0].EnhancedLoanAmount,
				"showEnhancedAmount": (res.Offers[0].EnhancedLoanAmount > 0.0),
			}
			bytes, _ := json.Marshal(metadata)
			metadataStr = string(bytes)
		}

		if res.Offers[0].MaxTenure == 0 {
			maxTenureFromStepperData, err := res.Offers[0].FetchMaxTenureFromStepperData(lenderID)
			if err != nil {
				err = fmt.Errorf("error getting maxTenure from stepperData, userID: %s, err: %s", userObj.ID, err.Error())
				logger.WithUser(userObj.ID).Errorln(err)
				errorHandler.ReportToSentryWithoutRequest(err)
				return ars, err
			}
			res.Offers[0].MaxTenure = maxTenureFromStepperData
		}

		res.Offers[0].OfferType = args.Intent
		offerMetadataBytes, _, _, _ := underwriting.TransposeOfferToOfferStructByLender(underwriting.TransposeFuncStruct{
			LenderOffer:               res.Offers[0],
			SourceEntityID:            userObj.SourceEntityID,
			LenderID:                  lenderID,
			PreTransposeOfferMetaData: metadata,
			UserID:                    userObj.ID,
		})
		metadataStr = string(offerMetadataBytes)

		if args.PersonalLoanOfferType != "" {
			offerType = args.PersonalLoanOfferType
		}
		if args.PersonalLoanOfferStatus != 0 {
			personalLoanOfferStatus = args.PersonalLoanOfferStatus
		}

		offerID, err := personalloanoffer.Create(nil, userObj.ID, userObj.SourceEntityID, lenderID, general.GetUUID(), res.Offers[0].MaxAmount, res.Offers[0].MinAmount,
			res.Offers[0].InterestRate, res.Offers[0].MaxTenure, res.Offers[0].MinTenure, offerType, constants.MethodReducingBalance, res.Offers[0].ProcessingFee,
			res.Offers[0].ProcessingFeeType, "", metadataStr, personalLoanOfferStatus)
		if err != nil {
			logger.WithUser(userObj.ID).Error(err)
			return ars, err
		}
		offerExpiryDays := constants.LenderOfferExpiryMapping[lenderID]
		if offerExpiryDays != 0 {
			expiryType := "offer_" + lenderID
			eventType := "offer_generated_" + lenderID
			offerExpiryDate := time.Now().AddDate(0, 0, offerExpiryDays)
			_, err := expiry.GetIDByUserIDAndExpiryType(userObj.ID, expiryType)

			if err != nil && err != sql.ErrNoRows {
				logger.WithUser(userObj.ID).Errorln(err)
				return ars, err
			}

			if err == sql.ErrNoRows {
				err = expiry.InsertV2(userObj.ID, expiryType, eventType, offerExpiryDate, offerID)
				if err != nil {
					logger.WithUser(userObj.ID).Error(err)
					return ars, err
				}
			} else {
				err = expiry.UpdateExpiryByUserIDAndType(ctx, userObj.ID, expiryType, eventType, offerExpiryDate, offerID, nil)
				if err != nil {
					logger.WithUser(userObj.ID).Error(err)
					return ars, err
				}
			}
		}

		if args.Intent == "hard" {
			data["isHardOfferShown"] = true
		}
	default:
		errorHandler.ReportToSentryWithoutRequest(fmt.Errorf("incorrect offer type %s recieved for userID: %s", args.LoanType, userObj.ID))
	}
	description := fmt.Sprintf(`{"interest": %.2f, "tenure": %d,"amount": %.2f,"lender":"%s","processingFee":"%.2f","offerType":"%s"}`, res.Offers[0].InterestRate, res.Offers[0].MaxTenure, res.Offers[0].MaxAmount, constants.LenderNamesMap[lenderID], res.Offers[0].ProcessingFee, res.Offers[0].OfferType)
	dateTimeNowString := general.GetTimeStampString()
	activityObj := finboxEvents.ActivityEvent{
		UserID:         userObj.ID,
		SourceEntityID: userObj.SourceEntityID,
		EntityType:     constants.EntityTypeCustomer,
		EventType:      eventType,
		EntityRef:      args.Intent,
		Description:    description,
	}
	finboxEvents.RegisterEvent(&activityObj, dateTimeNowString)

	return ars, nil
}

func (a *Activity) LisaGetOffersV2(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	var arguments struct {
		LenderID        string `arg:"lenderID" validate:"required"`
		Intent          string `arg:"intent" validate:"required"`
		OfferType       string `arg:"offerType"`
		EnableTranspose string `arg:"enableTranspose"`
		ToLogFBxEvent   string `arg:"toLogFBxEvent"`
	}
	err = GetArgs(&arguments, data)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithFields(errorHandler.ErrorFields{"workflowID": activityInfo.WorkflowExecution.ID, "runID": activityInfo.WorkflowExecution.RunID}, err)
		return ars, err
	}

	toEnableTranspose := arguments.EnableTranspose == "true"
	toLogFBxEvent := arguments.ToLogFBxEvent == "true"

	defer func() {
		if err != nil && toLogFBxEvent {
			dateTimeNowString := general.GetTimeStampString()
			activityObj := finboxEvents.ActivityEvent{
				UserID:         userObj.ID,
				SourceEntityID: userObj.SourceEntityID,
				EntityType:     constants.EntityTypeCustomer,
				EntityRef:      constants.EntityTypeSystem,
				EventType:      constants.ActivityOfferGenerationFailed,
				Description:    "",
			}
			finboxEvents.RegisterEvent(&activityObj, dateTimeNowString)
		}
	}()

	offerResponse, err := lenderservice.GetOffers(ctx, &lenderservice.GetOfferReq{
		ApplicationReq: lenderservice.ApplicationReq{
			UserID:         userObj.ID,
			SourceEntityID: userObj.SourceEntityID,
			LenderID:       arguments.LenderID,
			Intent:         arguments.Intent,
		},
		Intent: arguments.Intent,
	})
	if err != nil {
		logger.WithUser(userObj.ID).Errorln(err)
		return ars, err
	}
	if offerResponse == nil || offerResponse.Offers == nil || len(offerResponse.Offers) == 0 {
		logger.WithUser(userObj.ID).Errorln("offer not present")
		return ars, errors.New("offer not present")
	}
	if arguments.OfferType == "" {
		arguments.OfferType = constants.OfferTypeFinal
	}
	offerDetails := offerResponse.Offers[0]
	data["isEligible"] = strconv.FormatBool(offerDetails.OfferEligibility == "YES")
	data["offerType"] = offerDetails.OfferType
	loanType, _, _ := journey.GetLoanType(userObj.SourceEntityID)
	var offerID string
	var noOffersReason string
	if offerDetails.OfferEligibility == "YES" {
		var offerMetadataBytes []byte
		var offerExpiry time.Time
		if offerDetails.OfferValidity != "" {
			offerExpiry, err = time.Parse("01/02/2006", offerDetails.OfferValidity)
			if err != nil {
				logger.WithUser(userObj.ID).Errorln(err)
				errorHandler.ReportToSentryWithoutRequest(err)
				return ars, err
			}
			err = expiry.Insert(userObj.ID, offerExpiry)
			if err != nil {
				logger.WithUser(userObj.ID).Errorln(err)
				errorHandler.ReportToSentryWithoutRequest(err)
				return ars, err
			}
		}
		if toEnableTranspose {
			offerMetadataBytes, _, _, err = underwriting.TransposeOfferToOfferStructByLender(underwriting.TransposeFuncStruct{
				LenderID:       arguments.LenderID,
				UserID:         userObj.ID,
				SourceEntityID: userObj.SourceEntityID,
				LenderOffer:    offerDetails,
			})
			if err != nil {
				logger.WithUser(userObj.ID).Errorln(err)
				errorHandler.ReportToSentryWithoutRequest(err)
				return ars, err
			}
		}
		switch loanType {
		case constants.LoanTypePersonalLoan:
			offerID, err = personalloanoffer.Create(nil, userObj.ID, userObj.SourceEntityID, arguments.LenderID, "", offerDetails.Amount, 0.0, offerDetails.InterestRate, offerDetails.Tenure, 0, arguments.OfferType, "rb", offerDetails.ProcessingFee, "PERC", offerDetails.OfferType, string(offerMetadataBytes), constants.OfferStatusActive)
			if err != nil {
				logger.WithUser(userObj.ID).Error(err)
				errorHandler.ReportToSentryWithoutRequest(err)
				return ars, err
			}
			//TOOD: Add implementation of making an entry in business_loan_offer
		}
		if toLogFBxEvent {
			dateTimeNowString := general.GetTimeStampString()
			activityObj := finboxEvents.ActivityEvent{
				UserID:         userObj.ID,
				SourceEntityID: userObj.SourceEntityID,
				EntityType:     constants.EntityTypeSystem,
				EventType:      constants.ActivityOfferGenerated,
				Description:    fmt.Sprintf(`{"amount": %.2f, "roi": %.2f, "tenure": %d, "type": "%s", "lender": "%s"}`, offerDetails.Amount, offerDetails.InterestRate, offerDetails.Tenure, arguments.OfferType, constants.LenderNamesMap[arguments.LenderID]),
			}
			finboxEvents.RegisterEvent(&activityObj, dateTimeNowString)
		}
	} else {
		noOffersReason = offerDetails.Message
		if toLogFBxEvent {
			dateTimeNowString := general.GetTimeStampString()
			activityObj := finboxEvents.ActivityEvent{
				UserID:         userObj.ID,
				SourceEntityID: userObj.SourceEntityID,
				EntityType:     constants.EntityTypeSystem,
				EventType:      constants.ActivityNoOfferGenerated,
				Description:    noOffersReason,
			}
			finboxEvents.RegisterEvent(&activityObj, dateTimeNowString)
		}
	}
	data["offerID"] = offerID
	data["noOffersReason"] = noOffersReason

	return ars, nil
}

// LisaGenerateOTP is used to send otp to the user from lender side
func (a *Activity) LisaGenerateOTP(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	lenderID, ok := data["lenderID"].(string)
	if !ok {
		lenderID, _ = underwriting.GetLenderID(userObj.SourceEntityID, userObj.ID, "")
		data["lenderID"] = lenderID
	}

	reqBody := lenderservice.GenerateOtpReq{
		ApplicationReq: lenderservice.ApplicationReq{
			UserID:   userObj.ID,
			LenderID: lenderID,
		},
		Mobile: userObj.Mobile,
	}

	res, err := lenderservice.GenerateOtp(ctx, &reqBody)
	if err != nil {
		logger.WithUser(userObj.ID).Errorln(err)
		return ars, err
	}

	if res.ErrorMessage != "" {
		output["otp_verification_status"] = map[string]interface{}{
			"otpVerified":     false,
			"otpErrorMessage": res.ErrorMessage,
		}
	}

	// remove older verification values
	output["otp_verification_status"] = nil

	return ars, nil
}

// LisaSubmitOTP is used to authenticate otp at lender's side
func (a *Activity) LisaSubmitOTP(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	userID := userObj.ID
	lenderID, ok := data["lenderID"].(string)
	if !ok {
		lenderID, _ = underwriting.GetLenderID(userObj.SourceEntityID, userObj.ID, "")
		data["lenderID"] = lenderID
	}

	args := argument.GetActivityArguments(data)
	exists, otp := args.Get("otp", data)
	if !exists {
		err = fmt.Errorf("otp not found in args")
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	reqBody := lenderservice.AuthenticateOTPReq{
		ApplicationReq: lenderservice.ApplicationReq{
			UserID:   userID,
			LenderID: lenderID,
		},
		OTP: otp.(string),
	}

	res, err := lenderservice.AuthenticateOtp(ctx, &reqBody)
	if err != nil {
		logger.WithUser(userObj.ID).Errorln(err)
		return ars, err
	}

	otpVerified := res.OtpVerified
	data["otpVerified"] = otpVerified

	var OTPErrorMessage string
	if !otpVerified {
		OTPErrorMessage = "Incorrect OTP"
	} else {
		loanApplication, _ := loanapplication.GetLatestByUser(userID)
		dateTimeNowString := general.GetTimeStampString()
		activityObj := finboxEvents.ActivityEvent{
			UserID:            userObj.ID,
			SourceEntityID:    userObj.SourceEntityID,
			LoanApplicationID: loanApplication.ID.String(),
			EntityType:        constants.EntityTypeCustomer,
			EventType:         constants.ActivityKfsAgreementCompleted,
			Description:       "",
		}
		finboxEvents.RegisterEvent(&activityObj, dateTimeNowString)
	}

	output["otp_verification_status"] = map[string]interface{}{
		"otpVerified":     otpVerified,
		"otpErrorMessage": OTPErrorMessage,
	}

	return ars, nil
}

// LisaGetAgreement is used to get agreement doc/url for agreement from lender
func (a *Activity) LisaGetAgreement(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	userID := userObj.ID

	loanApplicationData, err := loanapplication.GetLatestByUser(userID)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	data["lenderID"] = loanApplicationData.LenderID
	loanApplicationID := loanApplicationData.ID.String()

	var reqBody = lenderservice.EsignReq{
		ApplicationReq: lenderservice.ApplicationReq{
			UserID:            userID,
			LenderID:          loanApplicationData.LenderID,
			LoanApplicationID: loanApplicationID,
		},
	}

	resp, err := lenderservice.GetEsignRequest(ctx, &reqBody)
	if err != nil {
		logger.WithUser(userObj.ID).Errorln(err)
		data["esignError"] = err.Error()
		return ars, err
	}

	if resp.EsignURL == "" {
		err = errors.New("esign url not found")
		logger.WithUser(userID).Errorln(err)
		return ars, err
	}

	//update unsigned url link in loanApplication table
	err = loanapplication.Update(nil, loanapplication.StructForSet{
		ID:        loanApplicationID,
		UnsignURL: resp.EsignURL,
	})
	if err != nil {
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	dateTimeNowString := general.GetTimeStampString()
	loanApplication, _ := loanapplication.GetLatestByUser(userID)
	activityObj := finboxEvents.ActivityEvent{
		UserID:            userObj.ID,
		SourceEntityID:    userObj.SourceEntityID,
		LoanApplicationID: loanApplication.ID.String(),
		EntityType:        constants.EntityTypeCustomer,
		EventType:         constants.ActivityKfsAgreementStarted,
		Description:       "",
	}
	finboxEvents.RegisterEvent(&activityObj, dateTimeNowString)

	output["agreementData"] = map[string]interface{}{
		"url":             resp.EsignURL,
		"disbursalAmount": resp.DisbursalAmount,
		"interestRate":    resp.InterestRate,
		"tenure":          resp.Tenure,
		"amountPayable":   resp.PayableAmount,
		"agreementFee":    resp.AgreementFee,
		"apr":             resp.AnnualPercentageRate,
		"allFees":         resp.AllFee,
		"processingFee":   resp.ProcessingFee,
	}
	return ars, nil
}

func (a *Activity) LisaEsign(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	userID := userObj.ID
	lenderID, ok := data["lenderID"].(string)
	if !ok {
		lenderID, _ = underwriting.GetLenderID(userObj.SourceEntityID, userObj.ID, "")
		data["lenderID"] = lenderID
	}

	args := argument.GetActivityArguments(data)

	loanApplicationData, err := loanapplication.GetLatestByUser(userID)
	if err != nil {
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	loanApplicationID := loanApplicationData.ID.String()

	exists, docType := args.Get("docType", data)
	if !exists {
		err = fmt.Errorf("doc type not found in args")
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	exists, lat := args.Get("lat", data)
	if !exists {
		err = fmt.Errorf("latitude not found in args")
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	exists, lon := args.Get("lon", data)
	if !exists {
		err = fmt.Errorf("longitude not found in args")
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	exists, ipAddress := args.Get("ipAddr", data)
	if !exists {
		err = fmt.Errorf("ipAddress not found in args")
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	legallogs.Save(userObj.ID, "KFS", lat.(string), lon.(string), "", "", ipAddress.(string))

	kycDocStatus := constants.KYCDocStatusUploaded
	docs, err := loankycdetails.GetLatestDetailType(loanApplicationID, docType.(string), &kycDocStatus)
	if err != nil {
		logger.WithUser(userObj.ID).Errorln("error fetching selfie:", err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	mediaSelfieDoc, err := media.Get(ctx, docs.MediaID)
	if err != nil {
		logger.WithUser(userObj.ID).Errorln("error fetching media doc:", err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	kycDoc := lenderservice.KYCDocumentstructsDetails{
		LoanKYCDetailsID: docs.UniqueID,
		DocumentType:     docs.DocType,
		Name:             docs.Name,
		Identifier:       docs.Identifier,
		Path:             "",
		MediaID:          docs.MediaID,
		DocumentName:     "esign",
		DocumentURL:      s3.GetPresignedURLS3(mediaSelfieDoc.Path, 3600),
	}

	var reqBody = lenderservice.EsignReq{
		ApplicationReq: lenderservice.ApplicationReq{
			UserID:            userID,
			LenderID:          lenderID,
			LoanApplicationID: loanApplicationID,
		},
		KYCDocumentstructsDetails: &kycDoc,
	}

	_, err = lenderservice.Esign(ctx, &reqBody)
	if err != nil {
		if err.Error() == constants.ErrSelfieReupload {
			data["reUploadSelfie"] = true
			return ars, nil
		}
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	loanApplication, _ := loanapplication.GetLatestByUser(userID)
	dateTimeNowString := general.GetTimeStampString()

	status := constants.LoanStatusESign
	err = loanapplication.Update(nil, loanapplication.StructForSet{
		ID:     loanApplicationID,
		Status: &status,
	})

	if err != nil {
		logger.WithUser(userID).Errorln("Error updating loanapplication status:", err)
		errorHandler.ReportToSentryWithoutRequest(fmt.Errorf("error updating loanapplication status for userID: %s, err: %s", userID, err.Error()))
		return ars, err
	}

	activityObj := finboxEvents.ActivityEvent{
		UserID:            userObj.ID,
		SourceEntityID:    userObj.SourceEntityID,
		LoanApplicationID: loanApplication.ID.String(),
		EntityType:        constants.EntityTypeCustomer,
		EventType:         constants.ActivityLoanESigned,
		Description:       "",
	}
	finboxEvents.RegisterEvent(&activityObj, dateTimeNowString)

	return ars, nil
}

func (a *Activity) LisaGetBankAccount(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}
	userID := userObj.ID

	loanApplicationData, err := loanapplication.GetLatestByUser(userID)
	if err != nil {
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}
	loanApplicationID := loanApplicationData.ID.String()

	var reqBody = lenderservice.BankAccountReq{
		ApplicationReq: lenderservice.ApplicationReq{
			UserID:            userID,
			LenderID:          loanApplicationData.LenderID,
			LoanApplicationID: loanApplicationID,
		},
	}

	lenderserviceResp, err := lenderservice.GetBankAccounts(ctx, &reqBody)
	if err != nil {
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	verifiedBankAccounts := lenderserviceResp.GetVerifiedAccounts()

	bankAccounts := make([]map[string]interface{}, 0)
	for i := 0; i < len(verifiedBankAccounts); i++ {
		bankAccounts = append(bankAccounts, map[string]interface{}{
			"bankID":        verifiedBankAccounts[i].ID,
			"bankName":      verifiedBankAccounts[i].BankName,
			"accountNumber": verifiedBankAccounts[i].AccountNumber,
			"ifscCode":      "",
		})
	}

	data["numVerifiedAccounts"] = len(bankAccounts)

	output["existingVerifiedBankAccounts"] = map[string]interface{}{
		"numAccounts":  len(bankAccounts),
		"bankAccounts": bankAccounts,
	}

	return ars, nil
}

func (a *Activity) LisaAddBankAccount(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}
	userID := userObj.ID

	loanApplicationData, err := loanapplication.GetLatestByUser(userID)
	if err != nil {
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}
	loanApplicationID := loanApplicationData.ID.String()

	var bankID string
	var bankName string
	var accountNumber string
	var ifscCode string

	args := argument.GetActivityArguments(data)

	bankIDexists, bankIDArg := args.Get("bankID", data)
	if bankIDexists {
		bankID = general.RemoveExtraSpaces(bankIDArg.(string))
	}

	bankNameexists, bankNameArg := args.Get("bankName", data)
	if bankNameexists {
		bankName = general.RemoveExtraSpaces(bankNameArg.(string))
	}

	accountNumberExists, accountNumberArg := args.Get("accountNumber", data)
	if accountNumberExists {
		accountNumber = general.RemoveExtraSpaces(accountNumberArg.(string))
	}

	ifscCodeExists, ifscCodeArg := args.Get("ifscCode", data)
	if ifscCodeExists {
		ifscCode = general.RemoveExtraSpaces(ifscCodeArg.(string))
	}

	logger.WithUser(userID).Debugf("====> bankID: %s, bankName: %s, accountNumber: %s, ifscCode: %s", bankID, bankName, accountNumber, ifscCode)

	var reqBody = lenderservice.BankAccountReq{
		ApplicationReq: lenderservice.ApplicationReq{
			UserID:            userID,
			LenderID:          loanApplicationData.LenderID,
			LoanApplicationID: loanApplicationID,
		},
		AccountNumber: accountNumber,
		BankID:        bankID,
		BankName:      bankName,
		IfscCode:      ifscCode,
	}

	res, err := lenderservice.CreateBankAccount(ctx, &reqBody)
	if err != nil {
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	data["pennydropStatus"] = res.Status
	if res.Status == "verified" {
		loanApplication, _ := loanapplication.GetLatestByUser(userID)
		dateTimeNowString := general.GetTimeStampString()
		activityObj := finboxEvents.ActivityEvent{
			UserID:            userObj.ID,
			SourceEntityID:    userObj.SourceEntityID,
			LoanApplicationID: loanApplication.ID.String(),
			EntityType:        constants.EntityTypeCustomer,
			EventType:         constants.ActivityBankDetailsVerified,
			Description:       "",
		}
		finboxEvents.RegisterEvent(&activityObj, dateTimeNowString)
	}
	// TODO: status to be updated to 2 on confirmation from lisa
	// Add change at lisa end to pick the latest bank account details

	return ars, nil
}

// LisaGetBankingStatus is used to get banking status from lender
func (a *Activity) LisaGetBankingStatus(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {

	userObj, span, ars, err := initActivity(ctx, a.Tracer, data, output)
	if err != nil {
		logger.WithWorkflow(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}
	defer func() {
		span.Finish(err)
	}()
	type argumentStruct struct {
		UserID           string                          `arg:"userID" required:"true"`
		LenderID         string                          `arg:"lenderID" required:"true"`
		BankingStatusReq *lenderservice.BankingStatusReq `arg:"applicationReq" required:"true"`
	}

	var args argumentStruct
	err = GetArgsV2(&args, data)
	if err != nil {
		logger.WithUser(args.UserID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": args.UserID}, err)
		return ars, err
	}

	resp, err := lenderservice.GetBankingStatus(ctx, args.BankingStatusReq)
	if err != nil {
		logger.WithUser(args.UserID).Error(err)
		if !strings.Contains(err.Error(), constants.ErrIncredBankingStatusNotFound) {
			errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": args.UserID}, err)
		}
		return ars, err
	}
	if resp == nil {
		err = fmt.Errorf("response is nil")
		logger.WithUser(args.UserID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": args.UserID}, err)
		return ars, err
	}

	data["bankingStatusResp"] = resp

	return ars, nil
}

func (a *Activity) LisaBookLoan(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	userID := userObj.ID
	lenderID, ok := data["lenderID"].(string)
	if !ok {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, fmt.Errorf("lenderID not found in data")
	}

	loanApplicationData, err := loanapplication.GetLatestByUser(userID)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	loanApplicationID := loanApplicationData.ID.String()

	var reqBody = lenderservice.ApplicationReq{
		UserID:            userID,
		LenderID:          lenderID,
		LoanApplicationID: loanApplicationID,
	}

	resp, err := lenderservice.ApproveApplication(ctx, &reqBody)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		if general.InArr(err.Error(), []string{constants.ErrFraudUser, constants.ErrActiveLoanAlreadyExists}) {
			data["rejectUser"] = true
			data["rejectReason"] = err.Error()
			return ars, nil
		}
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	err = loanapplication.Update(nil, loanapplication.StructForSet{
		ID:                loanApplicationID,
		LoanApplicationNo: resp.LoanApplicationNumber,
	})
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	return ars, nil
}

func (a *Activity) LisaLoanDetails(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	userID := userObj.ID
	lenderID, ok := data["lenderID"].(string)
	if !ok {
		lenderID, _ = underwriting.GetLenderID(userObj.SourceEntityID, userObj.ID, "")
		data["lenderID"] = lenderID
	}

	loanApplicationData, err := loanapplication.GetLatestByUser(userID)
	if err != nil {
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	data["existingLoanApplicationStatus"] = loanApplicationData.Status

	loanApplicationID := loanApplicationData.ID.String()

	var reqBody = lenderservice.ApplicationReq{
		UserID:            userID,
		LenderID:          lenderID,
		LoanApplicationID: loanApplicationID,
		LoanApplicationNo: loanApplicationData.LoanApplicationNo,
	}

	res, err := lenderservice.ApplicationDetails(ctx, &reqBody)
	if err != nil {
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	data["lisaLoanDetails"] = res

	return ars, nil
}

func (a *Activity) LisaRepayment(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	args := argument.GetActivityArguments(data)
	_, repaymentType := args.Get("repaymentType", data)

	userID := userObj.ID
	lenderID, ok := data["lenderID"].(string)
	if !ok {
		lenderID, _ = underwriting.GetLenderID(userObj.SourceEntityID, userObj.ID, "")
		data["lenderID"] = lenderID
	}

	loanApplicationData, err := loanapplication.GetLatestByUser(userID)
	if err != nil {
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	loanApplicationID := loanApplicationData.ID.String()

	var reqBody = lenderservice.RepaymentReq{
		ApplicationReq: lenderservice.ApplicationReq{
			UserID:            userID,
			LenderID:          lenderID,
			LoanApplicationID: loanApplicationID,
			LoanApplicationNo: loanApplicationData.LoanApplicationNo,
		},
		RepaymentType: repaymentType.(string),
	}

	res, err := lenderservice.Repayment(ctx, &reqBody)
	if err != nil {
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	dateTimeNowString := general.GetTimeStampString()
	loanApplication, _ := loanapplication.GetLatestByUser(userID)
	activityObj := finboxEvents.ActivityEvent{
		UserID:            userObj.ID,
		SourceEntityID:    userObj.SourceEntityID,
		LoanApplicationID: loanApplication.ID.String(),
		EntityType:        constants.EntityTypeCustomer,
		EventType:         constants.ActivityLenderThirdPartyLinkGenerated,
		Description:       "repayment",
	}
	finboxEvents.RegisterEvent(&activityObj, dateTimeNowString)

	output["paymentLink"] = map[string]interface{}{
		"url": res.RedirectionURL,
	}

	return ars, nil
}

func (a *Activity) LisaRepaymentV2(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var arguments struct {
		UserID            string `arg:"userID" validate:"required"`
		LoanApplicationNo string `arg:"loanApplicationNo" validate:"required"`
		LoanApplicationID string `arg:"loanApplicationID" validate:"required"`
		LenderID          string `arg:"lenderID" validate:"required"`
		VendorPaymentID   string `arg:"vendorPaymentID" validate:"required"`
		Program           string `arg:"program" validate:"required"`
		Submode           string `arg:"submode"`
		SourceEntityID    string `arg:"sourceEntityID" validate:"required"`
	}
	err = GetArgs(&arguments, data)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	res, err := lenderservice.Repayment(ctx, &lenderservice.RepaymentReq{
		ApplicationReq: lenderservice.ApplicationReq{
			UserID:            arguments.UserID,
			SourceEntityID:    arguments.SourceEntityID,
			LenderID:          arguments.LenderID,
			LoanApplicationID: arguments.LoanApplicationID,
			LoanApplicationNo: arguments.LoanApplicationNo,
			LenderSystem:      arguments.Program,
			VendorPaymentID:   arguments.VendorPaymentID,
		},
		SubMode: arguments.Submode,
	})

	if err != nil {
		logger.WithUser(arguments.UserID).Errorln(err)
		return ars, err
	}

	data["repaymentObj"] = res
	return ars, nil
}

// LisaRepaymentV3 (used for mflbl)
func (a *Activity) LisaRepaymentV3(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var arguments struct {
		UserID            string `arg:"userID" validate:"required"`
		LoanApplicationID string `arg:"loanApplicationID" validate:"required"`
		LenderID          string `arg:"lenderID" validate:"required"`
		Program           string `arg:"program" validate:"required"`
		Intent            string `arg:"intent"`
		SourceEntityID    string `arg:"sourceEntityID" validate:"required"`
	}
	err = GetArgs(&arguments, data)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{
			"workflowID": activityInfo.WorkflowExecution.ID,
			"args":       arguments,
			"userID":     arguments.UserID,
		}, err)
		return ars, err
	}

	res, err := lenderservice.Repayment(ctx, &lenderservice.RepaymentReq{
		ApplicationReq: lenderservice.ApplicationReq{
			UserID:            arguments.UserID,
			SourceEntityID:    arguments.SourceEntityID,
			LenderID:          arguments.LenderID,
			LoanApplicationID: arguments.LoanApplicationID,
			LenderSystem:      arguments.Program,
			Intent:            arguments.Intent,
		},
	})

	if err != nil {
		logger.WithUser(arguments.UserID).Errorln(err)
		return ars, err
	}

	data["repaymentObj"] = res
	return ars, nil
}

func (a *Activity) LisaSendConsent(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	args := argument.GetActivityArguments(data)

	_, intent := args.Get("intent", data)
	intentStr, ok := intent.(string)
	if !ok {
		err = errors.New("intent not found in args")
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	_, higherConsentID := args.Get("consent_id", data)
	higherConsentIDStr, ok := higherConsentID.(string)
	if !ok {
		err = errors.New("higherConsentID not found in args")
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	higherConsentIDInt, err := strconv.ParseInt(higherConsentIDStr, 10, 64)
	if err != nil {
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	userID := userObj.ID
	lenderID, ok := data["lenderID"].(string)
	if !ok {
		lenderID, _ = underwriting.GetLenderID(userObj.SourceEntityID, userObj.ID, "")
		data["lenderID"] = lenderID
	}

	loanApplicationData, err := loanapplication.GetLatestByUser(userID)
	var loanApplicationID, LoanApplicationNo string
	if err != nil {
		logger.WithUser(userObj.ID).Errorln(err)
		// errorHandler.ReportToSentryWithoutRequest(err)
		// return ars, err
	} else {
		loanApplicationID = loanApplicationData.ID.String()
		LoanApplicationNo = loanApplicationData.LoanApplicationNo
	}
	var reqBody = lenderservice.ConsentRequest{
		ApplicationReq: lenderservice.ApplicationReq{
			UserID:            userID,
			LenderID:          lenderID,
			SourceEntityID:    loanApplicationData.SourceEntityID,
			LoanApplicationID: loanApplicationID,
			LoanApplicationNo: LoanApplicationNo,
		},
		Intent:     intentStr,
		ConsentIDs: []int64{higherConsentIDInt},
	}

	_, _ = lenderservice.SendConsent(ctx, &reqBody)

	return ars, nil
}

// LisaFetchUTRDetails is used to fetch and save UTR details after loan disbursal for a particular lender
func (a *Activity) LisaFetchUTRDetails(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}
	ars.Data["utr_fetch_success"] = false
	ars.Data["utr_source"] = "lisa" // Denotes whether the UTR	was fetched from Lisa or from DB

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}
	userID := userObj.ID

	loanApp, err := loanapplication.GetLatestByUser(userObj.ID)
	if err != nil {
		logger.WithUser(userID).Error(err)
		return ars, err
	}

	args := argument.GetActivityArguments(data)
	_, fetchUTRIfExistsArg := args.Get("fetchUTRIfExistsArg", data)

	// A flag which decides whether to explicitly fetch UTR even if it already exists
	// Default to "not" fetching UTR if it already exists
	fetchUTRIfExists := fetchUTRIfExistsArg == "true"

	// If an UTR already exists, and we are not supposed to fetch it, return
	if !fetchUTRIfExists && loanApp.LenderTransactionNumber != "" {
		ars.Data["utr_fetch_success"] = true
		ars.Data["utr_source"] = "db" // Denotes that the UTR was fetched from DB
		return ars, nil
	}

	appReq := lenderservice.ApplicationReq{
		UserID:            userID,
		SourceEntityID:    loanApp.SourceEntityID,
		LenderID:          loanApp.LenderID,
		LoanApplicationNo: loanApp.LoanApplicationNo,
	}

	utrReq := lenderservice.UtrReq{
		ApplicationReq: appReq,
	}

	res, err := lenderservice.UtrDetail(ctx, &utrReq)
	if err != nil {
		logger.WithUser(userID).Error(err)
		return ars, nil
	}

	// if UTR is not available, re-trigger the activity linearly; TODO: add exponential backoff
	if res.UTRNo == "" {
		logger.WithUser(userID).Warn("got blank UTRNo from API")
		return ars, nil
	}

	err = loanapplication.Update(nil, loanapplication.StructForSet{
		ID:                      loanApp.ID.String(),
		LenderTransactionNumber: res.UTRNo,
		DisbursedDate:           res.LoanDisbursalDate,
		DisbursedDateTime:       res.LoanDisbursalDate,
		DisbursedAmount:         res.LoanAmountDisbursed,
	})
	if err != nil {
		logger.WithUser(userID).Error(err)
		return ars, err
	}

	activityObj := finboxEvents.ActivityEvent{
		UserID:         userID,
		SourceEntityID: userObj.SourceEntityID,
		EntityType:     constants.EntityTypeSystem,
		EventType:      constants.ActivityLoanUTRUpdated,
		EntityRef:      userObj.SourceEntityID,
		Description:    general.AnyToJSONString(map[string]any{"utr": res.UTRNo, "amount": res.LoanAmountDisbursed, "disbursalDate": res.LoanDisbursalDate}),
	}
	finboxEvents.RegisterEvent(&activityObj, general.GetTimeStampString())

	ars.Data["utr_fetch_success"] = true
	return ars, nil
}

// LisaFetchUTRDetailsV2 is used to fetch and save UTR details after loan disbursal for a particular lender
// this activity returns error in case of UTR not returned or lender UTR api failure
func (a *Activity) LisaFetchUTRDetailsV2(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}
	ars.Data["utr_fetch_success"] = false
	ars.Data["utr_source"] = "lisa" // Denotes whether the UTR	was fetched from Lisa or from DB

	// Get user
	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithFields(errorHandler.ErrorFields{"workflowID": activityInfo.WorkflowExecution.ID, "runID": activityInfo.WorkflowExecution.RunID}, err)
		return ars, err
	}
	userID := userObj.ID

	// Parse the arguments for activity
	var arguments struct {
		UpdateDisbursalDate   string `arg:"updateDisbursalDate" validate:"required"`
		UpdateUTR             string `arg:"updateUTR" validate:"required"`
		UpdateDisbursedAmount string `arg:"updateDisbursedAmount" validate:"required"`
		FetchUTRIfExists      string `arg:"fetchUTRIfExists" validate:"required"`
		LoanApplicationID     string `arg:"loanApplicationID" validate:"required"`
	}
	err = GetArgs(&arguments, data)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithFields(errorHandler.ErrorFields{"workflowID": activityInfo.WorkflowExecution.ID, "runID": activityInfo.WorkflowExecution.RunID}, err)
		return ars, err
	}
	updateDisbursalDate := arguments.UpdateDisbursalDate == "true"
	updateUTR := arguments.UpdateUTR == "true"
	updateDisbursedAmount := arguments.UpdateDisbursedAmount == "true"
	// A flag which decides whether to explicitly fetch UTR even if it already exists
	// Default to "not" fetching UTR if it already exists
	fetchUTRIfExists := arguments.FetchUTRIfExists == "true"

	// Get latest loan application
	loanApplicationID := arguments.LoanApplicationID
	if !general.ValidateUUID(loanApplicationID) {
		err = fmt.Errorf("invalid loanApplicationID passed in argument")
		logger.WithUser(userID).Error(err)
		return ars, err
	}
	loanApp, err := loanapplication.Get(ctx, loanApplicationID)
	if err != nil {
		logger.WithUser(userID).Error(err)
		return ars, err
	}

	// If an UTR already exists, and we are not supposed to fetch it, return
	if !fetchUTRIfExists && loanApp.LenderTransactionNumber != "" {
		ars.Data["utr_fetch_success"] = true
		ars.Data["utr_source"] = "db" // Denotes that the UTR was fetched from DB
		return ars, nil
	}

	appReq := lenderservice.ApplicationReq{
		UserID:            userID,
		SourceEntityID:    loanApp.SourceEntityID,
		LenderID:          loanApp.LenderID,
		LoanApplicationNo: loanApp.LoanApplicationNo,
	}
	utrReq := lenderservice.UtrReq{
		ApplicationReq: appReq,
	}

	res, err := lenderservice.UtrDetail(ctx, &utrReq)
	if err != nil {
		logger.WithUser(userID).Error(err, "UTR api failed")
		return ars, errors.New(constants.ErrStrLenderAPIFailure)
	}

	// if UTR is not available, fail the activity
	// UTR is empty in some cases where the loan amount is not yet transferred to borrowers account
	if res.UTRNo == "" {
		logger.WithUser(userID).Error(constants.ErrStrBlankUTRFromLender)
		return ars, fmt.Errorf(constants.ErrStrBlankUTRFromLender)
	}

	// Update the disbursal date, disbursal_datetime, lender_transac
	loanSetStruct := loanapplication.StructForSet{
		ID: loanApp.ID.String(),
	}
	if updateDisbursalDate {
		loanSetStruct.DisbursedDate = res.LoanDisbursalDate
		loanSetStruct.DisbursedDateTime = res.LoanDisbursalDate
	}
	if updateUTR {
		loanSetStruct.LenderTransactionNumber = res.UTRNo
	}
	if updateDisbursedAmount {
		loanSetStruct.DisbursedAmount = res.LoanAmountDisbursed
	}
	if updateDisbursalDate || updateUTR || updateDisbursedAmount {
		err = loanapplication.Update(nil, loanSetStruct)
		if err != nil {
			logger.WithUser(userID).Error(err)
			return ars, err
		}
	}

	activityObj := finboxEvents.ActivityEvent{
		UserID:         userID,
		SourceEntityID: userObj.SourceEntityID,
		EntityType:     constants.EntityTypeSystem,
		EventType:      constants.ActivityLoanUTRUpdated,
		EntityRef:      userObj.SourceEntityID,
		Description:    general.AnyToJSONString(map[string]any{"utr": res.UTRNo, "amount": res.LoanAmountDisbursed, "disbursalDate": res.LoanDisbursalDate}),
	}
	finboxEvents.RegisterEvent(&activityObj, general.GetTimeStampString())

	ars.Data["utr_fetch_success"] = true
	return ars, nil
}

func (a *Activity) LisaCreateReceivable(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}
	userID := userObj.ID

	type argumentStruct struct {
		LoanApplicationID string `arg:"loanApplicationID" validate:"required"`
		ChargeCode        string `arg:"chargeCode" validate:"required"`
		DueDate           string `arg:"dueDate" validate:"required"`
		Amount            string `arg:"amount" validate:"required"`
		Reason            string `arg:"reason" validate:"required"`
	}

	var args argumentStruct
	err = GetArgs(&args, data)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	amount, err := strconv.ParseFloat(args.Amount, 64)
	if err != nil {
		err = fmt.Errorf("amount not parsable to float64")
		logger.WithUser(userID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	loanApp, err := loanapplication.Get(ctx, args.LoanApplicationID)
	if err != nil {
		logger.WithUser(userID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}
	createReceivableReq := lenderservice.CreateReceivableReq{
		UserID:                userID,
		LoanApplicationID:     args.LoanApplicationID,
		SourceEntityID:        loanApp.SourceEntityID,
		LenderID:              loanApp.LenderID,
		LoanApplicationNumber: loanApp.LoanApplicationNo,
		ChargeType:            args.ChargeCode,
		Reason:                args.Reason,
		DueDate:               args.DueDate,
		DueAmount:             amount,
	}

	res, err := lenderservice.CreateReceivable(ctx, &createReceivableReq)
	if err != nil {
		logger.WithUser(userID).Error(err)
		return ars, lenderservice.ErrCreateReceivableServiceNotAvailable
	}

	data["loanApplicationNo"] = loanApp.LoanApplicationNo
	data["createReceivableResponse"] = res
	output["createReceivableResponse"] = res

	return ars, nil
}

func (a *Activity) LisaCancelReceivable(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}
	userID := userObj.ID

	type argumentStruct struct {
		LoanApplicationID       string `arg:"loanApplicationID" validate:"required"`
		LenderChargeReferenceID string `arg:"lenderChargeReferenceID" validate:"required"`
		Reason                  string `arg:"reason" validate:"required"`
		Remarks                 string `arg:"remarks" validate:"required"`
	}

	var args argumentStruct
	err = GetArgs(&args, data)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	loanApp, err := loanapplication.Get(ctx, args.LoanApplicationID)
	if err != nil {
		logger.WithUser(userID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	cancelReceivableReq := lenderservice.CancelReceivableReq{
		UserID:          userID,
		SourceEntityID:  loanApp.SourceEntityID,
		LenderID:        loanApp.LenderID,
		ReferenceNumber: args.LenderChargeReferenceID,
		Reason:          args.Reason,
		Remarks:         args.Remarks,
	}

	res, err := lenderservice.CancelReceivable(ctx, &cancelReceivableReq)
	if err != nil {
		return ars, lenderservice.ErrCancelReceivableServiceNotAvailable
	}

	data["lenderChargeReferenceID"] = args.LenderChargeReferenceID
	data["loanApplicationNo"] = loanApp.LoanApplicationNo
	data["reason"] = args.Reason
	data["cancelReceivableResponse"] = res

	output["cancelReceivableResponse"] = res

	return ars, nil
}

func (a *Activity) LisaCreatePayable(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}
	userID := userObj.ID
	type argumentStruct struct {
		LoanApplicationID string `arg:"loanApplicationID" validate:"required"`
		ChargeCode        string `arg:"chargeCode" validate:"required"`
		DueDate           string `arg:"dueDate" validate:"required"`
		Amount            string `arg:"amount" validate:"required"`
		Reason            string `arg:"reason" validate:"required"`
	}

	var args argumentStruct
	err = GetArgs(&args, data)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	amount, err := strconv.ParseFloat(args.Amount, 64)
	if err != nil {
		err = fmt.Errorf("amount not parsable to float64")
		logger.WithUser(userID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	loanApp, err := loanapplication.Get(ctx, args.LoanApplicationID)
	if err != nil {
		logger.WithUser(userID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}
	createPayableReq := lenderservice.CreatePayableReq{
		UserID:                userID,
		LoanApplicationID:     args.LoanApplicationID,
		SourceEntityID:        loanApp.SourceEntityID,
		LenderID:              loanApp.LenderID,
		LoanApplicationNumber: loanApp.LoanApplicationNo,
		ChargeType:            args.ChargeCode,
		Reason:                args.Reason,
		DueDate:               args.DueDate,
		DueAmount:             amount,
	}

	res, err := lenderservice.CreatePayable(ctx, &createPayableReq)
	if err != nil {
		logger.WithUser(userID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, lenderservice.ErrCreatePayableServiceNotAvailable
	}

	data["loanApplicationNo"] = loanApp.LoanApplicationNo
	data["createPayableResponse"] = res
	output["createPayableResponse"] = res

	return ars, nil
}

func (a *Activity) LisaCancelPayable(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}
	userID := userObj.ID

	type argumentStruct struct {
		LoanApplicationID       string `arg:"loanApplicationID" validate:"required"`
		LenderChargeReferenceID string `arg:"lenderChargeReferenceID" validate:"required"`
		Reason                  string `arg:"reason" validate:"required"`
		Remarks                 string `arg:"remarks" validate:"required"`
	}

	var args argumentStruct
	err = GetArgs(&args, data)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	loanApp, err := loanapplication.Get(ctx, args.LoanApplicationID)
	if err != nil {
		logger.WithUser(userID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	cancelPayableReq := lenderservice.CancelPayableReq{
		UserID:          userID,
		SourceEntityID:  loanApp.SourceEntityID,
		LenderID:        loanApp.LenderID,
		ReferenceNumber: args.LenderChargeReferenceID,
		Reason:          args.Reason,
		Remarks:         args.Remarks,
	}

	res, err := lenderservice.CancelPayable(ctx, &cancelPayableReq)
	if err != nil {
		return ars, lenderservice.ErrCancelReceivableServiceNotAvailable
	}

	data["lenderChargeReferenceID"] = args.LenderChargeReferenceID
	data["loanApplicationNo"] = loanApp.LoanApplicationNo
	data["reason"] = args.Reason
	data["cancelPayableResponse"] = res

	output["cancelPayableResponse"] = res

	return ars, nil
}

// CheckBusinessDate is used to check if the lender system's business date is current date and not backdated or future dated date
func (a *Activity) CheckBusinessDate(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}
	userID := userObj.ID

	// get activity arguments
	var arguments struct {
		LoanApplicationID string `arg:"loanApplicationID" validate:"required"`
	}
	err = GetArgs(&arguments, data)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	businessDate, err := lenderservice.CheckBusinessDate(ctx, userID, arguments.LoanApplicationID)
	if err != nil {
		logger.WithUser(userID).Error(err)
		return ars, err
	}

	ars.Data["business_date"] = businessDate

	return ars, nil
}

func (a *Activity) LisaDedupeCheck(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}
	userID := userObj.ID

	// get activity arguments
	var arguments struct {
		LenderID    string `arg:"lenderID" validate:"required"`
		ConsentType string `arg:"consentType"`
		Intent      string `arg:"intent"`
	}
	err = GetArgs(&arguments, data)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	appReq := lenderservice.ApplicationReq{
		UserID:         userID,
		SourceEntityID: userObj.SourceEntityID,
		ConsentType:    arguments.ConsentType,
		LenderID:       arguments.LenderID,
		Intent:         arguments.Intent,
	}
	req := lenderservice.DedupeCheckReq{
		ApplicationReq: appReq,
	}
	resp, err := lenderservice.DedupeCheck(ctx, &req)
	if err != nil {
		logger.WithUser(userID).Error(err)
		logger.WithUser(userID).Errorln("dedupe check failed")
		data["isLenderDedupe"] = false
		finboxEvents.ActivityLogger(userID, userObj.SourceEntityID, userID, constants.EntityTypeSystem, constants.ActivityLenderAPIFailed, "dedupe API failure: dedupe check bypassed", "", general.GetTimeStampString(), false)
	} else if resp != nil {
		data["isLenderDedupe"] = resp.IsExist
		data["rejectReason"] = resp.Message
	} else {
		logger.WithUser(userID).Errorln("nil response from lenderservice")
		finboxEvents.ActivityLogger(userID, userObj.SourceEntityID, userID, constants.EntityTypeSystem, constants.ActivityLenderAPIFailed, "dedupe API failure: dedupe check bypassed", "", general.GetTimeStampString(), false)
		data["isLenderDedupe"] = false
	}

	return ars, nil
}

// LisaAcceptOffer is used to call lisa accept offer
func (a *Activity) LisaAcceptOffer(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	lenderID, ok := data["lenderID"].(string)
	if !ok {
		lenderID, _ = underwriting.GetLenderID(userObj.SourceEntityID, userObj.ID, "")
		data["lenderID"] = lenderID
	}

	resp, err := lenderservice.AcceptOffer(context.TODO(), &lenderservice.AcceptOfferReq{
		ApplicationReq: lenderservice.ApplicationReq{
			UserID:         userObj.ID,
			SourceEntityID: userObj.SourceEntityID,
			LenderID:       lenderID,
		},
	})

	if err != nil {
		logger.WithUser(userObj.ID).Errorln(err)
		return ars, err
	}

	data["acceptOfferResponse"] = resp
	output["acceptOfferResponse"] = resp

	return ars, nil
}

func (a *Activity) LisaPostPaymentDetails(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {

	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()

	ars = runner.ActivityReturnStruct{
		Data:   map[string]interface{}{},
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	userID := userObj.ID

	var arguments struct {
		LoanApplicationNo string `arg:"loanApplicationNo" required:"true"`
		LoanApplicationID string `arg:"loanApplicationID" required:"true"`
		LenderID          string `arg:"lenderID" required:"true"`
		VendorPaymentID   string `arg:"vendorPaymentID" required:"true"`
		Program           string `arg:"program" required:"true"`
		SourceEntityID    string `arg:"sourceEntityID" required:"true"`
	}

	err = GetArgsV2(&arguments, data)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, NewNonRetryableApplicationError(err.Error())
	}

	res, err := lenderservice.PostPaymentDetails(ctx, &lenderservice.RepaymentReq{
		ApplicationReq: lenderservice.ApplicationReq{
			UserID:            userObj.ID,
			SourceEntityID:    arguments.SourceEntityID,
			LenderID:          arguments.LenderID,
			LoanApplicationID: arguments.LoanApplicationID,
			LoanApplicationNo: arguments.LoanApplicationNo,
			LenderSystem:      arguments.Program,
			VendorPaymentID:   arguments.VendorPaymentID,
		},
	})

	if err != nil {
		logger.WithUser(userID).Errorln(err)
		return ars, err
	}

	ars.Data["postPaymentDetails"] = res
	return ars, nil
}

// LisaGetOfferV2 is used to fetch offer for a user for a particular lender without creating loan offer entry
func (a *Activity) LisaGetOfferV2(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}
	var arguments struct {
		Intent             string      `arg:"intent"`
		NullOfferRejection interface{} `arg:"nullOfferRejection"`
		AdditionalData     interface{} `arg:"additionalData"`
		JourneyType        string      `arg:"journeyType"`
		LoanApplicationID  string      `arg:"loanApplicationID"`
		LenderID           string      `arg:"lenderID"`
	}

	err = GetArgsV2(&arguments, data)
	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	// if lenderID is not provided, use the lenderID from the data. Hack but couldn't do better :(
	if arguments.LenderID == "" {
		arguments.LenderID, _ = data["lenderID"].(string)
	}

	if arguments.LenderID == "" {
		arguments.LenderID, _ = underwriting.GetLenderID(userObj.SourceEntityID, userObj.ID, "")
		data["lenderID"] = arguments.LenderID
	}

	reqData := lenderservice.GetOfferReq{
		Intent: arguments.Intent,
		ApplicationReq: lenderservice.ApplicationReq{
			UserID:            userObj.ID,
			LenderID:          arguments.LenderID,
			SourceEntityID:    userObj.SourceEntityID,
			Type:              arguments.JourneyType,
			LoanApplicationID: arguments.LoanApplicationID,
			AdditionalData:    arguments.AdditionalData,
		},
	}
	logger.WithUser(userObj.ID).Info(reqData)
	res, err := lenderservice.GetOffers(ctx, &reqData)

	if arguments.Intent == "initial" {
		return ars, nil
	}
	if err != nil {
		logger.WithUser(userObj.ID).Errorln(err)
		if err.Error() != constants.ErrorPolicyRejected {
			errorHandler.ReportToSentryWithFields(errorHandler.ErrorFields{"userID": userObj.ID}, err)
		}
		return ars, err
	}

	logger.WithUser(userObj.ID).Info(res)
	if res == nil {
		if arguments.NullOfferRejection != nil {
			data["nullOfferRejection"] = arguments.NullOfferRejection
			return ars, nil
		}
		err = fmt.Errorf("no offers found")
		return ars, err
	}

	logger.WithUser(userObj.ID).Info(res)
	data["response"] = res
	data["offers"] = res.Offers
	if len(res.Offers) < 1 && res.RedirectionURL == "" {
		if arguments.NullOfferRejection != nil {
			data["nullOfferRejection"] = arguments.NullOfferRejection
			return ars, nil
		}
		err = fmt.Errorf("no offers found")
		return ars, err
	}
	return ars, nil
}

// LisaCreateApplicantV2 is used to create customer at lender's end
func (a *Activity) LisaCreateApplicantV2(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}
	userID := userObj.ID

	// Parse the arguments for activity
	var arguments struct {
		UpdateLenderVariables string `arg:"toUpdateLenderVariables" validate:"required"`
		UpdateLSQLead         string `arg:"toUpdateLSQLead" validate:"required"`
		UpdateUserCRM         string `arg:"toUpdateUserCRM" validate:"required"`
		SkipIfUserCRMExists   string `arg:"toSkipIfUserCRMExists" validate:"required"`
		LoanApplicationID     string `arg:"loanApplicationID" validate:"required"`
		CheckBusinessDate     string `arg:"checkBusinessDate"`
		LenderID              string `arg:"lenderID"`
		SkipLoanCheck         string `arg:"toSkipLoanCheck"`
	}
	err = GetArgs(&arguments, data)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithFields(errorHandler.ErrorFields{"workflowID": activityInfo.WorkflowExecution.ID, "runID": activityInfo.WorkflowExecution.RunID}, err)
		return ars, err
	}
	// Get LoanApplicationId from activity argument
	loanApplicationID := arguments.LoanApplicationID
	// Check if we need to update the CRM ID in DB
	toUpdateUserCRM := arguments.UpdateUserCRM == "true"
	// Check if we need to update the CRM ID in lender variables
	toUpdateLenderVariables := arguments.UpdateLenderVariables == "true"
	// Check if we need to skip the Create Applicant at lender end if User CRM is already created (avoids duplicate API call)
	toSkipIfUserCRMExists := arguments.SkipIfUserCRMExists == "true"
	// Check if we need to verify lender system business date
	checkLenderBusinessDate := arguments.CheckBusinessDate == "true"
	// Check if we need to put crm id in reference id or LSQLead
	toUpdateLsqLead := arguments.UpdateLSQLead == "true"
	// Check if we can continue even if loan application is not found
	toSkipLoanCheck := arguments.SkipLoanCheck == "true"

	// using CRMID blank check as idempotency check for this action
	if toSkipIfUserCRMExists {
		// Check if CRM is already there for User
		crmID, err := users.GetUserCRM(ctx, userID)
		if err != nil {
			logger.WithUser(userID).Error(err)
			return ars, err
		}
		if crmID != "" {
			logger.WithUser(userID).Info("Skipping create applicant as it already exists for the user")
			return ars, nil
		}
	}

	// Check business date
	if checkLenderBusinessDate {
		// if current date is not business date we'll throw error from current execution and retry again
		_, err = lenderservice.CheckBusinessDate(ctx, userID, arguments.LoanApplicationID)
		if err != nil {
			logger.WithUser(userID).Error(err)
			return ars, err
		}
	}

	// Prepare the LISA request for Create Applicant
	applicationReq, err := lenderservice.GetApplicationReqByUserAndLoan(ctx, userID, loanApplicationID, &lenderservice.GetApplicationReqOptions{
		LenderID:      arguments.LenderID,
		SkipLoanCheck: toSkipLoanCheck,
	})
	if err != nil {
		logger.WithUser(userID).Error(err)
		return ars, err
	}

	createApplicantResp, err := lenderservice.CreateApplicant(ctx, &applicationReq)
	if err != nil {
		logger.WithUser(userID).Error(err)
		return ars, err
	}

	tx, err := database.Beginx()
	if err != nil {
		logger.WithUser(userID).Error(err)
		return ars, errors.New("lender API Create Applicant failed")
	}
	defer tx.Rollback()

	// Update CRM Id
	if toUpdateUserCRM {
		err = users.Update(tx, users.User{ID: userID, CrmID: createApplicantResp.CrmID})
		if err != nil {
			logger.WithUser(userID).Error(err)
			errorHandler.ReportToSentryWithoutRequest(fmt.Errorf("[LisaCreateApplicant] failed to update CRMID in Users table: userID: %s, err: %v", userID, err))
			return ars, err
		}
	}

	// Update lender variables
	if toUpdateLenderVariables {
		var lenderVariablesNullable lendervariables.LenderVariablesStructNullable
		if toUpdateLsqLead {
			lenderVariablesNullable = lendervariables.LenderVariablesStructNullable{
				UserID:    userID,
				LenderID:  applicationReq.LenderID,
				LSQLeadID: sql.NullString{Valid: createApplicantResp.CrmID != "", String: createApplicantResp.CrmID},
			}
		} else if createApplicantResp.LoanApplicationNumber != "" {
			lenderVariablesNullable = lendervariables.LenderVariablesStructNullable{
				UserID:      userID,
				LenderID:    applicationReq.LenderID,
				ReferenceID: sql.NullString{Valid: createApplicantResp.LoanApplicationNumber != "", String: createApplicantResp.LoanApplicationNumber},
			}
		} else if createApplicantResp.CrmID != "" && general.InArr(applicationReq.LenderID, [](string){constants.RingMCID, constants.BajajID}) {
			lenderVariablesNullable = lendervariables.LenderVariablesStructNullable{
				UserID:       userID,
				LenderID:     applicationReq.LenderID,
				LenderUserID: sql.NullString{Valid: createApplicantResp.CrmID != "", String: createApplicantResp.CrmID},
			}
		} else {
			lenderVariablesNullable = lendervariables.LenderVariablesStructNullable{
				UserID:      userID,
				LenderID:    applicationReq.LenderID,
				ReferenceID: sql.NullString{Valid: createApplicantResp.CrmID != "", String: createApplicantResp.CrmID},
			}
		}
		err = lendervariables.Update(tx, lenderVariablesNullable)
		if err != nil {
			logger.WithUser(userID).Error(err)
			errorHandler.ReportToSentryWithoutRequest(fmt.Errorf("[LisaCreateApplicant] failed to update CRMID in Lender Variables table: userID: %s, err: %v", userID, err))
			return ars, err
		}
	}
	data["lisaCreateApplicantResponseStatus"] = createApplicantResp.Status
	data["lisaCreateApplicantResponseMessage"] = createApplicantResp.Message
	if createApplicantResp.LoanApplicationNumber != "" {
		data["lisaCreateApplicantLoanApplicationNo"] = createApplicantResp.LoanApplicationNumber
	}
	return ars, tx.Commit()
}

func (a *Activity) LisaApprove(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var reqBody lenderservice.ApplicationReq

	var arguments struct {
		UserID         string `arg:"userID" required:"true"`
		LenderID       string `arg:"lenderID" required:"true"`
		SourceEntityID string `arg:"sourceEntityID" required:"true"`
	}

	err = GetArgsV2(&arguments, data)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	argBytes, err := json.Marshal(arguments)
	err = json.Unmarshal(argBytes, &reqBody)

	resp, err := lenderservice.ApproveApplication(ctx, &reqBody)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		return ars, errors.New("LenderAPIFailure")
	}
	data["lisaApprovalResponse"] = resp
	return ars, nil
}

func (a *Activity) LisaDedupeCheckV2(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}
	userID := userObj.ID

	// get activity arguments
	var arguments struct {
		LenderID string `arg:"lenderID" validate:"required"`
		Intent   string `arg:"intent"`
	}
	err = GetArgs(&arguments, data)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	appReq := lenderservice.ApplicationReq{
		UserID:         userID,
		SourceEntityID: userObj.SourceEntityID,
		LenderID:       arguments.LenderID,
		Intent:         arguments.Intent,
	}
	req := lenderservice.DedupeCheckReq{
		ApplicationReq: appReq,
	}
	resp, err := lenderservice.DedupeCheck(ctx, &req)
	if err != nil {
		logger.WithUser(userID).Error(err)
		logger.WithUser(userID).Errorln("dedupe check failed")
		data["isLenderDedupe"] = false
		return ars, err
	} else if resp != nil {
		data["isLenderDedupe"] = resp.IsExist
		data["rejectReason"] = resp.Message
	} else {
		logger.WithUser(userID).Errorln("nil response from lenderservice")
		data["isLenderDedupe"] = false
		return ars, err
	}

	return ars, nil
}

// LISAGetReport is used to get the report from LISA. This takes in a intent and returns the report.
func (a *Activity) LISAGetReport(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   map[string]interface{}{},
		Output: map[string]interface{}{},
	}

	var args struct {
		UserID            string                 `arg:"userID" required:"true"`
		SourceEntityID    string                 `arg:"sourceEntityID" required:"true"`
		LenderID          string                 `arg:"lenderID" required:"true"`
		Intent            string                 `arg:"intent" required:"true"`
		LoanApplicationNo string                 `arg:"loanApplicationNo" required:"true"`
		Data              map[string]interface{} `arg:"data"` // optional
	}

	err = GetArgsV2(&args, data)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithFields(errorHandler.ErrorFields{"workflowID": activityInfo.WorkflowExecution.ID, "runID": activityInfo.WorkflowExecution.RunID, "args": args}, err)
		return ars, err
	}

	appReq := lenderservice.ApplicationReq{
		UserID:            args.UserID,
		SourceEntityID:    args.SourceEntityID,
		LenderID:          args.LenderID,
		LoanApplicationNo: args.LoanApplicationNo,
	}
	req := lenderservice.ReportReq{
		ApplicationReq: appReq,
		Intent:         args.Intent,
		Data:           general.AnyToJSONString(args.Data),
	}

	report, err := lenderservice.GetReportDetails(ctx, &req)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		return ars, err
	}

	s3ObjectKey := fmt.Sprintf("%s/lisa_report_%s/%s.pdf", args.UserID, args.Intent, general.GenerateRandomString(7))

	_, ok := s3.UploadBase64ToS3(report.Report, s3ObjectKey)
	if !ok {
		logger.WithUser(args.UserID).Errorln("failed to upload report to s3")
		errorHandler.ReportToSentryWithFields(errorHandler.ErrorFields{"workflowID": activityInfo.WorkflowExecution.ID, "userID": args.UserID, "lenderID": args.LenderID, "intent": args.Intent, "loanApplicationNo": args.LoanApplicationNo}, err)
		return ars, errors.New("failed to upload report to s3")
	}

	ars.Data["reportS3Object"] = s3ObjectKey

	return ars, nil
}

// LisaRedirectUser calls lisa redirect user api, pass the whole applicationReq as argument, ToUseBaseUrl is true, add the env base url to prefix. ApplicationReq needs to set via Mapper before passing as argument
// Expected values in req:
// UserID:
// Intent:
// LoanApplicationID:
// SourceEntityID:
// LenderID:
// RedirectURL:
func (a *Activity) LisaRedirectUser(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	type argumentStruct struct {
		UserID         string                        `arg:"userID" required:"true"`
		LenderID       string                        `arg:"lenderID" required:"true"`
		ApplicationReq *lenderservice.ApplicationReq `arg:"applicationReq" required:"true"`
		ToUseBaseURL   bool                          `arg:"toUseBaseUrl" required:"true"`
	}

	var args argumentStruct
	err = GetArgsV2(&args, data)
	if err != nil {
		logger.WithUser(args.UserID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"UserID": args.UserID}, err)
		return ars, err
	}

	if args.ToUseBaseURL {
		args.ApplicationReq.RedirectURL = conf.BaseURL + args.ApplicationReq.RedirectURL
	}

	var resp *lenderservice.RedirectUserURLResp
	err = retry.CustomRetry(3, 1*time.Second, func() error {
		resp, err = lenderservice.RedirectUser(ctx, args.ApplicationReq)
		if err != nil {
			errMessage := strings.ToUpper(err.Error())
			if strings.HasPrefix(errMessage, "REJECTED") {
				return retry.NewStop(err.Error())
			}
			return err
		}
		return nil
	})

	if err != nil {
		logger.WithUser(args.UserID).Error(err)
		return ars, err
	}
	data["redirectionURL"] = resp.URL
	data["redirectUserResp"] = resp

	ars.Output[constants.OutputKeyBankingRedirectURL] = resp.URL
	return ars, nil
}

// LisaPennydrop calls lisa pennydrop api, pass the whole applicationReq as argument. ApplicationReq needs to set via Mapper before passing as argument
func (a *Activity) LisaPennydrop(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	type argumentStruct struct {
		UserID         string                        `arg:"userID" required:"true"`
		ApplicationReq *lenderservice.ApplicationReq `arg:"applicationReq" required:"true"`
	}

	var args argumentStruct
	err = GetArgsV2(&args, data)
	if err != nil {
		logger.WithUser(args.UserID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"UserID": args.UserID}, err)
		return ars, err
	}
	resp, err := lenderservice.PennyDrop(ctx, args.ApplicationReq)
	if err != nil {
		logger.WithUser(args.UserID).Error(err)
		return ars, err
	}
	data["pennydropResp"] = resp
	return ars, nil
}

func (a *Activity) FetchKFS(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}

	type argumentStruct struct {
		LenderID string `arg:"lenderID" validate:"required"`
	}

	var args argumentStruct
	err = GetArgs(&args, data)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	loanApplicationID, err := loanapplication.GetLoanApplicationIDByLenderID(userObj.ID, args.LenderID)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error("unable to fetch loan application: ", err)
		return ars, err
	}

	resp, err := lenderservice.KFS(ctx, &lenderservice.ApplicationReq{
		UserID:            userObj.ID,
		LoanApplicationID: loanApplicationID,
		SourceEntityID:    userObj.SourceEntityID,
		LenderID:          args.LenderID,
	})
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error("unable to fetch kfs from lisa: ", err)
		return ars, err
	}

	output["gotKFSURL"] = map[string]interface{}{
		"isError":   false,
		"kfsStatus": "running",
	}
	if resp.URL != "" {
		output["kfsURL"] = resp.URL
		output["gotKFSURL"] = map[string]interface{}{
			"isError":   false,
			"kfsStatus": "success",
		}
	}
	if resp.ErrorMessage != "" {
		data["kfsResp"] = resp
		output["gotKFSURL"] = map[string]interface{}{
			"isError":   true,
			"kfsStatus": "failed",
		}
	}
	return ars, nil
}

func (a *Activity) CalculateCharges(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	type charge struct {
		ChargeType  string  `json:"chargeType"`
		Amount      float64 `json:"amount"`
		GstAmount   float64 `json:"gstAmount"`
		TotalAmount float64 `json:"totalAmount"`
	}
	var res []charge

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}

	// quick fix, change to fetch lenderID from args
	loanApplicationID, err := loanapplication.GetLoanApplicationIDByLenderID(userObj.ID, constants.AxisBankID)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error("unable to get loan application ID: ", err)
		return ars, err
	}
	loanObj, err := loanapplication.Get(context.Background(), loanApplicationID)

	if loanObj != nil && loanObj.DynamicCharges != "" {
		var result struct {
			Charges []personalloanoffer.Charges `json:"charges"`
		}
		err := json.Unmarshal([]byte(loanObj.DynamicCharges), &result)
		if err != nil {
			logger.WithUser(userObj.ID).Error("error while un-marshaling charges")
			return ars, err
		}
		for _, ch := range result.Charges {
			chargeAmount := calc.CalculateCharges(loanObj.Amount, ch.ChargeVal.(float64), ch.ChargeType)
			gst := calc.CalculateGST(chargeAmount, ch.ChargeGST)
			res = append(res, charge{
				ChargeType:  strings.ToUpper(general.CamelToSnakeCase(ch.ChargeName)),
				Amount:      chargeAmount,
				GstAmount:   gst,
				TotalAmount: chargeAmount + gst,
			})
		}
	}

	data["charges"] = res
	return ars, nil
}

func (a *Activity) LisaUpdateApplicantV2(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	type argument struct {
		UserID         string `arg:"userID" required:"true"`
		LenderID       string `arg:"lenderID" required:"true"`
		SourceEntityID string `arg:"sourceEntityID" required:"true"`
		Intent         string `arg:"intent"`
	}
	var args argument
	err = GetArgsV2(&args, data)
	if err != nil {
		logger.WithUser(args.UserID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"UserID": args.UserID}, err)
		return ars, err
	}

	var reqBody = lenderservice.ApplicationReq{
		UserID:         args.UserID,
		LenderID:       args.LenderID,
		SourceEntityID: args.SourceEntityID,
		Intent:         args.Intent,
	}
	_, err = lenderservice.UpdateApplicant(ctx, &reqBody)
	if err != nil {
		logger.WithUser(args.UserID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}
	return ars, nil
}

// LisaCreateApplicationV2 ...
func (a *Activity) LisaCreateApplicationV2(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	// Parse the arguments for activity
	var arguments struct {
		UserID         string `arg:"userID" required:"true"`
		SourceEntityID string `arg:"sourceEntityID" required:"true"`
		LenderID       string `arg:"lenderID" required:"true"`
		Intent         string `arg:"intent"`
		ConsentType    string `arg:"consentType"`
	}
	err = GetArgs(&arguments, data)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithFields(errorHandler.ErrorFields{
			"workflowID": activityInfo.WorkflowExecution.ID,
			"runID":      activityInfo.WorkflowExecution.RunID,
		}, err)
		return ars, err
	}

	applicationReq := lenderservice.ApplicationReq{
		UserID:         arguments.UserID,
		SourceEntityID: arguments.SourceEntityID,
		LenderID:       arguments.LenderID,
		Intent:         arguments.Intent,
		ConsentType:    arguments.ConsentType,
	}

	createApplicationResp, err := lenderservice.CreateApplication(ctx, &applicationReq)
	if err != nil {
		logger.WithUser(arguments.UserID).Error(err)
		return ars, err
	}

	data["createApplicationResp"] = createApplicationResp

	return ars, nil
}

// LisaUpdateApplicationV2 is used to PUT details for existing application using intent from activcity arguments
func (a *Activity) LisaUpdateApplicationV2(ctx context.Context, data map[string]interface{}, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}
	var arguments struct {
		Intent         string                       `arg:"intent"`
		ApplicationReq lenderservice.ApplicationReq `arg:"applicationReq" required:"true"`
		EventType      string                       `arg:"eventType"`
		Description    string                       `arg:"description"`
	}

	var eventType string
	var description string
	err = GetArgsV2(&arguments, data)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}
	var resp *lenderservice.ApplicationResp

	reqData := lenderservice.UpdateApplicationReq{
		Intent:         arguments.Intent,
		ApplicationReq: arguments.ApplicationReq,
	}

	resp, err = lenderservice.UpdateApplication(ctx, &reqData)
	if err != nil {
		logger.WithUser(userObj.ID).Errorln(err)
		if !strings.Contains(err.Error(), constants.ErrPoonawallaSomethingWentWrong) {
			errorHandler.ReportToSentryWithFields(map[string]interface{}{
				"userID":     userObj.ID,
				"workflowID": activityInfo.WorkflowExecution.ID,
			}, err)
		}
		return ars, err
	}

	eventType = constants.ActivityPreLoanDataSubmitted
	description = arguments.Intent
	if arguments.EventType != "" {
		eventType = arguments.EventType
	}
	if arguments.Description != "" {
		description = arguments.Description
	}

	dateTimeNowString := general.GetTimeStampString()
	activityObj := finboxEvents.ActivityEvent{
		UserID:         userObj.ID,
		SourceEntityID: userObj.SourceEntityID,
		EntityType:     constants.EntityTypeCustomer,
		EventType:      eventType,
		Description:    description,
	}
	finboxEvents.RegisterEvent(&activityObj, dateTimeNowString)

	data["updateApplicationResponse"] = resp

	return ars, nil
}

// KycInit is used to call lisa kyc/initiate api
func (a *Activity) LisaKycInit(ctx context.Context, data map[string]interface{}, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}
	var arguments struct {
		UserID         string                       `arg:"userID"`
		LenderID       string                       `arg:"lenderID"`
		Intent         string                       `arg:"intent"`
		ApplicationReq lenderservice.ApplicationReq `arg:"applicationReq" required:"true"`
		EventType      string                       `arg:"eventType"`
		Description    string                       `arg:"description"`
	}

	var eventType string
	var description string
	err = GetArgsV2(&arguments, data)

	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}
	reqData := lenderservice.KycInitReq{
		Intent:         arguments.Intent,
		ApplicationReq: arguments.ApplicationReq,
	}

	eventType = constants.ActivityVKYCRedirected
	description = arguments.Intent
	if arguments.EventType != "" {
		eventType = arguments.EventType
	}

	if arguments.Description != "" {
		description = arguments.Description
	}
	resp, err := lenderservice.KycInit(ctx, &reqData)
	if err != nil {
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	dateTimeNowString := general.GetTimeStampString()
	activityObj := finboxEvents.ActivityEvent{
		UserID:         userObj.ID,
		SourceEntityID: userObj.SourceEntityID,
		EntityType:     constants.EntityTypeCustomer,
		EventType:      eventType,
		Description:    description,
	}
	finboxEvents.RegisterEvent(&activityObj, dateTimeNowString)

	data["kycInitResponse"] = resp

	return ars, nil
}

// LisaGetKYCStatusV2 is used to get the status of external KYC triggered via LISA
func (a *Activity) LisaGetKYCStatusV2(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var arguments struct {
		ApplicationReq lenderservice.ApplicationReq `arg:"applicationReq" required:"true"`
	}

	err = GetArgsV2(&arguments, data)

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	kycStatus, err := lenderservice.KYCStatus(ctx, &arguments.ApplicationReq)
	if err != nil {
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	data["kycStatus"] = kycStatus.Status

	return ars, nil
}

// LisaRedirectUserV2 calls lisa redirect user api, pass the whole applicationReq as argument, ToUseBaseUrl is true, add the env base url to prefix. ApplicationReq needs to set via Mapper before passing as argument
// temporary v2 redirect activtity since v1 is already being used elsewhere
// TODO: need to make the above below acitivity backwards compatible according to the above aciivity
// Expected values in req:
// UserID:
// Intent:
// LoanApplicationID:
// SourceEntityID:
// LenderID:
// RedirectURL:
func (a *Activity) LisaRedirectUserV2(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	type argumentStruct struct {
		UserID         string                        `arg:"userID" required:"true"`
		LenderID       string                        `arg:"lenderID" required:"true"`
		ApplicationReq *lenderservice.ApplicationReq `arg:"applicationReq" required:"true"`
		ToUseBaseURL   bool                          `arg:"toUseBaseUrl" required:"true"`
		OutputKey      string                        `arg:"outputKey"`
	}

	var args argumentStruct
	err = GetArgsV2(&args, data)
	if err != nil {
		logger.WithUser(args.UserID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"UserID": args.UserID}, err)
		return ars, err
	}

	if args.ToUseBaseURL {
		args.ApplicationReq.RedirectURL = conf.BaseURL + args.ApplicationReq.RedirectURL
	}
	resp, err := lenderservice.RedirectUser(ctx, args.ApplicationReq)
	if err != nil {
		logger.WithUser(args.UserID).Error(err)
		return ars, err
	}

	data["redirectionURL"] = resp.URL
	data["redirectUserResp"] = resp

	ars.Output[args.OutputKey] = resp.URL
	return ars, nil
}

// LisaNachInitV2 is used to lisa nach init api
func (a *Activity) LisaNachInitV2(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	type argumentStruct struct {
		UserID       string                     `arg:"userID" required:"true"`
		LenderID     string                     `arg:"lenderID" required:"true"`
		NachInitReq  *lenderservice.NachInitReq `arg:"applicationReq" required:"true"`
		ToUseBaseURL bool                       `arg:"toUseBaseUrl" required:"true"`
	}

	var args argumentStruct
	err = GetArgsV2(&args, data)
	if err != nil {
		logger.WithUser(args.UserID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"UserID": args.UserID}, err)
		return ars, err
	}

	if args.ToUseBaseURL {
		args.NachInitReq.RedirectURL = conf.BaseURL + args.NachInitReq.RedirectURL
	}

	resp, err := lenderservice.InitiateNach(context.TODO(), args.NachInitReq)

	if err != nil {
		logger.WithUser(args.UserID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	if resp == nil {
		err = errors.New("lisa nach/init returned nil response")
		logger.WithUser(args.UserID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	output["redirectionURL"] = resp.NachURL
	data["mandateID"] = resp.MandateID
	data["nachInitResponse"] = resp
	return ars, nil
}

func (a *Activity) LisaGetLoanDetails(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	type argumentStruct struct {
		UserID         string                        `arg:"userID" required:"true"`
		ClosureDate    string                        `arg:"closureDate"`
		ApplicationReq *lenderservice.ApplicationReq `arg:"applicationReq" required:"true"`
	}

	var args argumentStruct
	err = GetArgsV2(&args, data)
	if err != nil {
		logger.WithUser(args.UserID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"UserID": args.UserID}, err)
		return ars, err
	}
	resp, err := lenderservice.GetLoanDetails(ctx, &lenderservice.LoanDetailsReq{
		ClosureDate:    args.ClosureDate,
		ApplicationReq: *args.ApplicationReq,
	})
	if err != nil {
		logger.WithUser(args.UserID).Error(err)
		return ars, err
	}
	data["loanDetails"] = resp
	return ars, nil
}

func (a *Activity) LisaApplicationStatusV3(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	type argumentStruct struct {
		UserID        string                              `arg:"userID" required:"true"`
		AppStatustReq *lenderservice.ApplicationStatusReq `arg:"applicationReq" required:"true"`
	}

	var args argumentStruct
	err = GetArgsV2(&args, data)
	if err != nil {
		logger.WithUser(args.UserID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"UserID": args.UserID}, err)
		return ars, err
	}

	resp, err := lenderservice.ApplicationStatus(context.TODO(), args.AppStatustReq, lenderservice.ApplicationStatusResource)

	if err != nil {
		logger.WithUser(args.UserID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	data["appStatusResp"] = resp
	return ars, nil
}

func (a *Activity) LisaRedirection(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	type request struct {
		LenderID    string `arg:"lenderID" required:"true"`
		Intent      string `arg:"intent"`
		RedirectURL string `arg:"redirectURL"`
	}

	var (
		args    request
		userObj users.User
		span    temporaltracer.Span
		resp    *lenderservice.RedirectUserURLResp
	)

	userObj, span, ars, err = initActivity(ctx, a.Tracer, data, output)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}

	defer func() {
		span.Finish(err)
	}()
	err = GetArgsV2(&args, data)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}

	appReq := lenderservice.ApplicationReq{
		UserID:         userObj.ID,
		LenderID:       args.LenderID,
		SourceEntityID: userObj.SourceEntityID,
		RedirectURL:    args.RedirectURL,
		Intent:         args.Intent,
	}

	resp, err = lenderservice.RedirectUser(context.TODO(), &appReq)

	if err != nil {
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	} else if resp == nil {
		return ars, errors.New("lenderservice call failed: null body")
	}

	data[runnerconstants.ActivityOutput] = resp
	return ars, nil
}

func (a *Activity) GenericFormPostHTMLGeneration(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	type request struct {
		FormName           string         `arg:"formName" required:"true"`
		URL                string         `arg:"url" required:"true"`
		JSONFormDataConfig map[string]any `arg:"jsonFormDataConfig" required:"true"`
	}

	var (
		args    request
		userObj users.User
		span    temporaltracer.Span
	)

	userObj, span, ars, err = initActivity(ctx, a.Tracer, data, output)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}

	defer func() {
		span.Finish(err)
	}()
	err = GetArgsV2(&args, data)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}
	outputMap := make(map[string]any)
	err = mapper.JQMapper(data, args.JSONFormDataConfig, outputMap)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}
	general.MergeMaps(outputMap, map[string]any{
		"url":      args.URL,
		"formName": args.FormName,
	})

	ars.Output["formDataDynamicParams"] = outputMap
	return ars, nil
}
