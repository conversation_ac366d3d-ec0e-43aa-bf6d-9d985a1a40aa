package activities

import (
	"context"
	"finbox/go-api/constants"
	"finbox/go-api/errorHandler"
	"finbox/go-api/functions/agreement"
	"finbox/go-api/functions/logger"
	"finbox/go-api/utils/general"
	"fmt"
	"time"

	"github.com/finbox-in/road-runner/runner"
	"go.temporal.io/sdk/activity"
)

// GenerateAgreementFromTemplate is an activity to generate agreement from template
func (a *Activity) GenerateAgreementFromTemplate(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var loanApplicationID string
	logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Info("loan application id : ", data["loanApplicationID"])
	err = general.DecodeToStruct(data["loanApplicationID"], &loanApplicationID)
	logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Info("loan application id : ", loanApplicationID)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}
	s3ObjectKey, htmlBody := agreement.GenerateUnsignedAgreement(loanApplicationID, time.Now(), false, "")
	if s3ObjectKey == "" || htmlBody == "" {
		generateAgreementErr := fmt.Errorf("error in generating agreement for loan application %s", loanApplicationID)
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(generateAgreementErr)
		errorHandler.ReportToSentryWithoutRequest(generateAgreementErr)
		return ars, generateAgreementErr
	}
	data["UnsignedAgreementS3ObjectKey"] = s3ObjectKey
	return ars, nil
}

// TODO: Consider making this a stateless generic API to generate document on demand - as the financial calculations in the document are driven by date of document generation and thus having this as an activity makes expiry of such documents tricky.
func (a *Activity) GenerateLenderDocument(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   map[string]any{},
		Output: map[string]any{},
	}

	var args struct {
		LoanApplicationID string                 `arg:"loanApplicationID" required:"true"`
		UserID            string                 `arg:"userID" required:"true"`
		DocumentType      agreement.DocumentType `arg:"documentType" required:"true"`
		TemplatePath      string                 `arg:"templatePath" required:"true"`
		CalculateRPS      bool                   `arg:"calculateRPS"`
	}

	if err := GetArgsV2(&args, data); err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	resp, err := agreement.GenerateDocument(ctx, agreement.GenerateDocumentRequest{
		LoanApplicationID: args.LoanApplicationID,
		UserID:            args.UserID,
		DocumentType:      args.DocumentType,
		TemplatePath:      args.TemplatePath,
		GenerateHTML:      true,
		GeneratePDF:       false, // No need for PDF generation, we are handling this outside
		CalculateRPS:      args.CalculateRPS,
	})

	// TODO: Remove
	/*
		{{if .RPSData}}
			<h3>Repayment Schedule</h3>
			<p>Total Interest: {{.RPSData.TotalInterest}}</p>
			<p>Total Amount: {{.RPSData.TotalAmount}}</p>

			<table>
							<tr><th>Installment</th><th>Date</th><th>EMI</th><th>Interest</th><th>Principal</th></tr>
							{{range .RPSData.Installments}}
							<tr>
											<td>{{.InstallmentNumber}}</td>
											<td>{{.Date.Format "02/01/2006"}}</td>
											<td>{{.EMI}}</td>
											<td>{{.Interest}}</td>
											<td>{{.Principal}}</td>
							</tr>
							{{end}}
			</table>
		{{end}}
	*/
	if err != nil {
		logger.WithUser(args.UserID).Errorln(err)
		errorHandler.ReportToSentryWithFields(map[string]any{
			"loanApplicationID": args.LoanApplicationID,
			"userID":            args.UserID,
			"documentType":      string(args.DocumentType),
			"templatePath":      args.TemplatePath,
		}, err)
		return ars, err
	}

	if !resp.Success {
		err = fmt.Errorf("failed to generate document: %w", err)
		logger.WithUser(args.UserID).Errorln(err)
		errorHandler.ReportToSentryWithFields(map[string]any{
			"loanApplicationID": args.LoanApplicationID,
			"userID":            args.UserID,
		}, err)
		return ars, err
	}

	ars.Data["lenderDocument"] = map[string]interface{}{
		"htmlObjectKey": resp.HTMLPath,
	}

	return ars, nil
}
