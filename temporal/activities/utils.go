package activities

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"finbox/go-api/common/usersutil"
	"finbox/go-api/constants"
	"finbox/go-api/errorHandler"
	finboxEvents "finbox/go-api/functions/activity"
	"finbox/go-api/functions/commonutils"
	"finbox/go-api/functions/emaillib"
	"finbox/go-api/functions/legallogs"
	"finbox/go-api/functions/loanutils"
	"finbox/go-api/functions/logger"
	"finbox/go-api/functions/services/gmaps"
	"finbox/go-api/functions/services/sms"
	"finbox/go-api/infra/db"
	"finbox/go-api/models/deviations"
	"finbox/go-api/models/deviceconnectdetails"
	"finbox/go-api/models/enachthirdparty"
	"finbox/go-api/models/featureflag"
	"finbox/go-api/models/lendervariables"
	"finbox/go-api/models/loanapplication"
	"finbox/go-api/models/sourceentity"
	"finbox/go-api/models/userbankdetails"
	"finbox/go-api/models/userbusiness"
	"finbox/go-api/models/userjourney"
	"finbox/go-api/models/userloandetails"
	"finbox/go-api/models/userlocation"
	"finbox/go-api/models/usermodulemapping"
	"finbox/go-api/models/users"
	"finbox/go-api/models/userworkflows"
	"finbox/go-api/temporal/temporaltracer"
	"finbox/go-api/utils/calc"
	"finbox/go-api/utils/fraudcheckutils"
	"finbox/go-api/utils/general"
	"finbox/go-api/utils/journeyutils"
	"finbox/go-api/utils/mapdecoder"
	"finbox/go-api/utils/mapper"
	"finbox/go-api/utils/prequal"
	"finbox/go-api/utils/workflowutils"
	"fmt"
	"reflect"
	"strconv"
	"time"

	"github.com/finbox-in/road-runner/runner"
	"github.com/finbox-in/road-runner/types/argument"
	"github.com/go-playground/validator/v10"
	"go.temporal.io/sdk/activity"
	"golang.org/x/text/cases"
	"golang.org/x/text/language"
)

var (
	database = db.GetDB()
	validate = validator.New()
)

func initActivity(ctx context.Context, tracer temporaltracer.Tracer, data map[string]interface{}, output map[string]interface{}) (users.User, temporaltracer.Span, runner.ActivityReturnStruct, error) {

	var (
		span temporaltracer.Span
		ars  runner.ActivityReturnStruct
	)
	isLoaderExecution := ctx.Value("loaderExecution")
	if isLoaderExecution == nil {
		activityInfo := activity.GetInfo(ctx)
		span, _ = tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	}
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}
	var userObj users.User
	err := general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithUser(userObj.ID).Errorln(err)
	}
	return userObj, span, ars, nil
}

func (a *Activity) CheckWorkflowAttemptsByStatus(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {

	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	args := argument.GetActivityArguments(data)
	exists, moduleName := args.Get("moduleName", data)
	if !exists {
		err = fmt.Errorf("mandatory argument moduleName not passed")
		logger.WithUser(userObj.ID).Errorln(err)
		return ars, err
	}
	moduleNameStr := moduleName.(string)

	exists, status := args.Get("status", data)
	if !exists {
		err = fmt.Errorf("mandatory argument status not passed")
		logger.WithUser(userObj.ID).Errorln(err)
		return ars, err
	}
	statusStr := status.(string)

	intervalHours := 1
	exists, intervalHoursInterface := args.Get("intervalHours", data)
	if exists {
		intervalHours, err = strconv.Atoi(intervalHoursInterface.(string))
		if err != nil {
			err = fmt.Errorf("could not typecast intervalHours arg %s to int, err: %s", intervalHoursInterface.(string), err.Error())
			logger.WithUser(userObj.ID).Errorln(err)
			return ars, err
		}
	}

	retryCount, err := userworkflows.GetWorkflowCountByStatusInInterval(userObj.ID, statusStr, moduleNameStr, intervalHours)
	if err != nil {
		logger.WithUser(userObj.ID).Errorln(err)
		return ars, err
	}

	data["retryCount"] = retryCount

	return ars, nil
}

// CalculateEMI is used to calculate EMI
func (a *Activity) CalculateEMI(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	type argumentStruct struct {
		LoanApplicationID string  `arg:"loanApplicationID"`
		LenderID          string  `arg:"lenderID"`
		SourceEntityID    string  `arg:"sourceEntityID"`
		Method            string  `arg:"method"`
		Amount            float64 `arg:"amount"`
		Interest          float64 `arg:"interest"`
		Tenure            int     `arg:"tenure"`
	}

	var args argumentStruct

	err = GetArgsV2(&args, data)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, NewNonRetryableApplicationError(err.Error())
	}

	emi, _, _ := calc.GetEMI(args.Method, args.Amount, args.Tenure, args.Interest, time.Now(), args.SourceEntityID, args.LenderID, userObj.ID)
	data["calculateEMI"] = map[string]interface{}{
		"emi": emi,
	}

	return ars, nil
}

// CalculateFlagWithProbability will basically calculate value for a boolean with a probability percentage
// that is passed in arguments and set the key provided in arguments to true.
// Useful for cases where you want to do certain routing with a probability
func (a *Activity) CalculateFlagWithProbability(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	var keyForDataMap string
	var trueProbabilityPercentage int

	args := argument.GetActivityArguments(data)
	exists, keyForDataMapArg := args.Get("keyForDataMap", data)
	if !exists {
		err = fmt.Errorf("mandatory argument keyForDataMap not passed, userID: %s", userObj.ID)
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}
	keyForDataMap = general.RemoveExtraSpaces(keyForDataMapArg.(string))

	exists, trueProbabilityPercentageArg := args.Get("trueProbabilityPercentage", data)
	if !exists {
		err = fmt.Errorf("mandatory argument trueProbabilityPercentage not passed, userID: %s", userObj.ID)
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	valueToSetOnTrue := "true"
	exists, setValueOnTrue := args.Get("setValueOnTrue", data)
	if exists {
		valueToSetOnTrue = setValueOnTrue.(string)
	}

	valueToSetOnFalse := "false"
	exists, setValueOnFalse := args.Get("setValueOnFalse", data)
	if exists {
		valueToSetOnFalse = setValueOnFalse.(string)
	}

	trueProbabilityPercentage, err = strconv.Atoi(trueProbabilityPercentageArg.(string))
	if err != nil {
		err = fmt.Errorf("argument trueProbabilityPercentage: %s is not a number, err: %s, userID: %s", trueProbabilityPercentageArg.(string), err.Error(), userObj.ID)
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	isTrue := general.CalculateFlag(userObj.ID, keyForDataMap, trueProbabilityPercentage)
	if isTrue {
		data[keyForDataMap] = valueToSetOnTrue
	} else {
		data[keyForDataMap] = valueToSetOnFalse
	}

	return ars, nil
}

func (a *Activity) UpdateValueForKey(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	args := argument.GetActivityArguments(data)
	exists, key := args.Get("key", data)
	if !exists {
		err = fmt.Errorf("mandatory argument key not passed")
		logger.WithUser(userObj.ID).Errorln(err)
		return ars, err
	}

	exists, value := args.Get("value", data)
	if !exists {
		err = fmt.Errorf("mandatory argument value not passed")
		logger.WithUser(userObj.ID).Errorln(err)
		return ars, err
	}

	data[key.(string)] = value

	return ars, nil
}

func (a *Activity) GenerateOTP(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	ars.Output["esignStatus"] = ""

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	args := argument.GetActivityArguments(data)
	_, otpType := args.Get("otpType", data)
	otpTypeStr, ok := otpType.(string)
	if !ok {
		err = errors.New("otpType not found in args")
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	_, errString := sms.SendOTP(userObj.Mobile, userObj.ID, "", otpTypeStr, userObj.SourceEntityID)
	if errString != "" {
		err = fmt.Errorf("%s", errString)
		logger.WithUser(userObj.ID).Errorln(err)
		return ars, err
	}

	ars.Output["esignStatus"] = EsignStatus{
		Status: "success",
	}

	return ars, nil
}

func (a *Activity) VerifyOTP(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	args := argument.GetActivityArguments(data)
	exists, otp := args.Get("otp", data)
	if !exists {
		err = errors.New("otp not found in args")
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	exists, lat := args.Get("lat", data)
	if !exists {
		err = errors.New("lat not found in args")
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	exists, lon := args.Get("lon", data)
	if !exists {
		err = errors.New("lon not found in args")
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	exists, ipAddr := args.Get("ipAddr", data)
	if !exists {
		err = errors.New("ipAddr not found in args")
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	_, otpType := args.Get("otpType", data)
	otpTypeStr, ok := otpType.(string)
	if !ok {
		err = errors.New("otpType not found in args")
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	otpString, ok := otp.(string)
	if !ok {
		err = errors.New("otp could not be typecasted to string")
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	otpInt, err := strconv.Atoi(otpString)
	if err != nil {
		logger.WithUser(userObj.ID).Errorln(err)
		return ars, err
	}

	otpVerified, otpErrMessage := sms.VerifyOTP(ctx, userObj.Mobile, userObj.ID, otpTypeStr, otpInt, lat.(string), lon.(string), "", "", ipAddr.(string), "")

	data["otpVerified"] = otpVerified
	output["otp_verification_status"] = map[string]interface{}{
		"otpVerified":     otpVerified,
		"otpErrorMessage": otpErrMessage,
	}

	return ars, nil
}

// GenerateOTPV2 - This is the improviztion of Generate OTP where userID and sourceEntityID will be taken from args.
func (a *Activity) GenerateOTPV2(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {

	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var user users.User
	err = general.DecodeToStruct(data["userObj"], &user)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{
			"userID":         user.ID,
			"sourceEntityID": user.SourceEntityID,
		}, err)
		return ars, err
	}
	type argumentStruct struct {
		Mobile         string `arg:"mobile" validate:"required"`
		UserID         string `arg:"userID"`
		SourceEntityID string `arg:"sourceEntityID"`
		OtpType        string `arg:"otpType"`
	}

	var args argumentStruct
	err = GetArgs(&args, data)
	if err != nil {
		logger.WithUser(user.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{
			"userID":         user.ID,
			"sourceEntityID": user.SourceEntityID,
		}, err)
		return ars, err
	}

	agrumentData := argument.GetActivityArguments(data)

	var (
		updateUserMobile bool
	)

	_, updateUserMobile = argument.GetAny("updateUserMobile", data, agrumentData, updateUserMobile)

	if args.SourceEntityID == "" {
		args.SourceEntityID = user.SourceEntityID
	}

	_, errorString := sms.SendOTP(args.Mobile, args.UserID, "", args.OtpType, args.SourceEntityID)
	if errorString != "" {
		logger.WithUser(user.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{
			"userID":         user.ID,
			"sourceEntityID": user.SourceEntityID,
		}, err)
		return ars, errors.New(errorString)
	}
	if updateUserMobile {
		err = users.Update(nil, users.User{
			ID:     args.UserID,
			Mobile: args.Mobile,
		})
		if err != nil {
			logger.WithUser(user.ID).Error(err)
			errorHandler.ReportToSentryWithFields(map[string]interface{}{
				"userID":         user.ID,
				"sourceEntityID": user.SourceEntityID,
			}, err)
			return ars, err
		}
	}

	return ars, nil

}

func (a *Activity) VerifyOTPV2(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {

	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var user users.User
	err = general.DecodeToStruct(data["userObj"], &user)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{
			"userID":         user.ID,
			"sourceEntityID": user.SourceEntityID,
		}, err)
		return ars, err
	}
	type argumentStruct struct {
		OtpNumber string `arg:"otpNumber" validate:"required"`
		IPAddr    string `arg:"ipAddr"`
		Mobile    string `arg:"mobile"  validate:"required"`
		UserID    string `arg:"userID"`
		OtpType   string `arg:"otpType" `
		IpAddress string `arg:"ipAddress"`
		Latitude  string `arg:"latitude"`
		Longitude string `arg:"longitude"`
		Height    string `arg:"height"`
		Accuaracy string `arg:"accuaracy"`
	}

	var args argumentStruct
	err = GetArgs(&args, data)
	if err != nil {
		logger.WithUser(user.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{
			"userID":         user.ID,
			"sourceEntityID": user.SourceEntityID,
		}, err)
		return ars, err
	}

	if args.UserID == "" {
		args.UserID = user.ID
	}

	otpInt, err := strconv.Atoi(args.OtpNumber)
	if err != nil {
		logger.WithUser(user.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{
			"userID":         user.ID,
			"sourceEntityID": user.SourceEntityID,
		}, err)
		return ars, err
	}

	isOtpVerified, otpErrMessage := sms.VerifyOTP(ctx, args.Mobile, args.UserID, args.OtpType, otpInt, args.Latitude, args.Longitude, args.Height, args.Accuaracy, args.IPAddr, "")

	if !isOtpVerified {
		return ars, errors.New(otpErrMessage)

	}

	return ars, nil

}

func (a *Activity) VerifyOTPV3(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {

	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var user users.User
	err = general.DecodeToStruct(data["userObj"], &user)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{
			"userID":         user.ID,
			"sourceEntityID": user.SourceEntityID,
		}, err)
		return ars, err
	}

	type argumentStruct struct {
		OtpNumber int    `arg:"otpNumber" required:"true"`
		IPAddr    string `arg:"ipAddr"`
		Mobile    string `arg:"mobile"  required:"true"`
		UserID    string `arg:"userID"`
		OtpType   string `arg:"otpType" `
		IpAddress string `arg:"ipAddress"`
		Latitude  string `arg:"latitude"`
		Longitude string `arg:"longitude"`
		Height    string `arg:"height"`
		Accuaracy string `arg:"accuaracy"`
	}

	var args argumentStruct
	err = GetArgsV2(&args, data)
	if err != nil {
		logger.WithUser(user.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{
			"userID":         user.ID,
			"sourceEntityID": user.SourceEntityID,
		}, err)
		return ars, err
	}

	if args.UserID == "" {
		args.UserID = user.ID
	}

	otpInt := args.OtpNumber
	if err != nil {
		logger.WithUser(user.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{
			"userID":         user.ID,
			"sourceEntityID": user.SourceEntityID,
		}, err)
		return ars, err
	}

	isOtpVerified, otpErrMessage := sms.VerifyOTP(ctx, args.Mobile, args.UserID, args.OtpType, otpInt, args.Latitude, args.Longitude, args.Height, args.Accuaracy, args.IPAddr, "")

	data["otpVerified"] = isOtpVerified
	data["ipAddr"] = args.IpAddress
	data["latitude"] = args.Latitude
	data["longitude"] = args.Longitude
	data["height"] = args.Height
	data["accuaracy"] = args.Accuaracy
	data["otpErrorMessage"] = otpErrMessage

	var redirect bool
	if otpErrMessage == sms.ErrMaxFailedAttempts {
		redirect = true
	}
	output["otp_verification_status"] = map[string]interface{}{
		"otpVerified":     isOtpVerified,
		"otpErrorMessage": otpErrMessage,
		"status":          "completed",
		"redirect":        redirect,
	}

	return ars, nil

}

/*
This activity will block user to retry the bureau for time defined in the
workflow definition and workflow will be set to be in sleep state for same
amount of time.
*/
func (a *Activity) PutUserInWait(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	args := argument.GetActivityArguments(data)
	exists, bufferTime := args.Get("time", data)
	if !exists {
		err = fmt.Errorf("mandatory argument time not passed")
		logger.WithUser(userObj.ID).Error(err)
		return ars, err
	}

	var isoDurationTime general.ISO8601Duration
	isoDurationTime, err = general.ParseISO8601(bufferTime.(string))
	if err != nil {
		err = fmt.Errorf("could not parse time passed")
		logger.WithUser(userObj.ID).Error(err)
		return ars, err
	}

	b, _ := json.Marshal(isoDurationTime)
	logger.Log.Info("Printing the isoDurationTime here !")
	logger.Log.Info(string(b))

	expiryTime := time.Now().Add(isoDurationTime.TimeDuration())
	output["expiry"] = expiryTime.UTC()
	data["retryCount"] = -1

	return ars, nil
}

func (a *Activity) UpdateDynamicUserInfo(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	type argumentStruct struct {
		DynamicUserInfo map[string]any `arg:"dynamicUserInfo" validate:"required"`
	}
	var args argumentStruct
	err = GetArgs(&args, data)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	dynamicUserInfoString, err := users.GetDynamicUserInfo(userObj.ID)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}
	dynamicUserInfo := make(map[string]interface{})
	err = json.Unmarshal([]byte(dynamicUserInfoString), &dynamicUserInfo)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}
	for key, value := range args.DynamicUserInfo {
		dynamicUserInfo[key] = value
	}
	dynamicUserInfoBytes, err := json.Marshal(dynamicUserInfo)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}
	err = users.Update(nil, users.User{DynamicUserInfo: string(dynamicUserInfoBytes), ID: userObj.ID})
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	userObj.DynamicUserInfoMap = dynamicUserInfo
	userObj.DynamicUserInfo = string(dynamicUserInfoBytes)

	data["userObj"] = userObj
	return ars, nil
}

/*UpdateDynamicUserInfoV2 - This function is a improvisation of dynamic user info. It takes userID as well
 */
func (a *Activity) UpdateDynamicUserInfoV2(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{
			"userID":    userObj.ID,
			"WorflowID": activityInfo.WorkflowExecution.ID,
		}, err)
		return ars, err
	}

	type argumentStruct struct {
		UserID string `arg:"userID"`
	}
	var args argumentStruct
	err = GetArgs(&args, data)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{
			"userID":    userObj.ID,
			"WorflowID": activityInfo.WorkflowExecution.ID,
		}, err)
		return ars, err
	}

	if args.UserID == "" {
		args.UserID = userObj.ID
	}

	agrumentData := argument.GetActivityArguments(data)

	var (
		dynamicUserInfoArg map[string]interface{}
	)

	exists, dynamicUserInfoArg := argument.GetAny("dynamicUserInfo", data, agrumentData, dynamicUserInfoArg)
	if !exists {
		err = fmt.Errorf("Dynamic User Info parameter doesn't exist")
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{
			"userID":    userObj.ID,
			"WorflowID": activityInfo.WorkflowExecution.ID,
		}, err)
		return ars, err
	}

	dynamicUserInfoString, err := users.GetDynamicUserInfo(args.UserID)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{
			"userID":    userObj.ID,
			"WorflowID": activityInfo.WorkflowExecution.ID,
		}, err)
		return ars, err
	}
	dynamicUserInfo := make(map[string]interface{})
	err = json.Unmarshal([]byte(dynamicUserInfoString), &dynamicUserInfo)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{
			"userID":    userObj.ID,
			"WorflowID": activityInfo.WorkflowExecution.ID,
		}, err)
		return ars, err
	}
	for key, value := range dynamicUserInfoArg {
		dynamicUserInfo[key] = value
	}
	dynamicUserInfoBytes, err := json.Marshal(dynamicUserInfo)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{
			"userID":    userObj.ID,
			"WorflowID": activityInfo.WorkflowExecution.ID,
		}, err)
		return ars, err
	}
	err = users.Update(nil, users.User{DynamicUserInfo: string(dynamicUserInfoBytes), ID: args.UserID})
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{
			"userID":    userObj.ID,
			"WorflowID": activityInfo.WorkflowExecution.ID,
		}, err)
		return ars, err
	}

	if args.UserID == userObj.ID {
		userObj.DynamicUserInfoMap = dynamicUserInfo
		userObj.DynamicUserInfo = string(dynamicUserInfoBytes)
		data["userObj"] = userObj
	}

	return ars, nil
}

// UserModuleMappingInsert inserts pending entry in user_module_mapping table
func (a *Activity) UserModuleMappingInsert(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	lastModule, err := usermodulemapping.GetLast(userObj.ID)
	if err != nil && err != sql.ErrNoRows {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	args := argument.GetActivityArguments(data)
	exists, module := args.Get("module", data)
	if !exists {
		err = errors.New("module not found in args")
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	moduleName, ok := module.(string)
	if !ok {
		err = errors.New("module not found")
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	isValidModule, err := commonutils.IsValidModule(moduleName)
	if err != nil {
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	if !isValidModule {
		err = fmt.Errorf("invalid moduleName  - %s", moduleName)
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	if lastModule != nil && lastModule.ModuleName != moduleName {
		if err = usermodulemapping.Create(nil, userObj.ID, userObj.ID, moduleName, constants.UserModuleStatusPending, ""); err != nil {
			logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
			errorHandler.ReportToSentryWithoutRequest(err)
			return ars, err
		}
	}

	return ars, nil
}

func (a *Activity) UserModuleMappingInsertSuccess(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{
			"userID":    userObj.ID,
			"WorflowID": activityInfo.WorkflowExecution.ID,
		}, err)
		return ars, err
	}

	lastModule, err := usermodulemapping.GetLast(userObj.ID)
	if err != nil && err != sql.ErrNoRows {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{
			"userID":    userObj.ID,
			"WorflowID": activityInfo.WorkflowExecution.ID,
		}, err)
		return ars, err
	}

	args := argument.GetActivityArguments(data)
	exists, module := args.Get("module", data)
	if !exists {
		err = errors.New("module not found in args")
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{
			"userID":    userObj.ID,
			"WorflowID": activityInfo.WorkflowExecution.ID,
		}, err)
		return ars, err
	}

	moduleName, ok := module.(string)
	if !ok {
		err = errors.New("module not found")
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{
			"userID":    userObj.ID,
			"WorflowID": activityInfo.WorkflowExecution.ID,
		}, err)
		return ars, err
	}

	isValidModule, err := commonutils.IsValidModule(moduleName)
	if err != nil {
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{
			"userID":    userObj.ID,
			"WorflowID": activityInfo.WorkflowExecution.ID,
		}, err)
		return ars, err
	}

	if !isValidModule {
		err = fmt.Errorf("invalid moduleName  - %s", moduleName)
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{
			"userID":    userObj.ID,
			"WorflowID": activityInfo.WorkflowExecution.ID,
		}, err)
		return ars, err
	}

	if lastModule != nil && lastModule.ModuleName != moduleName {
		if err = usermodulemapping.Create(nil, userObj.ID, userObj.ID, moduleName, constants.UserModuleStatusCompleted, ""); err != nil {
			logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
			errorHandler.ReportToSentryWithFields(map[string]interface{}{
				"userID":    userObj.ID,
				"WorflowID": activityInfo.WorkflowExecution.ID,
			}, err)
			return ars, err
		}
	}

	return ars, nil
}

// UpdateUserModuleMappingSuccess update entry in user_module_mapping table
func (a *Activity) UpdateUserModuleMappingSuccess(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	args := argument.GetActivityArguments(data)
	isModuleNamePassed, moduleName := args.Get("moduleName", data)
	if !isModuleNamePassed {
		err = fmt.Errorf("argument not found in %s activity args, userID: %s, workflowID: %s, runID: %s", activityInfo.ActivityType.Name, userObj.ID, activityInfo.WorkflowExecution.ID, activityInfo.WorkflowExecution.RunID)
		errorHandler.ReportToSentryWithoutRequest(err)
		logger.WithUser(userObj.ID).Errorln(err)
		return ars, err
	}

	logger.WithUser(userObj.ID).Errorln(args)
	logger.WithUser(userObj.ID).Errorln(moduleName.(string))

	isValidModule, err := commonutils.IsValidModule(moduleName.(string))
	if err != nil {
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	if !isValidModule {
		err = fmt.Errorf("invalid moduleName  - %s", moduleName.(string))
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	if err = usermodulemapping.UpdateModuleStatus(userObj.ID, moduleName.(string), constants.UserModuleStatusCompleted); err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	return ars, nil
}

// MoveUserToDisbursedState inserts details around disbursal.
func (a *Activity) MoveUserToDisbursedState(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	lastModule, err := usermodulemapping.GetLast(userObj.ID)
	if err != nil && err != sql.ErrNoRows {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	args := argument.GetActivityArguments(data)
	exists, module := args.Get("module", data)
	if !exists {
		err = errors.New("module not found in args")
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	moduleName, ok := module.(string)
	if !ok {
		err = errors.New("module not found")
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	isValidModule, err := commonutils.IsValidModule(moduleName)
	if err != nil {
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	if !isValidModule {
		err = fmt.Errorf("invalid moduleName  - %s", moduleName)
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	if lastModule != nil && lastModule.ModuleName != moduleName {
		if err = usermodulemapping.Create(nil, userObj.ID, userObj.ID, moduleName, constants.UserModuleStatusCompleted, ""); err != nil {
			logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
			errorHandler.ReportToSentryWithoutRequest(err)
			return ars, err
		}
	}

	loanApp, err := loanapplication.GetLatestByUser(userObj.ID)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		return ars, err
	}

	status := constants.LoanStatusDisbursed
	err = loanapplication.Update(nil, loanapplication.StructForSet{
		ID:              loanApp.ID.String(),
		DisbursedDate:   time.Now().Format(constants.DateFormat),
		DisbursedAmount: loanApp.Amount,
		Status:          &status,
	})
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		return ars, err
	}

	return ars, nil
}

func (a *Activity) DisburseLoan(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {

	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}
	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	loanApp, err := loanapplication.GetLatestByUser(userObj.ID)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		return ars, err
	}

	errString, err := loanutils.DisburseLoan(loanApp.ID.String(), loanApp.LenderID, "", "", time.Now().Format(constants.DateFormat), false)
	if err != nil || errString != "" {
		logger.WithUser(userObj.ID).Error(err, errString)
		return ars, err
	}

	return ars, nil

}

/*
# Prefer using GetArgsV2 over this.

Usage:

	type argument struct {
		Arg1           string `arg:"arg1" validate:"required"`
		Arg2           string `arg:"arg2"`
	}
	var args argument
	err = GetArgs(&args, data)
	if err != nil {
		do stuff
	}
*/
func GetArgs(arguments interface{}, data map[string]interface{}) (err error) {
	defer func() {
		if r := recover(); r != nil {
			switch errType := r.(type) {
			case string:
				err = errors.New(errType)
			case error:
				err = errType
			}
		}
	}()
	if arguments == nil || data == nil {
		return ErrNilObjReceived()
	}
	if reflect.TypeOf(arguments).Kind() != reflect.Pointer {
		return ErrUnsupportedType()
	}
	reflectValue := reflect.ValueOf(arguments).Elem()
	reflectType := reflect.TypeOf(arguments).Elem()
	args := argument.GetActivityArguments(data)
	for index := 0; index < reflectType.NumField(); index++ {
		fieldType := reflectType.Field(index)
		argumentField := fieldType.Tag.Get("arg")
		validate := fieldType.Tag.Get("validate")
		if argumentField == "" {
			return ErrRequiredTagsMissing()
		}
		exists, argValue := args.Get(argumentField, data)
		if !exists {
			if validate == "required" {
				return ErrArgumentDoesNotExist(argumentField)
			}
			continue
		}
		reflectValue.Field(index).Set(reflect.ValueOf(argValue).Convert(reflectValue.Field(index).Type()))
	}
	return nil
}

/*
Usage:

	type argument struct {
		FirstName string `arg:"firstName" required:"true" validate:"required"`
		SmsRequest sms.SendDLTSMSViaKarixReq `arg:"smsRequest"`
		SmsRequestOptional *sms.SendDLTSMSViaKarixReq `arg:"smsRequest"`
		Hobbies []string `arg:"hobbies" required:"true"`
		Education []education `arg:"education"`
		UserState string `arg:"userState" required:"true" validate:"oneof=one two three"`
		Age int `arg:"age" required:"true" validate:"gte=18,lte=65"`
		Metadata map[string]interface{} `arg:"metadata"`
		Email string `arg:"email" required:"true"`
		Gender string `arg:"gender"`
	}
	var args argument
	err = GetArgsV2(&args, data)
	if err != nil {
		do stuff
	}

Validations are only applied on fields which are present.

Please use this over GetArgs
*/
func GetArgsV2(arguments interface{}, data map[string]interface{}) (err error) {
	defer func() {
		if r := recover(); r != nil {
			switch errType := r.(type) {
			case string:
				err = errors.New(errType)
			case error:
				err = errType
			}
		}
	}()
	if arguments == nil || data == nil {
		return ErrNilObjReceived()
	}
	if reflect.TypeOf(arguments).Kind() != reflect.Pointer {
		return ErrUnsupportedType()
	}
	reflectValue := reflect.ValueOf(arguments).Elem()
	reflectType := reflect.TypeOf(arguments).Elem()
	args := argument.GetActivityArguments(data)

	for index := 0; index < reflectType.NumField(); index++ {
		fieldType := reflectType.Field(index)
		argumentField := fieldType.Tag.Get("arg")
		required := fieldType.Tag.Get("required")
		validateTag := fieldType.Tag.Get("validate")

		if argumentField == "" {
			return ErrRequiredTagsMissing()
		}
		var anyType any
		exists, argValue := argument.GetAny(argumentField, data, args, anyType)
		if !exists {
			if required == "true" {
				return ErrArgumentDoesNotExist(argumentField)
			}
			continue
		}

		field := reflectValue.Field(index)

		if field.Kind() == reflect.Ptr {
			// Handle pointer types
			if argValue == nil && required != "true" { //	If the argument is nil and not required, we can skip filling the pointer value
				continue
			}
		}

		if err := mapdecoder.JSONDecoder(argValue, field.Addr().Interface()); err != nil {
			logger.Log.Errorln(err)
			return err
		}

		// Apply validations only on fields which are present
		if err := validate.Var(argValue, validateTag); err != nil {
			validationErrs := err.(validator.ValidationErrors)
			for _, validationErr := range validationErrs {
				structTagName := fieldType.Name          // The name of the struct field. For eg. `FavoriteColor`
				validationTag := validationErr.Tag()     // The tag which failed the validation. For eg. `oneof`
				validationParam := validationErr.Param() // The parameter passed to the tag. For eg. `red blue green`

				// Return the first validation error
				return ErrValidationFailed(structTagName, validationTag, validationParam, argValue)
			}
			return validationErrs
		}
	}
	return nil
}

/*
Usage in workflows:
1. Set value for key disableChangePAN using jq expression
"actions": [

	  {
	    "functionRef": {
	      "refName": "Mapper",
	      "arguments": {
	        "config": {
	          "disableChangePAN":"if .fraudCheckStatus == 3 then \"true\" else \"false\" end"
	        }
	      }
	    }
	  }
	]

2. set value in a nested key pan using jq expression
"actions": [

	  {
	    "functionRef": {
	      "refName": "Mapper",
	      "arguments": {
	        "config": {
	          "signal_data_personal_info_submit":{
	            "pan":".signal_data_change_pan.pan"
	          }
	        }
	      }
	    }
	  }
	]
*/
func (a *Activity) Mapper(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	type argumentStruct struct {
		Config map[string]any `arg:"config" validate:"required"`
	}
	var args argumentStruct
	err = GetArgsV2(&args, data)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	mergeMap := make(map[string]any)
	err = mapper.JQMapper(data, args.Config, mergeMap)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	log.Debugln(mergeMap)
	general.MergeMaps(data, mergeMap)
	return ars, nil
}

// MapperV3 is an optimized version of the Mapper activity that avoids storing excessive data in the workflow history.
// Unlike the original Mapper which returns all data back to the workflow, MapperV3 only returns the mapped values,
// helping to keep the workflow history size minimal. This is particularly useful when dealing with large data sets
// or high-volume workflows where history size could become a concern.
func (a *Activity) MapperV3(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   map[string]any{},
		Output: map[string]any{},
	}

	var args struct {
		Config map[string]any `arg:"config" required:"true"`
	}
	err = GetArgsV2(&args, data)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	mergeMap := make(map[string]any)
	err = mapper.JQMapper(data, args.Config, mergeMap)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}
	ars.Data = mergeMap
	return ars, nil
}

func MergeMaps(dst, src map[string]any) {
	for key, srcVal := range src {
		if dstVal, exists := dst[key]; exists {
			dstMap, dstIsMap := dstVal.(map[string]any)
			srcMap, srcIsMap := srcVal.(map[string]any)
			if dstIsMap && srcIsMap {
				MergeMaps(dstMap, srcMap)
			} else {
				dst[key] = srcVal
			}
		} else {
			dst[key] = srcVal
		}
	}
}

func (a *Activity) OutputMapper(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}

	type argumentStruct struct {
		Config map[string]any `arg:"config" validate:"required"`
	}
	var args argumentStruct
	err = GetArgs(&args, data)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}
	mergeMap := make(map[string]any)

	err = mapper.JQMapper(data, args.Config, mergeMap)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}
	log.Debugln(mergeMap)
	general.MergeMaps(output, mergeMap)
	return ars, nil
}

func (a *Activity) OutputMapperV2(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}

	type argumentStruct struct {
		Config map[string]any `arg:"config" required:"true"`
	}
	var args argumentStruct
	err = GetArgsV2(&args, data)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}
	mergeMap := make(map[string]any)

	err = mapper.JQMapper(data, args.Config, mergeMap)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}
	general.MergeMaps(output, mergeMap)
	return ars, nil
}

func (a *Activity) SetUserWaitState(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}

	type argumentStruct struct {
		Value string `arg:"value" validate:"required"`
	}
	var args argumentStruct
	err = GetArgs(&args, data)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}
	waitState, err := strconv.ParseBool(args.Value)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}
	err = usersutil.SetUserWaitState(userObj.ID, waitState)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}
	return ars, nil
}

func (a *Activity) SetUserJourneyWaitState(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	type argumentStruct struct {
		Status int    `arg:"status" required:"true"`
		Reason string `arg:"reason" required:"true"`
	}
	var args argumentStruct
	err = GetArgsV2(&args, data)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	err = userjourney.SetWaitStatus(nil, userObj.ID, args.Status, args.Reason)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}
	return ars, nil
}

// CancelExistingENACH cancels NACH if exists
func (a *Activity) CancelExistingENACH(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}

	var args struct {
		LoanApplicationID string `arg:"loanApplicationID" validate:"required"`
	}
	err = GetArgs(&args, data)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}

	if err = enachthirdparty.CancelExistingENACH(nil, args.LoanApplicationID); err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}

	return ars, nil
}

func (a *Activity) AgeCheckV3(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	output["ageCheck"] = map[string]interface{}{
		"isError":     false,
		"errorString": "",
	}
	data["isError"] = false

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}
	args := argument.GetActivityArguments(data)
	exists, lowerAgeLimitArg := args.Get("lowerAgeLimit", data)
	if !exists {
		err = errors.New("lower age limit not exists")
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}
	exists, upperAgeLimitArg := args.Get("upperAgeLimit", data)
	if !exists {
		err = errors.New("upper age limit not exists")
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}
	exists, ageArg := args.Get("age", data)
	if !exists {
		err = errors.New("age not exists")
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}
	lowerAgeLimit, _ := strconv.ParseFloat(lowerAgeLimitArg.(string), 32)
	upperAgeLimit, _ := strconv.ParseFloat(upperAgeLimitArg.(string), 32)
	age, _ := ageArg.(string)

	qualify, err := prequal.AgeCheck(int(lowerAgeLimit), int(upperAgeLimit), userObj.ID, userObj.SourceEntityID, age)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}

	if !qualify {
		output["ageCheck"] = map[string]interface{}{
			"isError":     true,
			"errorString": "age criteria failed",
		}
		data["isError"] = true
	}

	return ars, nil
}

// UpdateUserWorkflowStatus : Updates the status of user's temporal workflow present in `user_workflows` table
func (a *Activity) UpdateUserWorkflowStatus(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}

	userID := userObj.ID
	workflowID := activityInfo.WorkflowExecution.ID
	runID := activityInfo.WorkflowExecution.RunID

	args := argument.GetActivityArguments(data)
	exists, workflowStatusObj := args.Get("workflowStatus", data)
	if !exists {
		err = fmt.Errorf("[UpdateUserWorkflowStatus] workflowStatus not found in args, userID: %s, workflowID: %s, runID: %s", userID, workflowID, runID)
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}

	// get error type from args
	exists, errorTypeObj := args.Get("errorType", data)
	errorType := ""
	if exists {
		errorType = errorTypeObj.(string)
	}

	// get failure reason from args
	exists, failureReasonObj := args.Get("failureReason", data)
	failureReason := ""
	if exists {
		failureReason = failureReasonObj.(string)
	}

	workflowStatus := workflowStatusObj.(string)

	err = userworkflows.UpdateStatus(nil, userID, workflowStatus, workflowID, runID, errorType, failureReason)
	if err != nil {
		err = fmt.Errorf("[UpdateUserWorkflowStatus] failed to update workflow status, userID: %s, workflowID: %s, runID: %s", userID, workflowID, runID)
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}

	return ars, nil
}

func (a *Activity) EndState(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}

	return ars, nil
}

func (a *Activity) SendEmail(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	args := argument.GetActivityArguments(data)

	var (
		exists               bool
		senderEmailID        string
		senderDisplayName    string
		recipientEmailID     string
		recipientDisplayName string
		emailConfig          map[string]interface{}
		emailBodyKey         string
		emailSubjectKey      string
	)

	exists, senderEmailID = argument.GetAny("senderEmailID", data, args, senderEmailID)
	if !exists {
		err = fmt.Errorf("senderEmailID not found in args")
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}

	exists, senderDisplayName = argument.GetAny("senderDisplayName", data, args, senderDisplayName)
	if !exists {
		err = fmt.Errorf("senderDisplayName not found in args")
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}

	exists, recipientEmailID = argument.GetAny("recipientEmailID", data, args, recipientEmailID)
	if !exists {
		err = fmt.Errorf("recipientEmailID not found in args")
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}

	exists, recipientDisplayName = argument.GetAny("recipientDisplayName", data, args, recipientDisplayName)
	if !exists {
		err = fmt.Errorf("recipientDisplayName not found in args")
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}

	exists, emailConfig = argument.GetAny("emailConfig", data, args, emailConfig)
	if !exists {
		err = fmt.Errorf("emailConfig not found in args")
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}

	exists, emailBodyKey = argument.GetAny("emailBodyKey", data, args, emailBodyKey)
	if !exists {
		err = fmt.Errorf("emailBodyKey not found in args")
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}

	exists, emailSubjectKey = argument.GetAny("emailSubjectKey", data, args, emailSubjectKey)
	if !exists {
		err = fmt.Errorf("emailSubjectKey not found in args")
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}

	if !general.ValidateEmail(senderEmailID) {
		err = fmt.Errorf("invalid sender email - %s", senderEmailID)
		logger.WithUser(userObj.ID).Errorln(err)
	}
	if !general.ValidateEmail(recipientEmailID) {
		err = fmt.Errorf("invalid recipient email - %s", recipientEmailID)
		logger.WithUser(userObj.ID).Errorln(err)
	}

	if _, exists := emaillib.EmailSubjectKeyMap[emailSubjectKey]; !exists {
		err = fmt.Errorf("email subject not found for given key - %s", emailSubjectKey)
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}
	if _, exists := emaillib.EmailBodyKeyMap[emailBodyKey]; !exists {
		err = fmt.Errorf("email body not found for given key - %s", emailBodyKey)
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}

	var emailSubject, emailBody string

	emailSubject = general.GetStringFromTemplate(emaillib.EmailSubjectKeyMap[emailSubjectKey], emailConfig)
	emailBody = general.GetStringFromTemplate(emaillib.EmailBodyKeyMap[emailBodyKey], emailConfig)

	recipientEmails := []string{recipientEmailID}
	recipientNames := []string{recipientDisplayName}

	go func() {
		emaillib.SendMailFrom(senderEmailID, senderDisplayName, recipientEmails, recipientNames, emailSubject, emailBody, nil, false)
	}()

	return ars, nil
}

// Deprecated: Use UpdateUserWorkflow instead
func (a *Activity) UpdateUserWorkflowMetadata(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}
	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}
	args := argument.GetActivityArguments(data)

	isUserWorkflowIDPassed, userWorkflowID := args.Get("userWorkflowID", data)
	if !isUserWorkflowIDPassed {
		err = fmt.Errorf("mandatory argument userWorkflowID not passed")
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		logger.WithUser(userObj.ID).Errorln(err)
		return ars, err
	}

	// args.Get doesn't work for now for nested fields, so using direct map access
	userWorkflowMetadata, ok := data["userWorkflowMetadata"]
	if !ok {
		err = fmt.Errorf("mandatory data field userWorkflowMetadata not found")
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		logger.WithUser(userObj.ID).Errorln(err)
		return ars, err
	}

	metadata, ok := userWorkflowMetadata.(map[string]interface{})
	if !ok {
		err = fmt.Errorf("userWorkflowMetadata should be a JSON object")
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		logger.WithUser(userObj.ID).Errorln(err)
		return ars, err
	}
	metadataMap := make(map[string]interface{})
	err = general.DecodeToStruct(metadata, &metadataMap)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}

	uwf := userworkflows.UserWorkflow{
		ID:       userWorkflowID.(string),
		Metadata: general.AnyToJSONString(metadataMap),
	}
	err = uwf.UpdateUsingID(context.Background(), nil)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}
	return ars, nil
}

// UpdateUserWorkflow : Updates the fields of user's temporal workflow present in `user_workflows` table
func (a *Activity) UpdateUserWorkflow(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()

	ars = runner.ActivityReturnStruct{
		Data:   map[string]interface{}{},
		Output: map[string]interface{}{},
	}

	var args struct {
		UserWorkflowID string                 `arg:"userWorkflowID" required:"true"`
		Metadata       map[string]interface{} `arg:"metadata"`
		ModuleName     string                 `arg:"moduleName"`
		Status         string                 `arg:"status"`
		FailureReason  string                 `arg:"failureReason"`
		ErrorType      string                 `arg:"errorType"`
		RunID          string                 `arg:"runID"`
		WorkflowID     string                 `arg:"workflowID"`
	}

	if err = GetArgsV2(&args, data); err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithFields(errorHandler.ErrorFields{"workflowID": activityInfo.WorkflowExecution.ID, "args": args}, err)
		return ars, err
	}

	var errorType sql.NullString
	if args.ErrorType != "" {
		errorType = sql.NullString{
			String: args.ErrorType,
			Valid:  true,
		}
	}

	uwf := userworkflows.UserWorkflow{
		ID:            args.UserWorkflowID,
		Metadata:      general.AnyToJSONString(args.Metadata),
		ModuleName:    args.ModuleName,
		Status:        args.Status,
		FailureReason: args.FailureReason,
		ErrorType:     errorType,
		RunID:         args.RunID,
		WorkflowID:    args.WorkflowID,
	}

	if err = uwf.UpdateUsingID(context.Background(), nil); err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithFields(errorHandler.ErrorFields{"workflowID": activityInfo.WorkflowExecution.ID, "userWorkflowID": args.UserWorkflowID}, err)
		return ars, err
	}

	return ars, nil
}

// SaveConsent - saves consent to legallogs table
func (a *Activity) SaveConsent(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	var args struct {
		ConsentText    string `arg:"consentText" validate:"required"`
		ConsentTextArr string `arg:"consentTextArr"`
		ConsentType    string `arg:"consentType"`
		Latitude       string `arg:"lat"`
		Longitude      string `arg:"lon"`
		Height         string `arg:"height"`
		Accuracy       string `arg:"accuracy"`
		IPAddress      string `arg:"ipAddr" validate:"required"`
	}

	if err = GetArgs(&args, data); err != nil {
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}
	if args.ConsentTextArr != "" {
		var consentTextArr []string
		err = json.Unmarshal([]byte(args.ConsentTextArr), &consentTextArr)
		if err != nil {
			logger.WithUser(userObj.ID).Error(err)
			errorHandler.ReportToSentryWithoutRequest(err)
			return ars, err
		}
		for _, consentText := range consentTextArr {
			if err = legallogs.SaveWithConsentTypeV2(ctx, userObj.ID, consentText, args.ConsentType, args.Latitude, args.Longitude, args.Height, args.Accuracy, args.IPAddress, time.Now()); err != nil {
				logger.WithUser(userObj.ID).Errorln(err)
				errorHandler.ReportToSentryWithoutRequest(err)
				return ars, err
			}
		}
	}
	if args.ConsentText != "" {
		if err = legallogs.SaveWithConsentTypeV2(ctx, userObj.ID, args.ConsentText, args.ConsentType, args.Latitude, args.Longitude, args.Height, args.Accuracy, args.IPAddress, time.Now()); err != nil {
			logger.WithUser(userObj.ID).Errorln(err)
			errorHandler.ReportToSentryWithoutRequest(err)
			return ars, err
		}
	}

	return ars, nil
}

func (a *Activity) VelocityCheck(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{
			"userID": userObj.ID,
		}, err)
		return ars, err
	}

	type argumentStruct struct {
		FraudCheckType      string `arg:"fraudCheckType"`
		EnableVelocityCheck string `arg:"enableVelocityCheck"`
		ThresholdCount      int    `arg:"thresholdCount"`
		Remark              string `arg:"remark"`
		DontCount           string `arg:"dontCount"`
		RejectReason        string `arg:"rejectReason"`
	}

	var args argumentStruct
	err = GetArgs(&args, data)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}

	if args.EnableVelocityCheck == "true" && args.ThresholdCount == 0 {
		err = errors.New("threshold count missing in arguments")
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{
			"userID": userObj.ID,
		}, err)
		return ars, err
	}

	data["isFraud"] = false

	if args.EnableVelocityCheck == "true" {
		velocityCheckRemark := args.Remark
		dontCount, err := strconv.ParseBool(args.DontCount)
		if err != nil {
			return ars, err
		}
		dontCountPtr := &dontCount
		entryID, isFraud, err := fraudcheckutils.VelocityCheck(userObj.ID, "", userObj.SourceEntityID, args.FraudCheckType, velocityCheckRemark, args.ThresholdCount)
		if err != nil {
			logger.WithUser(userObj.ID).Error(err)
			errorHandler.ReportToSentryWithFields(map[string]interface{}{
				"userID": userObj.ID,
			}, err)
			return ars, err
		}

		data["isFraud"] = isFraud
		data["rejectReason"] = args.RejectReason

		defer func() {
			err := fraudcheckutils.VelocityCountRemove(entryID, dontCountPtr)
			if err != nil {
				logger.WithUser(userObj.ID).Error(err)
				errorHandler.ReportToSentryWithFields(map[string]interface{}{
					"userID": userObj.ID,
				}, err)
			}
		}()
	}
	return ars, err
}

func (a *Activity) UpdateUserObj(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}

	user, err := users.Get(userObj.ID)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}

	data["userObj"] = user
	return ars, nil
}

func (a *Activity) FetchUserBusinessData(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}
	userBusinessData, err := userbusiness.Get(ctx, userObj.ID)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}
	data["userBusinessData"] = userBusinessData
	return ars, nil
}

// ForceFailWorkflow is an activity that will return an error to the workflow with the passed failureReason
// CAUTION: This activity was made to fail a workflow on infinite polling. DO NOT USE this in ur workflows
// without proper permissions
func (a *Activity) ForceFailWorkflow(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}
	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}

	type arguments struct {
		FailureReason string `arg:"failureReason"`
	}

	var args arguments
	err = GetArgs(&args, data)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	failureReason := args.FailureReason

	errorString := fmt.Sprintf("force fail - %s", failureReason)
	errorString = general.RemoveExtraSpaces(errorString)
	err = fmt.Errorf(errorString)

	return ars, err
}

func (a *Activity) SetWait(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}
	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	output["isWait"] = true
	output["isUserWait"] = ""

	return ars, nil
}

func (a *Activity) ReleaseWait(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}
	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	output["isWait"] = false
	output["isUserWait"] = "success"

	return ars, nil
}

// UpdateUserWorkflowStatusV2 : Updates the status of user's temporal workflow present in `user_workflows` table, takes userID from args
func (a *Activity) UpdateUserWorkflowStatusV2(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()

	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	type argumentStruct struct {
		UserID         string `arg:"userID" validate:"required"`
		WorkflowStatus string `arg:"workflowStatus" validate:"required"`
		ErrorType      string `arg:"errorType"`
		FailureReason  string `arg:"failureReason"`
	}

	var args argumentStruct

	err = GetArgs(&args, data)
	if err != nil {
		logger.WithUser(args.UserID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	workflowID := activityInfo.WorkflowExecution.ID
	runID := activityInfo.WorkflowExecution.RunID

	err = userworkflows.UpdateStatus(nil, args.UserID, args.WorkflowStatus, workflowID, runID, args.ErrorType, args.FailureReason)
	if err != nil {
		err = fmt.Errorf("[UpdateUserWorkflowStatusV2] failed to update workflow status, err: %s, userID: %s, workflowID: %s, runID: %s", err, args.UserID, workflowID, runID)
		logger.WithUser(args.UserID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	return ars, nil
}

func (a *Activity) SwitchCurrentModulesWorkflow(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	type argumentStruct struct {
		WorkflowID string `arg:"workflowID" validate:"required"`
	}
	var args argumentStruct
	err = GetArgs(&args, data)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	if err = userjourney.AssignWorkflow(args.WorkflowID, userObj.ID); err != nil {
		logger.WithUser(userObj.ID).Errorln(err)
		return ars, err
	}

	return ars, nil
}

func (a *Activity) UpdateWorkflow(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}
	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}

	type argumentStruct struct {
		WorkflowName string `arg:"workflowName" validate:"required"`
	}

	var args argumentStruct
	err = GetArgs(&args, data)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}

	errWf := workflowutils.UpdateWorkFlow(userObj.ID, userObj.SourceEntityID, args.WorkflowName)
	if errWf != nil {
		logger.WithUser(userObj.ID).Error(errWf)
		panic(errWf)
	}

	return ars, nil
}

func (a *Activity) RegisterActivityEvent(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()

	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	type argumentStruct struct {
		EventType         string `arg:"eventType" required:"true"`
		EntityType        string `arg:"entityType"`
		EntityRef         string `arg:"entityRef"`
		LoanApplicationID string `arg:"loanApplicationID"`
		Description       string `arg:"description"`
	}

	var args argumentStruct
	err = GetArgsV2(&args, data)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	dateTimeNowString := general.GetTimeStampString()
	activityObj := finboxEvents.ActivityEvent{
		UserID:            userObj.ID,
		SourceEntityID:    userObj.SourceEntityID,
		LoanApplicationID: args.LoanApplicationID,
		EntityType:        args.EntityType,
		EntityRef:         args.EntityRef,
		EventType:         args.EventType,
		Description:       args.Description,
	}
	finboxEvents.RegisterEvent(&activityObj, dateTimeNowString)

	return ars, nil
}

func (a *Activity) FetchLenderVariables(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}
	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}

	type argumentStruct struct {
		LenderID string `arg:"lenderID"`
	}
	var args argumentStruct
	lenderID, ok := data["lenderID"].(string)
	if !ok {
		// check for arguments
		err = GetArgsV2(&args, data)
		if err != nil {
			logger.WithUser(userObj.ID).Error(err)
			errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
			return ars, err
		}
		lenderID = args.LenderID
	}

	lenderVariables, err := lendervariables.Get(userObj.ID, lenderID)
	if err == sql.ErrNoRows {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		return ars, errors.New("NoRowsAvailable")
	} else if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}

	data["lenderVariables"] = lenderVariables

	return ars, nil
}

// UpdateLenderDynamicVariables is used for updating dynamic variables present in lender variables
// TODO: make this usable as a generic activity for all lender variables
func (a *Activity) UpdateLenderDynamicVariables(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}
	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}
	type argumentStruct struct {
		LenderID                     string                 `arg:"lenderID" required:"true"`
		UpdateLenderDynamicVariables map[string]interface{} `arg:"updateLenderDynamicVariables" required:"true"`
	}
	var args argumentStruct
	err = GetArgsV2(&args, data)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}
	mergeMap := make(map[string]any)
	if args.UpdateLenderDynamicVariables != nil {
		err = mapper.JQMapper(data, args.UpdateLenderDynamicVariables, mergeMap)
		if err != nil {
			logger.WithUser(userObj.ID).Error(err)
			errorHandler.ReportToSentryWithoutRequest(err)
			return ars, err
		}
		// mergeMap contains new keys to add in dynamic variables
		err := lendervariables.MergeUpdateDynamicVariables(userObj.ID, args.LenderID, mergeMap)
		if err != nil {
			logger.WithUser(userObj.ID).Error(err)
			errorHandler.ReportToSentryWithoutRequest(err)
			return ars, err
		}
	}

	return ars, nil
}

// UpsertLenderDynamicVariables just has an extra check for existance in lender variables and is same as UpdateLenderDynamicVariables
func (a *Activity) UpsertLenderDynamicVariables(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}
	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}
	type argumentStruct struct {
		LenderID                     string                 `arg:"lenderID" required:"true"`
		UpdateLenderDynamicVariables map[string]interface{} `arg:"updateLenderDynamicVariables" required:"true"`
	}
	var args argumentStruct
	err = GetArgsV2(&args, data)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}
	mergeMap := make(map[string]any)
	if args.UpdateLenderDynamicVariables != nil {
		err = mapper.JQMapper(data, args.UpdateLenderDynamicVariables, mergeMap)
		if err != nil {
			logger.WithUser(userObj.ID).Error(err)
			errorHandler.ReportToSentryWithoutRequest(err)
			return ars, err
		}

		//inserting in lender var
		exist, err := lendervariables.Exists(userObj.ID, args.LenderID, lendervariables.LenderVariableStatusActive)
		if err != nil && err != sql.ErrNoRows {
			logger.WithUser(userObj.ID).Error(err)
			errorHandler.ReportToSentryWithoutRequest(err)
			return ars, err
		}
		if !exist {
			if err = lendervariables.Insert(nil, lendervariables.LenderVariablesStructNullable{
				UserID:   userObj.ID,
				LenderID: args.LenderID,
				Status:   sql.NullInt64{Valid: true, Int64: lendervariables.LenderVariableStatusActive},
			}); err != nil {
				logger.WithUser(userObj.ID).Error(err)
				errorHandler.ReportToSentryWithoutRequest(err)
				return ars, err
			}
		}

		// mergeMap contains new keys to add in dynamic variables
		err = lendervariables.MergeUpdateDynamicVariables(userObj.ID, args.LenderID, mergeMap)
		if err != nil {
			logger.WithUser(userObj.ID).Error(err)
			errorHandler.ReportToSentryWithoutRequest(err)
			return ars, err
		}
	}

	return ars, nil
}

// CaptureConsents stores a list of consent information provided by a user
func (a *Activity) CaptureConsents(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}
	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	args := argument.GetActivityArguments(data)

	var (
		exists      bool
		consentData map[string]interface{}
	)

	type ConsentData struct {
		ConsentText string `json:"consentText" validate:"required"`
		ConsentType string `json:"consentType" validate:"required"`
		Latitude    string `json:"lat"`
		Longitude   string `json:"lon"`
		Height      string `json:"height"`
		Accuracy    string `json:"accuracy"`
	}

	type arguments struct {
		IPAddress string        `json:"ipAddr" validate:"required"`
		Consents  []ConsentData `json:"consents" validate:"required"`
	}

	exists, consentData = argument.GetAny("consentData", data, args, consentData)
	if !exists {
		err = fmt.Errorf("consentData not found in args")
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	var consentArgs arguments
	err = general.DecodeToStruct(consentData, &consentArgs)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	ipAddr := consentArgs.IPAddress
	for _, consentData := range consentArgs.Consents {
		err = legallogs.SaveWithConsentTypeV2(ctx, userObj.ID, consentData.ConsentText, consentData.ConsentType, consentData.Latitude, consentData.Longitude, consentData.Height, consentData.Accuracy, ipAddr, time.Now())
		if err != nil {
			logger.WithUser(userObj.ID).Errorln(err)
			errorHandler.ReportToSentryWithoutRequest(err)
			return ars, err
		}
	}
	return ars, nil
}

func (a *Activity) WeightedRandomness(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()

	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	type argumentStruct struct {
		WeightedEntities []general.WeightedEntities[string] `arg:"weightedEntities" validate:"required"`
	}
	var args argumentStruct
	err = GetArgsV2(&args, data)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	result := general.WeightedRandomness(args.WeightedEntities)

	data["weightedRandomnessResult"] = result

	return ars, nil
}

// GetFeatureFlagValue ... returns the feature flag value (boolean) for the given flag and key
func (a *Activity) GetFeatureFlagValue(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()

	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	type argumentStruct struct {
		Key  string `arg:"key" validate:"required"`
		Flag string `arg:"flag" validate:"required"`
	}
	var args argumentStruct
	err = GetArgs(&args, data)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	value := featureflag.Get(args.Key, args.Flag)

	data["featureFlagValue"] = value

	return ars, nil
}

// GetUserBankDetails fetches user_bank_details data by status
func (a *Activity) GetUserBankDetailsByStatus(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()

	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithFields(data, err)
		return ars, err
	}

	type argumentStruct struct {
		Status int `arg:"status" validate:"required"`
	}

	var args argumentStruct
	err = GetArgsV2(&args, data)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}

	userBankDetails, err := userbankdetails.GetBankDetailsByStatus(ctx, userObj.ID, args.Status)
	if err != nil && err != sql.ErrNoRows {
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}

	data["userBankDetails"] = userBankDetails
	output["userBankDetails"] = userBankDetails

	return ars, nil
}

// FetchCurrentTimeStamp fetches current timestamp in IST
func (a *Activity) FetchCurrentTimeStamp(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	data["currentTimeStamp"] = nil

	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()

	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithFields(data, err)
		return ars, err
	}

	var (
		args struct {
			Type string `arg:"type"`
		}
		loc         *time.Location
		currentTime time.Time
	)

	err = GetArgsV2(&args, data)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}

	loc, _ = time.LoadLocation("Asia/Calcutta")
	currentTime = time.Now().In(loc)

	switch args.Type {
	case "UnixMilliSeconds":
		data["currentTimeStamp"] = currentTime.UnixMilli()
	case "UnixMicroSeconds":
		data["currentTimeStamp"] = currentTime.UnixMicro()
	case "UnixNanoSeconds":
		data["currentTimeStamp"] = currentTime.UnixNano()
	}

	return ars, nil
}

func (a *Activity) ErrorStateActivity(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {

	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	type argumentStruct struct {
		ErrorMessage string `arg:"errorMessage"`
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}
	var args argumentStruct
	err = GetArgs(&args, data)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}
	if args.ErrorMessage == "" {
		args.ErrorMessage = "generic error state"
	}

	return ars, errors.New(args.ErrorMessage)

}

func (a *Activity) CheckLocationExistence(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}
	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}

	latLongExists := false

	userLocation, err := userlocation.Get(userObj.ID, "")
	if err != nil && err != sql.ErrNoRows {
		logger.WithUser(userObj.ID).Errorln(err)
		return ars, err
	} else if userLocation.Lat != "" && userLocation.Long != "" {
		latLongExists = true
	}

	data["latLongExists"] = latLongExists

	return ars, nil
}

// GetJourneyLendersStatusCount ... returns the number of active, inactive, rejected lenders from user journey
func (a *Activity) GetJourneyLendersStatusCount(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}
	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	journeyLenders, err := journeyutils.GetLenders(userObj.ID)

	if err != nil && err != sql.ErrNoRows {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithFields(errorHandler.ErrorFields{"user-id": userObj.ID}, err)
		return ars, err
	}

	var activeLendersCount, inActiveLendersCount, rejectedLendersCount int

	for _, lender := range journeyLenders {
		if lender.Status == constants.LenderStatusActive {
			activeLendersCount++
		} else if lender.Status == constants.LenderStatusInactive {
			inActiveLendersCount++
		} else if lender.Status == constants.LenderStatusRejected {
			rejectedLendersCount++
		}
	}

	data["inactiveLendersCount"] = inActiveLendersCount
	data["activeLendersCount"] = activeLendersCount
	data["rejectedLendersCount"] = rejectedLendersCount

	return ars, nil
}

// GetSourceEntityInfo :
func (a *Activity) GetSourceEntityInfo(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	type argumentStruct struct {
		UserID         string `arg:"userID"`
		SourceEntityID string `arg:"sourceEntityID" required:"true"`
	}
	var args argumentStruct

	err = GetArgsV2(&args, data)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	sourceEntityObj, err := sourceentity.FetchSourceEntityInfo(args.SourceEntityID)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	data["sourceEntityObj"] = sourceEntityObj
	return ars, nil
}

func (a *Activity) LenderDeviationCheck(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}
	data["isLenderDeviationPresent"] = false
	isPresent, _, err := deviations.GetLenderFromDeviation(userObj.ID)
	if err != nil || err != sql.ErrNoRows {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
	}

	data["isLenderDeviationPresent"] = isPresent

	return ars, nil
}

func (a *Activity) OrgLevelDisqualifyUser(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}

	type argumentStruct struct {
		RejectionReason string `arg:"rejectionReason" validate:"required"`
		EntityRef       string `arg:"entityRef"`
	}
	var args argumentStruct
	err = GetArgs(&args, data)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}

	organizationID, err := sourceentity.GetOrgByID(userObj.SourceEntityID)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		return ars, err
	}

	if err = usersutil.CustomDisqualifyUsers(userObj.UniqueID, organizationID, args.RejectionReason, args.EntityRef); err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		return ars, err
	}

	return ars, nil
}

func (a *Activity) GeocodeReverseLookup(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}

	type argumentStruct struct {
		Lat string `arg:"lat" validate:"required"`
		Lon string `arg:"lon" validate:"required"`
	}

	var args argumentStruct
	err = GetArgs(&args, data)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}

	addressObj, err := gmaps.GetPincodeAndNationalityForLatLong(userObj.ID, args.Lat, args.Lon)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		return ars, err
	}

	data["geocodeLookupAddressObj"] = addressObj

	return ars, nil
}

// WaitForDC polls by quering database to check for completion of device connect
func (a *Activity) WaitForDC(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}
	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}
	type argumentStruct struct {
		Attempt int `arg:"attempt" required:"true"`
	}

	var args argumentStruct
	err = GetArgsV2(&args, data)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{
			"userID": userObj.ID,
		}, err)
		return ars, err
	}
	var attempt = 0
	for attempt <= args.Attempt {
		attempt++
		_, err := deviceconnectdetails.GetLatestObjByUserAndStatus(userObj.ID, constants.DeviceConnectStatusCompleted)
		if err != nil {
			logger.WithUser(userObj.ID).Error(err)
			if attempt == args.Attempt {
				return ars, err
			}
			time.Sleep(1 * time.Second)
			continue
		}
	}

	return ars, nil
}

func (a *Activity) GetUserJourneyMetadata(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {

	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	userJourneyMetadata, err := userjourney.GetUserJourneyMetadata(userObj.ID)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}
	data["userJourneyMetadata"] = userJourneyMetadata

	return ars, nil
}

// GenerateOtpV3 only generates and add the otp in the workflow unlike previous versions which send the otp as well
func (a *Activity) GenerateOTPV3(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}
	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}
	type argumentStruct struct {
		AuthID string `arg:"authID" required:"true"`
	}

	var args argumentStruct
	err = GetArgsV2(&args, data)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{
			"userID": userObj.ID,
		}, err)
		return ars, err
	}

	otp, errStr := sms.GenerateOTPWithAuthID(userObj.ID, args.AuthID, constants.OTPPflEmailVerification, nil, "")
	if errStr != "" {
		logger.WithUser(userObj.ID).Error(errStr)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}
	data["otp"] = otp
	return ars, nil
}

// GenerateOTPV4 incorporates entire V3 along with OtpType as an argument
// TODO: Need to shift PFL OTP workflow activity and deprecate V3
func (a *Activity) GenerateOTPV4(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}
	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}
	type argumentStruct struct {
		AuthID  string `arg:"authID" required:"true"`
		OtpType string `arg:"otpType" required:"true"`
	}

	var args argumentStruct
	err = GetArgsV2(&args, data)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{
			"userID": userObj.ID,
		}, err)
		return ars, err
	}

	otp, errStr := sms.GenerateOTPWithAuthID(userObj.ID, args.AuthID, args.OtpType, nil, "")
	if errStr != "" {
		logger.WithUser(userObj.ID).Error(errStr)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}
	data["otp"] = otp
	return ars, nil
}

// IncrementValue increments the value of the passed argument variable by one. This can be used for maintaining a counter and incrementing its value when required
func (a *Activity) IncrementValue(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}
	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}
	type argumentStruct struct {
		CounterVariable string `arg:"counterVariable" required:"true"`
	}

	var args argumentStruct
	err = GetArgsV2(&args, data)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{
			"userID": userObj.ID,
		}, err)
		return ars, err
	}
	var counter = int(data[args.CounterVariable].(float64)) + 1
	data[args.CounterVariable] = counter
	return ars, nil
}

func (a *Activity) SendSMTPEmail(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	type argument struct {
		SenderEmailID        string                 `arg:"senderEmailID" required:"true"`
		SenderDisplayName    string                 `arg:"senderDisplayName" required:"true"`
		RecipientEmailID     string                 `arg:"recipientEmailID" required:"true"`
		RecipientDisplayName string                 `arg:"recipientDisplayName" required:"true"`
		EmailConfig          map[string]interface{} `arg:"emailConfig" required:"true"`
		EmailBodyKey         string                 `arg:"emailBodyKey" required:"true"`
		EmailSubjectKey      string                 `arg:"emailSubjectKey" required:"true"`
		Host                 string                 `arg:"host" required:"true"`
		Port                 int                    `arg:"port" required:"true"`
		Username             string                 `arg:"username"`
		Password             string                 `arg:"password"`
	}

	var args argument
	err = GetArgsV2(&args, data)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{
			"userID": userObj.ID,
		}, err)
		return ars, err
	}

	if !general.ValidateEmail(args.SenderEmailID) {
		err = fmt.Errorf("invalid sender email - %s", args.SenderEmailID)
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}
	if !general.ValidateEmail(args.RecipientEmailID) {
		err = fmt.Errorf("invalid recipient email - %s", args.RecipientEmailID)
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}

	if _, exists := emaillib.EmailSubjectKeyMap[args.EmailSubjectKey]; !exists {
		err = fmt.Errorf("email subject not found for given key - %s", args.EmailSubjectKey)
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}
	if _, exists := emaillib.EmailBodyKeyMap[args.EmailBodyKey]; !exists {
		err = fmt.Errorf("email body not found for given key - %s", args.EmailBodyKey)
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}

	var emailSubject, emailBody string

	emailSubject = general.GetStringFromTemplate(emaillib.EmailSubjectKeyMap[args.EmailSubjectKey], args.EmailConfig)
	emailBody = general.GetStringFromTemplate(emaillib.EmailBodyKeyMap[args.EmailBodyKey], args.EmailConfig)

	recipientEmails := []string{args.RecipientEmailID}
	recipientNames := []string{args.RecipientDisplayName}

	go func() {
		defer errorHandler.RecoveryNoResponse()
		_ = emaillib.SendSMTPMail(emaillib.MailStruct{UserID: userObj.ID, FromEmailInput: args.SenderEmailID, FromNameInput: args.SenderDisplayName, ToEmails: recipientEmails, ToNames: recipientNames, Subject: emailSubject, HTMLBody: emailBody, Attachments: nil, AddCC: false, Host: args.Host, Port: args.Port, Username: args.Username, Password: args.Password})
	}()

	return ars, nil
}

// SplitUserNameIntoFirstMiddleAndLastName : This activity accepts a full name and splits it into first, middle and last name.
// It also accepts an optional argument to disable striping salutation from the full name.
func (a *Activity) SplitUserNameIntoFirstMiddleAndLastName(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()

	ars = runner.ActivityReturnStruct{
		Data:   map[string]interface{}{},
		Output: map[string]interface{}{},
	}

	var args struct {
		FullName                   string `arg:"fullName" required:"true"`
		DisableStrippingSalutation bool   `arg:"disableStrippingSalutation"`
	}

	if err := GetArgsV2(&args, data); err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Errorln(err)
		return ars, err
	}

	var (
		stripSalutation = !args.DisableStrippingSalutation // Default behaviour is to strip salutation, i.e. remove Mr, Mrs, Ms etc from the name
	)

	firstName, middleName, lastName := general.GetFirstMiddleLastName(args.FullName, stripSalutation)

	// Capitalize
	caser := cases.Title(language.English, cases.Compact)

	firstName = caser.String(firstName)
	middleName = caser.String(middleName)
	lastName = caser.String(lastName)

	ars.Data["splitNames"] = map[string]string{
		"firstName":  firstName,
		"middleName": middleName,
		"lastName":   lastName,
	}
	return ars, nil
}

// SaveUserLocation saves the user's location data in the 'user_location' table.
// It extracts the user and location details from the provided data, validates them,
// and inserts the location into the database. Errors are logged and reported to Sentry.
//
// Parameters:
//   - ctx: The context for tracing and logging.
//   - data: A map containing the user object and location details.
//   - output: A map to hold any output data (currently unused).
//
// Returns:
//   - ActivityReturnStruct: Contains the input and output data.
//   - err: An error, if any, during the operation.
//
// The function handles decoding, validation, and insertion of user location data into the database.
func (a *Activity) SaveUserLocation(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()

	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	type argumentStruct struct {
		Lat          string `arg:"lat" validate:"required"`
		Lon          string `arg:"lon" validate:"required"`
		Accuracy     string `arg:"accuracy"`
		Height       string `arg:"height"`
		ModuleName   string `arg:"moduleName" validate:"required"`
		LocationType string `arg:"locationType" validate:"required"`
	}

	var args argumentStruct
	err = GetArgsV2(&args, data)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{
			"userID": userObj.ID,
		}, err)
		return ars, err
	}

	err = userlocation.Insert(userlocation.Struct{
		UserID:       userObj.ID,
		Lat:          args.Lat,
		Long:         args.Lon,
		Accuracy:     args.Accuracy,
		Height:       args.Height,
		Step:         args.ModuleName,
		LocationType: args.LocationType,
	})
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		return ars, err
	}
	return ars, nil
}

func (a *Activity) InsertUserLocation(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithFields(data, err)
		return ars, err
	}

	var arguments struct {
		Latitude     string `arg:"latitude" required:"true"`
		Longitude    string `arg:"longitude" required:"true"`
		Accuracy     string `arg:"accuracy"`
		Height       string `arg:"height"`
		Step         string `arg:"step"`
		LocationType string `arg:"locationType"`
		Address      string `arg:"address"`
	}
	if err := GetArgsV2(&arguments, data); err != nil {
		logger.WithUser(userObj.ID).Error(err)
		return ars, err
	}

	if err = userlocation.Insert(userlocation.Struct{
		UserID:       userObj.ID,
		Lat:          arguments.Latitude,
		Long:         arguments.Longitude,
		Accuracy:     arguments.Accuracy,
		Height:       arguments.Height,
		Step:         arguments.Step,
		LocationType: arguments.LocationType,
		Address:      arguments.Address,
	}); err != nil {
		logger.WithUser(userObj.ID).Error(err)
		return ars, err
	}

	return ars, nil
}

func (a *Activity) GetAddressDetails(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	defer errorHandler.RecoveryNoResponse()
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()

	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithFields(data, err)
		return ars, err
	}

	var arguments struct {
		LoanApplicationID string `arg:"loanApplicationID" required:"true"`
	}
	err = GetArgsV2(&arguments, data)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithFields(data, err)
		return ars, err
	}

	address, err := userloandetails.GetAddress(arguments.LoanApplicationID)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithFields(data, err)
		return ars, err
	}
	output["address_info"] = address.PermanentAddress

	data["userID"] = userObj.ID
	data["sourceEntityID"] = userObj.SourceEntityID

	return ars, nil
}

// InsertUserModuleMappingEntry adds a new row with module name and status for given userID along with any supported
// options
func (a *Activity) InsertUserModuleMappingEntry(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	type arguments struct {
		UserID            string                          `arg:"userID" validate:"required"`
		Module            string                          `arg:"module" validate:"required"`
		Status            int                             `arg:"moduleStatus" validate:"required"`
		LoanApplicationID string                          `arg:"loanApplicationID"`
		Options           usermodulemapping.CreateOptions `arg:"options"`
	}

	var args arguments
	err = GetArgsV2(&args, data)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	isValidModule, err := commonutils.IsValidModule(args.Module)
	if err != nil {
		logger.WithUser(args.UserID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	if !isValidModule {
		err = fmt.Errorf("invalid moduleName  - %s", args.Module)
		logger.WithUser(args.UserID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	if !general.InArr(args.Status, constants.UserModuleStatusList) {
		err = fmt.Errorf("invalid module status - %d", args.Status)
		logger.WithUser(args.UserID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	if err = usermodulemapping.CreateWithOptions(nil, args.UserID, args.UserID, args.Module, args.Status, args.LoanApplicationID, args.Options); err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	return ars, nil
}

func (a *Activity) IncrementKey(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	var arguments struct {
		Intent  string `arg:"intent"`
		Poll    bool   `arg:"poll"`
		PollKey string `arg:"pollKey"`
	}
	err = GetArgsV2(&arguments, data)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithFields(errorHandler.ErrorFields{"workflowID": activityInfo.WorkflowExecution.ID, "runID": activityInfo.WorkflowExecution.RunID}, err)
		return ars, err
	}
	if arguments.Poll {
		data[arguments.PollKey] = incrementCounterForKey(data, arguments.PollKey)
	}
	return ars, nil
}

// GetUserWorkflowByModuleNameAndStatus retrieves a user workflow by module name and status
func (a *Activity) GetUserWorkflowByModuleNameAndStatus(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()

	ars = runner.ActivityReturnStruct{
		Data:   map[string]interface{}{},
		Output: map[string]interface{}{},
	}

	type arguments struct {
		UserID string `arg:"userID" required:"true"`
		Module string `arg:"module" required:"true"`
		Status string `arg:"status" required:"true"`
	}
	var args arguments
	err = GetArgsV2(&args, data)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	userWorkflow, err := userworkflows.GetByModulenameAndStatus(ctx, args.UserID, args.Module, []string{args.Status})
	if err == sql.ErrNoRows {
		return ars, NewNonRetryableApplicationError(constants.ErrUserWorkflowNotFound)
	}
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	ars.Data["userWorkflow"] = userWorkflow

	return ars, nil
}

// GetAge gets age ed on date of birth passed
func (a *Activity) GetAge(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	type arguments struct {
		DOB string `arg:"dob"  validate:"required"`
	}

	var args arguments
	err = GetArgsV2(&args, data)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	dobObj, err := time.Parse(constants.DateFormat, args.DOB)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	age := general.AgeAt(dobObj, time.Now())

	data["age"] = age

	return ars, nil
}

func (a *Activity) InsertUserModuleMapping(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	// Define struct for extracting required arguments from input data
	type argumentStruct struct {
		UserID            string `arg:"userID" required:"true"`
		ModuleName        string `arg:"moduleName" required:"true"`
		ModuleStatus      int    `arg:"moduleStatus" required:"true"`
		LoanApplicationID string `arg:"loanApplicationID"`
	}

	var args argumentStruct
	err = GetArgsV2(&args, data)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{
			"workflowID": activityInfo.WorkflowExecution.ID,
			"runID":      activityInfo.WorkflowExecution.RunID,
		}, err)
		return ars, err
	}

	// Insert user-module mapping into the database
	if err = usermodulemapping.Create(nil, args.UserID, args.UserID, args.ModuleName, args.ModuleStatus, args.LoanApplicationID); err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{
			"userID":    args.UserID,
			"runID":     activityInfo.WorkflowExecution.RunID,
			"WorflowID": activityInfo.WorkflowExecution.ID,
		}, err)
		return ars, err
	}

	return ars, nil
}

func (a *Activity) FetchValueFromLenderDropDown(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	userObj, span, ars, err := initActivity(ctx, a.Tracer, data, output)
	if err != nil {
		logger.WithWorkflow(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}
	defer func() {
		span.Finish(err)
	}()
	type argumentStruct struct {
		SearchInput LenderDropDownSearch `arg:"searchInput" required:"true"`
	}

	var args argumentStruct
	err = GetArgsV2(&args, data)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{
			"userID": userObj.ID,
		}, err)
		return ars, err
	}

	result, err := getValueFromQueryFilters(args.SearchInput)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{
			"userID": userObj.ID,
		}, err)
		return ars, err
	}

	data["lenderDropDownResult"] = result

	return ars, nil
}

// GetBestFuzzyMatchFromChoices takes a target string and a list of choices, and returns the best matching string along with its match score.
// This is useful for finding the closest match in a predefined list of valid options.
func (a *Activity) GetBestFuzzyMatchFromChoices(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   map[string]interface{}{},
		Output: map[string]interface{}{},
	}

	type argumentStruct struct {
		Target  string   `arg:"target" required:"true"`
		Choices []string `arg:"choices" required:"true"`
	}

	var args argumentStruct
	err = GetArgsV2(&args, data)
	if err != nil {
		logger.WithUser(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	bestMatch, score := general.GetBestFuzzyMatch(args.Target, args.Choices)

	ars.Data["fuzzyMatchResult"] = map[string]interface{}{
		"bestMatch": bestMatch,
		"score":     score,
	}

	return ars, nil
}

func (a *Activity) ClearOTPAttemptsV1(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
    activityInfo := activity.GetInfo(ctx)
    span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
    defer func() {
        span.Finish(err)
    }()
    
    ars = runner.ActivityReturnStruct{
        Data:   data,
        Output: output,
    }

    var user users.User
    err = general.DecodeToStruct(data["userObj"], &user)
    if err != nil {
        logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
        errorHandler.ReportToSentryWithFields(map[string]interface{}{
            "userID":         user.ID,
            "sourceEntityID": user.SourceEntityID,
        }, err)
        return ars, err
    }

    type argumentStruct struct {
        EntityToClear string `arg:"entityToClear" required:"true"`
        OtpType      string `arg:"otpType" required:"true"`
        OtpDataKey   string `arg:"otpDataKey"`
    }

    var args argumentStruct
    err = GetArgsV2(&args, data)
    if err != nil {
        logger.WithUser(user.ID).Error(err)
        errorHandler.ReportToSentryWithFields(map[string]interface{}{
            "userID":         user.ID,
            "sourceEntityID": user.SourceEntityID,
        }, err)
        return ars, err
    }

    if args.OtpDataKey == "" {
        args.OtpDataKey = ""
    }

    err = sms.ClearOTPAttempts(user.ID, ctx, args.EntityToClear, args.OtpType, args.OtpDataKey)
    
    if err != nil {
        logger.WithUser(user.ID).Error("Failed to clear OTP attempts: ", err)
        errorHandler.ReportToSentryWithFields(map[string]interface{}{
            "userID":         user.ID,
            "sourceEntityID": user.SourceEntityID,
            "entityToClear":   args.EntityToClear,
            "otpType":        args.OtpType,
        }, err)
        return ars, err
    }

    logger.WithUser(user.ID).Info(fmt.Sprintf("Successfully cleared OTP attempts for email: %s", args.EntityToClear))

    return ars, nil
}
