package activities

import (
	"context"
	"finbox/go-api/constants"
	"finbox/go-api/errorHandler"
	"finbox/go-api/functions/insurance/iiflinsurance"
	"finbox/go-api/functions/insurance/riscovery"
	"finbox/go-api/functions/journey"
	"finbox/go-api/functions/logger"
	"finbox/go-api/functions/partner"
	"finbox/go-api/functions/underwriting"
	"finbox/go-api/infra/redis"
	"finbox/go-api/models/companydetails"
	"finbox/go-api/models/lendervariables"
	"finbox/go-api/models/sourceentityjourney"
	"finbox/go-api/models/userjourney"
	"finbox/go-api/models/users"
	"finbox/go-api/utils/general"
	"fmt"
	"strings"
	"time"

	"github.com/finbox-in/road-runner/runner"
	"go.temporal.io/sdk/activity"
)

// UpsertCompanyDetailsByLender is source specific activity which maps company name to per lender requirement and stores in lenderVariables
func (a *Activity) UpsertCompanyDetailsByLender(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}
	var (
		userObj users.User
	)
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}

	isCHSearchActive, _ := sourceentityjourney.GetKeyValueByName(userObj.SourceEntityID, sourceentityjourney.SearchCompanyNameCH, "")
	employmentType, _ := userObj.DynamicUserInfoMap["employmentType"].(string)
	employmentType = strings.ReplaceAll(strings.ToLower(employmentType), " ", "-")
	companyName, _ := userObj.DynamicUserInfoMap["companyName"].(string)
	otherCompanyName, _ := userObj.DynamicUserInfoMap["employerNameOthers"].(string)

	platformActiveLenders, _ := userjourney.GetLenders(userObj.ID)
	for lenderID, lender := range platformActiveLenders {
		if lender.Status == constants.LenderStatusActive {
			name := strings.TrimSpace(strings.ToLower(companyName))
			var details []companydetails.ResponseStruct
			// dont search in db when employee is self-employed
			if name != "other" && employmentType == constants.OccupationTypeSalaried {
				if isCHSearchActive == "ACTIVE" {
					details, err = companydetails.SearchCompanyV2(ctx, companyName, 1, 0, userObj.SourceEntityID, lenderID)
				} else {
					details, err = companydetails.SearchCompanyName(companyName, 1, 0, userObj.SourceEntityID, lenderID)
				}
				if err != nil {
					logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
					errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
					return ars, err
				}
			}

			if len(details) == 0 {

				var (
					companyNameByLender     string
					companyCategoryByLender string
				)
				// mapping company names as per lender specification
				switch lenderID {
				case constants.LandTID:
					if employmentType == constants.OccupationTypeSelfEmployed {
						companyNameByLender = companyName
					} else {
						companyNameByLender = "Other"
					}
					companyCategoryByLender = "CAT E"
				case constants.ABFLPLID:
					if employmentType == constants.OccupationTypeSelfEmployed {
						companyNameByLender = companyName
					} else if strings.Contains(strings.ToLower(companyName), "other") {
						companyNameByLender = otherCompanyName
					} else {
						companyNameByLender = companyName
					}
					companyCategoryByLender = "NC"
				case constants.BajajID:
					if employmentType == constants.OccupationTypeSelfEmployed {
						companyNameByLender = companyName
					} else {
						companyNameByLender = "Others"
					}
				case constants.IncredApiStackID:
					if employmentType == constants.OccupationTypeSelfEmployed {
						companyNameByLender = companyName
					} else if strings.Contains(strings.ToLower(companyName), "other") {
						companyNameByLender = otherCompanyName
					} else {
						companyNameByLender = companyName
					}
				case constants.MCKreditbeeID:
					companyCategoryByLender = "-1"
					if employmentType == constants.OccupationTypeSelfEmployed {
						companyNameByLender = companyName
					} else if strings.Contains(strings.ToLower(companyName), "other") {
						companyNameByLender = otherCompanyName
					} else {
						companyNameByLender = companyName
					}
				default:
					if employmentType == constants.OccupationTypeSelfEmployed {
						companyNameByLender = companyName
					} else {
						companyNameByLender = "NA"
					}
					companyCategoryByLender = "NA"
				}

				details = append(details, companydetails.ResponseStruct{
					CompanyCode:     companyNameByLender,
					CompanyCategory: companyCategoryByLender,
				})
			}

			lv := lendervariables.DynamicVariables{
				CompanyName:     strings.TrimSpace(details[0].CompanyCode),
				CompanyCategory: strings.TrimSpace(details[0].CompanyCategory),
			}

			if err := lv.UpdateDynamicVariablesByLenderID(userObj.ID, lenderID); err != nil {
				logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
				errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
				return ars, err
			}
		}
	}

	return ars, nil
}

// ABFLPLInsuranceDetails is an activity function that retrieves insurance premium details for ABFL Personal Loan applications.
// It handles tracing, error reporting, and optional error handling based on configuration.
func (a *Activity) ABFLPLInsuranceDetails(ctx context.Context, data map[string]interface{}, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}
	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}

	type argumentStruct struct {
		LoanApplicationID string `arg:"loanApplicationID" required:"true"`
		LoanType          string `arg:"loanType"`
		IgnoreError       bool   `arg:"ignoreError"` // used to ignore error in cases where we still want to move ahead if a user's insurance call fails.
	}

	var args argumentStruct
	err = GetArgsV2(&args, data)
	if err != nil {
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}

	err = riscovery.GetInsurancePremium(args.LoanApplicationID, args.LoanType)
	if err != nil {
		logger.WithUser(userObj.ID).Errorln("error fetching insurance details for ABFL PL user", err)
		if args.IgnoreError {
			logger.WithUser(userObj.ID).Warnf("ignoring error as specified by ignoreError arg")
		} else {
			return ars, err
		}
	}

	return ars, nil
}

// ABFLPLPostKYCUnderwriting is an activity function that performs post-KYC underwriting for ABFL Personal Loans.
// It evaluates the loan application after KYC verification and returns an underwriting decision.
func (a *Activity) ABFLPLPostKYCUnderwriting(ctx context.Context, data map[string]interface{}, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}
	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}

	type argumentStruct struct {
		LoanApplicationID string `arg:"loanApplicationID" required:"true"`
		SourceEntityID    string `arg:"sourceEntityID"`
		LenderID          string `arg:"lenderID"`
	}

	var args argumentStruct
	err = GetArgsV2(&args, data)
	if err != nil {
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}

	decision, rejectReason, err := underwriting.PostKYCABFLPLUnderwriting(userObj.ID, args.LoanApplicationID, args.SourceEntityID, args.LenderID, underwriting.OptionsKYCABFLPLUnderwriting{})
	if err != nil {
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}
	data["underwritingDecision"] = map[string]interface{}{
		"decision":     decision,
		"rejectReason": rejectReason,
	}

	return ars, nil
}

// IIFLInsuranceDetails is an activity function that retrieves and processes insurance details for IIFL loans.
// It fetches premium information by calling the IIFL Premium API with loan and user details.
func (a *Activity) IIFLInsuranceDetails(ctx context.Context, data map[string]interface{}, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}
	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}

	type argumentStruct struct {
		LoanApplicationID string  `arg:"loanApplicationID" required:"true"`
		SourceEntityID    string  `arg:"sourceEntityID"`
		LenderID          string  `arg:"lenderID"`
		LoanOfferID       string  `arg:"loanOfferID" required:"true"`
		Tenure            int     `arg:"tenure"`
		Amount            float64 `arg:"amount"`
		Interest          float64 `arg:"interest"`
		LoanType          string  `arg:"loanType"`
	}

	var args argumentStruct
	err = GetArgsV2(&args, data)
	if err != nil {
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}

	err = iiflinsurance.CallIIFLGetPremiumAPI(userObj.ID, args.LoanApplicationID, args.LoanOfferID, userObj.DOB, args.Tenure, args.Amount, args.Interest, args.LoanType)
	if err != nil {
		logger.WithUser(userObj.ID).Errorln("error fetching and updating insurance details:", err)
	}

	return ars, nil
}

// ExecuteBREOnHardPullData : iifl specific fetches hard pull data for a user and executes BRE based on that data for a loan application.
func (a *Activity) ExecuteBREOnHardPullData(ctx context.Context, data map[string]interface{}, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}
	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}

	type argumentStruct struct {
		LoanApplicationID string `arg:"loanApplicationID" required:"true"`
		SourceEntityID    string `arg:"sourceEntityID"`
		LenderID          string `arg:"lenderID"`
	}

	var args argumentStruct
	err = GetArgsV2(&args, data)
	if err != nil {
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}

	decision, rejectReason, err := underwriting.ExecuteBREOnHardPullData(userObj.ID, args.SourceEntityID, args.LoanApplicationID)
	if err != nil {
		logger.WithUser(userObj.ID).Errorln(err)
		decision = constants.UnderwritingDecisionCantDecide
	}
	key := fmt.Sprintf("%s-%s", args.LoanApplicationID, "hardpull-sentinel-run")
	_ = redis.Set(key, "1", time.Hour*24*45)

	data["underwritingDecision"] = map[string]interface{}{
		"decision":     decision,
		"rejectReason": rejectReason,
	}

	return ars, nil
}

func (a *Activity) IIFLBLPostKYCUnderwriting(ctx context.Context, data map[string]interface{}, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}
	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}

	type argumentStruct struct {
		LoanApplicationID string `arg:"loanApplicationID" required:"true"`
		SourceEntityID    string `arg:"sourceEntityID"`
		LenderID          string `arg:"lenderID"`
	}

	var args argumentStruct
	err = GetArgsV2(&args, data)
	if err != nil {
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}

	decision, rejectReason := underwriting.PostKYCIIFLBLUnderwriting(userObj.ID, args.LoanApplicationID, args.SourceEntityID, args.LenderID, underwriting.OptionsKYCIIFLBLUnderwriting{})
	data["underwritingDecision"] = map[string]interface{}{
		"decision":     decision,
		"rejectReason": rejectReason,
	}

	return ars, nil
}

// MFLBLCheckProfessionalInfo checks condition to reject/ pass the user based upon submitted professional info
func (a *Activity) MFLBLCheckProfessionalInfo(ctx context.Context, data map[string]interface{}, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithFields(map[string]any{"userID": userObj.ID}, err)
		return ars, err
	}

	type argumentStruct struct {
		ModeOfSalary   string  `arg:"modeOfSalary" required:"true"`
		Designation    string  `arg:"designation"`
		MonthlyIncome  float64 `arg:"monthlyIncome" required:"true"`
		FamilyIncome   float64 `arg:"familyIncome"`
		CompanyName    string  `arg:"companyName"`
		CompanyType    string  `arg:"companyType"`
		EmployeesCount string  `arg:"employeesCount" required:"true"`
		CompanyAge     string  `arg:"companyAge"`
		IsPFDeducted   string  `arg:"isPFDeducted"`
	}

	var (
		args             argumentStruct
		professionalInfo partner.ProfessionalInfo
		qualified        bool
		rejectionReason  string
	)

	err = GetArgsV2(&args, data)
	if err != nil {
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithFields(map[string]any{
			"userID":         userObj.ID,
			"sourceEntityID": userObj.SourceEntityID}, err)
		return ars, err
	}

	professionalInfo = partner.ProfessionalInfo(args)
	qualified, rejectionReason = partner.CheckProfessionalInfo(professionalInfo)

	data["qualified"] = qualified
	data["rejectionReason"] = rejectionReason

	return ars, nil
}

func (a *Activity) IsMFLBLPreApprovedUser(ctx context.Context, data map[string]interface{}, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}
	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}

	isPreApproved, err := partner.IsMFLBLPreApprovedUser(userObj.ID)
	if err != nil {
		logger.WithUser(userObj.ID).Errorln(err)
		return ars, err
	}
	isEDIJourney := journey.IsEDIJourney(userObj.ID, constants.MFLBLID)

	data["isPreApproved"] = isPreApproved
	data["isEDIJourney"] = isEDIJourney
	return ars, nil
}
