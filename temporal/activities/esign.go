// Package activities provides the implementation of various activities used in the workflow.
package activities

import (
	"context"
	"encoding/json"
	"errors"
	"finbox/go-api/conf"
	"finbox/go-api/constants"
	"finbox/go-api/core/services/thirdpartyservice/emudhra"
	"finbox/go-api/errorHandler"
	"finbox/go-api/functions/agreement"
	"finbox/go-api/functions/journey"
	"finbox/go-api/functions/loanutils"
	"finbox/go-api/functions/logger"
	"finbox/go-api/functions/postagreement"
	"finbox/go-api/functions/services/leegality"
	"finbox/go-api/functions/services/short"
	"finbox/go-api/infra/s3"
	"finbox/go-api/models/esignattempt"
	"finbox/go-api/models/loanapplication"
	"finbox/go-api/models/users"
	"finbox/go-api/temporal/temporaltracer"
	"finbox/go-api/utils/general"
	"fmt"
	"net/http"
	"time"

	fbActivity "finbox/go-api/functions/activity"

	configv1 "github.com/finbox-in/pickle-protos/gen/go/config/v1"
	runnerconstants "github.com/finbox-in/road-runner/constants"
	"github.com/finbox-in/road-runner/runner"
	"github.com/finbox-in/road-runner/types/argument"
	"go.temporal.io/sdk/activity"
)

type EsignStatus struct {
	Status  string `json:"status"`
	Message string `json:"message"`
}

// GetLeegalityRedirectionURL is an activity function that generated leegality sign url.
func (a *Activity) GetLeegalityRedirectionURL(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}
	// get loan application details
	var loanAppID string
	err = general.DecodeToStruct(data["loanApplicationID"], &loanAppID)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	loanObj, err := loanapplication.Get(ctx, loanAppID)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	var user users.User
	err = general.DecodeToStruct(data["userObj"], &user)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	var docS3ObjectKey string
	err = general.DecodeToStruct(data["unsignedDocUrl"], &docS3ObjectKey)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error("unsignedDocUrl is empty")
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	var fileName string
	err = general.DecodeToStruct(data["LeegalityFilename"], &fileName)
	if fileName == "" {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error("LeegalityFilename is empty")
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	fileName = fmt.Sprintf("Signed_Agreement_%s.pdf", loanObj.LoanApplicationNo)
	stampPaperReqd, _ := journey.GetStampPaperRequiredLeegality(user.ID, loanAppID)

	encodedContent := s3.GetBase64FromS3(docS3ObjectKey)

	url, _, lockCustomError := leegality.CreateSignRequest(fileName, encodedContent, user.Name, user.Email, user.Mobile, user.ID, loanAppID, user.SourceEntityID, stampPaperReqd)

	_, dateTimeNowString := general.GetTimeStampPair()

	activityObj := fbActivity.ActivityEvent{
		UserID:            user.ID,
		SourceEntityID:    user.SourceEntityID,
		LoanApplicationID: loanAppID,
		EntityType:        constants.EntityTypeSystem,
		EntityRef:         user.ID,
		EventType:         constants.ActivityESignURLFailed,
		Description:       "",
	}

	//check if a lock exits on this API's CreateSignRequest
	if lockCustomError != nil {
		logger.WithUser(user.ID).Error(lockCustomError)
		switch lockCustomError.HTTPCode {
		case http.StatusConflict:
			logger.WithUser(user.ID).Errorln(lockCustomError.Err)
			ars.Data["workflowStatus"] = constants.TemporalStatusFailed
			ars.Data["errorType"] = leegality.ErrorLegalityServiceStatusConflict.Error()
			ars.Data["failureReason"] = lockCustomError.Err.Error()
			return ars, leegality.ErrorLegalityServiceStatusConflict
		default:
			err = lockCustomError.Err
			if err != nil {
				logger.WithUser(user.ID).Errorln(err)
				fbActivity.RegisterEvent(&activityObj, dateTimeNowString)
				ars.Data["workflowStatus"] = constants.TemporalStatusFailed
				ars.Data["errorType"] = leegality.ErrorLegalityServiceNotAvailable.Error()
				ars.Data["failureReason"] = err.Error()
				// panic("esign request creation failed")
				return ars, leegality.ErrorLegalityServiceNotAvailable
			}
		}
	}
	ars.Data["leegalityURL"] = url
	activityObj.EventType = constants.ActivityESignURLCreated
	fbActivity.RegisterEvent(&activityObj, dateTimeNowString)
	return ars, nil
}

func (a *Activity) HandleEsignTermination(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	userID := userObj.ID
	logger.WithUser(userID).Println("******** terminating workflow with EndState **********")

	return ars, errors.New("workflow failed")
}

// GetLeegalityRedirectionURL is an activity function that generated leegality sign url.
func (a *Activity) GetUnsignedDocument(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}
	ars.Output["esignStatus"] = ""

	var user users.User
	err = general.DecodeToStruct(data["userObj"], &user)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	// get loan application details
	loanData, err := loanapplication.GetLatestByUser(user.ID)
	if err != nil {
		err = errors.New("loanApplicationID - userID combination does not exist")
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}
	loanApplicationID := loanData.ID.String()

	type dbRespStruct struct {
		WaitState bool
		Status    int
		LenderID  string
		PolicyURL string
		TncURL    string
	}

	obj := dbRespStruct{}
	query := `select la.status, coalesce(wait_state, FALSE) as waitstate, la.lender_id as lenderid, coalesce(l.privacy_policy_url, '') as policyurl, coalesce(l.tnc_url, '') as tncurl
						from loan_application la join users u on la.user_id = u.user_id
						join lender l on l.lender_id = la.lender_id
                        where loan_application_id = $1 and u.user_id = $2`
	err = database.Get(&obj, query, loanApplicationID, user.ID)
	if err != nil {
		err = errors.New("loan application not found")
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	if obj.WaitState {
		err = errors.New("user in wait state, agreement cannot be viewed")
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	mode := journey.GetESignMode(obj.LenderID)
	s3Path, _ := agreement.GenerateUnsignedAgreement(loanApplicationID, time.Now(), false, "")
	if s3Path == "" {
		err = errors.New("couldn't fetch agreement, please try again later")
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	ars.Data["hideHeaderFooter"] = false
	ars.Data["esignUrl"] = s3.GetPresignedURLForHTML(s3Path, 300)
	ars.Data["type"] = "html"
	ars.Data["mode"] = mode
	ars.Data["loanApplicationID"] = loanApplicationID
	ars.Data["userID"] = user.ID

	unsignedAgreementDetailsMap := map[string]interface{}{}

	unsignedAgreementDetailsMap["hideHeaderFooter"] = false
	unsignedAgreementDetailsMap["esignUrl"] = s3Path // s3.GetPresignedURLForHTML(s3Path, 300)
	unsignedAgreementDetailsMap["type"] = "html"
	unsignedAgreementDetailsMap["mode"] = mode
	unsignedAgreementDetailsMap["loanApplicationID"] = loanApplicationID

	ars.Output["unsignedAgreementDetails"] = unsignedAgreementDetailsMap

	ars.Output["esignStatus"] = EsignStatus{
		Status: "success",
	}

	return ars, nil
}

func (a *Activity) PostESignProcess(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var user users.User
	err = general.DecodeToStruct(data["userObj"], &user)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	var arguments struct {
		Latitude          string `arg:"latitude"`
		Longitude         string `arg:"longitude"`
		Height            string `arg:"height"`
		Accuracy          string `arg:"accuaracy"`
		IPAddress         string `arg:"ipAddr"`
		LoanApplicationID string `arg:"loanApplicationID"`
	}

	// get loan application details
	var loanAppID string
	err = general.DecodeToStruct(data, &arguments)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	loanAppID = arguments.LoanApplicationID

	obj, err := loanutils.GetLoanOfferDetails(loanAppID, user.ID)

	if obj.Status >= constants.LoanStatusESign {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	locationObj := postagreement.LocationStruct{
		Lat:       arguments.Latitude,
		Lon:       arguments.Longitude,
		Height:    arguments.Height,
		Accuracy:  arguments.Accuracy,
		IPAddress: arguments.IPAddress,
	}

	err = postagreement.UpdateModuleOnSigningAgreement(user.ID, loanAppID, user.SourceEntityID, obj, locationObj)

	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	postagreement.CallLenderDecisionAPI(user.ID, obj.LenderID, loanAppID, user.SourceEntityID, "", false)

	postagreement.HandleDisbursalAfterAgreementSign(user.ID, loanAppID, user.SourceEntityID, obj.LenderID, obj.LoanType)

	err = agreement.SendAgreementViaSMS(user.ID, user.SourceEntityID, user.Mobile, user.Name, obj.LoanApplicationNo, "", "", obj.Amount, obj.LenderID)

	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
	}

	data["loanAmount"] = obj.Amount
	data["userName"] = user.Name
	return ars, nil
}

// UpdateUserWorkflowStatus : Updates the status of user's temporal workflow present in `user_workflows` table
func (a *Activity) IgnoreErrors(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()

	return ars, nil
}

// Deprecated: SendDocumentToLegalityAsync is deprecated. SendDocumentToLegalityAsync is an activity function that sends a document to the Legality service asynchronously.
// It takes the input data and output map as parameters and returns an ActivityReturnStruct and an error.
func (a *Activity) SendDocumentToLegalityAsync(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}
	// get loan application details
	var loanAppID string
	err = general.DecodeToStruct(data["loanApplicationID"], &loanAppID)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	var user users.User
	err = general.DecodeToStruct(data["userObj"], &user)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	var docS3ObjectKey string
	err = general.DecodeToStruct(data["s3ObjectKey"], &docS3ObjectKey)
	if docS3ObjectKey == "" {
		err = general.DecodeToStruct(data["docS3ObjectKey"], &docS3ObjectKey)
		if docS3ObjectKey == "" {
			logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error("s3ObjectKey is empty")
			errorHandler.ReportToSentryWithoutRequest(err)
			return ars, err
		}
	}

	var fileName string
	err = general.DecodeToStruct(data["LeegalityFilename"], &fileName)
	if fileName == "" {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error("LeegalityFilename is empty")
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}
	// fileName := fmt.Sprintf("Signed_Agreement_%s.pdf", loanAppID.LoanAppIDlicationNo)
	// encodedContent := s3.GetBase64FromS3(docS3ObjectKey)
	// logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Info(encodedContent)
	// _, lockCustomError := leegality.CreateSignRequest(fileName, encodedContent, user.Name, user.Email, user.Mobile, user.ID, loanAppID, user.SourceEntityID, false)

	encodedContent := s3.GetBase64FromS3(docS3ObjectKey)
	stampPaperReqd, _ := journey.GetStampPaperRequiredLeegality(user.ID, loanAppID)
	_, _, lockCustomError := leegality.CreateSignRequest(fileName, encodedContent, user.Name, user.Email, user.Mobile, user.ID, loanAppID, user.SourceEntityID, stampPaperReqd)

	_, dateTimeNowString := general.GetTimeStampPair()
	activityObj := fbActivity.ActivityEvent{
		UserID:            user.ID,
		SourceEntityID:    user.SourceEntityID,
		LoanApplicationID: loanAppID,
		EntityType:        constants.EntityTypeSystem,
		EntityRef:         user.ID,
		EventType:         constants.ActivityESignURLFailed,
		Description:       "",
	}

	//check if a lock exits on this API's CreateSignRequest
	if lockCustomError != nil {
		logger.WithUser(user.ID).Error(lockCustomError)
		switch lockCustomError.HTTPCode {
		case http.StatusConflict:
			logger.WithUser(user.ID).Errorln(lockCustomError.Err)
			ars.Data["workflowStatus"] = constants.TemporalStatusFailed
			ars.Data["errorType"] = leegality.ErrorLegalityServiceStatusConflict.Error()
			ars.Data["failureReason"] = lockCustomError.Err.Error()
			return ars, leegality.ErrorLegalityServiceStatusConflict
		default:
			err = lockCustomError.Err
			if err != nil {
				logger.WithUser(user.ID).Errorln(err)
				fbActivity.RegisterEvent(&activityObj, dateTimeNowString)
				ars.Data["workflowStatus"] = constants.TemporalStatusFailed
				ars.Data["errorType"] = leegality.ErrorLegalityServiceNotAvailable.Error()
				ars.Data["failureReason"] = err.Error()
				// panic("esign request creation failed")
				return ars, leegality.ErrorLegalityServiceNotAvailable
			}
		}
	}
	ars.Data["workflowStatus"] = constants.TemporalStatusSuccess
	activityObj.EventType = constants.ActivityESignURLCreated
	fbActivity.RegisterEvent(&activityObj, dateTimeNowString)
	return ars, nil
}

// SendDocumentToLegalityAsyncV2 is an activity function that sends a document to the Legality service asynchronously.
// It takes the input data and output map as parameters and returns an ActivityReturnStruct and an error.
// The only way it differs from the v1 activity is that it uses the variables from arguments as opposed to from data.
func (a *Activity) SendDocumentToLegalityAsyncV2(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   map[string]interface{}{},
		Output: map[string]interface{}{},
	}

	var args struct {
		UserID            string `arg:"userID" required:"true"`
		UnsignedS3Key     string `arg:"unsignedS3Key" required:"true"`
		FileName          string `arg:"fileName" required:"true"`
		LoanApplicationID string `arg:"loanApplicationID" required:"true"`
	}

	if err := GetArgsV2(&args, data); err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Errorln(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{
			"workflowID": activityInfo.WorkflowExecution.ID,
		}, err)
		return ars, err
	}

	user, err := users.Get(args.UserID)
	if err != nil {
		logger.WithUser(args.UserID).Errorln(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{
			"workflowID": activityInfo.WorkflowExecution.ID,
			"userID":     args.UserID,
		}, err)
		return ars, err
	}

	encodedContent := s3.GetBase64FromS3(args.UnsignedS3Key)
	stampPaperReqd, _ := journey.GetStampPaperRequiredLeegality(user.ID, args.LoanApplicationID)
	_, _, lockCustomError := leegality.CreateSignRequest(args.FileName, encodedContent, user.Name, user.Email, user.Mobile, user.ID, args.LoanApplicationID, user.SourceEntityID, stampPaperReqd)

	_, dateTimeNowString := general.GetTimeStampPair()
	activityObj := fbActivity.ActivityEvent{
		UserID:            user.ID,
		SourceEntityID:    user.SourceEntityID,
		LoanApplicationID: args.LoanApplicationID,
		EntityType:        constants.EntityTypeSystem,
		EntityRef:         user.ID,
		Description:       "",
	}

	//check if a lock exits on this API's CreateSignRequest
	if lockCustomError != nil {
		logger.WithUser(user.ID).Error(lockCustomError)
		switch lockCustomError.HTTPCode {
		case http.StatusConflict:
			logger.WithUser(user.ID).Errorln(lockCustomError.Err)
			ars.Data["workflowStatus"] = constants.TemporalStatusFailed
			ars.Data["errorType"] = leegality.ErrorLegalityServiceStatusConflict.Error()
			ars.Data["failureReason"] = lockCustomError.Err.Error()
			return ars, leegality.ErrorLegalityServiceStatusConflict
		default:
			err = lockCustomError.Err
			if err != nil {
				logger.WithUser(user.ID).Errorln(err)
				activityObj.EventType = constants.ActivityESignURLFailed
				fbActivity.RegisterEvent(&activityObj, dateTimeNowString)
				ars.Data["workflowStatus"] = constants.TemporalStatusFailed
				ars.Data["errorType"] = leegality.ErrorLegalityServiceNotAvailable.Error()
				ars.Data["failureReason"] = err.Error()
				// panic("esign request creation failed")
				return ars, leegality.ErrorLegalityServiceNotAvailable
			}
		}
	}
	ars.Data["workflowStatus"] = constants.TemporalStatusSuccess
	activityObj.EventType = constants.ActivityESignURLCreated
	fbActivity.RegisterEvent(&activityObj, dateTimeNowString)
	return ars, nil
}

// TODO: Optimise leegality.GetESignStatus so that we can build a generic version of this Activity
func (a *Activity) GenerateShortURLOfSignedURLForLoanApplication(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	args := argument.GetActivityArguments(data)

	exists, loanApplicationID := args.Get("loanApplicationID", data)
	if !exists {
		err = fmt.Errorf("mandatory argument loanApplicationID not passed")
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	exists, userID := args.Get("userID", data)
	if !exists {
		err = fmt.Errorf("mandatory argument userID not passed")
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	exists, lenderID := args.Get("lenderID", data)
	if !exists {
		lenderID = constants.MuthootCLID
	}

	urlObj, err := agreement.GetSignedAgreement(ctx, loanApplicationID.(string), "", agreement.GetSignedAgreementOptions{
		UseDownloadableRedirect: true,
	})
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	if conf.ENV == conf.ENV_LOCAL {
		urlObj.DocumentURL = "https://example.com"
	}
	// get s3 object key from loan application

	// s3URL := s3.GetLongLivedPresignedURLS3(urlObj.DocumentURL, 10080) // 7 days
	logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Info(urlObj.DocumentURL)
	shortURL, err := short.ShortenURL(urlObj.DocumentURL, userID.(string), lenderID.(string))
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}
	ars.Data["shortURL"] = shortURL
	ars.Data["documentURL"] = urlObj.DocumentURL

	return ars, nil
}

func (a *Activity) GetEMudhraStatesMaster(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()

	ars = runner.ActivityReturnStruct{
		Data:   map[string]interface{}{},
		Output: map[string]interface{}{},
	}

	var args struct {
		SourceEntityID string `arg:"sourceEntityID" required:"true"`
	}

	if err := GetArgsV2(&args, data); err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	resp, err := a.ConfigManagementService.JourneyPickleProvider().GetConfigClient().GetConfig(ctx, &configv1.GetConfigRequest{
		ResourceName: "emudhra_states_master",
		KeyValueMap: map[string]string{
			"source_entity_id": args.SourceEntityID,
		},
	})

	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	type stampDuty struct {
		Type       string  `json:"type"`
		Amount     float64 `json:"amount"`
		Percentage float64 `json:"percentage"`
	}

	type State struct {
		SNo           int       `json:"s_no"`
		Name          string    `json:"name"`
		StateCode     string    `json:"state_code"`
		DigitalCode   string    `json:"digital_code"`
		ArticleNumber string    `json:"article_number"`
		StampDuty     stampDuty `json:"stamp_duty"`
	}
	type StateConfig struct {
		States []State `json:"states"`
	}

	var stateConfig StateConfig
	if err := json.Unmarshal([]byte(resp.ConfigData), &stateConfig); err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	ars.Data["states"] = stateConfig.States
	return ars, nil
}

func (a *Activity) EMudhraEmbeddedSigning(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	type request struct {
		LoanApplicationNo string                             `arg:"loanApplicationNo" required:"true"`
		StateCode         string                             `arg:"stateCode" required:"true"`
		AgreementS3Key    string                             `arg:"agreementS3Key" required:"true"`
		ArticleCode       string                             `arg:"articleCode" required:"true"`
		UserID            string                             `arg:"userID" required:"true"`
		ServiceName       string                             `arg:"serviceName" required:"true"`
		Amount            float64                            `arg:"amount" required:"true"`
		StampDutyAmount   float64                            `arg:"stampDutyAmount" required:"true"`
		Description       string                             `arg:"description" required:"true"`
		PrimaryApplicant  emudhra.EmbeddedSigningApplicant   `arg:"primaryApplicant" required:"true"`
		CoApplicants      []emudhra.EmbeddedSigningApplicant `arg:"coApplicants"`
	}

	type response struct {
		ReferenceNumber string `json:"referenceNo"`
		DocumentIDList  []int  `json:"documentIDList"`
		WorkflowID      string `json:"workflowID"`
		URL             string `json:"url"`
		EsignAttemptID  string `json:"esignAttemptID"`
	}

	var (
		args    request
		userObj users.User
		span    temporaltracer.Span
		resp    response
	)

	userObj, span, ars, err = initActivity(ctx, a.Tracer, data, output)
	if err != nil {
		logger.WithWorkflow(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}

	defer func() {
		span.Finish(err)
	}()

	err = GetArgsV2(&args, data)
	if err != nil {
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}

	// Convert activity arguments to service request
	serviceRequest := emudhra.EmbeddedSigningRequest{
		LoanApplicationNo: args.LoanApplicationNo,
		StateCode:         args.StateCode,
		AgreementS3Key:    args.AgreementS3Key,
		ArticleCode:       args.ArticleCode,
		UserID:            args.UserID,
		ServiceName:       args.ServiceName,
		Amount:            args.Amount,
		StampDutyAmount:   args.StampDutyAmount,
		Description:       args.Description,
		PrimaryApplicant:  args.PrimaryApplicant,
		CoApplicants:      args.CoApplicants,
	}

	// Call the service implementation
	serviceResponse, err := a.ThirdPartyService.EmudhraEmbeddedSigning(ctx, serviceRequest)
	if err != nil {
		logger.WithUser(args.UserID).Errorln(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": args.UserID}, err)
		return ars, err
	}

	// Map service response to activity response
	resp.ReferenceNumber = serviceResponse.ReferenceNumber
	resp.DocumentIDList = serviceResponse.DocumentIDList
	resp.WorkflowID = serviceResponse.WorkflowID
	resp.URL = serviceResponse.URL
	resp.EsignAttemptID = serviceResponse.EsignAttemptID

	ars.Data[runnerconstants.ActivityOutput] = resp

	return ars, nil
}

func (a *Activity) EMudhraSigningWithoutEStamp(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	type request struct {
		LoanApplicationNo string              `arg:"loanApplicationNo" required:"true"`
		UserID            string              `arg:"userID" required:"true"`
		ServiceName       string              `arg:"serviceName" required:"true"`
		Documents         []emudhra.Document  `arg:"documents" required:"true"`
		Callbacks         emudhra.Callbacks   `arg:"callbacks" required:"true"`
		PrimaryApplicant  emudhra.Signatory   `arg:"primaryApplicant" required:"true"`
		CoApplicants      []emudhra.Signatory `arg:"coApplicants"`
	}

	type response struct {
		ReferenceNumber string `json:"referenceNo"`
		DocumentIDList  []int  `json:"documentIDList"`
		WorkflowID      string `json:"workflowID"`
		URL             string `json:"url"`
		EsignAttemptID  string `json:"esignAttemptID"`
	}

	var (
		args    request
		userObj users.User
		span    temporaltracer.Span
		resp    response
	)

	userObj, span, ars, err = initActivity(ctx, a.Tracer, data, output)
	if err != nil {
		logger.WithWorkflow(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}

	defer func() {
		span.Finish(err)
	}()

	err = GetArgsV2(&args, data)
	if err != nil {
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}

	// Convert activity arguments to service request
	serviceRequest := emudhra.SigningWithoutEStampRequest{
		LoanApplicationNo: args.LoanApplicationNo,
		UserID:            args.UserID,
		ServiceName:       args.ServiceName,
		Documents:         args.Documents,
		Callbacks:         args.Callbacks,
		PrimaryApplicant:  args.PrimaryApplicant,
		CoApplicants:      args.CoApplicants,
	}

	// Call the service implementation
	serviceResponse, err := a.ThirdPartyService.EmudhraSigningWithoutEStamp(ctx, serviceRequest)
	if err != nil {
		logger.WithUser(args.UserID).Errorln(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": args.UserID}, err)
		return ars, err
	}

	// Map service response to activity response
	resp.ReferenceNumber = serviceResponse.ReferenceNumber
	resp.DocumentIDList = serviceResponse.DocumentIDList
	resp.WorkflowID = serviceResponse.WorkflowID
	resp.URL = serviceResponse.URL
	resp.EsignAttemptID = serviceResponse.EsignAttemptID

	ars.Data[runnerconstants.ActivityOutput] = resp

	return ars, nil
}

func (a *Activity) EMudhraDownloadDocument(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	type request struct {
		WorkflowID  string `arg:"workflowID" required:"true"`
		UserID      string `arg:"userID" required:"true"`
		ServiceName string `arg:"serviceName" required:"true"`
	}

	type response struct {
		S3ObjectKey string `json:"s3ObjectKey"`
		DocumentID  int    `json:"documentID"`
		FileName    string `json:"fileName"`
	}

	var (
		args    request
		userObj users.User
		span    temporaltracer.Span
		resp    response
	)

	userObj, span, ars, err = initActivity(ctx, a.Tracer, data, output)
	if err != nil {
		logger.WithWorkflow(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}

	defer func() {
		span.Finish(err)
	}()

	err = GetArgsV2(&args, data)
	if err != nil {
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}

	// Convert activity arguments to service request
	serviceRequest := emudhra.DownloadDocumentRequest{
		WorkflowID:  args.WorkflowID,
		UserID:      args.UserID,
		ServiceName: args.ServiceName,
	}

	// Call the service implementation
	serviceResponse, err := a.ThirdPartyService.EmudhraDownloadDocument(ctx, serviceRequest)
	if err != nil {
		logger.WithUser(args.UserID).Errorln(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": args.UserID}, err)
		return ars, err
	}

	// Map service response to activity response
	resp.S3ObjectKey = serviceResponse.S3ObjectKey
	resp.DocumentID = serviceResponse.DocumentID
	resp.FileName = serviceResponse.FileName

	ars.Data[runnerconstants.ActivityOutput] = resp

	return ars, nil
}

func (a *Activity) SetEsignAttemptURLAndMarkComplete(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()

	ars = runner.ActivityReturnStruct{
		Data:   map[string]interface{}{},
		Output: map[string]interface{}{},
	}

	var args struct {
		LoanApplicationID string `arg:"loanApplicationID" required:"true"`
		DocumentID        string `arg:"documentID" required:"true"`
		ESignObjectKey    string `arg:"esignObjectKey" required:"true"`
	}

	if err := GetArgsV2(&args, data); err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Errorln(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{
			"workflowID": activityInfo.WorkflowExecution.ID,
		}, err)
		return ars, err
	}

	var user users.User
	err = general.DecodeToStruct(data["userObj"], &user)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	// Update the esign attempt with the URL and mark as complete
	esignAttemptUpdate := esignattempt.EsignAttempt{
		LoanApplicationID: args.LoanApplicationID,
		DocumentID:        args.DocumentID,
		SignURL:           args.ESignObjectKey,
		ESignStatus:       constants.ESignStatusSuccess,
	}

	err = esignattempt.Update(nil, ctx, esignAttemptUpdate)
	if err != nil {
		logger.WithUser(user.ID).Errorln(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{
			"userID":            user.ID,
			"loanApplicationID": args.LoanApplicationID,
			"documentID":        args.DocumentID,
		}, err)
		return ars, err
	}

	// Structure the response in a map
	response := map[string]interface{}{
		"status":            "success",
		"esignURL":          args.ESignObjectKey,
		"documentID":        args.DocumentID,
		"loanApplicationID": args.LoanApplicationID,
	}

	ars.Data[runnerconstants.ActivityOutput] = response

	return ars, nil
}

func (a *Activity) MarkAllEsignAttemptsAsFailed(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()

	ars = runner.ActivityReturnStruct{
		Data:   map[string]interface{}{},
		Output: map[string]interface{}{},
	}

	var args struct {
		LoanApplicationID string `arg:"loanApplicationID" required:"true"`
	}

	if err := GetArgsV2(&args, data); err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Errorln(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{
			"workflowID": activityInfo.WorkflowExecution.ID,
		}, err)
		return ars, err
	}

	err = esignattempt.UpdateESignStatus(args.LoanApplicationID, constants.ESignStatusFailed)
	if err != nil {
		logger.WithLoanApplication(args.LoanApplicationID).Errorln(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{
			"loanApplicationID": args.LoanApplicationID,
		}, err)
		return ars, err
	}

	return ars, nil
}
