package activities

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"finbox/go-api/constants"
	"finbox/go-api/errorHandler"
	finboxEvents "finbox/go-api/functions/activity"
	"finbox/go-api/functions/agreement"
	"finbox/go-api/functions/journey"
	"finbox/go-api/functions/lenderservice"
	"finbox/go-api/functions/loanutils"
	"finbox/go-api/functions/logger"
	"finbox/go-api/functions/structs"
	"finbox/go-api/models/businessloanoffer"
	"finbox/go-api/models/expiry"
	"finbox/go-api/models/lendervariables"
	"finbox/go-api/models/loanapplication"
	loanclosure "finbox/go-api/models/loanclosure/models.go"
	"finbox/go-api/models/loanoffertemplate"
	"finbox/go-api/models/personalloanoffer"
	"finbox/go-api/models/preselectedlender"
	"finbox/go-api/models/userloandetails"
	"finbox/go-api/models/users"
	"finbox/go-api/models/vendorpayments"
	"finbox/go-api/utils/calc"
	"finbox/go-api/utils/general"
	"fmt"
	"strconv"
	"time"

	"github.com/finbox-in/road-runner/runner"
	"github.com/finbox-in/road-runner/types/argument"
	"go.temporal.io/sdk/activity"
)

func (a *Activity) GetOfferDetailsFromPersonalLoanOffer(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	type argumentStruct struct {
		LenderID    string `arg:"lenderID" validate:"required"`
		OfferStatus string `arg:"offerStatus" validate:"required"`
	}

	var args argumentStruct
	err = GetArgs(&args, data)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	offerStatus, err := strconv.Atoi(args.OfferStatus)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	personalLoanOffer, err := personalloanoffer.GetLatestOfferForLenderID(userObj.ID, args.LenderID, []int{offerStatus})
	if err != nil {
		err = fmt.Errorf("error getting offer for the lenderID: %s, userID: %s, err: %s", args.LenderID, userObj.ID, err.Error())
		errorHandler.ReportToSentryWithoutRequest(err)
		logger.WithUser(userObj.ID).Errorln(err)
		return ars, err
	}

	offerSection := personalloanoffer.OfferStruct{
		MaxAmount:         personalLoanOffer.MaxAmount,
		MinAmount:         personalLoanOffer.MinAmount,
		MaxTenure:         personalLoanOffer.MaxTenure,
		MinTenure:         personalLoanOffer.MinTenure,
		Tenure:            personalLoanOffer.MaxTenure,
		Interest:          personalLoanOffer.Interest,
		ProcessingFee:     personalLoanOffer.ProcessingFee,
		ProcessingFeeType: personalLoanOffer.ProcessingFeeType,
		MaxEMI:            personalLoanOffer.MaxEMI,
	}

	data["personalLoanOfferDetails"] = map[string]interface{}{
		"offerID":            personalLoanOffer.PersonalLoanOfferID,
		"amount":             strconv.FormatFloat(personalLoanOffer.MaxAmount, 'f', -1, 64),
		"tenure":             strconv.FormatFloat(float64(personalLoanOffer.MaxTenure), 'f', -1, 64),
		"offerSection":       offerSection,
		"offerSectionString": offerSection.Stringify(),
	}

	return ars, nil
}

func (a *Activity) MarkLoanStatus(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	userID := userObj.ID
	loanApplicationData, err := loanapplication.GetLatestByUser(userID)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	loanApplicationID := loanApplicationData.ID.String()

	args := argument.GetActivityArguments(data)
	exists, loanStatus := args.Get("loanStatus", data)
	if !exists {
		err = fmt.Errorf("mandatory argument loanStatus not passed")
		logger.WithLoanApplication(loanApplicationID).Errorln(err)
		return ars, err
	}

	var status int
	var disbursalDate string
	var eventType string
	var eventDescription string
	switch loanStatus.(string) {
	case "disbursed":
		status = constants.LoanStatusDisbursed
		eventType = constants.ActivityLoanDisbursed
		disbursalDate = general.GetTimeStampString()
		descriptionData := map[string]interface{}{
			"loanapplicationno": loanApplicationData.LoanApplicationNo,
			"disbursed_date":    disbursalDate,
			"amount":            loanApplicationData.Amount,
			"emi":               loanApplicationData.EMI,
			"tenure":            loanApplicationData.Tenure,
			"interest":          loanApplicationData.Interest,
			"processingFee":     loanApplicationData.ProcessingFee,
		}
		descriptionBytes, _ := json.Marshal(descriptionData)
		eventDescription = string(descriptionBytes)
	case "closed":
		status = constants.LoanStatusClosed
		eventType = constants.ActivityLoanClosed
	case "cancelled":
		status = constants.LoanStatusCancelled
		eventType = constants.ActivityLoanCancelled
	case "rejected":
		status = constants.LoanStatusLoanRejected
		eventType = constants.ActivityLoanRejected
	default:
		err = fmt.Errorf("unknown/unsupported loanStatus: %s passed", loanStatus.(string))
		logger.WithLoanApplication(loanApplicationID).Errorln(err)
		return ars, err
	}

	//mark loan status
	err = loanapplication.Update(nil, loanapplication.StructForSet{
		Status:        &status,
		ID:            loanApplicationID,
		DisbursedDate: disbursalDate,
	})

	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	//register activity
	var dateTimeNowString = general.GetTimeStampString()
	activityObj := finboxEvents.ActivityEvent{
		UserID:            userObj.ID,
		SourceEntityID:    userObj.SourceEntityID,
		LoanApplicationID: loanApplicationID,
		EntityType:        constants.EntityTypeCustomer,
		EventType:         eventType,
		Description:       eventDescription,
	}
	finboxEvents.RegisterEvent(&activityObj, dateTimeNowString)

	return ars, nil
}

func (a *Activity) CheckActiveLoanExists(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	cnt, err := loanapplication.GetActiveLoanCount(userObj.ID)
	if err != nil {
		logger.WithUser(userObj.ID).Errorln(err)
		return ars, err
	}

	data["activeLoanExists"] = cnt > 0
	return ars, nil
}

func (a *Activity) LoanApplicationUpdate(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	userID := userObj.ID
	loanApplicationThings, err := loanapplication.GetLatestByUser(userID)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	loanApplicationID := loanApplicationThings.ID.String()

	signalData, ok := data["signal_data_loan_offer_updated"].(map[string]interface{})
	if !ok {
		err = fmt.Errorf("signal %s not found in datamap", "signal_data_loan_offer_updated")
		errorHandler.ReportToSentryWithoutRequest(err)
		logger.WithUser(userID).Errorln(err)
		return ars, err
	}

	amount, ok := signalData["amount"].(float64)
	if !ok {
		err = fmt.Errorf("amount not found in datamap")
		errorHandler.ReportToSentryWithoutRequest(err)
		logger.WithUser(userID).Errorln(err)
		return ars, err
	}
	tenure, ok := signalData["tenure"].(float64)
	if !ok {
		err = fmt.Errorf("tenure not found in datamap")
		errorHandler.ReportToSentryWithoutRequest(err)
		logger.WithUser(userID).Errorln(err)
		return ars, err
	}
	emi, ok := signalData["emi"].(float64)
	if !ok {
		err = fmt.Errorf("emi not found in datamap")
		errorHandler.ReportToSentryWithoutRequest(err)
		logger.WithUser(userID).Errorln(err)
		return ars, err
	}
	offerID, ok := signalData["offerID"].(string)
	if !ok {
		err = fmt.Errorf("offerID not found in datamap")
		errorHandler.ReportToSentryWithoutRequest(err)
		logger.WithUser(userID).Errorln(err)
		return ars, err
	}
	processingFee, ok := signalData["processingFee"].(float64)
	if !ok {
		err = fmt.Errorf("offerID not found in datamap")
		errorHandler.ReportToSentryWithoutRequest(err)
		logger.WithUser(userID).Errorln(err)
		return ars, err
	}
	interest, ok := signalData["interest"].(float64)
	if !ok {
		err = fmt.Errorf("interest not found in datamap")
		errorHandler.ReportToSentryWithoutRequest(err)
		logger.WithUser(userID).Errorln(err)
		return ars, err
	}

	o, err := personalloanoffer.GetLatest(userID, constants.OfferStatusIsAccepted)
	if err != nil {
		logger.WithUser(userID).Error(err)
	}

	err = personalloanoffer.UpdateStatus(nil, o.LoanOfferID, userID, constants.OfferStatusWasAccepted)
	if err != nil {
		logger.WithUser(userID).Error(err)
	}

	err = personalloanoffer.UpdateStatus(nil, offerID, userID, constants.OfferStatusIsAccepted)
	if err != nil {
		logger.WithUser(userID).Error(err)
		return ars, err
	}

	err = loanapplication.Update(nil, loanapplication.StructForSet{
		ID:            loanApplicationID,
		Amount:        amount,
		Tenure:        int(tenure),
		EMI:           emi,
		LoanOfferID:   offerID,
		ProcessingFee: &processingFee,
		Interest:      &interest,
	})

	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	bytes, _ := json.Marshal(signalData)
	dateTimeNowString := general.GetTimeStampString()
	activityObj := finboxEvents.ActivityEvent{
		UserID:            userObj.ID,
		SourceEntityID:    userObj.SourceEntityID,
		LoanApplicationID: loanApplicationID,
		EntityType:        constants.EntityTypeCustomer,
		EventType:         constants.ActivityLoanOfferUpdated,
		Description:       string(bytes),
	}
	finboxEvents.RegisterEvent(&activityObj, dateTimeNowString)

	return ars, nil
}

func (a *Activity) AcceptPLLoanOffer(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	type argumentStruct struct {
		OfferID               string `arg:"offerID" validate:"required"`
		Amount                string `arg:"amount" validate:"required"`
		Tenure                string `arg:"tenure" validate:"required"`
		OfferSection          string `arg:"offerSection" validate:"required"`
		SkipOfferStatusUpdate string `arg:"skipOfferStatusUpdate"`
		Emi                   string `arg:"emi"`
		EventType             string `arg:"eventType"`
	}

	eventType := constants.ActivityLoanApplicationCreated
	var args argumentStruct
	err = GetArgs(&args, data)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	if args.EventType != "" {
		eventType = args.EventType
	}
	amount, err := strconv.ParseFloat(args.Amount, 64)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	var emi float64
	if args.Emi != "" {
		emi, err = strconv.ParseFloat(args.Emi, 64)
		if err != nil {
			logger.WithUser(userObj.ID).Error(err)
			errorHandler.ReportToSentryWithoutRequest(err)
			return ars, err
		}
	}

	tenure, err := strconv.Atoi(args.Tenure)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	skipOfferStatusUpdate := false
	if args.SkipOfferStatusUpdate == "true" {
		skipOfferStatusUpdate = true
	}

	var offerSection personalloanoffer.OfferStruct
	err = json.Unmarshal([]byte(args.OfferSection), &offerSection)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	offer := personalloanoffer.Get(args.OfferID)

	offerTemplate, err := loanoffertemplate.GetTemplate(context.TODO(), userObj.SourceEntityID, offer.LenderID)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	processingFee := calc.CalculateProcessingFee(amount, offerSection.ProcessingFee, offerSection.ProcessingFeeType)

	tx, err := database.Beginx()
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	defer tx.Rollback()
	loanApplicationID := general.GetUUID()
	err = preselectedlender.Set(userObj.ID, offer.LenderID)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	status := constants.LoanStatusFresh

	type dynamicChargesStruct struct {
		Charges   []personalloanoffer.Charges  `json:"charges"`
		Discounts []personalloanoffer.Discount `json:"discounts"`
	}

	var DynamicCharges dynamicChargesStruct

	DynamicCharges.Charges = offerSection.Charges
	DynamicCharges.Discounts = offerSection.Discounts

	dynamicChargesBytes, err := json.Marshal(DynamicCharges)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	// Create loan application
	loan := loanapplication.StructForSet{
		ID:                        loanApplicationID,
		UserID:                    userObj.ID,
		SourceEntityID:            userObj.SourceEntityID,
		CreatedBy:                 userObj.ID,
		LoanApplicationNo:         journey.GenerateLoanApplicationNo(userObj.SourceEntityID),
		LoanType:                  constants.LoanTypePersonalLoan,
		LoanOfferID:               args.OfferID,
		Amount:                    amount,
		Tenure:                    tenure,
		Interest:                  &offerSection.Interest,
		Status:                    &status,
		LenderID:                  offer.LenderID,
		ProcessingFee:             &processingFee,
		AppliedAmount:             amount, // This can be removed later as redundant to amount
		AppliedTenure:             tenure, // This can be removed later as redundant to tenure
		DynamicCharges:            string(dynamicChargesBytes),
		GST:                       offerTemplate.GST,
		SignedAgreementTemplate:   offerTemplate.SignedAgreement,
		UnsignedAgreementTemplate: offerTemplate.UnsignedAgreement,
		EMI:                       emi,
	}
	err = loanapplication.InsertV2(&ctx, tx, loan)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	if !skipOfferStatusUpdate {
		err = personalloanoffer.UpdateStatus(tx, args.OfferID, userObj.ID, constants.OfferStatusIsAccepted)
		if err != nil {
			logger.WithUser(userObj.ID).Error(err)
			errorHandler.ReportToSentryWithoutRequest(err)
			return ars, err
		}
	}

	lenderVariablesNullable := lendervariables.LenderVariablesStructNullable{
		UserID:            userObj.ID,
		LenderID:          offer.LenderID,
		LoanApplicationID: sql.NullString{Valid: true, String: loanApplicationID},
	}
	err = lendervariables.UpdateByStatus(tx, lendervariables.LenderVariableStatusActive, lenderVariablesNullable)
	if err != nil {
		tx.Rollback()
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	if err = tx.Commit(); err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	lenderName := constants.LenderNamesMap[offer.LenderID]

	dateTimeString := general.GetTimeStampString()
	go finboxEvents.ActivityLogger(userObj.ID, userObj.SourceEntityID, userObj.ID, constants.EntityTypeCustomer, constants.ActivityOfferAccepted, fmt.Sprintf(`{"lender": "%s", "amount":"%.2f","Roi":"%f","Tenure":"%v"}`, lenderName, amount, *loan.Interest, tenure), loanApplicationID, dateTimeString, false)
	dateTimeString = general.GetTimeStampString()
	go finboxEvents.ActivityLogger(userObj.ID, userObj.SourceEntityID, "", constants.EntityTypeSystem, eventType, fmt.Sprintf(`{"lender": "%s"}`, lenderName), loanApplicationID, dateTimeString, false)

	data["loanApplicationID"] = loanApplicationID
	data["lenderID"] = offer.LenderID
	data["createdAt"] = offer.CreatedAt
	return ars, nil
}

func (a *Activity) AcceptPLLoanOfferWithUpsertLoan(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	type argumentStruct struct {
		OfferID               string `arg:"offerID" validate:"required"`
		Amount                string `arg:"amount" validate:"required"`
		Tenure                string `arg:"tenure" validate:"required"`
		OfferSection          string `arg:"offerSection" validate:"required"`
		SkipOfferStatusUpdate string `arg:"skipOfferStatusUpdate"`
	}
	var args argumentStruct
	err = GetArgs(&args, data)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	amount, err := strconv.ParseFloat(args.Amount, 64)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	tenure, err := strconv.Atoi(args.Tenure)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	skipOfferStatusUpdate := false
	if args.SkipOfferStatusUpdate == "true" {
		skipOfferStatusUpdate = true
	}

	var offerSection personalloanoffer.OfferStruct
	err = json.Unmarshal([]byte(args.OfferSection), &offerSection)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	offer := personalloanoffer.Get(args.OfferID)

	offerTemplate, err := loanoffertemplate.GetTemplate(context.TODO(), userObj.SourceEntityID, offer.LenderID)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	processingFee := calc.CalculateProcessingFee(amount, offerSection.ProcessingFee, offerSection.ProcessingFeeType)

	tx, err := database.Beginx()
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	defer tx.Rollback()
	loanApplicationID := general.GetUUID()
	err = preselectedlender.Set(userObj.ID, offer.LenderID)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	status := constants.LoanStatusFresh

	type dynamicChargesStruct struct {
		Charges   []personalloanoffer.Charges  `json:"charges"`
		Discounts []personalloanoffer.Discount `json:"discounts"`
	}

	var DynamicCharges dynamicChargesStruct

	DynamicCharges.Charges = offerSection.Charges
	DynamicCharges.Discounts = offerSection.Discounts

	dynamicChargesBytes, err := json.Marshal(DynamicCharges)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	logger.WithUser(userObj.ID).Printf("userID in AcceptPLLoanOfferWithUpsertLoan is: %s, lenderID: %s", userObj.ID, offer.LenderID)
	loanID, err := loanapplication.GetActiveByLenderAndUser(userObj.ID, offer.LenderID)
	if err != nil && err != sql.ErrNoRows {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	var isLoanUpdate bool

	// Create loan application
	loan := loanapplication.StructForSet{
		ID:                        loanApplicationID,
		UserID:                    userObj.ID,
		SourceEntityID:            userObj.SourceEntityID,
		CreatedBy:                 userObj.ID,
		LoanApplicationNo:         journey.GenerateLoanApplicationNo(userObj.SourceEntityID),
		LoanType:                  constants.LoanTypePersonalLoan,
		LoanOfferID:               args.OfferID,
		Amount:                    amount,
		Tenure:                    tenure,
		Interest:                  &offerSection.Interest,
		Status:                    &status,
		LenderID:                  offer.LenderID,
		ProcessingFee:             &processingFee,
		AppliedAmount:             amount, // This can be removed later as redundant to amount
		AppliedTenure:             tenure, // This can be removed later as redundant to tenure
		DynamicCharges:            string(dynamicChargesBytes),
		GST:                       offerTemplate.GST,
		SignedAgreementTemplate:   offerTemplate.SignedAgreement,
		UnsignedAgreementTemplate: offerTemplate.UnsignedAgreement,
	}
	logger.WithUser(userObj.ID).Printf("loanApplicationID in AcceptPLLoanOfferWithUpsertLoan: %s, loanID: %s", loanApplicationID, loanID)
	if loanID != "" {
		loanApplicationID = loanID
		err = loanapplication.Update(tx, loanapplication.StructForSet{
			ID:                        loanApplicationID,
			Amount:                    amount,
			Tenure:                    tenure,
			Interest:                  &offerSection.Interest,
			ProcessingFee:             &processingFee,
			AppliedAmount:             amount,
			AppliedTenure:             tenure,
			DynamicCharges:            string(dynamicChargesBytes),
			LoanOfferID:               args.OfferID,
			SourceEntityID:            userObj.SourceEntityID,
			CreatedBy:                 userObj.ID,
			LoanType:                  constants.LoanTypePersonalLoan,
			LenderID:                  offer.LenderID,
			Status:                    &status,
			SignedAgreementTemplate:   offerTemplate.SignedAgreement,
			UnsignedAgreementTemplate: offerTemplate.UnsignedAgreement,
			GST:                       offerTemplate.GST,
		})
		if err != nil {
			logger.WithUser(userObj.ID).Error(err)
			errorHandler.ReportToSentryWithoutRequest(err)
			return ars, err
		}
		isLoanUpdate = true
		logger.WithUser(userObj.ID).Printf("loanApplication updated in AcceptPLLoanOfferWithUpsertLoan: %s, loanID: %s", loanApplicationID, loanID)
	} else {
		err = loanapplication.InsertV2(&ctx, tx, loan)
		if err != nil {
			logger.WithUser(userObj.ID).Error(err)
			errorHandler.ReportToSentryWithoutRequest(err)
			return ars, err
		}
	}

	if !skipOfferStatusUpdate {
		err = personalloanoffer.UpdateStatus(tx, args.OfferID, userObj.ID, constants.OfferStatusIsAccepted)
		if err != nil {
			logger.WithUser(userObj.ID).Error(err)
			errorHandler.ReportToSentryWithoutRequest(err)
			return ars, err
		}
	}

	if err = tx.Commit(); err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	lenderName := constants.LenderNamesMap[offer.LenderID]

	dateTimeString := general.GetTimeStampString()
	go finboxEvents.ActivityLogger(userObj.ID, userObj.SourceEntityID, userObj.ID, constants.EntityTypeCustomer, constants.ActivityOfferAccepted, fmt.Sprintf(`{"lender": "%s", "amount":"%.2f","Roi":"%f","Tenure":"%v"}`, lenderName, amount, *loan.Interest, tenure), loanApplicationID, dateTimeString, false)

	dateTimeString = general.GetTimeStampString()
	if isLoanUpdate {
		go finboxEvents.ActivityLogger(userObj.ID, userObj.SourceEntityID, "", constants.EntityTypeSystem, constants.ActivityLoanApplicationUpdated, fmt.Sprintf(`{"lender": "%s"}`, lenderName), loanApplicationID, dateTimeString, false)
	} else {
		go finboxEvents.ActivityLogger(userObj.ID, userObj.SourceEntityID, "", constants.EntityTypeSystem, constants.ActivityLoanApplicationCreated, fmt.Sprintf(`{"lender": "%s"}`, lenderName), loanApplicationID, dateTimeString, false)
	}
	data["loanApplicationID"] = loanApplicationID
	data["lenderID"] = offer.LenderID
	data["createdAt"] = offer.CreatedAt
	return ars, nil
}

// AcceptBLLoanOffer processes the acceptance of a business loan offer.
// It accepts the offer by creating a loan application, updating offer status,
// and updating lender variables. It also logs relevant activities.
//
// Parameters:
//
//	ctx: The context.Context for the operation.
//	data: A map containing necessary data including userObj and offer details.
//	output: A map to store output data if needed.
//
// Returns:
//
//	ars: An instance of runner.ActivityReturnStruct containing processed data.
//	err: An error if any occurred during the operation.
func (a *Activity) AcceptBLLoanOffer(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	type argumentStruct struct {
		OfferID             string `arg:"offerID" validate:"required"`
		Amount              string `arg:"amount" validate:"required"`
		Tenure              string `arg:"tenure" validate:"required"`
		OfferSection        string `arg:"offerSection" validate:"required"`
		PfCalculationMethod string `arg:"pfCalculationMethod"`
	}
	var args argumentStruct
	err = GetArgs(&args, data)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	amount, err := strconv.ParseFloat(args.Amount, 64)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	tenure, err := strconv.Atoi(args.Tenure)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}
	var offerSection businessloanoffer.OfferStruct
	err = json.Unmarshal([]byte(args.OfferSection), &offerSection)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	pfCalculationMethod := args.PfCalculationMethod
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	offer, err := businessloanoffer.Get(args.OfferID)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	loanApplicationID, err := loanutils.AcceptBLLoanOfferHelper(ctx, userObj.ID, userObj.SourceEntityID, args.OfferID, offer, offerSection, amount, tenure, pfCalculationMethod)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	lenderName := constants.LenderNamesMap[offer.LenderID]

	dateTimeString := general.GetTimeStampString()
	go finboxEvents.ActivityLogger(userObj.ID, userObj.SourceEntityID, userObj.ID, constants.EntityTypeCustomer, constants.ActivityOfferAccepted, fmt.Sprintf(`{"lender": "%s", "amount":"%.2f"}`, lenderName, amount), loanApplicationID, dateTimeString, false)
	dateTimeString = general.GetTimeStampString()
	go finboxEvents.ActivityLogger(userObj.ID, userObj.SourceEntityID, "", constants.EntityTypeSystem, constants.ActivityLoanApplicationCreated, fmt.Sprintf(`{"lender": "%s"}`, lenderName), loanApplicationID, dateTimeString, false)

	data["loanApplicationID"] = loanApplicationID
	data["selectedLenderID"] = offer.LenderID
	return ars, nil
}

// AcceptGeneratedBLLoanOffer processes the acceptance of a business loan offer. The only difference from AcceptBLLoanOffer is
// AcceptGeneratedBLLoanOffer uses generated offer details instead of taking offer parameters as input. This activity is to be used
// in cases where the flexibility to adjust offer parameters is not to be provided to borrower
func (a *Activity) AcceptGeneratedBLLoanOffer(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	type argumentStruct struct {
		OfferID      string `arg:"offerID" validate:"required"`
		OfferSection string `arg:"offerSection"` // do we need this ?
	}
	var args argumentStruct
	err = GetArgs(&args, data)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}
	var offerSection businessloanoffer.OfferStruct
	if args.OfferSection != "" {
		err = json.Unmarshal([]byte(args.OfferSection), &offerSection)
		if err != nil {
			logger.WithUser(userObj.ID).Error(err)
			errorHandler.ReportToSentryWithoutRequest(err)
			return ars, err
		}
	}

	offer, err := businessloanoffer.Get(args.OfferID)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	if offer.Amount == 0 && offer.MaxAmount > 0 {
		offer.Amount = offer.MaxAmount
	}
	if offer.Tenure == 0 && offer.MaxTenure > 0 {
		offer.Tenure = offer.MaxTenure
	}

	loanApplicationID, err := loanutils.AcceptBLLoanOfferHelper(ctx, userObj.ID, userObj.SourceEntityID, args.OfferID, offer, offerSection, offer.Amount, offer.Tenure, "")
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	lenderName := constants.LenderNamesMap[offer.LenderID]

	dateTimeString := general.GetTimeStampString()
	go finboxEvents.ActivityLogger(userObj.ID, userObj.SourceEntityID, userObj.ID, constants.EntityTypeCustomer, constants.ActivityOfferAccepted, fmt.Sprintf(`{"lender": "%s", "amount":"%.2f"}`, lenderName, offer.Amount), loanApplicationID, dateTimeString, false)
	dateTimeString = general.GetTimeStampString()
	go finboxEvents.ActivityLogger(userObj.ID, userObj.SourceEntityID, "", constants.EntityTypeSystem, constants.ActivityLoanApplicationCreated, fmt.Sprintf(`{"lender": "%s"}`, lenderName), loanApplicationID, dateTimeString, false)

	data["loanApplicationID"] = loanApplicationID
	data["selectedLenderID"] = offer.LenderID
	return ars, nil
}

func (a *Activity) AcceptGeneratedPLLoanOffer(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	type argumentStruct struct {
		OfferID                    string `arg:"offerID" validate:"required"`
		SkipOfferStatusUpdate      string `arg:"skipOfferStatusUpdate"`
		SkipCalcProcessingFee      string `arg:"skipCalcProcessingFee"`
		ToUpdateOrAddLoanObjInData string `arg:"toUpdateOrAddLoanObjInData"`
		ToSkipLoanOfferTemplate    string `arg:"toSkipLoanOfferTemplate"`
	}
	var args argumentStruct
	err = GetArgs(&args, data)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	var (
		toSkipOfferStatusUpdate    = args.SkipOfferStatusUpdate == "true"
		toSkipCalcProcessingFee    = args.SkipCalcProcessingFee == "true"
		toUpdateOrAddLoanObjInData = args.ToUpdateOrAddLoanObjInData == "true"
		toSkipLoanOfferTemplate    = args.ToSkipLoanOfferTemplate == "true"
	)

	offer := personalloanoffer.Get(args.OfferID)

	var offerTemplate loanoffertemplate.OfferTemplateStruct
	if !toSkipLoanOfferTemplate {
		offerTemplate, err = loanoffertemplate.GetTemplate(context.TODO(), userObj.SourceEntityID, offer.LenderID)
		if err != nil {
			logger.WithUser(userObj.ID).Error(err)
			errorHandler.ReportToSentryWithoutRequest(err)
			return ars, err
		}
	}

	if len(offer.OfferMetadataObj.Offers) < 1 {
		logger.WithUser(userObj.ID).Error("no offers found: ", err)
		return ars, errors.New("no offers found")
	}
	processingFee := offer.OfferMetadataObj.Offers[0].ProcessingFee
	if !toSkipCalcProcessingFee {
		processingFee = calc.CalculateProcessingFee(offer.OfferMetadataObj.Offers[0].MaxAmount, offer.OfferMetadataObj.Offers[0].ProcessingFee, offer.OfferMetadataObj.Offers[0].ProcessingFeeType)
	}
	tx, err := database.Beginx()
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	defer tx.Rollback()
	loanApplicationID := general.GetUUID()
	err = preselectedlender.Set(userObj.ID, offer.LenderID)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	status := constants.LoanStatusFresh

	type dynamicChargesStruct struct {
		Charges   []personalloanoffer.Charges  `json:"charges"`
		Discounts []personalloanoffer.Discount `json:"discounts"`
	}

	var DynamicCharges dynamicChargesStruct

	DynamicCharges.Charges = offer.OfferMetadataObj.Offers[0].Charges
	DynamicCharges.Discounts = offer.OfferMetadataObj.Offers[0].Discounts

	dynamicChargesBytes, err := json.Marshal(DynamicCharges)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	// Create loan application
	loan := loanapplication.StructForSet{
		ID:                        loanApplicationID,
		UserID:                    userObj.ID,
		SourceEntityID:            userObj.SourceEntityID,
		CreatedBy:                 userObj.ID,
		LoanApplicationNo:         journey.GenerateLoanApplicationNo(userObj.SourceEntityID),
		LoanType:                  constants.LoanTypePersonalLoan,
		LoanOfferID:               args.OfferID,
		Amount:                    offer.OfferMetadataObj.Offers[0].MaxAmount,
		Tenure:                    offer.OfferMetadataObj.Offers[0].Tenure,
		Interest:                  &offer.OfferMetadataObj.Offers[0].Interest,
		Status:                    &status,
		LenderID:                  offer.LenderID,
		ProcessingFee:             &processingFee,
		AppliedAmount:             offer.OfferMetadataObj.Offers[0].MaxAmount, // This can be removed later as redundant to amount
		AppliedTenure:             offer.OfferMetadataObj.Offers[0].Tenure,    // This can be removed later as redundant to tenure
		DynamicCharges:            string(dynamicChargesBytes),
		GST:                       offerTemplate.GST,
		SignedAgreementTemplate:   offerTemplate.SignedAgreement,
		UnsignedAgreementTemplate: offerTemplate.UnsignedAgreement,
	}

	err = loanapplication.InsertV2(&ctx, tx, loan)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	if !toSkipOfferStatusUpdate {
		err = personalloanoffer.UpdateStatus(tx, args.OfferID, userObj.ID, constants.OfferStatusIsAccepted)
		if err != nil {
			logger.WithUser(userObj.ID).Error(err)
			return ars, err
		}
	}

	lenderVariablesNullable := lendervariables.LenderVariablesStructNullable{
		UserID:            userObj.ID,
		LenderID:          offer.LenderID,
		LoanApplicationID: sql.NullString{Valid: true, String: loanApplicationID},
	}
	err = lendervariables.UpdateByStatus(tx, lendervariables.LenderVariableStatusActive, lenderVariablesNullable)
	if err != nil {
		tx.Rollback()
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	if err = tx.Commit(); err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	lenderName := constants.LenderNamesMap[offer.LenderID]

	dateTimeString := general.GetTimeStampString()
	go finboxEvents.ActivityLogger(userObj.ID, userObj.SourceEntityID, userObj.ID, constants.EntityTypeCustomer, constants.ActivityOfferAccepted, fmt.Sprintf(`{"lender": "%s", "amount":"%.2f","Roi":"%f","Tenure":"%v"}`, lenderName, offer.OfferMetadataObj.Offers[0].MaxAmount, *loan.Interest, offer.OfferMetadataObj.Offers[0].Tenure), loanApplicationID, dateTimeString, false)
	dateTimeString = general.GetTimeStampString()
	go finboxEvents.ActivityLogger(userObj.ID, userObj.SourceEntityID, "", constants.EntityTypeSystem, constants.ActivityLoanApplicationCreated, fmt.Sprintf(`{"lender": "%s"}`, lenderName), loanApplicationID, dateTimeString, false)

	data["loanApplicationID"] = loanApplicationID
	data["lenderID"] = offer.LenderID
	data["createdAt"] = offer.CreatedAt
	if toUpdateOrAddLoanObjInData {
		loanObj, err := loanapplication.GetLatestByUser(userObj.ID)
		if err != nil && err != sql.ErrNoRows {
			logger.WithUser(userObj.ID).Errorln(err)
			return ars, nil
		}
		data["loanObj"] = loanObj
	}
	return ars, nil
}

func (a *Activity) CreateUserLoanDetails(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	loanApplicationID := data["loanApplicationID"].(string)

	err = userloandetails.Insert(&ctx, loanApplicationID, userObj.ID, userObj.ID, nil)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	return ars, nil
}

func (a *Activity) ProcessRepayment(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var arguments struct {
		UserID          string `arg:"userID" validate:"required"`
		VendorPaymentID string `arg:"vendorPaymentID" validate:"required"`
	}
	err = GetArgs(&arguments, data)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	lisaResponse, ok := data["repaymentObj"].(map[string]interface{})

	if !ok {
		err := fmt.Errorf("[ProcessRepayment] unable to extract lisa response to process further") // TODO: check copy later
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	receiptID, ok := lisaResponse["receiptId"].(string)

	if !ok {
		err := fmt.Errorf("[ProcessRepayment] unable to extract receiptID")
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	err = vendorpayments.AddKeyInMetaData(nil, arguments.VendorPaymentID, "receipt_id", receiptID, vendorpayments.StatusSuccess)

	if err != nil {
		err := fmt.Errorf("[ProcessRepayment] unable to update the meta data in vendor_payments : %w", err)
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	return ars, nil
}

func (a *Activity) UpdateVendorPaymentsStatus(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var arguments struct {
		UserID            string `arg:"userID" validate:"required"`
		LoadApplicationID string `arg:"loanApplicationID" validate:"required"`
		SourceEntityID    string `arg:"sourceEntityID" validate:"required"`
		VendorPaymentID   string `arg:"vendorPaymentID" validate:"required"`
		Status            string `arg:"status" validate:"required"`
	}

	err = GetArgs(&arguments, data)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	var eventName *string
	var vendorStatusInt = vendorpayments.StatusInitiated
	switch arguments.Status {
	case "FAILED":
		vendorStatusInt = vendorpayments.StatusFailed
		evName := constants.ActivityRepaymentFailed
		eventName = &evName
	case "SUCCESS":
		vendorStatusInt = vendorpayments.StatusSuccess
		evName := constants.ActivityRepaymentSuccessful
		eventName = &evName
	case "INITIATED":
		vendorStatusInt = vendorpayments.StatusInitiated
	}

	err = vendorpayments.UpdateStatus(nil, arguments.VendorPaymentID, vendorStatusInt)
	if err != nil {
		logger.WithUser(arguments.UserID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	if eventName != nil {
		dateTimeNowString := general.GetTimeStampString()
		activityObj := finboxEvents.ActivityEvent{
			UserID:            arguments.UserID,
			SourceEntityID:    arguments.SourceEntityID,
			LoanApplicationID: arguments.LoadApplicationID,
			EntityType:        constants.EntityTypeSystem,
			EventType:         *eventName,
			Description:       fmt.Sprintf(`{"vendorPaymentID": "%s", "status": "%s"}`, arguments.VendorPaymentID, arguments.Status),
		}
		finboxEvents.RegisterEvent(&activityObj, dateTimeNowString)
	}

	return ars, nil
}

func (a *Activity) SelectOffer(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	type argumentStruct struct {
		OfferID string `arg:"offerID" validate:"required"`
	}
	var args argumentStruct
	err = GetArgs(&args, data)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	offer := personalloanoffer.Get(args.OfferID)
	if general.InArr(offer.OfferStatus, []int{constants.OfferStatusIsAccepted, constants.OfferStatusWasAccepted, constants.OfferStatusInactive, constants.OfferStatusExpired, constants.OfferStatusSelected}) {
		err = fmt.Errorf("offer is in invalid state: %s", args.OfferID)
		logger.WithUser(userObj.ID).Error(err)
		return ars, err
	}

	err = personalloanoffer.UpdateStatus(nil, args.OfferID, userObj.ID, constants.OfferStatusSelected)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	dateTimeString := general.GetTimeStampString()
	go finboxEvents.ActivityLogger(userObj.ID, userObj.SourceEntityID, userObj.ID, constants.EntityTypeCustomer, constants.ActivitytOfferSelected, fmt.Sprintf(`{"amount":"%.2f","Roi":"%f","Tenure":"%v","lender":"%s"}`, offer.MaxAmount, offer.Interest, offer.MaxTenure, constants.LenderNamesMap[offer.LenderID]), "", dateTimeString, false)
	data["offerID"] = args.OfferID
	data["lenderID"] = offer.LenderID
	return ars, nil
}

func (a Activity) RevertSelection(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	type argumentStruct struct {
		OfferID string `arg:"offerID" validate:"required"`
	}
	var args argumentStruct
	err = GetArgs(&args, data)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	offer := personalloanoffer.Get(args.OfferID)
	if general.InArr(offer.OfferStatus, []int{constants.OfferStatusIsAccepted, constants.OfferStatusWasAccepted, constants.OfferStatusInactive, constants.OfferStatusExpired}) {
		err = fmt.Errorf("offer is in invalid state: %s", args.OfferID)
		logger.WithUser(userObj.ID).Error(err)
		return ars, err
	}

	err = personalloanoffer.UpdateStatus(nil, args.OfferID, userObj.ID, constants.OfferStatusActive)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}
	dateTimeString := general.GetTimeStampString()
	go finboxEvents.ActivityLogger(userObj.ID, userObj.SourceEntityID, userObj.ID, constants.EntityTypeCustomer, constants.ActivitytOfferDeSelected, "", "", dateTimeString, false)

	return ars, nil
}

func (a *Activity) CancelLoan(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	type argumentStruct struct {
		LoanApplicationID  string `arg:"loanApplicationID" validate:"required"`
		CancellationReason string `arg:"cancellationReason" validate:"required"`
		EntityType         string `arg:"entityType" validate:"required"`
		EntityRef          string `arg:"entityRef" validate:"required"`
	}
	var args argumentStruct
	err = GetArgs(&args, data)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	errString, err := loanutils.CancelApplication(args.LoanApplicationID, userObj.SourceEntityID, args.CancellationReason, args.EntityType, args.EntityRef)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	if errString != "" {
		err = fmt.Errorf("error in cancelling application for userID: %s. %s", userObj.ID, errString)
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	return ars, nil
}

func (a *Activity) UpdateOfferAfterDiscount(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {

	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}
	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	type argumentStruct struct {
		ProcessingFee string `arg:"processingFee"`
		InterestRate  string `arg:"interestRate"`
	}
	var args argumentStruct
	err = GetArgs(&args, data)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	offerMetaData := personalloanoffer.OfferMetadata{}

	offer, err := personalloanoffer.GetLatest(userObj.ID, constants.OfferStatusActive)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	if offer.Method == "" {
		offer.Method = "rb" // default
	}

	err = json.Unmarshal([]byte(offer.OfferMetadata), &offerMetaData)
	if err != nil {
		logger.WithUser(userObj.ID).Errorln(err)
		return ars, err
	}

	var updatedProcessingFee, updatedInterestRate float64
	if args.ProcessingFee != "" {
		updatedProcessingFee, err = strconv.ParseFloat(args.ProcessingFee, 64)
		if err != nil {
			logger.WithUser(userObj.ID).Error(err)
			errorHandler.ReportToSentryWithoutRequest(err)
			return ars, err
		}
		for i := 0; i < len(offerMetaData.Offers); i++ {
			offerMetaData.Offers[i].ProcessingFee = updatedProcessingFee
		}
	}

	if args.InterestRate != "" {
		updatedInterestRate, err = strconv.ParseFloat(args.InterestRate, 64)
		if err != nil {
			logger.WithUser(userObj.ID).Error(err)
			errorHandler.ReportToSentryWithoutRequest(err)
			return ars, err
		}
		for i := 0; i < len(offerMetaData.Offers); i++ {
			offerMetaData.Offers[i].Interest = updatedInterestRate
		}
	}

	offerMetadataBytes, err := json.Marshal(offerMetaData)
	if err != nil {
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	// Marking the old offer as inactive
	txn, _ := database.Beginx()
	defer txn.Rollback()
	err = personalloanoffer.UpdateStatusForUsers(txn, userObj.ID, constants.OfferStatusInactive)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		txn.Rollback()
		return ars, err
	}

	_, err = personalloanoffer.Create(txn, userObj.ID, offer.LenderID, offer.LenderID, general.GetUUID(), offer.EligibleAmount, offer.MinAmount, updatedInterestRate, offer.Tenure, offer.MinTenure, "final", offer.Method, updatedProcessingFee, offer.ProcessingFeeType, offer.OfferType, string(offerMetadataBytes), constants.OfferStatusActive)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		txn.Rollback()
		return ars, err
	}

	err = txn.Commit()
	if err != nil {
		logger.WithUser(userObj.ID).Errorln(err)
		return ars, err
	}

	dateTimeString := general.GetTimeStampString()
	desccription := fmt.Sprintf(`{"Old Offer", "Roi":"%.2f","PF":"%.2f","New Offer", "Roi":"%.2f","PF":"%.2f"}`, offer.Interest, offer.ProcessingFee, updatedInterestRate, updatedProcessingFee)
	go finboxEvents.ActivityLogger(userObj.ID, userObj.SourceEntityID, userObj.ID, constants.EntityTypeCustomer, constants.ActivityLoanOfferUpdated, desccription, "", dateTimeString, false)
	return ars, nil
}

// UpdateLoanApplicationNo gets details from lender variables and updates the latest loan application data
func (a *Activity) UpdateLoanApplicationNo(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	type argumentStruct struct {
		LoanApplicationNo string `arg:"loanApplicationNo" validate:"required"`
	}

	var args argumentStruct
	err = GetArgs(&args, data)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}

	loanApplication, err := loanapplication.GetLatestByUser(userObj.ID)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}

	loanApplicationID := loanApplication.ID.String()

	err = loanapplication.Update(nil, loanapplication.StructForSet{
		ID:                loanApplicationID,
		OldApplicationNo:  loanApplication.LoanApplicationNo,
		LoanApplicationNo: args.LoanApplicationNo,
	})
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}

	return ars, nil
}

// SubmitPreLoanData submit preloan data like Father's name, Mother's name, etc.
func (a *Activity) SubmitPreLoanData(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithFields(data, err)
		return ars, err
	}

	var arguments struct {
		PreLoanData       map[string]interface{} `arg:"preLoanData" required:"true"`
		LoanApplicationID string                 `arg:"loanApplicationID" required:"true"`
	}
	if err := GetArgsV2(&arguments, data); err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		return ars, err
	}

	err = userloandetails.UpdatePreLoanData(nil, arguments.PreLoanData, arguments.LoanApplicationID, userObj.ID)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		return ars, err
	}

	lenderID, err := loanapplication.GetLender(arguments.LoanApplicationID)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		return ars, err
	}
	// set laon application id in output map
	data["loanApplicationID"] = arguments.LoanApplicationID
	data["lenderID"] = lenderID
	data["sourceEntityID"] = userObj.SourceEntityID

	return ars, nil
}

// GetLoanApplication : This activity accepts `loanApplicationID` as argument and returns `loanApplication` with the loan application object. Incase of no entries, it returns a non-retryable error `loan not found`.
func (a *Activity) GetLoanApplication(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   map[string]interface{}{},
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		err = fmt.Errorf("[GetLoanApplication] Unable to decode userObj: %v", err)
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	type argumentStruct struct {
		LoanApplicationID string `arg:"loanApplicationID" required:"true"`
	}
	var args argumentStruct
	err = GetArgsV2(&args, data)
	if err != nil {
		err = fmt.Errorf("[GetLoanApplication] Error in GetArgs: %v", err)
		logger.WithUser(userObj.ID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, NewNonRetryableApplicationError(err.Error())
	}

	loanApplication, err := loanapplication.Get(ctx, args.LoanApplicationID)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			err = errors.New(constants.ErrStringLoanNotFound)
			logger.WithLoanApplication(args.LoanApplicationID).Errorf("[GetLoanApplication] Error in fetching loanApplication: %v", err)
			return ars, NewNonRetryableApplicationError(constants.ErrStringLoanNotFound)
		}
		logger.WithLoanApplication(args.LoanApplicationID).Errorf("[GetLoanApplication] Error in fetching loanApplication: %v", err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	ars.Data["loanApplication"] = loanApplication
	return ars, nil
}

// UpdateLoanAppWithNewPLOffer fetches offer with offerID and update all params in loanApplication
func (a *Activity) UpdateLoanAppWithNewPLOffer(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	loanApplicationID := data["loanApplicationID"].(string)

	type argumentStruct struct {
		NewLoanOfferID       string `arg:"newLoanOfferID" validate:"required"`
		UpdateDynamicCharges string `arg:"updateDynamicCharges"`
	}

	var args argumentStruct
	err = GetArgs(&args, data)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}

	updateDynamicCharges := args.UpdateDynamicCharges == "true"

	offer := personalloanoffer.Get(args.NewLoanOfferID)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}

	updateStruct := loanapplication.StructForSet{
		ID:            loanApplicationID,
		LoanOfferID:   args.NewLoanOfferID,
		Amount:        offer.MaxAmount,
		Tenure:        offer.MaxTenure,
		Interest:      &offer.Interest,
		ProcessingFee: &offer.ProcessingFee,
		AppliedAmount: offer.MaxAmount,
		AppliedTenure: offer.MaxTenure,
	}

	if len(offer.OfferMetadataObj.Offers) > 0 && updateDynamicCharges {
		jsonData, marshalErr := json.Marshal(map[string]interface{}{
			"charges": offer.OfferMetadataObj.Offers[0].Charges,
		})
		if marshalErr != nil {
			logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error("Error in marshalling charges: ", marshalErr)
			errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, marshalErr)
			return ars, marshalErr
		} else {
			updateStruct.DynamicCharges = string(jsonData)
		}
	}

	err = loanapplication.Update(nil, updateStruct)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}

	return ars, nil
}

// UpdateLoanDetails updates the loan_application table with calculated values like emi,advance_emi...
func (a *Activity) UpdateLoanEMI(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var (
		userObj           users.User
		loanOfferDetails  structs.FinalLoanOfferDetails
		loanApplicationID string
		loanType          string
	)

	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}
	userID := userObj.ID

	loanApplicationID, err = loanapplication.GetLoanIDFromUserID(userID)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	loanType, _ = loanapplication.GetLoanTypeFromID(loanApplicationID)

	if loanOfferDetails, err = loanutils.GetFinalOffer(loanType, loanApplicationID); err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	var finalDate = time.Now()
	if loanOfferDetails.SignDate != "" {
		finalDate, _ = time.Parse("2006-01-02 15:04:05", loanOfferDetails.SignDate)
	}
	if loanOfferDetails.DisbursalDate != "" {
		finalDate, _ = time.Parse("2006-01-02 15:04:05", loanOfferDetails.DisbursalDate)
	}

	loanOfferDetails.EMI, loanOfferDetails.AdvanceEMI, _ = calc.GetEMI(loanOfferDetails.Method, loanOfferDetails.Amount, loanOfferDetails.Tenure, loanOfferDetails.Interest, finalDate, userObj.SourceEntityID, loanOfferDetails.LenderID, userObj.ID)

	err = loanapplication.Update(nil, loanapplication.StructForSet{
		ID:         loanApplicationID,
		EMI:        loanOfferDetails.EMI,
		AdvanceEMI: &loanOfferDetails.AdvanceEMI,
	})
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	return ars, nil
}

func (a *Activity) InActivatePLOffer(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	type argumentStruct struct {
		OfferID         string `arg:"offerID" validate:"required"`
		RejectionReason string `arg:"rejectionReason"`
	}
	var args argumentStruct
	err = GetArgs(&args, data)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	offer := personalloanoffer.Get(args.OfferID)
	if general.InArr(offer.OfferStatus, []int{constants.OfferStatusIsAccepted, constants.OfferStatusWasAccepted, constants.OfferStatusInactive, constants.OfferStatusExpired}) {
		err = fmt.Errorf("offer is in invalid state: %s", args.OfferID)
		logger.WithUser(userObj.ID).Error(err)
		return ars, err
	}

	err = personalloanoffer.UpdateStatus(nil, args.OfferID, userObj.ID, constants.OfferStatusInactive)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	dateTimeString := general.GetTimeStampString()
	go finboxEvents.ActivityLogger(userObj.ID, userObj.SourceEntityID, userObj.ID, constants.EntityTypeSystem, constants.ActivityLoanOfferInactive, fmt.Sprintf(`{"lender": "%s", "rejectReason":"%s"}`, constants.LenderNamesMap[offer.LenderID], args.RejectionReason), "", dateTimeString, false)
	return ars, nil
}

// Deprecated: Use UpdateLoanApplicationV2 instead.
// UpdateLoanApplication - updates loan application. Currently it supports status and kycStatus,
// TODO: In future it should support all the fields.
func (a *Activity) UpdateLoanApplication(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	type argumentStruct struct {
		Status    int `arg:"status" required:"true"`
		KycStatus int `arg:"kycStatus" required:"true"`
	}

	var args argumentStruct
	err = GetArgsV2(&args, data)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}

	loanApplication, err := loanapplication.GetLatestByUser(userObj.ID)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}

	loanApplicationID := loanApplication.ID.String()

	err = loanapplication.Update(nil, loanapplication.StructForSet{
		ID:        loanApplicationID,
		Status:    &args.Status,
		KYCStatus: &args.KycStatus,
	})
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userObj.ID}, err)
		return ars, err
	}
	return ars, nil

}

// UpdateLoanApplicationV2 updates the loan application with the given data.
// This activity supports partial update of loan application while not enforcing any of the optional fields.
func (a *Activity) UpdateLoanApplicationV2(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()

	ars = runner.ActivityReturnStruct{
		Data:   map[string]interface{}{},
		Output: map[string]interface{}{},
	}

	var args struct {
		LoanApplicationID         string   `arg:"loanApplicationID" required:"true"`
		Status                    *int     `arg:"status"`
		KYCStatus                 *int     `arg:"kycStatus"`
		SignedAgreementTemplate   string   `arg:"signedAgreementTemplate"`
		UnsignedAgreementTemplate string   `arg:"unsignedAgreementTemplate"`
		LenderID                  string   `arg:"lenderID"`
		LoanApplicationNo         string   `arg:"loanApplicationNo"`
		DisbursedAmount           float64  `arg:"disbursedAmount"`
		OldLoanApplicationNo      string   `arg:"oldLoanApplicationNo"`
		Amount                    float64  `arg:"amount"`
		Tenure                    int      `arg:"tenure"`
		Interest                  *float64 `arg:"interest"`
		ProcessingFee             *float64 `arg:"processingFee"`
	}

	if err := GetArgsV2(&args, data); err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	if err := loanapplication.Update(nil, loanapplication.StructForSet{
		ID:                        args.LoanApplicationID,
		SignedAgreementTemplate:   args.SignedAgreementTemplate,
		UnsignedAgreementTemplate: args.UnsignedAgreementTemplate,
		Status:                    args.Status,
		LenderID:                  args.LenderID,
		LoanApplicationNo:         args.LoanApplicationNo,
		KYCStatus:                 args.KYCStatus,
		DisbursedAmount:           args.DisbursedAmount,
		OldApplicationNo:          args.OldLoanApplicationNo,
		Amount:                    args.Amount,
		Tenure:                    args.Tenure,
		Interest:                  args.Interest,
		ProcessingFee:             args.ProcessingFee,
	}); err != nil {
		logger.WithLoanApplication(args.LoanApplicationID).Errorln(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"loanApplicationID": args.LoanApplicationID}, err)
		return ars, err
	}

	return ars, nil
}

// GetUnsignedAgreementFromLoanApplication gets the unsigned agreement from loan application and returns the s3 object key
func (a *Activity) GetUnsignedAgreementFromLoanApplication(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   map[string]interface{}{},
		Output: map[string]interface{}{},
	}

	var args struct {
		LoanApplicationID string `arg:"loanApplicationID" required:"true"`
	}

	if err := GetArgsV2(&args, data); err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Errorln(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{
			"workflowID": activityInfo.WorkflowExecution.ID,
		}, err)
		return ars, err
	}

	unsignedAgreementObjectKey, err := loanapplication.GetUnsignedAgreementObjectKey(args.LoanApplicationID)
	if err != nil {
		logger.WithLoanApplication(args.LoanApplicationID).Errorln(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{
			"loanApplicationID": args.LoanApplicationID,
			"workflowID":        activityInfo.WorkflowExecution.ID,
		}, err)
		return ars, err
	}

	ars.Data["unsignedAgreement"] = map[string]interface{}{
		"s3ObjectKey": unsignedAgreementObjectKey,
	}

	return
}

// UpdatePlMetadata updates the offer metadata of just the First index offer to the values passed in arguments.
// TODO Extend it for the whole offer array
func (a *Activity) UpdatePlMetadata(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	type argumentStruct struct {
		OfferID   string  `arg:"offerID" required:"true"`
		Amount    float64 `arg:"amount"`
		OfferType string  `arg:"offerType"`
	}
	var args argumentStruct
	err = GetArgsV2(&args, data)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}
	offer := personalloanoffer.Get(args.OfferID)
	if len(offer.OfferMetadataObj.Offers) < 1 {
		err = errors.New("no offer present in Metadata")
		return ars, err
	}
	if args.OfferType != "" {
		offer.OfferMetadataObj.OfferType = args.OfferType
	}
	if args.Amount > 0 {
		offer.OfferMetadataObj.Offers[0].MaxAmount = args.Amount
	}
	offerMetadataBytes, err := json.Marshal(offer.OfferMetadataObj)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		return ars, err
	}
	err = personalloanoffer.UpdateOfferMetadata(nil, args.OfferID, string(offerMetadataBytes))
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		return ars, err
	}
	return ars, nil
}

func (a *Activity) InsertIntoExpiry(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}
	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		return ars, err
	}
	type argumentStruct struct {
		OfferID string `arg:"offerID" required:"true"`
	}
	var args argumentStruct
	err = GetArgsV2(&args, data)
	if err != nil {
		return ars, err
	}
	lenderID := data["lenderID"].(string)
	createdAtStr := data["createdAt"].(string)
	// Parse the createdAt string into a time.Time object
	createdAt, err := time.Parse(time.RFC3339, createdAtStr)
	if err != nil {
		return ars, fmt.Errorf("error parsing createdAt: %w", err)
	}
	applicationCancellationDays := constants.LenderApplicationCancellationMapping[lenderID]
	var applicationCancellationDate time.Time
	if applicationCancellationDays != 0 {
		if general.InArr(lenderID, []string{constants.CasheMCID, constants.LandTID, constants.NiroID, constants.ABFLPLID}) {
			applicationCancellationDate = createdAt.AddDate(0, 0, applicationCancellationDays)
		} else {
			applicationCancellationDate = time.Now().AddDate(0, 0, applicationCancellationDays)
		}
		err = expiry.InsertV2(userObj.ID, "application_"+lenderID, "application_created_"+lenderID, applicationCancellationDate, args.OfferID)
		if err != nil {
			logger.WithUser(userObj.ID).Error(err)
			return ars, err
		}
	}
	return ars, nil
}

// PFLEL hack
// This activity makes no sense
func (a *Activity) AcceptELLoanOffer(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}
	type argumentStruct struct {
		Amount   float64 `arg:"amount"`
		Tenure   int     `arg:"tenure"`
		ROI      float64 `arg:"roi"`
		PF       float64 `arg:"pf"`
		LenderID string  `arg:"lenderID"`
		OfferID  string  `arg:"offerID"`
	}

	var args argumentStruct
	err = GetArgsV2(&args, data)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	tx, err := database.Beginx()
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	defer tx.Rollback()

	status := constants.LoanStatusFresh
	loanApplicationID := general.GetUUID()
	loan := loanapplication.StructForSet{
		ID:                loanApplicationID,
		UserID:            userObj.ID,
		SourceEntityID:    userObj.SourceEntityID,
		CreatedBy:         userObj.ID,
		LoanApplicationNo: journey.GenerateLoanApplicationNo(userObj.SourceEntityID),
		LoanType:          constants.LoanTypePersonalLoan,
		LoanOfferID:       args.OfferID,
		Amount:            args.Amount,
		Tenure:            args.Tenure,
		Interest:          &args.ROI,
		Status:            &status,
		LenderID:          args.LenderID,
		ProcessingFee:     &args.PF,
		AppliedAmount:     args.Amount, // This can be removed later as redundant to amount
		AppliedTenure:     args.Tenure, // This can be removed later as redundant to tenure
		GST:               18,
	}
	err = loanapplication.InsertV2(&ctx, tx, loan)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}
	if err = tx.Commit(); err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	lenderName := constants.LenderNamesMap[args.LenderID]

	dateTimeString := general.GetTimeStampString()
	go finboxEvents.ActivityLogger(userObj.ID, userObj.SourceEntityID, userObj.ID, constants.EntityTypeCustomer, constants.ActivityOfferAccepted, fmt.Sprintf(`{"lender": "%s", "amount":"%.2f","Roi":"%f","Tenure":"%v"}`, lenderName, args.Amount, *loan.Interest, args.Tenure), loanApplicationID, dateTimeString, false)
	dateTimeString = general.GetTimeStampString()
	go finboxEvents.ActivityLogger(userObj.ID, userObj.SourceEntityID, "", constants.EntityTypeSystem, constants.ActivityLoanApplicationCreated, fmt.Sprintf(`{"lender": "%s"}`, lenderName), loanApplicationID, dateTimeString, false)

	data["loanApplicationID"] = loanApplicationID

	return ars, nil

}

func (a *Activity) PrepareSanctionLetter(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}
	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		return ars, err
	}

	loanApplication, err := loanapplication.GetLatestByUser(userObj.ID)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		return ars, err
	}
	loanApplicationID := loanApplication.ID.String()
	preSignedURL, err := agreement.GenerateSanctionLetterPreSignedURL(loanApplicationID, userObj.ID, userObj.SourceEntityID, loanApplication.LenderID, "")
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		return ars, err
	}
	finboxEvents.ActivityLogger(userObj.ID, userObj.SourceEntityID, userObj.ID, constants.EntityTypeSystem, constants.ActivitySanctionLetterGenerated, "", loanApplicationID, general.GetTimeStampString(), false)
	err = lenderservice.UploadDocument(ctx, &lenderservice.UploadDocumentReq{
		ApplicationReq: lenderservice.ApplicationReq{
			UserID:         userObj.ID,
			SourceEntityID: userObj.SourceEntityID,
			LenderID:       loanApplication.LenderID,
		},
		Document: &lenderservice.KYCDocumentstructsDetails{
			DocumentURL: preSignedURL,
		},
	})
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		return ars, err
	}
	// set presignedURL in output
	output["sanctionPreSignedURL"] = preSignedURL

	return ars, nil
}

// GetOfferType gets the offer type of the latest active offer for the user
func (a *Activity) GetOfferType(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		return ars, err
	}

	offer, err := personalloanoffer.GetLatest(userObj.ID, constants.OfferStatusActive)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		return ars, err
	}

	var offerMetadatObj personalloanoffer.OfferMetadata
	err = json.Unmarshal([]byte(offer.OfferMetadata), &offerMetadatObj)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		return ars, err
	}

	output["offerType"] = offerMetadatObj.Offers[0].OfferType
	data["offerType"] = offerMetadatObj.Offers[0].OfferType

	return ars, nil
}

// CreateLoanApplication creates a new loan application for the user
func (a *Activity) CreateLoanApplication(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		return ars, err
	}

	type argumentStruct struct {
		Amount   float64 `arg:"amount"`
		Tenure   int     `arg:"tenure"`
		ROI      float64 `arg:"roi"`
		PF       float64 `arg:"pf"`
		LenderID string  `arg:"lenderID"`
	}

	var args argumentStruct
	err = GetArgsV2(&args, data)
	if err != nil {
		return ars, err
	}

	status := constants.LoanStatusFresh
	loanApplicationID := general.GetUUID()
	loan := loanapplication.StructForSet{
		ID:                loanApplicationID,
		UserID:            userObj.ID,
		SourceEntityID:    userObj.SourceEntityID,
		CreatedBy:         userObj.ID,
		LoanApplicationNo: journey.GenerateLoanApplicationNo(userObj.SourceEntityID),
		LoanType:          constants.LoanTypePersonalLoan,
		Amount:            args.Amount,
		Tenure:            args.Tenure,
		Interest:          &args.ROI,
		Status:            &status,
		LenderID:          args.LenderID,
		ProcessingFee:     &args.PF,
		AppliedAmount:     args.Amount, // This can be removed later as redundant to amount
		AppliedTenure:     args.Tenure, // This can be removed later as redundant to tenure
	}
	err = loanapplication.InsertV2(&ctx, nil, loan)
	if err != nil {
		return ars, err
	}
	lenderName := constants.LenderNamesMap[args.LenderID]
	dateTimeString := general.GetTimeStampString()
	go finboxEvents.ActivityLogger(userObj.ID, userObj.SourceEntityID, "", constants.EntityTypeSystem, constants.ActivityLoanApplicationCreated, fmt.Sprintf(`{"lender": "%s"}`, lenderName), loanApplicationID, dateTimeString, false)

	return ars, nil
}

func (a *Activity) PrepareConsentPDF(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}
	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		return ars, err
	}
	type argumentStruct struct {
		Date string `arg:"date" required:"true"`
	}

	var args argumentStruct
	err = GetArgsV2(&args, data)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		return ars, err
	}

	objectKey, err := agreement.GenerateConsentPDF(userObj.ID, userObj.Mobile, userObj.Email, args.Date)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		return ars, err
	}
	// set presignedURL in output
	output["consentObjKey"] = objectKey

	return ars, nil
}

func (a *Activity) GetLoansByDateRangeAndStatus(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   map[string]any{},
		Output: map[string]any{},
	}

	var args struct {
		StartDate      string `arg:"startDate" required:"true" validate:"required,datetime=2006-01-02"`
		EndDate        string `arg:"endDate" required:"true" validate:"required,datetime=2006-01-02"`
		Status         string `arg:"status" required:"true"`
		SourceEntityID string `arg:"sourceEntityID" required:"true"`
	}
	err = GetArgsV2(&args, data)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	startDate, err := time.Parse("2006-01-02", args.StartDate)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}

	endDate, err := time.Parse("2006-01-02", args.EndDate)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}
	status, ok := constants.ApplicationStatusToLoanStatusMap[args.Status]
	if !ok {
		err = fmt.Errorf("invalid status provided: %s", args.Status)
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}
	var loans []loanapplication.LoanApplicationLean
	switch status {
	case constants.LoanStatusClosed:
		loans, err = loanclosure.GetClosedLoansByDateRange(ctx, args.SourceEntityID, loanclosure.StatusAccepted, startDate, endDate)
		if err != nil {
			logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
			errorHandler.ReportToSentryWithoutRequest(err)
			return ars, err
		}
	default:
		loans, err = loanapplication.GetLoansByDateRangeAndStatus(ctx, args.SourceEntityID, status, startDate, endDate)
		if err != nil {
			logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
			errorHandler.ReportToSentryWithoutRequest(err)
			return ars, err
		}
	}

	ars.Data["loans"] = loans

	return ars, nil
}

func (a *Activity) GetAllPlOffer(ctx context.Context, data, output map[string]interface{}) (ars runner.ActivityReturnStruct, err error) {
	activityInfo := activity.GetInfo(ctx)
	span, _ := a.Tracer.StartSpan(ctx, constants.TemporalOperationName, activityInfo.ActivityType.Name)
	defer func() {
		span.Finish(err)
	}()
	ars = runner.ActivityReturnStruct{
		Data:   data,
		Output: output,
	}

	var userObj users.User
	err = general.DecodeToStruct(data["userObj"], &userObj)
	if err != nil {
		logger.WithWorkflow(activityInfo.WorkflowExecution.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}
	offers, err := personalloanoffer.GetLatestForUser(userObj.ID)
	if err != nil {
		logger.WithUser(userObj.ID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return ars, err
	}
	data["offers"] = offers
	return ars, nil
}
