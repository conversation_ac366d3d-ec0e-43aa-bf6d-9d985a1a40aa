package providers

import (
	"finbox/go-api/authentication"
	authCont "finbox/go-api/controller/auth"
	userCont "finbox/go-api/controller/user"
	userContV2 "finbox/go-api/controller/user/v2"
	"finbox/go-api/middlewares"
	userReq "finbox/go-api/requestHandler/user/v2"
	"finbox/go-api/responseHandler/responseCommon"
	"net/http"

	"github.com/go-chi/chi/v5"
)

// URLMethodRouter is a provider with returns router based on url and method
type URLMethodRouter struct {
	EndpointToAPINameMapping map[string]map[string]string
	APINameToRouteMapping    map[string]Route
}

var URLMethodRouterInstance = NewURLMethodRouter()

// apiNameToRouteMapping represents a map containing routers for different routes in a web application.
// The keys in the map are route patterns, and the values are router objects.
// Each router object contains the following fields:
// - Name: Represents the name of the router.
// - Middlewares: Represents a collection of middleware functions.
// - Controller: Defines a http.HandlerFunc for handling the specific route in the router.
// - authType: Represents the type of authentication used for the specific route in the router.
var apiNameToRouteMapping = map[string]Route{
	"getHealthCheck": {nil, func(w http.ResponseWriter, r *http.Request) { w.WriteHeader(http.StatusOK) }, NoAuth},
	"postCreateUserLead": {
		Middlewares: []MiddlewareFunc{
			middlewares.SMApiKeyModifier,
			middlewares.ModifyResponseBodyMiddleware,
			middlewares.ModifyRequestBodyMiddleware,
			authentication.ServerAuthFilterV2,
			userReq.CombinedUserDetailsReq,
			middlewares.UserExistsCheck, // Middleware to check if the user already exist
			userCont.CreateEntityServerCont,
			userContV2.CreateUserServerCont,
			userContV2.UserRejectCheckFilter, // Middleware to check if the user is rejected, even though the user is created just before, there might be a case where the user was created before but performed UUD, etc later.
			middlewares.APIStackValidAPICheck,
			userContV2.UpdateUserDetailsCont,
		},
		Controller: responseCommon.GenericResV3,
		AuthType:   OAuth,
	},

	"postCreateUserLeadWithPrequal": {
		Middlewares: []MiddlewareFunc{
			middlewares.SMApiKeyModifier,
			middlewares.ModifyResponseBodyMiddleware,
			middlewares.ModifyRequestBodyMiddleware,
			authentication.ServerAuthFilterV2,
			userReq.CombinedUserDetailsReq,
			middlewares.UserExistsCheck, // Middleware to check if the user already exists
			userCont.CreateEntityServerCont,
			userContV2.CreateUserServerCont,
			userContV2.UserRejectCheckFilter, // Middleware to check if the user is rejected, even though the user is created just before, there might be a case where the user was created before but performed UUD, etc later.
			middlewares.APIStackValidAPICheck,
			userContV2.UpdateUserDetailsCont,
			middlewares.APIStackValidAPICheck,
			userContV2.TriggerPrequalificationsCont,
			userContV2.PollPrequalificationResultsCont,
		},
		Controller: responseCommon.GenericResV3,
		AuthType:   OAuth,
	},

	"postGenerateOffer": {
		Middlewares: []MiddlewareFunc{
			middlewares.SMApiKeyModifier,
			middlewares.ModifyResponseBodyMiddleware,
			middlewares.ModifyRequestBodyMiddleware,
			authentication.ServerAuthFilterV2,
			userReq.GenerateOffersReq,
			userContV2.UserRejectCheckFilter,
			middlewares.APIStackValidAPICheck,
			userContV2.GenerateOffersCont,
		},
		Controller: responseCommon.GenericResV3,
		AuthType:   OAuth,
	},
	"postInitiateBankConnect": {
		Middlewares: []MiddlewareFunc{
			middlewares.SMApiKeyModifier,
			middlewares.ModifyResponseBodyMiddleware,
			middlewares.ModifyRequestBodyMiddleware,
			authentication.ServerAuthFilterV2,
			userReq.SessionServerReq,
			userContV2.UserRejectCheckFilter,
			middlewares.APIStackValidAPICheck,
			userContV2.InitiateBankConnectCont,
		},
		Controller: responseCommon.GenericResV3,
		AuthType:   OAuth,
	},
	"postGetOffer": {
		Middlewares: []MiddlewareFunc{
			middlewares.SMApiKeyModifier,
			middlewares.ModifyResponseBodyMiddleware,
			middlewares.ModifyRequestBodyMiddleware,
			authentication.ServerAuthFilterV2,
			userReq.SuperMoneyGetResultReq,
			userContV2.GetOffersCont,
		},
		Controller: responseCommon.GenericResV3,
		AuthType:   OAuth,
	},
	"postSubmitOffer": {
		Middlewares: []MiddlewareFunc{
			middlewares.SMApiKeyModifier,
			middlewares.ModifyResponseBodyMiddleware,
			middlewares.ModifyRequestBodyMiddleware,
			authentication.ServerAuthFilterV2,
			userReq.AcceptOfferReq,
			userContV2.UserRejectCheckFilter,
			middlewares.APIStackValidAPICheck,
			userContV2.AcceptOfferCont,
		},
		Controller: responseCommon.GenericResV3,
		AuthType:   OAuth,
	},
	"postGetApplicationStatus": {
		Middlewares: []MiddlewareFunc{
			middlewares.SMApiKeyModifier,
			middlewares.ModifyResponseBodyMiddleware,
			middlewares.ModifyRequestBodyMiddleware,
			authentication.ServerAuthFilterV2,
			userReq.ApplicationStatusReq,
			userContV2.ApplicationStatusCont,
		},
		Controller: responseCommon.GenericResV3,
		AuthType:   OAuth,
	},
	"postGetApplication": {
		Middlewares: []MiddlewareFunc{
			middlewares.SMApiKeyModifier,
			middlewares.ModifyResponseBodyMiddleware,
			middlewares.ModifyRequestBodyMiddleware,
			authentication.ServerAuthFilterV2,
			userReq.ApplicationStatusReq,
			userContV2.ApplicationStatusCont,
		},
		Controller: responseCommon.GenericResV3,
		AuthType:   OAuth,
	},
	"postOAuthTokenGenerate": {
		Middlewares: []MiddlewareFunc{
			middlewares.SMApiKeyModifier,
			middlewares.ModifyResponseBodyMiddleware,
			middlewares.ModifyRequestBodyMiddleware,
			authCont.OAuthCont,
		},
		Controller: responseCommon.GenericResV3,
		AuthType:   NoAuth,
	},
}

var endpointToAPINameMapping = map[string]map[string]string{
	"superMoney/lender/1/healthCheck":             {"GET": "getHealthCheck"},
	"superMoney/lender/2.0/createApplication":     {"POST": "postCreateUserLead"},
	"superMoney/lender/2.0/generateOffer":         {"POST": "postGenerateOffer"},
	"superMoney/lender/2.0/getOffer":              {"POST": "postGetOffer"},
	"superMoney/lender/2.0/initAccountAggregator": {"POST": "postInitiateBankConnect"},
	"superMoney/lender/2.0/submitOffer":           {"POST": "postSubmitOffer"},
	"superMoney/lender/2.0/getApplicationStatus":  {"POST": "postGetApplicationStatus"},
	"superMoney/lender/2.0/getApplication":        {"POST": "postGetApplication"},
	"superMoney/lender/1/token":                   {"POST": "postOAuthTokenGenerate"},
	"superMoney/lender/1/encrypt":                 {"POST": "postEncryptData"},
	"superMoney/lender/1/decrypt":                 {"POST": "postDecryptData"},

	"prefr/lender/1/healthCheck":            {"GET": "getHealthCheck"},
	"prefr/lender/1/token":                  {"POST": "postOAuthTokenGenerate"},
	"prefr/lender/2.0/createApplication":    {"POST": "postCreateUserLeadWithPrequal"},
	"prefr/lender/2.0/getApplicationStatus": {"POST": "postGetApplicationStatus"},
	"prefr/lender/2.0/getApplication":       {"POST": "postGetApplication"},
}

func NewURLMethodRouter() Provider {
	return &URLMethodRouter{
		APINameToRouteMapping:    apiNameToRouteMapping,
		EndpointToAPINameMapping: endpointToAPINameMapping,
	}
}

func (umr *URLMethodRouter) GetRoute(r *http.Request) (router Route, exists bool) {
	routeName := umr.GetRouteNameFromURL(r)
	if routeName == "" {
		return router, false
	}
	router, exists = umr.APINameToRouteMapping[routeName]
	return router, exists
}

func (umr *URLMethodRouter) GetRouteNameFromURL(r *http.Request) string {
	uri := chi.URLParam(r, "*")
	method := r.Method
	route, ok := umr.EndpointToAPINameMapping[uri]
	if !ok {
		return ""
	}
	routeName, ok := route[method]
	if !ok {
		return ""
	}
	return routeName
}
