package customRoutes

import (
	"context"
	"database/sql"
	"encoding/json"
	"finbox/go-api/common/datadancer"
	"finbox/go-api/functions/logger"
	"finbox/go-api/infra/db"
	"finbox/go-api/infra/redis"
	"finbox/go-api/infra/s3"
	configsql "finbox/go-api/internal/repository/psql/configmanagement"
	configsrv "finbox/go-api/internal/service/configmanagement"
	"finbox/go-api/internal/service/pickle"
	"finbox/go-api/internal/util/tags"
	"finbox/go-api/models/activitylog"
	"finbox/go-api/models/users"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	configv1 "github.com/finbox-in/pickle-protos/gen/go/config/v1"
	"github.com/spf13/cast"
)

var database = db.GetDB()

// TODO: can be reduce the duplicate lines
func GetCustomModifiedRequestPayload(
	ctx context.Context,
	req PayloadDetails,
	client string,
) (finalPayload interface{}, err error) {
	reqObj, _, uri := req.Obj, req.Header, req.URI
	// initialize internal srv repository
	svr := configsrv.NewConfigManagementSrvRepository(configsrv.ConfigManagementSrvRepository{
		ConfigDBRepositoryProvider: configsql.NewConfigManagementDBRepository(database),
	})

	// initialize options for config-management configs
	options := Options{
		Client:     client,
		SchemaType: "request",
		UserID:     "",
	}

	// getting basic flags which will determine to transform Data statusCodes and encryption and decryption logic
	flags, err := getCustomFlags(ctx, options, svr)
	if err != nil {
		logger.WithContext(ctx).Errorln(err)
		if err == sql.ErrNoRows {
			return reqObj, nil
		}
		return nil, err
	}

	// get payloadType config and other flags related to payload transformation
	config, err := getCustomConfig(ctx, options, svr)
	if err != nil {
		logger.WithContext(ctx).Errorln(err)
		if err == sql.ErrNoRows {
			return reqObj, nil
		}
		return nil, err
	}

	// transform payload using data-dancer
	finalPayload = reqObj
	if flags.RouteBasedPayloadModification {
		// payloadType from custom route which will different for different clients
		routeConfig := config.Routes[uri]
		if routeConfig.PayloadType == "" {
			return nil, fmt.Errorf("uri not found %s for getting payloadType", uri)
		}
		options.ConfigType = routeConfig.PayloadType
		jqConfig, jqErr := svr.LegacyProvider().GetJQConfigForDataConversion(ctx, options.Client,
			configsrv.ResourceIdentifierCustomPayloadJQConfig, options.SchemaType, options.ConfigType)
		if jqErr != nil {
			logger.WithContext(ctx).Errorln(jqErr)
			return nil, jqErr
		}
		var key string
		if flags.CryptoEnabled {
			key, err = getCryptoKey(ctx, config.CryptoConfig.S3PrivateKeyPath, client)
			if err != nil {
				return nil, err
			}
		}

		finalPayload, err = datadancer.TransformDataFromDataDancerConfigUsingDataDancer(
			context.Background(),
			reqObj,
			jqConfig,
			map[string]interface{}{"privateKey": key},
		)
		if err != nil {
			return nil, err
		}
	}

	return finalPayload, nil
}

func GetCustomModifiedResponsePayload(
	ctx context.Context,
	res PayloadDetails,
	client string,
) (finalPayload interface{}, modifiedHttpStatusCode int, err error) {
	respData, httpStatusCode, _, uri := res.Obj, res.StatusCode, res.Header, res.URI
	// initialize internal srv repository
	svr := configsrv.NewConfigManagementSrvRepository(configsrv.ConfigManagementSrvRepository{
		ConfigDBRepositoryProvider: configsql.NewConfigManagementDBRepository(database),
	})

	// initialize options for config-management configs
	options := Options{
		Client:     client,
		SchemaType: "response",
		UserID:     "",
	}

	// getting basic flags which will determine to transform Data statusCodes and encryption and decryption logic
	flags, err := getCustomFlags(ctx, options, svr)
	if err != nil {
		logger.WithContext(ctx).Errorln(err)
		if err == sql.ErrNoRows {
			return respData, httpStatusCode, nil
		}
		return nil, httpStatusCode, err
	}

	// get payloadType config and other flags related to payload transformation
	config, err := getCustomConfig(ctx, options, svr)
	if err != nil {
		logger.WithContext(ctx).Errorln(err)
		if err == sql.ErrNoRows {
			return respData, httpStatusCode, nil
		}
		return nil, httpStatusCode, err
	}

	// transform payload using data-dancer
	finalPayload = respData
	if flags.RouteBasedPayloadModification {
		// payloadType from custom route which will different for different clients
		routeConfig := config.Routes[uri]
		if routeConfig.PayloadType == "" {
			return nil, httpStatusCode, fmt.Errorf("uri not found %s for getting payloadType", uri)
		}

		cid := cast.ToString(tags.GetTagValue(ctx, tags.CustomerID))
		respDataMap := cast.ToStringMap(respData)
		respDataMap["customerID"] = cid

		respDataMap["events"] = []activitylog.ActivityLog{}
		if routeConfig.EnabledActivityLogFetch {
			user, ok := tags.GetTagValue(ctx, tags.UserObject).(users.User)
			if ok {
				events, err := activitylog.GetActivityLogsByUserID(user.ID)
				if err != nil {
					return nil, httpStatusCode, err
				}

				respDataMap["events"] = events
			}
		}
		options.ConfigType = routeConfig.PayloadType
		jqConfig, jqErr := svr.LegacyProvider().GetJQConfigForDataConversion(ctx, options.Client,
			configsrv.ResourceIdentifierCustomPayloadJQConfig, options.SchemaType, options.ConfigType)
		if jqErr != nil {
			logger.WithContext(ctx).Errorln(jqErr)
			return nil, httpStatusCode, jqErr
		}
		var key string
		if flags.CryptoEnabled {
			key, err = getCryptoKey(ctx, config.CryptoConfig.S3PublicKeyPath, client)
			if err != nil {
				return nil, httpStatusCode, err
			}
		}
		finalPayload, err = datadancer.TransformDataFromDataDancerConfigUsingDataDancer(
			context.Background(),
			respDataMap,
			jqConfig,
			map[string]interface{}{"publicKey": key},
		)
		if err != nil {
			return nil, httpStatusCode, err
		}
	}
	var finalHttpStatusCode = httpStatusCode
	if flags.StatusCodeModification {
		finalHttpStatusCode = config.StatusCodeMapping[cast.ToString(httpStatusCode)]
	}
	if finalHttpStatusCode == 0 {
		finalHttpStatusCode = httpStatusCode
	}
	return finalPayload, httpStatusCode, nil
}

func GetCustomModifiedWebhookPayload(
	ctx context.Context,
	res PayloadDetails,
	client string,
) (finalPayload interface{}, modifiedHttpStatusCode int, err error) {
	respData, httpStatusCode, _, uri := res.Obj, res.StatusCode, res.Header, res.URI

	// Get extra data from the context
	extraData := tags.GetTagValue(ctx, tags.ExtraResponseData)
	extraRespDataMap := cast.ToStringMap(extraData)

	// initialize internal srv repository
	svr := configsrv.NewConfigManagementSrvRepository(configsrv.ConfigManagementSrvRepository{
		ConfigDBRepositoryProvider: configsql.NewConfigManagementDBRepository(database),
	})

	// initialize options for config-management configs
	options := Options{
		Client:     client,
		SchemaType: "webhook",
		UserID:     "",
	}

	// getting basic flags which will determine to transform Data statusCodes and encryption and decryption logic
	flags, err := getCustomFlags(ctx, options, svr)
	if err != nil {
		logger.WithContext(ctx).Errorln(err)
		if err == sql.ErrNoRows {
			return respData, httpStatusCode, nil
		}
		return nil, httpStatusCode, err
	}

	// get payloadType config and other flags related to payload transformation
	config, err := getCustomConfig(ctx, options, svr)
	if err != nil {
		logger.WithContext(ctx).Errorln(err)
		if err == sql.ErrNoRows {
			return respData, httpStatusCode, nil
		}
		return nil, httpStatusCode, err
	}

	// transform payload using data-dancer
	finalPayload = respData
	if flags.RouteBasedPayloadModification {
		// payloadType from custom route which will different for different clients
		routeConfig := config.Routes[uri]
		if routeConfig.PayloadType == "" {
			return nil, httpStatusCode, fmt.Errorf("uri not found %s for getting payloadType", "webhook/*")
		}
		respDataMap := cast.ToStringMap(respData)
		respDataMap["data"] = map[string][]activitylog.ActivityLog{
			"events": []activitylog.ActivityLog{},
		}

		if routeConfig.EnabledActivityLogFetch {
			userID, _ := respDataMap["userID"].(string)
			events, err := activitylog.GetActivityLogsByUserID(userID)
			if err != nil {
				return nil, httpStatusCode, err
			}
			respDataMap["data"] = map[string][]activitylog.ActivityLog{
				"events": events,
			}
		}
		options.ConfigType = routeConfig.PayloadType
		jqConfig, jqErr := svr.LegacyProvider().GetJQConfigForDataConversion(ctx, options.Client,
			configsrv.ResourceIdentifierCustomPayloadJQConfig, options.SchemaType, options.ConfigType)
		if jqErr != nil {
			logger.WithContext(ctx).Errorln(jqErr)
			return nil, httpStatusCode, jqErr
		}
		var publicKey string
		var privateKey string
		if flags.CryptoEnabled {
			privateKey, err = getCryptoKey(ctx, config.CryptoConfig.S3PrivateKeyPath, client)
			if err != nil {
				logger.WithContext(ctx).Error(err)
				return nil, httpStatusCode, err
			}

			publicKey, err = getCryptoKey(ctx, config.CryptoConfig.S3PublicKeyPath, client)
			if err != nil {
				logger.WithContext(ctx).Error(err)
				return nil, httpStatusCode, err
			}
		}

		finalPayload, err = datadancer.TransformDataFromDataDancerConfigUsingDataDancer(
			context.Background(),
			respDataMap,
			jqConfig,
			map[string]interface{}{"publicKey": publicKey, "privateKey": privateKey, "extraResponseData": extraRespDataMap},
		)
		if err != nil {
			return nil, httpStatusCode, err
		}
	}
	if flags.StatusCodeModification {
		modifiedHttpStatusCode = config.StatusCodeMapping[cast.ToString(httpStatusCode)]
	}
	return finalPayload, modifiedHttpStatusCode, nil
}

func GetClientFromSourceEntity(ctx context.Context, sourceEntityID string) (client string, err error) {

	pickleClient, err := pickle.GetJourneyClient(ctx)
	if err != nil {
		logger.WithContext(ctx).Errorln(err)
		return "", err
	}
	response, err := pickleClient.GetConfigClient().GetConfig(ctx, &configv1.GetConfigRequest{
		ResourceName: pickle.ResourceCustomRouteClientName,
		KeyValueMap: map[string]string{
			pickle.KeySourceEntityID: sourceEntityID,
		},
	})

	if err != nil {
		logger.WithContext(ctx).Errorln(err)
		return "", err
	}

	return response.ConfigData, nil
}

func getCustomFlags(ctx context.Context, options Options, svr configsrv.ConfigManagementSrvRepositoryProvider) (Flags, error) {
	var flags Flags
	config, err := svr.LegacyProvider().GetCustomClientFlags(ctx, options.Client, configsrv.ResourceIdentifierCustomClientFlags)
	if err != nil {
		logger.WithContext(ctx).Errorln(err)
		return flags, err
	}

	if err = json.Unmarshal([]byte(config), &flags); err != nil {
		logger.WithContext(ctx).Errorln(err)
		return flags, err
	}

	return flags, nil
}

func getCustomConfig(ctx context.Context, options Options, svr configsrv.ConfigManagementSrvRepositoryProvider) (Config, error) {
	var clientConfig Config
	config, err := svr.LegacyProvider().GetCustomClientConfig(ctx, options.Client, configsrv.ResourceIdentifierCustomClientConfig)
	if err != nil {
		logger.WithContext(ctx).Errorln(err)
		return clientConfig, err
	}

	if err = json.Unmarshal([]byte(config), &clientConfig); err != nil {
		logger.WithContext(ctx).Errorln(err)
		return clientConfig, err
	}

	return clientConfig, nil
}

func getCryptoKey(ctx context.Context, s3Path, client string) (string, error) {
	redisKey := client + ":" + s3Path
	key, err := redis.Get(ctx, redisKey)
	if err != nil || key == "" {
		path, s3Err := s3.GetLocalFilePath(s3Path)
		if s3Err != nil {
			logger.WithContext(ctx).Error(s3Err)
			return "", s3Err
		}
		r, s3Err := os.ReadFile(filepath.Clean(path))
		if s3Err != nil {
			logger.WithContext(ctx).Error(s3Err)
			return "", s3Err
		}
		key = string(r)
		go func() {
			e := redis.Set(redisKey, key, time.Minute*5)
			if e != nil {
				logger.WithContext(ctx).Error(e)
				return
			}
		}()
	}
	fixedKey := strings.ReplaceAll(key, `\n`, "\n")
	return fixedKey, nil
}
