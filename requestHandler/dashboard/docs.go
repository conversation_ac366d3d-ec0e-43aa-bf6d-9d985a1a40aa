package dashboard

import (
	"context"
	"encoding/json"
	"finbox/go-api/constants"
	"finbox/go-api/errorHandler"
	mdashboard "finbox/go-api/models/dashboard"
	"finbox/go-api/utils/general"
	"net/http"
)

func UploadMedia(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		sourceEntityID := r.Header.Get(constants.SourceEntityID)

		// Set attributes to be passed along in the context
		var attributes = make(map[string]interface{})
		attributes["sourceEntityID"] = sourceEntityID

		ctx = context.WithValue(ctx, "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func GetDocumentsReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()
		var req mdashboard.GetDocumentsParam

		// Parse JSON request body instead of query parameters
		decoder := json.NewDecoder(r.Body)
		if err := decoder.Decode(&req); err != nil {
			log.WithContext(ctx).Errorf("[GetDocumentsReq] failed to decode request body. err: %v", err)
			errorHandler.CustomError(w, http.StatusBadRequest, "Invalid request format")
			return
		}
		defer r.Body.Close()

		// Validate required parameters
		if req.LoanApplicationID == "" {
			errorHandler.CustomError(w, http.StatusBadRequest, mdashboard.EmptyLoanApplicationID)
			return
		}

		// Validate UUID format
		if !general.ValidateUUID(req.LoanApplicationID) {
			errorHandler.CustomError(w, http.StatusBadRequest, mdashboard.InvalidLoanID)
			return
		}

		if req.UserID == "" {
			errorHandler.CustomError(w, http.StatusBadRequest, mdashboard.EmptyUserID)
			return
		}

		// Validate UUID format
		if !general.ValidateUUID(req.UserID) {
			errorHandler.CustomError(w, http.StatusBadRequest, mdashboard.InvalidUserID)
			return
		}

		// Process docIDs if present
		if len(req.DocIDs) > 0 {
			// Validate each DocID UUID if needed
			for _, docID := range req.DocIDs {
				if !general.ValidateUUID(docID) {
					errorHandler.CustomError(w, http.StatusBadRequest, "Invalid document ID format")
					return
				}
			}
		}

		// Set attributes to be passed along in the context
		var attributes = make(map[string]interface{})
		attributes["req"] = req

		ctx = context.WithValue(ctx, "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func ListMastersDataReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()
		var req mdashboard.ListMastersDataParam

		// Parse JSON request body
		decoder := json.NewDecoder(r.Body)
		if err := decoder.Decode(&req); err != nil {
			log.WithContext(ctx).Errorf("[ListMastersDataReq] failed to decode request body. err: %v", err)
			errorHandler.CustomError(w, http.StatusBadRequest, "Invalid request format")
			return
		}
		defer r.Body.Close()

		// Validate required parameters
		if req.MastersCode == "" {
			errorHandler.CustomError(w, http.StatusBadRequest, "Masters code cannot be empty")
			return
		}

		// Validate pagination parameters
		if req.Limit <= 0 {
			req.Limit = 10 // Default limit
		}

		if req.Page <= 0 {
			req.Page = 1 // Default page
		}

		// Set attributes to be passed along in the context
		var attributes = make(map[string]interface{})
		attributes["req"] = req

		ctx = context.WithValue(ctx, "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func DownloadDocumentsReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()
		var req mdashboard.DownloadDocumentsParam

		// Parse JSON request body
		decoder := json.NewDecoder(r.Body)
		if err := decoder.Decode(&req); err != nil {
			log.WithContext(ctx).Errorf("[DownloadDocumentsReq] failed to decode request body. err: %v", err)
			errorHandler.CustomError(w, http.StatusBadRequest, "Invalid request format")
			return
		}
		defer r.Body.Close()

		// Validate required parameters
		if req.LoanApplicationID == "" {
			errorHandler.CustomError(w, http.StatusBadRequest, mdashboard.EmptyLoanApplicationID)
			return
		}

		// Validate UUID format
		if !general.ValidateUUID(req.LoanApplicationID) {
			errorHandler.CustomError(w, http.StatusBadRequest, mdashboard.InvalidLoanID)
			return
		}

		if req.UserID == "" {
			errorHandler.CustomError(w, http.StatusBadRequest, mdashboard.EmptyUserID)
			return
		}

		// Validate UUID format
		if !general.ValidateUUID(req.UserID) {
			errorHandler.CustomError(w, http.StatusBadRequest, mdashboard.InvalidUserID)
			return
		}

		// Validate that either categories or docIDs are provided
		if req.FilterCategories == nil && req.DocIDs == nil {
			errorHandler.CustomError(w, http.StatusBadRequest, "Either categories or document IDs must be provided")
			return
		}

		// Process docIDs if present
		if len(req.DocIDs) > 0 {
			// Validate each DocID UUID
			for _, docID := range req.DocIDs {
				if !general.ValidateUUID(docID) {
					errorHandler.CustomError(w, http.StatusBadRequest, "Invalid document ID format")
					return
				}
			}
		}

		// Set attributes to be passed along in the context
		var attributes = make(map[string]interface{})
		attributes["req"] = req

		ctx = context.WithValue(ctx, "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}
