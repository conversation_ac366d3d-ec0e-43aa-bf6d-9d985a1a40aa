package lender

import (
	"context"
	"encoding/csv"
	"encoding/json"
	"finbox/go-api/authentication"
	"finbox/go-api/conf"
	"finbox/go-api/constants"
	"finbox/go-api/controller/services"
	"finbox/go-api/errorHandler"
	"finbox/go-api/functions/journey"
	"finbox/go-api/functions/logger"
	"finbox/go-api/functions/structs"
	"finbox/go-api/infra/redis"
	"finbox/go-api/models/dashboardworkflow"
	lenderModel "finbox/go-api/models/lender"
	rbacErrors "finbox/go-api/rbac/errors"
	"finbox/go-api/requestHandler/requestCommon"
	"finbox/go-api/utils/apistack"
	"finbox/go-api/utils/general"
	"finbox/go-api/utils/kycutils"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"strings"
	"time"

	"finbox/go-api/models/coapplicantkycdetails"
	dashbaord "finbox/go-api/models/dashboard"
	rbacLenderTypes "finbox/go-api/models/lender/rbac"
	rbacConstants "finbox/go-api/rbac/constants"

	validator "github.com/go-playground/validator/v10"

	dashboardModel "finbox/go-api/models/dashboard"
)

var (
	log      = logger.Log
	recovery = requestCommon.Recovery
)

func LoginReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			Email    string `json:"email" validate:"required,min=5,max=100"`
			Password string `json:"password" validate:"required,min=6,max=48"`
		}

		v := validator.New()

		decoder := json.NewDecoder(r.Body)
		defer r.Body.Close()
		var reqObj requestStruct
		err := decoder.Decode(&reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		err = v.Struct(reqObj)
		if err != nil {
			panic(err)
		}

		attributes := map[string]interface{}{
			"email":    reqObj.Email,
			"password": reqObj.Password,
		}

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func SendLoginOTPOnEmailReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			Email string `json:"email" validate:"required"`
		}

		v := validator.New()

		decoder := json.NewDecoder(r.Body)
		defer func(Body io.ReadCloser) {
			err := Body.Close()
			if err != nil {
				log.Println(err)
			}
		}(r.Body)
		var reqObj requestStruct
		err := decoder.Decode(&reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		err = v.Struct(reqObj)
		if err != nil {
			panic(err)
		}

		if !general.ValidateEmail(reqObj.Email) {
			errorHandler.CustomError(w, http.StatusBadRequest, "Invalid Email")
			return
		}

		attributes := map[string]interface{}{
			"email": reqObj.Email,
		}

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func VerifyLoginOTPUsingEmailReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			Email string `json:"email" validate:"required"`
			OTP   string `json:"otp"  validate:"required,len=6,numeric"`
		}

		v := validator.New()

		decoder := json.NewDecoder(r.Body)
		defer func(Body io.ReadCloser) {
			err := Body.Close()
			if err != nil {
				log.Println(err)
			}
		}(r.Body)
		var reqObj requestStruct
		err := decoder.Decode(&reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		err = v.Struct(reqObj)
		if err != nil {
			panic(err)
		}

		if !general.ValidateEmail(reqObj.Email) {
			errorHandler.CustomError(w, http.StatusBadRequest, "Invalid Email")
			return
		}

		attributes := map[string]interface{}{
			"email": reqObj.Email,
			"otp":   reqObj.OTP,
		}

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func ApproveLoanReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			LoanApplicationID string  `json:"loanApplicationID" validate:"required"`
			Amount            float64 `json:"amount"`
		}

		v := validator.New()
		decoder := json.NewDecoder(r.Body)
		defer r.Body.Close()
		var reqObj requestStruct
		err := decoder.Decode(&reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		if reqObj.Amount < 0 {
			errorHandler.CustomError(w, http.StatusBadRequest, "amount cannot be negative")
			return
		}
		if reqObj.Amount > constants.MaxEligibleAmountBL { // assumption BL is always higher than PL
			errorHandler.CustomError(w, http.StatusBadRequest, "amount cannot exceed 30 lakhs")
			return
		}

		err = v.Struct(reqObj)
		if err != nil {
			panic(err)
		}
		attributes := map[string]interface{}{
			"loanApplicationID": reqObj.LoanApplicationID,
			"amount":            reqObj.Amount,
		}

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func UpdateProfileOTPReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			Mobile string `json:"mobile"`
		}

		decoder := json.NewDecoder(r.Body)
		defer func(Body io.ReadCloser) {
			err := Body.Close()
			if err != nil {
				log.Errorln(err)
			}
		}(r.Body)
		var reqObj requestStruct
		err := decoder.Decode(&reqObj)
		if err != nil {
			log.Errorln(err)
			panic(err)
		}

		if !general.ValidateMobile(reqObj.Mobile) {
			errorHandler.CustomError(w, http.StatusBadRequest, "invalid mobile")
			return
		}

		attributes := map[string]interface{}{
			"mobile": reqObj.Mobile,
		}

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func UpdateUserProfileReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			Name   string `json:"name"`
			Mobile string `json:"mobile"`
			OTP    string `json:"otp"`
		}

		decoder := json.NewDecoder(r.Body)
		defer func(Body io.ReadCloser) {
			err := Body.Close()
			if err != nil {
				log.Errorln(err)
			}
		}(r.Body)
		var reqObj requestStruct
		err := decoder.Decode(&reqObj)
		if err != nil {
			log.Errorln(err)
			panic(err)
		}

		attributes := make(map[string]interface{})

		reqObj.Name = general.RemoveExtraSpaces(reqObj.Name)
		if len(reqObj.Name) > 25 {
			errorHandler.CustomError(w, http.StatusBadRequest, "name is too long, permissible limit for name is 25")
			return
		}

		if len(reqObj.Mobile) != 0 && !general.ValidateMobile(reqObj.Mobile) {
			errorHandler.CustomError(w, http.StatusBadRequest, "invalid mobile")
			return
		}

		otp := 0
		if reqObj.Mobile != "" {
			if len(reqObj.OTP) != 6 {
				errorHandler.CustomError(w, http.StatusBadRequest, "invalid OTP")
				return
			}
			otp, err = strconv.Atoi(reqObj.OTP)
			if err != nil {
				errorHandler.CustomError(w, http.StatusBadRequest, "invalid OTP")
				return
			}
		}

		attributes["mobile"] = reqObj.Mobile
		attributes["name"] = reqObj.Name
		attributes["otp"] = otp

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func ResetExpiredPasswordReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)
		type requestStruct struct {
			Email       string `json:"email"`
			OldPassword string `json:"oldPassword"`
			NewPassword string `json:"newPassword"`
		}
		decoder := json.NewDecoder(r.Body)
		defer func(Body io.ReadCloser) {
			err := Body.Close()
			if err != nil {
				log.Errorln(err)
			}
		}(r.Body)
		var reqObj requestStruct
		err := decoder.Decode(&reqObj)
		if err != nil {
			log.Errorln(err)
			panic(err)
		}

		if !general.ValidateEmail(reqObj.Email) {
			errorHandler.CustomError(w, http.StatusBadRequest, "Invalid Email")
			return
		}

		if reqObj.OldPassword == "" {
			errorHandler.CustomError(w, http.StatusBadRequest, "old password is empty")
			return
		}

		if reqObj.NewPassword == "" {
			errorHandler.CustomError(w, http.StatusBadRequest, "new password is empty")
			return
		}

		attributes := map[string]interface{}{
			"email":       reqObj.Email,
			"oldPassword": reqObj.OldPassword,
			"newPassword": reqObj.NewPassword,
		}
		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func SendForgetPasswordLinkReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)
		type requestStruct struct {
			Email string `json:"email"`
		}
		decoder := json.NewDecoder(r.Body)
		defer func(Body io.ReadCloser) {
			err := Body.Close()
			if err != nil {
				log.Errorln(err)
			}
		}(r.Body)
		var reqObj requestStruct
		err := decoder.Decode(&reqObj)
		if err != nil {
			log.Errorln(err)
			panic(err)
		}

		if !general.ValidateEmail(reqObj.Email) {
			errorHandler.CustomError(w, http.StatusBadRequest, "Invalid Email")
			return
		}

		rootURL := general.RemoveExtraSpaces(r.Header.Get("Origin"))
		if len(rootURL) == 0 {
			errorHandler.CustomError(w, http.StatusBadRequest, constants.MessageAccessDeny)
			return
		}

		attributes := map[string]interface{}{
			"email":   reqObj.Email,
			"rootURL": rootURL,
		}
		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func SendChangePasswordLinkReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		rootURL := general.RemoveExtraSpaces(r.Header.Get("Origin"))
		if len(rootURL) == 0 {
			errorHandler.CustomError(w, http.StatusBadRequest, constants.MessageAccessDeny)
			return
		}

		attributes := map[string]interface{}{
			"rootURL": rootURL,
		}
		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func ResetUserPasswordReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)
		type requestStruct struct {
			Email    string `json:"email"`
			Password string `json:"password"`
		}
		decoder := json.NewDecoder(r.Body)
		defer func(Body io.ReadCloser) {
			err := Body.Close()
			if err != nil {
				log.Errorln(err)
			}
		}(r.Body)
		var reqObj requestStruct
		err := decoder.Decode(&reqObj)
		if err != nil {
			log.Errorln(err)
			panic(err)
		}

		reqObj.Email, err = general.AESCBCPKCS5Decryption(reqObj.Email, conf.ChangePasswordSecretKey)
		if err != nil {
			log.Errorln(err)
			panic(err)
		}

		if reqObj.Password == "" {
			errorHandler.CustomError(w, http.StatusBadRequest, "password is empty")
			return
		}

		// Password can only be reset, if reset password was sent
		exist, err := redis.Get(context.Background(), reqObj.Email+constants.LenderDashboardResetPasswordSuffix)
		if err != nil {
			if err == redis.Nil {
				errorHandler.CustomError(w, http.StatusBadRequest, "password link expired")
				return
			}
			log.Errorln(err)
			panic(err)
		}
		if len(exist) == 0 {
			errorHandler.CustomError(w, http.StatusBadRequest, "password link expired")
			return
		}

		attributes := map[string]interface{}{
			"email":    reqObj.Email,
			"password": reqObj.Password,
		}
		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func ChangePasswordReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		email := r.Context().Value("user").(authentication.LenderUserStruct).Email

		type requestStruct struct {
			Password string `json:"password"`
		}
		decoder := json.NewDecoder(r.Body)
		defer func(Body io.ReadCloser) {
			err := Body.Close()
			if err != nil {
				log.Errorln(err)
			}
		}(r.Body)
		var reqObj requestStruct
		err := decoder.Decode(&reqObj)
		if err != nil {
			log.Errorln(err)
			panic(err)
		}

		if reqObj.Password == "" {
			errorHandler.CustomError(w, http.StatusBadRequest, "password is empty")
			return
		}

		// Password can only be reset, if reset password was sent
		exist, err := redis.Get(context.Background(), email+constants.LenderDashboardResetPasswordSuffix)
		if err != nil {
			if err == redis.Nil {
				errorHandler.CustomError(w, http.StatusBadRequest, "password link expired")
				return
			}
			log.Errorln(err)
			panic(err)
		}
		if len(exist) == 0 {
			errorHandler.CustomError(w, http.StatusBadRequest, "password link expired")
			return
		}

		attributes := map[string]interface{}{
			"password": reqObj.Password,
		}
		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func LoanActivityReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			LoanApplicationID []string `json:"loanApplicationID" validate:"required"`
			UserID            []string `json:"userID"`
			Limit             []string `json:"limit"`
			Page              []string `json:"page"`
		}

		ctx := r.Context()

		bytes, err := json.Marshal(r.URL.Query())
		if err != nil {
			log.Println(err)
			panic(err)
		}

		var reqObj requestStruct
		err = json.Unmarshal(bytes, &reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		v := validator.New()

		err = v.Struct(reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		attributes := make(map[string]interface{})

		loanApplicationID := reqObj.LoanApplicationID[0]
		if !general.ValidateUUID(loanApplicationID) {
			logger.WithContext(ctx).Errorf("[LoanActivityReq] invalid loanApplicationID: %s", loanApplicationID)
			errorHandler.CustomError(w, http.StatusBadRequest, constants.InValidLoanApplication)
			return
		}
		attributes["loanApplicationID"] = loanApplicationID

		if len(reqObj.Limit) == 0 {
			attributes["limit"] = 10
		} else {
			limitValue, err := strconv.Atoi(reqObj.Limit[0])
			if err != nil {
				panic(err)
			}
			if limitValue <= 0 {
				panic("limit should be greater than 0")
			}
			attributes["limit"] = limitValue
		}

		if len(reqObj.Page) == 0 {
			attributes["page"] = 1
		} else {
			pageValue, err := strconv.Atoi(reqObj.Page[0])
			if err != nil {
				panic(err)
			}
			if pageValue <= 0 {
				panic("page should be greater than 0")
			}
			attributes["page"] = pageValue
		}

		if len(reqObj.UserID) == 0 {
			attributes["userID"] = ""
		} else {
			attributes["userID"] = reqObj.UserID[0]
		}

		ctx = context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func RejectLoanReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			LoanApplicationID string `json:"loanApplicationID" validate:"required"`
			RejectReason      string `json:"rejectReason" validate:"required"`
		}

		v := validator.New()
		decoder := json.NewDecoder(r.Body)
		defer r.Body.Close()
		var reqObj requestStruct
		err := decoder.Decode(&reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		err = v.Struct(reqObj)
		if err != nil {
			panic(err)
		}
		attributes := map[string]interface{}{
			"loanApplicationID": reqObj.LoanApplicationID,
			"rejectReason":      reqObj.RejectReason,
		}

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func GetLoanDetailsReqForMandate(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			LoanApplicationID []string `json:"loanApplicationID" validate:"required"`
			MandateType       []string `json:"mandateType"`
		}

		bytes, err := json.Marshal(r.URL.Query())
		if err != nil {
			log.Println(err)
			panic(err)
		}

		var reqObj requestStruct
		err = json.Unmarshal(bytes, &reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		v := validator.New()

		err = v.Struct(reqObj)
		if err != nil {
			panic(err)
		}

		attributes := make(map[string]string)
		attributes["loanApplicationID"] = reqObj.LoanApplicationID[0]
		if len(reqObj.MandateType[0]) > 0 {
			attributes["mandateType"] = reqObj.MandateType[0]
		} else {
			attributes["mandateType"] = ""
		}
		if !general.ValidateUUID(attributes["loanApplicationID"]) {
			errorHandler.CustomError(w, http.StatusBadRequest, "invalid loanApplicationID")
			return
		}

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func GetLoanDetailsReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			LoanApplicationID []string `json:"loanApplicationID"`
		}

		bytes, err := json.Marshal(r.URL.Query())
		if err != nil {
			log.Println(err)
			panic(err)
		}

		var reqObj requestStruct
		err = json.Unmarshal(bytes, &reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		v := validator.New()

		err = v.Struct(reqObj)
		if err != nil {
			panic(err)
		}

		attributes := make(map[string]string)
		attributes["loanApplicationID"] = reqObj.LoanApplicationID[0]

		if !general.ValidateUUID(attributes["loanApplicationID"]) {
			errorHandler.CustomError(w, http.StatusBadRequest, "invalid loanApplicationID")
			return
		}

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func GetUpdateDirectorReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)
		ctx := r.Context()
		var (
			attributes = make(map[string]interface{})
			req        lenderModel.UpdateDirectorRequest
		)

		err := json.NewDecoder(r.Body).Decode(&req)
		if err != nil {
			log.WithContext(ctx).Errorf("[UpdateCoApplicant] failed to parse req. error: %v", err)
			errorHandler.CustomError(w, http.StatusBadRequest, "failed to parse request")
			return
		}

		if len(strings.TrimSpace(req.LoanApplicationID)) == 0 {
			errorHandler.CustomError(w, http.StatusBadRequest, "loanApplicationID cannot be empty")
			return
		}

		if len(strings.TrimSpace(req.UserID)) == 0 {
			errorHandler.CustomError(w, http.StatusBadRequest, "userID cannot be empty")
			return
		}

		if len(strings.TrimSpace(req.Name)) == 0 {
			errorHandler.CustomError(w, http.StatusBadRequest, "name cannot be empty")
			return
		}
		req.Name = general.RemoveExtraSpaces(req.Name)

		if len(strings.TrimSpace(req.AddressLine1)) == 0 {
			errorHandler.CustomError(w, http.StatusBadRequest, "address1 line cannot be empty")
			return
		}

		attributes["req"] = req

		ctx = context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))

	})
}

func GetReferenceDataReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		type requestStruct struct {
			Key []string `json:"key" validate:"required"`
		}

		bytes, err := json.Marshal(r.URL.Query())
		if err != nil {
			logger.Log.WithContext(ctx).Errorf("[GetReferenceDataReq] failed to parse request. err: %v", err)
			errorHandler.CustomError(w, http.StatusBadRequest, "failed to parse request")
			return
		}

		var reqObj requestStruct
		err = json.Unmarshal(bytes, &reqObj)
		if err != nil {
			logger.Log.WithContext(ctx).Errorf("[GetReferenceDataReq] failed to parse request. err: %v", err)
			errorHandler.CustomError(w, http.StatusBadRequest, "failed to parse request")
			return
		}
		v := validator.New()

		err = v.Struct(reqObj)
		if err != nil {
			logger.Log.WithContext(ctx).Errorf("[GetReferenceDataReq] failed to parse request. err: %v", err)
			errorHandler.CustomError(w, http.StatusBadRequest, "failed to parse request")
			return
		}

		attributes := make(map[string]string)
		attributes["key"] = reqObj.Key[0]

		if !apistack.IsValidKey(&reqObj.Key[0]) {
			errorHandler.CustomErrorV3(w, constants.ErrStringToStatusCodeMapping[constants.ErrStringInvalidReferenceDataKey], constants.ErrStringToStatusCodeStringMapping[constants.ErrStringInvalidReferenceDataKey], constants.ErrStringInvalidReferenceDataKey)
			return
		}

		ctx = context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func GetBREWFTimelineReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			ServiceRequestID []string `json:"serviceRequestID"`
		}

		bytes, err := json.Marshal(r.URL.Query())
		if err != nil {
			log.Println(err)
			panic(err)
		}

		var reqObj requestStruct
		err = json.Unmarshal(bytes, &reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		attributes := make(map[string]string)
		attributes["serviceRequestID"] = reqObj.ServiceRequestID[0]

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func ListAppliedTagRequest(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		loanApplicationID := r.URL.Query().Get("loanApplicationID")

		userObj := r.Context().Value("user")
		lenderID := userObj.(authentication.LenderUserStruct).LenderID

		if !journey.AllowTagsOnLender(lenderID, "", "") {
			panic("Invalid lender id")
		}

		attributes := make(map[string]string)
		attributes["loanApplicationID"] = loanApplicationID

		if !general.ValidateUUID(attributes["loanApplicationID"]) {
			errorHandler.CustomError(w, http.StatusBadRequest, "invalid loanApplicationID")
			return
		}

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func ApplyTagRequest(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		type requestStruct struct {
			LoanApplicationID string `json:"loanApplicationID" validate:"required,uuid"`
			TagValueID        string `json:"tagValueID" validate:"required"`
			TagValueName      string `json:"tagValueName" validate:"required"`
			TagCategoryName   string `json:"tagCategoryName" validate:"required"`
		}

		v := validator.New()
		decoder := json.NewDecoder(r.Body)
		defer r.Body.Close()
		var reqObj requestStruct
		err := decoder.Decode(&reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}
		err = v.Struct(reqObj)
		if err != nil {
			panic(err)
		}

		attributes := map[string]string{
			"loanApplicationID": reqObj.LoanApplicationID,
			"tagValueID":        reqObj.TagValueID,
			"tagValueName":      reqObj.TagValueName,
			"tagCategoryName":   reqObj.TagCategoryName,
		}

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func RemoveTagRequest(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// camel case argument.
		type requestStruct struct {
			LoanApplicationID string `json:"loanApplicationID" validate:"required,uuid"`
			TagValueID        string `json:"tagValueID" validate:"required"`
			TagValueName      string `json:"tagValueName" validate:"required"`
			TagCategoryName   string `json:"tagCategoryName" validate:"required"`
		}

		v := validator.New()
		decoder := json.NewDecoder(r.Body)
		defer r.Body.Close()
		var reqObj requestStruct
		err := decoder.Decode(&reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}
		err = v.Struct(reqObj)
		if err != nil {
			panic(err)
		}

		attributes := map[string]string{
			"loanApplicationID": reqObj.LoanApplicationID,
			"tagValueID":        reqObj.TagValueID,
			"tagValueName":      reqObj.TagValueName,
			"tagCategoryName":   reqObj.TagCategoryName,
		}

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func GetLoanListReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			Limit             []string `json:"limit"`
			Page              []string `json:"page"`
			FromDate          []string `json:"from"`
			ToDate            []string `json:"to"`
			UniqueID          []string `json:"customerID"`
			Name              []string `json:"name"`
			PAN               []string `json:"pan"`
			Mobile            []string `json:"mobile"`
			StatusText        []string `json:"statusText"`
			LoanApplicationNo []string `json:"loanApplicationNum"`
			LoanType          []string `json:"loanType"`
			Source            []string `json:"source"`
			PartnerCode       []string `json:"partnerCode"`
			Email             []string `json:"email"`
			FirmName          []string `json:"firmName"`
			Filter            []string `json:"filter"`
			Tag               []string `json:"tag"`
			SourceEntityIDs   []string `json:"sourceEntityIDs"`
			WorkflowFilter    []string `json:"workflowFilter"`
			ReviewStatus      []string `json:"reviewStatus"`
			WorkFlowGroups    []string `json:"workflowGroups"`
			Schemes           []string `json:"schemes"`
			Segments          []string `json:"segments"`
			SoftApproved      []string `json:"softApproved"`
		}

		bytes, err := json.Marshal(r.URL.Query())
		if err != nil {
			log.Println(err)
			panic(err)
		}

		var reqObj requestStruct
		err = json.Unmarshal(bytes, &reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		v := validator.New()
		err = v.Struct(reqObj)
		if err != nil {
			panic(err)
		}

		attributes := make(map[string]interface{})

		if len(reqObj.UniqueID) > 0 {
			attributes["uniqueID"] = reqObj.UniqueID[0]
		} else {
			attributes["uniqueID"] = ""
		}
		if len(reqObj.ReviewStatus) > 0 {
			reviewStatus, err := strconv.Atoi(reqObj.ReviewStatus[0])
			if err != nil {
				panic(err)
			}
			attributes["reviewStatus"] = reviewStatus
		}
		if len(reqObj.Mobile) > 0 {
			attributes["mobile"] = reqObj.Mobile[0]
		} else {
			attributes["mobile"] = ""
		}
		if len(reqObj.PAN) > 0 {
			attributes["pan"] = reqObj.PAN[0]
		} else {
			attributes["pan"] = ""
		}
		if len(reqObj.Tag) > 0 {
			attributes["tag"] = reqObj.Tag[0]
		} else {
			attributes["tag"] = ""
		}
		if len(reqObj.Name) > 0 {
			attributes["name"] = reqObj.Name[0]
		} else {
			attributes["name"] = ""
		}
		if len(reqObj.FirmName) > 0 {
			attributes["firmName"] = reqObj.FirmName[0]
		} else {
			attributes["firmName"] = ""
		}
		if len(reqObj.LoanApplicationNo) > 0 {
			attributes["loanApplicationNo"] = reqObj.LoanApplicationNo[0]
		} else {
			attributes["loanApplicationNo"] = ""
		}
		if len(reqObj.Source) > 0 {
			attributes["source"] = reqObj.Source[0]
		} else {
			attributes["source"] = ""
		}
		if len(reqObj.PartnerCode) > 0 {
			attributes["partnerCode"] = reqObj.PartnerCode[0]
		} else {
			attributes["partnerCode"] = ""
		}
		if len(reqObj.Email) > 0 {
			attributes["email"] = reqObj.Email[0]
		} else {
			attributes["email"] = ""
		}

		if len(reqObj.SoftApproved) > 0 {
			if reqObj.SoftApproved[0] == "1" {
				attributes["softApproved"] = true
			} else {
				attributes["softApproved"] = false
			}
		}

		if len(reqObj.Limit) == 0 {
			attributes["limit"] = 10
		} else {
			limitValue, err := strconv.Atoi(reqObj.Limit[0])
			if err != nil {
				panic(err)
			}
			if limitValue <= 0 {
				panic("limit should be greater than 0")
			}
			attributes["limit"] = limitValue
		}

		if len(reqObj.Page) == 0 {
			attributes["page"] = 1
		} else {
			pageValue, err := strconv.Atoi(reqObj.Page[0])
			if err != nil {
				panic(err)
			}
			if pageValue <= 0 {
				panic("page should be greater than 0")
			}
			attributes["page"] = pageValue
		}

		if len(reqObj.WorkflowFilter) > 0 {
			for _, values := range reqObj.WorkflowFilter {
				if !general.InArr(values, constants.AllowedWFFilterTypes) {
					errorHandler.CustomError(w, http.StatusBadRequest, "invalid filter type")
					return
				}
			}
			attributes["workflowFilter"] = reqObj.WorkflowFilter
		}
		if len(reqObj.Schemes) > 0 {
			var installmentProgramme []string
			for _, scheme := range reqObj.Schemes {
				if !general.InArr(scheme, constants.AllowedSchemeValues) {
					errorHandler.CustomError(w, http.StatusBadRequest, "invalid scheme type")
					return
				}
				installmentProgramme = append(installmentProgramme, constants.MFLBLSchemeToInstallmentType[scheme])
			}
			attributes["installmentProgramme"] = installmentProgramme
		}

		if len(reqObj.Segments) > 0 {
			for _, values := range reqObj.Segments {
				if !general.InArr(values, constants.AllowedSegmentTypes) {
					errorHandler.CustomError(w, http.StatusBadRequest, "invalid segment type")
					return
				}
			}
			attributes["segments"] = reqObj.Segments
		}

		if len(reqObj.WorkFlowGroups) > 0 {
			attributes["workflowGroups"] = reqObj.WorkFlowGroups
		}

		statusList := [][]int{}
		for _, statusText := range reqObj.StatusText {
			statusObj, found := constants.LoanStatusStrToNum[statusText]
			if !found {
				panic("Invalid status value " + statusText)
			}

			statusList = append(statusList, []int{statusObj.Status, statusObj.KYCStatus})
		}

		attributes["statusList"] = statusList

		if len(reqObj.LoanType) > 0 {
			finalLoanTypes := []string{}
			for _, loanType := range reqObj.LoanType {
				if general.InArr(loanType, constants.ValidLoanTypeList) {
					finalLoanTypes = append(finalLoanTypes, loanType)
				}
			}
			attributes["loanType"] = finalLoanTypes
		}

		// parse dates if they exists
		if !(len(reqObj.FromDate) == 0 && len(reqObj.ToDate) == 0) {

			_, err := time.Parse(constants.DateFormat, reqObj.ToDate[0])
			if err != nil {
				log.Println("Invalid `to` date")
				panic("Invalid `to` date")
			}

			_, err = time.Parse(constants.DateFormat, reqObj.FromDate[0])
			if err != nil {
				log.Println("Invalid `from` date")
				panic("Invalid `from` date")
			}

			// panic if from is ahead of to
			if reqObj.ToDate[0] < reqObj.FromDate[0] {
				log.Println("Invalid `from` or `to` date")
				panic("Invalid `from` or `to` date")
			}

			attributes["to"] = reqObj.ToDate[0]
			attributes["from"] = reqObj.FromDate[0]
		}

		filter := ""
		if len(reqObj.Filter) != 0 {
			filter = reqObj.Filter[0]
		}
		switch filter {
		case "created_at":
			attributes["filter"] = "created_at"
		case "disbursed_date":
			attributes["filter"] = "disbursed_date"
		default:
			attributes["filter"] = "updated_at"
		}
		if len(reqObj.SourceEntityIDs) > 0 {
			attributes["sourceEntityIDList"] = reqObj.SourceEntityIDs
		}
		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func LimitReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			Limit []string `json:"limit"`
			Page  []string `json:"page"`
		}

		bytes, err := json.Marshal(r.URL.Query())
		if err != nil {
			log.Println(err)
			panic(err)
		}

		var reqObj requestStruct
		err = json.Unmarshal(bytes, &reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		v := validator.New()

		err = v.Struct(reqObj)
		if err != nil {
			panic(err)
		}

		attributes := make(map[string]interface{})

		if len(reqObj.Limit) == 0 {
			attributes["limit"] = 10
		} else {
			limitValue, err := strconv.Atoi(reqObj.Limit[0])
			if err != nil {
				panic(err)
			}
			if limitValue <= 0 {
				panic("limit should be greater than 0")
			}
			attributes["limit"] = limitValue
		}

		if len(reqObj.Page) == 0 {
			attributes["page"] = 1
		} else {
			pageValue, err := strconv.Atoi(reqObj.Page[0])
			if err != nil {
				panic(err)
			}
			if pageValue <= 0 {
				panic("page should be greater than 0")
			}
			attributes["page"] = pageValue
		}

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func DisburseLoanReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			LoanApplicationID string `json:"loanApplicationID"`
			LenderTxnNum      string `json:"lenderTxnNum"`
			ForceUpdate       bool   `json:"forceUpdate"`
			DisbursedDate     string `json:"disbursedDate"`
		}

		decoder := json.NewDecoder(r.Body)
		defer r.Body.Close()
		var reqObj requestStruct
		err := decoder.Decode(&reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		if !general.ValidateUUID(reqObj.LoanApplicationID) {
			errorHandler.CustomError(w, http.StatusBadRequest, "invalid loanApplicationID")
			return
		}

		reqObj.LenderTxnNum = strings.TrimSpace(reqObj.LenderTxnNum)
		if reqObj.LenderTxnNum == "" {
			errorHandler.CustomError(w, http.StatusBadRequest, "UTR should be present")
			return
		}

		if len(reqObj.LenderTxnNum) > 100 {
			errorHandler.CustomError(w, http.StatusBadRequest, "UTR cannot be greater than 100 characters")
			return
		}

		attributes := map[string]interface{}{
			"loanApplicationID": reqObj.LoanApplicationID,
			"lenderTxnNum":      reqObj.LenderTxnNum,
			"forceUpdate":       reqObj.ForceUpdate,
			"disbursedDate":     reqObj.DisbursedDate,
		}

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func ApproveCreditLineReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			LoanApplicationID string  `json:"loanApplicationID" validate:"required"`
			Limit             float64 `json:"limit"`
			DisbursalDate     string  `json:"disbursalDate"`
		}

		v := validator.New()
		decoder := json.NewDecoder(r.Body)
		defer r.Body.Close()
		var reqObj requestStruct
		err := decoder.Decode(&reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		err = v.Struct(reqObj)
		if err != nil {
			panic(err)
		}

		if reqObj.Limit < 0 {
			log.Println("limit should be > 0")
			panic("limit should be > 0")
		}

		if reqObj.Limit > constants.MaxCreditLineLimit {
			log.Println("limit cannot exceed 5 lakhs")
			panic("limit cannot exceed 5 lakhs")
		}

		if reqObj.DisbursalDate != "" {
			loc, err := time.LoadLocation("Asia/Calcutta")
			if err != nil {
				log.Error(err)
				panic(err)
			}
			dateObj, err := time.ParseInLocation(constants.DateFormat, reqObj.DisbursalDate, loc)
			if err != nil {
				log.Warn(err)
				errorHandler.CustomError(w, http.StatusBadRequest, "invalid date format for disbursalDate, must be YYYY-MM-DD")
				return
			}
			if dateObj.After(time.Now().In(loc)) {
				errorHandler.CustomError(w, http.StatusBadRequest, "disbursalDate cannot be a future date")
				return
			}
		}

		attributes := map[string]interface{}{
			"loanApplicationID": reqObj.LoanApplicationID,
			"limit":             reqObj.Limit,
			"disbursalDate":     reqObj.DisbursalDate,
		}

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func RegisterLoanRepaymentReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			LoanApplicationID string  `json:"loanApplicationID" validate:"required"`
			InstallmentNum    int     `json:"installmentNum" validate:"required"`
			Source            string  `json:"source" validate:"required"`
			TransactionID     string  `json:"transactionID" validate:"required"`
			PaymentDate       string  `json:"paymentDate" validate:"required"`
			AmountReceived    float64 `json:"amountReceived" validate:"required"`
		}

		v := validator.New()
		decoder := json.NewDecoder(r.Body)
		defer r.Body.Close()
		var reqObj requestStruct
		err := decoder.Decode(&reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		err = v.Struct(reqObj)
		if err != nil {
			panic(err)
		}

		_, err = time.Parse("2006-01-02", reqObj.PaymentDate)
		if err != nil {
			errorHandler.CustomError(w, http.StatusBadRequest, "paymentDate must be in YYYY-MM-DD format")
			return
		}

		attributes := make(map[string]interface{})
		attributes["loanApplicationID"] = reqObj.LoanApplicationID
		attributes["installmentNum"] = reqObj.InstallmentNum
		attributes["source"] = reqObj.Source
		attributes["transactionID"] = reqObj.TransactionID
		attributes["paymentDate"] = reqObj.PaymentDate
		attributes["amountReceived"] = reqObj.AmountReceived

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func LoanDateRangeDumpReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			FromDate        []string `json:"from"`
			ToDate          []string `json:"to"`
			Filter          []string `json:"filter"`
			StatusText      []string `json:"statusText"`
			SourceEntityIDs []string `json:"sourceEntityIDs"`
			Source          []string `json:"source"`
			LoanType        []string `json:"loanType"`
			Segments        []string `json:"segments"`
		}

		bytes, err := json.Marshal(r.URL.Query())
		if err != nil {
			log.Println(err)
			panic(err)
		}

		var reqObj requestStruct
		err = json.Unmarshal(bytes, &reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		v := validator.New()

		err = v.Struct(reqObj)
		if err != nil {
			panic(err)
		}

		attributes := make(map[string]interface{})

		if !(len(reqObj.FromDate) == 0 && len(reqObj.ToDate) == 0) {

			_, err := time.Parse(constants.DateFormat, reqObj.ToDate[0])
			if err != nil {
				log.Println("Invalid `to` date")
				panic("Invalid `to` date")
			}

			_, err = time.Parse(constants.DateFormat, reqObj.FromDate[0])
			if err != nil {
				log.Println("Invalid `from` date")
				panic("Invalid `from` date")
			}

			// panic if from is ahead of to
			if reqObj.ToDate[0] < reqObj.FromDate[0] {
				log.Println("Invalid `from` or `to` date")
				panic("Invalid `from` or `to` date")
			}

			attributes["to"] = reqObj.ToDate[0]
			attributes["from"] = reqObj.FromDate[0]
		} else {
			panic("Invalid `from` or `to` date")
		}

		filter := ""
		if len(reqObj.Filter) != 0 {
			filter = reqObj.Filter[0]
		}
		switch filter {
		case "disbursed_date":
			attributes["filter"] = "disbursed_date"
		case "updated_at":
			attributes["filter"] = "updated_at"
		default:
			attributes["filter"] = "created_at"
		}

		attributes["source"] = ""
		if len(reqObj.Source) > 0 {
			attributes["source"] = reqObj.Source[0]
		}

		statusList := [][]int{}
		for _, statusText := range reqObj.StatusText {
			statusObj, found := constants.LoanStatusStrToNum[statusText]
			if !found {
				panic("Invalid status text")
			}
			statusList = append(statusList, []int{statusObj.Status, statusObj.KYCStatus})
		}

		if len(reqObj.Segments) > 0 {
			for _, values := range reqObj.Segments {
				if !general.InArr(values, constants.AllowedSegmentTypes) {
					errorHandler.CustomError(w, http.StatusBadRequest, "invalid segment type")
					return
				}
			}
			attributes["segments"] = reqObj.Segments
		}

		for _, loanType := range reqObj.LoanType {
			if !general.InArr(loanType, constants.ValidLoanTypeList) {
				panic("Invalid loan type")
			}
		}
		attributes["statusList"] = statusList
		attributes["loanTypeList"] = reqObj.LoanType
		attributes["sourceEntityIDs"] = reqObj.SourceEntityIDs

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func CollectionDumpReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			FromDate []string `json:"from"`
			ToDate   []string `json:"to"`
			Filter   []string `json:"filter"`
			Source   []string `json:"source"`
			LoanType []string `json:"loanType"`
		}

		bytes, err := json.Marshal(r.URL.Query())
		if err != nil {
			log.Println(err)
			panic(err)
		}

		var reqObj requestStruct
		err = json.Unmarshal(bytes, &reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		v := validator.New()

		err = v.Struct(reqObj)
		if err != nil {
			panic(err)
		}

		attributes := make(map[string]interface{})

		if !(len(reqObj.FromDate) == 0 && len(reqObj.ToDate) == 0) {

			_, err := time.Parse(constants.DateFormat, reqObj.ToDate[0])
			if err != nil {
				log.Println("Invalid `to` date")
				panic("Invalid `to` date")
			}

			_, err = time.Parse(constants.DateFormat, reqObj.FromDate[0])
			if err != nil {
				log.Println("Invalid `from` date")
				panic("Invalid `from` date")
			}

			// panic if from is ahead of to
			if reqObj.ToDate[0] < reqObj.FromDate[0] {
				log.Println("Invalid `from` or `to` date")
				panic("Invalid `from` or `to` date")
			}

			attributes["to"] = reqObj.ToDate[0]
			attributes["from"] = reqObj.FromDate[0]
		} else {
			panic("Invalid `from` or `to` date")
		}

		filter := ""
		if len(reqObj.Filter) != 0 {
			filter = reqObj.Filter[0]
		}
		switch filter {
		case "updated_at":
			attributes["filter"] = "updated_at"
		case "payment_date":
			attributes["filter"] = "payment_date"
		default:
			attributes["filter"] = "due_date"
		}

		attributes["source"] = ""
		if len(reqObj.Source) > 0 {
			attributes["source"] = reqObj.Source[0]
		}

		if len(reqObj.LoanType) == 0 {
			panic("Missing loan type")
		}
		if !general.InArr(reqObj.LoanType[0], constants.ValidLoanTypeList) {
			panic("Invalid loan type")
		}
		attributes["loanType"] = reqObj.LoanType[0]

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func DisburseCreditLineTxnReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			LoanApplicationID string `json:"loanApplicationID" validate:"required"`
			TxnID             string `json:"txnID" validate:"required"`
			LenderTxnNum      string `json:"lenderTxnNum" validate:"required"`
			DisbursalDate     string `json:"disbursalDate"`
		}

		v := validator.New()
		decoder := json.NewDecoder(r.Body)
		defer r.Body.Close()
		var reqObj requestStruct
		err := decoder.Decode(&reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		err = v.Struct(reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		if reqObj.DisbursalDate != "" {
			_, err = time.Parse("2006-01-02", reqObj.DisbursalDate)
			if err != nil {
				errorHandler.CustomError(w, http.StatusBadRequest, "disbursalDate should be in YYYY-MM-DD format")
				return
			}
		}

		attributes := map[string]string{
			"loanApplicationID": reqObj.LoanApplicationID,
			"txnID":             reqObj.TxnID,
			"lenderTxnNum":      reqObj.LenderTxnNum,
			"disbursalDate":     reqObj.DisbursalDate,
		}

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func RaiseQueryReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			TxnID string `json:"txnID" validate:"required"`
			Query string `json:"query" validate:"required"`
		}

		v := validator.New()
		decoder := json.NewDecoder(r.Body)
		defer r.Body.Close()
		var reqObj requestStruct
		err := decoder.Decode(&reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		err = v.Struct(reqObj)
		if err != nil {
			panic(err)
		}
		attributes := map[string]string{
			"txnID": reqObj.TxnID,
			"query": reqObj.Query,
		}

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func UpdateCreditLineStatusReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			LoanApplicationID string `json:"loanApplicationID" validate:"required"`
			ActiveStatus      bool   `json:"activeStatus"`
			InactiveReason    string `json:"inactiveReason"`
		}

		v := validator.New()
		decoder := json.NewDecoder(r.Body)
		defer r.Body.Close()
		var reqObj requestStruct
		err := decoder.Decode(&reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		err = v.Struct(reqObj)
		if err != nil {
			panic(err)
		}
		attributes := make(map[string]interface{})
		attributes["loanApplicationID"] = reqObj.LoanApplicationID
		attributes["activeStatus"] = reqObj.ActiveStatus
		attributes["inactiveReason"] = reqObj.InactiveReason

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func AssistedReviewReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			LoanApplicationID string  `json:"loanApplicationID" validate:"required"`
			Status            string  `json:"status" validate:"required"`
			Remark            string  `json:"remark"`
			CustomLimit       float64 `json:"customLimit"`
		}

		v := validator.New()
		decoder := json.NewDecoder(r.Body)
		defer r.Body.Close()
		var reqObj requestStruct
		err := decoder.Decode(&reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}
		err = v.Struct(reqObj)
		if err != nil {
			panic(err)
		}

		var actionType string
		switch reqObj.Status {
		case constants.AssistedJourneyReviewApprove:
			if reqObj.Remark != "" {
				errorHandler.CustomError(w, http.StatusBadRequest, "remark should be empty in case of approve status")
				return
			}
		case constants.AssistedJourneyReviewRequest:
			if reqObj.Remark == "" {
				errorHandler.CustomError(w, http.StatusBadRequest, "remark cannot be empty in case of request status")
				return
			}
			actionType = constants.AssistedJourneyActionDataModal
		}

		attributes := map[string]interface{}{
			"loanApplicationID": reqObj.LoanApplicationID,
			"status":            reqObj.Status,
			"remark":            reqObj.Remark,
			"actionType":        actionType,
			"customLimit":       reqObj.CustomLimit,
		}

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func UpdateCurrentAddressReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			LoanApplicationID         string `json:"loanApplicationID"`
			Line1                     string `json:"line1"`
			Line2                     string `json:"line2"`
			City                      string `json:"city"`
			State                     string `json:"state"`
			Area                      string `json:"area"`
			Landmark                  string `json:"landmark"`
			Pincode                   string `json:"pincode"`
			IsCurrentAndPermanentSame *bool  `json:"isCurrentAndPermanentSame"`
		}

		decoder := json.NewDecoder(r.Body)
		defer r.Body.Close()
		var reqObj requestStruct
		err := decoder.Decode(&reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		if !general.ValidateUUID(reqObj.LoanApplicationID) {
			errorHandler.CustomError(w, http.StatusBadRequest, "invalid loan application id")
			return
		}

		reqObj.Line1, reqObj.Line2, reqObj.City, reqObj.State, reqObj.Pincode, err = kycutils.ValidateAddress(reqObj.Line1, reqObj.Line2, reqObj.City, reqObj.State, reqObj.Pincode)
		if err != nil {
			errorHandler.CustomError(w, http.StatusBadRequest, err.Error())
			return
		}

		reqObj.Area = strings.TrimSpace(reqObj.Area)
		reqObj.Landmark = strings.TrimSpace(reqObj.Landmark)

		defaultValue := true
		if reqObj.IsCurrentAndPermanentSame == nil {
			reqObj.IsCurrentAndPermanentSame = &defaultValue
		}

		attributes := map[string]interface{}{
			"line1":                     reqObj.Line1,
			"line2":                     reqObj.Line2,
			"city":                      reqObj.City,
			"state":                     reqObj.State,
			"pincode":                   reqObj.Pincode,
			"area":                      reqObj.Area,
			"landmark":                  reqObj.Landmark,
			"loanApplicationID":         reqObj.LoanApplicationID,
			"isCurrentAndPermanentSame": *reqObj.IsCurrentAndPermanentSame,
		}

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func UpdateDOBReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			LoanApplicationID string `json:"loanApplicationID" validate:"required"`
			DOB               string `json:"dob" validate:"required"`
		}

		v := validator.New()

		decoder := json.NewDecoder(r.Body)
		defer r.Body.Close()
		var reqObj requestStruct
		err := decoder.Decode(&reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		err = v.Struct(reqObj)
		if err != nil {
			panic(err)
		}

		_, err = time.Parse("2006-01-02", reqObj.DOB)
		if err != nil {
			errorHandler.CustomError(w, http.StatusBadRequest, "dob should be in YYYY-MM-DD format")
			return
		}

		attributes := map[string]interface{}{
			"dob":               reqObj.DOB,
			"loanApplicationID": reqObj.LoanApplicationID,
		}

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func RegenerateAgreementReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			LoanApplicationID          []string `json:"loanApplicationID" validate:"required"`
			SignAsOfNow                []string `json:"signAsOfNow"`
			Name                       []string `json:"name"`
			ForceUpdateNameInAgreement []string `json:"forceUpdateNameInAgreement"`
		}

		bytes, err := json.Marshal(r.URL.Query())
		if err != nil {
			logger.WithRequest(r).Println(err)
			panic(err)
		}

		var reqObj requestStruct
		err = json.Unmarshal(bytes, &reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		v := validator.New()

		err = v.Struct(reqObj)
		if err != nil {
			panic(err)
		}

		attributes := make(map[string]string)
		attributes["loanApplicationID"] = reqObj.LoanApplicationID[0]
		attributes["name"] = ""
		if len(reqObj.SignAsOfNow) > 0 {
			if reqObj.SignAsOfNow[0] != "Y" && reqObj.SignAsOfNow[0] != "N" {
				errorHandler.CustomError(w, http.StatusBadRequest, "invalid signAsOfNow value")
				return
			}
			attributes["signAsOfNow"] = reqObj.SignAsOfNow[0]
		} else {
			errorHandler.CustomError(w, http.StatusBadRequest, "missing signAsOfNow")
			return
		}

		if len(reqObj.ForceUpdateNameInAgreement) > 0 && reqObj.ForceUpdateNameInAgreement[0] == "Y" {
			if len(reqObj.Name) == 0 {
				errorHandler.CustomError(w, http.StatusBadRequest, "name required")
				return
			}
			attributes["name"] = general.RemoveExtraSpaces(reqObj.Name[0])
			attributes["forceUpdateNameInAgreement"] = reqObj.ForceUpdateNameInAgreement[0]
		} else {
			attributes["name"] = ""
			attributes["forceUpdateNameInAgreement"] = "N"

		}
		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func UpdateBusinessDetailsReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			UserID              string `json:"userID" validate:"required"`
			FirmName            string `json:"firmName" validate:"required"`
			Constitution        string `json:"constitution"`
			BusinessPAN         string `json:"businessPAN"`
			Last12MonthsSale    string `json:"last12MonthsSale"`
			DateOfIncorporation string `json:"dateOfIncorporation" validate:"required"`
		}
		v := validator.New()
		decoder := json.NewDecoder(r.Body)
		defer r.Body.Close()
		var reqObj requestStruct
		err := decoder.Decode(&reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}
		err = v.Struct(reqObj)
		if err != nil {
			panic(err)
		}

		userObj := r.Context().Value("user")
		lenderID := userObj.(authentication.LenderUserStruct).LenderID

		if reqObj.BusinessPAN != "" && !general.ValidateNonPersonalPAN(reqObj.BusinessPAN) {
			errorHandler.CustomError(w, http.StatusBadRequest, "invalid businessPAN")
			return
		}
		if !general.InArr(lenderID, []string{constants.MuthootCLID, constants.ABFLID}) && !general.InArr(reqObj.Constitution, constants.ConstitutionList) {
			errorHandler.CustomError(w, http.StatusBadRequest, "invalid constitution, possible values: "+strings.Join(constants.ConstitutionList, ","))
			return
		}

		// validation on last 12 months sale
		if !general.InArr(lenderID, []string{constants.MuthootCLID, constants.ABFLID}) && !general.ValidateSalesRange(reqObj.Last12MonthsSale) {
			errorHandler.CustomError(w, http.StatusBadRequest, "invalid last 12 months sale submitted")
			return
		}

		attributes := make(map[string]interface{})
		attributes["userID"] = reqObj.UserID
		attributes["firmName"] = reqObj.FirmName
		attributes["constitution"] = reqObj.Constitution
		attributes["businessPAN"] = reqObj.BusinessPAN
		attributes["last12MonthsSale"] = reqObj.Last12MonthsSale
		attributes["dateOfIncorporation"] = reqObj.DateOfIncorporation

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func UpdateBusinessAddressReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			UserID              string `json:"userID"`
			Line1               string `json:"line1"`
			Line2               string `json:"line2"`
			City                string `json:"city"`
			State               string `json:"state"`
			Pincode             string `json:"pincode"`
			Area                string `json:"area"`
			BusinessAddressType string `json:"businessAddressType"`
			NearestBranch       string `json:"nearestBranch,omitempty"`
			BranchCode          string `json:"branchCode,omitempty"`
		}

		decoder := json.NewDecoder(r.Body)
		defer r.Body.Close()
		var reqObj requestStruct
		err := decoder.Decode(&reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		if !general.ValidateUUID(reqObj.UserID) {
			errorHandler.CustomError(w, http.StatusBadRequest, "invalid userID")
			return
		}

		reqObj.Line1, reqObj.Line2, reqObj.City, reqObj.State, reqObj.Pincode, err = kycutils.ValidateAddress(reqObj.Line1, reqObj.Line2, reqObj.City, reqObj.State, reqObj.Pincode)
		if err != nil {
			errorHandler.CustomError(w, http.StatusBadRequest, err.Error())
			return
		}

		reqObj.Area = strings.TrimSpace(reqObj.Area)

		businessAddressType, success := constants.ResidenceStrToNum[reqObj.BusinessAddressType]
		if !success {
			errorHandler.CustomError(w, http.StatusBadRequest, "invalid businessAddressType")
			return
		}

		attributes := map[string]interface{}{
			"userID":              reqObj.UserID,
			"line1":               reqObj.Line1,
			"line2":               reqObj.Line2,
			"city":                reqObj.City,
			"state":               reqObj.State,
			"pincode":             reqObj.Pincode,
			"businessAddressType": businessAddressType,
			"area":                reqObj.Area,
			"nearestBranch":       reqObj.NearestBranch,
			"branchCode":          reqObj.BranchCode,
		}

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func TransactionListReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			Limit             []string `json:"limit"`
			Page              []string `json:"page"`
			FromDate          []string `json:"from"`
			ToDate            []string `json:"to"`
			UtrNo             []string `json:"utrNo"`
			TxnID             []string `json:"txnID"`
			PartnerTxnID      []string `json:"partnerTxnID"`
			StatusText        []string `json:"statusText"`
			Source            []string `json:"source"`
			Mobile            []string `json:"mobile"`
			Name              []string `json:"name"`
			LoanApplicationNo []string `json:"loanApplicationNum"`
			Filter            []string `json:"filter"`
			FirmName          []string `json:"firmName"`
			LoanType          []string `json:"loanType"`
			InvoiceNo         []string `json:"invoiceNo"`
		}

		bytes, err := json.Marshal(r.URL.Query())
		if err != nil {
			log.Println(err)
			panic(err)
		}

		var reqObj requestStruct
		err = json.Unmarshal(bytes, &reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		v := validator.New()

		err = v.Struct(reqObj)
		if err != nil {
			panic(err)
		}

		attributes := make(map[string]interface{})

		if len(reqObj.UtrNo) > 0 {
			attributes["utrNo"] = reqObj.UtrNo[0]
		} else {
			attributes["utrNo"] = ""
		}
		if len(reqObj.TxnID) > 0 {
			attributes["txnID"] = reqObj.TxnID[0]
		} else {
			attributes["txnID"] = ""
		}
		if len(reqObj.PartnerTxnID) > 0 {
			attributes["partnerTxnID"] = reqObj.PartnerTxnID[0]
		} else {
			attributes["partnerTxnID"] = ""
		}
		if len(reqObj.Source) > 0 {
			attributes["source"] = reqObj.Source[0]
		} else {
			attributes["source"] = ""
		}
		if len(reqObj.FirmName) > 0 {
			attributes["firmName"] = reqObj.FirmName[0]
		} else {
			attributes["firmName"] = ""
		}
		if len(reqObj.LoanApplicationNo) > 0 {
			attributes["loanApplicationNo"] = reqObj.LoanApplicationNo[0]
		} else {
			attributes["loanApplicationNo"] = ""
		}
		if len(reqObj.LoanType) > 0 {
			attributes["loanType"] = reqObj.LoanType[0]
			if !general.InArr(reqObj.LoanType[0], constants.ValidLoanTypeList) {
				panic("Invalid loan type")
			}
		} else {
			attributes["loanType"] = ""
		}
		if len(reqObj.InvoiceNo) > 0 {
			attributes["invoiceNo"] = reqObj.InvoiceNo[0]
		} else {
			attributes["invoiceNo"] = ""
		}

		if len(reqObj.Limit) == 0 {
			attributes["limit"] = 10
		} else {
			limitValue, err := strconv.Atoi(reqObj.Limit[0])
			if err != nil {
				panic(err)
			}
			if limitValue <= 0 {
				panic("limit should be greater than 0")
			}
			attributes["limit"] = limitValue
		}

		if len(reqObj.Page) == 0 {
			attributes["page"] = 1
		} else {
			pageValue, err := strconv.Atoi(reqObj.Page[0])
			if err != nil {
				panic(err)
			}
			if pageValue <= 0 {
				panic("page should be greater than 0")
			}
			attributes["page"] = pageValue
		}

		statusList := []string{}
		for _, statusText := range reqObj.StatusText {
			status, found := constants.CreditLineTxnStatusConditionLender[statusText]
			if !found {
				panic("Invalid status value " + statusText)
			}
			statusList = append(statusList, status)
		}
		attributes["statusList"] = statusList

		if len(reqObj.Mobile) > 0 {
			attributes["mobile"] = reqObj.Mobile[0]
		} else {
			attributes["mobile"] = ""
		}

		if len(reqObj.Name) > 0 {
			attributes["name"] = reqObj.Name[0]
		} else {
			attributes["name"] = ""
		}

		// parse dates if they exist
		if !(len(reqObj.FromDate) == 0 && len(reqObj.ToDate) == 0) {

			_, err := time.Parse(constants.DateFormat, reqObj.ToDate[0])
			if err != nil {
				log.Println("Invalid `to` date")
				panic("Invalid `to` date")
			}

			_, err = time.Parse(constants.DateFormat, reqObj.FromDate[0])
			if err != nil {
				log.Println("Invalid `from` date")
				panic("Invalid `from` date")
			}

			// panic if from is ahead of to
			if reqObj.ToDate[0] < reqObj.FromDate[0] {
				log.Println("Invalid `from` or `to` date")
				panic("Invalid `from` or `to` date")
			}

			attributes["to"] = reqObj.ToDate[0]
			attributes["from"] = reqObj.FromDate[0]
		}

		filter := ""
		if len(reqObj.Filter) != 0 {
			filter = reqObj.Filter[0]
		}
		switch filter {
		case "confirmed_at":
			attributes["filter"] = "confirmed_at"
		case "disbursed_at":
			attributes["filter"] = "disbursed_at"
		case "created_at":
			attributes["filter"] = "created_at"
		case "payment_date":
			attributes["filter"] = "payment_date"
		case "due_date":
			attributes["filter"] = "due_date"
		default:
			attributes["filter"] = "updated_at"
		}

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func TransactionListDumpReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			FromDate     []string `json:"from"`
			ToDate       []string `json:"to"`
			Filter       []string `json:"filter"`
			UtrNo        []string `json:"utrNo"`
			TxnID        []string `json:"txnID"`
			PartnerTxnID []string `json:"partnerTxnID"`
			StatusText   []string `json:"statusText"`
			Source       []string `json:"source"`
		}

		bytes, err := json.Marshal(r.URL.Query())
		if err != nil {
			log.Println(err)
			panic(err)
		}

		var reqObj requestStruct
		err = json.Unmarshal(bytes, &reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		v := validator.New()

		err = v.Struct(reqObj)
		if err != nil {
			panic(err)
		}

		attributes := make(map[string]interface{})

		if len(reqObj.UtrNo) > 0 {
			attributes["utrNo"] = reqObj.UtrNo[0]
		} else {
			attributes["utrNo"] = ""
		}
		if len(reqObj.TxnID) > 0 {
			attributes["txnID"] = reqObj.TxnID[0]
		} else {
			attributes["txnID"] = ""
		}
		if len(reqObj.PartnerTxnID) > 0 {
			attributes["partnerTxnID"] = reqObj.PartnerTxnID[0]
		} else {
			attributes["partnerTxnID"] = ""
		}
		attributes["source"] = ""
		if len(reqObj.Source) > 0 {
			attributes["source"] = reqObj.Source[0]
		}

		statusList := []string{}
		for _, statusText := range reqObj.StatusText {
			status, found := constants.CreditLineTxnStatusConditionLender[statusText]
			if !found {
				panic("Invalid status value " + statusText)
			}
			statusList = append(statusList, status)
		}
		attributes["statusList"] = statusList

		filter := ""
		if len(reqObj.Filter) != 0 {
			filter = reqObj.Filter[0]
		}
		switch filter {
		case "confirmed_at":
			attributes["filter"] = "confirmed_at"
		case "disbursed_at":
			attributes["filter"] = "disbursed_at"
		case "updated_at":
			attributes["filter"] = "updated_at"
		case "payment_date":
			attributes["filter"] = "payment_date"
		case "due_date":
			attributes["filter"] = "due_date"
		default:
			attributes["filter"] = "created_at"
		}

		// parse dates if they exist
		if !(len(reqObj.FromDate) == 0 && len(reqObj.ToDate) == 0) {

			_, err := time.Parse(constants.DateFormat, reqObj.ToDate[0])
			if err != nil {
				log.Println("Invalid `to` date")
				panic("Invalid `to` date")
			}

			_, err = time.Parse(constants.DateFormat, reqObj.FromDate[0])
			if err != nil {
				log.Println("Invalid `from` date")
				panic("Invalid `from` date")
			}

			// panic if from is ahead of to
			if reqObj.ToDate[0] < reqObj.FromDate[0] {
				log.Println("Invalid `from` or `to` date")
				panic("Invalid `from` or `to` date")
			}

			attributes["to"] = reqObj.ToDate[0]
			attributes["from"] = reqObj.FromDate[0]
		} else {
			panic("Invalid `from` or `to` date")
		}

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func UpdateGenderReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			LoanApplicationID string `json:"loanApplicationID" validate:"required"`
			Gender            string `json:"gender" validate:"required"`
		}

		v := validator.New()

		decoder := json.NewDecoder(r.Body)
		defer r.Body.Close()
		var reqObj requestStruct
		err := decoder.Decode(&reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		err = v.Struct(reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		genderCode, isValid := constants.GenderStrToNum[reqObj.Gender]
		if !isValid {
			log.Println("Invalid gender option")
			panic("Invalid gender option")
		}

		attributes := map[string]interface{}{
			"loanApplicationID": reqObj.LoanApplicationID,
			"gender":            genderCode,
		}

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func UpdateFathersNameReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			LoanApplicationID string `json:"loanApplicationID" validate:"required"`
			FathersName       string `json:"fathersName" validate:"required"`
		}

		v := validator.New()

		decoder := json.NewDecoder(r.Body)
		defer r.Body.Close()
		var reqObj requestStruct
		err := decoder.Decode(&reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		err = v.Struct(reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		attributes := map[string]interface{}{
			"loanApplicationID": reqObj.LoanApplicationID,
			"fathersName":       reqObj.FathersName,
		}

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func UpdateKYCDocReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			LoanApplicationID string   `json:"loanApplicationID" validate:"required"`
			LoanKYCDetailsID  string   `json:"loanKYCDetailsID" validate:"required"`
			MediaID           string   `json:"mediaID" validate:"required"`
			IsBack            bool     `json:"isBack"`
			Changes           []string `json:"changes"`
		}

		v := validator.New()

		decoder := json.NewDecoder(r.Body)
		defer r.Body.Close()
		var reqObj requestStruct
		err := decoder.Decode(&reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		err = v.Struct(reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		reqObj.Changes = general.RemoveDuplicatesAndEmpty(reqObj.Changes)
		// if len(reqObj.Changes) == 0 {
		// 	errorHandler.CustomError(w, http.StatusBadRequest, "changes cannot be empty")
		// 	return
		// }
		for _, change := range reqObj.Changes {
			if !general.InArr(change, []string{"IMAGE UPDATED", "IMAGE EDITED"}) {
				errorHandler.CustomError(w, http.StatusBadRequest, "invalid changes value should be IMAGE UPDATED or IMAGE EDITED")
				return
			}
		}

		attributes := map[string]interface{}{
			"loanApplicationID": reqObj.LoanApplicationID,
			"loanKYCDetailsID":  reqObj.LoanKYCDetailsID,
			"mediaID":           reqObj.MediaID,
			"isBack":            reqObj.IsBack,
			"changes":           reqObj.Changes,
		}

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func UpdateCoApplicantKYCDocReviewStatusReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			LoanApplicationID       string `json:"loanApplicationID" validate:"required"`
			CoApplicantKYCDetailsID string `json:"coApplicantKYCDetailsID" validate:"required"`
			ReviewStatus            int    `json:"reviewStatus"`
		}

		v := validator.New()

		decoder := json.NewDecoder(r.Body)
		defer r.Body.Close()
		var reqObj requestStruct
		err := decoder.Decode(&reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		err = v.Struct(reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		if !general.ValidateUUID(reqObj.CoApplicantKYCDetailsID) {
			errorHandler.CustomError(w, http.StatusBadRequest, "invalid coApplicantKYCDetailsID")
			return
		}

		if !general.ValidateUUID(reqObj.LoanApplicationID) {
			errorHandler.CustomError(w, http.StatusBadRequest, "invalid loanApplicationID")
			return
		}

		if !general.InArr(reqObj.ReviewStatus, coapplicantkycdetails.CoApplicantKYCDocReviewStatusArr) {
			errorHandler.CustomError(w, http.StatusBadRequest, "invalid review status value")
			return
		}

		attributes := map[string]interface{}{
			"loanApplicationID":       reqObj.LoanApplicationID,
			"coApplicantKYCDetailsID": reqObj.CoApplicantKYCDetailsID,
			"reviewStatus":            reqObj.ReviewStatus,
		}

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func UpdateBankDetailsReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			LoanApplicationID    string `json:"loanApplicationID" validate:"required"`
			AccountNumber        string `json:"accountNumber" validate:"required"`
			IFSC                 string `json:"ifsc" validate:"required"`
			AccountType          string `json:"accountType"`
			ForceSkipNameCheck   bool   `json:"forceSkipNameCheck"`
			UpdateMandateDetails bool   `json:"updateMandateDetails"`
		}

		v := validator.New()

		decoder := json.NewDecoder(r.Body)
		defer r.Body.Close()
		var reqObj requestStruct
		err := decoder.Decode(&reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		err = v.Struct(reqObj)
		if err != nil {
			panic(err)
		}

		if !general.ValidateAccountNumber(reqObj.AccountNumber) {
			errorHandler.CustomError(w, http.StatusBadRequest, "invalid account number")
			return
		}

		if reqObj.AccountType != "" && !general.InArr(reqObj.AccountType, []string{constants.BankAccountTypeCurrent, constants.BankAccountTypeSavings}) {
			errorHandler.CustomError(w, http.StatusBadRequest, "invalid account type")
			return
		}

		attributes := map[string]interface{}{
			"loanApplicationID":    reqObj.LoanApplicationID,
			"ifsc":                 reqObj.IFSC,
			"accountNumber":        reqObj.AccountNumber,
			"accountType":          reqObj.AccountType,
			"forceSkipNameCheck":   reqObj.ForceSkipNameCheck,
			"updateMandateDetails": reqObj.UpdateMandateDetails,
		}

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func UpdateLoanOfferReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			LoanApplicationID string  `json:"loanApplicationID" validate:"required"`
			LoanOfferID       string  `json:"loanOfferID" validate:"required"`
			Amount            float64 `json:"amount" validate:"required"`
			ProcessingFee     float64 `json:"processingFee"`
			ProcessingFeeType string  `json:"processingFeeType" validate:"required"`
			Tenure            int     `json:"tenure" validate:"required"`
			Interest          float64 `json:"interest"`
			ProcessingFeePart float64 `json:"processingFeePart"`
		}

		v := validator.New()

		decoder := json.NewDecoder(r.Body)
		defer r.Body.Close()
		var reqObj requestStruct
		err := decoder.Decode(&reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		err = v.Struct(reqObj)
		if err != nil {
			panic(err)
		}

		if reqObj.Amount <= 0 {
			errorHandler.CustomError(w, http.StatusBadRequest, "amount cannot be <= 0")
			return
		}
		if reqObj.ProcessingFee <= 0 {
			errorHandler.CustomError(w, http.StatusBadRequest, "processing fee cannot be < 0")
			return
		}
		if reqObj.Tenure <= 0 {
			errorHandler.CustomError(w, http.StatusBadRequest, "tenure cannot be <= 0")
			return
		}
		if reqObj.Interest <= 0 {
			errorHandler.CustomError(w, http.StatusBadRequest, "interest cannot be < 0")
			return
		}
		if reqObj.Interest > 100 {
			errorHandler.CustomError(w, http.StatusBadRequest, "interest cannot exceed 100%")
			return
		}
		if reqObj.ProcessingFeeType == "FLAT" {
			if reqObj.ProcessingFee >= reqObj.Amount {
				errorHandler.CustomError(w, http.StatusBadRequest, "processing fee value cannot be >= amount")
				return
			}
		} else if strings.HasPrefix(reqObj.ProcessingFeeType, "PERC") {
			if reqObj.ProcessingFee >= 100 {
				errorHandler.CustomError(w, http.StatusBadRequest, "processing fee value cannot be >= 100%")
				return
			}
		} else {
			errorHandler.CustomError(w, http.StatusBadRequest, "invalid processing fee type")
			return
		}
		attributes := map[string]interface{}{
			"loanApplicationID": reqObj.LoanApplicationID,
			"loanOfferID":       reqObj.LoanOfferID,
			"amount":            reqObj.Amount,
			"processingFee":     reqObj.ProcessingFee,
			"tenure":            reqObj.Tenure,
			"interest":          reqObj.Interest,
			"processingFeePart": reqObj.ProcessingFeePart,
			"processingFeeType": reqObj.ProcessingFeeType,
		}

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func RemoveInsuranceReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			LoanApplicationID string `json:"loanApplicationID" validate:"required"`
		}

		v := validator.New()

		decoder := json.NewDecoder(r.Body)
		defer r.Body.Close()
		var reqObj requestStruct
		err := decoder.Decode(&reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		err = v.Struct(reqObj)
		if err != nil {
			panic(err)
		}

		if !general.ValidateUUID(reqObj.LoanApplicationID) {
			errorHandler.CustomError(w, http.StatusBadRequest, "invalid loanApplicationID")
			return
		}

		attributes := map[string]interface{}{
			"loanApplicationID": reqObj.LoanApplicationID,
		}

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func AddPartnerReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			PartnerCode string `json:"partnerCode" validate:"required"`
			PartnerName string `json:"partnerName" validate:"required"`
			SPOCName    string `json:"spocName" validate:"required"`
			SPOCEmail   string `json:"spocEmail" validate:"required"`
			SPOCMobile  string `json:"spocMobile" validate:"required"`
		}

		v := validator.New()

		decoder := json.NewDecoder(r.Body)
		defer r.Body.Close()
		var reqObj requestStruct
		err := decoder.Decode(&reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		err = v.Struct(reqObj)
		if err != nil {
			panic(err)
		}

		if !general.ValidateEmail(reqObj.SPOCEmail) {
			errorHandler.CustomError(w, http.StatusBadRequest, "invalid email")
			return
		}

		if !general.IsNumber(reqObj.SPOCMobile) || len(reqObj.SPOCMobile) != 10 {
			errorHandler.CustomError(w, http.StatusBadRequest, "invalid mobile")
			return
		}

		attributes := map[string]interface{}{
			"partnerCode": reqObj.PartnerCode,
			"partnerName": reqObj.PartnerName,
			"spocEmail":   reqObj.SPOCEmail,
			"spocMobile":  reqObj.SPOCMobile,
			"spocName":    reqObj.SPOCName,
		}

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func GetExportHistoryReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			Limit []string `json:"limit"`
			Page  []string `json:"page"`
		}

		bytes, err := json.Marshal(r.URL.Query())
		if err != nil {
			log.Println(err)
			panic(err)
		}

		var reqObj requestStruct
		err = json.Unmarshal(bytes, &reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		v := validator.New()

		err = v.Struct(reqObj)
		if err != nil {
			panic(err)
		}

		attributes := make(map[string]interface{})

		if len(reqObj.Limit) == 0 {
			attributes["limit"] = 10
		} else {
			limitValue, err := strconv.Atoi(reqObj.Limit[0])
			if err != nil {
				panic(err)
			}
			if limitValue <= 0 {
				panic("limit should be greater than 0")
			}
			attributes["limit"] = limitValue
		}

		if len(reqObj.Page) == 0 {
			attributes["page"] = 1
		} else {
			pageValue, err := strconv.Atoi(reqObj.Page[0])
			if err != nil {
				panic(err)
			}
			if pageValue <= 0 {
				panic("page should be greater than 0")
			}
			attributes["page"] = pageValue
		}

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func CancelEKYCReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			LoanApplicationID string `json:"loanApplicationID" validate:"required"`
		}

		v := validator.New()
		decoder := json.NewDecoder(r.Body)
		defer r.Body.Close()
		var reqObj requestStruct
		err := decoder.Decode(&reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		err = v.Struct(reqObj)
		if err != nil {
			panic(err)
		}

		attributes := map[string]interface{}{
			"loanApplicationID": reqObj.LoanApplicationID,
		}

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func RetriggerExperianReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			LoanApplicationID string `json:"loanApplicationID" validate:"required"`
			Mobile            string `json:"mobile" validate:"required"`
		}

		v := validator.New()
		decoder := json.NewDecoder(r.Body)
		defer r.Body.Close()
		var reqObj requestStruct
		err := decoder.Decode(&reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		err = v.Struct(reqObj)
		if err != nil {
			panic(err)
		}

		if len(reqObj.Mobile) != 10 || !general.IsNumber(reqObj.Mobile) {
			errorHandler.CustomError(w, http.StatusBadRequest, "mobile if provided should be 10 digit long")
			return
		}

		attributes := map[string]interface{}{
			"loanApplicationID": reqObj.LoanApplicationID,
			"mobile":            reqObj.Mobile,
		}

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func ChangeCreditLimitReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type reqStruct struct {
			LoanApplicationID string  `json:"loanApplicationID" validate:"required"`
			NewLimit          float64 `json:"newLimit" validate:"required"`
		}

		v := validator.New()
		decoder := json.NewDecoder(r.Body)
		defer r.Body.Close()
		var reqObj reqStruct
		err := decoder.Decode(&reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		err = v.Struct(reqObj)
		if err != nil {
			panic(err)
		}

		attributes := make(map[string]interface{})

		attributes["loanApplicationID"] = reqObj.LoanApplicationID
		attributes["newLimit"] = reqObj.NewLimit

		ctx := context.WithValue(r.Context(), "attributes", attributes)

		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func UpdateLoanUTRReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			LoanApplicationID string `json:"loanApplicationID" validate:"required"`
			UTR               string `json:"utr" validate:"required"`
		}

		v := validator.New()
		decoder := json.NewDecoder(r.Body)
		defer r.Body.Close()
		var reqObj requestStruct
		err := decoder.Decode(&reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		err = v.Struct(reqObj)
		if err != nil {
			panic(err)
		}

		attributes := make(map[string]string)
		attributes["loanApplicationID"] = reqObj.LoanApplicationID
		attributes["utr"] = reqObj.UTR

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func UpdateTxnUTRReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			TxnID string `json:"txnID" validate:"required"`
			UTR   string `json:"utr" validate:"required"`
		}

		v := validator.New()
		decoder := json.NewDecoder(r.Body)
		defer r.Body.Close()
		var reqObj requestStruct
		err := decoder.Decode(&reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		err = v.Struct(reqObj)
		if err != nil {
			panic(err)
		}

		attributes := make(map[string]string)
		attributes["txnID"] = reqObj.TxnID
		attributes["utr"] = reqObj.UTR

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func UpdateCLRepaymentDetailsReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			TxnID                string  `json:"txnID" validate:"required"`
			ActualPaymentDate    string  `json:"actualPaymentDate" validate:"required"`
			ActualAmountReceived float64 `json:"actualAmountReceived" validate:"required"`
			PGTransactionID      string  `json:"pgTransactionID" validate:"required"`
		}

		v := validator.New()
		decoder := json.NewDecoder(r.Body)
		defer r.Body.Close()
		var reqObj requestStruct
		err := decoder.Decode(&reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		err = v.Struct(reqObj)
		if err != nil {
			panic(err)
		}

		_, err = time.Parse(constants.DateFormat, reqObj.ActualPaymentDate)
		if err != nil {
			log.Errorln("Invalid payment date date")
			panic("Invalid format payment date provided")
		}

		attributes := make(map[string]interface{})
		attributes["txnID"] = reqObj.TxnID
		attributes["actualPaymentDate"] = reqObj.ActualPaymentDate
		attributes["actualAmountReceived"] = reqObj.ActualAmountReceived
		attributes["pgTransactionID"] = reqObj.PGTransactionID

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func ESignLoanReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			LoanApplicationID string `json:"loanApplicationID" validate:"required"`
		}

		v := validator.New()
		decoder := json.NewDecoder(r.Body)
		defer r.Body.Close()
		var reqObj requestStruct
		err := decoder.Decode(&reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		err = v.Struct(reqObj)
		if err != nil {
			panic(err)
		}

		attributes := make(map[string]string)
		attributes["loanApplicationID"] = reqObj.LoanApplicationID

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func UpdateBoosterRequestReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type reqStruct struct {
			LoanApplicationID string  `json:"loanApplicationID" validate:"required"`
			NewLimit          float64 `json:"newLimit"`
			Status            string  `json:"status" validate:"required"`
		}

		v := validator.New()
		decoder := json.NewDecoder(r.Body)
		defer r.Body.Close()
		var reqObj reqStruct
		err := decoder.Decode(&reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		err = v.Struct(reqObj)
		if err != nil {
			panic(err)
		}

		attributes := make(map[string]interface{})

		if reqObj.Status != "APPROVE" && reqObj.Status != "REJECT" {
			errorHandler.CustomError(w, http.StatusBadRequest, "status should be APPROVE or REJECT")
			return
		}

		attributes["loanApplicationID"] = reqObj.LoanApplicationID
		attributes["newLimit"] = reqObj.NewLimit
		attributes["status"] = reqObj.Status

		ctx := context.WithValue(r.Context(), "attributes", attributes)

		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func GetODBillsReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			Limit             []string `json:"limit"`
			Page              []string `json:"page"`
			Mobile            []string `json:"mobile"`
			BillID            []string `json:"billID"`
			LoanApplicationNo []string `json:"loanApplicationNum"`
			StatusText        []string `json:"statusText"`
			Filter            []string `json:"filter"`
			FromDate          []string `json:"from"`
			ToDate            []string `json:"to"`
		}

		b, err := json.Marshal(r.URL.Query())
		if err != nil {
			log.Println(err)
			panic(err)
		}

		var reqObj requestStruct
		err = json.Unmarshal(b, &reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		v := validator.New()
		err = v.Struct(reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		attributes := make(map[string]interface{})

		if len(reqObj.Limit) == 0 {
			attributes["limit"] = 10
		} else {
			limitValue, err := strconv.Atoi(reqObj.Limit[0])
			if err != nil {
				log.Println(err)
				panic(err)
			}
			if limitValue <= 0 {
				panic("limit should be greater than 0")
			}
			attributes["limit"] = limitValue
		}

		if len(reqObj.Page) == 0 {
			attributes["page"] = 1
		} else {
			pageValue, err := strconv.Atoi(reqObj.Page[0])
			if err != nil {
				log.Println(err)
				panic(err)
			}
			if pageValue <= 0 {
				panic("page should be greater than 0")
			}
			attributes["page"] = pageValue
		}

		if len(reqObj.Mobile) > 0 {
			attributes["mobile"] = reqObj.Mobile[0]
		} else {
			attributes["mobile"] = ""
		}

		if len(reqObj.BillID) > 0 {
			attributes["bill_id"] = reqObj.BillID[0]
		} else {
			attributes["bill_id"] = ""
		}

		if len(reqObj.LoanApplicationNo) > 0 {
			attributes["loanApplicationNo"] = reqObj.LoanApplicationNo[0]
		} else {
			attributes["loanApplicationNo"] = ""
		}

		if len(reqObj.StatusText) > 0 {
			status, found := constants.ODBillStatusConditionPlatform[reqObj.StatusText[0]]
			if !found {
				panic("Invalid status value")
			}
			attributes["status"] = status
		} else {
			attributes["status"] = ""
		}

		attributes["status"] = ""
		if len(reqObj.StatusText) > 0 {
			status := ""
			for index, statusText := range reqObj.StatusText {
				statusVal, found := constants.ODBillStatusConditionPlatform[statusText]
				if !found {
					continue
				}
				if index == 0 || len(status) == 0 {
					status = fmt.Sprintf("(%s)", statusVal)
				} else {
					status = fmt.Sprintf("%s OR (%s)", status, statusVal)
				}
			}

			if status != "" {
				attributes["status"] = fmt.Sprintf("(%s)", status)
			}
		}

		if !(len(reqObj.FromDate) == 0 && len(reqObj.ToDate) == 0) {
			_, err := time.Parse(constants.DateFormat, reqObj.ToDate[0])
			if err != nil {
				log.Println("Invalid `to` date")
				panic("Invalid `to` date")
			}

			_, err = time.Parse(constants.DateFormat, reqObj.FromDate[0])
			if err != nil {
				log.Println("Invalid `from` date")
				panic("Invalid `from` date")
			}

			if reqObj.ToDate[0] < reqObj.FromDate[0] {
				log.Println("Invalid `from` or `to` date")
				panic("Invalid `from` or `to` date")
			}

			attributes["to"] = reqObj.ToDate[0]
			attributes["from"] = reqObj.FromDate[0]
		}

		filter := ""
		if len(reqObj.Filter) != 0 {
			filter = reqObj.Filter[0]
		}
		switch filter {
		case "due_date":
			attributes["filter"] = "due_date"
		case "updated_at":
			attributes["filter"] = "updated_at"
		default:
			attributes["filter"] = "created_at"
		}

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func UpdateBankAccountTypeReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			LoanApplicationID string `json:"loanApplicationID" validate:"required"`
			UserBankDetailsID string `json:"userBankDetailsID" validate:"required"`
			AccountType       string `json:"accountType" validate:"required"`
		}

		v := validator.New()

		decoder := json.NewDecoder(r.Body)
		defer r.Body.Close()
		var reqObj requestStruct
		err := decoder.Decode(&reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		err = v.Struct(reqObj)
		if err != nil {
			panic(err)
		}

		if !general.InArr(reqObj.AccountType, []string{constants.BankAccountTypeCurrent, constants.BankAccountTypeSavings}) {
			errorHandler.CustomError(w, http.StatusBadRequest, "invalid account type")
			return
		}

		attributes := map[string]interface{}{
			"loanApplicationID": reqObj.LoanApplicationID,
			"userBankDetailsID": reqObj.UserBankDetailsID,
			"accountType":       reqObj.AccountType,
		}

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func LoanApplicationReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)
		defer r.Body.Close()

		type reqStruct struct {
			LoanApplicationID string `json:"loanApplicationID" validate:"required,uuid"`
		}
		var reqObj reqStruct

		decoder := json.NewDecoder(r.Body)
		err := decoder.Decode(&reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		v := validator.New()
		err = v.Struct(reqObj)
		if err != nil {
			panic(err)
		}

		attributes := make(map[string]interface{})
		attributes["loanApplicationID"] = reqObj.LoanApplicationID

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func SignPhysicalMandateReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)
		defer r.Body.Close()

		type reqStruct struct {
			MandateID string `json:"mandateID" validate:"required"`
			MediaID   string `json:"mediaID" validate:"required,uuid"`
		}
		var reqObj reqStruct

		decoder := json.NewDecoder(r.Body)
		err := decoder.Decode(&reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		v := validator.New()
		err = v.Struct(reqObj)
		if err != nil {
			panic(err)
		}

		attributes := make(map[string]interface{})
		attributes["mandateID"] = reqObj.MandateID
		attributes["mediaID"] = reqObj.MediaID

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func LOSPushReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			TxnID string `json:"txnID" validate:"required"`
		}

		v := validator.New()
		decoder := json.NewDecoder(r.Body)
		defer r.Body.Close()
		var reqObj requestStruct
		err := decoder.Decode(&reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		err = v.Struct(reqObj)
		if err != nil {
			panic(err)
		}
		attributes := make(map[string]interface{})
		attributes["txnID"] = reqObj.TxnID
		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func MarkDisbursedInBulkReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		r.ParseForm()
		csvFile, _, err := r.FormFile("file")
		if err != nil {
			panic(err)
		}
		defer csvFile.Close()
		reader := csv.NewReader(csvFile)

		data, err := reader.ReadAll()
		if err != nil {
			panic(err)
		}

		for i, val := range data[1:] {
			if len(val) < 4 {
				errorHandler.CustomError(w, http.StatusBadRequest, "csv format incorrect, please check there are four columns with loan number/txnID/invoiceNo, utr number, disbursal date and disbursal amount")
				return
			}
			if len(general.RemoveAllSpaces(val[0])) == 0 {
				errorHandler.CustomError(w, http.StatusBadRequest, "missing loanApplicationNum/txnID/invoiceNo at row "+strconv.Itoa(i+1))
				return
			}
			if len(general.RemoveAllSpaces(val[1])) == 0 {
				errorHandler.CustomError(w, http.StatusBadRequest, "missing utrNo at row "+strconv.Itoa(i+1))
				return
			}
			if len(general.RemoveAllSpaces(val[2])) == 0 {
				errorHandler.CustomError(w, http.StatusBadRequest, "missing disbursal date at row "+strconv.Itoa(i+1))
				return
			}
			_, err = time.Parse(constants.DateFormat, val[2])
			if err != nil {
				errorHandler.CustomError(w, http.StatusBadRequest, "Incorrect date format at row "+strconv.Itoa(i+1)+", it should be YYYY-MM-DD")
				return
			}
			if len(val[3]) != 0 {
				_, err := strconv.ParseFloat(val[3], 64)
				if err != nil {
					errorHandler.CustomError(w, http.StatusBadRequest, "Invalid disbursal amount format at row "+strconv.Itoa(i+1))
					return
				}
			} else {
				errorHandler.CustomError(w, http.StatusBadRequest, "missing disbursal amount at row "+strconv.Itoa(i+1))
				return
			}
		}

		attributes := make(map[string]interface{})
		attributes["data"] = data[1:]
		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func MarkDisbursedLoanInBulkReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		r.ParseForm()
		csvFile, _, err := r.FormFile("file")
		if err != nil {
			panic(err)
		}
		defer csvFile.Close()
		reader := csv.NewReader(csvFile)

		data, err := reader.ReadAll()
		if err != nil {
			panic(err)
		}

		for i, val := range data[1:] {
			if len(val) < 3 {
				errorHandler.CustomError(w, http.StatusBadRequest, "csv format incorrect, please check there are four columns with loan number/txnID/invoiceNo, utr number, disbursal date and disbursal amount")
				return
			}
			if len(general.RemoveAllSpaces(val[0])) == 0 {
				errorHandler.CustomError(w, http.StatusBadRequest, "missing loanApplicationNum/txnID/invoiceNo at row "+strconv.Itoa(i+1))
				return
			}
			if len(general.RemoveAllSpaces(val[1])) == 0 {
				errorHandler.CustomError(w, http.StatusBadRequest, "missing utrNo at row "+strconv.Itoa(i+1))
				return
			}
			if len(general.RemoveAllSpaces(val[2])) == 0 {
				errorHandler.CustomError(w, http.StatusBadRequest, "missing disbursal date at row "+strconv.Itoa(i+1))
				return
			}
			_, err = time.Parse(constants.DateFormat, val[2])
			if err != nil {
				errorHandler.CustomError(w, http.StatusBadRequest, "Incorrect date format at row "+strconv.Itoa(i+1)+", it should be YYYY-MM-DD")
				return
			}
		}

		attributes := make(map[string]interface{})
		attributes["data"] = data[1:]
		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func MarkLoanRejectInBulkReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		r.ParseForm()
		csvFile, _, err := r.FormFile("file")
		if err != nil {
			panic(err)
		}
		defer csvFile.Close()
		reader := csv.NewReader(csvFile)

		data, err := reader.ReadAll()
		if err != nil {
			panic(err)
		}

		for i, val := range data[1:] {
			if len(val) < 2 {
				errorHandler.CustomError(w, http.StatusBadRequest, "csv format incorrect, please check there are two columns with loan number and rejection reason")
				return
			}
			if len(general.RemoveAllSpaces(val[0])) == 0 {
				errorHandler.CustomError(w, http.StatusBadRequest, "missing loanApplicationNum at row "+strconv.Itoa(i+1))
				return
			}
			if len(general.RemoveAllSpaces(val[1])) == 0 {
				errorHandler.CustomError(w, http.StatusBadRequest, "missing rejection reason at row "+strconv.Itoa(i+1))
				return
			}
		}

		attributes := make(map[string]interface{})
		attributes["data"] = data[1:]
		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func NachUpdateReportReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			FromDate []string `json:"fromdate"`
			ToDate   []string `json:"toDate"`
		}

		bytes, err := json.Marshal(r.URL.Query())
		if err != nil {
			log.Println(err)
			panic(err)
		}

		var reqObj requestStruct
		err = json.Unmarshal(bytes, &reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		v := validator.New()

		err = v.Struct(reqObj)
		if err != nil {
			panic(err)
		}

		attributes := make(map[string]interface{})

		if len(reqObj.FromDate) != 0 {
			_, err := time.Parse(constants.DateFormat, reqObj.FromDate[0])
			if err != nil {
				log.Println("Invalid from date")
				panic("Invalid from date")
			}
			attributes["fromDate"] = reqObj.FromDate[0]
		} else {
			panic("Invalid from date")
		}

		if len(reqObj.ToDate) != 0 {
			_, err := time.Parse(constants.DateFormat, reqObj.ToDate[0])
			if err != nil {
				log.Println("Invalid to date")
				panic("Invalid to date")
			}
			attributes["toDate"] = reqObj.ToDate[0]
		} else {
			panic("Invalid to date")
		}

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func ODBillsDumpReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			FromDate          []string `json:"from"`
			ToDate            []string `json:"to"`
			Filter            []string `json:"filter"`
			StatusText        []string `json:"statusText"`
			LoanApplicationNo []string `json:"loanApplicationNo"`
		}

		bytes, err := json.Marshal(r.URL.Query())
		if err != nil {
			log.Println(err)
			panic(err)
		}

		var reqObj requestStruct
		err = json.Unmarshal(bytes, &reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		v := validator.New()
		err = v.Struct(reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		attributes := make(map[string]interface{})

		if len(reqObj.LoanApplicationNo) > 0 {
			attributes["loanApplicationNo"] = reqObj.LoanApplicationNo[0]
		} else {
			attributes["loanApplicationNo"] = ""
		}

		if len(reqObj.StatusText) > 0 {
			statuses := strings.Split(reqObj.StatusText[0], ",")
			attribStatus := []string{}

			for _, s := range statuses {
				if status, ok := constants.ODBillStatusConditionPlatform[s]; ok {
					attribStatus = append(attribStatus, status)
				}
			}

			attributes["status"] = attribStatus
		} else {
			attributes["status"] = []string{}
		}

		if len(reqObj.Filter) > 0 {
			if general.InArr(reqObj.Filter[0], []string{"due_date", "updated_at", "billing_period"}) {
				attributes["filter"] = reqObj.Filter[0]
			} else {
				attributes["filter"] = "created_at"
			}
		} else {
			attributes["filter"] = "created_at"
		}

		// parse dates if they exist
		if !(len(reqObj.FromDate) == 0 && len(reqObj.ToDate) == 0) {

			_, err := time.Parse(constants.DateFormat, reqObj.ToDate[0])
			if err != nil {
				log.Println("Invalid `to` date")
				panic("Invalid `to` date")
			}

			_, err = time.Parse(constants.DateFormat, reqObj.FromDate[0])
			if err != nil {
				log.Println("Invalid `from` date")
				panic("Invalid `from` date")
			}

			// panic if from is ahead of to
			if reqObj.ToDate[0] < reqObj.FromDate[0] {
				log.Println("Invalid `from` or `to` date")
				panic("Invalid `from` or `to` date")
			}

			attributes["to"] = reqObj.ToDate[0]
			attributes["from"] = reqObj.FromDate[0]
		} else {
			panic("Invalid `from` or `to` date")
		}

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func NachUpdateReportByRequestIDReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			RequestID []string `json:"requestID"`
		}

		bytes, err := json.Marshal(r.URL.Query())
		if err != nil {
			log.Println(err)
			panic(err)
		}

		var reqObj requestStruct
		err = json.Unmarshal(bytes, &reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		v := validator.New()

		err = v.Struct(reqObj)
		if err != nil {
			panic(err)
		}

		attributes := make(map[string]interface{})

		if len(reqObj.RequestID) != 0 {
			requestID := reqObj.RequestID[0]
			attributes["requestID"] = requestID
		} else {
			errorHandler.CustomError(w, http.StatusBadRequest, "missing requestId field")
			return
		}

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func SourceEntityIDReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			SourceEntityID []string `json:"sourceEntityID" validate:"required"`
		}

		bytes, err := json.Marshal(r.URL.Query())
		if err != nil {
			log.Println(err)
			panic(err)
		}

		var reqObj requestStruct
		err = json.Unmarshal(bytes, &reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		v := validator.New()

		err = v.Struct(reqObj)
		if err != nil {
			panic(err)
		}

		attributes := make(map[string]interface{})
		attributes["sourceEntityID"] = reqObj.SourceEntityID[0]

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func RuleEngineDumpReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			FromDate []string `json:"from"`
			ToDate   []string `json:"to"`
			Filter   []string `json:"filter"`
			Source   []string `json:"source"`
		}

		bytes, err := json.Marshal(r.URL.Query())
		if err != nil {
			log.Println(err)
			panic(err)
		}

		var reqObj requestStruct
		err = json.Unmarshal(bytes, &reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		attributes := make(map[string]interface{})

		if !(len(reqObj.FromDate) == 0 && len(reqObj.ToDate) == 0) {

			_, err := time.Parse(constants.DateFormat, reqObj.ToDate[0])
			if err != nil {
				log.Println("Invalid `to` date")
				panic("Invalid `to` date")
			}

			_, err = time.Parse(constants.DateFormat, reqObj.FromDate[0])
			if err != nil {
				log.Println("Invalid `from` date")
				panic("Invalid `from` date")
			}

			// panic if from is ahead of to
			if reqObj.ToDate[0] < reqObj.FromDate[0] {
				log.Println("Invalid `from` or `to` date")
				panic("Invalid `from` or `to` date")
			}

			attributes["to"] = reqObj.ToDate[0]
			attributes["from"] = reqObj.FromDate[0]
		} else {
			panic("Invalid `from` or `to` date")
		}

		filter := ""
		if len(reqObj.Filter) != 0 {
			filter = reqObj.Filter[0]
		}
		switch filter {
		case "decision_date":
			attributes["filter"] = "decision_date"
		default:
			attributes["filter"] = "user_created_at"
		}

		attributes["source"] = ""
		if len(reqObj.Source) > 0 {
			attributes["source"] = reqObj.Source[0]
		}

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func CancelLoanReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			LoanApplicationID  string `json:"loanApplicationID" validate:"required"`
			CancellationReason string `json:"cancellationReason" validate:"required"`
		}

		v := validator.New()
		decoder := json.NewDecoder(r.Body)
		defer r.Body.Close()
		var reqObj requestStruct
		err := decoder.Decode(&reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		err = v.Struct(reqObj)
		if err != nil {
			panic(err)
		}
		attributes := map[string]interface{}{
			"loanApplicationID":  reqObj.LoanApplicationID,
			"cancellationReason": reqObj.CancellationReason,
		}

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func AdhocEnachPresentationReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			RequestID     string `json:"requestID" validate:"required"`
			RequestStatus string `json:"requestStatus" validate:"required"`
		}

		v := validator.New()
		decoder := json.NewDecoder(r.Body)
		defer r.Body.Close()

		var reqObj requestStruct
		err := decoder.Decode(&reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		err = v.Struct(reqObj)
		if err != nil {
			panic(err)
		}

		if !general.ValidateUUID(reqObj.RequestID) {
			panic("invalid requestID")
		}

		status, ok := constants.AdhocNachStatusStrIntMap[reqObj.RequestStatus]
		if !ok || !general.InArr(status, []int{constants.AdhocNachStatusUploaded, constants.AdhocNachStatusCancelled}) {
			panic("invalid request status, can be one of `uploaded` or `cancelled`")
		}

		attributes := map[string]interface{}{
			"requestID":     reqObj.RequestID,
			"requestStatus": status,
		}

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func GetRequestDetailsReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			RequestID []string `json:"requestID"`
		}

		bytes, err := json.Marshal(r.URL.Query())
		if err != nil {
			log.Println(err)
			panic(err)
		}

		var reqObj requestStruct
		err = json.Unmarshal(bytes, &reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		attributes := make(map[string]interface{})

		if len(reqObj.RequestID) == 0 {
			panic("missing field requestID")
		}

		if !general.ValidateUUID(reqObj.RequestID[0]) {
			panic("invalid requestID")
		}

		attributes["requestID"] = reqObj.RequestID[0]

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func GetNachPresentationReportsReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			Page          []string `json:"page"`
			Limit         []string `json:"limit"`
			RequestStatus []string `json:"requestStatus"`
			RequestedBy   []string `json:"requestedBy"`
			FromDate      []string `json:"fromDate"`
			ToDate        []string `json:"toDate"`
			RequestID     []string `json:"requestID"`
		}

		bytes, err := json.Marshal(r.URL.Query())
		if err != nil {
			log.Println(err)
			panic(err)
		}

		var reqObj requestStruct
		err = json.Unmarshal(bytes, &reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		attributes := make(map[string]interface{})

		if len(reqObj.Limit) == 0 {
			attributes["limit"] = 10
		} else {
			limitValue, err := strconv.Atoi(reqObj.Limit[0])
			if err != nil {
				panic(err)
			}
			if limitValue <= 0 {
				panic("limit should be greater than 0")
			}
			attributes["limit"] = limitValue
		}

		if len(reqObj.Page) == 0 {
			attributes["page"] = 1
		} else {
			pageValue, err := strconv.Atoi(reqObj.Page[0])
			if err != nil {
				panic(err)
			}
			if pageValue <= 0 {
				panic("page should be greater than 0")
			}
			attributes["page"] = pageValue
		}

		filter := ""
		if len(reqObj.RequestStatus) != 0 {
			if !general.InArr(reqObj.RequestStatus[0], []string{"completed", "cancelled", "created", "failed", "processing", "uploaded"}) {
				panic("invalid request status, can be one of [`completed`, `cancelled`, `created`, `failed`, `processing`, `uploaded`]")
			}
			filter = reqObj.RequestStatus[0]
		}
		switch filter {
		case "created":
			attributes["filter"] = constants.AdhocNachStatusStrIntMap["created"]
		case "cancelled":
			attributes["filter"] = constants.AdhocNachStatusStrIntMap["cancelled"]
		case "completed":
			attributes["filter"] = constants.AdhocNachStatusStrIntMap["completed"]
		case "failed":
			attributes["filter"] = constants.AdhocNachStatusStrIntMap["failed"]
		case "processing":
			attributes["filter"] = constants.AdhocNachStatusStrIntMap["processing"]
		case "uploaded":
			attributes["filter"] = constants.AdhocNachStatusStrIntMap["uploaded"]
		default:
			attributes["filter"] = -1
		}

		if len(reqObj.RequestID) > 0 {
			if !general.ValidateUUID(reqObj.RequestID[0]) {
				panic("invalid requestID")
			}

			attributes["requestID"] = reqObj.RequestID[0]
		} else {
			attributes["requestID"] = ""
		}
		if len(reqObj.RequestedBy) > 0 {
			attributes["requestedBy"] = reqObj.RequestedBy[0]
		} else {
			attributes["requestedBy"] = ""
		}
		// parse dates if exists
		if !(len(reqObj.FromDate) == 0 && len(reqObj.ToDate) == 0) {

			_, err := time.Parse(constants.DateFormat, reqObj.ToDate[0])
			if err != nil {
				log.Println("Invalid `to` date")
				panic("Invalid `to` date")
			}

			_, err = time.Parse(constants.DateFormat, reqObj.FromDate[0])
			if err != nil {
				log.Println("Invalid `from` date")
				panic("Invalid `from` date")
			}

			// panic if from is ahead of to
			if reqObj.ToDate[0] < reqObj.FromDate[0] {
				log.Println("Invalid `from` or `to` date")
				panic("Invalid `from` or `to` date")
			}
			attributes["to"] = reqObj.ToDate[0]
			attributes["from"] = reqObj.FromDate[0]
		}

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func ExportLedgerReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			LoanApplicationID []string `json:"loanApplicationID" validate:"required"`
		}

		b, _ := json.Marshal(r.URL.Query())
		var reqObj requestStruct
		err := json.Unmarshal(b, &reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		v := validator.New()
		err = v.Struct(reqObj)
		if err != nil {
			panic(err)
		}

		attributes := make(map[string]string)
		attributes["loanApplicationID"] = reqObj.LoanApplicationID[0]
		attributes["dashboardUser"] = constants.LenderDashboardRef

		if !general.ValidateUUID(attributes["loanApplicationID"]) {
			errorHandler.CustomError(w, http.StatusBadRequest, "invalid loanApplicationID")
			return
		}

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func CheckPincodeReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type RequestStruct struct {
			Pincode        string `json:"pincode" validate:"required"`
			SourceEntityID string `json:"sourceEntityID" validate:"required"`
		}

		v := validator.New()
		decoder := json.NewDecoder(r.Body)
		defer r.Body.Close()
		var reqObj RequestStruct
		err := decoder.Decode(&reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}
		err = v.Struct(reqObj)
		if err != nil {
			panic(err)
		}
		if !general.ValidatePincode(reqObj.Pincode) {
			errorHandler.CustomError(w, http.StatusBadRequest, "Invalid Pincode")
			return
		}
		if !general.ValidateUUID(reqObj.SourceEntityID) {
			errorHandler.CustomError(w, http.StatusBadRequest, "invalid SourceEntityID")
			return
		}

		userObj := r.Context().Value("user")
		attributes := make(map[string]interface{})
		attributes["pincode"] = reqObj.Pincode
		attributes["sourceEntityID"] = reqObj.SourceEntityID
		attributes["lenderID"] = userObj.(authentication.LenderUserStruct).LenderID
		attributes["lenderEmail"] = userObj.(authentication.LenderUserStruct).Email

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func AddMemberReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)
		type requestStruct struct {
			Name      string `json:"name"`
			Email     string `json:"email"`
			GroupName string `json:"groupName"`
			MFAStatus int    `json:"mfaStatus"`
		}

		decoder := json.NewDecoder(r.Body)
		defer func(Body io.ReadCloser) {
			err := Body.Close()
			if err != nil {
				log.Errorln(err)
			}
		}(r.Body)
		var reqObj requestStruct
		err := decoder.Decode(&reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		if len(reqObj.GroupName) == 0 {
			errorHandler.CustomError(w, http.StatusBadRequest, rbacErrors.ErrEmptyGroupName.Error())
			return
		}

		if len(reqObj.Email) == 0 {
			errorHandler.CustomError(w, http.StatusBadRequest, rbacErrors.ErrEmailMissing.Error())
			return
		}

		if !general.ValidateEmail(reqObj.Email) {
			errorHandler.CustomError(w, http.StatusBadRequest, rbacErrors.ErrInvalidEmailID.Error())
			return
		}

		if len(reqObj.Name) < 5 || len(reqObj.Name) >= 40 {
			errorHandler.CustomError(w, http.StatusBadRequest, rbacErrors.ErrInvalidNameLength.Error())
			return
		}

		if !general.IsValidMfaStatus(reqObj.MFAStatus, constants.LenderDashboardRef) {
			errorHandler.CustomError(w, http.StatusBadRequest, "incorrect mfa status")
			return
		}

		lenderID := r.Context().Value("user").(authentication.LenderUserStruct).LenderID
		reqObj.GroupName = fmt.Sprintf("%s%s%s", lenderID, rbacConstants.RBACSeparator, reqObj.GroupName)

		attributes := map[string]interface{}{
			"name":      reqObj.Name,
			"email":     strings.ToLower(general.RemoveAllSpaces(reqObj.Email)),
			"groupName": reqObj.GroupName,
			"mfaStatus": reqObj.MFAStatus,
		}
		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func CheckBulkPincodeReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		err := r.ParseMultipartForm(32 << 20)
		if err != nil {
			logger.WithRequest(r).Error(err)
			errorHandler.CustomError(w, http.StatusBadRequest, "couldn't parse the request")
			return
		}

		sourceEntityID := r.FormValue("sourceEntityID")
		if len(sourceEntityID) == 0 || !general.ValidateUUID(sourceEntityID) {
			errorHandler.CustomError(w, http.StatusBadRequest, "Empty or Invalid SourceEntityID")
			return
		}

		file, header, err := r.FormFile("file")
		if err != nil {
			errorHandler.CustomError(w, http.StatusBadRequest, "file field not found")
			return
		}

		defer file.Close()

		if header == nil {
			log.Println("header is null")
			errorHandler.CustomError(w, http.StatusBadRequest, "couldn't determine the file headers, please try again")
			return
		}
		fileNameArr := strings.Split(header.Filename, ".")
		if len(fileNameArr) < 2 {
			log.Println("extension not found for", header.Filename)
			errorHandler.CustomError(w, http.StatusBadRequest, "couldn't check the file extension, please try again")
			return
		}
		extension := strings.ToLower(fileNameArr[1])
		if extension != "csv" {
			errorHandler.CustomError(w, http.StatusBadRequest, "please upload a csv file")
			return
		}
		userObj := r.Context().Value("user")
		attributes := make(map[string]interface{})
		attributes["pincodeList"] = file
		attributes["sourceEntityID"] = sourceEntityID
		attributes["lenderID"] = userObj.(authentication.LenderUserStruct).LenderID
		attributes["lenderEmail"] = userObj.(authentication.LenderUserStruct).Email
		attributes["lenderName"] = userObj.(authentication.LenderUserStruct).Name
		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func GetPincodeListReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)
		type queryStruct struct {
			SourceEntityID []string `json:"SourceEntityID" validate:"required"`
		}
		bytes, err := json.Marshal(r.URL.Query())
		if err != nil {
			log.Println(err)
			panic(err)
		}

		var reqObj queryStruct

		err = json.Unmarshal(bytes, &reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}
		if !general.ValidateUUID(reqObj.SourceEntityID[0]) {
			errorHandler.CustomError(w, http.StatusBadRequest, "invalid SourceEntityID")
			return
		}
		userObj := r.Context().Value("user")
		attributes := make(map[string]interface{})
		attributes["sourceEntityID"] = reqObj.SourceEntityID[0]
		attributes["lenderID"] = userObj.(authentication.LenderUserStruct).LenderID
		attributes["lenderEmail"] = userObj.(authentication.LenderUserStruct).Email
		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func AddNewPincodeListForPartner(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)
		sourceEntityID := r.FormValue("sourceEntityID")
		checkMode, err := strconv.Atoi(r.FormValue("checkMode"))

		sourceEntityIDList := strings.Split(sourceEntityID, ",")

		if err != nil {
			errorHandler.CustomError(w, http.StatusBadRequest, "Invalid CheckMode")
			return
		}

		// csv checks
		file, header, err := r.FormFile("file")
		if err != nil {
			errorHandler.CustomError(w, http.StatusBadRequest, "file field not found")
			return
		}

		defer file.Close()

		if header == nil {
			log.Println("header is null")
			errorHandler.CustomError(w, http.StatusBadRequest, "couldn't determine the file headers, please try again")
			return
		}
		fileNameArr := strings.Split(header.Filename, ".")
		if len(fileNameArr) < 2 {
			log.Println("extension not found for", header.Filename)
			errorHandler.CustomError(w, http.StatusBadRequest, "couldn't check the file extension, please try again")
			return
		}
		extension := strings.ToLower(fileNameArr[1])
		if extension != "csv" {
			errorHandler.CustomError(w, http.StatusBadRequest, "please upload a csv file")
			return
		}
		attributes := make(map[string]interface{})
		attributes["sourceEntityID"] = sourceEntityIDList
		attributes["checkMode"] = checkMode
		attributes["pincodeList"] = file
		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func CheckDeletePincodeSEListReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type queryStruct struct {
			SourceEntityID []string `json:"SourceEntityID" validate:"required"`
		}
		log.Println(r.URL.Query())
		bytes, err := json.Marshal(r.URL.Query())
		if err != nil {
			log.Println(err)
			panic(err)
		}

		var reqObj queryStruct
		err = json.Unmarshal(bytes, &reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		if !general.ValidateUUID(reqObj.SourceEntityID[0]) {
			errorHandler.CustomError(w, http.StatusBadRequest, "invalid SourceEntityID")
			return
		}

		userObj := r.Context().Value("user")
		lenderID := userObj.(authentication.LenderUserStruct).LenderID
		if lenderID == constants.IIFLID &&
			(reqObj.SourceEntityID[0] == constants.IIFLBLID || reqObj.SourceEntityID[0] == constants.IIFLID) {
			errorHandler.CustomError(w, http.StatusBadRequest, "Default List cannot be deleted")
			return
		}
		attributes := make(map[string]interface{})
		attributes["sourceEntityID"] = reqObj.SourceEntityID[0]
		attributes["lenderID"] = lenderID
		attributes["lenderEmail"] = userObj.(authentication.LenderUserStruct).Email
		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func UserListReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			Limit     []string `json:"limit"`
			Page      []string `json:"page"`
			FromDate  []string `json:"from"`
			ToDate    []string `json:"to"`
			LenderID  []string `json:"lenderID"`
			Email     []string `json:"email"`
			CreatedAt []string `json:"createdAt"`
			Name      []string `json:"name"`
			UpdatedAt []string `json:"updatedAt"`
			Mobile    []string `json:"mobile"`
			Filter    []string `json:"filter"`
		}

		userObj := r.Context().Value("user").(authentication.LenderUserStruct)

		if !userObj.IsAdmin {
			errorHandler.CustomError(w, http.StatusBadRequest, "Not an admin user")
			return
		}

		bytes, err := json.Marshal(r.URL.Query())
		if err != nil {
			log.Println(err)
			panic(err)
		}

		var reqObj requestStruct
		err = json.Unmarshal(bytes, &reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		v := validator.New()

		err = v.Struct(reqObj)
		if err != nil {
			panic(err)
		}

		attributes := make(map[string]interface{})

		if len(reqObj.Email) > 0 {
			attributes["email"] = reqObj.Email[0]
		} else {
			attributes["email"] = ""
		}
		if len(reqObj.Name) > 0 {
			attributes["name"] = reqObj.Name[0]
		} else {
			attributes["name"] = ""
		}
		if len(reqObj.Mobile) > 0 {
			attributes["mobile"] = reqObj.Mobile[0]
		} else {
			attributes["mobile"] = ""
		}
		if len(reqObj.Limit) == 0 {
			attributes["limit"] = 10
		} else {
			limitValue, err := strconv.Atoi(reqObj.Limit[0])
			if err != nil {
				panic(err)
			}
			if limitValue <= 0 {
				panic("limit should be greater than 0")
			}
			attributes["limit"] = limitValue
		}

		if len(reqObj.Page) == 0 {
			attributes["page"] = 1
		} else {
			pageValue, err := strconv.Atoi(reqObj.Page[0])
			if err != nil {
				panic(err)
			}
			if pageValue <= 0 {
				panic("page should be greater than 0")
			}
			attributes["page"] = pageValue
		}

		filter := ""
		if len(reqObj.Filter) != 0 {
			filter = reqObj.Filter[0]
		}
		switch filter {
		case "created_at":
			attributes["filter"] = "created_at"
		default:
			attributes["filter"] = "updated_at"
		}

		if !(len(reqObj.FromDate) == 0 && len(reqObj.ToDate) == 0) {

			_, err := time.Parse(constants.DateFormat, reqObj.ToDate[0])
			if err != nil {
				log.Println("Invalid `to` date")
				panic("Invalid `to` date")
			}

			_, err = time.Parse(constants.DateFormat, reqObj.FromDate[0])
			if err != nil {
				log.Println("Invalid `from` date")
				panic("Invalid `from` date")
			}

			if reqObj.ToDate[0] < reqObj.FromDate[0] {
				log.Println("Invalid `from` or `to` date")
				panic("Invalid `from` or `to` date")
			}

			attributes["to"] = reqObj.ToDate[0]
			attributes["from"] = reqObj.FromDate[0]
		}

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func DeactivateUserReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		userObj := r.Context().Value("user").(authentication.LenderUserStruct)

		if !userObj.IsAdmin {
			errorHandler.CustomError(w, http.StatusBadRequest, "Not an admin user")
			return
		}

		type requestStruct struct {
			Email string `json:"email" validate:"required,email,min=5,max=100"`
		}

		v := validator.New()
		decoder := json.NewDecoder(r.Body)
		defer r.Body.Close()
		var reqObj requestStruct
		err := decoder.Decode(&reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}
		err = v.Struct(reqObj)
		if err != nil {
			panic(err)
		}

		if !general.ValidateEmail(reqObj.Email) {
			errorHandler.CustomError(w, http.StatusBadRequest, "not a valid email")
			return
		}

		attributes := map[string]interface{}{
			"email": reqObj.Email,
		}
		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func UploadAdditionalDocsReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		ctx := r.Context()

		type requestStruct struct {
			LoanApplicationID     string                 `json:"loanApplicationID" validate:"required"`
			MediaIDs              []string               `json:"mediaIDs" validate:"required"`
			Comment               string                 `json:"comment"`
			AdditionalData        map[string]interface{} `json:"additionalData"`
			MediaIdsToDocumentIds map[string]string      `json:"mediaIdsToDocumentIds"`
			UserID                string                 `json:"userID"`
		}

		v := validator.New()
		decoder := json.NewDecoder(r.Body)
		defer r.Body.Close()
		var reqObj requestStruct
		err := decoder.Decode(&reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}
		err = v.Struct(reqObj)
		if err != nil {
			panic(err)
		}

		if !general.ValidateUUID(reqObj.LoanApplicationID) {
			errorHandler.CustomError(w, http.StatusBadRequest, "invalid loan application id")
			return
		}

		if reqObj.UserID != "" && !general.ValidateUUID(reqObj.UserID) {
			logger.WithContext(ctx).Errorf("[UploadAdditionalDocsReq] Invalid user Id. param: %+v", reqObj.UserID)
			errorHandler.CustomError(w, http.StatusBadRequest, constants.InValidUserID)
			return
		}

		if len(reqObj.MediaIDs) == 0 {
			errorHandler.CustomError(w, http.StatusBadRequest, "no media id found")
			return
		}

		if len(reqObj.MediaIDs) > 5 {
			errorHandler.CustomError(w, http.StatusBadRequest, "maximum 5 files can be uploaded")
			return
		}

		for _, mediaID := range reqObj.MediaIDs {
			if !general.ValidateUUID(mediaID) {
				errorHandler.CustomError(w, http.StatusBadRequest, "invalid media id")
				return
			}
		}

		for _, value := range reqObj.MediaIdsToDocumentIds {
			if !general.ValidateUUID(value) {
				errorHandler.CustomError(w, http.StatusBadRequest, "invalid document id")
				return
			}
		}

		attributes := map[string]interface{}{
			"loanApplicationID":     reqObj.LoanApplicationID,
			"mediaIDs":              reqObj.MediaIDs,
			"comment":               reqObj.Comment,
			"additionalData":        reqObj.AdditionalData,
			"mediaIdsToDocumentIds": reqObj.MediaIdsToDocumentIds,
			"userID":                reqObj.UserID,
		}
		ctx = context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func GetAdditionalDocsReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			LoanApplicationID []string `json:"loanApplicationID" validate:"required"`
		}

		bytes, err := json.Marshal(r.URL.Query())
		if err != nil {
			log.Println(err)
			panic(err)
		}

		var reqObj requestStruct
		err = json.Unmarshal(bytes, &reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		v := validator.New()

		err = v.Struct(reqObj)
		if err != nil {
			panic(err)
		}

		attributes := make(map[string]string)

		if len(reqObj.LoanApplicationID) > 0 {
			attributes["loanApplicationID"] = reqObj.LoanApplicationID[0]
			if !general.ValidateUUID(attributes["loanApplicationID"]) {
				errorHandler.CustomError(w, http.StatusBadRequest, "invalid loan application id")
				return
			}
		} else {
			attributes["loanApplicationID"] = ""
		}

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func DeleteDocumentReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			LoanApplicationID string `json:"loanApplicationID" validate:"required"`
			DocID             string `json:"docID" validate:"required"`
		}

		v := validator.New()
		decoder := json.NewDecoder(r.Body)
		defer r.Body.Close()
		var reqObj requestStruct
		err := decoder.Decode(&reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}
		err = v.Struct(reqObj)
		if err != nil {
			panic(err)
		}

		if !general.ValidateUUID(reqObj.LoanApplicationID) {
			errorHandler.CustomError(w, http.StatusBadRequest, "invalid loan application id")
			return
		}
		if !general.ValidateUUID(reqObj.DocID) {
			errorHandler.CustomError(w, http.StatusBadRequest, "invalid doc id")
			return
		}

		attributes := map[string]string{
			"loanApplicationID": reqObj.LoanApplicationID,
			"docID":             reqObj.DocID,
		}
		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func AddNoteReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			LoanApplicationID string `json:"loanApplicationID" validate:"required"`
			Note              string `json:"note" validate:"required"`
		}

		v := validator.New()
		decoder := json.NewDecoder(r.Body)
		defer r.Body.Close()
		var reqObj requestStruct
		err := decoder.Decode(&reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}
		err = v.Struct(reqObj)
		if err != nil {
			panic(err)
		}

		if !general.ValidateUUID(reqObj.LoanApplicationID) {
			errorHandler.CustomError(w, http.StatusBadRequest, "invalid loan application id")
			return
		}

		attributes := map[string]string{
			"loanApplicationID": reqObj.LoanApplicationID,
			"note":              reqObj.Note,
		}
		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func GetNotesReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			LoanApplicationID []string `json:"loanApplicationID" validate:"required"`
		}

		bytes, err := json.Marshal(r.URL.Query())
		if err != nil {
			log.Println(err)
			panic(err)
		}

		var reqObj requestStruct
		err = json.Unmarshal(bytes, &reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		v := validator.New()

		err = v.Struct(reqObj)
		if err != nil {
			panic(err)
		}

		attributes := make(map[string]string)

		if len(reqObj.LoanApplicationID) > 0 {
			attributes["loanApplicationID"] = reqObj.LoanApplicationID[0]
			if !general.ValidateUUID(attributes["loanApplicationID"]) {
				errorHandler.CustomError(w, http.StatusBadRequest, "invalid loan application id")
				return
			}
		} else {
			attributes["loanApplicationID"] = ""
		}

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func AddDSAReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		err := r.ParseMultipartForm(32 << 20)
		if err != nil {
			logger.WithRequest(r).Error(err)
			errorHandler.CustomError(w, http.StatusBadRequest, "couldn't parse the request")
			return
		}

		lenderID := r.Context().Value("user").(authentication.LenderUserStruct).LenderID
		agentName := general.RemoveExtraSpaces(r.FormValue("agentName"))
		productType := r.FormValue("productType")
		email := strings.ToLower(general.RemoveAllSpaces(r.FormValue("email")))
		agentCode := strings.ToLower(general.RemoveAllSpaces(r.FormValue("agentCode")))
		password := r.FormValue("password")
		mobile := general.RemoveAllSpaces(r.FormValue("mobile"))
		isExistingLogin, _ := strconv.ParseBool(r.FormValue("isExistingLogin"))
		productTypeList := strings.Split(productType, ",")
		subvention := r.FormValue("subvention")
		firstEmiAfterDays := r.FormValue("firstEmiAfterDays")
		gapEmiDays := r.FormValue("gapEmiDays")
		totalEmiCount := r.FormValue("totalEmiCount")
		lateChargeType := r.FormValue("lateChargeType")
		lateCharge := r.FormValue("lateCharge")
		nodeType := r.FormValue("type")
		isMasked := r.FormValue("isMasked")
		isInsuranceMandatory := r.FormValue("isInsuranceMandatory")

		if agentName == "" || productType == "" || agentCode == "" {
			errorHandler.CustomError(w, http.StatusBadRequest, "required field(s) missing")
			return
		}

		productTypeList = general.RemoveDuplicatesAndEmpty(productTypeList)
		for _, productType := range productTypeList {
			productType = general.RemoveAllSpaces(productType)
			if lenderID != constants.MFLBLID && !general.InArr(productType, []string{constants.LoanTypePersonalLoan, constants.LoanTypeBusinessLoan, constants.LoanTypeCreditLine, constants.LoanTypeEducationLoan}) {
				errorHandler.CustomError(w, http.StatusBadRequest, "invalid product type: "+productType)
				return
			}
			if lenderID == constants.MFLBLID && !general.InArr(productType, constants.ValidLoanTypeForMFLBLenderID) {
				errorHandler.CustomError(w, http.StatusBadRequest, "invalid product type: "+productType)
				return
			}
		}

		if len(agentName) > 40 {
			errorHandler.CustomError(w, http.StatusBadRequest, "agent name must be less than 40 characters")
			return
		}

		if !general.ValidateAgentCode(agentCode) {
			errorHandler.CustomError(w, http.StatusBadRequest, "invalid agent code, can contain only alphabets, numbers, dashes, and underscores")
			return
		}

		if email == "" && mobile == "" {
			errorHandler.CustomError(w, http.StatusBadRequest, "either email or mobile must be present")
			return
		}

		userObj := r.Context().Value("user").(authentication.LenderUserStruct)

		if email != "" && userObj.LenderID != constants.MFLBLID {
			if !general.ValidateEmail(email) {
				errorHandler.CustomError(w, http.StatusBadRequest, "invalid email")
				return
			}
			if strings.Contains(email, "finbox") {
				errorHandler.CustomError(w, http.StatusBadRequest, "please input work email of the DSA")
				return
			}
			if !isExistingLogin && (len(password) < 5 || len(password) > 16) {
				errorHandler.CustomError(w, http.StatusBadRequest, "password can be no shorter than 5 and no longer than 16 characters")
				return
			}
		}

		if mobile != "" && !general.ValidateMobile(mobile) {
			errorHandler.CustomError(w, http.StatusBadRequest, "invalid mobile")
			return
		}

		if nodeType == "" {
			errorHandler.CustomError(w, http.StatusBadRequest, "partner type is required")
			return
		}

		if !general.InArr(nodeType, []string{constants.DSAIndividualType, constants.DSAOrganizationType, constants.DSAInternalEmployeeType}) {
			errorHandler.CustomError(w, http.StatusBadRequest, "invalid partner type")
			return
		}

		_, fileHeader, err := r.FormFile("file")
		if err != nil {
			logger.WithRequest(r).Error(err)
			errorHandler.CustomError(w, http.StatusBadRequest, "file was not passed or was in invalid format")
			return
		}

		if fileHeader.Size > constants.MaxSizeLogoUpload {
			logger.WithRequest(r).Error("file size exceeds max permitted value")
			errorHandler.CustomError(w, http.StatusBadRequest, "file size exceeds max permitted value")
			return
		}

		file, extension, httpCode, httpErrMessage := services.CheckSupportedFormats(r, []string{"jpeg", "png", "jpg", "svg", "webp"}, []string{"image/jpeg", "image/jpg", "image/png", "image/svg+xml", "image/webp"})
		if httpCode > 0 {
			errorHandler.CustomError(w, httpCode, httpErrMessage)
			return
		}

		if lateChargeType != "" && !general.InArr(lateChargeType, []string{constants.LateChargeTypeFlatDaily, constants.LateChargeTypeInterestMonthly}) {
			errorHandler.CustomError(w, http.StatusBadRequest, "invalid late charge selected")
			return
		}

		if gapEmiDays != "" {
			gapEmiDaysInt, err := strconv.ParseInt(gapEmiDays, 10, 32)
			if err != nil {
				errorHandler.CustomError(w, http.StatusBadRequest, err.Error())
				return
			}
			if gapEmiDaysInt > 60 || gapEmiDaysInt < 0 {
				errorHandler.CustomError(w, http.StatusBadRequest, "gap emi days must be between 0 - 60 days")
				return
			}
		}

		attributes := map[string]interface{}{
			"agentName":            agentName,
			"productType":          productTypeList,
			"email":                email,
			"agentCode":            agentCode,
			"password":             password,
			"mobile":               mobile,
			"file":                 file,
			"extension":            extension,
			"subvention":           subvention,
			"firstEmiAfterDays":    firstEmiAfterDays,
			"gapEmiDays":           gapEmiDays,
			"totalEmiCount":        totalEmiCount,
			"lateChargeType":       lateChargeType,
			"lateCharge":           lateCharge,
			"nodeType":             nodeType,
			"isMasked":             isMasked,
			"isInsuranceMandatory": isInsuranceMandatory,
		}

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func ListPartnersReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		userObj := r.Context().Value("user")
		lenderID := userObj.(authentication.LenderUserStruct).LenderID

		type requestStruct struct {
			Limit       []string `json:"limit"`
			Page        []string `json:"page"`
			FromDate    []string `json:"fromDate"`
			ToDate      []string `json:"toDate"`
			ProductType []string `json:"productType"`
			PartnerName []string `json:"partnerName"`
			PartnerCode []string `json:"partnerCode"`
			Mobile      []string `json:"mobile"`
			Email       []string `json:"email"`
		}

		bytes, err := json.Marshal(r.URL.Query())
		if err != nil {
			log.Println(err)
			panic(err)
		}

		var reqObj requestStruct
		err = json.Unmarshal(bytes, &reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		v := validator.New()

		err = v.Struct(reqObj)
		if err != nil {
			panic(err)
		}

		attributes := make(map[string]interface{})

		if len(reqObj.Limit) == 0 {
			attributes["limit"] = 10
		} else {
			limitValue, err := strconv.Atoi(reqObj.Limit[0])
			if err != nil {
				panic(err)
			}
			if limitValue <= 0 {
				panic("limit should be greater than 0")
			}
			attributes["limit"] = limitValue
		}

		if len(reqObj.Page) == 0 {
			attributes["page"] = 1
		} else {
			pageValue, err := strconv.Atoi(reqObj.Page[0])
			if err != nil {
				panic(err)
			}
			if pageValue <= 0 {
				panic("page should be greater than 0")
			}
			attributes["page"] = pageValue
		}

		attributes["toDate"] = ""
		attributes["fromDate"] = ""
		attributes["productType"] = ""
		attributes["partnerCode"] = ""
		attributes["partnerName"] = ""
		attributes["mobile"] = ""
		attributes["email"] = ""

		// parse dates if they exists
		if !(len(reqObj.FromDate) == 0 && len(reqObj.ToDate) == 0) {

			_, err := time.Parse(constants.DateFormat, reqObj.ToDate[0])
			if err != nil {
				log.Println("Invalid `to` date")
				panic("Invalid `to` date")
			}

			_, err = time.Parse(constants.DateFormat, reqObj.FromDate[0])
			if err != nil {
				log.Println("Invalid `from` date")
				panic("Invalid `from` date")
			}

			// panic if from is ahead of to
			if reqObj.ToDate[0] < reqObj.FromDate[0] {
				log.Println("Invalid `from` or `to` date")
				panic("Invalid `from` or `to` date")
			}

			attributes["toDate"] = reqObj.ToDate[0]
			attributes["fromDate"] = reqObj.FromDate[0]

		}

		if len(reqObj.ProductType) > 0 && len(reqObj.ProductType[0]) > 0 {
			productType := general.RemoveAllSpaces(reqObj.ProductType[0])
			if lenderID != constants.MFLBLID && !general.InArr(productType, []string{constants.LoanTypePersonalLoan, constants.LoanTypeBusinessLoan, constants.LoanTypeCreditLine, constants.LoanTypeEducationLoan}) {
				errorHandler.CustomError(w, http.StatusBadRequest, "invalid product type: "+productType)
				return
			}
			if lenderID == constants.MFLBLID && !general.InArr(productType, constants.ValidLoanTypeForMFLBLenderID) {
				errorHandler.CustomError(w, http.StatusBadRequest, "invalid product type: "+productType)
				return
			}
			attributes["productType"] = productType
		}
		if len(reqObj.PartnerCode) > 0 {
			attributes["partnerCode"] = strings.ToLower(reqObj.PartnerCode[0])
		}
		if len(reqObj.PartnerName) > 0 {
			attributes["partnerName"] = strings.ToLower(reqObj.PartnerName[0])
		}
		if len(reqObj.Mobile) > 0 {
			attributes["mobile"] = strings.ToLower(reqObj.Mobile[0])
		}
		if len(reqObj.Email) > 0 {
			attributes["email"] = strings.ToLower(reqObj.Email[0])
		}

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func CheckExistingMDEmailReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			Email string `json:"email" validate:"required"`
		}

		v := validator.New()
		decoder := json.NewDecoder(r.Body)
		defer func(Body io.ReadCloser) {
			err := Body.Close()
			if err != nil {
				log.Println(err)
			}
		}(r.Body)
		var reqObj requestStruct
		err := decoder.Decode(&reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}
		err = v.Struct(reqObj)
		if err != nil {
			panic(err)
		}

		if !general.ValidateEmail(reqObj.Email) {
			errorHandler.CustomError(w, http.StatusBadRequest, "invalid email")
			return
		}

		attributes := map[string]string{
			"email": reqObj.Email,
		}
		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func UpdateDSACredsReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			Email string `json:"email"`
			DSAID string `json:"dsaID"`
		}

		decoder := json.NewDecoder(r.Body)
		defer func(Body io.ReadCloser) {
			err := Body.Close()
			if err != nil {
				log.Println(err)
			}
		}(r.Body)
		var reqObj requestStruct
		err := decoder.Decode(&reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		if !general.ValidateEmail(reqObj.Email) {
			errorHandler.CustomError(w, http.StatusBadRequest, "invalid email")
			return
		}
		if !general.ValidateUUID(reqObj.DSAID) {
			errorHandler.CustomError(w, http.StatusBadRequest, "invalid dsa")
			return
		}
		reqObj.Email = strings.ToLower(general.RemoveAllSpaces(reqObj.Email))

		attributes := map[string]string{
			"email": reqObj.Email,
			"dsaID": reqObj.DSAID,
		}
		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func CloseApplicationReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			LoanApplicationID string `json:"loanApplicationID" validate:"required"`
			Remark            string `json:"remark" validate:"required"`
			ReferenceID       string `json:"referenceID"`
		}

		decoder := json.NewDecoder(r.Body)
		defer r.Body.Close()
		var reqObj requestStruct
		err := decoder.Decode(&reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		if !general.ValidateUUID(reqObj.LoanApplicationID) {
			errorHandler.CustomError(w, http.StatusBadRequest, "invalid loanApplicationID")
			return
		}

		attributes := map[string]string{
			"loanApplicationID": reqObj.LoanApplicationID,
			"remark":            reqObj.Remark,
			"referenceID":       reqObj.ReferenceID,
		}
		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func GetPincodeResponseFileReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			ActivityID []string `json:"activityID"`
		}

		bytes, err := json.Marshal(r.URL.Query())
		if err != nil {
			log.Println(err)
			panic(err)
		}

		var reqObj requestStruct
		err = json.Unmarshal(bytes, &reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		if len(reqObj.ActivityID) == 0 {
			errorHandler.CustomError(w, http.StatusBadRequest, "blank activity id")
			return
		}

		if !general.ValidateUUID(reqObj.ActivityID[0]) {
			errorHandler.CustomError(w, http.StatusBadRequest, "invalid activity id")
			return
		}

		attributes := map[string]string{
			"activityID": reqObj.ActivityID[0],
		}

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func PreApproveReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		// csv checks
		_, header, err := r.FormFile("file")
		if err != nil {
			errorHandler.CustomError(w, http.StatusBadRequest, "file field not found")
			return
		}

		if header == nil {
			log.Println("header is null")
			errorHandler.CustomError(w, http.StatusBadRequest, "couldn't determine the file headers, please try again")
			return
		}

		fileNameArr := strings.Split(header.Filename, ".")
		if len(fileNameArr) < 2 {
			log.Println("extension not found for", header.Filename)
			errorHandler.CustomError(w, http.StatusBadRequest, "couldn't check the file extension, please try again")
			return
		}
		extension := strings.ToLower(fileNameArr[1])
		if extension != "csv" {
			errorHandler.CustomError(w, http.StatusBadRequest, "please upload a csv file")
			return
		}

		attributes := map[string]string{
			"email": r.PostForm.Get("email"),
		}

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func GetTopUpEligibilityReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			Limit             []string `json:"limit"`
			Page              []string `json:"page"`
			LoanApplicationNo []string `json:"loanApplicationNo"`
			FromDate          []string `json:"fromDate"`
			ToDate            []string `json:"toDate"`
		}

		bytes, err := json.Marshal(r.URL.Query())
		if err != nil {
			log.Println(err)
			panic(err)
		}

		var reqObj requestStruct
		err = json.Unmarshal(bytes, &reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		//pageValue default1?
		attributes := make(map[string]interface{})
		if len(reqObj.Page) == 0 {
			attributes["page"] = 1
		} else {
			pageValue, err := strconv.Atoi(reqObj.Page[0])
			if err != nil {
				panic(err)
			}
			if pageValue == 0 {
				pageValue = 1
			} else if pageValue < 0 {
				panic("page should be greater than 0")
			}
			attributes["page"] = pageValue
		}
		if len(reqObj.Limit) == 0 {
			attributes["limit"] = 10
		} else {
			limitValue, err := strconv.Atoi(reqObj.Limit[0])
			if err != nil {
				panic(err)
			}
			if limitValue == 0 {
				limitValue = 1
			} else if limitValue < 0 {
				panic("limit should be greater than 0")
			}
			attributes["limit"] = limitValue
		}

		if !(len(reqObj.FromDate) == 0 && len(reqObj.ToDate) == 0) {

			_, err := time.Parse(constants.DateFormat, reqObj.ToDate[0])
			if err != nil {
				logger.WithRequest(r).Error("Invalid `to` date")
				panic("Invalid `to` date")
			}

			_, err = time.Parse(constants.DateFormat, reqObj.FromDate[0])
			if err != nil {
				logger.WithRequest(r).Error("Invalid `from` date")
				panic("Invalid `from` date")
			}

			// panic if from is ahead of to
			if reqObj.ToDate[0] < reqObj.FromDate[0] {
				logger.WithRequest(r).Error("Invalid `from` or `to` date")
				panic("Invalid `from` or `to` date")
			}
			attributes["to"] = reqObj.ToDate[0]
			attributes["from"] = reqObj.FromDate[0]
		} else {
			attributes["to"] = ""
			attributes["from"] = ""
		}
		if len(reqObj.LoanApplicationNo) > 0 {
			attributes["loanApplicationNo"] = reqObj.LoanApplicationNo[0]
		} else {
			attributes["loanApplicationNo"] = ""
		}

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// PostTopUpEligibilityReq validates csv file
func PostTopUpEligibilityReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		if err := r.ParseMultipartForm(10 << 20); err != nil {
			errorHandler.CustomError(w, http.StatusBadRequest, "Error parsing form data")
			return
		}

		_, header, err := r.FormFile("file")
		if err != nil {
			errorHandler.CustomError(w, http.StatusBadRequest, "File field not found")
			return
		}
		if header == nil {
			errorHandler.CustomError(w, http.StatusBadRequest, "Couldn't determine the file headers")
			return
		}

		fileNameArr := strings.Split(header.Filename, ".")
		if len(fileNameArr) < 2 {
			errorHandler.CustomError(w, http.StatusBadRequest, "Couldn't check the file extension")
			return
		}

		extension := strings.ToLower(fileNameArr[1])
		if extension != "csv" {
			errorHandler.CustomError(w, http.StatusBadRequest, "Please upload a csv file")
			return
		}

		file, err := header.Open()
		if err != nil {
			errorHandler.CustomError(w, http.StatusBadRequest, "Couldn't open the file")
			return
		}

		defer file.Close()
		attributes := map[string]interface{}{}
		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func GetGroupPermissionsReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			GroupName string `json:"groupName"`
		}

		decoder := json.NewDecoder(r.Body)
		defer func(Body io.ReadCloser) {
			err := Body.Close()
			if err != nil {
				log.Println(err)
			}
		}(r.Body)
		var reqObj requestStruct
		err := decoder.Decode(&reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		if len(reqObj.GroupName) == 0 {
			errorHandler.CustomError(w, http.StatusBadRequest, "empty group name")
			return
		}

		lenderID := r.Context().Value("user").(authentication.LenderUserStruct).LenderID
		reqObj.GroupName = fmt.Sprintf("%s%s%s", lenderID, rbacConstants.RBACSeparator, reqObj.GroupName)

		attributes := map[string]interface{}{
			"groupName": reqObj.GroupName,
		}
		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func AddNewGroupPermissionsReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			GroupName       string                             `json:"groupName"`
			GroupPermission []rbacLenderTypes.GroupPermissions `json:"groupPermission"`
		}

		decoder := json.NewDecoder(r.Body)
		defer func(Body io.ReadCloser) {
			err := Body.Close()
			if err != nil {
				log.Println(err)
			}
		}(r.Body)
		var reqObj requestStruct
		err := decoder.Decode(&reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		if len(reqObj.GroupName) < 5 || len(reqObj.GroupName) > 50 {
			errorHandler.CustomError(w, http.StatusBadRequest, rbacErrors.ErrInvalidGroupNameLength.Error())
			return
		}

		if len(reqObj.GroupPermission) == 0 {
			errorHandler.CustomError(w, http.StatusBadRequest, rbacErrors.ErrEmptyGroupPermissions.Error())
			return
		}

		lenderID := r.Context().Value("user").(authentication.LenderUserStruct).LenderID
		reqObj.GroupName = fmt.Sprintf("%s%s%s", lenderID, rbacConstants.RBACSeparator, reqObj.GroupName)

		attributes := map[string]interface{}{
			"groupName":       reqObj.GroupName,
			"groupPermission": reqObj.GroupPermission,
		}
		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func UpdateGroupPermissionsReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			GroupName       string                             `json:"groupName"`
			GroupPermission []rbacLenderTypes.GroupPermissions `json:"groupPermission"`
		}

		decoder := json.NewDecoder(r.Body)
		defer func(Body io.ReadCloser) {
			err := Body.Close()
			if err != nil {
				log.Println(err)
			}
		}(r.Body)
		var reqObj requestStruct
		err := decoder.Decode(&reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		if len(reqObj.GroupName) == 0 {
			errorHandler.CustomError(w, http.StatusBadRequest, rbacErrors.ErrEmptyGroupName.Error())
			return
		}

		if len(reqObj.GroupPermission) == 0 {
			errorHandler.CustomError(w, http.StatusBadRequest, rbacErrors.ErrEmptyGroupPermissions.Error())
			return
		}

		if general.InArr(reqObj.GroupName, rbacConstants.RbacDefaultLenderGroups) {
			errorHandler.CustomError(w, http.StatusBadRequest, rbacErrors.ErrDefaultGroupPermissionUpdateNotAllowed.Error())
			return
		}

		lenderID := r.Context().Value("user").(authentication.LenderUserStruct).LenderID
		reqObj.GroupName = fmt.Sprintf("%s%s%s", lenderID, rbacConstants.RBACSeparator, reqObj.GroupName)

		attributes := map[string]interface{}{
			"groupName":       reqObj.GroupName,
			"groupPermission": reqObj.GroupPermission,
		}
		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func DeleteGroupPermissionsReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			GroupName       string                             `json:"groupName"`
			GroupPermission []rbacLenderTypes.GroupPermissions `json:"groupPermission"`
		}

		decoder := json.NewDecoder(r.Body)
		defer func(Body io.ReadCloser) {
			err := Body.Close()
			if err != nil {
				log.Println(err)
			}
		}(r.Body)
		var reqObj requestStruct
		err := decoder.Decode(&reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		if len(reqObj.GroupName) == 0 {
			errorHandler.CustomError(w, http.StatusBadRequest, rbacErrors.ErrEmptyGroupName.Error())
			return
		}

		if len(reqObj.GroupPermission) == 0 {
			errorHandler.CustomError(w, http.StatusBadRequest, rbacErrors.ErrEmptyGroupPermissions.Error())
			return
		}

		if general.InArr(reqObj.GroupName, rbacConstants.RbacDefaultLenderGroups) {
			errorHandler.CustomError(w, http.StatusBadRequest, rbacErrors.ErrDefaultGroupPermissionUpdateNotAllowed.Error())
			return
		}

		lenderID := r.Context().Value("user").(authentication.LenderUserStruct).LenderID
		reqObj.GroupName = fmt.Sprintf("%s%s%s", lenderID, rbacConstants.RBACSeparator, reqObj.GroupName)

		attributes := map[string]interface{}{
			"groupName":       reqObj.GroupName,
			"groupPermission": reqObj.GroupPermission,
		}
		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func DeleteGroupReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			GroupName string `json:"groupName"`
		}

		decoder := json.NewDecoder(r.Body)
		defer func(Body io.ReadCloser) {
			err := Body.Close()
			if err != nil {
				log.Println(err)
			}
		}(r.Body)
		var reqObj requestStruct
		err := decoder.Decode(&reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		if len(reqObj.GroupName) == 0 {
			errorHandler.CustomError(w, http.StatusBadRequest, rbacErrors.ErrEmptyGroupName.Error())
			return
		}

		if general.InArr(reqObj.GroupName, rbacConstants.RbacDefaultLenderGroups) {
			errorHandler.CustomError(w, http.StatusBadRequest, rbacErrors.ErrDefaultGroupDeletionNotAllowed.Error())
			return
		}

		lenderID := r.Context().Value("user").(authentication.LenderUserStruct).LenderID
		reqObj.GroupName = fmt.Sprintf("%s%s%s", lenderID, rbacConstants.RBACSeparator, reqObj.GroupName)

		attributes := map[string]interface{}{
			"groupName": reqObj.GroupName,
		}
		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func DeleteUsersReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			Emails []string `json:"emails"`
		}

		decoder := json.NewDecoder(r.Body)
		defer func(Body io.ReadCloser) {
			err := Body.Close()
			if err != nil {
				log.Println(err)
			}
		}(r.Body)
		var reqObj requestStruct
		err := decoder.Decode(&reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		if len(reqObj.Emails) == 0 {
			errorHandler.CustomError(w, http.StatusBadRequest, rbacErrors.ErrEmailMissing.Error())
			return
		}

		for _, email := range reqObj.Emails {
			if !general.ValidateEmail(email) {
				errorHandler.CustomError(w, http.StatusBadRequest, rbacErrors.ErrInvalidEmailID.Error())
				return
			}
		}

		attributes := map[string]interface{}{
			"emails": reqObj.Emails,
		}
		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func ActivateUsersReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			Emails []string `json:"emails"`
		}

		decoder := json.NewDecoder(r.Body)
		defer func(Body io.ReadCloser) {
			err := Body.Close()
			if err != nil {
				log.Println(err)
			}
		}(r.Body)
		var reqObj requestStruct
		err := decoder.Decode(&reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		if len(reqObj.Emails) == 0 {
			errorHandler.CustomError(w, http.StatusBadRequest, rbacErrors.ErrEmailMissing.Error())
			return
		}

		for _, email := range reqObj.Emails {
			if !general.ValidateEmail(email) {
				errorHandler.CustomError(w, http.StatusBadRequest, rbacErrors.ErrInvalidEmailID.Error())
				return
			}
		}

		attributes := map[string]interface{}{
			"emails": reqObj.Emails,
		}
		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func UpdateUserGroupReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			GroupName string `json:"groupName"`
			Email     string `json:"email"`
		}

		decoder := json.NewDecoder(r.Body)
		defer func(Body io.ReadCloser) {
			err := Body.Close()
			if err != nil {
				log.Println(err)
			}
		}(r.Body)
		var reqObj requestStruct
		err := decoder.Decode(&reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		if len(reqObj.GroupName) == 0 {
			errorHandler.CustomError(w, http.StatusBadRequest, rbacErrors.ErrEmptyGroupName.Error())
			return
		}

		if len(reqObj.Email) == 0 {
			errorHandler.CustomError(w, http.StatusBadRequest, rbacErrors.ErrEmailMissing.Error())
			return
		}

		if !general.ValidateEmail(reqObj.Email) {
			errorHandler.CustomError(w, http.StatusBadRequest, rbacErrors.ErrInvalidEmailID.Error())
			return
		}

		lenderID := r.Context().Value("user").(authentication.LenderUserStruct).LenderID
		reqObj.GroupName = fmt.Sprintf("%s%s%s", lenderID, rbacConstants.RBACSeparator, reqObj.GroupName)

		attributes := map[string]interface{}{
			"groupName": reqObj.GroupName,
			"email":     reqObj.Email,
		}
		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func GetAllUsersReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			Limit         []string `json:"limit"`
			Page          []string `json:"page"`
			Name          []string `json:"name"`
			Email         []string `json:"email"`
			RbacGroupName []string `json:"rbacGroupName"`
			Status        []string `json:"status"`
		}

		bytes, err := json.Marshal(r.URL.Query())
		if err != nil {
			log.Println(err)
			panic(err)
		}

		var reqObj requestStruct
		err = json.Unmarshal(bytes, &reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		v := validator.New()

		err = v.Struct(reqObj)
		if err != nil {
			panic(err)
		}

		attributes := make(map[string]interface{})

		if len(reqObj.Limit) > 0 {
			limit, err := strconv.Atoi(reqObj.Limit[0])
			if err != nil {
				panic("error in parsing limit")
			}

			if limit <= 0 {
				panic("limit should be greater than 0")
			}

			attributes["limit"] = limit
		} else {
			attributes["limit"] = 10
		}

		if len(reqObj.Page) > 0 {
			page, err := strconv.Atoi(reqObj.Page[0])
			if err != nil {
				panic("error in parsing page")
			}

			if page <= 0 {
				panic("page should be greater than 0")
			}
			attributes["page"] = page
		} else {
			attributes["page"] = 1
		}

		if len(reqObj.Name) > 0 {
			attributes["name"] = reqObj.Name[0]
		} else {
			attributes["name"] = ""
		}

		if len(reqObj.Email) > 0 {
			attributes["email"] = reqObj.Email[0]
		} else {
			attributes["email"] = ""
		}

		if len(reqObj.RbacGroupName) > 0 {
			attributes["rbac_group_name"] = reqObj.RbacGroupName[0]
		} else {
			attributes["rbac_group_name"] = ""
		}

		attributes["status"] = -1
		if len(reqObj.Status) > 0 {
			switch reqObj.Status[0] {
			case "ACTIVE":
				{
					attributes["status"] = 1
					break
				}
			case "INACTIVE":
				{
					attributes["status"] = 0
					break
				}
			default:
				{
					errorHandler.CustomError(w, http.StatusBadRequest, rbacErrors.ErrGetAllUserInvalidStatus.Error())
					return
				}
			}
		}

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func UpdateMFAStatusReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			Emails    []string `json:"emails"`
			MFAStatus int      `json:"mfaStatus"`
			AllUsers  bool     `json:"allUsers"`
		}

		decoder := json.NewDecoder(r.Body)
		defer func(Body io.ReadCloser) {
			err := Body.Close()
			if err != nil {
				log.Println(err)
			}
		}(r.Body)
		var reqObj requestStruct
		err := decoder.Decode(&reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		if !reqObj.AllUsers && len(reqObj.Emails) == 0 {
			errorHandler.CustomError(w, http.StatusBadRequest, rbacErrors.ErrEmailMissing.Error())
			return
		}

		if reqObj.AllUsers {
			reqObj.Emails = []string{}
		}

		if !general.IsValidMfaStatus(reqObj.MFAStatus, constants.LenderDashboardRef) {
			errorHandler.CustomError(w, http.StatusBadRequest, rbacErrors.ErrInvalidMFAStatus.Error())
			return
		}

		for _, email := range reqObj.Emails {
			if !general.ValidateEmail(email) {
				errorHandler.CustomError(w, http.StatusBadRequest, rbacErrors.ErrInvalidEmailID.Error())
				return
			}
		}

		attributes := map[string]interface{}{
			"emails":    reqObj.Emails,
			"mfaStatus": reqObj.MFAStatus,
			"allUsers":  reqObj.AllUsers,
		}
		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func ValidateGSTINReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)
		type requestStruct struct {
			Gstin string `json:"gstin"`
		}
		decoder := json.NewDecoder(r.Body)
		defer r.Body.Close()
		var reqObj requestStruct
		err := decoder.Decode(&reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}
		gstin := strings.ToUpper(reqObj.Gstin)
		if !general.ValidateGSTIN(gstin) {
			errorHandler.CustomError(w, http.StatusBadRequest, "invalid gstin")
			return
		}

		attributes := make(map[string]interface{})
		attributes["gstin"] = reqObj.Gstin

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func ValidateBankAccountReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			BankAccountNo     string `json:"bankAccountNo"`
			IfscCode          string `json:"ifscCode"`
			AccountHolderName string `json:"accountHolderName"`
		}

		decoder := json.NewDecoder(r.Body)
		defer r.Body.Close()
		var reqObj requestStruct
		err := decoder.Decode(&reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		if !general.ValidateIFSC(reqObj.IfscCode) {
			errorHandler.CustomError(w, http.StatusBadRequest, "invalid ifsc code")
			return
		}

		if !general.ValidateAccountNumber(reqObj.BankAccountNo) {
			errorHandler.CustomError(w, http.StatusBadRequest, "invalid bank account number")
			return
		}

		if reqObj.AccountHolderName == "" {
			errorHandler.CustomError(w, http.StatusBadRequest, "account holder name cannot be empty")
			return
		}

		attributes := make(map[string]string)
		attributes["bankAccountNo"] = reqObj.BankAccountNo
		attributes["ifscCode"] = reqObj.IfscCode
		attributes["accountHolderName"] = reqObj.AccountHolderName
		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func UpdateManualReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		type requestStruct struct {
			LoanApplicationID string `json:"loanApplicationID" validate:"required"`
			KycStatus         string `json:"kycStatus"`
			RejectionReason   string `json:"rejectionReason"`
			Remarks           string `json:"remarks"`
		}

		v := validator.New()
		decoder := json.NewDecoder(r.Body)
		defer r.Body.Close()
		var reqObj requestStruct
		err := decoder.Decode(&reqObj)
		if err != nil {
			logger.Log.WithContext(ctx).Errorf("[UpdateManualReq] failed to parse request. err: %v", err)
			errorHandler.CustomError(w, http.StatusBadRequest, "failed to parse request")
			return
		}

		err = v.Struct(reqObj)
		if err != nil {
			logger.Log.WithContext(ctx).Errorf("[UpdateManualReq] failed to parse request. err: %v", err)
			errorHandler.CustomError(w, http.StatusBadRequest, "failed to parse request")
			return
		}
		if !apistack.IsValidManualKYCStatus(&reqObj.KycStatus) {
			errorHandler.CustomErrorV3(w, constants.ErrStringToStatusCodeMapping[constants.ErrStringInvalidManualKYCStatus], constants.ErrStringToStatusCodeStringMapping[constants.ErrStringInvalidManualKYCStatus], constants.ErrStringInvalidManualKYCStatus)
			return
		}
		attributes := map[string]interface{}{
			"loanApplicationID": reqObj.LoanApplicationID,
			"kycStatus":         reqObj.KycStatus,
			"rejectionReason":   reqObj.RejectionReason,
			"remarks":           reqObj.Remarks,
		}

		ctx = context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func ApproveLoanKYCReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			LoanApplicationID string `json:"loanApplicationID" validate:"required"`
			IsDoubleName      bool   `json:"isDoubleName"`
		}

		v := validator.New()
		decoder := json.NewDecoder(r.Body)
		defer r.Body.Close()
		var reqObj requestStruct
		err := decoder.Decode(&reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		err = v.Struct(reqObj)
		if err != nil {
			panic(err)
		}
		attributes := map[string]interface{}{
			"loanApplicationID": reqObj.LoanApplicationID,
			"isDoubleName":      reqObj.IsDoubleName,
		}

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func SubmitKYCReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			EKYC                      bool                           `json:"ekyc"`
			Documents                 []structs.DocumentUploadStruct `json:"documents"`
			LoanApplicationID         string                         `json:"loanApplicationID"`
			ConsentText               string                         `json:"consentText"` // required for kyc submit
			Lat                       string                         `json:"lat"`
			Lon                       string                         `json:"lon"`
			Height                    string                         `json:"height"`
			Accuracy                  string                         `json:"accuracy"`
			IsCurrentAndPermanentSame *bool                          `json:"isCurrentAndPermanentSame"`
		}

		decoder := json.NewDecoder(r.Body)
		defer r.Body.Close()
		var reqObj requestStruct
		err := decoder.Decode(&reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		if reqObj.LoanApplicationID == "" {
			errorHandler.CustomError(w, http.StatusBadRequest, "loanApplicationID cannot be empty")
			return
		}

		defaultValue := true
		if reqObj.IsCurrentAndPermanentSame == nil {
			reqObj.IsCurrentAndPermanentSame = &defaultValue
		}

		attributes := map[string]interface{}{
			"ekyc":                      reqObj.EKYC,
			"documents":                 reqObj.Documents,
			"loanApplicationID":         reqObj.LoanApplicationID,
			"consentText":               reqObj.ConsentText,
			"lat":                       reqObj.Lat,
			"lon":                       reqObj.Lon,
			"height":                    reqObj.Height,
			"accuracy":                  reqObj.Accuracy,
			"isCurrentAndPermanentSame": *reqObj.IsCurrentAndPermanentSame,
		}

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func RejectSingleDocReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			LoanApplicationID string `json:"loanApplicationID" validate:"required"`
			LoanKYCDetailsID  string `json:"loanKycDetailsID" validate:"required"`
			RejectionReason   string `json:"rejectionReason" validate:"required"`
		}

		v := validator.New()
		decoder := json.NewDecoder(r.Body)
		defer r.Body.Close()
		var reqObj requestStruct
		err := decoder.Decode(&reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		err = v.Struct(reqObj)
		if err != nil {
			panic(err)
		}

		attributes := map[string]string{
			"loanApplicationID": reqObj.LoanApplicationID,
			"loanKYCDetailsID":  reqObj.LoanKYCDetailsID,
			"rejectionReason":   reqObj.RejectionReason,
		}
		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func GetWFsReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type reqModel struct {
			ResourceID   []string `json:"resourceID" validate:"required"`
			ResourceType []string `json:"resourceType" validate:"required"`
		}
		attributes, err := general.ExtractRequestParams[reqModel](r.URL.Query())
		errorHandler.LogErrorAndPanic(err)

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func GetWFTimelineReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type reqModel struct {
			RequestID []string `json:"requestID" validate:"required"`
		}
		attributes, err := general.ExtractRequestParams[reqModel](r.URL.Query())
		errorHandler.LogErrorAndPanic(err)

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func UpdateWorkFlowStatusReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			LoanApplicationID string   `json:"loanApplicationID" validate:"required"`
			ServiceRequestID  string   `json:"serviceRequestID" validate:"required"`
			Comment           string   `json:"comments"`
			RiskRating        string   `json:"riskRating"`
			RejectionReason   []string `json:"rejectionReason"`
		}

		urlpath := r.URL.EscapedPath()[strings.LastIndex(r.URL.EscapedPath(), "/"):]

		v := validator.New()
		decoder := json.NewDecoder(r.Body)
		defer func(Body io.ReadCloser) {
			err := Body.Close()
			if err != nil {
				log.Errorln(err)
			}
		}(r.Body)
		var reqObj requestStruct
		err := decoder.Decode(&reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		err = v.Struct(reqObj)
		if err != nil {
			panic(err)
		}

		if !general.ValidateUUID(reqObj.LoanApplicationID) {
			errorHandler.CustomError(w, http.StatusBadRequest, "invalid loanApplicationID")
			return
		}

		if urlpath == "/approveManualCreditReviewDetails" {
			if reqObj.RiskRating == "" {
				errorHandler.CustomError(w, http.StatusBadRequest, "risk rating is mandatory")
				return
			}
		}

		if urlpath == "/rejectManualCreditReviewDetails" {
			if len(reqObj.RejectionReason) == 0 {
				errorHandler.CustomError(w, http.StatusBadRequest, "rejection reason is mandatory")
				return
			}
		}

		var attributes = make(map[string]interface{})
		attributes["loanApplicationID"] = reqObj.LoanApplicationID
		attributes["serviceRequestID"] = reqObj.ServiceRequestID
		attributes["comments"] = reqObj.Comment
		attributes["riskRating"] = reqObj.RiskRating
		attributes["rejectionReason"] = reqObj.RejectionReason

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func BreWorkflowStateTransitionReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			LoanApplicationID string `json:"loanApplicationID" validate:"required"`
			ServiceRequestID  string `json:"serviceRequestID" validate:"required"`
			Comment           string `json:"comments" validate:"required"`
			ActionTo          string `json:"actionTo" validate:"required"`
		}

		v := validator.New()
		decoder := json.NewDecoder(r.Body)
		defer func(Body io.ReadCloser) {
			err := Body.Close()
			if err != nil {
				log.Errorln(err)
			}
		}(r.Body)
		var reqObj requestStruct
		err := decoder.Decode(&reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		err = v.Struct(reqObj)
		if err != nil {
			panic(err)
		}

		if !general.ValidateUUID(reqObj.LoanApplicationID) {
			errorHandler.CustomError(w, http.StatusBadRequest, "invalid loanApplicationID")
			return
		}

		// if !general.InArr(reqObj.EventType, constants.BREValidEventTypes) {
		// 	errorHandler.CustomError(w, http.StatusBadRequest, "invalid event type")
		// 	return
		// }

		attributes := make(map[string]interface{})
		attributes["loanApplicationID"] = reqObj.LoanApplicationID
		attributes["serviceRequestID"] = reqObj.ServiceRequestID
		attributes["comments"] = reqObj.Comment
		attributes["actionTo"] = reqObj.ActionTo

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// WorkflowRejectionReversalReq request for workflow rejection reversal.
func WorkflowRejectionReversalReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			LoanApplicationID string `json:"loanApplicationID"`
			ServiceRequestID  string `json:"serviceRequestID"`
		}

		decoder := json.NewDecoder(r.Body)
		defer func(Body io.ReadCloser) {
			err := Body.Close()
			if err != nil {
				log.Errorln(err)
			}
		}(r.Body)
		var reqObj requestStruct
		err := decoder.Decode(&reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		if !general.ValidateUUID(reqObj.LoanApplicationID) {
			errorHandler.CustomError(w, http.StatusBadRequest, "invalid loanApplicationID")
			return
		}

		attributes := make(map[string]string)
		attributes["loanApplicationID"] = reqObj.LoanApplicationID
		attributes["serviceRequestID"] = reqObj.ServiceRequestID

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func UpdateAdditionalKYCDocReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type req struct {
			DocID             string   `json:"documentID" validate:"required"`
			LoanApplicationID string   `json:"loanApplicationID" validate:"required"`
			MediaIDs          []string `json:"mediaIDs"`
			Status            int      `json:"status"`
			Action            string   `json:"action"`
			Comment           string   `json:"comment"`
			TargetGroup       string   `json:"targetGroup"`
		}
		reqObj, err := general.ExtractRequestBody[req](r)
		if err != nil {
			errorHandler.CustomError(w, http.StatusBadRequest, err.Error())
			return
		}

		if !general.InArr(reqObj.Status, []int{constants.DocumentNotVerified, constants.DocumentSampled,
			constants.DocumentScreened, constants.DocumentUnderReview, constants.DocumentSampledPositive,
			constants.DocumentSampledRefer, constants.DocumentSampledNegative, constants.DocumentSampledDecline}) {
			errorHandler.CustomError(w, http.StatusBadRequest, "invalid document status")
			return
		}

		if !general.ValidateUUID(reqObj.DocID) {
			errorHandler.CustomError(w, http.StatusBadRequest, "invalid document ID")
			return
		}

		attributes := make(map[string]interface{})
		attributes["documentID"] = reqObj.DocID
		attributes["loanApplicationID"] = reqObj.LoanApplicationID
		attributes["mediaIDs"] = reqObj.MediaIDs
		attributes["status"] = reqObj.Status
		attributes["action"] = reqObj.Action
		attributes["comment"] = reqObj.Comment
		attributes["targetGroup"] = reqObj.TargetGroup

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func LoanDedupeChecksReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			LoanApplicationID []string `json:"loanApplicationID"`
			DedupeType        []string `json:"type"`
		}

		bytes, err := json.Marshal(r.URL.Query())
		if err != nil {
			log.Println(err)
			panic(err)
		}

		var reqObj requestStruct
		err = json.Unmarshal(bytes, &reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		v := validator.New()

		err = v.Struct(reqObj)
		if err != nil {
			panic(err)
		}

		attributes := make(map[string][]string)
		attributes["loanApplicationID"] = reqObj.LoanApplicationID
		attributes["type"] = reqObj.DedupeType

		if !general.ValidateUUID(attributes["loanApplicationID"][0]) {
			errorHandler.CustomError(w, http.StatusBadRequest, "invalid loanApplicationID")
			return
		}

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func UpdateBusinessIndustryReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			LoanApplicationID  string `json:"loanApplicationID" validate:"required"`
			Industry           string `json:"industry" validate:"required"`
			SubIndustry        string `json:"subIndustry" validate:"required"`
			RiskClassification string `json:"riskClassification" validate:"required"`
			RiskCategorization string `json:"riskCategorization" validate:"required"`
		}
		v := validator.New()
		decoder := json.NewDecoder(r.Body)
		defer r.Body.Close()
		var reqObj requestStruct
		err := decoder.Decode(&reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}
		err = v.Struct(reqObj)
		if err != nil {
			panic(err)
		}

		attributes := map[string]string{
			"loanApplicationID":  reqObj.LoanApplicationID,
			"industry":           reqObj.Industry,
			"subIndustry":        reqObj.SubIndustry,
			"riskClassification": reqObj.RiskClassification,
			"riskCategorization": reqObj.RiskCategorization,
		}

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func UpdateInsuranceDetailsReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			LoanApplicationID string  `json:"loanApplicationID" validate:"required"`
			InsuranceID       string  `json:"insuranceID" validate:"required"`
			Premium           float64 `json:"premium"  validate:"required"`
		}

		v := validator.New()

		decoder := json.NewDecoder(r.Body)
		defer r.Body.Close()
		var reqObj requestStruct
		err := decoder.Decode(&reqObj)
		if err != nil {
			panic(err)
		}
		err = v.Struct(reqObj)
		if err != nil {
			log.Errorln(err)
		}

		if !general.ValidateUUID(reqObj.LoanApplicationID) {
			errorHandler.CustomError(w, http.StatusBadRequest, "invalid loanApplicationID")
			return
		}

		if !general.ValidateUUID(reqObj.InsuranceID) {
			errorHandler.CustomError(w, http.StatusBadRequest, "invalid insuranceID")
			return
		}

		if reqObj.Premium <= 0 {
			errorHandler.CustomError(w, http.StatusBadRequest, "premium should be greater than 0")
			return
		}

		attributes := map[string]interface{}{
			"loanApplicationID": reqObj.LoanApplicationID,
			"insuranceID":       reqObj.InsuranceID,
			"premium":           reqObj.Premium,
		}

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func GetRequestBody[T any](next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		// parse request object
		reqObj, err := general.ExtractRequestBody[T](r)
		errorHandler.LogErrorAndPanic(err)

		// validate
		v := validator.New()
		err = v.Struct(reqObj)
		errorHandler.LogErrorAndPanic(err)

		// pass to controller
		ctx := context.WithValue(r.Context(), "attributes", reqObj)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func GetQueryParams[T any](next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		// parse request object
		reqObj, err := general.ExtractRequestParamsWithStruct[T](r.URL.Query())
		errorHandler.LogErrorAndPanic(err)

		// validate
		v := validator.New()
		err = v.Struct(reqObj)
		errorHandler.LogErrorAndPanic(err)

		// pass to controller
		ctx := context.WithValue(r.Context(), "attributes", reqObj)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func CustomReportReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			FromDate string `json:"from"`
			ToDate   string `json:"to"`
			Filter   string `json:"filter"`
		}

		decoder := json.NewDecoder(r.Body)
		defer r.Body.Close()
		var reqObj requestStruct
		err := decoder.Decode(&reqObj)
		if err != nil {
			panic(err)
		}
		var attributes = make(map[string]interface{})

		// Filter should be present and be in the valid filter list
		if len(reqObj.Filter) == 0 || !general.InArr(reqObj.Filter, []string{"created_at", "updated_at"}) {
			log.Println("invalid filter type ")
			errorHandler.CustomError(w, http.StatusBadRequest, "invalid filter type")
			return
		}

		attributes["filter"] = reqObj.Filter

		// parse dates if they exist
		if len(reqObj.FromDate) != 0 && len(reqObj.ToDate) != 0 {

			toDateTime, err := time.Parse(time.RFC3339, reqObj.ToDate)
			if err != nil {
				log.Println("invalid `to` date")
				errorHandler.CustomError(w, http.StatusBadRequest, "invalid `to` date")
				return
			}

			fromDateTime, err := time.Parse(time.RFC3339, reqObj.FromDate)
			if err != nil {
				log.Println("invalid `from` date")
				errorHandler.CustomError(w, http.StatusBadRequest, "invalid `from` date")
				return
			}

			// panic if from is ahead of to
			if reqObj.ToDate < reqObj.FromDate {
				log.Println("invalid `from` or `to` date")
				errorHandler.CustomError(w, http.StatusBadRequest, "invalid `from` or `to` date")
				return
			}

			reportDurationInDays := toDateTime.Sub(fromDateTime).Hours() / 24
			if reportDurationInDays > 93 { // 3 months
				log.Errorln("report requested for a period greater than defined threshold: ", reportDurationInDays)
				errorHandler.CustomError(w, http.StatusConflict, "Report requested for a period greater than allowed threshold. Please try with a time range less than 3 months.")
				return
			}

			attributes["to"] = reqObj.ToDate
			attributes["from"] = reqObj.FromDate
		} else {
			errorHandler.CustomError(w, http.StatusBadRequest, "invalid `from` or `to` date")
			return
		}
		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func MarkNACHSuccessReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			UMRN              string `json:"umrn"`
			LoanApplicationID string `json:"loanApplicationID"`
		}

		decoder := json.NewDecoder(r.Body)
		defer r.Body.Close()
		var reqObj requestStruct
		err := decoder.Decode(&reqObj)
		if err != nil {
			panic(err)
		}

		if !general.ValidateUUID(reqObj.LoanApplicationID) {
			log.Println("Invalid LoanApplicationID")
			errorHandler.CustomError(w, http.StatusBadRequest, "Invalid LoanApplicationID")
			return
		}

		attributes := map[string]string{
			"umrn":              reqObj.UMRN,
			"loanApplicationID": reqObj.LoanApplicationID,
		}

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func ABFLCustomReportReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			FromDate string `json:"from"`
			ToDate   string `json:"to"`
			Filter   string `json:"filter"`
		}

		decoder := json.NewDecoder(r.Body)
		defer r.Body.Close()
		var reqObj requestStruct
		err := decoder.Decode(&reqObj)
		if err != nil {
			panic(err)
		}
		var attributes = make(map[string]interface{})

		// Filter should be present and be in the valid filter list
		if len(reqObj.Filter) == 0 || !general.InArr(reqObj.Filter, []string{"created_at", "updated_at"}) {
			log.Println("invalid filter type ")
			errorHandler.CustomError(w, http.StatusBadRequest, "invalid filter type")
			return
		}

		attributes["filter"] = reqObj.Filter

		// parse dates if they exist
		if len(reqObj.FromDate) != 0 && len(reqObj.ToDate) != 0 {

			toDateTime, err := time.Parse(time.RFC3339, reqObj.ToDate)
			if err != nil {
				log.Println("invalid `to` date")
				errorHandler.CustomError(w, http.StatusBadRequest, "invalid `to` date")
				return
			}

			fromDateTime, err := time.Parse(time.RFC3339, reqObj.FromDate)
			if err != nil {
				log.Println("invalid `from` date")
				errorHandler.CustomError(w, http.StatusBadRequest, "invalid `from` date")
				return
			}

			// panic if from is ahead of to
			if reqObj.ToDate < reqObj.FromDate {
				log.Println("invalid `from` or `to` date")
				errorHandler.CustomError(w, http.StatusBadRequest, "invalid `from` or `to` date")
				return
			}

			reportDurationInDays := toDateTime.Sub(fromDateTime).Hours() / 24
			if reportDurationInDays > 45 {
				log.Errorln("report requested for a period greater than defined threshold: ", reportDurationInDays)
				errorHandler.CustomError(w, http.StatusConflict, "Report requested for a period greater than allowed threshold. Please try with a time range less than 45 days.")
				return
			}

			attributes["to"] = reqObj.ToDate
			attributes["from"] = reqObj.FromDate
		} else {
			errorHandler.CustomError(w, http.StatusBadRequest, "invalid `from` or `to` date")
			return
		}
		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func CheckPincodeWithoutSourceEntityReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		type requestStruct struct {
			Pincode []string `json:"pincode" validate:"required"`
		}

		bytes, err := json.Marshal(r.URL.Query())
		if err != nil {
			logger.WithRequest(r).Println(err)
			panic(err)
		}

		var reqObj requestStruct
		err = json.Unmarshal(bytes, &reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		v := validator.New()

		err = v.Struct(reqObj)
		if err != nil {
			panic(err)
		}

		var pincode = reqObj.Pincode
		if !general.ValidatePincode(pincode[0]) {
			errorHandler.CustomError(w, http.StatusBadRequest, "Invalid Pincode")
			return
		}

		var attributes = make(map[string]string)
		attributes["pincode"] = pincode[0]

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func GetUserLogsReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()
		queryParams := r.URL.Query()

		limit, err := strconv.Atoi(queryParams.Get("limit"))
		if err != nil {
			errorHandler.CustomError(w, http.StatusBadRequest, "invalid value for limit")
			return
		}
		page, err := strconv.Atoi(queryParams.Get("page"))
		if err != nil {
			errorHandler.CustomError(w, http.StatusBadRequest, "invalid value for page")
			return
		}
		fromDate, err := time.Parse(constants.DateFormat, queryParams.Get("from"))
		if err != nil {
			errorHandler.CustomError(w, http.StatusBadRequest, "invalid value for from date")
			return
		}
		toDate, err := time.Parse(constants.DateFormat, queryParams.Get("to"))
		if err != nil {
			errorHandler.CustomError(w, http.StatusBadRequest, "invalid value for to date")
			return
		}
		uniqueID := queryParams.Get("uniqueID")
		userID := queryParams.Get("userID")

		var attributes = make(map[string]interface{})
		attributes["limit"] = limit
		attributes["page"] = page
		attributes["from"] = fromDate
		attributes["to"] = toDate
		attributes["uniqueID"] = uniqueID
		attributes["userID"] = userID

		ctx = context.WithValue(ctx, "attributes", attributes)

		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func GetWorkflowKeyReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		var reqObj struct {
			UserID     []string `json:"userID"`
			Key        []string `json:"key"`
			ModuleName []string `json:"moduleName"`
		}

		bytesData, err := json.Marshal(r.URL.Query())
		if err != nil {
			logger.Log.Errorln(err)
			panic(err)
		}

		err = json.Unmarshal(bytesData, &reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		if len(reqObj.Key) < 1 || len(reqObj.UserID) < 1 || len(reqObj.ModuleName) < 1 {
			errorHandler.CustomError(w, http.StatusBadRequest, constants.ErrStringMissingRequiredKey)
			return
		}

		if !general.ValidateUUID(reqObj.UserID[0]) {
			errorHandler.CustomError(w, http.StatusBadRequest, "invalid userID")
			return
		}

		var attributes = map[string]interface{}{
			"userID":     reqObj.UserID[0],
			"key":        reqObj.Key[0],
			"moduleName": reqObj.ModuleName[0],
		}

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func UpdateUdyamDetailsReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		decoder := json.NewDecoder(r.Body)
		defer func(Body io.ReadCloser) {
			err := Body.Close()
			if err != nil {
				log.Errorln(err)
			}
		}(r.Body)

		var reqObj UpdateUydamDetail
		err := decoder.Decode(&reqObj)
		if err != nil {
			log.Println(err)
			errorHandler.CustomError(w, http.StatusBadRequest, "invalid request body")
			return
		}

		if !general.ValidateUUID(reqObj.LoanApplicationID) {
			errorHandler.CustomError(w, http.StatusBadRequest, "invalid loan application id")
			return
		}

		if len(reqObj.UdyamNumber) == 0 {
			errorHandler.CustomError(w, http.StatusBadRequest, "udyam number missing")
			return
		}

		attributes := make(map[string]string)
		attributes["loanApplicationID"] = reqObj.LoanApplicationID
		attributes["udyamNumber"] = reqObj.UdyamNumber

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func GetCamReportReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)
		decoder := json.NewDecoder(r.Body)
		defer func(Body io.ReadCloser) {
			err := Body.Close()
			if err != nil {
				log.Errorln(err)
			}
		}(r.Body)

		var reqObj dashboardworkflow.CamReportRequest
		err := decoder.Decode(&reqObj)
		if err != nil {
			log.WithContext(r.Context()).Errorf("[GetCamReportReq] Unable to unmarshal err: %v, reqBody: %+v", err, r.Body)
			errorHandler.CustomError(w, http.StatusBadRequest, constants.InvalidRequest)
			return
		}

		if !general.ValidateUUID(reqObj.LoanApplicationID) {
			log.WithContext(r.Context()).Errorf("[GetCamReportReq] Invalid Loan Application UUID: reqBody: %+v", r.Body)
			errorHandler.CustomError(w, http.StatusBadRequest, constants.MessageInvalidLoanApplication)
			return
		}

		attributes := make(map[string]interface{})
		attributes["req"] = reqObj

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))

	})

}

func UpdateUTRDetailsReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {

		defer recovery(w)

		var reqObj structs.UpdateUTRDetails

		if err := json.NewDecoder(r.Body).Decode(&reqObj); err != nil {
			logger.WithRequest(r).Errorf("[UpdateUTRDetailsReq] failed to decode request payload. err: %v", err)
			errorHandler.CustomError(w, http.StatusBadRequest, "invalid request")
			return
		}

		if err := validator.New().Struct(reqObj); err != nil {
			logger.WithRequest(r).Errorf("[UpdateUTRDetailsReq] validation error, check request payload. err: %v, req: %+v", err, reqObj)
			errorHandler.CustomError(w, http.StatusBadRequest, "invalid request")
			return
		}

		if reqObj.DisbursedAmount <= 0 {
			logger.WithRequest(r).Error("[UpdateUTRDetailsReq] disbursed amount should be greater than 0")
			errorHandler.CustomError(w, http.StatusBadRequest, "disbursed amount should be greater than 0")
			return
		}

		if _, err := time.Parse(constants.DBTimeFormat, reqObj.DisbursalDateTime); err != nil {
			logger.WithRequest(r).Errorf("[UpdateUTRDetailsReq] invalid date time format, required format: YYYY-MM-DD HH:MM:SS. err: %v", err)
			errorHandler.CustomError(w, http.StatusBadRequest, "invalid date time format, required format: YYYY-MM-DD HH:MM:SS")
			return
		}

		ctx := context.WithValue(r.Context(), "attributes", reqObj)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func UpdateDisbursalDetailsReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {

		defer recovery(w)

		var reqObj structs.UpdateDisbursalDetails

		if err := json.NewDecoder(r.Body).Decode(&reqObj); err != nil {
			logger.WithRequest(r).Error(err)
			errorHandler.CustomError(w, http.StatusBadRequest, "invalid request")
			return
		}

		if err := validator.New().Struct(reqObj); err != nil {
			logger.WithRequest(r).Error(err)
			errorHandler.CustomError(w, http.StatusBadRequest, "invalid request")
			return
		}

		ctx := context.WithValue(r.Context(), "attributes", reqObj)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func GetSentinelPolicyLinkReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)
		ctx := r.Context()

		var reqObj dashboardModel.GetSentinelPolicyLinkReq

		reqObj.LoanApplicationID = r.URL.Query().Get("loanApplicationID")
		reqObj.EvaluationID = r.URL.Query().Get("evaluationID")
		reqObj.UserID = r.URL.Query().Get("userID")

		if reqObj.EvaluationID == "" {
			errorHandler.CustomError(w, http.StatusBadRequest, "missing evaluation ID in the params")
			return
		}

		if reqObj.UserID == "" {
			errorHandler.CustomError(w, http.StatusBadRequest, "missing user ID in the params")
			return
		}

		var attributes = map[string]interface{}{
			"req": reqObj,
		}

		ctx = context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// CancelLoanBulkReq cancels loans in bulk using CSV file input, only column in csv file - loan_application_no
func CancelLoanBulkReq(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer recovery(w)

		file, header, err := r.FormFile("file")

		if err != nil {
			logger.WithRequest(r).Errorf("[CancelLoanBulkReq] file field not found. err: %v", err)
			errorHandler.CustomError(w, http.StatusBadRequest, "file field not found")
			return
		}

		if header == nil {
			logger.WithRequest(r).Errorf("[CancelLoanBulkReq] header is null")
			errorHandler.CustomError(w, http.StatusBadRequest, "couldn't determine the file headers, please try again")
			return
		}

		if header.Size > constants.MaxSizeCSVUpload {
			logger.WithRequest(r).Error("[CancelLoanBulkReq] file size exceeds max permitted value")
			errorHandler.CustomError(w, http.StatusBadRequest, "file size exceeds max permitted value")
			return
		}

		fileNameArr := strings.Split(header.Filename, ".")
		if len(fileNameArr) < 2 {
			logger.WithRequest(r).Errorf("[CancelLoanBulkReq] extension not found for %v", header.Filename)
			errorHandler.CustomError(w, http.StatusBadRequest, "couldn't check the file extension, please try again")
			return
		}

		extension := strings.ToLower(fileNameArr[1])
		if extension != "csv" {
			errorHandler.CustomError(w, http.StatusBadRequest, "please upload a csv file")
			return
		}

		defer file.Close()

		csvReader := csv.NewReader(file)
		uploadedFileHeader, err := csvReader.Read()
		if err != nil {
			logger.WithRequest(r).Errorf("[CancelLoanBulkReq] error in parsing the uploaded file, err: %v", err)
			errorHandler.CustomError(w, http.StatusBadRequest, "please verify the uploaded file")
			return
		}

		rows, err := csvReader.ReadAll()
		if err != nil {
			logger.WithRequest(r).Errorf("[CancelLoanBulkReq] unable to read uploaded file, err: %v", err)
			errorHandler.CustomError(w, http.StatusBadRequest, err.Error())
			return
		}

		// validating min rows
		if len(rows) == 0 {
			logger.WithRequest(r).Errorf("[CancelLoanBulkReq] uploaded blank csv file")
			errorHandler.CustomError(w, http.StatusBadRequest, "uploaded blank csv file")
			return
		}

		// validating max rows
		maxRows := 250 //maximum limit
		if len(rows) > maxRows {
			logger.WithRequest(r).Errorf("uploaded csv has rows more than max limit, max rows allowed: %d", maxRows)
			errorHandler.CustomError(w, http.StatusBadRequest, fmt.Sprintf("uploaded csv has rows more than max limit, max rows allowed : %d", maxRows))
			return
		}

		//validating headers sequence
		for i, x := range dashbaord.BulkCancelLoanApplicationFileHeaders {
			if x != uploadedFileHeader[i] {
				msg := fmt.Sprintf("incorrect column header order, expected %s got %s", dashbaord.BulkCancelLoanApplicationFileHeaders[i], uploadedFileHeader[i])
				logger.WithRequest(r).Errorln(msg)
				errorHandler.CustomError(w, http.StatusBadRequest, msg)
				return
			}
		}

		var csvCancelList []dashbaord.BulkCancelLoanApplicationInput
		for i, row := range rows {

			if len(strings.TrimSpace(row[0])) == 0 {
				logger.WithRequest(r).Errorln("missing loan application no. at row " + strconv.Itoa(i+1))
				errorHandler.CustomError(w, http.StatusBadRequest, "missing loan application no. at row "+strconv.Itoa(i+1))
				return
			}
			if len(strings.TrimSpace(row[1])) == 0 {
				logger.WithRequest(r).Errorln("missing cancelation reason at row " + strconv.Itoa(i+1))
				errorHandler.CustomError(w, http.StatusBadRequest, "missing cancelation reason at row "+strconv.Itoa(i+1))
				return
			}

			csvCancelList = append(csvCancelList, dashbaord.BulkCancelLoanApplicationInput{
				LoanApplicationNo: strings.TrimSpace(row[0]),
				CancelationReason: strings.TrimSpace(row[1]),
			})

		}

		attributes := map[string]interface{}{
			"cancelList": csvCancelList,
		}

		ctx := context.WithValue(r.Context(), "attributes", attributes)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}
