package services

import (
	"context"
	"database/sql"
	"encoding/json"
	"finbox/go-api/functions/lenders/abflpl"
	"finbox/go-api/functions/lenders/cashe"
	"finbox/go-api/functions/lenders/cashemc"
	"finbox/go-api/functions/lenders/fibe"
	"finbox/go-api/functions/lenders/kissht"
	"finbox/go-api/functions/lenders/kreditbee"
	"finbox/go-api/functions/lenders/moneyview"
	"finbox/go-api/functions/lenders/niro"
	"finbox/go-api/functions/lenders/pflcallback"
	"finbox/go-api/functions/lenders/prefr"
	"finbox/go-api/functions/lenders/prefrmc"
	"finbox/go-api/functions/lenders/prefrsm"
	"finbox/go-api/functions/lenders/ring"
	"finbox/go-api/utils/general"
	"finbox/go-api/utils/lisautil"
	"io"
	"net/http"

	"finbox/go-api/constants"
	"finbox/go-api/errorHandler"
	"finbox/go-api/functions/lenders/dmi"
	"finbox/go-api/functions/lenderservice"
	"finbox/go-api/functions/logger"
	"finbox/go-api/models/lisawebhook"
)

func LISACallback(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)
		var reqObj lenderservice.CallbackReq
		body, err := io.ReadAll(r.Body)

		defer r.Body.Close()
		if err != nil {
			logger.WithRequest(r).Error(err)
			errorHandler.CustomError(w, http.StatusInternalServerError, err.Error())
			return
		}

		logger.WithRequest(r).Debug(string(body))
		err = json.Unmarshal(body, &reqObj)
		if err != nil {
			logger.WithRequest(r).Error(err)
			panic(err)
		}

		err = processCallback(r.Context(), reqObj)
		if err != nil {
			logger.WithRequest(r).Error(err)
			if general.InArr(err.Error(), constants.StatusesAndSubStatusesToBeSkipped) || err == sql.ErrNoRows {
				errorHandler.CustomError(w, http.StatusConflict, err.Error())
				return
			} else {
				rejectReason := lisautil.ProcessRejectionReason(err, "lisa_get_loan_details_fatal:http_status:")
				errorHandler.CustomError(w, http.StatusConflict, rejectReason)
				return
			}
		}

		ctx := context.WithValue(r.Context(), "resData", nil)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func processCallback(ctx context.Context, callbackDtls lenderservice.CallbackReq) error {
	appReq := callbackDtls.ApplicationReq
	reqID := appReq.RequestID
	userID := appReq.UserID
	lenderID := appReq.LenderID

	if !callbackDtls.Status {
		if err := lisawebhook.Upsert(ctx, userID, &lisawebhook.Lisawebhook{
			UserID:      userID,
			LenderID:    lenderID,
			RequestID:   reqID,
			ServiceName: callbackDtls.Type,
			Error:       callbackDtls.Err,
		}); err != nil {
			logger.WithUser(userID).Error(err)
			return err
		}
		return nil
	}

	var err error
	dataByte, err := json.Marshal(callbackDtls.Data)
	if err != nil {
		logger.WithUser(userID).Error(err)
		return err
	}
	if err := lisawebhook.Upsert(ctx, userID, &lisawebhook.Lisawebhook{
		UserID:       userID,
		LenderID:     lenderID,
		RequestID:    reqID,
		ServiceName:  callbackDtls.Type,
		CallbackResp: string(dataByte),
	}); err != nil {
		logger.WithUser(userID).Error(err)
		return err
	}

	// New callback config flow
	config, _ := getCallbackConfig(callbackDtls)
	if config != nil {
		if err = ProcessCallbackV4(ctx, callbackDtls); err != nil {
			logger.WithUser(userID).Error(err)
			return err
		}
		return nil
	}

	logger.WithUser(userID).Debug("No config found for callback")

	switch lenderID {
	case constants.DMIID:
		err = dmi.ProcessCallBack(callbackDtls)
		if err != nil {
			logger.WithUser(userID).Error(err)
			return err
		}
	case constants.MoneyViewID:
		err = moneyview.ProcessCallBack(callbackDtls)
		if err != nil {
			logger.WithUser(userID).Error(err)
			return err
		}
	case constants.CasheID:
		err = cashe.ProcessCallBack(callbackDtls)
		if err != nil {
			logger.WithUser(userID).Error(err)
			return err
		}
	case constants.CasheMCID:
		err = cashemc.ProcessCallBack(callbackDtls)
		if err != nil {
			logger.WithUser(userID).Error(err)
			return err
		}
	case constants.FibeID:
		err = fibe.ProcessCallBack(callbackDtls)
		if err != nil {
			logger.WithUser(userID).Error(err)
			return err
		}
	case constants.TDLKreditBeeID:
		err = kreditbee.ProcessCallBack(callbackDtls)
		if err != nil {
			logger.WithUser(userID).Error(err)
			return err
		}
	case constants.ABFLID:
		err = abflpl.ProcessCallBack(callbackDtls)
		if err != nil {
			logger.WithUser(userID).Error(err)
			return err
		}
	case constants.NiroID:
		err = niro.ProcessCallBack(callbackDtls)
		if err != nil {
			logger.WithUser(userID).Error(err)
			return err
		}
	case constants.PrefrID:
		err = prefr.ProcessCallBack(callbackDtls)
		if err != nil {
			logger.WithUser(userID).Error(err)
			return err
		}
	case constants.KisshtID:
		err = kissht.ProcessCallBack(callbackDtls)
		if err != nil {
			logger.WithUser(userID).Error(err)
			return err
		}
	case constants.PrefrMCID:
		err = prefrmc.ProcessCallBack(callbackDtls)
		if err != nil {
			logger.WithUser(userID).Error(err)
			return err
		}
	case constants.RingMCID:
		err = ring.ProcessCallBack(callbackDtls)
		if err != nil {
			logger.WithUser(userID).Error(err)
			return err
		}
	case constants.PoonawallaFincorpID:
		err = pflcallback.ProcessCallBack(callbackDtls)
		if err != nil {
			logger.WithUser(userID).Error(err)
			return err
		}
	case constants.PrefrSMLenderID:
		err = prefrsm.ProcessCallBack(callbackDtls)
		if err != nil {
			logger.WithUser(userID).Error(err)
			return err
		}
	default:
		logger.WithUser(userID).Error("No lender configured for callback")
	}
	return nil
}
