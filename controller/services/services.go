// Package services has server to servser apis
package services

import (
	"context"
	"database/sql"
	"encoding/csv"
	"encoding/json"
	"errors"
	"finbox/go-api/functions/agreementutils"
	"finbox/go-api/functions/downloadables"
	"finbox/go-api/functions/insurance/riscovery"
	"finbox/go-api/functions/lenders/abflpl"
	"finbox/go-api/functions/lenders/cashe"
	"finbox/go-api/functions/lenders/cashemc"
	"finbox/go-api/functions/lenders/indifi"
	"finbox/go-api/functions/lenders/landt"
	"finbox/go-api/functions/lenders/pflcallback"
	"finbox/go-api/functions/lenders/poonawalla"
	"finbox/go-api/functions/partner"
	"finbox/go-api/functions/retry"
	"finbox/go-api/functions/tracer"
	"finbox/go-api/models/decisionengine"
	"finbox/go-api/models/featureflag"
	"finbox/go-api/models/lenderpayments"
	"finbox/go-api/models/loankycdetails"
	"finbox/go-api/models/sourceentityjourney"
	"finbox/go-api/models/userdocuments"
	"finbox/go-api/temporal/temporalutility"
	lenderspecificutils "finbox/go-api/temporal/utils/lenderspecific"
	"finbox/go-api/utils/lisautil"
	"finbox/go-api/utils/lockutils"
	"finbox/go-api/utils/workflowutils"
	"finbox/go-api/utils/workflowutils/tsm"
	"fmt"
	"io"
	"math"
	"mime/multipart"
	"net/http"
	"net/url"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/mitchellh/mapstructure"
	"github.com/sirupsen/logrus"

	"github.com/getsentry/sentry-go"
	validator "github.com/go-playground/validator/v10"
	"github.com/jmoiron/sqlx"

	"finbox/go-api/authentication"
	"finbox/go-api/common/usersutil"
	"finbox/go-api/conf"

	"finbox/go-api/constants"
	"finbox/go-api/errorHandler"
	"finbox/go-api/functions/activity"
	"finbox/go-api/functions/agreement"
	"finbox/go-api/functions/booster"
	"finbox/go-api/functions/clutils"
	"finbox/go-api/functions/commonutils"
	"finbox/go-api/functions/disbursal"
	"finbox/go-api/functions/emaillib"
	"finbox/go-api/functions/enach"
	"finbox/go-api/functions/insuranceutils"
	"finbox/go-api/functions/journey"
	"finbox/go-api/functions/kycengine"
	"finbox/go-api/functions/lenders/axis"
	"finbox/go-api/functions/lenders/capitalfloat"
	"finbox/go-api/functions/lenders/dmi"
	lenderFibe "finbox/go-api/functions/lenders/fibe"
	"finbox/go-api/functions/lenders/hcin"
	"finbox/go-api/functions/lenders/iifl"
	"finbox/go-api/functions/lenders/kotak"
	"finbox/go-api/functions/lenders/lendingkart"
	"finbox/go-api/functions/lenders/mintifi"
	"finbox/go-api/functions/lenders/onemuthoot"
	"finbox/go-api/functions/lenders/saraloan"
	"finbox/go-api/functions/lenderservice"
	"finbox/go-api/functions/loanutils"
	"finbox/go-api/functions/logger"
	"finbox/go-api/functions/masterDashboardUtils"
	"finbox/go-api/functions/octopus"
	"finbox/go-api/functions/postagreement"
	"finbox/go-api/functions/services/aadhaarApi"
	"finbox/go-api/functions/services/bankconnect"
	"finbox/go-api/functions/services/digio"
	"finbox/go-api/functions/services/fibe"
	"finbox/go-api/functions/services/ifscapi"
	"finbox/go-api/functions/services/leegality"
	"finbox/go-api/functions/services/ocr"
	"finbox/go-api/functions/services/pennydrop"
	"finbox/go-api/functions/services/razorpay"
	"finbox/go-api/functions/services/tdl"
	"finbox/go-api/functions/structs"
	"finbox/go-api/functions/underwriting"
	"finbox/go-api/htmltemplates"
	"finbox/go-api/infra/db"
	"finbox/go-api/infra/s3"
	"finbox/go-api/infra/temporalclient"
	"finbox/go-api/models/companydetails"
	"finbox/go-api/models/dashboarddocs"
	"finbox/go-api/models/documents"
	"finbox/go-api/models/enachthirdparty"
	"finbox/go-api/models/esignattempt"
	"finbox/go-api/models/exportlogs"
	"finbox/go-api/models/idempotence"
	"finbox/go-api/models/insurance"
	"finbox/go-api/models/lender"
	"finbox/go-api/models/lenderdropdown"
	"finbox/go-api/models/lendervariables"
	"finbox/go-api/models/loanapplication"
	"finbox/go-api/models/media"
	"finbox/go-api/models/octopuswebhook"
	"finbox/go-api/models/onefinenach"
	"finbox/go-api/models/onemuthootwebhook"
	"finbox/go-api/models/paymentvirtualaccount"
	"finbox/go-api/models/personalloanoffer"
	"finbox/go-api/models/refunds"
	sourceentitymodel "finbox/go-api/models/sourceentity"
	"finbox/go-api/models/temporalsignallogging"
	"finbox/go-api/models/userbusinessgst"
	"finbox/go-api/models/userjourney"
	"finbox/go-api/models/usermodulemapping"
	"finbox/go-api/models/users"
	"finbox/go-api/models/userworkflows"
	"finbox/go-api/models/webhooklogs"
	camspaythirdparty "finbox/go-api/thirdparty/enach"
	"finbox/go-api/thirdparty/karza"
	"finbox/go-api/thirdparty/onefin"
	"finbox/go-api/utils/calc"
	export "finbox/go-api/utils/exportutils"
	"finbox/go-api/utils/general"
	"finbox/go-api/utils/retryutils"
)

var log = logger.Log
var database = db.GetDB()

// CheckSupportedFormats checks for supported formats and returns file object, extension, error http code and error message if any
func CheckSupportedFormats(r *http.Request, extensions, contentTypes []string) (multipart.File, string, int, string) {
	// check for file types using file name
	file, header, _ := r.FormFile("file")
	if header == nil {
		log.Println("header is null")
		return nil, "", http.StatusBadRequest, "couldn't determine the file headers, please try again"
	}
	fileNameArr := strings.Split(header.Filename, ".")
	if len(fileNameArr) < 2 {
		// no extension case
		return nil, "", http.StatusBadRequest, "no file extension found"
	}
	extension := strings.ToLower(fileNameArr[len(fileNameArr)-1]) // change case to lower for easy checks
	if !general.InArr(extension, extensions) {                    // []string{"pdf", "jpeg", "png", "jpg"}
		return nil, "", http.StatusBadRequest, "file type not supported"
	}

	// now check using content type
	buff := make([]byte, 512) // why 512 bytes, see http://golang.org/pkg/net/http/#DetectContentType
	_, err := file.Read(buff)
	if err != nil {
		log.Println(err)
		return nil, "", http.StatusBadRequest, "couldn't read the file, please try again"
	}
	contentType := strings.ToLower(http.DetectContentType(buff))
	if !general.InArr(contentType, contentTypes) { // []string{"image/jpeg", "image/jpg", "image/png", "application/pdf"}
		return nil, "", http.StatusBadRequest, "file type not supported"
	}

	// reset the read pointer
	_, err = file.Seek(0, 0)
	if err != nil {
		log.Println(err)
	}

	return file, extension, 0, ""
}

// UploadFunc uploads the file to S3 and store the file in media table
func UploadFunc(r *http.Request, userType string, userID string, sourceEntityID string, lenderID string, email string) (string, int, string, error) {
	data, errStr := UploadMediaRequestSanity(r, userType)
	if errStr != "" {
		logger.WithRequest(r).Error(errStr)
		return "", http.StatusBadRequest, errStr, nil
	}
	if userType != userTypeSDKUser {
		userID = data.UserID
	}
	// sanity checks based on usertype
	switch userType {
	case userTypeDashboardUser:
		// check for userID
		var count int
		query := `SELECT count(*) from users where user_id = $1 and source_entity_id = $2`
		err := database.Get(&count, query, data.UserID, sourceEntityID)
		if err != nil || count == 0 {
			logger.WithRequest(r).Error("user not found")
			return "", 0, "user not found", nil
		}
	case userTypeSDKUser:
		data.UserID = userID
	case UserTypeLenderUser:
		if isCoApplicant(userID) {
			query := `select u.source_entity_id as sourceentityid from users u where user_id = $1`
			err := database.Get(&sourceEntityID, query, data.UserID)
			if err != nil {
				logger.WithRequest(r).Error("user not found")
				return "", 0, "user not found", nil
			}
		} else {
			query := `SELECT u.source_entity_id as sourceentityid from users u join loan_application la on u.user_id = la.user_id
					where u.user_id = $1 and la.lender_id = $2 order by la.created_at desc limit 1`
			err := database.Get(&sourceEntityID, query, data.UserID, lenderID)
			if err != nil {
				logger.WithRequest(r).Error("user not found")
				return "", 0, "user not found", nil
			}
		}
	}

	mediaID, path, errStr := AddMediaInS3(&data)
	if errStr != "" {
		logger.WithRequest(r).Error(errStr)
		return mediaID, 0, errStr, nil
	}

	err := media.Insert(nil, r.Context(), data, email)
	if err != nil {
		logger.WithRequest(r).Error(err)
		return mediaID, 0, "", err
	}

	if data.DocumentID != "" {
		documentName, err := documents.GetNameFromDocID(r.Context(), data.DocumentID)
		if err != nil {
			logger.WithRequest(r).Error(err)
			return mediaID, 0, "", err
		}
		if data.MediaType == "Address_Proof" && data.IsBack {
			err = AddressProofBackFunc(r.Context(), path, documentName, userID, mediaID, sourceEntityID)
			if err != nil {
				logger.WithRequest(r).Error(err)
				return mediaID, 0, "", err
			}
		}
		if data.MediaType == constants.DocTypeCurrentAddressProof && documentName == "CURRENT_AADHAAR" {
			maskAadhaarCurrentAddressProof(userID, sourceEntityID, data, documentName)
		}
	}
	return mediaID, 0, "", nil
}

// maskAadhaarCurrentAddressProof calls OCR service on aadhaar front/back which internally return the aadhaar image with aadhaar number masked
func maskAadhaarCurrentAddressProof(userID string, sourceEntityID string, data media.UploadMediaDataStruct, documentName string) {
	maskedURL := ""
	filePath, err := s3.GetLocalFilePath(data.Path)
	if err != nil {
		logger.WithUser(userID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return
	}
	if data.IsBack {
		_, _, maskedURL, _ = ocr.GetOCRAddressFromAadhaarBack(filePath, userID, data.MediaID, sourceEntityID)
	} else {
		_, _, _, maskedURL, _ = ocr.GetOCRNameDOBIdentifierFromAadhaarFront(filePath, userID, data.MediaID, sourceEntityID)
	}
	_ = os.Remove(filePath)
	if maskedURL != "" {
		_, _ = s3.ReadFromURLAndUploadFileS3(maskedURL, data.Path)
	}
}

// UploadMediaCont uploads a file for given user
func UploadMediaCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)
		userObj := r.Context().Value("user").(authentication.UserStruct)
		userID := userObj.UserID
		sourceEntityID := userObj.SourceEntityID
		email := userObj.Email

		mediaID, httpCode, errStr, err := UploadFunc(r, userTypeSDKUser, userID, sourceEntityID, "", email)
		if errStr != "" {
			if httpCode != 0 {
				logger.WithRequest(r).Error(errStr)
				errorHandler.CustomError(w, http.StatusBadRequest, errStr)
				return
			}
			logger.WithRequest(r).Panic(errStr)
			panic(errStr)
		}
		if err != nil {
			logger.WithRequest(r).Panic(err)
			panic(err)
		}
		var resData = map[string]string{
			"mediaID": mediaID,
		}
		ctx := context.WithValue(r.Context(), "resData", resData)
		next.ServeHTTP(w, r.WithContext(ctx))

	})
}

func ValidateDocumentCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)
		userObj := r.Context().Value("user").(authentication.UserStruct)
		attributes := r.Context().Value("attributes").(map[string]string)
		userID := userObj.UserID
		sourceEntityID := userObj.SourceEntityID
		mediaID := attributes["mediaID"]
		documentType := attributes["documentType"]

		mediaObj, err := media.Get(r.Context(), mediaID)
		if err != nil && err == sql.ErrNoRows {
			logger.WithUser(userID).Errorln(err)
			errorHandler.ReportToSentryWithoutRequest(fmt.Errorf("[ValidateDocumentCont] unknown mediaID %s submitted for userID: %s and documentType:%s", mediaID, userID, documentType))
			panic("mediaID does not exist")
		} else if err != nil {
			logger.WithUser(userID).Errorln(err)
			panic(err)
		}

		filePath, err := s3.GetLocalFilePath(mediaObj.Path)
		if err != nil {
			logger.WithUser(userID).Errorln(err)
			panic(err)
		}

		isValidDoc, err := ocr.ValidateDocumentAuthenticity(documentType, filePath, userID, sourceEntityID, "")
		if err != nil {
			logger.WithUser(userID).Errorln(err)
			errorHandler.CustomError(w, http.StatusConflict, "unable to validate document authenticity")
			return
		}

		if !isValidDoc {
			errMsg := fmt.Sprintf("invalid document for %s", documentType)
			logger.WithUser(userID).Errorln(errMsg)
			panic(errMsg)
		}

		var resData = map[string]string{
			"message": "ok",
		}

		ctx := context.WithValue(r.Context(), "resData", resData)
		next.ServeHTTP(w, r.WithContext(ctx))

	})
}

// UploadLenderMediaCont uploads a file for given user in lender dashboard
func UploadLenderMediaCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)
		userObj := r.Context().Value("user").(authentication.LenderUserStruct)
		lenderID := userObj.LenderID
		email := userObj.Email

		mediaID, httpCode, errStr, err := UploadFunc(r, UserTypeLenderUser, "", "", lenderID, email)
		if errStr != "" {
			if httpCode != 0 {
				errorHandler.CustomError(w, httpCode, errStr)
				return
			}
			logger.WithRequest(r).Panic(errStr)
			panic(errStr)
		}
		if err != nil {
			logger.WithRequest(r).Panic(err)
			panic(err)
		}

		var resData = map[string]string{
			"mediaID": mediaID,
		}
		ctx := context.WithValue(r.Context(), "resData", resData)
		next.ServeHTTP(w, r.WithContext(ctx))

	})

}

// GetMediaCont controller returns a presigned URL for a given media for a user
func GetMediaCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		attributes := r.Context().Value("attributes").(map[string]string)
		mediaID := attributes["mediaID"]

		userObj := r.Context().Value("user")
		userID := userObj.(authentication.UserStruct).UserID

		type dbResStruct struct {
			MediaID   string `db:"media_id" json:"mediaID"`
			MediaType string `db:"media_type" json:"mediaType"`
			Path      string `json:"path"`
		}

		obj := dbResStruct{}
		query := "select media_id, media_type, path from media where status = $1 and media_id = $2 and user_id = $3"
		err := database.Get(&obj, query, constants.MediaStatusActive, mediaID, userID)
		if err != nil {
			if err == sql.ErrNoRows {
				log.Println("new media found")
				panic("no media found")
			} else {
				log.Println(err)
				panic(err)
			}
		}

		obj.Path = s3.GetPresignedURLS3(obj.Path, 60)

		ctx := context.WithValue(r.Context(), "resData", obj)
		next.ServeHTTP(w, r.WithContext(ctx))

	})

}

func HCINWebhookCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		b, err := io.ReadAll(r.Body)
		defer r.Body.Close()
		if err != nil {
			log.Println(err)
			panic(err)
		}
		attributes := string(b)

		// writing payload to DB
		query := `INSERT INTO hcin_webhook (webhook_payload, created_at) VALUES ($1, NOW())`
		_, err = database.Exec(query, attributes)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		go hcin.ProcessWebhook(attributes)

		ctx := context.WithValue(r.Context(), "resData", "")
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func IIFLWebhookCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		errJSON := map[string]interface{}{
			"message": "Unauthorized!",
		}

		if conf.ENV == conf.ENV_PROD {
			found := false
			for _, ipToMatch := range conf.IIFLIPs {
				if strings.Contains(r.RemoteAddr, ipToMatch) {
					found = true
					break
				}
			}
			if !found {
				log.Println("request unauthorised. IP:", r.RemoteAddr)
				errorHandler.CustomErrorJSON(w, http.StatusUnauthorized, errJSON)
				return
			}
		}

		apiKey := r.Header.Get(constants.InternalHeaderAPIKey)
		reqdAPIKey := conf.IIFLWebhookAPIKey
		if apiKey != reqdAPIKey {
			log.Println("request unauthorised. API Key:", apiKey)
			errorHandler.CustomErrorJSON(w, http.StatusUnauthorized, errJSON)
			return
		}

		b, err := io.ReadAll(r.Body)
		defer r.Body.Close()
		if err != nil {
			log.Println(err)
			panic(err)
		}
		attributes := string(b)

		// writing payload to DB
		query := `INSERT INTO iifl_webhook (webhook_payload, created_at) VALUES ($1, NOW())`
		_, err = database.Exec(query, attributes)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		go iifl.ProcessWebhook(b)

		next.ServeHTTP(w, r)

	})
}

func OneMuthootWebhookCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		errJSON := map[string]interface{}{
			"message": "Unauthorized!",
		}

		if conf.ENV == conf.ENV_PROD {
			found := false
			for _, ipToMatch := range conf.OneMuthootIPS {
				if strings.Contains(r.RemoteAddr, ipToMatch) {
					found = true
					break
				}
			}
			if !found {
				logger.WithRequest(r).Errorln("request unauthorised. IP:", r.RemoteAddr)
				errorHandler.CustomErrorJSON(w, http.StatusUnauthorized, errJSON)
				return
			}
		}

		apiKey := r.Header.Get(constants.InternalHeaderAPIKey)
		reqdAPIKey := conf.OneMuthootAPIKey
		if apiKey != reqdAPIKey {
			logger.WithRequest(r).Errorln("request unauthorised. API Key:", apiKey)
			errorHandler.CustomErrorJSON(w, http.StatusUnauthorized, errJSON)
			return
		}

		b, err := io.ReadAll(r.Body)
		defer r.Body.Close()
		if err != nil {
			logger.WithRequest(r).Println(err)
			errorHandler.CustomError(w, http.StatusBadRequest, err.Error())
			return
		}

		// writing payload to DB
		err = onemuthootwebhook.Insert(string(b))
		if err != nil {
			logger.WithRequest(r).Println(err)
			errorHandler.CustomError(w, http.StatusBadRequest, err.Error())
			return
		}

		dbObjs, err := onemuthoot.ValidateWebhook(b)
		if err != nil {
			logger.WithRequest(r).Println(err)
			errorHandler.CustomError(w, http.StatusBadRequest, err.Error())
			return
		}
		go onemuthoot.ProcessWebhook(b, dbObjs)

		next.ServeHTTP(w, r)
	})
}

// DocHTMLCont controller returns an html text
func DocHTMLCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		attributes := r.Context().Value("attributes").(map[string]string)
		id := attributes["id"]

		htmlBody := `
		<!doctype html>
		<html>
		<head></head>
		<body>
		`
		switch id {
		case "hcin_ld":
			htmlBody += `
			<p style='font-size:15px;text-align:justify;'><strong><span>Loan Declaration</span></strong></p>
				<ol style="list-style-type: decimal;">
					<li><span>I hereby confirm that I am a citizen of India and competent to give this declaration/ undertaking and to execute and submit this Application Form and all other documents for the purpose of availing loan, creation of security and representing generally for all the purposes mentioned/required to be done for these presents.</span></li>
					<li><span>I confirm that all the particulars and information given in the application form and otherwise in writing or over the Contact Options by me, are true, correct and complete and no information has been withheld/suppressed.</span></li>
					<li><span>I confirm that I am submitting this application form after having read and fully understood the Privacy Policy &amp; Interest Rate Policy (as amended from time to time) of Home Credit India Finance Private Limited (&ldquo;HCIN&rdquo;) as well as the terms and conditions of availing loan from HCIN, including those provided in the General Terms and Conditions for obtaining Personal Loan and POS loan from HCIN registered with the Sub-Registrar of Assurances at Gurgaon (Haryana) under Deed of Declaration having Registration No. 23050 dated 18.12.2015 (&quot;GTC&quot;). I acknowledge that HCIN shall be entitled to reject my application without assigning any reason.</span></li>
					<li><span>I have no insolvency proceedings against me nor have I ever been adjudicated insolvent. &nbsp;I confirm that no suit for recovery of outstanding dues or monies whatsoever and/or criminal proceedings have been initiated and/or pending against me. There is no restriction on me in respect of availing the loan from HCIN and no person&apos;s consent is required by me for availing the loan from HCIN. Further, I undertake and confirm that loan amount shall not be used for purposes of gambling, lottery, or races, for purchase of gold in any form, gold bullion, gold jewellery, gold coins, units of Exchange Traded Funds (ETF) and units of gold mutual fund, or for any purpose prohibited by RBI/ other regulatory body from time to time or any other illegal purposes of similar nature. I confirm that I have been informed that the loan would be subject to Pre-payment Penalties and other charges.</span></li>
					<li><span>I understand that HCIN offers different interest rate to different borrowers based on loan amount, tenor, down payment, payment history with HCIN, credit score provided by credit information companies, borrower&apos;s age, &nbsp;income, &nbsp; type of documents provided by the borrower and any other information as may be required for the purpose of credit evaluation and I agree to abide by such rationale. <strong>HCIN has confirmed that any changes in the rate of interest and fee/ charges shall take effect only prospectively.</strong></span></li>
				</ol>
				<p style='font-size:15px;text-align:justify;'><span>I further confirm that HCIN shall be entitled to categorize me under High, Low, Medium or any other risk category based on the aforementioned factors and the loan performance.</span></p>
			`
		case "hcin_perfios":
			htmlBody += `
			<p>You are being re-directed to a third party website for verification of your bank account for the purpose of underwriting the application submitted by you. As on date HCIN is availing the services of Yodlee Inc. and Perfios Software Solutions Private Limited (hereinafter referred to as &ldquo;Service Provider&rdquo;) for this purpose. The Loan Amount (in case of personal loan), if approved, shall be disbursed in your verified bank account only. Further you hereby understand &amp; agree that:</p>
			<ul>
				<li>You have provided the same bank account wherein you intend to receive loan amount (in case of personal loan).</li>
				<li>The bank account should be at least 6 months old.</li>
				<li>The name appearing in such bank account should be the same as mentioned in Identity Proof.</li>
				<li>To evaluate your eligibility, we may use, store and analyse information of complete name of customer, customer&rsquo;s bank name, IFSC Code and credit/ debit transactions&rsquo; information for last 6 months as obtained from your bank account through this service.</li>
				<li>The provisions of availing this service shall also be governed by the terms and conditions of the Service Provider.</li>
			</ul>
			<p><br /> HCIN assures that HCIN never stores any password of your bank account. All reasonable care has been taken towards guarding against unauthorized use of any information transmitted by you, however, you understand that your details relating to debit card/ net banking transmitted over the internet is susceptible to misuse and that HCIN or the Service Provider has no control over these matters. Therefore, HCIN or the concerned Service Provider does not guarantee that the use of account verification service will not result in any unauthorized use of data over the internet. HCIN/ the Service Provider expressly disclaims any claim or liability arising out of the provision of service of account verification. You agree and acknowledge that you shall be solely responsible for your conduct and that HCIN reserves the right to terminate your rights to use this service immediately. You shall indemnify and hold harmless HCIN, Service Provider its agents and their respective officers from any such claim or demand, or actions.</p>
			<p>&nbsp;</p>
			<p>Please click&nbsp;Continue button if you agree and proceed further.</p>
			`
		}
		htmlBody += `</body></html>`

		ctx := context.WithValue(r.Context(), "resData", htmlBody)
		next.ServeHTTP(w, r.WithContext(ctx))

	})
}

// DisbursedContentCont controller returns disbursed content
func DisbursedContentCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		userObj := r.Context().Value("user")
		userID := userObj.(authentication.UserStruct).UserID
		sourceEntityID := userObj.(authentication.UserStruct).SourceEntityID

		if !journey.ShowDisbursedModule(sourceEntityID) {
			errorHandler.CustomError(w, http.StatusForbidden, "permission denied")
			return
		}
		loan, err := loanapplication.GetLatestValidByUser(userID)
		if err != nil {
			logger.WithUser(userID).Errorln(err)
			if err == sql.ErrNoRows {
				errorHandler.CustomError(w, http.StatusConflict, "no loan application found for the user")
				return
			} else {
				panic(err)
			}
		}

		isDisbursalReplica := false
		if journey.IsABFLBLSourcing(sourceEntityID) {
			isDisbursalReplica = true
		}

		ctaURL := constants.DisbursalCTA[loan.LenderID]
		if journey.CallLisaRedirectOnDisbursalCTA(loan.LenderID) {
			appReq := lenderservice.ApplicationReq{
				UserID:         userID,
				LenderID:       loan.LenderID,
				SourceEntityID: loan.SourceEntityID,
			}
			resp, err := lenderservice.RedirectUser(r.Context(), &appReq)
			if err != nil {
				logger.WithUser(userID).Println(err)
			}
			ctaURL = resp.URL
		}

		// Fields below have values specific to PFL, can create a config if this API is extended to more platforms
		var resData = map[string]interface{}{
			"ctaURL":             ctaURL,
			"ctaText":            constants.DisbursalCTAText[loan.LenderID],
			"contentHTML":        constants.DisbursalContent[loan.LenderID],
			"isDisbursalReplica": isDisbursalReplica,
			"showLenderPartner":  journey.ShowLenderPartnerDisbursal(loan.SourceEntityID),
			"hideLoanAmount":     journey.HideLoanAmountOnDisbursalPage(sourceEntityID),
		}

		ctx := context.WithValue(r.Context(), "resData", resData)
		next.ServeHTTP(w, r.WithContext(ctx))

	})
}

// SearchIFSCCont controller is the controller for search IFSC feature
func SearchIFSCCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		attributes := r.Context().Value("attributes").(map[string]interface{})
		bank := attributes["bank"].(string)
		page := attributes["page"].(int)
		searchString := attributes["searchString"].(string)
		var limit = 20
		offset := (page - 1) * limit

		type ResponseStruct struct {
			IFSC   string `json:"ifsc"`
			Branch string `json:"branch"`
			State  string `json:"state"`
			City   string `json:"city"`
		}
		respObj := []ResponseStruct{}

		/* order
		   branch, city, state
		   branch%, city%, state%
		   %branch, %city, %state
		   %branch%, %city%, %state%
		*/

		query := `select ifsc, branch, state, city from ifsc where bank_name = $1 and 
			(lower(branch) like $5 or lower(city) like $5 or lower(state) like $5) 
			order by 
				(case when lower(branch) = $2 then 1 when lower(city) = $2 then 2 when lower(state) = $2 then 3
					when lower(branch) like $3 then 4 when lower(city) like $3 then 5 when lower(state) like $3 then 6 
					when lower(branch) like $4 then 7 when lower(city) like $4 then 8 when lower(city) like $4 then 9
					when lower(branch) like $5 then 10 when lower(city) like $5 then 11 when lower(state) like $5 then 12 
				else 13 end), ifsc
			limit $6 offset $7`
		err := database.Select(&respObj, query, bank, searchString, searchString+"%", "%"+searchString, "%"+searchString+"%", limit, offset)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		ctx := context.WithValue(r.Context(), "resData", map[string]interface{}{"res": respObj})
		next.ServeHTTP(w, r.WithContext(ctx))

	})
}

func SearchCompanyNameCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		attributes := r.Context().Value("attributes").(map[string]interface{})
		page := attributes["page"].(int)
		platformName := attributes["platformName"].(string)
		searchTerm := attributes["searchTerm"].(string)
		searchTerm = strings.ToLower(searchTerm)
		var limit = 20
		if page == 0 {
			page++
		}
		var sourceEntityID string
		offset := (page - 1) * limit
		if val, ok := constants.PlatformNamesMap[platformName]; ok {
			sourceEntityID = val
		} else {
			sourceEntityID = constants.TataPLID // setting default value to Tata ID
		}

		// Check if the search is active for CH
		isCHSearchActive, _ := sourceentityjourney.GetKeyValueByName(sourceEntityID, sourceentityjourney.SearchCompanyNameCH, "")

		var respObj []companydetails.ResponseStruct

		var err error

		if isCHSearchActive == "ACTIVE" {
			respObj, err = companydetails.SearchCompanyV2(r.Context(), searchTerm, limit, offset, sourceEntityID, "")
		} else {
			respObj, err = companydetails.SearchCompanyName(searchTerm, limit, offset, sourceEntityID, "")
		}
		if err != nil {
			log.Errorln(err)
			panic(err)
		}
		respObj = general.RemoveDuplicatesByKey(respObj, "CompanyName")
		ctx := context.WithValue(r.Context(), "resData", map[string]interface{}{"res": respObj})
		next.ServeHTTP(w, r.WithContext(ctx))

	})
}

func IFSCStatesCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		attributes := r.Context().Value("attributes").(map[string]interface{})
		bank := attributes["bank"].(string)

		type ResponseStruct struct {
			State string `json:"name"`
		}
		respObj := []ResponseStruct{}

		query := `select distinct lower(state) as state from ifsc where bank_name = $1
			order by lower(state)`
		err := database.Select(&respObj, query, bank)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		ctx := context.WithValue(r.Context(), "resData", map[string]interface{}{"res": respObj})
		next.ServeHTTP(w, r.WithContext(ctx))

	})
}

func IFSCCitiesCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		attributes := r.Context().Value("attributes").(map[string]interface{})
		bank := attributes["bank"].(string)
		state := attributes["state"].(string)

		type ResponseStruct struct {
			City string `json:"name"`
		}
		respObj := []ResponseStruct{}

		query := `select distinct lower(city) as city from ifsc where bank_name = $1 and lower(state) = $2
			order by lower(city)`
		err := database.Select(&respObj, query, bank, state)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		ctx := context.WithValue(r.Context(), "resData", map[string]interface{}{"res": respObj})
		next.ServeHTTP(w, r.WithContext(ctx))

	})
}

func IFSCBranchesCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		attributes := r.Context().Value("attributes").(map[string]interface{})
		bank := attributes["bank"].(string)
		city := attributes["city"].(string)
		state := attributes["state"].(string)
		page := attributes["page"].(int)
		searchString := attributes["searchString"].(string)
		limit := 20
		offset := (page - 1) * limit

		type ResponseStruct struct {
			IFSC   string `json:"ifsc"`
			Branch string `json:"branch"`
			State  string `json:"state"`
			City   string `json:"city"`
		}
		respObj := []ResponseStruct{}

		/* order
		   branch, branch%, %branch, %branch%
		*/

		query := `select ifsc, branch, state, city from ifsc where bank_name = $1 and
			lower(state) = $2 and lower(city) = $3 and 
			lower(branch) like $4
			order by 
				(case when lower(branch) = $5 then 1
					when lower(branch) like $6 then 2
					when lower(branch) like $7 then 3
				else 4 end), ifsc
			limit $8 offset $9`
		err := database.Select(&respObj, query, bank, state, city, "%"+searchString+"%", searchString, searchString+"%", "%"+searchString, limit, offset)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		ctx := context.WithValue(r.Context(), "resData", map[string]interface{}{"res": respObj})
		next.ServeHTTP(w, r.WithContext(ctx))

	})
}

func GetBanksCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		userObj := r.Context().Value("user").(authentication.UserStruct)
		userID := userObj.UserID
		sourceEntityID := userObj.SourceEntityID

		type ResponseStruct struct {
			Name string `json:"name"`
		}
		var responseObj []ResponseStruct

		loanApplication, err := loanapplication.GetLatestValidByUser(userID)
		if err != nil {
			logger.WithUser(userID).Error(err)
		}

		if loanApplication.LenderID == constants.DMIID {
			bankResp, err := lenderservice.FetchBanks(r.Context(), &lenderservice.ApplicationReq{
				UserID:            userID,
				SourceEntityID:    sourceEntityID,
				LoanApplicationID: loanApplication.ID.String(),
				LenderID:          constants.DMIID,
			})
			if err != nil {
				logger.WithUser(userID).Error(err)
				panic(err)
			}
			for _, bank := range bankResp {
				temp := ResponseStruct{
					Name: bank.Name,
				}
				responseObj = append(responseObj, temp)
			}
		} else {
			query := "select distinct(bank_name) as name from ifsc"
			err = database.Select(&responseObj, query)
			if err != nil {
				log.Println(err)
				panic(err)
			}
		}

		resData := map[string]interface{}{
			"bank": responseObj,
		}

		ctx := context.WithValue(r.Context(), "resData", resData)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func RetryPipelineCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		userObj := r.Context().Value("user").(authentication.UserStruct)
		userID := userObj.UserID
		sourceEntityID := userObj.SourceEntityID
		mobile := userObj.Mobile
		name := userObj.Name
		email := userObj.Email
		ipAddress := r.RemoteAddr

		waitStatus, _, err := userjourney.GetWaitStatusWithReason(userID)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		if waitStatus != userjourney.WaitStatusIndefinite {
			errString := "user not in indefinite wait"
			errorHandler.CustomError(w, http.StatusForbidden, errString)
			return
		}

		err = userjourney.SetWaitStatus(nil, userID, userjourney.WaitStatusShortPeriod, userjourney.WaitStatusReasonCallingLenderAPI)
		if err != nil {
			logger.WithRequest(r).Error(err)
			panic(err)
		}

		go retryutils.RetryAPI(userID, mobile, name, ipAddress, sourceEntityID, email)

		resData := map[string]interface{}{
			"message": "ok",
		}

		ctx := context.WithValue(r.Context(), "resData", resData)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func PennyDropCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		sourceEntityID := r.Context().Value("sourceEntityID").(string)
		attributes := r.Context().Value("attributes").(map[string]string)
		accountNumber := attributes["accountNumber"]
		ifsc := attributes["ifsc"]

		// Validate IFSC
		bankName, errString := ifscapi.CheckAndGetBankName(ifsc, sourceEntityID)
		if errString != "" {
			log.Println(errString)
			if strings.Contains(errString, "Invalid IFSC") {
				errorHandler.CustomError(w, http.StatusBadRequest, "invalid ifsc")
				return
			}
		}

		// Check for exisiting pennydrop
		type dbStruct struct {
			AccountHolderName string
			UTR               string
			ExternalServiceID string
			RequestID         string
		}
		var dbObj dbStruct
		query := `SELECT request_id as requestid,
		response_data::jsonb->>'accountHolderName' as accountholdername,
		response_data::jsonb->>'utr' as utr, external_service_id as externalserviceid
						from requests
						where request_data::jsonb->>'accountNumber' = $1 and request_data::jsonb->>'ifsc' = $2
							and created_at >= CURRENT_DATE - INTERVAL '2 months' and status = $3 order by created_at desc limit 1`
		err := database.Get(&dbObj, query, accountNumber, ifsc, constants.RequestStatusCompleted)

		//Do Pennydrop
		if err != nil {
			log.Println(err)
			dbObj.AccountHolderName, dbObj.UTR, dbObj.ExternalServiceID, errString, _ = aadhaarApi.VerifyAccountV2(accountNumber, ifsc, constants.DummyPennyUser, "", "", "")
			if errString != "" {
				errorHandler.CustomError(w, http.StatusConflict, errString)
				return
			}
		}

		status := "SUCCESS"
		if errString != "" {
			log.Debugln(errString)
			status = "FAILED"
		}

		// Check supported NACH
		supportedNACH := []string{"physical"}
		netbanking, debit, err := digio.GetAllowedMandateTypeForBank(ifsc, bankName, constants.DummyPennyUser, true)
		if err != nil {
			log.Println(err)
			panic(err)
		}
		if debit {
			supportedNACH = append(supportedNACH, "debit")
		}
		if netbanking {
			supportedNACH = append(supportedNACH, "net_banking")
		}

		var newRequestID string
		// Generate request
		if dbObj.RequestID == "" {
			newRequestID = general.GetUUID()
			dbObj.RequestID = newRequestID
		}

		resData := map[string]interface{}{
			"requestID":         dbObj.RequestID,
			"status":            status,
			"description":       errString,
			"utr":               dbObj.UTR,
			"accountHolderName": dbObj.AccountHolderName,
			"supportedNACH":     supportedNACH,
		}

		requestData, _ := json.Marshal(attributes)
		responseData, _ := json.Marshal(resData)

		statusInt := constants.RequestStatusFailed
		if status == "SUCCESS" {
			statusInt = constants.RequestStatusCompleted
		}

		if newRequestID != "" {
			query = "insert into requests(request_id, request_data, response_data, status, external_service_id, created_at) values ($1, $2, $3, $4, $5, now())"
			_, err = database.Exec(query, newRequestID, string(requestData), string(responseData), statusInt, dbObj.ExternalServiceID)
			if err != nil {
				log.Println(err)
				panic(err)
			}
		}

		ctx := context.WithValue(r.Context(), "resData", resData)
		next.ServeHTTP(w, r.WithContext(ctx))

	})
}

func SubmitBankDetailsCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		sourceEntityID := r.Context().Value("sourceEntityID").(string)
		attributes := r.Context().Value("attributes").(map[string]string)
		accountNumber := attributes["accountNumber"]
		ifsc := attributes["ifsc"]
		loanApplicationID := attributes["loanApplicationID"]

		if !journey.PostOfferAPIAccess(sourceEntityID) {
			panic("you do not have access to this api")
		}

		// Check loan
		var userID string
		query := "select user_id from loan_application where loan_application_id = $1"
		if err := database.Get(&userID, query, loanApplicationID); err != nil {
			log.Println(err)
			errorHandler.CustomError(w, http.StatusConflict, "loan application not found")
			return
		}

		// Check IFSC
		_, errString := ifscapi.CheckAndGetBankName(ifsc, sourceEntityID)
		if errString != "" {
			log.Println(errString)
			if strings.Contains(errString, "Invalid IFSC") {
				errorHandler.CustomError(w, http.StatusBadRequest, "invalid ifsc")
				return
			}
		}

		// Save details
		id := general.GetUUID()
		query = ` insert into user_bank_details (user_bank_details_id, user_id,
			account_number, ifsc_code, status, created_at, created_by )
			values($1, $2, $3, $4, $5, current_timestamp, $6)`
		if _, err := database.Exec(query, id, userID, accountNumber, ifsc, constants.UserBankStatusAdded, userID); err != nil {
			log.Println(err)
			panic(err)
		}

		dateTimeNowString := general.GetTimeStampString()
		go func() {
			activityObj := activity.ActivityEvent{
				UserID:            userID,
				SourceEntityID:    sourceEntityID,
				LoanApplicationID: loanApplicationID,
				EntityType:        constants.EntityTypeSystem,
				EntityRef:         "",
				EventType:         constants.ActivityBankDetailsAdded,
				Description:       "",
			}
			activity.RegisterEvent(&activityObj, dateTimeNowString)
		}()

		var resData = map[string]interface{}{
			"message": "submitted",
		}
		ctx := context.WithValue(r.Context(), "resData", resData)
		next.ServeHTTP(w, r.WithContext(ctx))

	})
}

func ValidateBankDetailsCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		sourceEntityID := r.Context().Value("sourceEntityID").(string)
		attributes := r.Context().Value("attributes").(map[string]string)
		loanApplicationID := attributes["loanApplicationID"]
		requestID := attributes["requestID"]

		if !journey.PostOfferAPIAccess(sourceEntityID) {
			panic("you do not have access to this api")
		}

		var userObj struct {
			UserID    string
			Name      string
			KYCStatus int
		}

		//Check Loan

		query := `select u.user_id as userid, u.name, coalesce(l.kyc_status, 0) as kycstatus, 
					from users u, loan_application l 
				where l.user_id = u.user_id and l.loan_application_id = $1 and l.source_entity_id = $2`

		err := database.Get(&userObj, query, loanApplicationID, sourceEntityID)
		if err != nil {
			log.Println(err)
			errorHandler.CustomError(w, http.StatusBadRequest, "invalid loanApplicationID")
			return
		}

		var requestObj struct {
			RequestID         string
			AccountHolderName string
			AccountNumber     string
			IFSC              string
			UserBankDetailsID string
			UTR               string
			ExternalServiceID string
		}

		// Validate if request ID given
		if requestID != "" {

			query = `select request_id as requestid, response_data::jsonb->>'accountHolderName' as accountholdername, 
			request_data::jsonb->>'accountNumber' as accountnumber, request_data::jsonb->>'ifsc' as ifsc,
			response_data::jsonb->>'utr' as utr, external_service_id as externalserviceid
			from requests where request_id = $1`
			err = database.Get(&requestObj, query, requestID)
			if err != nil {
				log.Println(err)
				errorHandler.CustomError(w, http.StatusBadRequest, "invalid requestID")
				return
			}

			// Insert into user bank details
			requestObj.UserBankDetailsID = general.GetUUID()
			query = `insert into user_bank_details(user_bank_details_id, account_number, ifsc_code, user_id, status, bank_ref, name, created_at, created_by, external_service_id) 
			values ($1, $2, $3, $4, $5, $6, $7, now(), $8, $9)`
			_, err = database.Exec(query, requestObj.UserBankDetailsID, requestObj.AccountNumber, requestObj.IFSC, userObj.UserID, constants.UserBankStatusAdded, requestObj.UTR, requestObj.AccountHolderName, userObj.UserID, requestObj.ExternalServiceID)
			if err != nil {
				log.Println(err)
				panic(err)
			}

		} else {
			// Fetch submitted bank details
			query = `select account_number as accountnumber, ifsc_code as ifsc, user_bank_details_id as userbankdetailsid from user_bank_details 
			where user_id = $1 and status = $2 order by created_at desc limit 1`
			if err := database.Get(&requestObj, query, userObj.UserID, constants.UserBankStatusAdded); err != nil {
				log.Println(err)
				errorHandler.CustomError(w, http.StatusConflict, "account details not found")
				return
			}
		}

		// Validate IFSC and get bank name
		bankName, errString := ifscapi.CheckAndGetBankName(requestObj.IFSC, sourceEntityID)
		if errString != "" {
			log.Println(errString)
			if strings.Contains(errString, "Invalid IFSC") {
				errorHandler.CustomError(w, http.StatusBadRequest, "invalid ifsc")
				return
			}
		}

		var status string
		// Validate Pennydrop
		_, errString, err = pennydrop.Verify(requestObj.AccountNumber, requestObj.IFSC, bankName, userObj.UserID, loanApplicationID, requestObj.UserBankDetailsID, sourceEntityID, false)
		if errString != "" {
			status = "FAILED"
		} else if err != nil {
			log.Println(err)
			panic(err)
		} else {
			status = "SUCCESS"
		}

		// Fetch bank holder name in case of fresh pennydrop
		query = "select name from user_bank_details where user_id = $1 order by created_at desc limit 1"
		err = database.Get(&requestObj.AccountHolderName, query, userObj.UserID)
		if err != nil {
			log.Println(err)
			panic(err)
		}

		// Get match score
		_, score := kycengine.CheckBankAccountHolderName(userObj.UserID, loanApplicationID, requestObj.AccountHolderName, sourceEntityID)

		dateTimeNowString := general.GetTimeStampString()
		go func() {
			defer errorHandler.RecoveryNoResponse()
			var activityObj activity.ActivityEvent
			if status == "SUCCESS" {
				activityObj = activity.ActivityEvent{
					UserID:            userObj.UserID,
					SourceEntityID:    sourceEntityID,
					LoanApplicationID: loanApplicationID,
					EntityType:        constants.EntityTypeSystem,
					EntityRef:         "",
					EventType:         constants.ActivityBankDetailsVerified,
					Description:       "",
				}

				// Add ubd ID in loan_application
				query = "update loan_application set user_bank_details_id = $1, updated_at = now() where loan_application_id = $2"
				_, err = database.Exec(query, requestObj.UserBankDetailsID, loanApplicationID)
				if err != nil {
					log.Println(err)
					panic(err)
				}

			} else {
				activityObj = activity.ActivityEvent{
					UserID:            userObj.UserID,
					SourceEntityID:    sourceEntityID,
					LoanApplicationID: loanApplicationID,
					EntityType:        constants.EntityTypeSystem,
					EntityRef:         "",
					EventType:         constants.ActivityBankVerificationFailed,
					Description:       "",
				}
			}
			activity.RegisterEvent(&activityObj, dateTimeNowString)
		}()

		var resData = map[string]interface{}{
			"status":         status,
			"matchScore":     score,
			"bankHolderName": requestObj.AccountHolderName,
			"panName":        userObj.Name,
		}
		ctx := context.WithValue(r.Context(), "resData", resData)
		next.ServeHTTP(w, r.WithContext(ctx))

	})
}

func SaraloanApplicationStatusWebhookCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		errJSON := map[string]interface{}{
			"message": "Unauthorized!",
		}

		webhookIP := conf.SaraloanCreds["webhookIP"]
		if !strings.Contains(r.RemoteAddr, webhookIP) {
			log.Warn("request unauthorised. IP:", r.RemoteAddr)
			errorHandler.CustomErrorJSON(w, http.StatusUnauthorized, errJSON)
			return
		}

		// read body
		b, err := io.ReadAll(r.Body)
		defer r.Body.Close()
		if err != nil {
			log.Println(err)
			panic(err)
		}
		// write payload to DB
		query := `INSERT INTO saraloan_webhook (webhook_payload, created_at) VALUES ($1, NOW())`
		_, err = database.Exec(query, string(b))
		if err != nil {
			log.Println(err)
			panic(err)
		}

		type saraloanWebhookStruct struct {
			Data map[string]interface{} `json:"data"`
		}

		var obj saraloanWebhookStruct
		err = json.Unmarshal(b, &obj)
		if err != nil {
			log.Println(err)
			errorHandler.CustomError(w, http.StatusOK, "error but ok")
			return
		}

		loanStatus, found := obj.Data["status"].(string)
		if !found {
			panic("status field not found")
		}

		switch loanStatus {
		case saraloan.LineInitiated:
			loanApplicationNo := obj.Data["application_id"].(string)
			obj, customErr := saraloan.GetDetailsByNo(loanApplicationNo)
			if customErr != nil {
				log.Error(customErr)
				switch customErr.HTTPCode {
				case http.StatusConflict:
					panic(customErr.Error())
				default:
					panic(customErr)
				}
			}

			newCreditLineAmount := saraloan.CheckCreditLineAmountChanged(obj.UserID)
			if newCreditLineAmount > 0 {
				query = `UPDATE loan_application set updated_at = NOW(), amount = $2 where loan_application_id = $1`
				_, err = database.Exec(query, obj.LoanApplicationID, newCreditLineAmount)
				if err != nil {
					log.Println(err)
					panic(err)
				}

				query = `UPDATE loan_offer set amount = $2 where loan_id = $1`
				_, err = database.Exec(query, obj.LoanApplicationID, newCreditLineAmount)
				if err != nil {
					log.Println(err)
					panic(err)
				}
				dateTimeNowString := general.GetTimeStampString()
				go func() {
					activityObj := activity.ActivityEvent{
						UserID:            obj.UserID,
						SourceEntityID:    obj.SourceEntityID,
						LoanApplicationID: obj.LoanApplicationID,
						EntityType:        constants.EntityTypeSystem,
						EntityRef:         "",
						EventType:         constants.ActivityLoanOfferUpdated,
						Description:       fmt.Sprintf(`{"New Amount": %.2f}`, newCreditLineAmount),
					}
					activity.RegisterEvent(&activityObj, dateTimeNowString)
				}()
			}
		case saraloan.LineActivated:
			loanApplicationNo := obj.Data["application_id"].(string)
			obj, customErr := saraloan.GetDetailsByNo(loanApplicationNo)
			if customErr != nil {
				log.Error(customErr)
				switch customErr.HTTPCode {
				case http.StatusConflict:
					panic(customErr.Error())
				default:
					panic(customErr)
				}
			}

			signDate, dateTimeNowString := general.GetTimeStampPair()
			if obj.Status >= constants.LoanStatusESign {
				log.Println("loan already signed")
				panic("loan already signed")
			}

			emi, advanceEMI, _ := calc.GetEMI(obj.Method, obj.Amount, obj.Tenure, obj.Interest, signDate, obj.SourceEntityID, obj.LenderID, obj.UserID)
			tx, _ := database.Beginx()

			query := `update loan_application set
					 updated_at = current_timestamp, status = $1,
					 sign_date = $2,
					 emi = $3, advance_emi = $4
					where loan_application_id = $5`

			_, err = tx.Exec(query, constants.LoanStatusESign, dateTimeNowString, emi, advanceEMI, obj.LoanApplicationID)
			if err != nil {
				tx.Rollback()
				log.Println(err)
				panic(err)
			}
			err = usermodulemapping.Create(tx, obj.UserID, obj.UserID, usermodulemapping.ESign, constants.UserModuleStatusCompleted, obj.LoanApplicationID)
			if err != nil {
				tx.Rollback()
				log.Println(err)
				panic(err)
			}

			tx.Commit()

			go func() {
				activityObj := activity.ActivityEvent{
					UserID:            obj.UserID,
					SourceEntityID:    obj.SourceEntityID,
					LoanApplicationID: obj.LoanApplicationID,
					EntityType:        constants.EntityTypeLenderUser,
					EntityRef:         "",
					EventType:         constants.ActivityLoanESigned,
					Description:       "",
				}
				activity.RegisterEvent(&activityObj, dateTimeNowString)

				clutils.ActivateCreditLine(obj.LoanApplicationID, obj.Amount, obj.LenderID, "", "")
			}()
		case saraloan.LineRejected:
			loanApplicationNo := obj.Data["application_id"].(string)
			obj, customErr := saraloan.GetDetailsByNo(loanApplicationNo)
			if customErr != nil {
				log.Error(customErr)
				switch customErr.HTTPCode {
				case http.StatusConflict:
					panic(customErr.Error())
				default:
					panic(customErr)
				}
			}

			errorString, err := underwriting.RejectLoan(obj.LoanApplicationID, obj.LenderID, constants.EntityTypeSystem, "", "line rejected by lender")
			if errorString != "" {
				log.Println(errorString)
				panic(errorString)
			} else if err != nil {
				log.Println(err)
				panic(err)
			}
		case saraloan.AmountDisbursed:
			invoiceID := obj.Data["invoice_id"].(string)
			applicationID := obj.Data["application"].(float64)
			loanData := obj.Data["loan_data"].(map[string]interface{})
			subventedAmountInStr := loanData["subvented_amount"].(string)
			subventedAmount, _ := strconv.ParseFloat(subventedAmountInStr, 64)

			// get user id
			var userID string
			if m, ok := obj.Data["identifiers"].(map[string]interface{}); ok {
				loanApplicationNo := m["application_id"].(string)
				userID, _ = loanapplication.GetUserIDByNo(context.TODO(), loanApplicationNo)
			}

			res, err := saraloan.GetApplicationInvoiceDetails(userID, applicationID, invoiceID)
			if err != nil {
				log.Println(err)
				panic(err)
			}
			type creditlineStruct struct {
				TxnID        string
				CreditlineID string
				ParentloanID string
				LenderID     string
			}
			obj := creditlineStruct{}
			for _, invoiceDetail := range res.InvoiceResult {
				if invoiceID == invoiceDetail.InvoiceID {
					for _, invoiceFile := range invoiceDetail.InvoiceFiles {
						query := `
						select 
    						clt.txn_id as txnid,
    						clt.credit_line_id as creditlineid,
    						cl.parent_loan_id as parentloanid,
   							cl.lender_id as lenderid
						from credit_line_txn clt
						left join credit_line cl on cl.credit_line_id = clt.credit_line_id
						where invoice_no=$1	
						`
						err := database.Get(&obj, query, invoiceFile.InvoiceNumber)
						if err != nil {
							log.Println(err)
							panic("couldn't find invoice number" + invoiceFile.InvoiceNumber)
						}
						loanDisbursedAmount, _ := strconv.ParseFloat(invoiceFile.DisbursedAmount, 64)
						errorString, httpCode, err := disbursal.DisburseCreditLineTxn("UTR_NOT_PROVIDED", invoiceFile.InvoiceDate, obj.TxnID, obj.ParentloanID, obj.LenderID, "", loanDisbursedAmount, subventedAmount)
						if httpCode > 0 {
							errorHandler.CustomError(w, httpCode, errorString)
							return
						} else if err != nil {
							log.Println(err)
							panic(err)
						}
					}
				}
			}
		}

		var resData = map[string]interface{}{
			"message": "success",
		}

		ctx := context.WithValue(r.Context(), "resData", resData)
		next.ServeHTTP(w, r.WithContext(ctx))

	})
}

type leegalityDocumentQueryStruct struct {
	LoanApplicationID string
	SourceEntityID    string
	UserID            string
	CurrentStatus     int
	LenderID          string
	Amount            float64
	LoanApplicationNo string
}

func getLeegalityDocumentObj(documentID string) (leegalityDocumentQueryStruct, error) {
	dbObj := leegalityDocumentQueryStruct{}
	query := `select l.source_entity_id as sourceentityid, l.user_id as userid, l.lender_id as lenderid, l.amount as amount, 
			l.loan_application_id as loanapplicationid, e.esign_status as currentstatus, l.loan_application_no as loanapplicationno from loan_application l join esign_attempt e on
			l.loan_application_id = e.loan_application_id where e.document_id = $1 and e.esign_status not in ($2, $3)`
	err := database.Get(&dbObj, query, documentID, constants.ESignStatusDeleted, constants.ESignStatusExpired)
	return dbObj, err
}

func LeegalityESignCallback(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		decoder := json.NewDecoder(r.Body)
		defer r.Body.Close()
		var reqObj leegality.WebhookRequestStruct
		err := decoder.Decode(&reqObj)
		if err != nil {
			log.Println(err)
			panic(err)
		}
		payload, _ := json.Marshal(reqObj)

		query := `INSERT INTO leegality_webhook (document_id, webhook_payload, created_at) values ($1, $2, NOW())`
		_, err = database.Exec(query, reqObj.DocumentID, string(payload))
		if err != nil {
			log.Println(err)
			panic(err)
		}
		dbObj, err := getLeegalityDocumentObj(reqObj.DocumentID)
		if err != nil {
			log.Println(err)
			panic("documentID not found")
		}

		leegalityCreds := conf.GetLeegalityCreds(dbObj.LenderID)
		salt := leegalityCreds["salt"]

		if !general.ValidMAC(reqObj.DocumentID, salt, reqObj.Mac) {
			logger.WithRequest(r).Println("mac mismatch, client cannot be verified")
			errorHandler.CustomError(w, http.StatusOK, "mac mismatch, client cannot be verified")
			return
		}

		obj, err := loanutils.GetLoanOfferDetails(dbObj.LoanApplicationID, dbObj.UserID)

		if err != nil {
			log.Println(err)
			errorHandler.CustomError(w, http.StatusOK, "something went wrong")
			return
		}

		//update signer object
		go func() {

			signerObj, err := json.Marshal(reqObj.Signer)
			if err != nil {
				log.Println(err)
			}
			err = esignattempt.Update(nil, context.TODO(), esignattempt.EsignAttempt{
				DocumentID:        reqObj.DocumentID,
				LoanApplicationID: dbObj.LoanApplicationID,
				SignerObject:      signerObj,
			})
			if err != nil {
				log.Println(err)
			}

		}()

		if reqObj.Request.Expired && dbObj.CurrentStatus != constants.ESignStatusFailed && len(reqObj.Messages) > 0 &&
			(reqObj.Messages[0].Code == "smartname.verification.failed" || reqObj.Messages[0].Code == "name.verification.failed") {
			go func() {
				defer errorHandler.RecoveryNoResponse()
				err = esignattempt.Update(nil, context.TODO(), esignattempt.EsignAttempt{
					DocumentID:         reqObj.DocumentID,
					LoanApplicationID:  dbObj.LoanApplicationID,
					ESignStatus:        constants.ESignStatusFailed,
					ESignFailureReason: reqObj.Messages[0].Message,
					SmartMatchScore:    reqObj.Verification.SmartName,
				})
				if err != nil {
					log.Println(err)
					panic(err)
				}
			}()

			_, dateTimeNowString := general.GetTimeStampPair()
			go func() {
				activityObj := activity.ActivityEvent{
					UserID:            dbObj.UserID,
					SourceEntityID:    dbObj.SourceEntityID,
					LoanApplicationID: dbObj.LoanApplicationID,
					EntityType:        constants.EntityTypeCustomer,
					EntityRef:         dbObj.UserID,
					EventType:         constants.ActivityLoanESignFailed,
					Description:       reqObj.Messages[0].Message,
				}
				activity.RegisterEvent(&activityObj, dateTimeNowString)
			}()
			err = usermodulemapping.Create(nil, dbObj.UserID, dbObj.UserID, usermodulemapping.ESign, constants.UserModuleStatusFailed, dbObj.LoanApplicationID)
			if err != nil {
				log.Println(err)
				panic(err)
			}
		} else if reqObj.Request.Expired && dbObj.CurrentStatus != constants.ESignStatusExpired {
			err = esignattempt.Update(nil, context.TODO(), esignattempt.EsignAttempt{
				DocumentID:        reqObj.DocumentID,
				LoanApplicationID: dbObj.LoanApplicationID,
				ESignStatus:       constants.ESignStatusExpired,
			})
			if err != nil {
				log.Println(err)
				panic(err)
			}
		} else if reqObj.Request.Signed && len(reqObj.Files) > 0 {
			if obj.Status >= constants.LoanStatusESign {
				log.Println("loan already signed for: ", reqObj.DocumentID)
				//panic("loan already signed")
			} else {
				boostID := booster.GetLastBoostID(dbObj.LoanApplicationID)

				if boostID != "" {
					_ = postagreement.UpdateBoostAttemptOnSigningAgreement(dbObj.UserID, dbObj.SourceEntityID, dbObj.LoanApplicationID, boostID, postagreement.LocationStruct{}, obj)
					_, err := leegality.UpdateSignedAgreement(dbObj.UserID, dbObj.LoanApplicationID, reqObj.DocumentID, reqObj.Files[0], reqObj.Verification.SmartName)
					if err != nil {
						log.Println(err)
						panic(err)
					}
				} else {
					_ = postagreement.UpdateModuleOnSigningAgreement(dbObj.UserID, dbObj.LoanApplicationID, dbObj.SourceEntityID, obj, postagreement.LocationStruct{})
					objectKey, err := leegality.UpdateSignedAgreement(dbObj.UserID, dbObj.LoanApplicationID, reqObj.DocumentID, reqObj.Files[0], reqObj.Verification.SmartName)
					if err != nil {
						log.Println(err)
						panic(err)
					}
					_, err = leegality.UpdateAuditTrailDoc(dbObj.UserID, dbObj.LoanApplicationID, reqObj.DocumentID, reqObj.AuditTrail)
					if err != nil {
						logger.WithLoanApplication(dbObj.LoanApplicationID).Error(err)
						errorHandler.ReportToSentryWithoutRequest(err)
					}

					go postagreement.CallLenderDecisionAPI(dbObj.UserID, obj.LenderID, dbObj.LoanApplicationID, dbObj.SourceEntityID, objectKey, true)

					go postagreement.HandleDisbursalAfterAgreementSign(dbObj.UserID, dbObj.LoanApplicationID, dbObj.SourceEntityID, obj.LenderID, obj.LoanType)

					lenderObj, err := lender.Get(obj.LenderID)
					if err != nil {
						logger.WithRequest(r).Error(err)
					}

					pdfURL := s3.GetPresignedURLS3(objectKey, 300)

					if journey.SendSMSPostAgreement(dbObj.SourceEntityID) {
						go func() {
							defer errorHandler.RecoveryNoResponse()
							userObj, err := users.Get(dbObj.UserID)
							if err != nil {
								logger.WithUser(dbObj.UserID).Error(err)
								panic(err)
							}

							err = agreement.SendAgreementViaSMS(dbObj.UserID, dbObj.SourceEntityID, userObj.Mobile, userObj.Name, dbObj.LoanApplicationNo, lenderObj.LenderName, pdfURL, dbObj.Amount, dbObj.LenderID)
							if err != nil {
								logger.WithUser(dbObj.UserID).Error(err)
							}
						}()
					}

					if journey.SendCustomerMail(dbObj.SourceEntityID) {
						go func(lenderName string) {
							signDate, _ := general.GetTimeStampPair()

							// now fetch source entity information
							sourceEntityObj, err := sourceentitymodel.FetchSourceEntityInfo(dbObj.SourceEntityID)
							if err != nil {
								log.Println(err)
							}
							agreementObj, err := agreementutils.GetAgreementDataValues(dbObj.LoanApplicationID, false, "")
							if err != nil {
								log.Println(err)
							}

							emi, advanceEMI, _ := calc.GetEMI(agreementObj.Method, agreementObj.Amount, agreementObj.Tenure,
								agreementObj.Interest, signDate, agreementObj.SourceEntityID, agreementObj.LenderID, dbObj.UserID)
							gstAmount := agreementObj.ProcessingFee * agreementObj.GST / 100
							processingFeePlusGST := agreementObj.ProcessingFee + gstAmount
							processingFeePlusGST = math.Round(processingFeePlusGST*100) / 100
							//TODO: Get a insurance object
							premium, _ := insuranceutils.GetUnpaidPremium(dbObj.LoanApplicationID)
							insuranceType, _ := insuranceutils.GetUnpaidInsuranceType(dbObj.LoanApplicationID)
							disbursalAmount := calc.CalculateDisbursalAmount(agreementObj.Amount, agreementObj.ProcessingFee,
								agreementObj.GST, advanceEMI, premium, commonutils.OtherCharges(agreementObj.LenderID, dbObj.UserID), insuranceType)
							insurancePremiumPlusGST := calc.CalculateAmountWithGST(premium, agreementObj.GST)
							paymentDetailObj := agreement.PaymentDetailStruct{
								EMI:                     emi,
								ProcessingFeePlusGST:    processingFeePlusGST,
								AdvanceEMIAmount:        advanceEMI,
								DisbursalAmount:         disbursalAmount,
								InsurancePremiumPlusGST: insurancePremiumPlusGST,
							}

							_, errStr := agreement.SendEmailToUser(agreementObj, lenderName, sourceEntityObj.SourceEntityName,
								pdfURL, paymentDetailObj)
							if errStr != "" {
								log.Println(errStr)
							}
						}(lenderObj.LenderName)
					}
				}
			}
		}
		resData := map[string]string{
			"message": "OK",
		}
		ctx := context.WithValue(r.Context(), "resData", resData)
		next.ServeHTTP(w, r.WithContext(ctx))

	})
}

func LeegalityESignCallbackV2(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		decoder := json.NewDecoder(r.Body)
		defer r.Body.Close()
		var reqObj leegality.WebhookRequestStruct
		err := decoder.Decode(&reqObj)
		if err != nil {
			logger.WithRequest(r).Errorln(err)
			panic(err)
		}
		payload, _ := json.Marshal(reqObj)

		query := `INSERT INTO leegality_webhook (document_id, webhook_payload, created_at) values ($1, $2, NOW())`
		_, err = database.Exec(query, reqObj.DocumentID, string(payload))
		if err != nil {
			logger.WithRequest(r).Errorln(err)
			panic(err)
		}

		dbObj, err := getLeegalityDocumentObj(reqObj.DocumentID)
		if err == sql.ErrNoRows {
			logger.WithRequest(r).Println("document already deleted or expired")
			errorHandler.CustomError(w, http.StatusOK, "document already deleted or expired")
			return
		} else if err != nil {
			logger.WithRequest(r).Errorln(err)
			panic(err)
		}

		ctx := context.WithValue(r.Context(), "userID", dbObj.UserID)
		r = r.WithContext(ctx)

		leegalityCreds := conf.GetLeegalityCreds(dbObj.LenderID)
		salt := leegalityCreds["salt"]

		if !general.ValidMAC(reqObj.DocumentID, salt, reqObj.Mac) {
			logger.WithRequest(r).Println("mac mismatch, client cannot be verified")
			errorHandler.CustomError(w, http.StatusOK, "mac mismatch, client cannot be verified")
			return
		}

		obj, err := loanutils.GetLoanOfferDetails(dbObj.LoanApplicationID, dbObj.UserID)
		if err != nil {
			logger.WithRequest(r).Errorln(err)
			errorHandler.CustomError(w, http.StatusOK, "something went wrong")
			return
		}

		if (strings.ToUpper(reqObj.DocumentStatus) == constants.LeegalityStatusExpired || reqObj.Request.Expired || reqObj.Request.Rejected) && dbObj.CurrentStatus != constants.ESignStatusFailed && len(reqObj.Messages) > 0 &&
			(reqObj.Messages[0].Code == "smartname.verification.failed" || reqObj.Messages[0].Code == "name.verification.failed") {
			go func() {
				defer errorHandler.RecoveryNoResponse()
				err = esignattempt.Update(nil, context.TODO(), esignattempt.EsignAttempt{
					DocumentID:         reqObj.DocumentID,
					LoanApplicationID:  dbObj.LoanApplicationID,
					ESignStatus:        constants.ESignStatusFailed,
					ESignFailureReason: reqObj.Messages[0].Message,
					SmartMatchScore:    reqObj.Verification.SmartName,
				})
				if err != nil {
					logger.WithRequest(r).Errorln(err)
					panic(err)
				}
			}()

			_, dateTimeNowString := general.GetTimeStampPair()
			go func() {
				activityObj := activity.ActivityEvent{
					UserID:            dbObj.UserID,
					SourceEntityID:    dbObj.SourceEntityID,
					LoanApplicationID: dbObj.LoanApplicationID,
					EntityType:        constants.EntityTypeCustomer,
					EntityRef:         dbObj.UserID,
					EventType:         constants.ActivityLoanESignFailed,
					Description:       reqObj.Messages[0].Message,
				}
				activity.RegisterEvent(&activityObj, dateTimeNowString)
			}()

			err = usermodulemapping.Create(nil, dbObj.UserID, dbObj.UserID, "ESIGN", constants.UserModuleStatusFailed, dbObj.LoanApplicationID)
			if err != nil {
				logger.WithRequest(r).Errorln(err)
				panic(err)
			}
		} else if (strings.ToUpper(reqObj.DocumentStatus) == constants.LeegalityStatusExpired || reqObj.Request.Expired || reqObj.Request.Rejected) && dbObj.CurrentStatus != constants.ESignStatusExpired {
			err = esignattempt.Update(nil, context.TODO(), esignattempt.EsignAttempt{
				DocumentID:        reqObj.DocumentID,
				LoanApplicationID: dbObj.LoanApplicationID,
				ESignStatus:       constants.ESignStatusExpired,
			})
			if err != nil {
				logger.WithRequest(r).Errorln(err)
				panic(err)
			}
		} else if strings.ToUpper(reqObj.DocumentStatus) == constants.LeegalityStatusCompleted || (strings.ToUpper(reqObj.DocumentStatus) == constants.LeegaltityStatusSent) {
			if obj.Status >= constants.LoanStatusESign {
				logger.WithRequest(r).Println("loan already signed for: ", reqObj.DocumentID)
				//panic("loan already signed")
			} else {
				_, err, _ := leegality.GetESignStatus(dbObj.UserID, reqObj.DocumentID, dbObj.LenderID)
				if err != nil {
					logger.WithUser(dbObj.UserID).Error(err, "error from GetESignStatus")

					var errorFromLeegalityAPI *structs.CustomError
					// If error is identified as an error in fetching leegality document, only then send an error to the webhook
					if errors.As(err, &errorFromLeegalityAPI) {
						logger.WithUser(dbObj.UserID).Error(err, "error received from leegality documentDetails API")
						errorHandler.CustomError(w, http.StatusConflict, "something went wrong")
						return
					}

					if !leegality.ShouldIgnoreErrorsFromGetEsignDetailsApi(err) {
						errorHandler.ReportToSentryWithoutRequest(fmt.Errorf("error handling leegality webhook, userID: %s, error: %v", dbObj.UserID, err))
					}

				}
			}
		}
		err = postESignTask(dbObj.UserID, dbObj.SourceEntityID, reqObj)
		if err != nil {
			logger.WithUser(dbObj.UserID).Error(err, "error performing Post eSign task")
			panic(err)
		}
		resData := map[string]string{
			"message": "OK",
		}
		ctx = context.WithValue(r.Context(), "resData", resData)
		next.ServeHTTP(w, r.WithContext(ctx))

	})
}

// AxioAccountDetailsWebhookCont handles account details webhook from axio
func AxioAccountDetailsWebhookCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		attributes := r.Context().Value("attributes").(map[string]interface{})

		payload, err := json.Marshal(attributes)
		if err != nil {
			logger.WithRequest(r).Error(err)
			panic(err)
		}

		webhookID := general.GetUUID()
		err = webhooklogs.Create(nil, webhookID, capitalfloat.AccountDetailsWebhook, string(payload))
		if err != nil {
			logger.WithRequest(r).Error(err)
			panic(err)
		}

		var webhookStruct capitalfloat.AccountDetailsWebhookStruct
		err = json.Unmarshal(payload, &webhookStruct)
		if err != nil {
			logger.WithRequest(r).Error(err)
			panic(err)
		}

		responseMsg, customError := capitalfloat.AccountDetailsHookHandler(webhookID, &webhookStruct)
		if customError != nil {
			logger.WithRequest(r).Error(customError)
			switch customError.HTTPCode {
			case http.StatusConflict:
				// not sending it to sentry
				// handled error
				errorHandler.CustomError(w, customError.HTTPCode, customError.Err.Error())
				return
			}
			panic(customError)
		}

		resData := map[string]interface{}{
			"message": responseMsg,
		}

		ctx := context.WithValue(r.Context(), "resData", resData)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// AxioRepaymentDetailsWebhookCont handles repayment webhook from axio
func AxioRepaymentDetailsWebhookCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		attributes := r.Context().Value("attributes").(map[string]interface{})

		var webhookID string
		var repaymentWebhookStruct capitalfloat.RepaymentWebhook

		payload, err := json.Marshal(attributes)
		if err != nil {
			logger.WithRequest(r).Error(err)
			panic(err)
		}

		webhookID = general.GetUUID()
		err = webhooklogs.Create(nil, webhookID, capitalfloat.RepaymentDetailsWebhook, string(payload))
		if err != nil {
			logger.WithRequest(r).Error(err)
			panic(err)
		}

		err = json.Unmarshal(payload, &repaymentWebhookStruct)
		if err != nil {
			logger.WithRequest(r).Error(err)
			panic(err)
		}

		_, customErr := capitalfloat.RepaymentWebhookHandler(webhookID, capitalfloat.RepaymentDetailsWebhook, repaymentWebhookStruct)
		if customErr != nil {
			logger.WithRequest(r).Error(customErr)
			switch customErr.HTTPCode {
			case http.StatusConflict, http.StatusFailedDependency:
				errorHandler.CustomError(w, http.StatusConflict, customErr.Err.Error())
				return
			default:
				panic(customErr)
			}
		}

		resData := map[string]interface{}{
			"message": "axio repayment callback successfull",
		}

		ctx := context.WithValue(r.Context(), "resData", resData)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// AxioUserJourneyUpdatesWebhookCont handles onboarding journey updates from axio - includes user drop off
func AxioUserJourneyUpdatesWebhookCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		attributes := r.Context().Value("attributes").(map[string]interface{})

		payload, err := json.Marshal(attributes)
		if err != nil {
			logger.WithRequest(r).Error(err)
			panic(err)
		}
		webhookID := general.GetUUID()
		err = webhooklogs.Create(nil, webhookID, capitalfloat.JourneyUpdateWebhook, string(payload))
		if err != nil {
			logger.WithRequest(r).Error(err)
			panic(err)
		}

		var webhookStruct capitalfloat.WebhookStruct
		err = json.Unmarshal(payload, &webhookStruct)
		if err != nil {
			logger.WithRequest(r).Error(err)
			panic(err)
		}

		customError := capitalfloat.OnboardingHookHandlerFunction(&webhookStruct, webhookID)
		if customError != nil {
			logger.WithRequest(r).Error(customError)
			switch customError.HTTPCode {
			case http.StatusConflict:
				// not sending it to sentry
				// handled error
				errorHandler.CustomError(w, http.StatusConflict, customError.Err.Error())
				return
			}
			panic(customError)
		}
		resData := map[string]interface{}{
			"message": "axio journey updates callback successfull",
		}

		ctx := context.WithValue(r.Context(), "resData", resData)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// AxioKYCRedirectCont handles redirection to finbox post axio KYC web view
func AxioKYCRedirectCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		attributes := r.Context().Value("attributes").(map[string]interface{})
		redirectionReq := attributes["redirectionReq"].(capitalfloat.RedirectionRequest)
		decodedPayload := attributes["decodedPayload"].([]byte)

		var webhookID string
		var onboardingResp capitalfloat.WebhookStruct
		var isGracefulExitRequired bool
		var htmlText string
		var customError *structs.CustomError

		b, err := json.Marshal(redirectionReq)
		if err != nil {
			logger.WithRequest(r).Error(err)
			errorHandler.ReportToSentry(r, err)
			goto html
		}

		webhookID = general.GetUUID()
		err = webhooklogs.Create(nil, webhookID, capitalfloat.KYCFormPost, string(b))
		if err != nil {
			logger.WithRequest(r).Error(err)
			errorHandler.ReportToSentry(r, err)
			goto html
		}

		err = json.Unmarshal(b, &redirectionReq)
		if err != nil {
			logger.WithRequest(r).Error(err)
			errorHandler.ReportToSentry(r, err)
			goto html
		}

		err = json.Unmarshal(decodedPayload, &onboardingResp)
		if err != nil {
			logger.WithRequest(r).Error(err)
			errorHandler.ReportToSentry(r, err)
			goto html
		}

		isGracefulExitRequired = capitalfloat.CheckGraceFulExit(&onboardingResp, capitalfloat.KYCFormPost)
		if isGracefulExitRequired {
			goto html
		}

		customError = capitalfloat.OnboardingHookHandlerFunction(&onboardingResp, webhookID)
		if customError != nil {
			logger.WithRequest(r).Error(customError)
			switch customError.HTTPCode {
			case http.StatusConflict:
				// not sending it to sentry
				// handled error
				goto html
			}
			errorHandler.ReportToSentry(r, customError)
			goto html
		}

	html:
		var exitCondition string
		if isGracefulExitRequired {
			exitCondition = `?exit=true`
		}
		// TODO : Remove hardcoded source entity from URL
		jsEvent := fmt.Sprintf("window.location.replace('%s/%s')", conf.GetWebSDKBaseURLV2(constants.TataBNPLID), exitCondition)
		timeOutEvent := fmt.Sprintf("setTimeout(() => window.location.replace('%s/%s'), 1000)", conf.GetWebSDKBaseURLV2(constants.TataBNPLID), exitCondition)
		pageData := map[string]interface{}{
			"Title":        "Please Wait",
			"SubText":      "We are redirecting",
			"Event":        jsEvent,
			"TimeOutEvent": timeOutEvent,
		}
		htmlText = general.GetStringFromTemplate(htmltemplates.TataDarkThemePendingPage, pageData)

		w.Header().Set("Content-Type", "text/html")
		fmt.Fprint(w, htmlText)
	})
}

// AxioNACHRedirectCont handles redirection to finbox post axio SI web view
func AxioNACHRedirectCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		attributes := r.Context().Value("attributes").(map[string]interface{})
		redirectionReq := attributes["redirectionReq"].(capitalfloat.RedirectionRequest)
		decodedPayload := attributes["decodedPayload"].([]byte)

		var webhookID string
		var siWebhook capitalfloat.SIDetailsWebhook
		var isGracefulExitRequired bool
		var customError *structs.CustomError

		b, err := json.Marshal(redirectionReq)
		if err != nil {
			logger.WithRequest(r).Error(err)
			errorHandler.ReportToSentry(r, err)
			goto html
		}

		webhookID = general.GetUUID()
		err = webhooklogs.Create(nil, webhookID, capitalfloat.AutoPayFormPost, string(b))
		if err != nil {
			logger.WithRequest(r).Error(err)
			errorHandler.ReportToSentry(r, err)
			goto html
		}

		err = json.Unmarshal(b, &redirectionReq)
		if err != nil {
			logger.WithRequest(r).Error(err)
			errorHandler.ReportToSentry(r, err)
			goto html
		}
		err = json.Unmarshal(decodedPayload, &siWebhook)
		if err != nil {
			logger.WithRequest(r).Error(err)
			errorHandler.ReportToSentry(r, err)
			goto html
		}
		isGracefulExitRequired = capitalfloat.CheckGraceFulExit(&siWebhook, capitalfloat.AutoPayFormPost)
		if isGracefulExitRequired {
			goto html
		}

		customError = capitalfloat.SIHookHandler(webhookID, &siWebhook)
		if customError != nil {
			logger.WithRequest(r).Error(customError)
			switch customError.HTTPCode {
			case http.StatusConflict:
				// not sending it to sentry
				// handled error
				goto html
			}
			errorHandler.ReportToSentry(r, customError)
			goto html
		}
	html:
		var exitCondition string
		if isGracefulExitRequired {
			exitCondition = `?exit=true`
		}
		// TODO : Remove hardcoded program and source entity from URL
		jsEvent := fmt.Sprintf(`window.location.replace('%s/%s');`, conf.GetWebSDKBaseURLV2(constants.TataBNPLID), exitCondition)
		timeOutEvent := fmt.Sprintf(`setTimeout(() => window.location.replace('%s/%s'), 1000)`, conf.GetWebSDKBaseURLV2(constants.TataBNPLID), exitCondition)
		pageData := map[string]interface{}{
			"Title":        "Please Wait",
			"SubText":      "We are redirecting",
			"Event":        jsEvent,
			"TimeOutEvent": timeOutEvent,
		}
		htmlText := general.GetStringFromTemplate(htmltemplates.TataDarkThemePendingPage, pageData)

		w.Header().Set("Content-Type", "text/html")
		fmt.Fprint(w, htmlText)
	})
}

func AxioRepaymentRedirectCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		attributes := r.Context().Value("attributes").(map[string]interface{})
		redirectionReq := attributes["redirectionReq"].(capitalfloat.RedirectionRequest)
		decodedPayload := attributes["decodedPayload"].([]byte)

		var webhookID string
		paymentStatus := "?orderstatus=Failed"
		var repaymentWebhook capitalfloat.RepaymentWebhook
		var customErr *structs.CustomError
		var isSuccess bool

		b, err := json.Marshal(redirectionReq)
		if err != nil {
			logger.WithRequest(r).Error(err)
			errorHandler.ReportToSentry(r, err)
			goto html
		}

		webhookID = general.GetUUID()
		err = webhooklogs.Create(nil, webhookID, capitalfloat.RepaymentFormPost, string(b))
		if err != nil {
			logger.WithRequest(r).Error(err)
			errorHandler.ReportToSentry(r, err)
			goto html
		}

		err = json.Unmarshal(decodedPayload, &repaymentWebhook)
		if err != nil {
			logger.WithRequest(r).Error(err)
			errorHandler.ReportToSentry(r, err)
			goto html
		}

		isSuccess, customErr = capitalfloat.RepaymentWebhookHandler(webhookID, capitalfloat.RepaymentFormPost, repaymentWebhook)
		if customErr != nil {
			logger.WithRequest(r).Error(err)
			switch customErr.HTTPCode {
			case http.StatusConflict, http.StatusFailedDependency:
				// not sending to sentry
				goto html
			default:
				errorHandler.ReportToSentry(r, customErr)
				goto html
			}
		}

		if isSuccess {
			paymentStatus = "?orderstatus=Success"
		}

	html:
		// TODO : Remove hardcoded program and source entity from URL
		jsEvent := fmt.Sprintf("window.location.replace('%s/tata/payment-status%s')", conf.GetWebSDKBaseURLV2(constants.TataBNPLID), paymentStatus)
		timeOutEvent := fmt.Sprintf("setTimeout(() => window.location.replace('%s/tata/payment-status%s'), 1000)", conf.GetWebSDKBaseURLV2(constants.TataBNPLID), paymentStatus)
		pageData := map[string]interface{}{
			"Title":        "Please Wait",
			"SubText":      "We are redirecting",
			"Event":        jsEvent,
			"TimeOutEvent": timeOutEvent,
		}
		htmlText := general.GetStringFromTemplate(htmltemplates.TataDarkThemePendingPage, pageData)

		w.Header().Set("Content-Type", "text/html")
		fmt.Fprint(w, htmlText)
	})
}

// AxioWebhookIndependentSICont handles SI webhook from axio
func AxioWebhookIndependentSICont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		attributes := r.Context().Value("attributes").(map[string]interface{})

		payload, err := json.Marshal(attributes)
		if err != nil {
			logger.WithRequest(r).Error(err)
			panic(err)
		}
		webhookID := general.GetUUID()
		err = webhooklogs.Create(nil, webhookID, capitalfloat.IndependentSIWebhook, string(payload))
		if err != nil {
			logger.WithRequest(r).Error(err)
			panic(err)
		}

		var siWebhook capitalfloat.SIDetailsWebhook

		b, err := json.Marshal(attributes)
		if err != nil {
			logger.WithRequest(r).Error(err)
			panic(err)
		}

		err = json.Unmarshal(b, &siWebhook)
		if err != nil {
			logger.WithRequest(r).Error(err)
			panic(err)
		}

		customError := capitalfloat.SIHookHandler(webhookID, &siWebhook)
		if customError != nil {
			logger.WithRequest(r).Error(customError)
			switch customError.HTTPCode {
			case http.StatusConflict:
				// not sending it to sentry
				// handled error

				errorHandler.CustomError(w, customError.HTTPCode, customError.Err.Error())
				return
			}
			panic(customError)
		}

		resData := map[string]interface{}{
			"message": "si webhook successfull",
		}

		ctx := context.WithValue(r.Context(), "resData", resData)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func WesternCapitalWebhookCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		errJSON := map[string]interface{}{
			"message": "Unauthorized!",
		}

		webhookIP := conf.WesternCapitalCreds["webhookIP"]
		if !strings.Contains(r.RemoteAddr, webhookIP) {
			log.Warn("request unauthorised. IP:", r.RemoteAddr)
			errorHandler.CustomErrorJSON(w, http.StatusUnauthorized, errJSON)
			return
		}

		// read body
		b, err := io.ReadAll(r.Body)
		defer r.Body.Close()
		if err != nil {
			logger.WithRequest(r).Println(err)
			panic(err)
		}
		// write payload to DB
		query := `INSERT INTO western_capital_webhook (webhook_payload, created_at) VALUES ($1, NOW())`
		_, err = database.Exec(query, string(b))
		if err != nil {
			logger.WithRequest(r).Println(err)
			panic(err)
		}

		type wcDisbursalWebhook struct {
			Entity  string `json:"entity"`
			Payload struct {
				Status          string `json:"status" validate:"required"`
				Amount          string `json:"amount"`
				TransactionTime string `json:"transaction_time"`
				TransactionID   string `json:"transaction_id" validate:"required"`
				ClosureReason   string `json:"closure_reason"`
				NachUmrn        string `json:"nach_umrn"`
				LoanAccNo       string `json:"loan_ac_no" validate:"required"`
				LeadID          string `json:"lead_id" validate:"required"`
			} `json:"payload"`
			Event     string `json:"event"`
			ID        string `json:"id"`
			CreatedAt string `json:"created_at"`
		}

		var obj wcDisbursalWebhook
		err = json.Unmarshal(b, &obj)
		if err != nil {
			logger.WithRequest(r).Println(err)
			errorHandler.CustomError(w, http.StatusOK, "error but ok")
			return
		}

		err = validator.New().Struct(obj)
		if err != nil {
			logger.WithRequest(r).Println(err)
			panic("validation failed")
		}

		getLoanApplicationIDQuery := `select loan_application_id, lender_id from loan_application where loan_application_no = $1`
		type lenderLoanID struct {
			LenderID string `db:"lender_id"`
			LoanID   string `db:"loan_application_id"`
		}

		idstruct := lenderLoanID{}

		loanStatus := obj.Payload.Status
		switch loanStatus {
		case constants.WesternCapitalDisbursed:
			err = database.Get(&idstruct, getLoanApplicationIDQuery, obj.Payload.LoanAccNo)
			if err != nil {
				panic(err)
			}
			_, err = loanutils.DisburseLoan(idstruct.LoanID, idstruct.LenderID, obj.Payload.TransactionID, "", "", false)
			if err != nil {
				panic(err)
			}
		default:
			errorHandler.CustomError(w, http.StatusOK, fmt.Sprintf("Loan isn't disbursed and the status of loan is %s", loanStatus))
		}

		var resData = map[string]interface{}{
			"message": "success",
		}

		ctx := context.WithValue(r.Context(), "resData", resData)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func ClearCacheCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)
		apiKey := r.Header.Get("x-api-key")
		sourceentitymodel.ClearSourceEntityFromCache(apiKey)

		ctx := context.WithValue(r.Context(), "resData", "ok")
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// SendTransactionReportCont extracts the list of transactions to a csv for a given date and emails the list
func SendTransactionReportCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		attributes := r.Context().Value("attributes").(map[string]interface{})
		sourceEntityID := attributes["sourceEntityID"].(string)
		txnDate := attributes["date"].(time.Time)

		type transactionStruct struct {
			MerchantOrderID       string  `db:"merchant_order_id"`
			CartSourceEntityTxnID string  `db:"cart_source_entity_txn_id"`
			SourceEntityTxnID     string  `db:"source_entity_txn_id"`
			CreatedAt             string  `db:"created_at"`
			Status                int     `db:"status"`
			CreditLineType        string  `db:"credit_line_type"`
			CartAmount            string  `db:"cartamount"`
			MerchantUserID        string  `db:"merchantuserid"`
			Amount                float64 `db:"amount"`
			MerchantID            string  `db:"merchant_id"`
			LenderName            string  `db:"lender_name"`
			UpdatedAt             string  `db:"updatedat"`
		}

		var creditLineTypeMap = map[string]string{
			"emi":  "EMI",
			"bnpl": "BNPL",
		}

		var transactions []transactionStruct
		query := `select clt.merchant_order_id, 
			clt.cart_source_entity_txn_id,
			clt.source_entity_txn_id, 
			to_char(clt.created_at AT TIME ZONE 'utc' AT TIME ZONE 'Asia/Calcutta', 'YYYY-MM-DD HH24:MI:SS') as created_at, 
			clt.status, 
			cl.credit_line_type,  
			coalesce(clt.txn_metadata::jsonb ->> 'cartAmount', '') as cartamount,
			coalesce(clt.parent_txn_metadata::jsonb ->> 'merchantUserID', '') as merchantuserid,
			clt.amount, 
			clt.merchant_id, 
			l.lender_name, 
			coalesce(to_char(clt.updated_at AT TIME ZONE 'utc' AT TIME ZONE 'Asia/Calcutta', 'YYYY-MM-DD HH24:MI:SS'), '') as updatedat
		from credit_line_txn clt 
		join credit_line cl on clt.credit_line_id = cl.credit_line_id and cl.source_entity_id = $1
		join lender l on cl.lender_id = l.lender_id
		where to_char(clt.created_at AT TIME ZONE 'utc' AT TIME ZONE 'Asia/Calcutta', 'YYYY-MM-DD') = $2
		order by clt.created_at desc`
		err := database.Select(&transactions, query, sourceEntityID, txnDate.Format(constants.DateFormat))
		if err != nil {
			log.Error(err)
			errorHandler.ReportToSentryWithoutRequest(err)
			panic(err)
		}

		log.Debug("transactions count --> ", len(transactions))

		transactionCSVFile := fmt.Sprintf("%s_*.csv", sourceEntityID)
		csvFile, err := os.CreateTemp("/tmp/", filepath.Clean(transactionCSVFile))
		if err != nil {
			log.Error(err)
			panic(err)
		}
		defer os.Remove(csvFile.Name())
		defer csvFile.Close()

		writer := csv.NewWriter(csvFile)

		if csvFile != nil {
			// adding headers
			err := writer.Write([]string{"merchantOrderId", "transactionId", "createdOn", "UpdatedOn", "status", "tenderId/Type", "totalAmount", "loanAmount", "merchantId", "LenderName", "subTransactionID", "merchantRefID"})
			if err != nil {
				log.Error(err)
				panic(err)
			}
		} else {
			panic("error writing csv")
		}

		for i := range transactions {
			err = writer.Write([]string{
				transactions[i].MerchantOrderID,
				transactions[i].CartSourceEntityTxnID,
				transactions[i].CreatedAt,
				transactions[i].UpdatedAt,
				constants.CreditLineTxnStatusNumToStr[transactions[i].Status],
				creditLineTypeMap[transactions[i].CreditLineType],
				transactions[i].CartAmount,
				fmt.Sprintf("%.2f", transactions[i].Amount),
				transactions[i].MerchantID,
				transactions[i].LenderName,
				transactions[i].SourceEntityTxnID,
				transactions[i].MerchantUserID,
			})
			if err != nil {
				log.Error(err)
				continue
			}
		}

		writer.Flush()
		err = writer.Error()
		if err != nil {
			log.Error("an error occurred during the flush")
			log.Error(err)
			panic(err)
		}

		attachments := []emaillib.EmailAttachment{}
		attachments = append(attachments, emaillib.EmailAttachment{
			Path:     csvFile.Name(),
			FileName: fmt.Sprintf("tata_paylater_transactions_%s.csv", txnDate.Format("2006_01_02")),
			Type:     "file",
		})

		// TODO: implement SFTP once configured
		isSent, errString := emaillib.SendMail([]string{"<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"}, []string{"Devashish", "Raguram", "Abhishek", "Ankit"}, "Transactions Report", htmltemplates.TransactionsReportText, attachments, false)
		if !isSent || errString != "" {
			errString = general.Coalesce(errString, "error in sending email")
			err := errors.New(errString)
			log.Error(err)
			panic(err)
		}

		response := map[string]string{
			"message": "ok",
		}

		ctx := context.WithValue(r.Context(), "resData", response)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// MuthootCLLoanStatusUpdateCont fetch status from muthoot api and update FB db.
// TODO: MUTHOOT LISA this is a dangling controller, it is not being used anywhere in the code
func MuthootCLLoanStatusUpdateCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		go func() {
			onemuthoot.UpdateLoanStatus(context.Background())
		}()

		response := map[string]string{
			"message": "ok",
		}

		ctx := context.WithValue(r.Context(), "resData", response)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// SendRefundReportCont extracts the list of transactions to a csv for a given date and emails the list
func SendRefundReportCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		attributes := r.Context().Value("attributes").(map[string]interface{})
		sourceEntityID := attributes["sourceEntityID"].(string)
		refundDate := attributes["date"].(time.Time)

		type refundStruct struct {
			MerchantOrderID       string  `db:"merchant_order_id"`
			CartSourceEntityTxnID string  `db:"cart_source_entity_txn_id"`
			RefundRequestID       string  `db:"refund_request_id"`
			RefundID              string  `db:"refund_id"`
			SourceEntityTxnID     string  `db:"source_entity_txn_id"`
			CreatedAt             string  `db:"created_at"`
			Status                int     `db:"status"`
			CreditLineType        string  `db:"credit_line_type"`
			ProcessedRefundAmount float64 `db:"processed_refund_amount"`
			MerchantID            string  `db:"merchant_id"`
			LenderName            string  `db:"lender_name"`
			UpdatedAt             string  `db:"updatedat"`
			RefundedAt            string  `db:"refundedat"`
			MerchantUserID        string  `db:"merchantuserid"`
		}

		var creditLineTypeMap = map[string]string{
			"emi":  "EMI",
			"bnpl": "BNPL",
		}

		var refundList []refundStruct
		query := `select clt.merchant_order_id,
					clt.cart_source_entity_txn_id,
					to_char(rf.created_at AT TIME ZONE 'utc' AT TIME ZONE 'Asia/Calcutta', 'YYYY-MM-DD HH24:MI:SS') as created_at,
					cl.credit_line_type,
					rf.refund_request_id,
					rf.processed_refund_amount,
					rf.status,
					clt.merchant_id,
					coalesce(clt.parent_txn_metadata::jsonb ->> 'merchantUserID', '') as merchantuserid,
					coalesce(to_char(rf.refunded_at AT TIME ZONE 'utc' AT TIME ZONE 'Asia/Calcutta', 'YYYY-MM-DD HH24:MI:SS'), '') as refundedat,
					l.lender_name,
					coalesce(to_char(rf.updated_at AT TIME ZONE 'utc' AT TIME ZONE 'Asia/Calcutta', 'YYYY-MM-DD HH24:MI:SS'), '') as updatedat,
					rf.refund_id,
					clt.source_entity_txn_id
				from refunds rf 
				join credit_line_txn clt on rf.credit_line_txn_id = clt.txn_id 
				join credit_line cl on clt.credit_line_id = cl.credit_line_id 
				join lender l on cl.lender_id = l.lender_id 
				where cl.source_entity_id = $1
				and to_char(rf.created_at AT TIME ZONE 'utc' AT TIME ZONE 'Asia/Calcutta', 'YYYY-MM-DD') = $2
				order by rf.created_at desc;`
		err := database.Select(&refundList, query, sourceEntityID, refundDate.Format(constants.DateFormat))
		if err != nil {
			log.Error(err)
			errorHandler.ReportToSentryWithoutRequest(err)
			panic(err)
		}

		log.Debug("refunds count --> ", len(refundList))

		refundCSVFile := fmt.Sprintf("%s_*.csv", sourceEntityID)
		csvFile, err := os.CreateTemp("/tmp/", filepath.Clean(refundCSVFile))
		if err != nil {
			log.Error(err)
			panic(err)
		}

		defer os.Remove(csvFile.Name())
		defer csvFile.Close()

		writer := csv.NewWriter(csvFile)

		if csvFile != nil {
			// adding headers
			err := writer.Write([]string{"merchantOrderId", "refundTransactionId", "createdOn", "tenderId/Type", "refundId", "refundAmount", "status", "merchantId", "Refund Date", "LenderName", "Updatedon", "subRefundID", "refundSubTransactionID", "merchantRefID"})
			if err != nil {
				log.Error(err)
				panic(err)
			}
		} else {
			panic("error writing csv")
		}

		for i := range refundList {
			log.Debug("count - ", i)
			err = writer.Write([]string{
				refundList[i].MerchantOrderID,
				refundList[i].CartSourceEntityTxnID,
				refundList[i].CreatedAt,
				creditLineTypeMap[refundList[i].CreditLineType],
				refundList[i].RefundRequestID,
				fmt.Sprintf("%f", refundList[i].ProcessedRefundAmount),
				refunds.StatusNumToStr[refundList[i].Status],
				refundList[i].MerchantID,
				refundList[i].RefundedAt,
				refundList[i].LenderName,
				refundList[i].UpdatedAt,
				refundList[i].RefundID,
				refundList[i].SourceEntityTxnID,
				refundList[i].MerchantUserID,
			})
			if err != nil {
				log.Error(err)
				continue
			}
		}

		writer.Flush()
		err = writer.Error()
		if err != nil {
			log.Error("an error occurred during the flush")
			log.Error(err)
			panic(err)
		}

		attachments := []emaillib.EmailAttachment{}
		attachments = append(attachments, emaillib.EmailAttachment{
			Path:     csvFile.Name(),
			FileName: fmt.Sprintf("tata_paylater_refunds_%s.csv", refundDate.Format("2006_01_02")),
			Type:     "file",
		})

		isSent, errString := emaillib.SendMail([]string{"<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"}, []string{"Devashish", "Raguram", "Abhishek", "Ankit"}, "Refunds Report", htmltemplates.RefundsReportText, attachments, false)
		if !isSent || errString != "" {
			errString = general.Coalesce(errString, "error in sending email")
			err := errors.New(errString)
			log.Error(err)
			panic(err)
		}

		response := map[string]string{
			"message": "ok",
		}

		ctx := context.WithValue(r.Context(), "resData", response)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// GetTransactionRecordsCont extracts the list of transactions with a page size of 1000
func GetTransactionRecordsCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		attributes := r.Context().Value("attributes").(map[string]interface{})
		sourceEntityID := attributes["sourceEntityID"].(string)
		txnDate := attributes["date"].(time.Time)
		page := attributes["page"].(int)
		limit := 1000

		offset := (page - 1) * limit

		type transactionStruct struct {
			MerchantOrderID       string  `db:"merchant_order_id"`
			CartSourceEntityTxnID string  `db:"cart_source_entity_txn_id"`
			SourceEntityTxnID     string  `db:"source_entity_txn_id"`
			CreatedAt             string  `db:"created_at"`
			Status                int     `db:"status"`
			CreditLineType        string  `db:"credit_line_type"`
			CartAmount            string  `db:"cartamount"`
			MerchantUserID        string  `db:"merchantuserid"`
			Amount                float64 `db:"amount"`
			MerchantID            string  `db:"merchant_id"`
			LenderName            string  `db:"lender_name"`
			UpdatedAt             string  `db:"updatedat"`
			PageCount             int     `db:"pagecount"`
		}

		var creditLineTypeMap = map[string]string{
			"emi":  "EMI",
			"bnpl": "BNPL",
		}

		var transactions []transactionStruct
		query := `select clt.merchant_order_id, 
			clt.cart_source_entity_txn_id,
			clt.source_entity_txn_id, 
			to_char(clt.created_at AT TIME ZONE 'utc' AT TIME ZONE 'Asia/Calcutta', 'YYYY-MM-DD HH24:MI:SS') as created_at, 
			clt.status, 
			cl.credit_line_type,  
			coalesce(clt.txn_metadata::jsonb ->> 'cartAmount', '') as cartamount,
			coalesce(clt.parent_txn_metadata::jsonb ->> 'merchantUserID', '') as merchantuserid,
			clt.amount, 
			clt.merchant_id, 
			l.lender_name, 
			coalesce(to_char(clt.updated_at AT TIME ZONE 'utc' AT TIME ZONE 'Asia/Calcutta', 'YYYY-MM-DD HH24:MI:SS'), '') as updatedat,
			CEIL(CAST(count(*) OVER () AS DECIMAL)/ $3) AS pagecount
		from credit_line_txn clt 
		join credit_line cl on clt.credit_line_id = cl.credit_line_id and cl.source_entity_id = $1
		join lender l on cl.lender_id = l.lender_id
		where to_char(clt.created_at AT TIME ZONE 'utc' AT TIME ZONE 'Asia/Calcutta', 'YYYY-MM-DD') = $2
		order by clt.created_at desc
		limit $3 offset $4;`
		err := database.Select(&transactions, query, sourceEntityID, txnDate.Format(constants.DateFormat), limit, offset)
		if err != nil {
			log.Error(err)
			panic(err)
		}

		log.Debug("transactions count --> ", len(transactions))

		var pageCount int
		if len(transactions) > 0 {
			pageCount = transactions[0].PageCount
		}

		var txns []map[string]interface{}

		for i := range transactions {
			txns = append(txns, map[string]interface{}{
				"merchantOrderId":  transactions[i].MerchantOrderID,
				"transactionId":    transactions[i].CartSourceEntityTxnID,
				"createdOn":        transactions[i].CreatedAt,
				"UpdatedOn":        transactions[i].UpdatedAt,
				"status":           constants.CreditLineTxnStatusNumToStr[transactions[i].Status],
				"tenderId/Type":    creditLineTypeMap[transactions[i].CreditLineType],
				"totalAmount":      transactions[i].CartAmount,
				"loanAmount":       fmt.Sprintf("%.2f", transactions[i].Amount),
				"merchantId":       transactions[i].MerchantID,
				"LenderName":       transactions[i].LenderName,
				"subTransactionID": transactions[i].SourceEntityTxnID,
				"merchantRefID":    transactions[i].MerchantUserID,
			})
		}

		var response = map[string]interface{}{
			"txns":  txns,
			"pages": pageCount,
		}

		ctx := context.WithValue(r.Context(), "resData", response)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// GetRefundRecordsCont extracts the list of transactions to a csv for a given date with a page size of 1000
func GetRefundRecordsCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		attributes := r.Context().Value("attributes").(map[string]interface{})
		sourceEntityID := attributes["sourceEntityID"].(string)
		refundDate := attributes["date"].(time.Time)
		page := attributes["page"].(int)
		limit := 1000

		offset := (page - 1) * limit

		type refundStruct struct {
			MerchantOrderID       string  `db:"merchant_order_id"`
			CartSourceEntityTxnID string  `db:"cart_source_entity_txn_id"`
			RefundRequestID       string  `db:"refund_request_id"`
			RefundID              string  `db:"refund_id"`
			SourceEntityTxnID     string  `db:"source_entity_txn_id"`
			CreatedAt             string  `db:"created_at"`
			Status                int     `db:"status"`
			CreditLineType        string  `db:"credit_line_type"`
			ProcessedRefundAmount float64 `db:"processed_refund_amount"`
			MerchantID            string  `db:"merchant_id"`
			LenderName            string  `db:"lender_name"`
			UpdatedAt             string  `db:"updatedat"`
			RefundedAt            string  `db:"refundedat"`
			MerchantUserID        string  `db:"merchantuserid"`
			PageCount             int     `db:"pagecount"`
		}

		var creditLineTypeMap = map[string]string{
			"emi":  "EMI",
			"bnpl": "BNPL",
		}

		var refundList []refundStruct
		query := `select clt.merchant_order_id,
					clt.cart_source_entity_txn_id,
					to_char(rf.created_at AT TIME ZONE 'utc' AT TIME ZONE 'Asia/Calcutta', 'YYYY-MM-DD HH24:MI:SS') as created_at,
					cl.credit_line_type,
					rf.refund_request_id,
					rf.processed_refund_amount,
					rf.status,
					clt.merchant_id,
					coalesce(clt.parent_txn_metadata::jsonb ->> 'merchantUserID', '') as merchantuserid,
					coalesce(to_char(rf.refunded_at AT TIME ZONE 'utc' AT TIME ZONE 'Asia/Calcutta', 'YYYY-MM-DD HH24:MI:SS'), '') as refundedat,
					l.lender_name,
					coalesce(to_char(rf.updated_at AT TIME ZONE 'utc' AT TIME ZONE 'Asia/Calcutta', 'YYYY-MM-DD HH24:MI:SS'), '') as updatedat,
					rf.refund_id,
					clt.source_entity_txn_id,
					CEIL(CAST(count(*) OVER () AS DECIMAL)/ $3) AS pagecount
				from refunds rf 
				join credit_line_txn clt on rf.credit_line_txn_id = clt.txn_id 
				join credit_line cl on clt.credit_line_id = cl.credit_line_id 
				join lender l on cl.lender_id = l.lender_id 
				where cl.source_entity_id = $1
				and to_char(rf.created_at AT TIME ZONE 'utc' AT TIME ZONE 'Asia/Calcutta', 'YYYY-MM-DD') = $2
				order by rf.created_at desc
				limit $3 offset $4;`
		err := database.Select(&refundList, query, sourceEntityID, refundDate.Format(constants.DateFormat), limit, offset)
		if err != nil {
			log.Error(err)
			panic(err)
		}

		log.Debug("refunds count --> ", len(refundList))

		var pageCount int
		if len(refundList) > 0 {
			pageCount = refundList[0].PageCount
		}

		var rfnds []map[string]interface{}

		for i := range refundList {
			log.Debug("count - ", i)
			rfnds = append(rfnds, map[string]interface{}{
				"merchantOrderId":        refundList[i].MerchantOrderID,
				"refundTransactionId":    refundList[i].CartSourceEntityTxnID,
				"createdOn":              refundList[i].CreatedAt,
				"tenderId/Type":          creditLineTypeMap[refundList[i].CreditLineType],
				"refundId":               refundList[i].RefundRequestID,
				"refundAmount":           fmt.Sprintf("%f", refundList[i].ProcessedRefundAmount),
				"status":                 refunds.StatusNumToStr[refundList[i].Status],
				"merchantId":             refundList[i].MerchantID,
				"Refund Date":            refundList[i].RefundedAt,
				"LenderName":             refundList[i].LenderName,
				"Updatedon":              refundList[i].UpdatedAt,
				"subRefundID":            refundList[i].RefundID,
				"refundSubTransactionID": refundList[i].SourceEntityTxnID,
				"merchantRefID":          refundList[i].MerchantUserID,
			})
		}

		var response = map[string]interface{}{
			"refunds": rfnds,
			"pages":   pageCount,
		}

		ctx := context.WithValue(r.Context(), "resData", response)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func AxisCallbackCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		attributes := r.Context().Value("attributes").(map[string]interface{})
		log.Println("axis callback successful")
		log.Println(attributes)

		callbackReq := axis.CallbackReq{}
		b, err := json.Marshal(attributes)
		if err != nil {
			log.Error(err)
			errorHandler.CustomErrorJSON(w, http.StatusInternalServerError, map[string]interface{}{
				"data": nil,
				"error": map[string]interface{}{
					"errorCode":   axis.ErrKYCCallbackRetry,
					"errorString": err.Error(),
				},
				"serverTimeStamp": time.Now().Format("2006-01-02T15:04:05.000000"),
			})
			return
		}

		err = json.Unmarshal(b, &callbackReq)
		if err != nil {
			log.Error(err)
			errorHandler.CustomErrorJSON(w, http.StatusInternalServerError, map[string]interface{}{
				"data": nil,
				"error": map[string]interface{}{
					"errorCode":   axis.ErrKYCCallbackRetry,
					"errorString": "please verify input field types",
				},
				"serverTimeStamp": time.Now().Format("2006-01-02T15:04:05.000000"),
			})
			return
		}

		var metaData axis.CallbackMetadata
		err = json.Unmarshal([]byte(callbackReq.Metadata), &metaData)
		if err != nil {
			log.Error(err)
			errorHandler.CustomErrorJSON(w, http.StatusInternalServerError, map[string]interface{}{
				"data": nil,
				"error": map[string]interface{}{
					"errorCode":   axis.ErrKYCCallbackRetry,
					"errorString": err.Error(),
				},
				"serverTimeStamp": time.Now().Format("2006-01-02T15:04:05.000000"),
			})
			return
		}

		kycTypeTableMap := map[string]string{
			"AADHAAR_KYC": "ekyc_data",
			"VIDEO_KYC":   "vkyc_data",
			"CKYC":        "ckyc_data",
		}

		kycStateToStatusMap := map[string]int{
			"AADHAAR_KYC_SUCCESS":    constants.EKYCStatusCompleted,
			"VIDEO_KYC_CALL_SUCCESS": constants.VKYCStatusCompleted,
			"VIDEO_KYC_FAILED":       constants.VKYCStatusFailed,
			"CKYC_FAILED":            constants.CKYCStatusDeclined,
			"AADHAAR_KYC_FAILED":     constants.EKYCStatusFailed,
		}

		tableName := ""
		status := 0

		switch callbackReq.EventType {
		case "RESUME_KYC_SUCCESS":
			// TODO: find out how to handle this event
		case "KYC_COMPLETED":
			tableName = kycTypeTableMap[metaData.KYCType]
			status = kycStateToStatusMap[metaData.KYCState]
		case "KYC_FAILED", "RESUME_KYC_FAILED":
			tableName = kycTypeTableMap[metaData.KYCType]
			status = kycStateToStatusMap[fmt.Sprintf("%s_FAILED", metaData.KYCType)]
		default:
			log.Error("unexpected eventType received")
			errorHandler.CustomErrorJSON(w, http.StatusInternalServerError, map[string]interface{}{
				"data": nil,
				"error": map[string]interface{}{
					"errorCode":   axis.ErrKYCCallbackNoRetry,
					"errorString": "unexpected eventType received",
				},
				"serverTimeStamp": time.Now().Format("2006-01-02T15:04:05.000000"),
			})
			return
		}

		// loanApplicationID := ""
		// query := `
		// SELECT l.loan_application_id
		// FROM loan_application l
		// JOIN dual_loan_offers dlo ON dlo.offer_id=l.offer_id
		// WHERE dlo.onboarding_txn_id=$1
		// `
		// err = database.Get(&loanApplicationID, query, metaData.ApplicationID)
		// if err != nil {
		// 	log.Error(err)
		// 	if errors.Is(err, sql.ErrNoRows) {
		// 		errorHandler.CustomErrorJSON(w, http.StatusInternalServerError, map[string]interface{}{
		// 			"data": nil,
		// 			"error": map[string]interface{}{
		// 				"errorCode":   axis.ErrKYCCallbackNoRetry,
		// 				"errorString": "loan application not found",
		// 			},
		// 			"serverTimeStamp": time.Now().Format("2006-01-02T15:04:05.000000"),
		// 		})
		// 		return
		// 	}
		// 	return
		// }

		// query = fmt.Sprintf(`UPDATE %s SET status=$1 WHERE loan_application_id=$2`, tableName)
		// _, err = database.Exec(query, status, loanApplicationID)
		// if err != nil {
		// 	log.Error(err)
		// 	if errors.Is(err, sql.ErrNoRows) {
		// 		errorHandler.CustomErrorJSON(w, http.StatusInternalServerError, map[string]interface{}{
		// 			"data": nil,
		// 			"error": map[string]interface{}{
		// 				"errorCode":   axis.ErrKYCCallbackNoRetry,
		// 				"errorString": "loan application not found",
		// 			},
		// 			"serverTimeStamp": time.Now().Format("2006-01-02T15:04:05.000000"),
		// 		})
		// 		return
		// 	}
		// 	panic(err)
		// }

		log.Info(fmt.Sprintf("entry will be made in table %s with status %d", tableName, status))
		data := map[string]interface{}{
			"transactionId": general.GetOnlyAlphaNumUpper(general.GetUUID()),
		}

		dataBytes, err := json.Marshal(data)
		if err != nil {
			log.Error(err)
			errorHandler.CustomErrorJSON(w, http.StatusInternalServerError, map[string]interface{}{
				"data": nil,
				"error": map[string]interface{}{
					"errorCode":   axis.ErrKYCCallbackRetry,
					"errorString": err.Error(),
				},
				"serverTimeStamp": time.Now().Format("2006-01-02T15:04:05.000000"),
			})
			return
		}

		resData := map[string]interface{}{
			"data": string(dataBytes),
		}

		ctx := context.WithValue(r.Context(), "resData", resData)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// MintifiApplicationStatusCont updates the loanapplication status for mintifi
func MintifiApplicationStatusCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		ctx := r.Context()

		resourceName := "MintifiApplicationStatusCont"

		// passing any sourceentity to get creds
		mintifiConfig := conf.GetMintifiOnboardingCreds(constants.CostboID)
		webhookIP := mintifiConfig["webhookIP"]
		if conf.ENV == conf.ENV_PROD && !strings.Contains(r.RemoteAddr, webhookIP) {
			err := errors.New("unauthorized request")
			logger.Log.Error(err)
			errorHandler.CustomError(w, http.StatusUnauthorized, err.Error())
			errorHandler.ReportToSentryWithoutRequest(err)
			return
		}

		payload, err := io.ReadAll(r.Body)
		defer r.Body.Close()
		if err != nil {
			logger.WithRequest(r).Error(err)
			panic(err)

		}

		// write payload to DB
		query := `INSERT INTO mintifi_webhook (service_name, webhook_payload, created_at) VALUES ($1, $2, NOW())`
		_, err = database.ExecContext(ctx, query, resourceName, string(payload))
		if err != nil {
			logger.WithRequest(r).Error(err)
			panic(err)
		}

		type requstBody struct {
			Status            string `json:"account_status" validate:"required"`
			LoanApplicationNo string `json:"id" validate:"required"` // This is the loan application no
			Date              string `json:"date"`
		}

		var req requstBody
		err = json.Unmarshal(payload, &req)
		if err != nil {
			logger.WithRequest(r).Error(err)
			panic(err)
		}

		err = validator.New().Struct(req)
		if err != nil {
			logger.WithRequest(r).Println(err)
			panic(err)
		}

		var resData = map[string]interface{}{
			"message": "success",
		}

		loanStatus := strings.ToLower(req.Status)
		switch loanStatus {
		case mintifi.ActiveLoanStatus:
			loanapplicationDetails, err := loanapplication.GetByNo(ctx, req.LoanApplicationNo)
			if err != nil {
				err := fmt.Errorf("unable to find the loan_application_no %s err:%s", req.LoanApplicationNo, err.Error())
				logger.WithRequest(r).Error(err)
				errorHandler.ReportToSentry(r, err)
				ctx = context.WithValue(r.Context(), "resData", resData)
				next.ServeHTTP(w, r.WithContext(ctx))
				return
			}

			err = paymentvirtualaccount.Insert(paymentvirtualaccount.PaymentVirtualAccount{
				LoanApplicationID:    loanapplicationDetails.ID.String(),
				UserID:               loanapplicationDetails.UserID,
				VirtualAccountID:     loanapplicationDetails.LoanApplicationNo[3:], // Remove first 3 digits (BL1)
				VirtualAccountNumber: "MINT" + loanapplicationDetails.LoanApplicationNo,
				IFSC:                 constants.MintifiVirtualAccountIFSCCode,
				AccountHolderName:    constants.MintifiVirtualAccountHolderName,
				BankName:             constants.MintifiVitrualAccountBankName,
				Status:               paymentvirtualaccount.StatusActive,
			})

			if err != nil {
				err := fmt.Errorf("unable to insert into virtual account %s err:%s", req.LoanApplicationNo, err.Error())
				logger.WithRequest(r).Error(err)
				errorHandler.ReportToSentry(r, err)
				ctx = context.WithValue(r.Context(), "resData", resData)
				next.ServeHTTP(w, r.WithContext(ctx))
				return
			}

			_, err = clutils.ActivateCreditLine(loanapplicationDetails.ID.String(), 0, "", "", "")
			if err != nil {
				logger.WithRequest(r).Error(err)
				errorHandler.ReportToSentry(r, err)
				ctx = context.WithValue(r.Context(), "resData", resData)
				next.ServeHTTP(w, r.WithContext(ctx))
				return
			}

		case mintifi.MaturedLoanStatus:
		case mintifi.ClosedLoanStatus:
		default:
			err := fmt.Errorf("unable to process request status %s", loanStatus)
			panic(err)
		}

		ctx = context.WithValue(r.Context(), "resData", resData)
		next.ServeHTTP(w, r.WithContext(ctx))

	})
}

// MintifiDrawdownStatusCont updates the drawdown status for mintifi
func MintifiDrawdownStatusCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		ctx := r.Context()

		resourceName := "MintifiDrawdownStatusCont"

		// passing any sourceentity to get creds
		mintifiConfig := conf.GetMintifiOnboardingCreds(constants.CostboID)
		webhookIP := mintifiConfig["webhookIP"]
		if conf.ENV == conf.ENV_PROD && !strings.Contains(r.RemoteAddr, webhookIP) {
			err := errors.New("unauthorized request")
			logger.Log.Error(err)
			errorHandler.CustomError(w, http.StatusUnauthorized, err.Error())
			errorHandler.ReportToSentryWithoutRequest(err)
			return
		}

		payload, err := io.ReadAll(r.Body)
		defer r.Body.Close()
		if err != nil {
			logger.WithRequest(r).Error(err)
			panic(err)
		}

		// write payload to DB
		query := `INSERT INTO mintifi_webhook (service_name, webhook_payload, created_at) VALUES ($1, $2, NOW())`
		_, err = database.Exec(query, resourceName, string(payload))
		if err != nil {
			logger.WithRequest(r).Error(err)
			panic(err)
		}

		type requstBody struct {
			Status            string  `json:"status" validate:"required"`
			DrawdownID        string  `json:"id" validate:"required"`
			LoanApplicationID string  `json:"loan_application_id" validate:"required"`
			Amount            float64 `json:"drawdown_amount"`
			DisbursementDate  string  `json:"disbursement_date"`
			UTR               string  `json:"utr"`
			Email             string  `json:"borrower_email"`
			CompanyName       string  `json:"company_name"`
			AnchorName        string  `json:"anchor_name"`
			LenderName        string  `json:"lender_name"`
			UserName          string  `json:"user_name"`
		}

		var req requstBody
		err = json.Unmarshal(payload, &req)
		if err != nil {
			logger.WithRequest(r).Error(err)
			panic(err)
		}

		err = validator.New().Struct(req)
		if err != nil {
			logger.WithRequest(r).Println(err)
			panic(err)
		}

		type dbObj struct {
			TransactionID     string `db:"txn_id"`
			Status            string `db:"status"`
			CreditLineID      string `db:"credit_line_id"`
			LoanApplicationID string `db:"parent_loan_id"`
			LenderID          string `db:"lender_id"`
		}

		var resData = map[string]interface{}{
			"message": "success",
		}

		loanStatus := strings.ToLower(req.Status)

		switch loanStatus {
		case mintifi.VerifiedDrawdownStatus, mintifi.AuthorisedDrawdownStatus:
			ctx = context.WithValue(r.Context(), "resData", resData)
			next.ServeHTTP(w, r.WithContext(ctx))
			return
		}

		var obj dbObj
		qry, args, err := sqlx.In(`select cl_txn.txn_id, cl_txn.status, cl_txn.credit_line_id, cl.parent_loan_id, cl.lender_id
			from credit_line_txn as cl_txn inner join credit_line as cl 
			on cl_txn.credit_line_id = cl.credit_line_id 
			where cl_txn.lender_term_loan_no = $1`, req.DrawdownID)
		if err != nil {
			logger.WithRequest(r).Error(err)
			ctx = context.WithValue(r.Context(), "resData", resData)
			next.ServeHTTP(w, r.WithContext(ctx))
			return
		}
		if err := database.GetContext(ctx, &obj, qry, args...); err != nil {
			logger.WithRequest(r).Error(err)
			sentry.CaptureException(err)
			ctx = context.WithValue(r.Context(), "resData", resData)
			next.ServeHTTP(w, r.WithContext(ctx))
			return
		}
		switch loanStatus {
		case mintifi.CompletedDrawdownStatus:
			if req.UTR == "" {
				err := fmt.Errorf("no UTR found")
				logger.WithRequest(r).Error(err)
				sentry.CaptureException(err)
				ctx = context.WithValue(r.Context(), "resData", resData)
				next.ServeHTTP(w, r.WithContext(ctx))
				return
			}
			// TODO: setting dummy email
			errStr, _, err := disbursal.DisburseCreditLineTxn(req.UTR, req.DisbursementDate, obj.TransactionID, obj.LoanApplicationID, obj.LenderID, "<EMAIL>", req.Amount, 0)
			if errStr != "" {
				err := fmt.Errorf(errStr)
				logger.WithRequest(r).Error(err)
				sentry.CaptureException(err)
				ctx = context.WithValue(r.Context(), "resData", resData)
				next.ServeHTTP(w, r.WithContext(ctx))
				return
			}
			if err != nil {
				logger.WithRequest(r).Error(err)
				sentry.CaptureException(err)
				ctx = context.WithValue(r.Context(), "resData", resData)
				next.ServeHTTP(w, r.WithContext(ctx))
				return
			}
		case mintifi.RejectedDrawdownStatus:
			if err := clutils.CancelTransaction(ctx, obj.LoanApplicationID, obj.TransactionID); err != nil {
				logger.WithRequest(r).Error(err)
				sentry.CaptureException(err)
				ctx = context.WithValue(r.Context(), "resData", resData)
				next.ServeHTTP(w, r.WithContext(ctx))
				return
			}
		default:
			err := fmt.Errorf("unable to process request status %s", loanStatus)
			panic(err)
		}

		ctx = context.WithValue(r.Context(), "resData", resData)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// ThirdPartyEmandateCallbackCont controller for handling callback after Mintifi Enach
func ThirdPartyEmandateCallbackCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		ctx := r.Context()
		status := r.URL.Query().Get("status")
		mandateID := r.URL.Query().Get("source_id")

		type loanStruct struct {
			SDKVersion        string
			Status            int
			LoanType          string
			UserID            string
			LoanStatus        int
			LenderID          string
			LoanApplicationID string
			SourceEntityID    string
		}
		var loanObj loanStruct
		query := `SELECT
					coalesce(u.sdk_version, '') as sdkversion,
					s.status as status,
					la.user_id as userid,
					la.status as loanstatus,
					coalesce(la.loan_type, '') as loantype,
					coalesce(la.lender_id::TEXT, '') as lenderid,
					la.source_entity_id as sourceentityid,
					la.loan_application_id as loanapplicationid
					from enach_third_party s join loan_application la
						on s.loan_id = la.loan_application_id
						join users u on la.user_id = u.user_id
						where mandate_id = $1
				`
		err := database.GetContext(ctx, &loanObj, query, mandateID)
		if err != nil {
			logger.WithRequest(r).Errorf("unable to get loan_application details for mandateID: %s, : %s", mandateID, err.Error())
			panic("E-Mandate not found")
		}

		isWeb := false
		loanObj.SDKVersion = strings.ToLower(loanObj.SDKVersion)
		if loanObj.SDKVersion == constants.SDKVersionWeb || strings.HasPrefix(loanObj.SDKVersion, "hybrid") {
			isWeb = true
		}
		var mandateStatus string
		var statusValue int

		if status == mintifi.EnachSuccess {
			mandateStatus = "success"
			query = `UPDATE enach_third_party set status = $1, updated_at = NOW()
					where mandate_id = $2 and status = $3`
			_, err = database.ExecContext(ctx, query, constants.ENACHThirdPartyCompleted, mandateID,
				constants.ENACHThirdPartyInitiated)
			if err != nil {
				logger.WithRequest(r).Error(err)
				panic(err)
			}
			statusValue = constants.UserModuleStatusCompleted
			activityObj := activity.ActivityEvent{
				UserID:            loanObj.UserID,
				SourceEntityID:    loanObj.SourceEntityID,
				LoanApplicationID: loanObj.LoanApplicationID,
				EntityType:        constants.EntityTypeCustomer,
				EntityRef:         loanObj.UserID,
				EventType:         constants.ActivityENachCompleted,
				Description:       "",
			}
			go activity.RegisterEvent(&activityObj, general.GetTimeStampString())
		} else {
			activityObj := activity.ActivityEvent{
				UserID:            loanObj.UserID,
				SourceEntityID:    loanObj.SourceEntityID,
				LoanApplicationID: loanObj.LoanApplicationID,
				EntityType:        constants.EntityTypeCustomer,
				EntityRef:         loanObj.UserID,
				EventType:         constants.ActivityEnachFailed,
				Description:       "",
			}
			go activity.RegisterEvent(&activityObj, general.GetTimeStampString())
			mandateStatus = "failed"
			query = `UPDATE enach_third_party set status = $1, updated_at = NOW()
					where mandate_id = $2 and status = $3`
			_, err = database.ExecContext(ctx, query, constants.ENACHThirdPartyFailed, mandateID,
				constants.ENACHThirdPartyInitiated)
			if err != nil {
				logger.WithRequest(r).Errorf("unable to get update enach_third_party details for mandateID: %s, : %s", mandateID, err.Error())
				panic(err)
			}
			statusValue = constants.UserModuleStatusFailed
		}

		// create user module with success / failure value
		err = usermodulemapping.Create(nil, loanObj.UserID, loanObj.UserID, usermodulemapping.ENACH, statusValue, "")
		if err != nil {
			logger.WithRequest(r).Errorf("unable to get update user_module_mapping details for mandateID: %s, : %s", mandateID, err.Error())
			panic(err)
		}

		var resData = map[string]interface{}{
			"status":         mandateStatus,
			"isWeb":          isWeb,
			"loanType":       loanObj.LoanType,
			"lenderID":       loanObj.LenderID,
			"loanStatus":     loanObj.LoanStatus,
			"programName":    "",
			"sourceEntityID": loanObj.SourceEntityID,
		}
		ctx = context.WithValue(r.Context(), "resData", resData)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// ThirdPartyESignCallbackCont controller for handling callback after Mintifi Esign
func ThirdPartyESignCallbackCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		ctx := r.Context()
		status := r.URL.Query().Get("status")
		requestID := r.URL.Query().Get("request_id")

		// As failure redirection is not specifying a loan application
		// not marking the status in
		if status != "success" {
			var resData = map[string]interface{}{
				"status":         status,
				"isWeb":          true,
				"loanType":       "",
				"lenderID":       "",
				"loanStatus":     "",
				"sourceEntityID": "",
				"programName":    "",
			}
			ctx = context.WithValue(r.Context(), "resData", resData)
			next.ServeHTTP(w, r.WithContext(ctx))
			return
		}
		type loanStruct struct {
			SDKVersion        string
			Status            int
			LoanType          string
			UserID            string
			LoanStatus        int
			LenderID          string
			LoanApplicationID string
			SourceEntityID    string
		}
		var loanObj loanStruct
		query := `SELECT coalesce(u.sdk_version, '') as sdkversion,
						s.esign_status as status,
						la.user_id as userid,
						la.status as loanstatus,
						coalesce(la.loan_type, '') as loantype,
						coalesce(la.lender_id::TEXT, '') as lenderid,
						la.source_entity_id as sourceentityid,
						la.loan_application_id as loanapplicationid
						from esign_attempt s join loan_application la
							on s.loan_application_id = la.loan_application_id
							join users u on la.user_id = u.user_id
							where s.document_id = $1 and s.esign_status != $2`
		err := database.GetContext(ctx, &loanObj, query, requestID, constants.ESignStatusDeleted)
		if err == sql.ErrNoRows {
			err = fmt.Errorf("unable to get loan_application details: %s", err.Error())
			logger.WithRequest(r).Error(err)
			errorHandler.CustomError(w, http.StatusBadRequest, "details not found")
			return
		} else if err != nil {
			logger.WithRequest(r).Errorf("unable to get loan_application details: %s", err.Error())
			panic(err)
		}

		isWeb := false
		loanObj.SDKVersion = strings.ToLower(loanObj.SDKVersion)
		if loanObj.SDKVersion == constants.SDKVersionWeb || strings.HasPrefix(loanObj.SDKVersion, "hybrid") {
			isWeb = true
		}
		userID := loanObj.UserID
		loanApplicationID := loanObj.LoanApplicationID

		if status == "success" {

			query := `update loan_application set
				updated_at = current_timestamp, status = $1
			where loan_application_id = $2`

			_, err := database.ExecContext(ctx, query, constants.LoanStatusESign, loanApplicationID)
			if err != nil {
				logger.WithUser(userID).Errorf("unable to get loan_application details: %s", err.Error())
				panic(err)
			}
			err = esignattempt.Update(nil, context.TODO(), esignattempt.EsignAttempt{
				DocumentID:        requestID,
				LoanApplicationID: loanApplicationID,
				ESignStatus:       constants.ESignStatusSuccess,
			})
			if err != nil {
				logger.WithUser(userID).Errorf("unable to update failure status err: %s", err.Error())
				panic(err)
			}
			err = usermodulemapping.Create(nil, userID, userID, usermodulemapping.ESign, constants.UserModuleStatusCompleted, loanApplicationID)
			if err != nil {
				logger.WithUser(userID).Errorf("unable to mark module success: %s", err.Error())
				panic(err)
			}
			// fetch the signed agreement from mintifi
			if err := agreement.SaveAndSendEmailToUserFormintifi(ctx, loanObj.UserID, loanObj.LenderID, loanObj.LoanApplicationID, loanObj.SourceEntityID); err != nil {
				// senty logging
				logger.WithUser(userID).Error("failed to send agrrement to user")
			}
		}

		go func() {
			dateTimeNowString := general.GetTimeStampString()
			activityObj := activity.ActivityEvent{
				UserID:            userID,
				SourceEntityID:    loanObj.SourceEntityID,
				LoanApplicationID: loanApplicationID,
				EntityType:        constants.EntityTypeSystem,
				EntityRef:         "",
				EventType:         constants.ActivityLoanESigned,
			}
			activity.RegisterEvent(&activityObj, dateTimeNowString)
		}()

		var resData = map[string]interface{}{
			"status":         status,
			"isWeb":          isWeb,
			"loanType":       loanObj.LoanType,
			"lenderID":       loanObj.LenderID,
			"loanStatus":     loanObj.LoanStatus,
			"sourceEntityID": loanObj.SourceEntityID,
			"programName":    "",
		}
		ctx = context.WithValue(r.Context(), "resData", resData)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// DisburseLoanCont controller is used by airflow DAG to mark case as disbursed, based
// on lender exposed apis for e.g. bucket api in case of IIFL
func DisburseLoanCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		attributes := r.Context().Value("attributes").(map[string]interface{})
		loanApplicationID := attributes["loanApplicationID"].(string)
		lenderTxnNum := attributes["lenderTxnNum"].(string)
		forceUpdate := attributes["forceUpdate"].(bool)
		disbursedDate := attributes["disbursedDate"].(string)
		lenderID := attributes["lenderID"].(string)

		errStr, err := loanutils.DisburseLoan(loanApplicationID, lenderID, lenderTxnNum, "", disbursedDate, forceUpdate)
		if errStr != "" {
			errorHandler.CustomError(w, http.StatusBadRequest, errStr)
			return
		}
		if err != nil {
			panic(err)
		}

		var resData = map[string]string{
			"msg": "Success",
		}
		ctx := context.WithValue(r.Context(), "resData", resData)
		next.ServeHTTP(w, r.WithContext(ctx))

	})

}

func OctopusHookCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		var reqObj octopus.InvokeRespStruct
		b, err := io.ReadAll(r.Body)
		defer r.Body.Close()
		if err != nil {
			logger.WithRequest(r).Error(err)
			panic(err)
		}

		logger.WithRequest(r).Debug(string(b))
		err = json.Unmarshal(b, &reqObj)
		if err != nil {
			logger.WithRequest(r).Error(err)
			panic(err)
		}

		type dbStruct struct {
			UserID            string
			LoanApplicationID string
		}

		var obj dbStruct
		query := `select user_id as userid, 
				loan_application_id as loanapplicationid 
				from camspay_enach where trxn_no = $1;`
		if err = database.Get(&obj, query, reqObj.Data.TrxnNo); err != nil {
			logger.WithRequest(r).Errorln(err)
			panic(err)
		}

		err = octopuswebhook.Update(reqObj.RequestID, string(b))
		if err != nil {
			logger.WithRequest(r).Error(err)
			if strings.Contains(err.Error(), "no rows updated") {
				// do not raise error (to avoid retries by octopus and raising sentry)
				errorHandler.CustomError(w, http.StatusOK, "")
				return
			}
			panic(err)
		}

		// using idempotence model to prevent processing the same webhooks again
		idemp, err := idempotence.Insert(nil, reqObj.Data.TrxnNo, idempotence.ReferenceTypeLoanApplication, obj.LoanApplicationID)
		if err != nil {
			logger.WithLoanApplication(obj.LoanApplicationID).Error(err)
			if err != nil {
				if err.Error() == idempotence.ErrStringIdempotenceIDExists {
					tErr := fmt.Errorf("camspay webhook already processed - loanApplicationID - %s camspay trxnNo %s", obj.LoanApplicationID, reqObj.Data.TrxnNo)
					logger.WithLoanApplication(obj.LoanApplicationID).Error(tErr)
					errorHandler.CustomError(w, http.StatusBadRequest, "webhook already processed")
					return
				} else {
					// reports to sentry
					panic(err)
				}
			}
		}

		defer func() {
			// updating the status of the idempotence key to done post processing
			err = idemp.Done(nil)
			if err != nil {
				logger.WithUser(obj.UserID).Errorln(err)
				errorHandler.ReportToSentryWithoutRequest(fmt.Errorf("%+v in camspayCallback userID: %s idempotenceID: %s ", err, obj.UserID, idemp.IdempotenceID))
			}
		}()

		go func() {
			defer errorHandler.RecoveryNoResponse()
			obj, err := octopuswebhook.GetByWebhookRequestID(reqObj.RequestID)
			if err != nil {
				logger.WithRequest(r).Error(err)
				panic(err)
			}

			switch obj.OctopusServiceID {
			case constants.ServiceIDCAMSPAYRegistration:
				data := reqObj.Data
				err = enach.RegisterCamspayEnach(obj.UserID, data.MandateRefNo, data.AcceptanceRefNo, data.TrxnNo, data.Umn,
					data.Status.(string), data.StatusDesc)
				if err != nil {
					logger.WithRequest(r).Error(err)
					panic(err)
				}
			}
		}()

		resData := map[string]interface{}{
			"data": "ok",
		}

		ctx := context.WithValue(r.Context(), "resData", resData)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func CancelCamspayEnachCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)
		attributes := r.Context().Value("attributes").(map[string]interface{})
		loanApplicationID := attributes["loanApplicationID"].(string)
		err := enach.CancelCamspayEnach(loanApplicationID)
		if err != nil {
			logger.WithRequest(r).Error(err)
			panic(err)
		}
		resData := map[string]interface{}{
			"data": "ok",
		}

		ctx := context.WithValue(r.Context(), "resData", resData)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// BulkUserCreationCont takes xlsx and processes it async, sends result via lender email
func BulkUserCreationCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		organizationID := r.Context().Value("organizationID").(string)
		attributes := r.Context().Value("attributes").(map[string]string)
		email := attributes["email"]
		fileName := attributes["fileName"]
		fileExtension := attributes["fileExtension"]
		sourceEntityID := r.Context().Value("sourceEntityID").(string)

		err := r.ParseMultipartForm(32 << 20)
		if err != nil {
			logger.WithRequest(r).Warn(err)
			panic("couldn't parse the request")
		}
		go func() {
			defer errorHandler.RecoveryNoResponse()
			file, fileHeader, _ := r.FormFile("file")
			if journey.IsOneMuthootPartner(sourceEntityID) {
				BulkUserCreationOneMuthoot(file, fileHeader, r.RemoteAddr, email, "One Muthoot User", fileName, fileExtension, organizationID)
			}
			if journey.IsMuthootCLPartner(sourceEntityID) {
				BulkUserCreationMuthootCL(file, fileHeader, r.RemoteAddr, email, "Muthoot CL User", sourceEntityID, fileName, fileExtension, organizationID)
			}
		}()
		resData := map[string]interface{}{
			"message": "Please check email for status",
		}
		ctx := context.WithValue(r.Context(), "resData", resData)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// CamspayEmandateCallbackCont controller for handling callback from Campspay Enach
func CamspayEmandateCallbackCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		output := r.URL.Query().Get("output")

		decryptedOutput, err := camspaythirdparty.DecryptCamsPay(output, conf.GetCamspayCreds(constants.MCSLID)["CamspayEncryptionSecret"])
		if err != nil {
			logger.WithRequest(r).Errorln(err)
			panic(err)
		}
		logger.WithRequest(r).Debugln("decrypted callback", decryptedOutput)

		// removes unicode characters if present at the end of the decrypted response
		i := len(decryptedOutput) - 1
		for i >= 0 && fmt.Sprintf("%c", decryptedOutput[i]) != "}" && fmt.Sprintf("%c", decryptedOutput[i]) != "]" {
			i--
		}
		decryptedOutput = decryptedOutput[:i+1]

		webhookID := general.GetUUID()
		if err = webhooklogs.Create(nil, webhookID, camspaythirdparty.CamspayCallback, decryptedOutput); err != nil {
			logger.WithRequest(r).Errorln(err)
			panic(err)
		}

		var callbackResp camspaythirdparty.CamspayCallbackResp
		if err = json.Unmarshal([]byte(decryptedOutput), &callbackResp); err != nil {
			logger.WithRequest(r).Errorln(err)
			panic(err)
		}

		type dbStruct struct {
			UserID            string
			LoanApplicationID string
			LoanType          string
			LoanStatus        int
			EnachStatus       string
			LenderID          string
			SourceEntityID    string
		}

		var obj dbStruct
		query := `select ce.user_id as userid, 
				ce.loan_application_id as loanapplicationid,
				ce.status as enachstatus,
				la.loan_type as loantype,
				la.status as loanstatus,
				la.lender_id as lenderid,
				la.source_entity_id as sourceentityid
				from camspay_enach ce
				join loan_application la on la.loan_application_id = ce.loan_application_id
				where ce.trxn_no = $1;`
		if err = database.Get(&obj, query, callbackResp.TrxnNo); err != nil {
			logger.WithRequest(r).Errorln(err)
			panic(err)
		}

		if err = webhooklogs.Update(nil, obj.UserID, webhookID); err != nil {
			logger.WithRequest(r).Errorln(err)
			panic(err)
		}

		// using idempotence model to prevent processing the same webhooks again
		idemp, err := idempotence.Insert(nil, callbackResp.TrxnNo, idempotence.ReferenceTypeLoanApplication, obj.LoanApplicationID)
		if err != nil {
			logger.WithLoanApplication(obj.LoanApplicationID).Error(err)
			if err != nil {
				if err.Error() == idempotence.ErrStringIdempotenceIDExists {
					tErr := fmt.Errorf("camspay webhook already processed - loanApplicationID - %s camspay trxnNo %s", obj.LoanApplicationID, callbackResp.TrxnNo)
					logger.WithLoanApplication(obj.LoanApplicationID).Error(tErr)
					var resp = map[string]interface{}{
						"status":         "",
						"isWeb":          true,
						"loanType":       obj.LoanType,
						"lenderID":       obj.LenderID,
						"loanStatus":     obj.LoanStatus,
						"programName":    "",
						"sourceEntityID": obj.SourceEntityID,
					}

					switch obj.EnachStatus {
					case "success":
						resp["status"] = "success"
					case "failure":
						resp["status"] = "failure"
					default:
						resp["status"] = "pending"
					}

					ctx := context.WithValue(r.Context(), "resData", resp)
					next.ServeHTTP(w, r.WithContext(ctx))
					return
				} else {
					// reports to sentry
					panic(err)
				}
			}
		}

		defer func() {
			// updating the status of the idempotence key to done post processing
			err = idemp.Done(nil)
			if err != nil {
				logger.WithUser(obj.UserID).Errorln(err)
				errorHandler.ReportToSentryWithoutRequest(fmt.Errorf("%+v in camspayCallback userID: %s idempotenceID: %s ", err, obj.UserID, idemp.IdempotenceID))
			}
		}()

		if err = enach.RegisterCamspayEnach(obj.UserID, callbackResp.MandateRefNo, callbackResp.AcceptanceRefNo, callbackResp.TrxnNo,
			callbackResp.Umn, callbackResp.Status, callbackResp.StatusDesc); err != nil {
			logger.WithUser(obj.UserID).Errorln(err)
			panic(err)
		}

		var mandateStatus string
		if strings.ToLower(callbackResp.Status) == "success" {
			mandateStatus = "success"
		} else if strings.ToLower(callbackResp.Status) == "failure" {
			mandateStatus = "failure"
		} else {
			mandateStatus = "pending"
		}

		var resData = map[string]interface{}{
			"status":         mandateStatus,
			"isWeb":          true,
			"loanType":       obj.LoanType,
			"lenderID":       obj.LenderID,
			"loanStatus":     obj.LoanStatus,
			"programName":    "",
			"sourceEntityID": obj.SourceEntityID,
		}

		ctx := context.WithValue(r.Context(), "resData", resData)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func OneFinEmandateCallbackCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		b, err := io.ReadAll(r.Body)
		defer r.Body.Close()
		if err != nil {
			log.Error(err)
			panic(err)
		}

		// write payload to DB
		query := `INSERT INTO onefin_webhooks (webhook_payload, created_at) VALUES ($1, NOW())`
		_, err = database.Exec(query, string(b))
		if err != nil {
			log.Error(err)
			panic(err)
		}
		enachID := r.URL.Query().Get("enachID")
		mandateID, err := onefinenach.GetMandateIDByENACHID(enachID)
		if err != nil {
			log.Errorln(err)
			panic(err)
		}
		userID, err := onefinenach.GetUserIDByENACHID(enachID)
		if err != nil {
			log.Errorln(err)
			panic(err)
		}

		apiResp, err := onefin.GetResponseFromOneFinENACHAPI(userID, mandateID)
		if err != nil {
			logger.WithUser(userID).Errorln(err)
			panic(err)
		}

		var mandateStatus string
		var moduleStatus int
		var showEndModule bool
		loanInfo, err := loanapplication.GetLatestByUser(userID)
		if err != nil {
			logger.WithUser(userID).Errorln(err)
			panic(err)
		}
		if apiResp.Data.Status == "registered" {
			mandateStatus = "success"
			moduleStatus = constants.UserModuleStatusCompleted
			showEndModule = true

		} else {
			mandateStatus = "failed"
			moduleStatus = constants.UserModuleStatusFailed
		}
		// tx, _ := database.Beginx() TODO: uncomment and test
		err = onefinenach.UpdateStatusForENACHID(mandateStatus, enachID)
		if err != nil {
			logger.WithUser(userID).Error(err)
			errorHandler.ReportToSentryWithoutRequest(err)
			// tx.Rollback()
			return
		}
		err = usermodulemapping.Create(nil, userID, userID, "ENACH", moduleStatus, loanInfo.ID.String())
		if err != nil {
			logger.WithUser(userID).Error(err)
			errorHandler.ReportToSentryWithoutRequest(err)
			// tx.Rollback()
			return
		}
		if showEndModule {
			err = usermodulemapping.Create(nil, userID, userID, constants.EndModule, constants.UserModuleStatusCompleted, loanInfo.ID.String())
			if err != nil {
				logger.WithUser(userID).Error(err)
				errorHandler.ReportToSentryWithoutRequest(err)
				// tx.Rollback()
				return
			}
		}
		//tx.Commit()
		var resData = map[string]interface{}{
			"status":         mandateStatus,
			"isWeb":          true,
			"loanType":       loanInfo.LoanType,
			"lenderID":       constants.EcofyID,
			"loanStatus":     constants.LoanStatusESign,
			"programName":    "",
			"sourceEntityID": constants.SupremeSolarID,
		}
		ctx := context.WithValue(r.Context(), "resData", resData)
		next.ServeHTTP(w, r.WithContext(ctx))

	})
}

// ActivityHookCont accepts webhook requests from external services like KYC, octopus, lisa and BankConnect
// and adds them to the activity_log table
func ActivityHookCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		errJSON := map[string]interface{}{
			"message": "Unauthorized!",
		}

		apiKey := r.Header.Get(constants.InternalHeaderAPIKey)
		if activityService, ok := conf.ActivityServiceAPIKey[apiKey]; !ok {
			logger.WithRequest(r).Errorln("request unauthorised. API Key:", apiKey)
			errorHandler.CustomErrorJSON(w, http.StatusUnauthorized, errJSON)
			return
		} else {
			attributes := r.Context().Value("attributes").(map[string]interface{})
			ID := attributes["ID"].(string)
			eventType := attributes["eventType"].(string)
			eventLoggedAt := attributes["eventLoggedAt"].(string)
			metaData := attributes["metaData"].(map[string]interface{})
			go func() {
				activityObj := activity.ActivityWebhookEvent{
					ID:            ID,
					EventType:     eventType,
					EventLoggedAt: eventLoggedAt,
					MetaData:      metaData,
				}
				activity.RegisterWebhookEvent(&activityObj, activityService)
			}()
		}

		resData := map[string]interface{}{
			"data": "ok",
		}

		ctx := context.WithValue(r.Context(), "resData", resData)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func PFLApplicationCallbackCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		ctx := r.Context()
		sourceEnityID := ctx.Value("sourceEntityID").(string)

		type requestStruct struct {
			ReferenceID          string `json:"reference_id"`
			PFLLSQ               string `json:"pfl_lsq_application_id"`
			LoanAccountNumber    string `json:"loan_account_number"`
			LSQLeadID            string `json:"LSQ_Lead_ID"`
			LSQApplicationStatus string `json:"application_status"`
			Remark               string `json:"remark"`
			InterestRate         string `json:"Interest_Rate"`
			ProcessingFee        string `json:"Processing_Fee"`
		}

		b, err := io.ReadAll(r.Body)
		defer r.Body.Close()
		if err != nil {
			logger.WithRequest(r).Println(err)
			panic(err)
		}

		webhookID := general.GetUUID()
		err = webhooklogs.Create(nil, webhookID, "pfl_application_callback", string(b))
		if err != nil {
			logger.WithRequest(r).Error(err)
			panic(err)
		}

		var reqObj requestStruct
		err = json.Unmarshal(b, &reqObj)
		if err != nil {
			logger.WithRequest(r).Error(err)
			panic(err)
		}

		if !general.ValidateUUID(reqObj.ReferenceID) {
			errorHandler.CustomError(w, http.StatusBadRequest, "invalid reference_id")
			return
		}

		if reqObj.PFLLSQ == "" {
			errorHandler.CustomError(w, http.StatusBadRequest, "invalid pfl_lsq")
			return
		}

		// this is currently implemented for PFL source entity
		// if we need to use it for pfl dsa, we need to fetch source entity id from loan_application table and pass it
		// as sourceEnityID fetched from apikey will always be of pfl source

		var userID string
		query := `select user_id from lender_variables where reference_id = $1 and lender_id = $2 and status = $3`
		err = database.Get(&userID, query, reqObj.ReferenceID, constants.PoonawallaFincorpID, lendervariables.LenderVariableStatusActive)
		if err != nil {
			logger.WithUser(userID).Error(err)
			errorHandler.CustomError(w, http.StatusConflict, "active user not found")
			return
		}

		err = webhooklogs.Update(nil, userID, webhookID)
		if err != nil {
			logger.WithRequest(r).Error(err)
			panic(err)
		}

		type dbStruct struct {
			LoanApplicationID string
			OldApplicationNo  string
			LoanStatus        int
			UserWaitState     bool
			UserStatus        int
			LoanAmount        float64
		}
		var dbObj dbStruct
		query = `select coalesce(la.loan_application_id::TEXT, '') as loanapplicationid, coalesce(la.old_application_no, '') as oldapplicationno, 
					coalesce(la.status, -1) as loanstatus, coalesce(u.wait_state, false) as userwaitstate, u.status as userstatus, coalesce(la.amount, 0) as loanamount
					from users u 
					left join loan_application la on u.user_id = la.user_id and la.is_valid = true where u.user_id = $1
					order by la.created_at desc limit 1`
		err = database.Get(&dbObj, query, userID)
		if err != nil {
			logger.WithRequest(r).Error(err)
			panic(err)
		}

		// not updating reference ID, it will only be inserted after the dedupe api call

		// it is expected from PFL callback to send the below fields consistently
		lenderVariablesNullable := lendervariables.LenderVariablesStructNullable{
			UserID:               userID,
			LenderID:             constants.PoonawallaFincorpID,
			LSQLeadID:            sql.NullString{Valid: true, String: reqObj.LSQLeadID},
			LoanAccountNumber:    sql.NullString{Valid: true, String: reqObj.LoanAccountNumber},
			LSQApplicationID:     sql.NullString{Valid: true, String: reqObj.PFLLSQ},
			LSQApplicationStatus: sql.NullString{Valid: true, String: reqObj.LSQApplicationStatus},
		}
		err = lendervariables.UpdateByStatus(nil, lendervariables.LenderVariableStatusActive, lenderVariablesNullable)
		if err != nil {
			logger.WithRequest(r).Error(err)
			panic(err)
		}

		err = users.UpdateCRMID(r.Context(), userID, reqObj.LSQLeadID)
		if err != nil {
			logger.WithRequest(r).Error(err)
			panic(err)
		}

		userObj, err := users.Get(userID)
		if err != nil {
			logger.WithUser(userID).Errorln(err)
			panic(err)
		}

		if dbObj.OldApplicationNo == "" && dbObj.LoanApplicationID != "" {
			err = loanapplication.UpdateApplicationNo(r.Context(), userID, dbObj.LoanApplicationID, reqObj.PFLLSQ)
			if err != nil {
				logger.WithRequest(r).Error(err)
				panic(err)
			}
		}

		if dbObj.LoanStatus == constants.LoanStatusBankAdded {
			if dbObj.UserWaitState {
				err = usersutil.SetUserWaitState(userID, false)
				if err != nil {
					logger.WithRequest(r).Error(err)
					panic(err)
				}
			} else {
				currentModule, err := workflowutils.GetCurrentModule(userID, sourceEnityID, "")
				if err != nil {
					logger.WithRequest(r).Error(err)
					panic(err)
				}
				if currentModule.Module.ModuleName == usermodulemapping.PennyDrop && journey.IsTemporalFlow(userID, sourceEnityID, currentModule.Module.ModuleName) {
					wf, err := userworkflows.Get(userID, currentModule.Module.ModuleName)
					if err != nil {
						err = fmt.Errorf("error getting workflow for userID: %s, moduleName: %s, error: %s", userID, wf.ModuleName, err.Error())
						logger.WithUser(userID).Errorln(err)
						panic(err)
					}

					if wf.IsSuccess() {
						err = fmt.Errorf("workflow already successful for userID: %s, moduleName: %s", userID, wf.ModuleName)
						logger.WithUser(userID).Errorln(err)

						var resData = map[string]string{
							"msg": "Success",
						}

						ctx := context.WithValue(r.Context(), "resData", resData)
						next.ServeHTTP(w, r.WithContext(ctx))
						return
					}

					signalName := "release_user_wait"
					err = temporalclient.Client.SignalWorkflow(context.Background(), wf.WorkflowID, wf.RunID, signalName, nil)
					if err != nil {
						logger.WithUser(userID).Errorln(err)
						err = fmt.Errorf("unable to send signal for user %s, err : %s", userID, err)
						errorHandler.ReportToSentryWithoutRequest(err)
					}
					go temporalsignallogging.Insert(signalName, userID, wf.WorkflowID, nil)
				}
			}
		}

		switch reqObj.LSQApplicationStatus {
		case "Disbursal Approved":
			if dbObj.LoanStatus != constants.LoanStatusESign {
				errorHandler.CustomError(w, http.StatusConflict, "invalid loan status for disbursal")
				return
			}
			loc, err := time.LoadLocation("Asia/Calcutta")
			if err != nil {
				logger.WithUser(userID).Error(err)
				panic(err)
			}
			disbursalDateObj := time.Now().In(loc)

			var uQuery = `update loan_application set status = $1, updated_at = current_timestamp,
					disbursed_date = $2
					where loan_application_id = $3; `
			_, err = database.Exec(uQuery, constants.LoanStatusDisbursed,
				disbursalDateObj.Format("2006-01-02"), dbObj.LoanApplicationID)
			if err != nil {
				logger.WithLoanApplication(dbObj.LoanApplicationID).Error(err)
				panic(err)
			}

			//Update insurance status to paid if insurance is available
			_, _, err = insuranceutils.GetUnpaidInsurance(dbObj.LoanApplicationID)
			if err == nil {
				err = insurance.UpdateStatus(nil, dbObj.LoanApplicationID, constants.InsuranceStatusPaid)
				if err != nil {
					logger.WithLoanApplication(dbObj.LoanApplicationID).Error(err)
					panic(err)
				}
			}

			err = usermodulemapping.Create(nil, userID, userID, "DISBURSAL", constants.UserModuleStatusCompleted, dbObj.LoanApplicationID)
			if err != nil {
				logger.WithLoanApplication(dbObj.LoanApplicationID).Error(err)
				panic(err)
			}

			dateTimeNowString := disbursalDateObj.UTC().Format("2006-01-02 15:04:05.000000") // logging activity time in UTC for consistency
			go func() {
				activityObj := activity.ActivityEvent{
					UserID:            userID,
					SourceEntityID:    sourceEnityID,
					LoanApplicationID: dbObj.LoanApplicationID,
					EntityType:        constants.EntityTypeSystem,
					EntityRef:         "application callback",
					EventType:         constants.ActivityLoanDisbursed,
					Description:       fmt.Sprintf(`{"loanAmount":"%.2f"}`, dbObj.LoanAmount),
				}
				activity.RegisterEvent(&activityObj, dateTimeNowString)
			}()
		case "Drop Off Rejected NI":
			if general.InArr(dbObj.LoanStatus, []int{constants.LoanStatusCancelled, constants.LoanStatusLoanRejected, constants.LoanStatusDisbursed, constants.LoanStatusClosed}) || general.InArr(dbObj.UserStatus, []int{constants.UserStatusArchived, constants.UserStatusExpired, constants.UserStatusDisqualified}) {
				errorHandler.CustomError(w, http.StatusConflict, "invalid user status")
				return
			}
			if dbObj.LoanApplicationID != "" {
				errString, err := loanutils.CancelApplication(dbObj.LoanApplicationID, sourceEnityID, reqObj.Remark, constants.EntityTypeSystem, "application callback")
				if err != nil {
					logger.WithRequest(r).Error(err)
					panic(err)
				}

				if errString != "" {
					errorHandler.ReportToSentry(r, fmt.Errorf("error in canceling application for userID: %s. %s", userID, errString)) // this is can be removed later just to check the callback behaviour
					errorHandler.CustomError(w, http.StatusConflict, errString)
					return
				}
			} else {
				done := usersutil.DisqualifyUser(userID, sourceEnityID, reqObj.Remark, "application callback")
				if !done {
					err = fmt.Errorf("error in disqualifying user for userID: %s", userID)
					logger.WithRequest(r).Error(err)
					panic(err)
				}
			}
		case "CPV VERIFIED":
			if userObj.Status != nil {
				if *(userObj.Status) != constants.UserStatusQualified {
					errorHandler.CustomError(w, http.StatusConflict, "invalid user status")
					return
				}
			}
			moduleName := usermodulemapping.OfferSelection
			if journey.IsTemporalFlow(userID, sourceEnityID, moduleName) {
				wf, err := userworkflows.Get(userID, moduleName)
				if err != nil {
					err = fmt.Errorf("error getting workflow for userID: %s, moduleName: %s, error: %s", userID, wf.ModuleName, err.Error())
					logger.WithUser(userID).Errorln(err)
					errorHandler.CustomError(w, http.StatusConflict, "invalid request")
					return
				}
				signalName := "unblock_user"
				err = temporalclient.Client.SignalWorkflow(context.Background(), wf.WorkflowID, wf.RunID, signalName, nil)
				if err != nil {
					logger.WithUser(userID).Errorln(err)
					panic(fmt.Errorf("unable to send signal for user %s, err : %s", userID, err))
				}
				go temporalsignallogging.Insert(signalName, userID, wf.WorkflowID, nil)
			} else {
				dynamicUserInfoString, err := users.GetDynamicUserInfo(userID)
				if err != nil {
					logger.WithUser(userID).Errorln(err)
					panic(err)
				}
				dynamicUserInfoPFL := make(map[string]interface{})
				err = json.Unmarshal([]byte(dynamicUserInfoString), &dynamicUserInfoPFL)
				if err != nil {
					logger.WithUser(userID).Errorln(err)
					panic(err)
				}
				val, found := dynamicUserInfoPFL[constants.PFLDynamicUserInfoKeyBlockUserDueToDistance]
				if found && !val.(bool) {
					errorHandler.CustomError(w, http.StatusConflict, "loan already applicable for user")
					return
				}
				dynamicUserInfoPFL[constants.PFLDynamicUserInfoKeyBlockUserDueToDistance] = false // unblock user
				dynamicUserInfoPFLBytes, err := json.Marshal(dynamicUserInfoPFL)
				if err != nil {
					logger.WithUser(userID).Errorln(err)
					panic(err)
				}
				err = users.Update(nil, users.User{DynamicUserInfo: string(dynamicUserInfoPFLBytes), ID: userID})
				if err != nil {
					logger.WithUser(userID).Errorln(err)
					panic(err)
				}
			}

			//TODO: create a temporal activity for activity logs
			go func(timeString string) {
				activityObj := activity.ActivityEvent{
					UserID:            userID,
					SourceEntityID:    sourceEnityID,
					LoanApplicationID: dbObj.LoanApplicationID,
					EntityType:        constants.EntityTypeSystem,
					EntityRef:         "application callback",
					EventType:         constants.ActivityUserQualified,
					Description:       "CPV VERIFIED",
				}
				activity.RegisterEvent(&activityObj, timeString)
			}(time.Now().Format("2006-01-02 15:04:05.000000"))
		case "CPV REJECTED":
			if userObj.Status != nil {
				if *(userObj.Status) != constants.UserStatusQualified {
					errorHandler.CustomError(w, http.StatusConflict, "invalid user status")
					return
				}
			}
			moduleName := usermodulemapping.OfferSelection
			if journey.IsTemporalFlow(userID, sourceEnityID, moduleName) {
				wf, err := userworkflows.Get(userID, moduleName)
				if err != nil {
					err = fmt.Errorf("error getting workflow for userID: %s, moduleName: %s, error: %s", userID, wf.ModuleName, err.Error())
					logger.WithUser(userID).Errorln(err)
					panic(err)
				}
				signalName := "disqualify_user"
				err = temporalclient.Client.SignalWorkflow(context.Background(), wf.WorkflowID, wf.RunID, signalName, nil)
				if err != nil {
					logger.WithUser(userID).Errorln(err)
					panic(fmt.Errorf("unable to send signal for user %s, err : %s", userID, err))
				}
				go temporalsignallogging.Insert(signalName, userID, wf.WorkflowID, nil)
			} else {
				done := usersutil.DisqualifyUser(userID, sourceEnityID, "CPV REJECTED", "application callback")
				if !done {
					err = fmt.Errorf("error in disqualifying user for userID: %s", userID)
					logger.WithRequest(r).Error(err)
					panic(err)
				}
			}
		case "Conditional Rejected":
			if dbObj.LoanStatus != constants.LoanStatusESign {
				errorHandler.CustomError(w, http.StatusConflict, "invalid loan status for rejection")
				return
			}
			if dbObj.LoanApplicationID != "" {
				errMessage, err := underwriting.RejectLoan(dbObj.LoanApplicationID, constants.PoonawallaFincorpID, constants.EntityTypeSystem, "application callback", reqObj.Remark)
				if err != nil {
					logger.WithRequest(r).Error(err)
					panic(err)
				} else if errMessage != "" {
					err = errors.New(errMessage)
					logger.WithRequest(r).Error(err)
					panic(err)
				}
			}
		case "Drop Off":

			if reqObj.ProcessingFee == "" && reqObj.InterestRate == "" {
				errorHandler.CustomError(w, http.StatusConflict, "not discounting offer callback")
				return
			}

			// if callback received after offer (i.e. user has already accepted the offer)
			_, err = personalloanoffer.GetLatestOfferForLenderID(userID, constants.PoonawallaFincorpID, []int{constants.OfferStatusIsAccepted})
			if err == nil { // if user has already accepted the offer
				customErr := "incorrect loan status for this function"
				errorHandler.CustomError(w, http.StatusConflict, customErr)
				return
			}

			if err != nil && err != sql.ErrNoRows {
				logger.WithUser(userID).Errorln(err)
				panic(err)
			}

			// if callback received before offer
			currModule, err := workflowutils.GetCurrentModuleNameByUserID(userID)
			if err != nil {
				logger.WithUser(userID).Errorln(err)
				panic(err)
			}

			if currModule != usermodulemapping.OfferSelection {
				customErr := "incorrect loan status for this function"
				errorHandler.CustomError(w, http.StatusConflict, customErr)
				return
			}

			// if user hasn't accepted the offer yet (Drop off case)
			if journey.IsTemporalFlow(userID, sourceEnityID, currModule) {
				wf, err := userworkflows.Get(userID, currModule)
				if err != nil {
					err = fmt.Errorf("error getting workflow for userID: %s, moduleName: %s, error: %s", userID, wf.ModuleName, err.Error())
					logger.WithUser(userID).Errorln(err)
					panic(err)
				}

				signalName := "update_offer"
				data := make(map[string]interface{})
				if reqObj.ProcessingFee != "" {
					processingFee, _ := strconv.ParseFloat(reqObj.ProcessingFee, 64)
					if processingFee <= 0 {
						customErr := "invalid processing fee"
						errorHandler.CustomError(w, http.StatusConflict, customErr)
						return
					}
					data["processingFee"] = strconv.FormatFloat(processingFee, 'f', -1, 64)
				}

				if reqObj.InterestRate != "" {
					interestRate, _ := strconv.ParseFloat(reqObj.InterestRate, 64)
					if interestRate <= 0 {
						customErr := "invalid interest rate"
						errorHandler.CustomError(w, http.StatusConflict, customErr)
						return
					}
					data["interestRate"] = strconv.FormatFloat(interestRate, 'f', -1, 64)
				}

				err = temporalclient.Client.SignalWorkflow(context.Background(), wf.WorkflowID, wf.RunID, signalName, data)
				if err != nil {
					logger.WithUser(userID).Errorln(err)
					if !strings.Contains(err.Error(), constants.TemporalWorkflowErrExecutionCompleted) {
						errorHandler.ReportToSentryWithFields(errorHandler.ErrorFields{
							"userID":     userID,
							"workflowID": wf.WorkflowID,
						}, err)
					}
					errorHandler.CustomError(w, http.StatusConflict, "Failed to execute Workflow Signal")
					return
				}

				go temporalsignallogging.Insert(signalName, userID, wf.WorkflowID, nil)
			}
		}

		var resData = map[string]string{
			"msg": "Success",
		}

		ctx = context.WithValue(r.Context(), "resData", resData)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// ThirdpartyMandateStatusCont Currently handling redirection from SuperMoney
func ThirdpartyMandateStatusCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		var requestObj struct {
			Status    string `json:"status" validate:"required"`
			MandateID string `json:"digio_doc_id" validate:"required"`
			Message   string `json:"message" validate:"required"`
			NpciTxnID string `json:"npci_txn_id" validate:"required"`
		}

		queryParams := r.URL.Query()

		requestObj.Status = queryParams.Get("status")
		requestObj.MandateID = queryParams.Get("digio_doc_id")
		requestObj.Message = queryParams.Get("message")
		requestObj.NpciTxnID = queryParams.Get("npci_txn_id")

		var payload strings.Builder
		payload.WriteString(fmt.Sprintf(`{"status": "%s", "digio_doc_id": "%s", "message": "%s", "npci_txn_id": "%s"}`, requestObj.Status, requestObj.MandateID, requestObj.Message, requestObj.NpciTxnID))

		webhookID := general.GetUUID()
		webhookName := "third_party_mandate_callback"
		if err := webhooklogs.Create(nil, webhookID, webhookName, payload.String()); err != nil {
			logger.WithRequest(r).Errorln(err)
		}

		// Validate request object
		v := validator.New()
		if err := v.Struct(requestObj); err != nil {
			logger.Log.Errorln(err)
			errorHandler.CustomError(w, http.StatusBadRequest, constants.ErrStringInvalidPayload)
			return
		}

		thirdPartyData, err := enachthirdparty.GetDetailsByMandateID(requestObj.MandateID)
		if err != nil {
			logger.WithRequest(r).Error(err)
			if errors.Is(err, sql.ErrNoRows) {
				errorHandler.CustomError(w, http.StatusConflict, constants.ErrStringInvalidMandateID)
			}
			return
		}
		userID := thirdPartyData.UserID

		userData, err := users.Get(userID)
		if err != nil {
			logger.WithRequest(r).Error(err)
			if errors.Is(err, sql.ErrNoRows) {
				errorHandler.CustomError(w, http.StatusConflict, constants.ErrStringInvalidUserID)
			}
			return
		}

		if err = webhooklogs.Update(nil, userID, webhookID); err != nil {
			logger.WithRequest(r).Error(err)
		}

		currentModule, err := workflowutils.GetCurrentModule(userID, userData.SourceEntityID, "")
		if err != nil {
			logger.WithRequest(r).Error(err)
			panic(err)
		}
		if !journey.IsTemporalFlow(userID, constants.SuperMoneyID, currentModule.Module.ModuleName) {
			err = fmt.Errorf("invalid temporal flow")
			logger.WithRequest(r).Error(err)
			panic(err)
		}

		wf, err := userworkflows.Get(userID, currentModule.Module.ModuleName)
		if err != nil {
			err = fmt.Errorf("error getting workflow for userID: %s, moduleName: %s, error: %s", userID, wf.ModuleName, err.Error())
			logger.WithUser(userID).Errorln(err)
			panic(err)
		}

		data := map[string]interface{}{
			"mandateID": requestObj.MandateID,
			"userID":    userID,
		}

		signalName := "mandate_redirect"
		if err = temporalclient.Client.SignalWorkflow(context.Background(), wf.WorkflowID, wf.RunID, signalName, data); err != nil {
			logger.WithUser(userID).Errorln(err)
			err = fmt.Errorf("unable to send signal for user %s, err : %s", userID, err)
			errorHandler.ReportToSentryWithFields(data, err)
			return
		}

		if err = temporalsignallogging.Insert(signalName, userID, wf.WorkflowID, data); err != nil {
			logger.WithUser(userID).Errorln(err)
			return
		}

		var resData = map[string]string{
			"success":         "true",
			"sourceEntityID":  userData.SourceEntityID,
			"source_redirect": "false",
		}

		ctx := context.WithValue(r.Context(), "resData", resData)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// RPSSignalCont - RPS Signal for SuperMoney
func RPSSignalCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		// VAS - Value Added Service
		var requestObj struct {
			ValueAddedService string `json:"vas" validate:"required"`
			LoanApplicationID string `json:"loan_application_id" validate:"required"`
		}

		queryParams := r.URL.Query()

		requestObj.ValueAddedService = queryParams.Get("vas")
		requestObj.LoanApplicationID = queryParams.Get("loan_application_id")

		// Validate request object
		v := validator.New()
		if err := v.Struct(requestObj); err != nil {
			http.Error(w, "[RPSSignalCont] Invalid input parameters", http.StatusBadRequest)
			logger.WithRequest(r).Errorf("[RPSSignalCont] Invalid input parameters: %v", err)
			return
		}

		userObj := r.Context().Value("user").(authentication.UserStruct)
		userID := userObj.UserID

		currentModule, err := workflowutils.GetCurrentModule(userID, constants.SuperMoneyID, requestObj.LoanApplicationID)
		if err != nil {
			logger.WithRequest(r).Error(err)
			return
		}
		if !journey.IsTemporalFlow(userID, constants.SuperMoneyID, currentModule.Module.ModuleName) {
			err = fmt.Errorf("[RPSSignalCont] invalid temporal flow")
			logger.WithRequest(r).Error(err)
			return
		}

		wf, err := userworkflows.Get(userID, currentModule.Module.ModuleName)
		if err != nil {
			err = fmt.Errorf("[RPSSignalCont] error getting workflow for userID: %s, moduleName: %s, error: %s", userID, wf.ModuleName, err.Error())
			logger.WithUser(userID).Errorln(err)
			return
		}

		data := map[string]interface{}{
			"valueAddedService": requestObj.ValueAddedService,
			"userID":            userID,
			"loanApplicationID": requestObj.LoanApplicationID,
		}

		if requestObj.ValueAddedService == "true" {
			if err = insurance.UpdateStatus(nil, requestObj.LoanApplicationID, constants.InsuranceStatusUnpaid); err != nil {
				err = fmt.Errorf("[RPSSignalCont] error updating insurance for userID: %s, moduleName: %s, vas: %s, error: %s",
					userID,
					wf.ModuleName,
					requestObj.ValueAddedService,
					err.Error())
				logger.WithUser(userID).Errorln(err)
				return
			}
		}

		signalName := "rps_signal"
		if err = temporalclient.Client.SignalWorkflow(context.Background(), wf.WorkflowID, wf.RunID, signalName, data); err != nil {
			logger.WithUser(userID).Errorln(err)
			err = fmt.Errorf("[RPSSignalCont] unable to send signal for user %s, err : %s", userID, err)
			errorHandler.ReportToSentryWithFields(data, err)
			return
		}

		if err = temporalsignallogging.Insert(signalName, userID, wf.WorkflowID, data); err != nil {
			logger.WithUser(userID).Errorln(err)
			return
		}

		var resData = map[string]string{
			"msg": "Success",
		}

		ctx := context.WithValue(r.Context(), "resData", resData)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// KYCRedirectSMCont KYC redirect for SuperMoney
func KYCRedirectSMCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.RecoveryHTML(w, r)

		// Extract attributes from context
		attributes, ok := r.Context().Value("attributes").(map[string]string)
		if !ok {
			http.Error(w, "Missing attributes in context", http.StatusBadRequest)
			return
		}

		userID := attributes["userID"]
		lenderVariableDetail, err := lendervariables.Get(userID, constants.PoonawallaFincorpID)
		if err != nil {
			http.Error(w, "userID not found", http.StatusNotFound)
			logger.WithUser(userID).Error(err)
			return
		}

		dbData, err := loanapplication.Get(r.Context(), lenderVariableDetail.LoanApplicationID)
		if err != nil {
			if errors.Is(err, sql.ErrNoRows) {
				http.Error(w, "loan not found", http.StatusNotFound)
				logger.WithUser(userID).Error(err)
			} else {
				http.Error(w, "error fetching loan data", http.StatusInternalServerError)
				logger.WithUser(userID).Error(err)
			}
			return
		}

		var resData = map[string]string{
			"status":         attributes["status"],
			"failMessage":    "",
			"programName":    "",
			"kycType":        "",
			"sourceEntityID": dbData.SourceEntityID,
		}

		resData["programName"], _ = sourceentitymodel.GetProgramByID(dbData.SourceEntityID)

		_ = usersutil.SetUserWaitState(userID, true)

		ctx := context.WithValue(r.Context(), "resData", resData)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func RedirectLeegalitySMCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		var reqObj struct {
			Status string `json:"status" validate:"required"`
			UserID string `json:"userId" validate:"required"`
		}

		// Parse query parameters directly to reqObj
		queryParams := r.URL.Query()

		reqObj.Status = queryParams.Get("status")
		reqObj.UserID = queryParams.Get("userId")

		// Validate request object
		v := validator.New()
		if err := v.Struct(reqObj); err != nil {
			http.Error(w, "Invalid input parameters", http.StatusBadRequest)
			logger.WithUser(reqObj.UserID).Error(err)
			return
		}

		status := 1
		message := "successfully signed"

		if reqObj.Status != "success" {
			status = 2
			message = "Document signing failed. Please try again"
		}

		//set user in wait state
		if err := usersutil.SetUserWaitState(reqObj.UserID, true); err != nil {
			logger.Log.WithFields(logrus.Fields{"userID": reqObj.UserID, "errCustom": "failure in creating user wait state"}).Error(err)
			return
		}

		moduleName := userworkflows.GetLastWorkflowsModuleName(reqObj.UserID)
		userWF, err := userworkflows.Get(reqObj.UserID, moduleName)
		if err != nil && !errors.Is(err, sql.ErrNoRows) {
			logger.WithUser(reqObj.UserID).Errorf("Error fetching user workflow: %v", err)
			return
		}

		if err = pflcallback.HandleWorkflowSignal(pflcallback.CallbackHandlerParams{
			UserID:     reqObj.UserID,
			ModuleName: moduleName,
			UserWF:     userWF,
			Signal:     constants.SignalRedirectLeegality,
		}); err != nil {
			logger.WithUser(reqObj.UserID).Errorf("Error signaling redirect_leegality: %v", err)
			return
		}

		resData := map[string]interface{}{
			"status":          status,
			"message":         message,
			"isWeb":           true,
			"docID":           "",
			"sourceEntityID":  constants.SuperMoneyID,
			"coApplicantLink": "",
			"mode":            "",
		}

		ctx := context.WithValue(r.Context(), "resData", resData)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// KotakBLStatusUpdateCont fetch status from kotak.
func KotakBLStatusUpdateCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		go func() {
			kotak.UpdateLoanStatus()
		}()

		response := map[string]string{
			"message": "ok",
		}

		ctx := context.WithValue(r.Context(), "resData", response)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// LendingKartStatusUpdateCont fetch status from lk.
func LendingKartStatusUpdateCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		// TODO: migrate entire go-routine to background workers, prepare list of loans in sync and push LK status call for each loan as a task
		go func() {
			loans, err := loanapplication.GetByLoanTypeAndLender(constants.LendingKartID, constants.LoanTypeBusinessLoan)
			if err != nil {
				log.Println(err)
				return
			}
			for idx, loan := range loans {
				loanApplicationID := loan.ID.String()
				res, err := lendingkart.GetApplicationStatus(loan.UserID, loan.SourceEntityID, loan.LoanApplicationNo)
				if err != nil {
					logger.WithUser(loan.UserID).Println(err)
					continue
				}
				switch res.ApplicationStatus {
				case lendingkart.StatusRejected:
					rejectReason := "rejected by lender: " + constants.LenderNamesMap[constants.LendingKartID]
					if len(res.RejectionReasons) > 0 {
						rejectReason += " due to " + res.RejectionReasons[0]
					}
					loanStatus := constants.LoanStatusLoanRejected
					err = loanapplication.Update(nil, loanapplication.StructForSet{
						ID:     loanApplicationID,
						Status: &loanStatus,
					})
					if err != nil {
						logger.WithUser(loan.UserID).Println(err)
						continue
					}
					dateTimeNowString := general.GetTimeStampString()
					activityObj := activity.ActivityEvent{
						UserID:            loan.UserID,
						SourceEntityID:    loan.SourceEntityID,
						LoanApplicationID: loanApplicationID,
						EntityType:        constants.EntityTypeSystem,
						EntityRef:         "",
						EventType:         constants.ActivityLoanRejected,
						Description:       rejectReason,
					}
					activity.RegisterEvent(&activityObj, dateTimeNowString)
				case lendingkart.StatusDisbursed:
					loanStatus := constants.LoanStatusDisbursed
					// commented because this API doesn't work

					// loanDisbursalDetails, err := lendingkart.GetLoanDetails(context.Background(), loan.UserID, loan.LoanApplicationNo, loan.SourceEntityID)
					// if err != nil {
					// 	logger.WithUser(loan.UserID).Error(err)
					// 	return
					// }

					err = loanapplication.Update(nil, loanapplication.StructForSet{
						ID:            loanApplicationID,
						Status:        &loanStatus,
						DisbursedDate: time.Now().Format(constants.DateFormat),
						// Amount:        loanDisbursalDetails.LoanAmount,
						// ProcessingFee: &loanDisbursalDetails.ProcessingFee,
						// Interest:      &loanDisbursalDetails.InterestRate,
					})
					if err != nil {
						logger.WithUser(loan.UserID).Println(err)
						continue
					}

					if loan.SourceEntityID == constants.ABFLMarketplaceID {
						_ = usermodulemapping.Create(nil, loan.UserID, loan.UserID, usermodulemapping.Redirection, constants.UserModuleStatusCompleted, loanApplicationID)
					}

					dateTimeNowString := general.GetTimeStampString()
					activityObj := activity.ActivityEvent{
						UserID:            loan.UserID,
						SourceEntityID:    loan.SourceEntityID,
						LoanApplicationID: loanApplicationID,
						EntityType:        constants.EntityTypeSystem,
						EntityRef:         "",
						EventType:         constants.ActivityLoanDisbursed,
						Description:       fmt.Sprintf("Lender: %s", constants.LenderNamesMap[constants.LendingKartID]),
					}
					activity.RegisterEvent(&activityObj, dateTimeNowString)
				default:
					fbxApplicationDetails := lendingkart.LKStatusToFBxStatusMapping[res.ApplicationStatus]
					if fbxApplicationDetails.Status == 0 {
						err = fmt.Errorf("unexpected status %s received from LK", res.ApplicationStatus)
						logger.WithLoanApplication(loanApplicationID).Warnln(err)
						continue
					}
					loanStatus := fbxApplicationDetails.Status
					var subStatusPtr *int
					if fbxApplicationDetails.SubStatus != 0 {
						subStatusPtr = &fbxApplicationDetails.SubStatus
					}
					if loanStatus != loan.Status || (fbxApplicationDetails.SubStatus != 0 && fbxApplicationDetails.SubStatus != loan.KYCStatus) {
						err = loanapplication.Update(nil, loanapplication.StructForSet{
							ID:        loanApplicationID,
							Status:    &loanStatus,
							KYCStatus: subStatusPtr,
						})
						if err != nil {
							logger.WithUser(loan.UserID).Errorln(err)
							continue
						}
						dateTimeNowString := general.GetTimeStampString()
						activityObj := activity.ActivityEvent{
							UserID:            loan.UserID,
							SourceEntityID:    loan.SourceEntityID,
							LoanApplicationID: loanApplicationID,
							EntityType:        constants.EntityTypeSystem,
							EntityRef:         "",
							EventType:         fbxApplicationDetails.EventName,
							Description:       fmt.Sprintf("Lender: %s", constants.LenderNamesMap[constants.LendingKartID]),
						}
						activity.RegisterEvent(&activityObj, dateTimeNowString)
					} else {
						logger.WithLoanApplication(loan.ID.String()).Infoln("no movement in status")
					}
				}
				if ((idx + 1) % 10) == 0 {
					time.Sleep(2 * time.Second)
				}
			}
		}()

		response := map[string]string{
			"message": "ok",
		}

		ctx := context.WithValue(r.Context(), "resData", response)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func TataReferralCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)
		userObj := r.Context().Value("user").(authentication.UserStruct)
		userID := userObj.UserID
		uniqueID := userObj.UniqueID
		sourceEntityID := userObj.SourceEntityID

		var productCode string

		switch sourceEntityID {
		case constants.TataBNPLID:
			productCode = "BNPL"
		case constants.TataPLID:
			productCode = "PL"
		default:
			errorHandler.CustomError(w, http.StatusBadRequest, "invalid program")
			return
		}

		referralResponse, customErr := tdl.ReferralWrapper(userID, uniqueID, sourceEntityID, productCode)
		if customErr != nil {
			logger.WithRequest(r).Errorln(customErr)
			errorHandler.CustomError(w, customErr.HTTPCode, "referral api failed")
			return
		}

		ctx := context.WithValue(r.Context(), "resData", referralResponse)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func DedupeCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		attributes := r.Context().Value("attributes").(map[string]interface{})

		request := attributes["request"].(structs.DeDupeRequest)

		result, err := lenderspecificutils.Dedupe(r.Context(), request)
		if err != nil && !errors.Is(err, sql.ErrNoRows) {
			logger.WithRequest(r).Errorln(err)
			errorHandler.CustomError(w, http.StatusInternalServerError, "something went wrong")
			return
		}

		response := &structs.GenericResponse{
			Data:       result,
			StatusCode: http.StatusOK,
			Status:     true,
		}

		if result == nil || errors.Is(err, sql.ErrNoRows) {
			response.Error = `No data found`
		}

		ctx := context.WithValue(r.Context(), "resData", response)
		next.ServeHTTP(w, r.WithContext(ctx))
	})

}

// SMAACallBackCont redirection from superMoney after account aggregate
func SMAACallBackCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		applicationID := r.URL.Query().Get("Transaction_ID")
		req := map[string]interface{}{
			"Transaction_ID": applicationID,
		}
		b, err := json.Marshal(req)
		if err != nil {
			logger.WithRequest(r).Error(err)
			panic(err)
		}

		webhookID := general.GetUUID()
		err = webhooklogs.Create(nil, webhookID, poonawalla.AaRedirection, string(b))
		if err != nil {
			logger.WithRequest(r).Error(err)
			panic(err)
		}

		userID, err := lendervariables.GetLatestUserIDByReferenceID(r.Context(), applicationID, constants.PoonawallaFincorpID)
		if err != nil {
			logger.WithUser(userID).Error(errors.New("user not found: " + err.Error()))
			errorHandler.CustomError(w, http.StatusConflict, fmt.Sprintf("user not found, applicationID of supermoney:%s", applicationID))
			return
		}

		err = webhooklogs.Update(nil, userID, webhookID)
		if err != nil {
			logger.WithUser(userID).Error(err)
			panic(err)
		}

		wf, err := userworkflows.Get(userID, usermodulemapping.BankConnect)
		if err != nil {
			err := fmt.Errorf("error fetching latest running workflow for user: %s", userID)
			logger.WithUser(userID).Errorln(err)
			errorHandler.CustomError(w, http.StatusPreconditionFailed, "invalid state for given action")
			return
		}
		if wf.IsTerminalStatus() {
			err := fmt.Errorf("invalid workflow status %s for user: %s", wf.Status, userID)
			logger.WithUser(userID).Errorln(err)
			errorHandler.CustomError(w, http.StatusPreconditionFailed, "invalid state for given action")
			return
		}

		workflowHadFailed, _, failureReason, err := temporalutility.RunSanityChecksOnWorkflowID(r.Context(), wf.UserID, wf.WorkflowID, wf.RunID)
		if err != nil {
			err := fmt.Errorf("invalid workflow status %s for user: %s, failureReason: %s", wf.Status, userID, failureReason)
			logger.WithUser(userID).Errorln(err)
			errorHandler.CustomError(w, http.StatusPreconditionFailed, "invalid state for given action")
			return
		}
		if workflowHadFailed {
			err := fmt.Errorf("workflow failed abrubly %s for user: %s, failureReason: %s", wf.Status, userID, failureReason)
			logger.WithUser(userID).Errorln(err)
			errorHandler.CustomError(w, http.StatusPreconditionFailed, "invalid state for given action")
			return
		}

		signalName := constants.SignalBankConnectUploaded
		err = temporalclient.Client.SignalWorkflow(context.Background(), wf.WorkflowID, wf.RunID, signalName, nil)
		if err != nil {
			logger.WithUser(userID).Error(err)
			errorHandler.CustomErrorSentry(w, r, http.StatusPreconditionFailed, "failed while sending bank connect signal", err)
			return
		}
		_ = temporalsignallogging.Insert(signalName, userID, wf.WorkflowID, nil)

		redirectUrl, err := users.GetDynamicUserInfoField(userID, "AARedirectionURL")
		if err != nil {
			logger.WithUser(userID).Error(err)
			panic(err)
		}
		if redirectUrl == "" {
			err := fmt.Errorf("redirectURL missing")
			logger.WithUser(userID).Error(err)
			panic(err)
		}
		http.Redirect(w, r, redirectUrl, http.StatusFound)
	})
}

// DMIAACallBackCont redirection from dmi after account aggregate
func DMIAACallBackCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		applicationID := r.URL.Query().Get("Transaction_ID")
		status := r.URL.Query().Get("Status")
		req := map[string]interface{}{
			"status":         status,
			"Transaction_ID": applicationID,
		}
		b, err := json.Marshal(req)
		if err != nil {
			logger.WithRequest(r).Error(err)
			panic(err)
		}

		webhookID := general.GetUUID()
		err = webhooklogs.Create(nil, webhookID, dmi.AaRedirection, string(b))
		if err != nil {
			logger.WithRequest(r).Error(err)
			panic(err)
		}

		var userID string
		query := `select user_id from lender_variables where dynamic_variables::JSONB->>'applicationId' = $1 and lender_id = $2`
		err = database.Get(&userID, query, applicationID, constants.DMIID)
		if err != nil {
			logger.WithUser(userID).Error(errors.New("user not found"))
			errorHandler.CustomError(w, http.StatusConflict, fmt.Sprintf("user not found, applicationID of dmi:%s", applicationID))
			return
		}

		user, err := users.Get(userID)
		if err != nil {
			logger.WithUser(userID).Error(err)
			panic(err)
		}

		err = webhooklogs.Update(nil, userID, webhookID)
		if err != nil {
			logger.WithUser(userID).Error(err)
			panic(err)
		}

		if strings.ToUpper(status) == "SUCCESS" {
			_, err := personalloanoffer.GetByLenderAndOfferType(userID, constants.DMIID, constants.OfferTypeFinal)
			if err != nil && err == sql.ErrNoRows {
				err = userjourney.SetWaitStatus(nil, userID, userjourney.WaitStatusShortPeriod, userjourney.WaitStatusReasonExpectingCallback)
				if err != nil {
					logger.WithUser(userID).Error(err)
					goto htmlLabel
				}
				go func() {
					_, err = lenderservice.ApplicationStatus(r.Context(), &lenderservice.ApplicationStatusReq{
						ApplicationReq: lenderservice.ApplicationReq{
							UserID:         userID,
							LenderID:       constants.DMIID,
							SourceEntityID: user.SourceEntityID,
						},
						Intent: "AccountAggregate",
					}, lenderservice.ApplicationStatusResource)
				}()
			}
		} else if strings.ToUpper(status) == "FAILED" {
			lenderName := constants.LenderNamesMap[constants.DMIID]
			description := fmt.Sprintf(`{"lender": "%s"}`, lenderName)
			dateTimeNowString := general.GetTimeStampString()
			go func() {
				activityObj := activity.ActivityEvent{
					UserID:            userID,
					SourceEntityID:    user.SourceEntityID,
					LoanApplicationID: "",
					EntityType:        constants.EntityTypeCustomer,
					EntityRef:         userID,
					EventType:         constants.ActivityAccountAggregatorFailed,
					Description:       description,
				}
				activity.RegisterEvent(&activityObj, dateTimeNowString)
			}()
		}

	htmlLabel:
		exitCondition := `?exit=true`
		jsEvent := fmt.Sprintf("window.location.replace('%s/%s')", conf.GetWebSDKBaseURLV2(constants.TataPLID), exitCondition)
		timeOutEvent := fmt.Sprintf("setTimeout(() => window.location.replace('%s/%s'), 1000)", conf.GetWebSDKBaseURLV2(constants.TataPLID), exitCondition)
		pageData := map[string]interface{}{
			"Title":        "Please Wait",
			"SubText":      "We are redirecting",
			"CTA":          "Retry",
			"Event":        jsEvent,
			"TimeOutEvent": timeOutEvent,
		}
		html := general.GetStringFromTemplate(htmltemplates.TataLightThemePendingPage, pageData)
		ctx := context.WithValue(r.Context(), "resData", html)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// DMIKYCCallBackCont redirection from dmi after kyc
func DMIKYCCallBackCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		status := r.URL.Query().Get("Status")
		applicationID := r.URL.Query().Get("Request_ID")

		req := map[string]interface{}{
			"Status":     status,
			"Request_ID": applicationID,
		}

		b, err := json.Marshal(req)
		if err != nil {
			logger.WithRequest(r).Error(err)
			panic(err)
		}

		webhookID := general.GetUUID()
		err = webhooklogs.Create(nil, webhookID, dmi.KycRedirection, string(b))
		if err != nil {
			logger.WithRequest(r).Error(err)
			panic(err)
		}

		var userID string
		query := `select user_id from lender_variables where dynamic_variables::JSONB->>'applicationId' = $1 and lender_id = $2`
		err = database.Get(&userID, query, applicationID, constants.DMIID)
		if err != nil {
			logger.WithUser(userID).Error(errors.New("user not found"))
			panic(fmt.Errorf("user not found, applicationID of dmi:%s", applicationID))
		}

		loanApplication, err := loanapplication.GetLatestByUser(userID)
		if err != nil {
			logger.WithRequest(r).Error(err)
			panic(err)
		}

		err = webhooklogs.Update(nil, userID, webhookID)
		if err != nil {
			logger.WithUser(userID).Error(err)
			panic(err)
		}

		if strings.ToUpper(status) == "SUCCESS" {
			lastModuleMapping, err := usermodulemapping.GetLast(userID)
			if err != nil && err != sql.ErrNoRows {
				log.Error(err)
			}
			if lastModuleMapping.ModuleName != "KYC" || (lastModuleMapping.ModuleName == "KYC" && lastModuleMapping.ModuleStatus != constants.UserModuleStatusCompleted) {
				err = userjourney.SetWaitStatus(nil, userID, userjourney.WaitStatusShortPeriod, userjourney.WaitStatusReasonExpectingCallback)
				if err != nil {
					logger.WithUser(userID).Error(err)
					goto htmlLabel
				}
				go func() {
					_, err = lenderservice.ApplicationStatus(r.Context(), &lenderservice.ApplicationStatusReq{
						ApplicationReq: lenderservice.ApplicationReq{
							UserID:            userID,
							LenderID:          constants.DMIID,
							SourceEntityID:    loanApplication.SourceEntityID,
							LoanApplicationID: loanApplication.ID.String(),
						},
						Intent: "UploadKYC",
					}, lenderservice.ApplicationStatusResource)
				}()
			}
		} else if strings.ToUpper(status) == "FAILED" {
			err = usermodulemapping.Create(nil, userID, userID, "KYC", constants.UserModuleStatusFailed, "")
			if err != nil {
				log.Error(err)
				goto htmlLabel
			}
			dateTimeString := general.GetTimeStampString()
			lenderName := constants.LenderNamesMap[constants.DMIID]
			description := fmt.Sprintf(`{"lender": "%s"}`, lenderName)
			go activity.ActivityLogger(userID, loanApplication.SourceEntityID, "", constants.EntityTypeCustomer, constants.ActivityEKYCFailed, description, loanApplication.ID.String(), dateTimeString, false)
		}
	htmlLabel:
		exitCondition := `?exit=true`
		jsEvent := fmt.Sprintf("window.location.replace('%s/%s')", conf.GetWebSDKBaseURLV2(constants.TataPLID), exitCondition)
		timeOutEvent := fmt.Sprintf("setTimeout(() => window.location.replace('%s/%s'), 1000)", conf.GetWebSDKBaseURLV2(constants.TataPLID), exitCondition)
		pageData := map[string]interface{}{
			"Title":        "Please Wait",
			"SubText":      "We are redirecting",
			"CTA":          "Retry",
			"Event":        jsEvent,
			"TimeOutEvent": timeOutEvent,
		}
		html := general.GetStringFromTemplate(htmltemplates.TataLightThemePendingPage, pageData)
		ctx := context.WithValue(r.Context(), "resData", html)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// DMINACHCallBackCont redirection from dmi after nach
func DMINACHCallBackCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		status := r.URL.Query().Get("data")
		loanApplicationNo := r.URL.Query().Get("oppname")

		req := map[string]interface{}{
			"status":  status,
			"oppname": loanApplicationNo,
		}
		b, err := json.Marshal(req)
		if err != nil {
			logger.WithRequest(r).Error(err)
			panic(err)
		}
		webhookID := general.GetUUID()
		err = webhooklogs.Create(nil, webhookID, dmi.NachRedirection, string(b))
		if err != nil {
			logger.WithRequest(r).Error(err)
			panic(err)
		}

		loanApplication, err := loanapplication.GetByNo(r.Context(), loanApplicationNo)
		if err != nil {
			logger.WithRequest(r).Error(err)
			panic(err)
		}

		err = webhooklogs.Update(nil, loanApplication.UserID, webhookID)
		if err != nil {
			logger.WithUser(loanApplication.UserID).Error(err)
			panic(err)
		}

		switch strings.ToUpper(status) {
		case "SUCCESS":
			lastModuleMapping, err := usermodulemapping.GetLast(loanApplication.UserID)
			if err != nil && err != sql.ErrNoRows {
				log.Error(err)
			}
			if lastModuleMapping.ModuleName != "ENACH" || (lastModuleMapping.ModuleName == "ENACH" && lastModuleMapping.ModuleStatus != constants.UserModuleStatusCompleted) {
				err = userjourney.SetWaitStatus(nil, loanApplication.UserID, userjourney.WaitStatusShortPeriod, userjourney.WaitStatusReasonExpectingCallback)
				if err != nil {
					logger.WithUser(loanApplication.UserID).Error(err)
					goto htmlLabel
				}
				go func() {
					_, err = lenderservice.ApplicationStatus(r.Context(), &lenderservice.ApplicationStatusReq{
						ApplicationReq: lenderservice.ApplicationReq{
							UserID:            loanApplication.UserID,
							LenderID:          constants.DMIID,
							SourceEntityID:    loanApplication.SourceEntityID,
							LoanApplicationID: loanApplication.ID.String(),
						},
						Intent: "EmandateNach",
					}, lenderservice.ApplicationStatusResource)
				}()
			}
		case "FAILED":
			err = usermodulemapping.Create(nil, loanApplication.UserID, loanApplication.UserID, "ENACH", constants.UserModuleStatusFailed, "")
			if err != nil {
				log.Error(err)
				goto htmlLabel
			}
			dateTimeString := general.GetTimeStampString()
			lenderName := constants.LenderNamesMap[constants.DMIID]
			description := fmt.Sprintf(`{"lender": "%s"}`, lenderName)
			go activity.ActivityLogger(loanApplication.UserID, loanApplication.SourceEntityID, "", constants.EntityTypeCustomer, constants.ActivityEKYCFailed, description, loanApplication.ID.String(), dateTimeString, false)
		}

	htmlLabel:
		exitCondition := `?exit=true`
		jsEvent := fmt.Sprintf("window.location.replace('%s/%s')", conf.GetWebSDKBaseURLV2(constants.TataPLID), exitCondition)
		timeOutEvent := fmt.Sprintf("setTimeout(() => window.location.replace('%s/%s'), 1000)", conf.GetWebSDKBaseURLV2(constants.TataPLID), exitCondition)
		pageData := map[string]interface{}{
			"Title":        "Please Wait",
			"SubText":      "We are redirecting",
			"CTA":          "Retry",
			"Event":        jsEvent,
			"TimeOutEvent": timeOutEvent,
		}
		html := general.GetStringFromTemplate(htmltemplates.TataLightThemePendingPage, pageData)
		ctx := context.WithValue(r.Context(), "resData", html)
		next.ServeHTTP(w, r.WithContext(ctx))

	})
}

// DMINACHCallBackCont redirection from dmi after nach
func MoneyControlConsumeInfo(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		// IP checks
		if conf.ENV == conf.ENV_PROD {
			if !(strings.Contains(r.RemoteAddr, "************") || strings.Contains(r.RemoteAddr, "************")) {
				errorHandler.CustomError(w, http.StatusUnauthorized, "request from unauthorised source")
				return
			}
		}

		// read body
		b, err := io.ReadAll(r.Body)
		defer r.Body.Close()
		if err != nil {
			logger.WithRequest(r).Error(err)
			errorHandler.CustomError(w, http.StatusInternalServerError, constants.GenericFailureMessage)
			return
		}

		// write payload to DB
		webhookID := general.GetUUID()
		err = webhooklogs.Create(nil, webhookID, partner.MoneyControlWebhookServiceName, string(b))
		if err != nil {
			logger.WithRequest(r).Error(err)
			errorHandler.CustomError(w, http.StatusInternalServerError, constants.GenericFailureMessage)
			return
		}

		var obj partner.MoneyControlInfo
		err = json.Unmarshal(b, &obj)

		IPAddress := strings.Split(r.RemoteAddr, ":")
		obj.IpAddress = IPAddress[0]

		if err != nil || obj.IpAddress == "" {
			logger.WithRequest(r).Error(err)
			errorHandler.CustomError(w, http.StatusBadRequest, constants.RequestValidationFailedMessage)
			return
		}

		err, statusCode := partner.ProcessMoneyControlInfo(obj)
		if err != nil {
			logger.WithRequest(r).Error(err)
			errorHandler.CustomError(w, statusCode, err.Error())
			return
		}

		response := map[string]interface{}{
			"message": "ok",
		}

		ctx := context.WithValue(r.Context(), "resData", response)
		next.ServeHTTP(w, r.WithContext(ctx))

	})
}

// MuthootBNPLAmountUpdateCont updates net closure amount on users
func MuthootBNPLClosureAmountUpdateCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		go func() {
			onemuthoot.UpdateNetClosureAmount()
		}()

		response := map[string]string{
			"message": "ok",
		}

		ctx := context.WithValue(r.Context(), "resData", response)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// MuthootBNPLAmountUpdateCont updates net closure amount on users
func DigioFetchAndUpdateUmrn(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		go func() {
			digio.FetchAndUpdateUmrn()
		}()

		response := map[string]string{
			"message": "ok",
		}

		ctx := context.WithValue(r.Context(), "resData", response)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// ElectricityServiceProvidersCont returns list of electricity service providers for source_entity_id
func ElectricityServiceProvidersCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		var electricityProviders []string
		query := `select name from electricity_service_providers order by name;`
		if err := database.Select(&electricityProviders, query); err != nil {
			logger.WithRequest(r).Errorln(err)
			panic(err)
		}

		response := map[string]interface{}{
			"electricityProviders": electricityProviders,
		}

		ctx := context.WithValue(r.Context(), "resData", response)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func SubmitAdditionalDocsCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)
		userObj := r.Context().Value("user").(authentication.UserStruct)
		attributes := r.Context().Value("attributes").([]map[string]interface{})

		userID := userObj.UserID
		sourceEntityID := userObj.SourceEntityID
		var mediaID, documentID, documentName, documentType string
		loanApplicationData, err := loanapplication.GetLatestByUser(userID)
		if err != nil {
			logger.WithRequest(r).Errorln(err)
			panic(err)
		}

		// Tcap upload document struct obj array
		var document []*lenderservice.KYCDocumentstructsDetails
		for _, attribute := range attributes {

			documentID = attribute["documentID"].(string)
			// documentType := attribute["documentType"].(string)
			mediaID = attribute["mediaID"].(string)
			documentType = attribute["documentType"].(string)

			if documentID == "" || mediaID == "" {
				errorHandler.CustomError(w, http.StatusBadRequest, constants.ErrStringInvalidPayload)
				return
			}

			if journey.IsMFLBLSourcing(sourceEntityID) {
				mediaObj, err := media.Get(r.Context(), mediaID)
				if err != nil {
					logger.WithUser(userID).Errorln(err)
					panic(err)
				}

				docObj, err := documents.Get(r.Context(), documentID)
				if err != nil {
					logger.WithUser(userID).Errorln(err)
					panic(err)
				}

				err = userdocuments.Insert(r.Context(), userdocuments.UserDocument{
					ID:           general.GetUUID(),
					UserID:       userID,
					Program:      userObj.ProgramName,
					FileKey:      mediaObj.Path,
					Status:       userdocuments.StatusActive,
					DocumentType: documentType,
					FileName:     mediaObj.Path,
					MediaID:      mediaID,
					DocumentID:   documentID,
					DocumentName: docObj.DocumentName,
				})

				if err != nil {
					logger.WithUser(userID).Errorln(err)
					panic(err)
				}
			}

			if journey.IsMintifiAsLender(sourceEntityID) {
				ctx := r.Context()
				var mediaPath string

				mediaDetails, err := media.Get(ctx, mediaID)
				if err != nil {
					if err == sql.ErrNoRows {
						continue
					} else {
						errMsg := fmt.Sprintf("unable to find media details for Additional Documents err:%s", err.Error())
						logger.WithUser(userID).Error(errMsg)
						errorHandler.CustomError(w, http.StatusNotFound, errMsg)
						return
					}
				}

				if mediaDetails.Path == "" {
					errMsg := fmt.Sprintf("unable to find media details for Additional Documents err:%s", err.Error())
					logger.WithUser(userID).Error(errMsg)
					errorHandler.CustomError(w, http.StatusNotFound, errMsg)
					return
				}

				appReq, err := lenderservice.GetApplicationReq(ctx, userID)
				if err != nil {
					err := fmt.Errorf("unable to get user details for service err: %s", err.Error())
					logger.WithUser(userID).Error(err)
					panic(err)
				}

				docURL := s3.GetPresignedURLS3(mediaPath, 300)

				err = lenderservice.UploadDocument(ctx, &lenderservice.UploadDocumentReq{
					ApplicationReq: appReq,
					Document: &lenderservice.KYCDocumentstructsDetails{
						DocumentType: constants.DocCancelledCheque,
						DocumentURL:  docURL,
					},
				})

				if err != nil {
					errMsg := fmt.Sprintf("unable to upload physical nach document : %s", err.Error())
					logger.WithUser(userID).Error(errMsg)
					activityObj := activity.ActivityEvent{
						UserID:            userID,
						SourceEntityID:    sourceEntityID,
						LoanApplicationID: loanApplicationData.ID.String(),
						EntityType:        constants.EntityTypeCustomer,
						EntityRef:         userID,
						EventType:         constants.ActivityLenderAPIFailed,
						Description:       fmt.Sprintf("resource: %s", lenderservice.UploadDocumentResource),
					}
					activity.RegisterEvent(&activityObj, general.GetTimeStampString())
					errorHandler.CustomError(w, http.StatusConflict, err.Error())
					return
				}
			}

			if journey.IsTataCapitalAsLender(sourceEntityID) {

				mediaDetails, err := media.Get(r.Context(), mediaID)
				if err != nil {
					errMsg := fmt.Sprintf("unable to find media details for Additional Documents err:%s", err.Error())
					logger.WithUser(userID).Error(errMsg)
					errorHandler.CustomError(w, http.StatusNotFound, errMsg)
					return
				}

				if mediaDetails.Path == "" {
					errMsg := fmt.Sprintf("unable to find media details for Additional Documents err:%s", err.Error())
					logger.WithUser(userID).Error(errMsg)
					errorHandler.CustomError(w, http.StatusNotFound, errMsg)
					return
				}

				documentName, err = documents.GetNameFromDocID(r.Context(), documentID)
				if err != nil {
					errMsg := fmt.Sprintf("unable to find document err:%s", err.Error())
					logger.WithUser(userID).Error(errMsg)
					errorHandler.CustomError(w, http.StatusNotFound, errMsg)
					return
				}
				docURL := s3.GetPresignedURLS3(mediaDetails.Path, 300)
				fileName := fmt.Sprintf("%s_%s", documentName, mediaDetails.Path)

				var documentObj lenderservice.KYCDocumentstructsDetails
				documentObj.DocumentURL = docURL
				documentObj.DocumentName = fileName
				documentObj.DocumentID = documentID
				documentObj.MediaID = mediaID
				documentObj.DocumentType = constants.DocTypeAdditionalDocument
				documentObj.Path = mediaDetails.Path
				document = append(document, &documentObj)
			} else {

				dashboardDoc := dashboarddocs.DashboardDoc{
					LoanApplicationID: loanApplicationData.ID.String(),
					DocID:             general.GetUUID(),
					DocumentID:        sql.NullString{String: documentID, Valid: true},
					MediaID:           mediaID,
					EntityType:        constants.EntityTypeCustomer,
					CreatedBy:         userID,
					Status:            true,
					ReviewStatus:      0,
				}
				if err = dashboardDoc.Insert(nil); err != nil {
					logger.WithRequest(r).Errorln(err)
					panic(err)
				}
			}
		}

		if journey.IsTataCapitalAsLender(sourceEntityID) && len(document) > 0 {

			appReq := lenderservice.ApplicationReq{
				UserID:            userID,
				LoanApplicationID: loanApplicationData.ID.String(),
				SourceEntityID:    sourceEntityID,
				LenderID:          constants.TataCapitalID,
				LoanApplicationNo: loanApplicationData.LoanApplicationNo,
				LoanType:          "FRESH",
			}

			req := lenderservice.UploadDocumentsReq{
				ApplicationReq: appReq,
				KycDocuments:   document,
			}

			_, err = lenderservice.UploadKYC(context.Background(), &req)
			if err != nil {
				errMsg := constants.GenericFailureMessage
				logger.WithUser(userID).Error(errMsg)
				activityObj := activity.ActivityEvent{
					UserID:            userID,
					SourceEntityID:    sourceEntityID,
					LoanApplicationID: loanApplicationData.ID.String(),
					EntityType:        constants.EntityTypeCustomer,
					EntityRef:         userID,
					EventType:         constants.ActivityLenderAPIFailed,
					Description:       fmt.Sprintf("resource: %s", lenderservice.UploadDocumentResource),
				}
				activity.RegisterEvent(&activityObj, general.GetTimeStampString())
				errorHandler.CustomError(w, http.StatusConflict, errMsg)
				return
			}

			err = loankycdetails.Insert(nil, loankycdetails.LoanKYCDetailStruct{
				UniqueID:    general.GetUUID(),
				LoanID:      loanApplicationData.ID.String(),
				MediaID:     mediaID,
				DocType:     documentName,
				DocumentID:  documentID,
				Status:      constants.KYCDocStatusUploaded,
				CreatedBy:   userObj.Email,
				BackMediaID: "",
				Name:        "",
				Identifier:  "",
			})
			if err != nil {
				errMsg := constants.GenericFailureMessage
				logger.WithUser(userID).Error(err)
				errorHandler.CustomError(w, http.StatusConflict, errMsg)
				errorHandler.ReportToSentryWithoutRequest(err)
				return
			}

		}

		if err := usermodulemapping.Create(nil, userID, userID, usermodulemapping.AdditionalDocuments, constants.UserModuleStatusCompleted, loanApplicationData.ID.String()); err != nil {
			logger.WithRequest(r).Panic(err)
			panic(err)
		}

		if journey.IsABFLBLSourcing(sourceEntityID) {
			var decision string
			// For new endpoint integration, get the decision from evaluation code.
			if featureflag.Get(userID, journey.FlagEndpointVersion2) {

				bankingConfig, err := underwriting.GetEvaluationConfig(userID, userObj.SourceEntityID, loanApplicationData.LenderID, constants.SourceTypeAdditionalDocumentsKYCCheck)
				if err != nil {
					logger.WithUser(userID).Error(err)
					errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userID}, err)
					panic(err)
				}

				decisionOutput, err := decisionengine.GetDecisionAndOutputVariablesV2(decisionengine.DecisionAndOutputVariablesInput{
					UserID:         userID,
					RuleType:       constants.RuleTypeWorkflow,
					EvaluationCode: bankingConfig.EvaluationCode,
				})
				if err != nil {
					logger.WithUser(userID).Error(err)
					errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userID}, err)
					panic(err)
				}

				decision = decisionOutput.Decision
				// Else follow old method.
			} else {
				query := `SELECT decision from decision_engine_response where user_id = $1 and rule_version ilike 'ohp_waiver_for_address_match%' order by created_at desc limit 1`
				err := database.Get(&decision, query, userID)
				if err != nil {
					logger.WithRequest(r).Errorln(err)
					panic(err)
				}

			}

			if general.InArr(decision, []string{constants.UnderwritingDecisionHideBusinessDocsOHP, constants.UnderwritingDecisionShowBusinessDocsOHP, constants.UnderwritingDecisionShowBusinessDocsOHPAddDocMand, constants.UnderwritingDecisionHideBusinessDocsOHPAddDocMand}) {
				err := loanapplication.UpdateStatus(nil, loanApplicationData.ID.String(), constants.LoanStatusDetails)
				if err != nil {
					logger.WithRequest(r).Panic(err)
					panic(err)
				}
			}
		}

		if journey.IsMFLBLSourcing(sourceEntityID) {
			err := loanapplication.UpdateStatus(nil, loanApplicationData.ID.String(), constants.LoanStatusDetails)
			if err != nil {
				logger.WithRequest(r).Panic(err)
				panic(err)
			}
		}

		dateTimeString := general.GetTimeStampString()
		go activity.ActivityLogger(userID, userObj.SourceEntityID, "", constants.EntityTypeCustomer, constants.ActivityAdditionalDocsUploaded, "", loanApplicationData.ID.String(), dateTimeString, false)

		var response = map[string]string{
			"message": "success",
		}

		ctx := context.WithValue(r.Context(), "resData", response)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// RazorpayCallbackCont handles callback from razorpay
func RazorpayCallbackCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		attributes := r.Context().Value("attributes").(map[string]interface{})
		payload := attributes["redirectionReq"].(razorpay.CallbackReq)

		byteData, err := json.Marshal(payload)
		if err != nil {
			logger.WithRequest(r).Errorln(err)
			panic(err)
		}

		webhookID := general.GetUUID()
		if err = webhooklogs.Create(nil, webhookID, razorpay.RazorpayCallback, string(byteData)); err != nil {
			logger.WithRequest(r).Errorln(err)
			panic(err)
		}

		var loanObj struct {
			SDKVersion        string `db:"sdk_version"`
			LoanType          string `db:"loan_type"`
			UserID            string `db:"user_id"`
			LoanApplicationID string `db:"loan_application_id"`
			LoanStatus        int    `db:"loan_status"`
			LenderID          string `db:"lender_id"`
			SourceEntityID    string `db:"source_entity_id"`
		}

		query := `SELECT
					coalesce(u.sdk_version, '') as sdk_version,
					la.user_id,
					la.loan_application_id,
					la.status as loan_status,
					coalesce(la.loan_type, '') as loan_type,
					coalesce(la.lender_id::TEXT, '') as lender_id,
					la.source_entity_id as source_entity_id
				from enach_third_party etp 
					join loan_application la
						on etp.loan_id = la.loan_application_id
					join users u on la.user_id = u.user_id
						where etp.mandate_id = $1
						order by etp.created_at desc limit 1;`
		err = database.Get(&loanObj, query, payload.RazorpayOrderID)
		if err != nil {
			logger.WithUser(loanObj.UserID).Errorln(err)
			panic("e-mandate not found")
		}

		if !razorpay.VerifySignature(payload.RazorpaySignature, loanObj.LenderID, payload.RazorpayOrderID, payload.RazorpayPaymentID) {
			logger.WithRequest(r).Errorln("signature verification failed for loanApplicationID ", loanObj.LoanApplicationID)
			errorHandler.CustomError(w, http.StatusUnauthorized, "signature verification failed")
			return
		}

		isWeb := false
		loanObj.SDKVersion = strings.ToLower(loanObj.SDKVersion)
		if loanObj.SDKVersion == constants.SDKVersionWeb || strings.HasPrefix(loanObj.SDKVersion, "hybrid") {
			isWeb = true
		}

		if err = webhooklogs.Update(nil, loanObj.UserID, webhookID); err != nil {
			logger.WithRequest(r).Errorln(err)
			panic(err)
		}

		programName, _ := sourceentitymodel.GetProgramByID(loanObj.SourceEntityID)

		var response = map[string]interface{}{
			"status":         "success",
			"isWeb":          isWeb,
			"loanType":       loanObj.LoanType,
			"lenderID":       loanObj.LenderID,
			"loanStatus":     loanObj.LoanStatus,
			"programName":    programName,
			"sourceEntityID": loanObj.SourceEntityID,
		}

		ctx := context.WithValue(r.Context(), "resData", response)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// RazorpayWebhookCont handles callback from razorpay
func RazorpayWebhookCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		decoder := json.NewDecoder(r.Body)
		defer r.Body.Close()

		var reqObj razorpay.RazorpayWebhookStruct
		err := decoder.Decode(&reqObj)
		if err != nil {
			logger.WithRequest(r).Errorln(err)
			panic(err)
		}

		payloadBytes, err := json.Marshal(reqObj)
		if err != nil {
			logger.WithRequest(r).Errorln(err)
			panic(err)
		}

		webhookID := general.GetUUID()
		if err = webhooklogs.Create(nil, webhookID, razorpay.RazorpayWebhook, string(payloadBytes)); err != nil {
			logger.WithRequest(r).Errorln(err)
			panic(err)
		}

		switch {
		case general.InArr(reqObj.Event, []string{razorpay.EventTokenRejected, razorpay.EventTokenConfirmed}):
			value, ok := reqObj.Payload["token"]
			if !ok {
				logger.WithRequest(r).Errorln("invalid token ", reqObj)
				errorHandler.CustomError(w, http.StatusBadRequest, constants.ErrStringInvalidPayload)
				return
			}
			tokenBytes, err := json.Marshal(value["entity"])
			if err != nil {
				logger.WithRequest(r).Errorln(err)
				panic(err)
			}
			var tokenData razorpay.TokenEntity
			if err = json.Unmarshal(tokenBytes, &tokenData); err != nil {
				logger.WithRequest(r).Errorln(err)
				panic(err)
			}

			switch reqObj.Event {
			case razorpay.EventTokenConfirmed:
				if err = tokenData.ProcessTokenConfirmed(webhookID); err != nil {
					logger.WithRequest(r).Errorln(err)
					panic(err)
				}
			case razorpay.EventTokenRejected:
				if err = tokenData.ProcessTokenRejected(webhookID); err != nil {
					logger.WithRequest(r).Errorln(err)
					panic(err)
				}
			}

		case general.InArr(reqObj.Event, []string{razorpay.EventPaymentAuthorized, razorpay.EventPaymentCaptured, razorpay.EventPaymentFailed}):
			value, ok := reqObj.Payload["payment"]
			if !ok {
				logger.WithRequest(r).Errorln("invalid payment ", reqObj)
				errorHandler.CustomError(w, http.StatusBadRequest, constants.ErrStringInvalidPayload)
				return
			}
			paymentBytes, err := json.Marshal(value["entity"])
			if err != nil {
				logger.WithRequest(r).Errorln(err)
				panic(err)
			}
			var paymentData razorpay.PaymentEntity
			if err = json.Unmarshal(paymentBytes, &paymentData); err != nil {
				logger.WithRequest(r).Errorln(err)
				panic(err)
			}

			switch reqObj.Event {
			case razorpay.EventPaymentAuthorized, razorpay.EventPaymentCaptured:
				if err = paymentData.ProcessPaymentSuccess(webhookID); err != nil {
					logger.WithRequest(r).Errorln(err)
				}
			case razorpay.EventPaymentFailed:
				if err = paymentData.ProcessPaymentFailed(webhookID); err != nil {
					logger.WithRequest(r).Errorln(err)
				}
			}
		}

		var response = map[string]interface{}{
			"status": "success",
		}

		ctx := context.WithValue(r.Context(), "resData", response)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func DisbursalStatusCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		go func() {
			dmi.GetDisbursalStatus()
		}()

		response := map[string]string{
			"message": "ok",
		}

		ctx := context.WithValue(r.Context(), "resData", response)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func PFLCustomUserDataDumpCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		attributes := r.Context().Value("attributes").(map[string]interface{})
		fromDate := attributes["from"].(string)
		toDate := attributes["to"].(string)
		filter := attributes["filter"].(string)

		exportID, csvFile, err := export.CreateExportLog("Airflow Dag", attributes, "Custom Lead Dump", exportlogs.AirflowDAG, "Airflow Dag", "", "csv")

		go func() {
			defer errorHandler.RecoveryNoResponse()
			// Write export status as failed incase of any errors
			defer export.ExportRecoveryHandler(exportID)
			sourceEntityID := constants.PoonawallaFincorpID
			if err != nil {
				logger.WithRequest(r).Errorln(err)
				panic(err)
			}
			defer func() {
				if err := csvFile.Close(); err != nil {
					errorHandler.ReportToSentryWithoutRequest(err)
				}
			}()
			ctx, cancel := context.WithTimeout(context.Background(), constants.QueryTimeoutExportsViaDAG)
			defer cancel()
			// now get actual records

			pwUserReport, err := export.PFLCustomLeadDump(ctx, filter, fromDate, toDate, sourceEntityID, []int{}, [][]int{})

			if err != nil {
				logger.WithRequest(r).Errorln(err)

				if errorHandler.IsContextCancelledError(err) {
					err = exportlogs.Update(exportlogs.StatusFailed, "operation timed out, please try with a smaller time range", "", exportID)
					if err != nil {
						logger.WithRequest(r).Errorln(err)
						panic(err)
					}
					return
				}
				panic(err)
			}
			csvWriter := csv.NewWriter(csvFile)
			//writing headers
			err = csvWriter.Write([]string{
				"lead_id", "customer_id", "source", "status", "user_created_at", "name", "email", "pan", "mobile", "occupation_type", "city", "state", "pincode", "permanent_address_pincode", "radius_flag_result", "radius_value_kms", "distance_blocking", "loan_application_no", "loan_status", "application_created_at", "platform", "customer_entered_loan_amount", "eligible_loan_amount", "customer_entered_tenure", "eligible_tenure", "annual_interest", "emi_amount", "processing_fee", "GST", "advance_emi", "disbursal_amount", "net_disbursal_amount", "sign_agreement_date", "disbursed_or_activation_date", "rejection_reason", "latitude", "longitude", "face_match_score", "face_match_result", "face_match_confidence_score", "VAS_type", "VAS_charges"})
			if err != nil {
				logger.WithRequest(r).Errorln(err)
				panic(err)
			}

			for index := range pwUserReport {
				if err != nil {
					logger.WithRequest(r).Error(err)
					panic(err)
				}

				loanAmount := strings.ReplaceAll(masterDashboardUtils.ConvertFloatToCurrency(pwUserReport[index].LoanAmount), ",", "")
				eligibleLoanAmount := strings.ReplaceAll(masterDashboardUtils.ConvertFloatToCurrency(pwUserReport[index].EligibleAmount), ",", "")
				emiAmount := strings.ReplaceAll(masterDashboardUtils.ConvertFloatToCurrency(pwUserReport[index].EMIAmount), ",", "")
				processingFee := strings.ReplaceAll(masterDashboardUtils.ConvertFloatToCurrency(pwUserReport[index].ProcessingFee), ",", "")
				advanceEMI := strings.ReplaceAll(masterDashboardUtils.ConvertFloatToCurrency(pwUserReport[index].AdvanceEMI), ",", "")
				disbursalAmount := strings.ReplaceAll(masterDashboardUtils.ConvertFloatToCurrency(pwUserReport[index].DisbursalAmount), ",", "")
				netDisbursalAmount := strings.ReplaceAll(masterDashboardUtils.ConvertFloatToCurrency(pwUserReport[index].NetDisbursalAmount), ",", "")
				gst := strings.ReplaceAll(masterDashboardUtils.ConvertFloatToString(pwUserReport[index].GST), ",", "")
				vasCharges := masterDashboardUtils.ConvertFloatToString(pwUserReport[index].VASCharges)

				// write rows to temp csv
				err = csvWriter.Write([]string{
					pwUserReport[index].LeadID, pwUserReport[index].CustomerID, pwUserReport[index].Source, constants.UserStatusNumToStr[pwUserReport[index].Status], pwUserReport[index].UserCreatedAt, pwUserReport[index].Name,
					pwUserReport[index].Email, pwUserReport[index].Pan, pwUserReport[index].Mobile, pwUserReport[index].OccupationType, pwUserReport[index].City, pwUserReport[index].State, pwUserReport[index].Pincode, pwUserReport[index].PermanentAddressPincode, pwUserReport[index].RadiusFlagResult, pwUserReport[index].RadiusValueInKMS, pwUserReport[index].DistanceBlocking, pwUserReport[index].LoanApplicationNo, constants.GetLoanStatusText(pwUserReport[index].LoanStatus, pwUserReport[index].LoanKYCStatus), pwUserReport[index].ApplicationCreatedAt,
					pwUserReport[index].Platform, loanAmount, eligibleLoanAmount, pwUserReport[index].Tenure, pwUserReport[index].EligibleTenure, fmt.Sprintf("%v", pwUserReport[index].AnnualInterest), emiAmount, processingFee, fmt.Sprintf("%v", gst), advanceEMI, disbursalAmount, netDisbursalAmount, pwUserReport[index].SignAgreementDate, pwUserReport[index].DisbursedOrActivationDate, pwUserReport[index].RejectionReason, pwUserReport[index].Latitude, pwUserReport[index].Longitude, pwUserReport[index].FaceMatchScore, pwUserReport[index].FaceMatchStatus, pwUserReport[index].FaceMatchConfidenceScore, pwUserReport[index].VASType, vasCharges})
				if err != nil {
					logger.WithRequest(r).Errorln(err)
					panic(err)
				}
			}

			csvWriter.Flush()
			err = csvWriter.Error()
			if err != nil {
				log.Println("an error occurred during the flush")
				logger.WithRequest(r).Errorln(err)
				panic(err)
			}
			fileName, filePath := export.GetFileDetails(exportID, "csv")

			s3Path := "exports/" + fileName
			_, flag := s3.UploadFileS3(filePath, s3Path)
			if !flag {
				log.Println("unable to upload file to s3")
				panic("Unable to upload file to s3")
			}

			err = exportlogs.Update(exportlogs.StatusCompleted, "", s3Path, exportID)
			if err != nil {
				log.Errorln(err)
				panic(err)
			}
		}()

		var resData = map[string]interface{}{
			"exportID": exportID,
			"status":   "Export in progress",
		}
		ctx := context.WithValue(r.Context(), "resData", resData)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func GenerateSignedURLForExportsCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		attributes := r.Context().Value("attributes").(map[string]interface{})
		exportID := attributes["exportID"].(string)

		export, err := exportlogs.Get(exportID)
		if err != nil {
			log.Error(err)
			panic(err)
		}
		if export.Status == exportlogs.StatusProcessing || export.DownloadLink == "" {
			panic("Export is still processing")
		}

		signedS3URL := s3.GetPresignedURLS3(export.DownloadLink, 300)

		var resData = map[string]interface{}{
			"signedS3URL": signedS3URL,
		}
		ctx := context.WithValue(r.Context(), "resData", resData)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// RazorpayENACHRedirectCont return HTML for redirecting to Razorpay Web
func RazorpayENACHRedirectCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		attributes := r.Context().Value("attributes").(map[string]string)
		loanApplicationID := attributes["loanApplicationID"]

		enachThirdpartyData, err := enachthirdparty.GetENACHDetailsFromLoanID(loanApplicationID)
		if err != nil {
			logger.WithRequest(r).Errorln(err)
			panic(err)
		}

		lenderID, err := loanapplication.GetLender(loanApplicationID)
		if err != nil {
			logger.WithRequest(r).Errorln(err)
			panic(err)
		}

		var response = map[string]string{
			"key":         conf.GetRazorpayCreds(lenderID)["key"],
			"customerID":  enachThirdpartyData.ReferenceID,
			"orderID":     enachThirdpartyData.MandateID,
			"callbackURL": fmt.Sprintf("%s/v1/services/razorpay/callback", conf.BaseURL),
			// TODO: check mapping
			"recurring":         "1",
			"loanApplicationID": loanApplicationID,
		}

		ctx := context.WithValue(r.Context(), "resData", response)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// PayURedirectionCont return HTML for redirecting to Razorpay Web
func PayURedirectionCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		attributes := r.Context().Value("attributes").(map[string]string)
		txnID := attributes["txnID"]

		lenderPayment, err := lenderpayments.GetByTxnID(nil, txnID)
		if err != nil {
			logger.WithRequest(r).Errorln(err)
			panic(err)
		}

		loanObj, err := loanapplication.Get(r.Context(), lenderPayment.LoanApplicationID)
		if err != nil {
			logger.WithRequest(r).Error(err)
			panic(err)
		}

		signalName := "wait_for_payment_status"

		currentModule, err := workflowutils.GetCurrentModule(loanObj.UserID, loanObj.SourceEntityID, "")
		if err != nil {
			logger.WithRequest(r).Error(err)
			panic(err)
		}

		wf, err := userworkflows.Get(loanObj.UserID, currentModule.Module.ModuleName)
		if err != nil {
			err = fmt.Errorf("error getting workflow for userID: %s, moduleName: %s, error: %s", loanObj.UserID, wf.ModuleName, err.Error())
			logger.WithRequest(r).Errorln(err)
			panic(err)
		}

		if wf.IsRunning() {
			err = temporalclient.Client.SignalWorkflow(context.Background(), wf.WorkflowID, wf.RunID, signalName, nil)
			if err != nil {
				logger.WithUser(loanObj.UserID).Errorln(err)
				err = fmt.Errorf("unable to send signal for user %s, err : %s", loanObj.UserID, err)
				errorHandler.ReportToSentryWithFields(map[string]interface{}{
					"userID": loanObj.UserID,
				}, err)
			}
			go temporalsignallogging.Insert(signalName, loanObj.UserID, wf.WorkflowID, nil)
		}

		var response = map[string]string{
			"sourceEntityID": loanObj.SourceEntityID,
		}

		ctx := context.WithValue(r.Context(), "resData", response)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// FibeRedirectCont : This is the Redirection controller called by fibe when it is redirected back to our screen.
func FibeRedirectCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		queryParams, err := json.Marshal(r.URL.Query())
		if err != nil {
			logger.WithRequest(r).Error(err)
			panic(err)
		}

		// Log the webhook in db.
		webhookID := general.GetUUID()
		err = webhooklogs.Create(nil, webhookID, constants.FibeWebhookRedirection, string(queryParams))
		if err != nil {
			logger.WithRequest(r).Error(err)
			panic(err)
		}

		var reqObj fibe.FibeWebhookStructEncrypted
		err = json.Unmarshal(queryParams, &reqObj)
		if err != nil {
			logger.WithRequest(r).Error(err)
			panic(err)
		}

		var userID string
		response := map[string]string{}

		// Fibe gives request params only when at the disbursal. Before that whenever it redirects, nothing should be done.
		if len(reqObj.LoanDetails) == 0 {

			response["source_redirect"] = "true"

			ctx := context.WithValue(r.Context(), "resData", response)
			next.ServeHTTP(w, r.WithContext(ctx))
			return
		}

		secretkey, err := strconv.Atoi(conf.GetFibeSecretKey())
		if err != nil {
			logger.WithRequest(r).Error(err)
			panic(err)

		}

		reqStr, err := fibe.AESDecrypt(reqObj.LoanDetails[0], int64(secretkey))
		if err != nil {
			logger.WithRequest(r).Error(err)
			panic(err)
		}

		var reqObj2 fibe.FibeWebhookStruct

		err = json.Unmarshal([]byte(strings.TrimSpace(reqStr)), &reqObj2)
		if err != nil {
			logger.WithRequest(r).Error(err)
			panic(err)
		}

		lenderID := constants.FibeID
		lenderVariables := lendervariables.LenderVariablesStruct{}

		// Fibe will communicate with the reference id for a users, we need to get the user_id from reference id to identify the user in our db.
		query := `SELECT user_id, lender_id, 
					coalesce(reference_id, '') as reference_id 
		FROM lender_variables WHERE reference_id = $1 and lender_id = $2`
		err = database.Get(&lenderVariables, query, reqObj2.EsRefID, lenderID)
		if err != nil {
			logger.WithRequest(r).Error(err)
			panic(err)
		}

		userID = lenderVariables.UserID

		// Update the webhook with userID
		go func() {
			defer errorHandler.RecoveryNoResponse()
			err = webhooklogs.Update(nil, userID, webhookID)
			if err != nil {
				logger.WithRequest(r).Errorln(err)
				panic(err)
			}

		}()

		// Get loan details from user
		loandetails, err := loanapplication.GetLatestByUser(userID)
		if err != nil {
			logger.WithRequest(r).Error(err)
			panic(err)
		}

		switch strings.ToUpper(reqObj2.LoanStatus) {

		// Complete the redirection module, if disbursal is pending.
		case constants.FibeLoanStatusPending:

			if loandetails.Status != constants.LoanStatusDisbursed {
				err = usermodulemapping.Create(nil, userID, userID, usermodulemapping.Redirection, constants.UserModuleStatusCompleted, loandetails.ID.String())
				if err != nil {
					logger.WithRequest(r).Errorln(err)
					panic(err)
				}
			}

		// If active, loan is disbursed to users.
		case constants.FibeLoanStatusActive:

			if loandetails.Status != constants.LoanStatusDisbursed {
				// Call Fibe's loanDetails API to get disbursal date.
				fibeLoanDetails, err := lenderFibe.GetLoanDetails(context.Background(), userID, loandetails.SourceEntityID)
				if err != nil {
					logger.WithUser(userID).Error(err)
					rejectionReason := lisautil.ProcessRejectionReason(err, "lisa_get_loan_details_fatal:http_status")
					errorHandler.CustomError(w, http.StatusConflict, rejectionReason)
					return
				}

				err = lenderFibe.HandleDisbursedCase(userID, loandetails.SourceEntityID, loandetails.ID.String(), reqObj2.LoanAccountNumber, loandetails.LoanApplicationNo, reqObj2.LoanAmount, fibeLoanDetails.DisbursalDate, fibeLoanDetails.DisbursalAmount, fibeLoanDetails.ProcessingFee)
				if err != nil {
					logger.WithRequest(r).Errorln(err)
					panic(err)
				}

				dateTimeNowString := general.GetTimeStampString()
				lenderName := constants.LenderNamesMap[constants.FibeID]
				description := fmt.Sprintf(`{"lender": "%s"}`, lenderName)
				go activity.ActivityLogger(userID, loandetails.SourceEntityID, userID, constants.EntityTypeSystem, constants.ActivityLoanDisbursed, description, loandetails.ID.String(), dateTimeNowString, false)

			}

		// Closed state for fibe is checked only here for fibe. There is no webhook indicating loan is closed from fibe.
		// It can be only known by this.
		case constants.FibeLoanStatusClosed:
			if loandetails.Status != constants.LoanStatusClosed {
				status := constants.LoanStatusClosed
				err := loanapplication.Update(nil, loanapplication.StructForSet{
					ID:     loandetails.ID.String(),
					Status: &status,
				})
				if err != nil {
					logger.WithUser(userID).Error(err)
					panic(err)
				}

				err = usermodulemapping.Create(nil, userID, userID, usermodulemapping.LenderRepayment, constants.UserModuleStatusCompleted, loandetails.ID.String())
				if err != nil {
					logger.WithUser(userID).Error(err)
					panic(err)
				}

				dateTimeNowString := general.GetTimeStampString()
				lenderName := constants.LenderNamesMap[constants.FibeID]
				description := fmt.Sprintf(`{"lender": "%s"}`, lenderName)
				go activity.ActivityLogger(userID, loandetails.SourceEntityID, userID, constants.EntityTypeSystem, constants.ActivityLoanClosed, description, loandetails.ID.String(), dateTimeNowString, false)
			}
		default:
			logger.WithUser(userID).Error(err)
			errorHandler.ReportToSentry(r, err)
		}

		response["sourceEntityID"] = loandetails.SourceEntityID
		ctx := context.WithValue(r.Context(), "resData", response)

		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func KarzaGSTINHookCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusInternalServerError)

		attributes := r.Context().Value("attributes").(map[string]interface{})
		gstData := attributes["gstData"].(karza.GSTDataHookPayload)
		userID := attributes["userID"].(string)
		gstin := attributes["gstin"].(string)
		statusCode := attributes["statusCode"].(int)

		if exists := userbusinessgst.UserIDGSTINExists(userID, gstin); !exists {
			errorHandler.CustomError(w, http.StatusConflict, "invalid refID")
			return
		}

		pdfLink, _ := gstData.Result["pdfDownloadLink"].(string)
		excelLink, _ := gstData.Result["excelDownloadLink"].(string)

		gstBytesData, err := json.Marshal(gstData)
		if err != nil {
			logger.WithRequest(r).Errorln(err)
			panic(err)
		}

		timestamp := general.GetTimeStampString()

		targetKey := fmt.Sprintf("%s/%s_%s.json", userID, gstin, timestamp)
		pdfReportKey := fmt.Sprintf("%s/%s_%s.pdf", userID, gstin, timestamp)
		excelReportKey := fmt.Sprintf("%s/%s_%s.xlsx", userID, gstin, timestamp)

		_, _ = s3.UploadRawFileS3(strings.NewReader(string(gstBytesData)), targetKey)

		// upload excel and pdf reports
		if !strings.Contains(conf.ENV, conf.ENV_DEV) {
			if pdfLink != "" {
				_ = s3.UploadFromPublicURL(pdfLink, pdfReportKey)
			}
			if excelLink != "" {
				_ = s3.UploadFromPublicURL(excelLink, excelReportKey)
			}
		}

		var (
			gstStatus          string
			gstConnectActivity string
		)

		switch statusCode {
		// 101, 202
		case http.StatusSwitchingProtocols, http.StatusAccepted:
			gstStatus = constants.GSTStatusCompleted
			gstConnectActivity = constants.ActivityGSTConnected
		// 103
		case http.StatusEarlyHints:
			gstStatus = constants.GSTStatusFailed
			gstConnectActivity = constants.ActivityGSTConnectFailed
		default:
			gstStatus = constants.GSTStatusFailed
			gstConnectActivity = constants.ActivityGSTConnectFailed
		}

		ubg := userbusinessgst.Struct{
			UserID:      userID,
			GSTIN:       gstin,
			DataS3Key:   targetKey,
			PDFReport:   pdfReportKey,
			ExcelReport: excelReportKey,
			Status:      gstStatus,
		}
		if err = ubg.UpdateConnectStatus(nil); err != nil {
			logger.WithRequest(r).Errorln(err)
			panic(err)
		}

		sourceEntityID, err := users.GetSourceEntityByID(userID)
		if err != nil {
			logger.WithUser(userID).Errorln(err)
			panic(err)
		}

		activityObj := activity.ActivityEvent{
			UserID:            userID,
			SourceEntityID:    sourceEntityID,
			LoanApplicationID: "",
			EntityType:        constants.EntityTypeSystem,
			EntityRef:         "",
			EventType:         gstConnectActivity,
			Description:       "",
		}
		go activity.RegisterEvent(&activityObj, general.GetTimeStampString())

		var response = map[string]interface{}{
			"message": "success",
		}

		ctx := context.WithValue(r.Context(), "resData", response)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// UpdateLandTDisbursalDetailsCont will update the disbursal status of l and t users
func UpdateLandTDisbursalDetailsCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		key := lockutils.ConstructKey("landt_update_loan_details_", constants.LandTID)
		customErr := lockutils.LockWithTimeout(key, 30*time.Minute)
		if customErr != nil {
			logger.WithRequest(r).Error(customErr)
			switch customErr.HTTPCode {
			case http.StatusConflict:
				errorHandler.CustomError(w, http.StatusTooManyRequests, "an earlier operation is in progress, please wait sometime before requesting another.")
				return
			default:
				panic(customErr)
			}
		}

		landt.UpdateLoanStatus()

		response := map[string]string{
			"message": "ok",
		}

		ctx := context.WithValue(r.Context(), "resData", response)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// UpdateDisbursalDetailsCont will update the disbursal status of cashe users having the status as ESign
func UpdateDisbursalDetailsCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		esignCheck := r.URL.Query().Get("esignCheck")
		var forceDisableESignCheck bool
		if esignCheck == "yes" {
			forceDisableESignCheck = true
		}

		key := lockutils.ConstructKey("update_loan_details_", constants.CasheID)
		customErr := lockutils.LockWithTimeout(key, 15*time.Minute)
		if customErr != nil {
			logger.WithRequest(r).Error(customErr)
			switch customErr.HTTPCode {
			case http.StatusConflict:
				errorHandler.CustomError(w, http.StatusTooManyRequests, "an earlier operation is in progress, please wait sometime before requesting another.")
				return
			default:
				panic(customErr)
			}
		}

		go func() {
			defer errorHandler.RecoveryNoResponse()
			cashe.UpdateLoanDetails(forceDisableESignCheck)

		}()

		response := map[string]string{
			"message": "ok",
		}

		ctx := context.WithValue(r.Context(), "resData", response)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// ExpireABFLUsersCont will expire users and cancels the loan if exists and it is triggered by airflow dag
func ExpireABFLUsersCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		users := r.Context().Value("attributes").([]structs.UserExpiryStruct)
		// entityType := constants.EntityTypeSystem
		// entityRef := ""
		errList := []map[string]interface{}{}
		for _, user := range users {
			err := usersutil.CustomExpireUserLoan(user.UserID, user.LoanApplicationID, user.SourceEntityID, user.LoanStatus, "expired/cancelled due to old bureau")
			if err != nil {
				log.Println(err)
				errList = append(errList, map[string]interface{}{
					"loanApplicationID": user.UserID,
					"reason":            err.Error(),
				})
			}
		}

		resData := map[string]interface{}{
			"message": "expired",
			"error":   errList,
		}

		ctx := context.WithValue(r.Context(), "resData", resData)
		next.ServeHTTP(w, r.WithContext(ctx))

	})
}

func UpdateCustomerStatusCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		attributes := r.Context().Value("attributes").(map[string]string)
		loanApplicationID := attributes["loanApplicationID"]

		lenderID, err := loanapplication.GetLender(loanApplicationID)
		if err != nil {
			logger.WithRequest(r).Errorln(err)
			panic(err)
		}

		if lenderID != constants.CasheID {
			log.Println("this loan application doesn't belong to cashe")
			return
		}

		var wg sync.WaitGroup
		key := lockutils.ConstructKey("update_customer_status_", constants.CasheID)
		customErr := lockutils.LockWithTimeout(key, 10*time.Minute)

		if customErr != nil {
			logger.WithRequest(r).Error(customErr)
			switch customErr.HTTPCode {
			case http.StatusConflict:
				errorHandler.CustomError(w, http.StatusTooManyRequests, "an earlier operation is in progress, please wait sometime before requesting another.")
				return
			default:
				panic(customErr)
			}
		}

		defer lockutils.UnLockWithTimeout(key, &wg, 700*time.Second)
		wg.Add(1)

		go func(wg *sync.WaitGroup) {
			defer errorHandler.RecoveryNoResponse()
			defer wg.Done()
			cashe.UpdateCustomerStatus(loanApplicationID)
		}(&wg)

		response := map[string]string{
			"message": "ok",
		}

		ctx := context.WithValue(r.Context(), "resData", response)
		next.ServeHTTP(w, r.WithContext(ctx))

	})
}

func BankConnectWebhookListener(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		webhookID := r.Context().Value("webhookID").(string)

		var reqObj struct {
			SessionID string `json:"session_id"`
			EventName string `json:"event_name"`
			Accounts  []struct {
				BankName      string `json:"bank_name"`
				AccountID     string `json:"account_id"`
				AccountStatus string `json:"account_status"`
				ErrorCode     any    `json:"error_code"`
				ErrorMessage  any    `json:"error_message"`
			} `json:"accounts"`
		}

		b := r.Context().Value("body").([]byte)
		err := json.Unmarshal(b, &reqObj)
		if err != nil {
			logger.WithRequest(r).Errorln(err)
			panic(err)
		}

		session, err := bankconnect.GetSessionDetails(reqObj.SessionID)
		if err != nil {
			logger.WithRequest(r).Errorln(err)
			panic(err)
		}

		err = webhooklogs.Update(nil, session.UserID, webhookID)
		if err != nil {
			logger.WithRequest(r).Errorln(err)
		}

		if reqObj.EventName == "ENRICHMENT_NOTIFICATION" {
			wf, err := userworkflows.Get(session.UserID, usermodulemapping.BankConnect)
			if err != nil {
				logger.WithUser(session.UserID).Error(err)
				err = fmt.Errorf("error getting workflow information for userID: %s, moduleName: %s", session.UserID, usermodulemapping.BankConnect)
				errorHandler.ReportToSentry(r, err)
			} else {
				signalName := constants.SignalExtractionComplete
				err := temporalclient.Client.SignalWorkflow(context.Background(), wf.WorkflowID, wf.RunID, signalName, nil)
				if err != nil {
					logger.WithUser(session.UserID).Error(err)
					errorHandler.ReportToSentry(r, err)
				}
				temporalsignallogging.Insert(signalName, session.UserID, wf.WorkflowID, nil)
			}
		}

		var response = map[string]interface{}{
			"status": "success",
		}

		ctx := context.WithValue(r.Context(), "resData", response)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// MuthootCreditLineUTRUpdateCont updates net closure amount on users
func MuthootCreditLineUTRUpdateCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		go func() {
			defer errorHandler.RecoveryNoResponse()
			onemuthoot.UpdateCreditLineTxnUTR()
		}()

		response := map[string]string{
			"message": "ok",
		}

		ctx := context.WithValue(r.Context(), "resData", response)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// GSTWaitRevertCont reverts the gst connect status from progress to added
func GSTWaitRevertCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		attributes := r.Context().Value("attributes").(map[string]interface{})
		userID := attributes["userID"].(string)
		gstin := attributes["gstin"].(string)

		userData, err := users.Get(userID)
		if err != nil {
			logger.WithRequest(r).Errorln(err)
			panic(err)
		}

		gstDetails, err := userbusinessgst.GetUserBusinessGST(userID, gstin)
		if err != nil {
			logger.WithRequest(r).Errorln(err)
			panic(err)
		}

		if gstDetails.Status == constants.GSTStatusInProgress && gstDetails.JourneyStatus == constants.GSTJourneyStatusOTPFlow {
			query := `update user_business_gst 
						set gst_metadata = null, journey_status = $1, status = $2, updated_at = now()
						where user_id = $3 and gstin = $4;`
			_, err = database.ExecContext(r.Context(), query, constants.GSTJourneyStatusSelected, constants.GSTStatusAdded, userID, gstin)
			if err != nil {
				logger.WithRequest(r).Errorln(err)
				panic(err)
			}

			if err = usermodulemapping.Create(nil, userID, userID, usermodulemapping.BOOSTER, constants.UserModuleStatusPending, ""); err != nil {
				logger.WithRequest(r).Errorln(err)
				panic(err)
			}

			timestamp := general.GetTimeStampString()
			go activity.RegisterAuxiliaryEvent(&activity.ActivityEvent{
				UserID:         userID,
				SourceEntityID: userData.SourceEntityID,
				EntityRef:      constants.EntityTypeSystem,
				EventType:      constants.ActivityGSTWaitStatusReverted,
			}, timestamp)
		}

		response := map[string]string{
			"message": "success",
		}

		ctx := context.WithValue(r.Context(), "resData", response)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// TempMuthootCreditLineUTRUpdateCont updates net closure amount on all users
func TempMuthootCreditLineUTRUpdateCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		go func() {
			onemuthoot.TempUpdateCreditLineTxnUTR()
		}()

		response := map[string]string{
			"message": "ok",
		}

		ctx := context.WithValue(r.Context(), "resData", response)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// SearchBankNameCont returns the list of matching bank names
func SearchBankNameCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		attributes := r.Context().Value("attributes").(map[string]interface{})
		bankName := attributes["bankName"].(string)

		searchTerm := strings.ToLower(bankName)

		var bankNames []string
		query := `select bank_name 
					from banks
					where lower(bank_name) like $4
					order by (case
						when lower(bank_name) = $1 then 1
						when lower(bank_name) like $2 then 2 
						when lower(bank_name) like $3 then 3 
						when lower(bank_name) like $4 then 4
						else 5
						end
					);`
		err := database.Select(&bankNames, query, searchTerm, searchTerm+"%", "%"+searchTerm, "%"+searchTerm+"%")
		if err != nil {
			logger.WithRequest(r).Errorln(err)
			panic(err)
		}

		ctx := context.WithValue(r.Context(), "resData", bankNames)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// SearchBranchNameCont returns the list of matching bank names
func SearchBranchNameCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		attributes := r.Context().Value("attributes").(map[string]interface{})
		bankName := attributes["bankName"].(string)
		branchName := attributes["branchName"].(string)

		searchTerm := strings.ToLower(branchName)

		type responseStruct struct {
			IFSC       string `db:"ifsc" json:"ifsc"`
			BranchName string `db:"branch" json:"branchName"`
		}

		var response []responseStruct

		query := `select ifsc, branch 
					from ifsc
					where lower(bank_name) = $1
					and lower(branch) like $5
					order by (case
						when lower(branch) = $2 then 1
						when lower(branch) like $3 then 2 
						when lower(branch) like $4 then 3 
						when lower(branch) like $5 then 4
						else 5
						end
					);`
		err := database.Select(&response, query, strings.ToLower(bankName), searchTerm, searchTerm+"%", "%"+searchTerm, "%"+searchTerm+"%")
		if err != nil {
			logger.WithRequest(r).Errorln(err)
			panic(err)
		}

		ctx := context.WithValue(r.Context(), "resData", response)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// SearchIndustryCont ... fetches the industry details from industry search term
func SearchIndustryCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		userObj := r.Context().Value("user").(authentication.UserStruct)
		sourceEntityID := userObj.SourceEntityID
		industryName := r.URL.Query().Get("industryName")

		if industryName == "" {
			panic("industry is empty")
		}

		var dropDownType, dropDownName, lenderID string
		if journey.IsABFLBLSourcing(sourceEntityID) {
			dropDownType = "abfl_bl_business_details"
			dropDownName = "industry_sub_industry"
			lenderID = constants.ABFLID
		} else if journey.IsIIFLBLSourcing(sourceEntityID) {

			dropDownName = "industry_sub_industry"
			dropDownType = "iifl_bl_business_details"
			lenderID = constants.IIFLID
		} else if journey.IsMFLBLSourcing(sourceEntityID) {
			dropDownName = "industry_subindustry"
			dropDownType = "mfl_bl_business_details"
			lenderID = constants.MFLBLID
		} else {
			panic("permission denied")
		}

		searchTerm := strings.ToLower(industryName)

		industryDetails, err := lenderdropdown.GetIndustries(dropDownType, dropDownName, lenderID, searchTerm)
		if err != nil {
			logger.WithRequest(r).Errorln(err)
			panic(err)
		}

		finalArr := []string{}

		for _, industry := range industryDetails {
			if !general.InArr(industry, finalArr) {
				finalArr = append(finalArr, industry)
			}
		}

		ctx := context.WithValue(r.Context(), "resData", finalArr)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// SearchSubIndustryCont ... fetches the subindustries from industry and sub industry search term
func SearchSubIndustryCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		userObj := r.Context().Value("user").(authentication.UserStruct)
		sourceEntityID := userObj.SourceEntityID
		industryName := r.URL.Query().Get("industryName")
		subIndustryName := r.URL.Query().Get("subIndustryName")

		if industryName == "" {
			panic("industry is empty")
		}

		var dropDownType, dropDownName, lenderID string
		if journey.IsABFLBLSourcing(sourceEntityID) {
			dropDownType = "abfl_bl_business_details"
			dropDownName = "industry_sub_industry"
			lenderID = constants.ABFLID
		} else if journey.IsIIFLBLSourcing(sourceEntityID) {
			dropDownName = "industry_sub_industry"
			dropDownType = "iifl_bl_business_details"
			lenderID = constants.IIFLID
		} else if journey.IsMFLBLSourcing(sourceEntityID) {
			dropDownName = "industry_subindustry"
			dropDownType = "mfl_bl_business_details"
			lenderID = constants.MFLBLID
		} else {
			panic("permission denied")
		}

		searchTerm := strings.ToLower(subIndustryName)

		subIndustryDetails, err := lenderdropdown.GetSubIndustriesFromIndustry(dropDownType, dropDownName, lenderID, searchTerm, industryName)
		if err != nil {
			logger.WithRequest(r).Errorln(err)
			panic(err)
		}

		finalArr := []string{}

		for _, subIndustry := range subIndustryDetails {
			if !general.InArr(subIndustry, finalArr) {
				finalArr = append(finalArr, subIndustry)
			}
		}

		ctx := context.WithValue(r.Context(), "resData", finalArr)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func CallExternalAPICont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		userObj := r.Context().Value("user").(authentication.UserStruct)
		userID := userObj.UserID
		sourceEntityID := userObj.SourceEntityID
		attributes := r.Context().Value("attributes").(map[string]interface{})
		endPoint := strings.TrimSpace(attributes["endPoint"].(string))
		data := attributes["data"]
		apiType := strings.TrimSpace(attributes["apiType"].(string))
		method := strings.TrimSpace(attributes["method"].(string))

		var clientID string

		var baseURL string
		if general.InArr(sourceEntityID, []string{constants.TataBNPLID, constants.TataPLID}) {
			baseURL = conf.TDLCreds["baseURL"]
			clientID = conf.TDLCreds["clientID"]

			if !general.InArr(endPoint, []string{"/api/FinBoxPartner/Controller/cta/webhook", "/api/v1/marketplacepartnercontroller/contact-us"}) {
				err := errors.New("invalid API end point")
				errorHandler.CustomError(w, http.StatusBadRequest, err.Error())
				return

			}

			//Not to allow any other source entities
		} else {
			ctx := context.WithValue(r.Context(), "resData", "fail")
			next.ServeHTTP(w, r.WithContext(ctx))
		}

		url := baseURL + endPoint

		btes, err := json.Marshal(data)
		if err != nil {
			logger.Log.WithContext(r.Context()).Errorf("[CallExternalAPI] failed to Marshal data. err: %v, data: %v", err, data)
			errorHandler.CustomError(w, http.StatusBadRequest, err.Error())
			return
		}

		var gobj = map[string]string{
			"url":    url,
			"strReq": "",
			"strRes": "",
			"userID": userID,
			"id":     general.GetUUID(),
		}

		gobj["strReq"] = string(btes)

		token, err := activity.RetrieveOauthTokenWithRedis(userID)
		if err != nil {
			logger.WithUser(userID).Errorln("error retrieving oauth token for tdl: ", err.Error())
			return
		}

		err = retry.CustomRetry(3, 1000*time.Millisecond, func() error {
			payload := strings.NewReader(string(btes))

			serviceName := "tdl-external-api"

			client := tracer.GetTraceableHTTPClient(nil, serviceName)
			req, err := http.NewRequest(method, url, payload)

			if err != nil {
				logger.WithUser(userID).Error(err)
				return err
			}

			if apiType == "tata_events" {
				req.Header.Add("client_id", clientID)
				req.Header.Add("Authorization", token)
				req.Header.Add("posAuthorization", "true")
			}

			_, err = client.Do(req)
			if err != nil {
				logger.WithUser(userID).Error(err)
				return err
			}

			return nil

		})

		if err != nil {
			logger.Log.WithContext(r.Context()).Errorf("[CallExternalAPI] failed to Call Api. err: %v", err)
			errorHandler.CustomError(w, http.StatusInternalServerError, err.Error())
			return
		}

		err = activity.RegisterAuxiliaryEvent(&activity.ActivityEvent{
			UserID:         userID,
			SourceEntityID: sourceEntityID,
			EntityType:     constants.EntityTypeCustomer,
			EntityRef:      userID,
			EventType:      constants.AuxiliaryActivityTriggerExternalAPI,
			Description:    url,
		}, general.GetTimeStampString())
		if err != nil {
			logger.Log.WithContext(r.Context()).Errorf("[CallExternalAPI] failed to Log the activity. err: %v", err)
			errorHandler.CustomError(w, http.StatusInternalServerError, err.Error())
			return
		}

		ctx := context.WithValue(r.Context(), "resData", "success")
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// AbflLoanStatusUpdateCont updates the loan application status
func AbflLoanStatusUpdateCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		attributes := r.Context().Value("attributes").(map[string]interface{})

		loanApplications, ok := attributes["loanApplications"].([]struct {
			LoanApplicationID string `json:"loan_application_id"`
			UserID            string `json:"user_id"`
		})
		if !ok {
			err := errors.New("type assertion failed")
			panic(err)
		}

		var wg sync.WaitGroup
		for _, loanApplication := range loanApplications {

			loanApplicationID := loanApplication.LoanApplicationID
			userID := loanApplication.UserID
			key := lockutils.ConstructKey("abfl_loan_application_", loanApplicationID)
			customErr := lockutils.LockWithTimeout(key, 10*time.Minute)

			if customErr != nil {
				logger.WithRequest(r).Error(customErr)
				switch customErr.HTTPCode {
				case http.StatusConflict:
					errorHandler.CustomError(w, http.StatusTooManyRequests, "an earlier operation is in progress, please wait sometime before requesting another.")
					return
				default:
					panic(customErr)
				}
			}

			defer lockutils.UnLockWithTimeout(key, &wg, 700*time.Second)
			wg.Add(1)

			go func(loanApplicationID, userID string, wg *sync.WaitGroup) {
				defer errorHandler.RecoveryNoResponse()
				defer wg.Done()
				abflpl.UpdateLoanDetails(userID, loanApplicationID)
			}(loanApplicationID, userID, &wg)
		}
		wg.Wait()

		response := map[string]string{
			"message": "ok",
		}

		ctx := context.WithValue(r.Context(), "resData", response)

		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func ActivityEventCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusInternalServerError)

		userObj := r.Context().Value("user").(authentication.UserStruct)
		userID := userObj.UserID
		sourceEntityID := userObj.SourceEntityID

		if sourceEntityID != constants.TataPLID {
			errorHandler.CustomError(w, http.StatusConflict, "permission denied")
			return
		}

		type requestStruct struct {
			EventType string `json:"eventType"`
		}

		var reqObj requestStruct
		decoder := json.NewDecoder(r.Body)
		if err := decoder.Decode(&reqObj); err != nil {
			logger.WithRequest(r).Errorln(err)
			panic(err)
		}

		if !general.InArr(reqObj.EventType, []string{constants.ActivityPanForm2Submitted}) {
			err := errors.New("invalid event type")
			errorHandler.CustomError(w, http.StatusBadRequest, err.Error())
			return

		}

		dateTimeNowString := general.GetTimeStampString()
		go func() {
			activityObj := activity.ActivityEvent{
				UserID:            userID,
				SourceEntityID:    sourceEntityID,
				LoanApplicationID: "",
				EntityType:        constants.EntityTypeCustomer,
				EntityRef:         userID,
				EventType:         reqObj.EventType,
				Description:       "",
			}
			activity.RegisterEvent(&activityObj, dateTimeNowString)
		}()

		ctx := context.WithValue(r.Context(), "resData", "success")
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// TDLPLBringBackToMLOCont handles the user bringing back to mlo after threshold days of inactivity
func TDLPLBringBackToMLOCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		err := tdl.TriggerBringBackUsersJob()
		if err != nil {
			log.Error(err)
			panic(err)
		}

		response := map[string]string{
			"message": "ok",
		}

		ctx := context.WithValue(r.Context(), "resData", response)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// ArchiveTdlUsersCont marks the users as archived
func ArchiveTdlUsersCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		key := lockutils.ConstructKey("tdl_archive_users_", constants.TataPLID)
		customErr := lockutils.LockWithTimeout(key, 30*time.Minute)
		if customErr != nil {
			logger.WithRequest(r).Error(customErr)
			switch customErr.HTTPCode {
			case http.StatusConflict:
				errorHandler.CustomError(w, http.StatusTooManyRequests, "an earlier operation is in progress, please wait sometime before requesting another.")
				return
			default:
				panic(customErr)
			}
		}

		tdl.ArchiveUsers()

		response := map[string]string{
			"message": "ok",
		}

		ctx := context.WithValue(r.Context(), "resData", response)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// PFLSuperMoneyRedirectCont : This is the Redirection controller called by supermoney when it is redirected back to our screen.
func PFLSuperMoneyRedirectCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		// Extract query parameters
		queryParams := r.URL.Query()

		// Log the webhook in the database
		webhookID := general.GetUUID()
		if err := webhooklogs.Create(nil, webhookID, "pfl-supermoney-redirect", queryParams.Encode()); err != nil {
			logger.WithRequest(r).Error(err)
			panic(err)
		}

		var reqObj struct {
			Status       string `json:"status"`
			RedirectType string `json:"redirectType"`
			CustomerID   string `json:"customerID"`
		}

		// Parse query parameters into reqObj
		if err := mapstructure.Decode(queryParams, &reqObj); err != nil {
			logger.WithRequest(r).Error(err)
			panic(err)
		}

		// Initialize the response map
		response := map[string]string{}

		// If RedirectType is empty, handle early return
		if reqObj.RedirectType == "" {
			response["source_redirect"] = "true"
			ctx := context.WithValue(r.Context(), "resData", response)
			next.ServeHTTP(w, r.WithContext(ctx))
			return
		}

		// Retrieve the user based on CustomerID
		users, err := users.GetUserFromUniqueID(reqObj.CustomerID, constants.SuperMoneyID)
		if err != nil {
			logger.WithRequest(r).Error(err)
			panic(err)
		}

		// Update the webhook with userID asynchronously
		go func() {
			defer errorHandler.RecoveryNoResponse()
			if err := webhooklogs.Update(nil, users.ID, webhookID); err != nil {
				logger.WithRequest(r).Errorln(err)
			}
		}()

		// Set sourceEntityID in the response and pass it along in the context
		response["sourceEntityID"] = users.SourceEntityID
		ctx := context.WithValue(r.Context(), "resData", response)

		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func InsuranceGetStatusCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusInternalServerError)

		type dbStruct struct {
			UserID            string `db:"user_id"`
			LoanApplicationID string `db:"loan_application_id"`
			InsuranceID       string `db:"insurance_id"`
			ReferenceNo       string `db:"reference_no"`
		}

		query := `select insurance.loan_application_id, insurance.reference_no, insurance_id, user_id from insurance join loan_application on insurance.loan_application_id = loan_application.loan_application_id where insurance_type = $1 and insurance.status = $2 and (insurance_policy_url is null or insurance_policy_url = '')`

		var dbObj []dbStruct

		err := database.Select(&dbObj, query, constants.InsuranceTypeLifeInsurance, constants.InsuranceStatusPaid)
		if err != nil {
			logger.WithRequest(r).Errorln(err)
			panic(err)
		}

		for _, insurance := range dbObj {
			coi, err := riscovery.RiscoveryStatus(insurance.UserID, constants.ABFLID, insurance.LoanApplicationID, insurance.InsuranceID, octopus.LifeInsurance, insurance.ReferenceNo)
			if err != nil {
				logger.WithRequest(r).Errorln(err)
				panic(err)
			}

			userObj, err := users.Get(insurance.UserID)
			if err != nil {
				logger.WithRequest(r).Errorln(err)
				panic(err)
			}

			loanObj, err := loanapplication.Get(context.Background(), insurance.LoanApplicationID)
			if err != nil {
				logger.WithRequest(r).Errorln(err)
				panic(err)
			}

			filePath := fmt.Sprintf("%s_life_insurance.pdf", loanObj.LoanApplicationNo)

			attachments := []emaillib.EmailAttachment{}
			attachments = append(attachments, emaillib.EmailAttachment{
				Path:     coi,
				FileName: filePath,
				Type:     "file",
			})

			emailData := map[string]interface{}{
				"Name":              userObj.Name,
				"LoanAccountNumber": loanObj.LoanApplicationNo,
			}

			htmlBody := general.GetStringFromTemplate(emaillib.LifeInsuranceHTMLABFL, emailData)
			emaillib.SendMailFrom(emaillib.ABFLFromEmail, emaillib.ABFLFromName, []string{userObj.Email}, []string{userObj.Name}, "Test mail", htmlBody, attachments, false)
		}

		ctx := context.WithValue(r.Context(), "resData", "success")
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func BulkInsuranceGetStatusCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusInternalServerError)

		attributes := r.Context().Value("attributes").(map[string]interface{})
		usersList := attributes["usersList"].([]structs.UserInsurance)

		for _, user := range usersList {

			attachments := []emaillib.EmailAttachment{}

			for _, insuranceObj := range user.InsuranceData {
				if insuranceObj.InsuranceVendorID != constants.RiscoveryID {
					continue
				}

				coi, err := riscovery.RiscoveryStatus(user.UserID, user.LenderID, user.LoanApplicationID, insuranceObj.InsuranceID, insuranceObj.InsuranceType, insuranceObj.ReferenceNo)
				if err != nil {
					logger.WithRequest(r).Errorln(err)
					continue
				}

				// validate url
				if _, err = url.Parse(coi); err != nil {
					logger.WithRequest(r).Errorln(err)
					continue
				}

				filePath := fmt.Sprintf("%s_%s.pdf", user.LoanApplicationNo, insuranceObj.InsuranceType)
				attachments = append(attachments, emaillib.EmailAttachment{
					Path:     coi,
					FileName: filePath,
					Type:     "file",
				})
			}

			if len(attachments) > 0 {
				var fromEmail, fromName, emailTemplate, emailSubject string

				emailData := map[string]interface{}{
					"Name":              user.Name,
					"LoanAccountNumber": user.LoanApplicationNo,
				}

				switch user.LenderID {
				case constants.ABFLID, constants.ABFLPLID:
					fromEmail = emaillib.ABFLFromEmail
					fromName = emaillib.ABFLFromName
					emailSubject, _ = emaillib.EmailSubjectKeyMap[emaillib.InsuranceStatusEmailSubjectABFL]
					emailTemplate, _ = emaillib.EmailBodyKeyMap[emaillib.InsuranceStatusEmailBodyABFL]
				}

				htmlBody := general.GetStringFromTemplate(emailTemplate, emailData)
				emaillib.SendMailFrom(fromEmail, fromName, []string{user.EmailID}, []string{user.Name}, emailSubject, htmlBody, attachments, false)
			}
		}

		ctx := context.WithValue(r.Context(), "resData", "success")
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// GetDownloadableDocumentsCont is a middleware function that handles the retrieval of a presigned URL for downloading documents.
// The function retrieves the downloadable ID from the request context and uses it to get the presigned URL from the downloadables package.
// It redirects the client to the presigned URL.
func GetDownloadableDocumentsCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		attributes := r.Context().Value("attributes").(map[string]interface{})
		downloadableID := attributes["downloadableID"].(string)
		presignedURL, err := downloadables.GetPresignedURL(r.Context(), downloadableID)
		if err != nil {
			logger.WithRequest(r).Error(err)
			if err.Error() == constants.ErrStringDownloadLinkExpired {
				errorHandler.CustomError(w, constants.ErrStringToStatusCodeMapping[constants.ErrStringDownloadLinkExpired], constants.ErrStringDownloadLinkExpired)
				return
			}
			panic(err)
		}

		http.Redirect(w, r, presignedURL, http.StatusFound)
	})
}

func SearchLenderDropDownCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		userObj := r.Context().Value("user").(authentication.UserStruct)
		attributes := r.Context().Value("attributes").(map[string]interface{})

		lenderDropDownSearchInput := attributes["lenderDropdownSearchInput"].(lenderdropdown.LenderDropDownSearch)

		if journey.IsABFLBLSourcing(userObj.SourceEntityID) {
			lenderDropDownSearchInput.LenderID = constants.ABFLID
		}
		if journey.IsMFLBLSourcing(userObj.SourceEntityID) {
			lenderDropDownSearchInput.LenderID = constants.MFLBLID
		}

		result, err := lenderdropdown.GetValueFromQueryFilters(lenderDropDownSearchInput)
		if err != nil {
			logger.WithRequest(r).Errorln(err)
			errorHandler.CustomError(w, http.StatusInternalServerError, "something went wrong")
			return
		}

		// TODO: should be optimized
		var searchResult any
		if lenderDropDownSearchInput.ResultFormat == "json" && len(result) == 1 {
			searchResult = result[0]
		} else {
			searchResult = result
		}

		ctx := context.WithValue(r.Context(), "resData", searchResult)
		next.ServeHTTP(w, r.WithContext(ctx))

	})

}

// UpdateCasheDisbursalDetailsCont will update the disbursal status of cashe users
func UpdateCasheDisbursalDetailsCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		key := lockutils.ConstructKey("cashe_update_loan_details_", constants.CasheMCID)
		customErr := lockutils.LockWithTimeout(key, 30*time.Minute)
		if customErr != nil {
			logger.WithRequest(r).Error(customErr)
			switch customErr.HTTPCode {
			case http.StatusConflict:
				errorHandler.CustomError(w, http.StatusTooManyRequests, "an earlier operation is in progress, please wait sometime before requesting another.")
				return
			default:
				panic(customErr)
			}
		}
		cashemc.UpdateLoanStatus()

		response := map[string]string{
			"message": "ok",
		}

		ctx := context.WithValue(r.Context(), "resData", response)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func IndifiLoansStatusUpdateCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		indifi.UpdateLoanStatus()

		response := map[string]string{
			"message": "ok",
		}

		ctx := context.WithValue(r.Context(), "resData", response)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func FlexiLoansStatusUpdateCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		// TODO: migrate entire go-routine to background workers, prepare list of loans in sync and push flexi status call for each loan as a task
		go func() {
			defer errorHandler.RecoveryNoResponse()

			loans, err := loanapplication.GetByLoanTypeAndLender(constants.FlexiLoansID, constants.LoanTypeBusinessLoan)
			if err != nil {
				log.Println(err)
				return
			}
			for idx, loan := range loans {
				loanApplicationID := loan.ID.String()
				if general.InArr(loan.Status, []int{constants.LoanStatusCancelled, constants.LoanStatusLoanRejected, constants.LoanStatusClosed}) {
					continue
				}
				res, err := lenderservice.ApplicationStatus(context.Background(), &lenderservice.ApplicationStatusReq{
					ApplicationReq: lenderservice.ApplicationReq{
						UserID:         loan.UserID,
						LenderID:       constants.FlexiLoansID,
						SourceEntityID: loan.SourceEntityID,
					},
					ApplicationNo: loan.LoanApplicationNo,
				}, lenderservice.ApplicationStatusResource)
				if err != nil {
					logger.WithUser(loan.UserID).Error(err)
					continue
				}
				switch res.ApplicationStatus {
				case constants.FlexiLoansStatusRejected:
					rejectReason := "rejected by lender: " + constants.LenderNamesMap[constants.FlexiLoansID]
					if len(res.RejectionReasons) > 0 {
						rejectReason += " due to " + res.RejectionReasons[0]
					}
					loanStatus := constants.LoanStatusLoanRejected
					err = loanapplication.Update(nil, loanapplication.StructForSet{
						ID:     loanApplicationID,
						Status: &loanStatus,
					})
					if err != nil {
						logger.WithUser(loan.UserID).Println(err)
						continue
					}
					dateTimeNowString := general.GetTimeStampString()
					activityObj := activity.ActivityEvent{
						UserID:            loan.UserID,
						SourceEntityID:    loan.SourceEntityID,
						LoanApplicationID: loanApplicationID,
						EntityType:        constants.EntityTypeSystem,
						EntityRef:         "",
						EventType:         constants.ActivityLoanRejected,
						Description:       rejectReason,
					}
					activity.RegisterEvent(&activityObj, dateTimeNowString)
				case constants.FlexiLoansStatusDisbursed:
					loanStatus := constants.LoanStatusDisbursed
					err = loanapplication.Update(nil, loanapplication.StructForSet{
						ID:            loanApplicationID,
						Status:        &loanStatus,
						DisbursedDate: time.Now().Format(constants.DateFormat),
					})
					if err != nil {
						logger.WithUser(loan.UserID).Println(err)
						continue
					}

					if loan.SourceEntityID == constants.ABFLMarketplaceID {
						_ = usermodulemapping.Create(nil, loan.UserID, loan.UserID, usermodulemapping.Redirection, constants.UserModuleStatusCompleted, loanApplicationID)
					}

					dateTimeNowString := general.GetTimeStampString()
					activityObj := activity.ActivityEvent{
						UserID:            loan.UserID,
						SourceEntityID:    loan.SourceEntityID,
						LoanApplicationID: loanApplicationID,
						EntityType:        constants.EntityTypeSystem,
						EntityRef:         "",
						EventType:         constants.ActivityLoanDisbursed,
						Description:       fmt.Sprintf("Lender: %s", constants.LenderNamesMap[constants.FlexiLoansID]),
					}
					activity.RegisterEvent(&activityObj, dateTimeNowString)
				default:
					fbxApplicationDetails := constants.FlexiStatusToFBxStatusMapping[res.ApplicationStatus]
					if fbxApplicationDetails.Status == 0 {
						err = fmt.Errorf("unexpected status %s received from flexi", res.ApplicationStatus)
						logger.WithLoanApplication(loanApplicationID).Warnln(err)
						continue
					}
					loanStatus := fbxApplicationDetails.Status
					var subStatusPtr *int
					if fbxApplicationDetails.SubStatus != 0 {
						subStatusPtr = &fbxApplicationDetails.SubStatus
					}
					if loanStatus != loan.Status || (fbxApplicationDetails.SubStatus != 0 && fbxApplicationDetails.SubStatus != loan.KYCStatus) {
						err = loanapplication.Update(nil, loanapplication.StructForSet{
							ID:        loanApplicationID,
							Status:    &loanStatus,
							KYCStatus: subStatusPtr,
						})
						if err != nil {
							logger.WithUser(loan.UserID).Errorln(err)
							continue
						}
						dateTimeNowString := general.GetTimeStampString()
						activityObj := activity.ActivityEvent{
							UserID:            loan.UserID,
							SourceEntityID:    loan.SourceEntityID,
							LoanApplicationID: loanApplicationID,
							EntityType:        constants.EntityTypeSystem,
							EntityRef:         "",
							EventType:         fbxApplicationDetails.EventName,
							Description:       fmt.Sprintf("Lender: %s", constants.LenderNamesMap[constants.FlexiLoansID]),
						}
						activity.RegisterEvent(&activityObj, dateTimeNowString)
					} else {
						logger.WithLoanApplication(loan.ID.String()).Infoln("no movement in status")
					}
				}
				if ((idx + 1) % 10) == 0 {
					time.Sleep(2 * time.Second)
				}
			}
		}()

		response := map[string]string{
			"message": "ok",
		}

		ctx := context.WithValue(r.Context(), "resData", response)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func BulkInitiateNewApplicationCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		attributes := r.Context().Value("attributes").(map[string]interface{})
		userIDs := attributes["userIDs"].([]string)

		type tempStruct struct {
			UserID      string `json:"userID"`
			ErrorString string `json:"errorString,omitempty"`
		}
		type result struct {
			OverallSuccess   bool         `json:"overallSuccess"`
			SuccessFullCases []tempStruct `json:"successfullCases"`
			FailedCases      []tempStruct `json:"failedCases"`
		}

		maxConcurrency := 5
		sem := make(chan struct{}, maxConcurrency)

		var wg sync.WaitGroup
		var mu sync.Mutex

		var res result

		for _, userID := range userIDs {
			sem <- struct{}{}
			wg.Add(1)
			go func(userID string) {
				defer errorHandler.RecoveryNoResponse()
				defer func() { <-sem }()
				defer wg.Done()

				user, err := users.Get(userID)
				if err != nil {
					logger.WithUser(userID).Errorln(err)
					mu.Lock()
					res.FailedCases = append(res.FailedCases, tempStruct{
						UserID:      userID,
						ErrorString: err.Error(),
					})
					mu.Unlock()
					return
				}

				if journey.DoNotAllowInitiateNewApplication(user.SourceEntityID, constants.EntityTypeCustomer) {

					mu.Lock()
					res.FailedCases = append(res.FailedCases, tempStruct{
						UserID:      userID,
						ErrorString: "user not allowed to apply new loan",
					})
					mu.Unlock()

					return
				}

				errStr, err := commonutils.InitiateNewApplication(r.Context(), user.ID, user.SourceEntityID, constants.EntityTypeCustomer, user.ID, "", authentication.UserStruct{
					UserID:          user.ID,
					Name:            user.Name,
					Email:           user.Email,
					Mobile:          user.Mobile,
					SourceEntityID:  user.SourceEntityID,
					Status:          *user.Status,
					UniqueID:        user.UniqueID,
					PAN:             user.PAN,
					JourneyConfigID: user.JourneyConfigID,
				}, false, false)

				if errStr != "" {
					logger.WithUser(userID).Errorln(errStr)
					mu.Lock()
					res.FailedCases = append(res.FailedCases, tempStruct{
						UserID:      userID,
						ErrorString: errStr,
					})
					mu.Unlock()
					return
				}

				if err != nil {
					logger.WithUser(userID).Errorln(err)
					mu.Lock()
					res.FailedCases = append(res.FailedCases, tempStruct{
						UserID:      userID,
						ErrorString: err.Error(),
					})
					mu.Unlock()
					return
				}

				mu.Lock()
				res.SuccessFullCases = append(res.SuccessFullCases, tempStruct{
					UserID: userID,
				})
				mu.Unlock()
			}(userID)
		}

		wg.Wait()
		res.OverallSuccess = len(res.FailedCases) == 0

		var resData map[string]interface{}

		if err := json.Unmarshal([]byte(general.AnyToJSONString(res)), &resData); err != nil {
			logger.WithRequest(r).Error(err)
			panic(err)
		}

		ctx := context.WithValue(r.Context(), "resData", resData)

		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// ArchiveTdlInactiveUsersCont marks the users as archived which are in inactive state
func ArchiveTdlInactiveUsersCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		key := lockutils.ConstructKey("tdl_archive_inactive_users_", constants.TataPLID)
		customErr := lockutils.LockWithTimeout(key, 30*time.Minute)
		if customErr != nil {
			logger.WithRequest(r).Error(customErr)
			switch customErr.HTTPCode {
			case http.StatusConflict:
				errorHandler.CustomError(w, http.StatusTooManyRequests, "an earlier operation is in progress, please wait sometime before requesting another.")
				return
			default:
				panic(customErr)
			}
		}

		tdl.ArchiveInactiveUsers()

		response := map[string]string{
			"message": "ok",
		}

		ctx := context.WithValue(r.Context(), "resData", response)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func TSMSignalRedirectionCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		user := r.Context().Value("user").(authentication.UserStruct)

		// Mark redirect step as started
		err := tsm.SendSignalWithStatus(r.Context(), user.UserID, usermodulemapping.Redirection, tsm.ModuleStatusPending, 1, tsm.SourceAPIStack, nil, nil)
		if err != nil {
			logger.WithUser(user.UserID).Errorln(err)
			panic(err)
		}

		// Mark redirect step as completed
		err = tsm.SendSignalWithStatus(r.Context(), user.UserID, usermodulemapping.Redirection, tsm.ModuleStatusCompleted, 1, tsm.SourceAPIStack, nil, nil)
		if err != nil {
			logger.WithUser(user.UserID).Errorln(err)
			panic(err)
		}

		resData := map[string]string{
			"message": "ok",
		}

		ctx := context.WithValue(r.Context(), "resData", resData)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func RedirectAxisAppStatusCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)
		logger.WithContext(r.Context()).Debugln("Entering axis callback flow")

		// ToDo: @paras uncomment this before making it live post CUG
		//errJSON := map[string]interface{}{
		//	"message": "Unauthorized!",
		//}

		//if conf.ENV == conf.ENV_PROD {
		//	found := false
		//	for _, ipToMatch := range conf.AxisCallbackIPs {
		//		if strings.Contains(r.RemoteAddr, ipToMatch) {
		//			found = true
		//			break
		//		}
		//	}
		//	if !found {
		//		logger.WithRequest(r).Errorln("request unauthorised. IP:", r.RemoteAddr)
		//		errorHandler.CustomErrorJSON(w, http.StatusUnauthorized, errJSON)
		//		return
		//	}
		//}

		uniqueID := r.URL.Query().Get("customerHash")
		userObj, err := users.GetByUniqueID(uniqueID, constants.TataPLID)
		if err != nil {
			if errors.Is(err, sql.ErrNoRows) {
				logger.WithRequest(r).Warnln("No user found for uniqueID: ", uniqueID, err)
				errorHandler.CustomError(w, http.StatusNotFound, fmt.Sprintf("No user found for uniqueID: %s", uniqueID))
				return
			}
			logger.WithRequest(r).Errorln("Failed to fetch user : ", uniqueID, err)
			errorHandler.CustomError(w, http.StatusNotFound, fmt.Sprintf("Failed to fetch user : %s", uniqueID))
			return
		}

		loanApplicationID, err := loanapplication.GetActiveByLenderAndUser(userObj.ID, constants.AxisBankID)
		if err != nil {
			logger.WithRequest(r).Errorln("Failed to fetch lender : ", uniqueID, err)
			errorHandler.CustomError(w, http.StatusNotFound, fmt.Sprintf("Failed to fetch lender : %s", uniqueID))
			return
		}
		if loanApplicationID == constants.EmptyString {
			logger.WithRequest(r).Errorln("No active loan application for user and lender: ", userObj.ID, constants.AxisBankID)
			errorHandler.CustomError(w, http.StatusNotFound, fmt.Sprintf("No active loan application for user and lender: %s", uniqueID))
			return
		}

		wf, err := userworkflows.Get(userObj.ID, usermodulemapping.LenderRedirection)
		if err != nil {
			log.Errorln("error getting worfklow: ", err)
			errorHandler.CustomError(w, http.StatusInternalServerError, fmt.Sprintf("Error while fetching workflow: %s", err.Error()))
			return
		}

		_, err = temporalutility.SignalWorkflow(context.TODO(), userObj.ID, usermodulemapping.LenderRedirection, constants.SignalContinueWF, "", nil, nil)
		if err != nil {
			logger.WithUser(userObj.ID).Error("Failed to signal wf: ", err)
			errorHandler.CustomError(w, http.StatusInternalServerError, fmt.Sprintf("Failed to signal workflow"))
			return
		}
		go temporalsignallogging.Insert(constants.SignalContinueWF, userObj.ID, wf.WorkflowID, nil)

		// save callback as received
		err = lendervariables.MergeUpdateDynamicVariables(userObj.ID, constants.AxisBankID, map[string]interface{}{
			"axisCallback": "true",
		})
		if err != nil {
			logger.WithUser(userObj.ID).Errorln("error in saving received callback: ", err)
		}

		resData := map[string]string{
			"message":         "Signaled Successfully",
			"source_redirect": "false",
			"sourceEntityID":  userObj.SourceEntityID,
		}

		ctx := context.WithValue(r.Context(), "resData", resData)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func EmuhdraWebhookCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		body, err := io.ReadAll(r.Body)
		defer r.Body.Close()
		if err != nil {
			logger.WithRequest(r).Errorln("error in reading body: ", err)
			errorHandler.CustomError(w, http.StatusInternalServerError, fmt.Sprintf("error in reading body: %s", err.Error()))
			return
		}

		logger.WithRequest(r).Debugln("emudhra webhook body: ", string(body))

		// Insert into webhook logs table
		if err := webhooklogs.Create(nil, general.GetUUID(), "emudhra-esigning-webhook", string(body)); err != nil {
			logger.WithRequest(r).Errorln("error in inserting into webhook logs: ", err)
			errorHandler.CustomError(w, http.StatusInternalServerError, fmt.Sprintf("error in inserting into webhook logs: %s", err.Error()))
			return
		}

		type emudhraWebhookStruct struct {
			DocumentName           string `json:"DocumentName"`
			ReferenceNumber        string `json:"ReferenceNumber"`
			WorkflowID             string `json:"WorkflowID"`
			WorkflowStatus         string `json:"WorkflowStatus"`
			CurrentSignatory       string `json:"Currentsignatory"`
			CurrentSignatoryStatus string `json:"CurrentSignatorystatus"`
			Remarks                string `json:"Remarks"`
			CustomStatus           string `json:"CustomStatus"`
			RequestFrom            string `json:"RequestFrom"`
		}
		type octopusWebhookStruct struct {
			Data                  emudhraWebhookStruct `json:"data" validate:"required"`
			RequestID             string               `json:"requestId" validate:"required"`
			UserProvidedRequestID string               `json:"userProvidedRequestId" validate:"required"`
		}

		var emudhraWebhook octopusWebhookStruct
		if err := json.Unmarshal(body, &emudhraWebhook); err != nil {
			logger.WithRequest(r).Errorln("error in unmarshalling body: ", err)
			errorHandler.ReportToSentryWithFields(map[string]interface{}{
				"body": string(body),
			}, err)
			errorHandler.CustomError(w, http.StatusInternalServerError, constants.APIErrorInternalFailure)
			return
		}

		// 1. Find the esign attempt for the workflow id
		esignAttempt, err := esignattempt.GetDetailsByDocumentID(emudhraWebhook.Data.WorkflowID)
		if err != nil {
			logger.WithRequest(r).Errorln("error in getting esign attempt: ", err)
			errorHandler.ReportToSentryWithFields(map[string]interface{}{
				"body":       string(body),
				"workflowID": emudhraWebhook.Data.WorkflowID,
			}, err)
			errorHandler.CustomError(w, http.StatusInternalServerError, constants.APIErrorInternalFailure)
			return
		}

		loanApplication, err := loanapplication.Get(r.Context(), esignAttempt.LoanApplicationID)
		if err != nil {
			logger.WithRequest(r).Errorln("error in getting loan application: ", err)
			errorHandler.ReportToSentryWithFields(map[string]interface{}{
				"body":              string(body),
				"workflowID":        emudhraWebhook.Data.WorkflowID,
				"esignAttemptID":    esignAttempt.EsignAttemptID,
				"loanApplicationID": esignAttempt.LoanApplicationID,
			}, err)
			errorHandler.CustomError(w, http.StatusInternalServerError, constants.APIErrorInternalFailure)
			return
		}

		userWorkflow, err := userworkflows.GetByModulenameAndStatus(r.Context(), loanApplication.UserID, usermodulemapping.Agreement, []string{constants.TemporalStatusRunning})
		if err != nil {
			logger.WithRequest(r).Errorln("error in getting user workflow: ", err)
			errorHandler.ReportToSentryWithFields(map[string]interface{}{
				"body":              string(body),
				"workflowID":        emudhraWebhook.Data.WorkflowID,
				"esignAttemptID":    esignAttempt.EsignAttemptID,
				"loanApplicationID": esignAttempt.LoanApplicationID,
			}, err)
			errorHandler.CustomError(w, http.StatusInternalServerError, constants.APIErrorInternalFailure)
			return
		}

		// 2. Identify the status of the request, and send necessary signal to the workflow
		switch {
		case emudhraWebhook.Data.WorkflowStatus == "Completed":
			// All the applicants have signed the document
			// Send signal to the workflow
			err = temporalclient.Client.SignalWorkflow(context.Background(), userWorkflow.WorkflowID, userWorkflow.RunID, "esign_completed", nil)
			if err != nil {
				logger.WithRequest(r).Errorln("error in sending signal to the workflow: ", err)
				errorHandler.ReportToSentryWithFields(map[string]interface{}{
					"body":              string(body),
					"workflowID":        emudhraWebhook.Data.WorkflowID,
					"esignAttemptID":    esignAttempt.EsignAttemptID,
					"loanApplicationID": esignAttempt.LoanApplicationID,
				}, err)
				errorHandler.CustomError(w, http.StatusInternalServerError, constants.APIErrorInternalFailure)
				return
			}
		case emudhraWebhook.Data.WorkflowStatus == "Pending" && emudhraWebhook.Data.CurrentSignatoryStatus == "Signed":
			// One of the applicant signed the document

			// Get the applicant info using emailID

			coApplicant, err := users.GetByEmail(emudhraWebhook.Data.CurrentSignatory, loanApplication.SourceEntityID)
			if err != nil {
				logger.WithRequest(r).Errorln("error in getting co applicant: ", err)
				errorHandler.ReportToSentryWithFields(map[string]interface{}{
					"body":       string(body),
					"workflowID": emudhraWebhook.Data.WorkflowID,
				}, err)
			}

			// Send signal to the workflow
			err = temporalclient.Client.SignalWorkflow(context.Background(), userWorkflow.WorkflowID, userWorkflow.RunID, "esign_partial_completed", map[string]interface{}{
				"applicant": map[string]interface{}{
					"id":    coApplicant.ID,
					"name":  coApplicant.Name,
					"email": coApplicant.Email,
				},
			})
			if err != nil {
				logger.WithRequest(r).Errorln("error in sending signal to the workflow: ", err)
				errorHandler.ReportToSentryWithFields(map[string]interface{}{
					"body":               string(body),
					"workflowID":         emudhraWebhook.Data.WorkflowID,
					"userWorkflowID":     userWorkflow.ID,
					"temporalWorkflowID": userWorkflow.WorkflowID,
				}, err)
				errorHandler.CustomError(w, http.StatusInternalServerError, constants.APIErrorInternalFailure)
				return
			}

		case emudhraWebhook.Data.WorkflowStatus == "Declined":
			// One of the applicant declined the document
			// Send signal to the workflow
			err = temporalclient.Client.SignalWorkflow(context.Background(), userWorkflow.WorkflowID, userWorkflow.RunID, "esign_failed", nil)
			if err != nil {
				logger.WithRequest(r).Errorln("error in sending signal to the workflow: ", err)
				errorHandler.ReportToSentryWithFields(map[string]interface{}{
					"body":               string(body),
					"workflowID":         emudhraWebhook.Data.WorkflowID,
					"userWorkflowID":     userWorkflow.ID,
					"temporalWorkflowID": userWorkflow.WorkflowID,
				}, err)
				errorHandler.CustomError(w, http.StatusInternalServerError, constants.APIErrorInternalFailure)
				return
			}
		}

		resData := map[string]string{
			"message": "ok",
		}

		ctx := context.WithValue(r.Context(), "resData", resData)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}
