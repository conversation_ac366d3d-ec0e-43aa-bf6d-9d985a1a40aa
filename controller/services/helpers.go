package services

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"finbox/go-api/conf"
	"finbox/go-api/constants"
	"finbox/go-api/functions/commonutils"
	"finbox/go-api/functions/journey"
	"finbox/go-api/functions/logger"
	"finbox/go-api/functions/organization"
	"finbox/go-api/functions/services/ocr"
	"finbox/go-api/functions/sourceentity"
	"finbox/go-api/infra/s3"
	"finbox/go-api/models/media"
	"finbox/go-api/models/users"
	dsa "finbox/go-api/utils/dsautils"
	"finbox/go-api/utils/general"
	"net/http"
	"os"
	"strconv"
	"time"
)

// this function will create a user and calculate eligibility for the same. Returns userID of createduser and error if any
func createUserAndCalculateEligibility(customerID string, sourceEntityID string, mobile string, remoteAddr string, organizationID string) (userID string, err error) {
	if conf.ENV != conf.ENV_PROD {
		// remove existing users if any for testing only
		query := `UPDATE users set mobile = '6636884008' where mobile = $1`
		_, err := database.Exec(query, mobile)
		if err != nil {
			log.Errorln(err)
			return "", err
		}

		query = `delete from freedom_users where mobile = $1`
		_, err = database.Exec(query, mobile)
		if err != nil {
			log.Errorln(err)
			return "", err
		}
		customerID += strconv.FormatInt(time.Now().Unix(), 10)
	}
	_, _, _, _, _, err = organization.CreateEntity(organizationID, customerID, mobile, "", remoteAddr)
	if err != nil {
		log.Errorln(err)
		return "", err
	}
	userID, _, errMessage, _, err := sourceentity.CreateUser(sourceEntityID, customerID, mobile, "", remoteAddr, constants.LeadSourceOrganic, "", false)
	if err != nil {
		log.Println(err)
		return "", err
	}
	if errMessage != "" && errMessage != constants.ErrUserAlreadyExists {
		log.Println(errMessage)
		return "", errors.New(errMessage)
	}

	// check if user has progressed in the journey
	if errMessage == constants.ErrUserAlreadyExists {
		uObj, err := users.Get(userID)
		if err != nil {
			logger.WithUser(userID).Error(err)
			return "", err
		}

		if uObj.Status == nil {
			err := errors.New("user does not have status")
			logger.WithUser(userID).Error(err)
			return "", err
		}

		if *uObj.Status > constants.UserStatusTokenIssued { // profile updated
			err := errors.New("user has already started the journey")
			logger.WithUser(userID).Error(err)
			return "", err
		}
	}

	var dsaOwnerType string
	switch {
	case journey.IsPlatformDSA(sourceEntityID):
		dsaOwnerType = constants.OwnerTypePlatform
	default:
		dsaOwnerType = constants.OwnerTypeLender
	}

	dsaID, err := dsa.GetDSAID(sourceEntityID, dsaOwnerType)
	if err == sql.ErrNoRows {
		logger.WithUser(userID).Warnf("sourceEntityID: %s is not a dsa, thus we do not tag this user to a dsa", sourceEntityID)
	} else if err != nil {
		logger.WithUser(userID).Errorln(err)
		return "", err
	} else {
		err = dsa.TagUserToDSA(userID, dsaID, "", sourceEntityID)
		if err != nil {
			logger.WithUser(userID).Errorln(err)
			return "", err
		}
	}

	_, _, errMessage, _, err = sourceentity.CalculateEligibility(sourceEntityID, customerID, time.Now(), false)
	if err != nil {
		log.Println(err)
		return "", err
	}
	if errMessage != "" {
		log.Println(errMessage)
		return "", errors.New(errMessage)
	}
	return userID, nil
}

func AddressProofBackFunc(ctx context.Context, path string, documentName string, userID string, mediaID string, sourceEntityID string) error {
	filePath, err := s3.GetLocalFilePath(path)
	if err != nil {
		log.WithContext(ctx).Error(err)
		return err
	}
	defer os.Remove(filePath)
	var addressProofOCRAddressJSON string
	var maskedURL string
	var croppedURL string
	switch documentName {
	case constants.DocumentNameAadhaar:
		addressProofOCRAddressJSON, _, maskedURL, _ = ocr.GetOCRAddressFromAadhaarBack(filePath, userID, mediaID, sourceEntityID)
		if maskedURL != "" {
			_, _ = s3.ReadFromURLAndUploadFileS3(maskedURL, path)
		}
	case constants.DocumentNamePassport:
		addressProofOCRAddressJSON, _, croppedURL, _ = ocr.GetOCRAddressFromPassportBack(filePath, userID, mediaID)
		if croppedURL != "" {
			_, _ = s3.ReadFromURLAndUploadFileS3(croppedURL, path)
		}
	case constants.DocumentNameVoter:
		addressProofOCRAddressJSON, _, _, croppedURL, _ = ocr.GetOCRAddressDOBFromVoterIDBack(filePath, userID, mediaID, sourceEntityID)
		if croppedURL != "" {
			_, _ = s3.ReadFromURLAndUploadFileS3(croppedURL, path)
		}
	}
	if addressProofOCRAddressJSON != "" {
		addressObj, err := commonutils.ParseAddress(addressProofOCRAddressJSON)
		if err != nil {
			log.WithContext(ctx).Error(err)
			return err
		}
		addressObj.Line1 = general.CleanAddressLine(addressObj.Line1)
		addressObj.Line2 = general.CleanAddressLine(addressObj.Line2)
		addressJSON, err := json.Marshal(addressObj)
		if err != nil {
			log.WithContext(ctx).Error(err)
			return err
		}
		query := `update user_loan_details set permanent_address=$1, current_address=$2, updated_at=NOW() where loan_application_id in(
			select loan_application_id from loan_application where user_id=$3 order by created_at desc limit 1) `
		_, err = database.Exec(query, string(addressJSON), string(addressJSON), userID)
		if err != nil {
			log.WithContext(ctx).Error(err)
			return err
		}
	}
	return nil
}

func UploadMediaRequestSanity(r *http.Request, userType string) (media.UploadMediaDataStruct, string) {
	var uploadMediaData media.UploadMediaDataStruct
	// check request now
	err := r.ParseMultipartForm(32 << 20)
	if err != nil {
		logger.WithRequest(r).Error(err)
		return uploadMediaData, "invalid request format"
	}
	type requestStruct struct {
		MediaType  []string `json:"mediaType"`
		UserID     []string `json:"userID"`
		DocumentID []string `json:"documentID"`
		IsBack     []string `json:"isBack"`
		Metadata   []string `json:"metadata"`
		Password   []string `json:"password"`
	}
	bytes, err := json.Marshal(r.Form)
	if err != nil {
		logger.WithRequest(r).Error(err)
		return uploadMediaData, "invalid request format"

	}
	var reqObj requestStruct
	err = json.Unmarshal(bytes, &reqObj)
	if err != nil {
		logger.WithRequest(r).Error(err)
		return uploadMediaData, "invalid request format"
	}
	if len(reqObj.MediaType) < 1 {
		return uploadMediaData, "missing mediaType"
	}
	if len(reqObj.UserID) < 1 && userType != userTypeSDKUser {
		return uploadMediaData, "missing userID"
	}
	uploadMediaData.MediaType = reqObj.MediaType[0]
	if userType != userTypeSDKUser {
		uploadMediaData.UserID = reqObj.UserID[0]
	}
	uploadMediaData.IsBack = false
	if len(reqObj.IsBack) > 0 {
		uploadMediaData.IsBack = reqObj.IsBack[0] == "true"
	}
	if uploadMediaData.MediaType == "Address_Proof" && uploadMediaData.IsBack && len(reqObj.DocumentID) < 1 {
		return uploadMediaData, "missing documentID"
	}
	// check for file type and get file object and extension
	file, extension, httpCode, httpErrMessage := CheckSupportedFormats(r, []string{"pdf", "jpeg", "png", "jpg", "zip"}, []string{"image/jpeg", "image/jpg", "image/png", "application/pdf", "application/zip"})
	if httpCode > 0 {
		logger.WithRequest(r).Error(httpErrMessage)
		return uploadMediaData, httpErrMessage
	}
	uploadMediaData.File = file
	uploadMediaData.Extension = extension
	if len(reqObj.Password) > 0 {
		uploadMediaData.Password = reqObj.Password[0]
	}

	if len(reqObj.DocumentID) > 0 {
		uploadMediaData.DocumentID = reqObj.DocumentID[0]
	}

	return uploadMediaData, ""
}

const UserTypeLenderUser = "lenderUser"
const userTypeDashboardUser = "dashboardUser"
const userTypeSDKUser = "sdkUser"
const UserTypeMasterDashboardUser = "masterDashboardUser"

func AddMediaInS3(data *media.UploadMediaDataStruct) (string, string, string) {
	mediaID := general.GetUUID()
	path := data.UserID + "/" + mediaID + "." + data.Extension
	_, flag := s3.ReadFromMultipartFileAndUploadFileS3(data.File, path)
	if !flag {
		log.Println("unable to save file")
		return mediaID, path, "unable to save file"
	}
	data.MediaID = mediaID
	data.Path = path
	return mediaID, path, ""
}
