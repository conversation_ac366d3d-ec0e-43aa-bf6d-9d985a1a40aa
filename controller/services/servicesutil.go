package services

import (
	"context"
	"database/sql"
	"finbox/go-api/common/datawebhook"
	"finbox/go-api/constants"
	"finbox/go-api/errorHandler"
	"finbox/go-api/functions/apistack/apiresult"
	"finbox/go-api/functions/journey"
	"finbox/go-api/functions/logger"
	"finbox/go-api/functions/services/leegality"
	"finbox/go-api/infra/temporalclient"
	"finbox/go-api/models/datawebhookfailedtriggers"
	"finbox/go-api/models/jobs"
	"finbox/go-api/models/temporalsignallogging"
	"finbox/go-api/models/userapimodulemapping"
	"finbox/go-api/models/usermodulemapping"
	"finbox/go-api/models/userworkflows"
	"fmt"
	"strings"
)

func postESignTask(userID, sourceEntityID string, reqObj leegality.WebhookRequestStruct) (err error) {
	if journey.IsMuthootEDIPartner(sourceEntityID) || journey.IsMFLEMIPartner(sourceEntityID) {

		// find a workflow for this and send a signal to it
		wf, err := userworkflows.Get(userID, usermodulemapping.AgreementSignAsync)
		// if no err found, then send a signal to the workflow

		if err == nil {
			signalName := "leegality_callback"
			data := map[string]interface{}{
				"leegalityCallbackStatus": strings.ToUpper(reqObj.DocumentStatus),
			}
			err = temporalclient.Client.SignalWorkflow(context.Background(), wf.WorkflowID, wf.RunID, signalName, data)
			if err != nil {
				logger.WithUser(userID).Errorln(err)
				if !strings.Contains(err.Error(), constants.TemporalWorkflowErrExecutionCompleted) {
					errorHandler.ReportToSentryWithFields(errorHandler.ErrorFields{
						"userID":     userID,
						"workflowID": wf.WorkflowID,
					}, err)
				}
				return fmt.Errorf("unable to send signal for user %s, err : %s", userID, err)
			}
			go temporalsignallogging.Insert(signalName, userID, wf.WorkflowID, data)
		} else if err == sql.ErrNoRows {
			// mark JOB as completed or failed
			job, err := jobs.GetJobForUser(userID, jobs.AgreementSignAsync)
			if err != nil {
				logger.WithUser(userID).Errorln(err)
				return err
			}
			if strings.ToUpper(reqObj.DocumentStatus) == constants.LeegalityStatusCompleted {
				err = job.Complete()
			} else {
				err = job.Fail("failure response from eSign service")
			}
			if err != nil {
				logger.WithUser(userID).Error(err)
				return err
			}
			// get the current user state
			lastActivity, err := userapimodulemapping.GetLast(userID)
			if err != nil {
				logger.WithUser(userID).Error(err)
				return err
			}
			// mark API stack workflow as completed
			workflowStep := lastActivity.WorkflowStep + 1
			err = userapimodulemapping.Create(userID, userID, userapimodulemapping.ModuleAgreement, userapimodulemapping.StatusCompleted, workflowStep, "")
			if err != nil {
				logger.WithUser(userID).Error(err)
				return err
			}
			// push webhook through temporal
			webhookPayload, err := apiresult.GetJobResult(context.TODO(), job.JobID, sourceEntityID, jobs.AgreementSignAsync)
			if err != nil {
				logger.WithUser(userID).Error(fmt.Errorf("error while getting webhook payload: %w", err))
				datawebhookfailedtriggers.FailedToTriggerWebhooks(context.TODO(), webhookPayload, sourceEntityID, job.JobID, job.UserID)
			} else {
				err = datawebhook.Trigger(context.TODO(), webhookPayload, sourceEntityID, jobs.AgreementSignAsync, job.UserID)
				if err != nil {
					logger.WithUser(userID).Errorln(err, "error while triggering webhook for client")
					datawebhookfailedtriggers.FailedToTriggerWebhooks(context.TODO(), webhookPayload, sourceEntityID, job.JobID, job.UserID)
				}

			}
		} else {
			err = fmt.Errorf("error getting workflow for userID: %s, moduleName: %s, error: %s", userID, wf.ModuleName, err.Error())
			logger.WithUser(userID).Errorln(err)
			return err
		}

	}

	return nil
}

func isCoApplicant(userID string) bool {
	query := `select user_id
			    from multi_user_loan_relations
				where user_id = $1`
	var result string
	params := []interface{}{userID}

	err := database.Get(&result, query, params...)
	if err != nil {
		if err == sql.ErrNoRows {
			return false
		}
		logger.WithUser(userID).Errorln(err)
		return false
	}
	return true
}
