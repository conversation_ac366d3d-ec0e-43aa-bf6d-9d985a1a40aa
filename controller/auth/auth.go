// Package auth constains controllers and functions for customer verification
package auth

import (
	"context"
	"encoding/json"
	"errors"
	"finbox/go-api/constants"
	"finbox/go-api/errorHandler"
	"finbox/go-api/functions/journey"
	"finbox/go-api/functions/logger"
	"finbox/go-api/functions/paylater"
	"finbox/go-api/functions/services/sms"
	"finbox/go-api/functions/sourceentity"
	"finbox/go-api/functions/structs"
	"finbox/go-api/functions/transactions"
	"finbox/go-api/infra/redis"
	"finbox/go-api/models/paylateraccounts"
	sourceentitymodel "finbox/go-api/models/sourceentity"
	"finbox/go-api/models/transactionauth"
	"finbox/go-api/models/users"
	"finbox/go-api/utils/lockutils"
	"net/http"
	"strconv"
	"strings"
)

var log = logger.Log

// ValidateOTP validates an OTP. Handles OTP flow for transactions and authlink.
// TODO: Ideally should be able to handle any type of OTP verification with context
func ValidateOTP(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		attributes := r.Context().Value("attributes").(map[string]string)
		organizationID := r.Context().Value("organizationID").(string)
		orgEnabled := r.Context().Value("orgEnabled").(bool)
		authID := attributes["authID"]
		otp := attributes["otp"]

		// TODO: otpType should be available in a central auth table
		otpType := ""
		ok, err := transactionauth.AuthIDExists(authID)
		if err != nil {
			logger.WithRequest(r).Errorln(err)
			panic(err)
		}
		if ok {
			otpType = constants.OTPTypeTransaction
		} else {
			ok, err = paylateraccounts.AuthIDExists(authID)
			if err != nil {
				logger.WithRequest(r).Errorln(err)
				panic(err)
			}
			if ok {
				otpType = constants.OTPTypePayLaterLinkAccount
			}
		}

		var resData map[string]interface{}
		switch otpType {
		case constants.OTPTypePayLaterLinkAccount:
			resData, err = paylater.ValidateOTP(authID, otp, r.RemoteAddr)
		case constants.OTPTypeTransaction:
			// TODO: improve check here
			if !orgEnabled || organizationID != constants.TataID {
				errorHandler.CustomError(w, http.StatusForbidden, constants.ErrStringUnauthorizedRequest)
				return
			}
			//locking the transaction
			lockKey := authID + "_auth_validation"
			customErr := lockutils.Lock(lockKey)
			if customErr != nil {
				logger.WithRequest(r).Error(customErr)
				switch customErr.HTTPCode {
				case http.StatusConflict:
					panic(customErr.Error())
				default:
					panic(customErr)
				}
			}
			defer lockutils.UnLock(lockKey, nil)

			// TODO: remove hard coded sourceEntityID
			resData, err = transactions.ValidateOTP(constants.TataBNPLID, authID, otp, r.RemoteAddr)

		default:
			logger.WithRequest(r).Errorln("invalid otp type")
			panic("invalid otp type")
		}

		if err != nil {
			logger.WithRequest(r).Error(err)
			if errorCode, exists := constants.ErrStringToStatusCodeMapping[err.Error()]; exists {
				errorHandler.CustomError(w, errorCode, err.Error())
				return
			}
			panic(err)
		}

		if resData == nil {
			err = errors.New("response data is empty")
			logger.WithRequest(r).Error(err)
			panic(err)
		}

		ctx := context.WithValue(r.Context(), "resData", resData)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// ResendOTPCont Handles resend OTP flow for transactions and authlink.
// TODO: Ideally should be able to handle any type of OTP resend with context
func ResendOTPCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		attributes := r.Context().Value("attributes").(map[string]string)
		organizationID := r.Context().Value("organizationID").(string)
		orgEnabled := r.Context().Value("orgEnabled").(bool)
		// sourceEntityID := r.Context().Value("sourceEntityID").(string)
		authID := attributes["authID"]

		// TODO: otpType should be available in a central auth table
		otpType := ""
		ok, err := transactionauth.AuthIDExists(authID)
		if err != nil {
			logger.WithRequest(r).Errorln(err)
			panic(err)
		}
		if ok {
			otpType = constants.OTPTypeTransaction
		} else {
			ok, err = paylateraccounts.AuthIDExists(authID)
			if err != nil {
				logger.WithRequest(r).Errorln(err)
				panic(err)
			}
			if ok {
				otpType = constants.OTPTypePayLaterLinkAccount
			}
		}

		if !orgEnabled || organizationID != constants.TataID {
			errorHandler.ReportToSentry(r, errors.New(constants.ErrStringUnauthorizedRequest))
			errorHandler.CustomError(w, http.StatusForbidden, constants.ErrStringUnauthorizedRequest)
			return
		}

		sourceEntityID := constants.TataBNPLID

		switch otpType {
		case constants.OTPTypePayLaterLinkAccount:
			err = paylater.ResendOTP(authID, sourceEntityID)
		case constants.OTPTypeTransaction:
			if !journey.IsProgramApplicable(sourceEntityID) {
				errorHandler.CustomError(w, http.StatusForbidden, constants.ErrStringUnauthorizedRequest)
				return
			}

			lockKey := authID + "_resend_otp_cont"
			customErr := lockutils.Lock(lockKey)
			if customErr != nil {
				logger.WithRequest(r).Error(customErr)
				switch customErr.HTTPCode {
				case http.StatusConflict:
					panic(customErr.Error())
				default:
					panic(customErr)
				}
			}
			defer lockutils.UnLock(lockKey, nil)

			err = transactions.ResendOTP(sourceEntityID, authID)
		}

		if err != nil {
			logger.WithRequest(r).Error(err)
			if errorCode, exists := constants.ErrStringToStatusCodeMapping[err.Error()]; exists {
				errorHandler.CustomError(w, errorCode, err.Error())
				return
			}
			panic(err)
		}

		resendCount, err := sms.GetOTPResendsLeft(authID, otpType)
		if err != nil {
			logger.WithRequest(r).Errorln(err)
			errorHandler.ReportToSentryWithoutRequest(err)
			panic(err)
		}

		resData := map[string]interface{}{
			"message":     "otp sent",
			"resendsLeft": resendCount,
		}

		ctx := context.WithValue(r.Context(), "resData", resData)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// FetchSessionAuthCont triggers the auth validation based on session
func FetchSessionAuthCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		attributes := r.Context().Value("attributes").(map[string]string)
		sessionID := attributes["sessionID"]

		val, err := redis.Get(r.Context(), sessionID+constants.SessionRedisSuffix)
		if err != nil {
			log.Error(err)
			panic("invalid or expired session")
		}

		type dbStruct struct {
			structs.WebLinkStruct
		}

		var obj dbStruct
		rawIn := json.RawMessage(val)
		bytes, err := rawIn.MarshalJSON()
		if err != nil {
			log.Error(err)
			panic(err)
		}

		err = json.Unmarshal(bytes, &obj)
		if err != nil {
			log.Error(err)
			panic(err)
		}

		if obj.AuthenticationMode == constants.AuthModeMobileOTP {

			sourceEntityID, _, _, err := sourceentitymodel.GetIDAndNameFromClientAPIKey(obj.ClientAPIKey)
			if err != nil {
				log.Error(err)
				panic(err)
			}

			userObj, err := users.GetUserFromUniqueID(obj.UniqueID, sourceEntityID)
			if err != nil {
				log.Error(err)
				panic(err)
			}

			_, errString := sms.SendOTPWithAuthID(sessionID, obj.AuthenticationKey, userObj.ID, "", constants.OTPTypeSessionAuth, sourceEntityID, "", &sms.TataOTPReq{})
			if errString != "" {
				logger.WithUser(userObj.ID).Error(errString)
				panic(errString)
			}
		} else {
			panic("invalid authentication mode")
		}

		resData := map[string]interface{}{
			"message": "ok",
		}

		ctx := context.WithValue(r.Context(), "resData", resData)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// SubmitSessionAuthCont submits the authentication value based on session and authentication mode
func SubmitSessionAuthCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		attributes := r.Context().Value("attributes").(map[string]string)
		sessionID := attributes["sessionID"]
		authValue := attributes["authValue"]

		val, err := redis.Get(r.Context(), sessionID+constants.SessionRedisSuffix)
		if err != nil {
			log.Error(err)
			panic("invalid or expired session ID")
		}

		type dbStruct struct {
			structs.WebLinkStruct
		}

		var obj dbStruct

		rawIn := json.RawMessage(val)
		bytes, err := rawIn.MarshalJSON()
		if err != nil {
			log.Error(err)
			panic(err)
		}

		err = json.Unmarshal(bytes, &obj)
		if err != nil {
			log.Error(err)
			panic(err)
		}

		var verified bool
		var token string
		if obj.AuthenticationMode == constants.AuthModeMobileOTP {
			authValue = strings.TrimSpace(authValue)
			otp, err := strconv.Atoi(authValue)
			if err != nil {
				log.Error(err)
				panic(err)
			}

			sourceEntityID, _, _, err := sourceentitymodel.GetIDAndNameFromClientAPIKey(obj.ClientAPIKey)
			if err != nil {
				log.Error(err)
				panic(err)
			}

			userObj, err := users.GetUserFromUniqueID(obj.UniqueID, sourceEntityID)
			if err != nil {
				log.Error(err)
				panic(err)
			}

			var errString string
			verified, errString = sms.VerifyOTPWithAuthID(r.Context(), sessionID, obj.AuthenticationKey, userObj.ID, constants.OTPTypeSessionAuth, otp, "", "", "", "", "", "")
			if errString != "" {
				logger.WithUser(userObj.ID).Error(errString)
				panic(errString)
			}

			token, _, errString, _, err = sourceentity.GetToken(sourceEntityID, obj.UniqueID, obj.TTL, "", "", "", obj.StagingGroupID)
			if err != nil {
				logger.WithUser(userObj.ID).Error(err)
				panic(err)
			}
			if errString != "" {
				logger.WithUser(userObj.ID).Error(errString)
				panic(errString)
			}
		} else {
			panic("invalid authentication mode")
		}

		resData := map[string]interface{}{
			"verified": verified,
			"token":    token,
		}

		ctx := context.WithValue(r.Context(), "resData", resData)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}
