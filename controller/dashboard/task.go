package dashboard

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"finbox/go-api/authentication"
	"finbox/go-api/authorization/authProviders"
	"finbox/go-api/constants"
	"finbox/go-api/errorHandler"
	"finbox/go-api/functions/activity"
	"finbox/go-api/functions/additionaldocs"
	"finbox/go-api/functions/logger"
	"finbox/go-api/functions/taskmanagement"
	"finbox/go-api/functions/taskmanagement/event"
	"finbox/go-api/functions/taskmanagement/model"
	"finbox/go-api/functions/underwriting"
	"finbox/go-api/infra/db"
	"finbox/go-api/infra/s3"
	"finbox/go-api/internal/repository/psql"
	documentsql "finbox/go-api/internal/repository/psql/document"
	loansql "finbox/go-api/internal/repository/psql/loan"
	mediasql "finbox/go-api/internal/repository/psql/media"
	taskmanagementsql "finbox/go-api/internal/repository/psql/taskmanagement"
	workflowinstancesql "finbox/go-api/internal/repository/psql/workflowinstance"
	dashboardModel "finbox/go-api/models/dashboard"
	mdashboard "finbox/go-api/models/dashboard"
	"finbox/go-api/models/featureflag"
	"finbox/go-api/models/loanapplication"
	"finbox/go-api/models/userjourney"
	"finbox/go-api/thirdparty/redgate"
	"finbox/go-api/utils/general"

	"github.com/jmoiron/sqlx"

	"fmt"
	"net/http"
	"strings"
)

func (dsr *DashboardServiceRepository) GetQuestionnaireCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		ctx := r.Context()
		attributes := ctx.Value("attributes").(map[string]interface{})
		req := attributes["req"].(mdashboard.GetTaskListParam)

		var (
			err      error
			response []mdashboard.GetQuestionnaireResponse
		)

		taskType, err := dsr.TaskManagementDBRepositoryProvider.DBGetTaskTypesByParams(ctx, &taskmanagementsql.DBGetTaskTypesParam{
			Name: &req.Type,
		}, nil)
		if err != nil {
			logger.WithContext(ctx).Errorf("[GetQuestionnaireCont]error in getting task types for loanApplicationID:%s, err:%v", req.LoanApplicationID, err)
			errorHandler.CustomError(w, http.StatusNotFound, constants.TaskNotFound)
			return
		}

		identifierType := mdashboard.LoanApplicationID.String()
		tasks, err := dsr.TaskManagementDBRepositoryProvider.DBListTasksByParams(ctx, &taskmanagementsql.DBGetTasksListParam{
			IdentifierID:   &req.LoanApplicationID,
			IdentifierType: &identifierType,
			TypeID:         &taskType.Id,
		}, nil, nil)
		if err != nil {
			logger.WithContext(ctx).Errorf("[GetQuestionnaireCont]error in getting task for loanApplicationID: %s, err: %v", req.LoanApplicationID, err)
			errorHandler.HandleCustomErrorBeforeSentry(w, err)
			return
		}

		for _, task := range tasks {
			taskType := mdashboard.Blocker
			check, err := dsr.TaskManagementDBRepositoryProvider.DBGetChecksByParams(ctx, &taskmanagementsql.DBGetChecksParam{
				TaskID: &task.Id,
				Type:   &taskType,
			}, nil)
			if err != nil {
				logger.WithContext(ctx).Errorf("[GetQuestionnaireCont]error in getting checks for loanApplicationID:%s, err:%v", req.LoanApplicationID, err)
				errorHandler.CustomError(w, http.StatusNotFound, constants.ChecksNotFound)
				return
			}

			var questionnaire mdashboard.QuestionnaireMetaData
			if err := json.Unmarshal([]byte(task.Metadata), &questionnaire); err != nil {
				logger.WithContext(ctx).Errorf("[GetQuestionnaireCont] error in unmarshal questionnaire for loanApplicationID:%s, err:%v", req.LoanApplicationID, err)
				errorHandler.HandleCustomErrorBeforeSentry(w, err)
				return
			}

			var answer mdashboard.CheckAnswer
			if err := json.Unmarshal([]byte(check.Result), &answer); err != nil {
				logger.WithContext(ctx).Errorf("[GetQuestionnaireCont] error in unmarshal answer for loanApplicationID:%s, err:%v", req.LoanApplicationID, err)
				errorHandler.HandleCustomErrorBeforeSentry(w, err)
				return
			}

			singleSelect := "singleSelect"
			response = append(response, mdashboard.GetQuestionnaireResponse{
				Name:          task.Name,
				TaskID:        task.Id,
				Type:          singleSelect,
				Answer:        answer,
				Questionnaire: questionnaire,
			})
		}

		var resData = map[string]interface{}{
			"tasks": response,
		}
		ctx = context.WithValue(ctx, "resData", resData)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func isTasksResolvedForAddCondition(ctx context.Context, taskTypeName mdashboard.TaskType, identifierType mdashboard.IdentifierType, identifierID string) (bool, error) {
	taskmanagementRepo := taskmanagementsql.NewTaskManagementDBRepository(db.GetDB())
	taskType, err := taskmanagementRepo.DBGetTaskTypesByParams(ctx, &taskmanagementsql.DBGetTaskTypesParam{Name: &taskTypeName}, nil)
	if err != nil {
		return false, err
	}

	statusList, err := taskmanagementRepo.DBListTaskStatusByParams(ctx, &taskmanagementsql.DBGetTaskStatusParam{TaskType: &taskType.Id}, nil, nil)
	if err != nil {
		return false, err
	}

	idType := identifierType.String()
	tasks, err := taskmanagementRepo.DBListTasksByParams(ctx, &taskmanagementsql.DBGetTasksListParam{TypeID: &taskType.Id, IdentifierID: &identifierID, IdentifierType: &idType}, nil, nil)
	if err != nil || len(tasks) == 0 {
		return false, err
	}
	taskTypeStatusMap := map[string]taskmanagementsql.DBGetTaskStatusResponse{}
	for _, status := range statusList {
		taskTypeStatusMap[status.Id] = status
	}
	for _, task := range tasks {
		if task.DeletedAt != "" {
			continue
		}
		status := taskTypeStatusMap[task.StatusId]
		if status.State != mdashboard.Completed && status.State != mdashboard.Cancelled && status.State != mdashboard.Rejected {
			return false, errors.New("task not resolved")
		}
	}

	return true, nil
}

var (
	PL_Sanctions_Credit = "cf8e1416-4fac-4a26-8ca2-5c3db4c70b08"
	PL_Sanctions_RCU    = "32f33cc5-3bbe-41ae-b8b0-8655cc02caa7"
)

func isAddConditionEnabled(ctx context.Context, lenderID, identifierId string, rbacGroupName string) bool {
	identifierType := mdashboard.LoanApplicationID.String()
	switch lenderID {
	case constants.ABFLPLID:
		if !featureflag.Get(lenderID, dashboardModel.FlagPLSanctionCheck) {
			if strings.HasPrefix(rbacGroupName, "Credit") {
				creaditTaskCount, _ := taskmanagementsql.DBGetTasksCountByParams(ctx, &taskmanagementsql.DBGetTasksCountParam{
					IdentifierID:   &identifierId,
					TypeID:         &PL_Sanctions_Credit,
					IdentifierType: &identifierType,
				})
				if creaditTaskCount >= 10 {
					return false
				}
				resolved, err := isTasksResolvedForAddCondition(ctx, "abfl_credit_approval", mdashboard.LoanApplicationID, identifierId)
				if err != nil || !resolved {
					return true
				}
			} else if rbacGroupName == "RCU" {
				rcuTaskCount, _ := taskmanagementsql.DBGetTasksCountByParams(ctx, &taskmanagementsql.DBGetTasksCountParam{
					IdentifierID:   &identifierId,
					TypeID:         &PL_Sanctions_RCU,
					IdentifierType: &identifierType,
				})
				if rcuTaskCount >= 10 {
					return false
				}
				resolved, err := isTasksResolvedForAddCondition(ctx, "abfl_rcu_approval", mdashboard.LoanApplicationID, identifierId)
				if err != nil || !resolved {
					return true
				}
			}
		}
	case constants.ABFLID:
		loanRepo := loansql.NewLoanDBRepository(psql.Database)
		loanApplication, err := loanRepo.DBGetLoanApplicationByParams(ctx, loansql.DBGetLoanApplicationParam{LoanApplicationID: identifierId})
		if err != nil {
			logger.WithContext(ctx).Errorf("[isAddConditionEnabled] error in getting loan application for loanApplicationID: %s, err: %v", identifierId, err)
		}

		if rbacGroupName == "RCU" {
			return false
		}

		isPending, err := isWorkflowPendingOrNotFound(ctx, psql.Database, identifierId)
		fmt.Printf("isPending is %v\n", isPending)
		if err != nil {
			logger.WithContext(ctx).Errorf("[isAddConditionEnabled] Error checking workflow status for loanApplicationID: %s, err: %v", identifierId, err)
			return false
		}
		if !isPending {
			return false
		}

		md := map[string]interface{}{}
		err = json.Unmarshal([]byte(loanApplication.Metadata), &md)
		if err != nil {
			logger.WithContext(ctx).Errorf("[isAddConditionEnabled] error in unmarshal metadata for loanApplicationID: %s, err: %v", identifierId, err)
			return true
		}

		if isSoftApproved, ok := md["softApproved"]; ok {
			if val, ok := isSoftApproved.(mdashboard.SoftApproved); ok {
				return !val.Status
			}
		}
		return true
	}

	return true
}

func GetCreditAndRCUCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		ctx := r.Context()
		attributes := ctx.Value("attributes").(map[string]interface{})
		req := attributes["req"].(mdashboard.GetTaskListParam)

		var (
			resp mdashboard.FetchTaskResponse
		)

		taskmanagementRepo := taskmanagementsql.NewTaskManagementDBRepository(database)
		taskType, err := taskmanagementRepo.DBGetTaskTypesByParams(ctx, &taskmanagementsql.DBGetTaskTypesParam{
			Name: &req.Type,
		}, nil)
		if err != nil {
			logger.WithContext(ctx).Errorf("[GetCreditAndRCUCont] error in getting task type for loanApplicationID: %s, err: %v", req.LoanApplicationID, err)
			errorHandler.HandleCustomErrorBeforeSentry(w, err)
			return
		}

		identifierType := mdashboard.LoanApplicationID.String()
		tasks, err := taskmanagementRepo.DBListTasksByParams(ctx, &taskmanagementsql.DBGetTasksListParam{
			IdentifierID:   &req.LoanApplicationID,
			TypeID:         &taskType.Id,
			IdentifierType: &identifierType,
		}, nil, nil)
		if err != nil {
			logger.WithContext(ctx).Errorf("[GetCreditAndRCUCont]error in getting task for loanApplicationID:%s, err:%v", req.LoanApplicationID, err)
			errorHandler.HandleCustomErrorBeforeSentry(w, err)
			return
		}

		status, err := taskmanagementRepo.DBListTaskStatusByParams(ctx, &taskmanagementsql.DBGetTaskStatusParam{TaskType: &taskType.Id}, nil, nil)
		if err != nil {
			logger.WithContext(ctx).Errorf("[GetCreditAndRCUCont]error in getting task for loanApplicationID:%s, err:%v", req.LoanApplicationID, err)
			errorHandler.HandleCustomErrorBeforeSentry(w, err)
			return
		}
		statusMap := make(map[string]mdashboard.Status)
		for _, s := range status {
			statusMap[s.Id] = mdashboard.Status{
				Id:       s.Id,
				Name:     s.Name,
				Sequence: s.Sequence,
				State:    s.State.String(),
			}
		}

		var taskIds []string

		for _, task := range tasks {
			taskIds = append(taskIds, task.Id)
		}

		workflowInstanceRepo := workflowinstancesql.NewWorkflowInstancesRepository(database)
		var workflowInstances []workflowinstancesql.DBGetWorkflowInstancesResponse
		if len(taskIds) != 0 {
			workflowInstances, err = workflowInstanceRepo.DBListWorkflowInstancesByParams(ctx, &workflowinstancesql.DBListWorkflowInstancesParam{
				IdentifierIds: taskIds,
			}, nil)
			if err != nil {
				logger.WithContext(ctx).Errorf("[GetTaskListCont] err during fetch workflow instances err:%s, loanApplicationID:%v", err, req.LoanApplicationID)
				errorHandler.HandleCustomErrorBeforeSentry(w, err)
				return
			}
		}

		var workflowIndentifierMap = make(map[string]workflowinstancesql.DBGetWorkflowInstancesResponse)

		for _, workflowInstance := range workflowInstances {
			workflowIndentifierMap[workflowInstance.IdentifierID] = workflowInstance
		}

		for _, task := range tasks {
			resp.Tasks = append(resp.Tasks, mdashboard.Task{
				TaskID:               task.Id,
				StatusID:             task.StatusId,
				Metadata:             task.Metadata,
				ApprovedAt:           task.ApprovedAt,
				ApprovedBy:           task.ApprovedBy,
				TypeID:               task.TypeId,
				UpdatedBy:            task.UpdatedBy,
				CreatedAt:            task.CreatedAt,
				CreatedBy:            task.CreatedBy,
				MasterTaskID:         task.MasterTaskId,
				WorkflowCurrentState: workflowIndentifierMap[task.Id].CurrentState,
				WorkflowInstanceID:   workflowIndentifierMap[task.Id].ID,
				StatusName:           statusMap[task.StatusId].Name,
			})
		}
		var resData = map[string]interface{}{
			"tasks": resp,
		}
		ctx = context.WithValue(ctx, "resData", resData)
		next.ServeHTTP(w, r.WithContext(ctx))

	})
}

func UpdateCreditAndRCUCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		ctx := r.Context()
		attributes := ctx.Value("attributes").(map[string]interface{})
		req := attributes["req"].(mdashboard.EventData)
		dashboardUser := ctx.Value("user").(authentication.DashboardUser)

		data, err := json.Marshal(req.AdditionalData)
		if err != nil {
			log.WithContext(ctx).Errorf("[UpdateCreditAndRCUCont] Failed to marshal additional data: %v, error: %v", req, err)
			errorHandler.CustomError(w, http.StatusBadRequest, err.Error())
			return
		}

		taskManagementService := taskmanagement.NewTaskManagementService(event.Factory)
		err = taskManagementService.HandleEvent(ctx, dashboardUser.GetEmail(), req.IdentifierType, req.IdentifierID, model.Event{
			Event:     taskmanagementsql.UpdateTask.String(),
			EventData: data,
		})

		if err != nil {
			log.WithContext(ctx).Errorf("[UpdateCreditAndRCUCont] Failed to handle event: %v, error: %v", req.Event, err)
			errorHandler.CustomError(w, http.StatusInternalServerError, err.Error())
			return
		}

		err = processWaitStatusLoanState(ctx, data, req.IdentifierID, dashboardUser.GetEmail())
		if err != nil {
			log.WithContext(ctx).Errorf("[UpdateCreditAndRCUCont] Failed to update loan state: %v, error: %v", req.Event, err)
			errorHandler.CustomError(w, http.StatusInternalServerError, constants.GenericInternalIssuesMessage)
			return
		}

		resData := map[string]string{
			"msg": "Event handled successfully",
		}
		ctx = context.WithValue(r.Context(), "resData", resData)

		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// processWaitStatusLoanState to update loan wait state and loan status based on task update status
func processWaitStatusLoanState(ctx context.Context, data []byte, relEntityID string, dashboardUserEmail string) error {
	loanApplication, err := loanapplication.Get(ctx, relEntityID)
	if err != nil {
		logger.WithContext(ctx).Errorf("[processWaitStatusLoanState] error getting loan details for err: %v", err)
		return err
	}

	var request event.UpdateTaskServiceData
	err = json.Unmarshal(data, &request)
	if err != nil {
		logger.WithContext(ctx).Errorf("[processWaitStatusLoanState]error in getting task for loanApplicationID:%s, err:%v", relEntityID, err)
		return err
	}

	task, err := taskmanagementsql.DBGetTasksByParams(ctx, &taskmanagementsql.DBGetTasksParam{
		ID: &request.Id,
	}, nil)
	if err != nil {
		logger.WithContext(ctx).Errorf("[processWaitStatusLoanState]error in getting task for loanApplicationID:%s, err:%v", relEntityID, err)
		return err
	}

	taskMasterCredit := constants.MasterTaskCreditApprovalABFL
	taskMasterRCU := constants.MasterTaskRCUApprovalABFL

	//to check task belongs to credit or RCU and update loan status based on task status
	if task.MasterTaskId == taskMasterCredit {
		if request.Task.StatusId != nil && *request.Task.StatusId == constants.CreditApprovalRejectedStatusABFL {
			dateTimeString := general.GetTimeStampString()
			go activity.ActivityLogger(loanApplication.UserID, loanApplication.SourceEntityID, dashboardUserEmail, constants.EntityTypeLenderUser, constants.ActivityCreditWorkflowRejectedTriggered, "manual workflow update", loanApplication.ID.String(), dateTimeString, false)
			err := setWaitStatusAndRejectLoan(ctx, relEntityID, loanApplication.UserID)
			if err != nil {
				logger.WithContext(ctx).Errorf("[processWaitStatusLoanState] error in updating wait state: %v", err)
				return err
			}
			return nil
		}
		if request.Task.StatusId != nil && *request.Task.StatusId == constants.CreditApprovalApprovalStatusABFL {
			dateTimeString := general.GetTimeStampString()
			go activity.ActivityLogger(loanApplication.UserID, loanApplication.SourceEntityID, dashboardUserEmail, constants.EntityTypeLenderUser, constants.ActivityCreditWorkflowApprovedTriggered, "manual workflow update", loanApplication.ID.String(), dateTimeString, false)
			taskRCU, err := taskmanagementsql.DBGetTasksByParams(ctx, &taskmanagementsql.DBGetTasksParam{
				MasterTaskID: &taskMasterRCU,
				IdentifierID: &relEntityID,
			}, nil)
			if err == sql.ErrNoRows {
				logger.WithContext(ctx).Errorf("[processWaitStatusLoanState] no RCU tasks found for loanApplicationID:%s", relEntityID)
				err := setWaitStatusAndApproveLoan(ctx, relEntityID, loanApplication.UserID)
				if err != nil {
					logger.WithContext(ctx).Errorf("[processWaitStatusLoanState] error in updating wait state: %v", err)
					return err
				}
				return nil
			} else if err != nil {
				logger.WithContext(ctx).Errorf("[processWaitStatusLoanState]error in getting task for loanApplicationID:%s, err:%v", relEntityID, err)
				return err
			}

			if taskRCU.StatusId == constants.RCUApprovalApprovalStatusABFL {
				err := setWaitStatusAndApproveLoan(ctx, relEntityID, loanApplication.UserID)
				if err != nil {
					logger.WithContext(ctx).Errorf("[processWaitStatusLoanState] error in updating wait state: %v", err)
					return err
				}
			}
		}
	}

	//to check task belongs to RCU and update loan status based on task status
	if task.MasterTaskId == taskMasterRCU {
		if *request.Task.StatusId == constants.RCUApprovalRejectedStatusABFL {
			dateTimeString := general.GetTimeStampString()
			go activity.ActivityLogger(loanApplication.UserID, loanApplication.SourceEntityID, dashboardUserEmail, constants.EntityTypeLenderUser, constants.ActivityKYCWorkflowRejectedTriggered, "manual workflow update", loanApplication.ID.String(), dateTimeString, false)
			err := setWaitStatusAndRejectLoan(ctx, relEntityID, loanApplication.UserID)
			if err != nil {
				logger.WithContext(ctx).Errorf("[processWaitStatusLoanState] error in updating wait state: %v", err)
				return err
			}
			return nil
		}

		if *request.Task.StatusId == constants.RCUApprovalApprovalStatusABFL {
			dateTimeString := general.GetTimeStampString()
			go activity.ActivityLogger(loanApplication.UserID, loanApplication.SourceEntityID, dashboardUserEmail, constants.EntityTypeLenderUser, constants.ActivityKYCWorkflowApprovedTriggered, "manual workflow update", loanApplication.ID.String(), dateTimeString, false)
			taskCredit, err := taskmanagementsql.DBGetTasksByParams(ctx, &taskmanagementsql.DBGetTasksParam{
				MasterTaskID: &taskMasterCredit,
				IdentifierID: &relEntityID,
			}, nil)

			if err == sql.ErrNoRows {
				logger.WithContext(ctx).Errorf("[processWaitStatusLoanState] no credit tasks found for loanApplicationID:%s", relEntityID)
				err := setWaitStatusAndApproveLoan(ctx, relEntityID, loanApplication.UserID)
				if err != nil {
					logger.WithContext(ctx).Errorf("[processWaitStatusLoanState] error in updating wait state: %v", err)
					return err
				}
				return nil
			} else if err != nil {
				logger.WithContext(ctx).Errorf("[processWaitStatusLoanState]error in getting task for loanApplicationID:%s, err:%v", relEntityID, err)
				return err
			}

			if taskCredit.StatusId == constants.CreditApprovalApprovalStatusABFL {
				err := setWaitStatusAndApproveLoan(ctx, relEntityID, loanApplication.UserID)
				if err != nil {
					logger.WithContext(ctx).Errorf("[processWaitStatusLoanState] error in updating wait state: %v", err)
					return err
				}
			}
		}
	}

	return nil
}

// setWaitStatusAndApproveLoan to approve loan and set wait status to false
func setWaitStatusAndApproveLoan(ctx context.Context, relEntityID string, userID string) error {
	errorString, err := underwriting.ApproveLoan(relEntityID, constants.ABFLPLID, 0, "", "", "")
	if errorString != "" {
		logger.WithContext(ctx).Errorf("[setWaitStatusAndApproveLoan] error in approviong loan with errString: %s", errorString)
		return fmt.Errorf("error in approviong loan err: %v", errorString)
	}
	if err != nil {
		logger.WithContext(ctx).Errorf("[setWaitStatusAndApproveLoan] error in approviong loan with err: %v", err)
		return err
	}

	err = userjourney.SetWaitStatus(nil, userID, userjourney.WaitStatusFalse, "")
	if err != nil {
		logger.WithContext(ctx).Errorf("[setWaitStatusAndApproveLoan] error in setting waitStatus for loanApplicationID:%s, err:%v", relEntityID, err)
		return err
	}

	return nil
}

// setWaitStatusAndRejectLoan to reject loan and set wait status to false
func setWaitStatusAndRejectLoan(ctx context.Context, relEntityID string, userID string) error {
	_, err := underwriting.RejectLoan(relEntityID, constants.ABFLPLID, constants.EntityTypeSystem, "", "rejected due to workflow rejection")
	if err != nil {
		logger.WithContext(ctx).Errorf("[setWaitStatusAndRejectLoan] error in rejecting loan with err: %v", err)
		return err
	}

	err = userjourney.SetWaitStatus(nil, userID, userjourney.WaitStatusFalse, "")
	if err != nil {
		logger.WithContext(ctx).Errorf("[setWaitStatusAndRejectLoan] error in setting waitStatus for loanApplicationID:%s, err:%v", relEntityID, err)
		return err
	}

	return nil
}

func (dsr *DashboardServiceRepository) GetTaskListCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		ctx := r.Context()
		attributes := ctx.Value("attributes").(map[string]interface{})
		req := attributes["req"].(mdashboard.GetSanctionParam)

		var (
			resp              mdashboard.SanctionResponse
			statusIDToNameMap = make(map[string]string)

			dashboard        = r.Context().Value("dashboard").(string)
			userType         = r.Context().Value("user")
			lenderUser       authentication.LenderUserStruct
			sourceEntityID   = attributes["sourceEntityID"].(string)
			masterUser       authentication.MasterDashboardUserStruct
			rbacGroupName    string
			loanparam        loansql.DBGetLoanApplicationParam
			isUserAuthorized = true
		)

		switch v := userType.(type) {
		case authentication.LenderUserStruct:
			lenderUser = v
		case authentication.MasterDashboardUserStruct:
			masterUser = v
		default:
			log.WithContext(ctx).Errorf("[WorkflowActionCont] invalid user")
			errorHandler.CustomError(w, http.StatusInternalServerError, constants.InvalidDashboardUserMessage)
			return
		}

		if dashboard == constants.LenderDashboardRef {
			rbacGroupName = lenderUser.RBACGroupName
		} else {
			rbacGroupName = masterUser.SourceEntityWithGroupMapping[sourceEntityID]
			rbacGroupName = strings.Split(masterUser.SourceEntityWithGroupMapping[sourceEntityID], "#")[1]
		}

		taskTypes, err := dsr.TaskManagementDBRepositoryProvider.DBListTaskTypesByParams(ctx, &taskmanagementsql.DBGetTaskTypesParam{
			NameList: &req.Type,
		}, nil, nil)
		if err != nil {
			logger.WithContext(ctx).Errorf("[GetTaskListCont] error in getting task type for loanApplicationID: %s, err: %v", req.LoanApplicationID, err)
			errorHandler.HandleCustomErrorBeforeSentry(w, err)
			return
		}
		loanparam.LoanApplicationID = req.LoanApplicationID

		loanInfo, err := dsr.LoanDBRepositoryProvider.DBGetLoanApplicationByParams(ctx, loanparam)

		resp.AddConditionEnabled = isAddConditionEnabled(ctx, loanInfo.LenderID, req.LoanApplicationID, rbacGroupName)

		var taskTypeIDs []string
		for _, taskType := range taskTypes {
			taskTypeIDs = append(taskTypeIDs, taskType.Id)
		}

		identifierType := mdashboard.LoanApplicationID.String()

		resp.Count, err = taskmanagementsql.DBGetTasksCountByParams(ctx, &taskmanagementsql.DBGetTasksCountParam{
			IdentifierID:   &req.LoanApplicationID,
			TypeIDs:        &taskTypeIDs,
			IdentifierType: &identifierType,
		})
		if err != nil {
			logger.WithContext(ctx).Errorf("[GetTaskListCont]error in getting task count for loanApplicationID:%s, err:%v", req.LoanApplicationID, err)
			errorHandler.CustomError(w, http.StatusInternalServerError, constants.GenericInternalIssuesMessage)
			return
		}

		tasks, err := dsr.TaskManagementDBRepositoryProvider.DBListTasksByParams(ctx, &taskmanagementsql.DBGetTasksListParam{
			IdentifierID:   &req.LoanApplicationID,
			TypeIDs:        &taskTypeIDs,
			IdentifierType: &identifierType,
		}, nil, &psql.Pagination{
			Limit:  req.Limit,
			Offset: (req.Page - 1) * req.Limit,
		})
		if err != nil {
			logger.WithContext(ctx).Errorf("[GetTaskListCont]error in getting task for loanApplicationID:%s, err:%v", req.LoanApplicationID, err)
			errorHandler.HandleCustomErrorBeforeSentry(w, err)
			return
		}
		clientAuthorizer := authProviders.NewAuthorizer(lenderUser.LenderID)
		if clientAuthorizer != nil {
			authorizeUserRequest := redgate.AuthorizeUserRequest{
				ResourceName:       "GET_TASK_API",
				AppClientID:        lenderUser.LenderID,
				AppID:              constants.AppIdLendingDashboard,
				Action:             "VALIDATE",
				AuthorizationToken: r.Header.Get("Authorization"),
				UserID:             loanInfo.UserID,
			}
			authResp, err := clientAuthorizer.AuthorizeUser(ctx, authorizeUserRequest)
			if err != nil {
				logger.WithContext(ctx).Errorf("[GetTaskListCont] Error during authorization: %v", err)
				errorHandler.CustomError(w, http.StatusInternalServerError, constants.GenericInternalIssuesMessage)
				return
			}
			isUserAuthorized = authResp.IsAllowed
		}
		status, err := dsr.TaskManagementDBRepositoryProvider.DBListTaskStatusByParams(ctx, &taskmanagementsql.DBGetTaskStatusParam{TaskType: &taskTypes[0].Id}, nil, nil)
		if err != nil {
			logger.WithContext(ctx).Errorf("[GetTaskListCont]error in getting task status for loanApplicationID:%s, err:%v", req.LoanApplicationID, err)
			errorHandler.HandleCustomErrorBeforeSentry(w, err)
			return
		}
		for _, s := range status {
			resp.Status = append(resp.Status, mdashboard.Status{
				Id:       s.Id,
				Name:     s.Name,
				Sequence: s.Sequence,
				State:    s.State.String(),
			})
			statusIDToNameMap[s.Id] = s.Name
		}

		var taskIds []string
		for _, task := range tasks {
			taskIds = append(taskIds, task.Id)
		}

		var workflowInstances []workflowinstancesql.DBGetWorkflowInstancesResponse
		workflowInstanceRepo := workflowinstancesql.NewWorkflowInstancesRepository(db.GetDB())
		if len(taskIds) != 0 {
			workflowInstances, err = workflowInstanceRepo.DBListWorkflowInstancesByParams(ctx, &workflowinstancesql.DBListWorkflowInstancesParam{
				IdentifierIds: taskIds,
			}, nil)
			if err != nil {
				logger.WithContext(ctx).Errorf("[GetTaskListCont] err during fetch workflow instances err:%s, loanApplicationID:%v", err, req.LoanApplicationID)
				errorHandler.HandleCustomErrorBeforeSentry(w, err)
				return
			}
		}
		var workflowIndentifierMap = make(map[string]workflowinstancesql.DBGetWorkflowInstancesResponse)

		for _, workflowInstance := range workflowInstances {
			workflowIndentifierMap[workflowInstance.IdentifierID] = workflowInstance
		}

		for _, task := range tasks {

			notes, err := dsr.TaskManagementDBRepositoryProvider.DBListTaskNotesByParams(ctx, &taskmanagementsql.DBGetTaskNotesParam{TaskID: &task.Id}, nil, nil)
			if err != nil {
				logger.WithContext(ctx).Errorf("[GetTaskListCont]error in getting task for loanApplicationID:%s, err:%v", req.LoanApplicationID, err)
				errorHandler.HandleCustomErrorBeforeSentry(w, err)
				return
			}
			var sNotes []mdashboard.Note
			for _, note := range notes {
				sNotes = append(sNotes, mdashboard.Note{
					Id:   note.Id,
					Type: note.Type,
					Data: note.Data,
				})
			}

			attachments, err := taskmanagementsql.DBListTaskAttachmentsByParams(ctx, &taskmanagementsql.DBGetTaskAttachmentsParam{TaskID: &task.Id}, nil, nil)
			if err != nil {
				logger.WithContext(ctx).Errorf("[GetTaskListCont]error in getting task for loanApplicationID:%s, err:%v", req.LoanApplicationID, err)
				errorHandler.HandleCustomErrorBeforeSentry(w, err)
				return
			}
			var sAttachment []mdashboard.Attachment
			for _, attachment := range attachments {
				if pAttchment, err := ParseAttachment(ctx, attachment); err == nil {
					sAttachment = append(sAttachment, pAttchment)
					continue
				}
				logger.WithContext(ctx).Warnf("[GetTaskListCont]error in getting attachment for loanApplicationID:%s, err:%v", req.LoanApplicationID, err)
			}

			canDelete := false
			if rbacGroupName == "RCU" && task.TypeId == PL_Sanctions_RCU {
				canDelete = true
			} else if strings.Contains(rbacGroupName, "Credit") && task.TypeId == PL_Sanctions_Credit {
				canDelete = true
			}

			if loanInfo.LenderID == constants.ABFLID {
				canDelete = true
			}

			if loanInfo.LenderID == constants.MFLBLID && strings.Contains(rbacGroupName, "Credit") {
				canDelete = true
			}

			taskResolved := true
			var sChecks []mdashboard.Check
			checks, err := taskmanagementsql.DBListChecksByParams(ctx, &taskmanagementsql.DBGetChecksParam{TaskID: &task.Id}, nil, nil)
			if err != nil {
				logger.WithContext(ctx).Warnf("[GetTaskListCont]error in getting checks for loanApplicationID:%s, err:%v", req.LoanApplicationID, err)
			}
			for _, check := range checks {

				sChecks = append(sChecks, mdashboard.Check{
					Type:     check.Type.String(),
					Result:   check.Result,
					Resolved: check.Resolved,
				})

				taskResolved = taskResolved && (check.Type != mdashboard.Blocker || check.Resolved)
			}

			var (
				workflowInstanceID   string
				workflowCurrentState string
				assignedTo           string
				assignedUserGroup    string
			)

			if instance, exists := workflowIndentifierMap[task.Id]; exists {
				workflowInstanceID = instance.ID
				workflowCurrentState = instance.CurrentState
				assignedTo = instance.AssignedTo
				assignedUserGroup = instance.AssignedGroup
			}

			// Similarly, ensure statusIDToNameMap has the key
			statusName := ""
			if name, exists := statusIDToNameMap[task.StatusId]; exists {
				logger.WithContext(ctx).Warnf("[GetTaskList]status name not found for statusID:%s", task.StatusId)
				statusName = name
			}
			taskPermissions := map[string]interface{}{
				"isEditable":             isUserAuthorized,
				"showCompleteTaskButton": isUserAuthorized,
			}

			var metadataMap map[string]interface{}
			if task.Metadata != "" {
				err := json.Unmarshal([]byte(task.Metadata), &metadataMap)
				if err != nil {
					metadataMap = make(map[string]interface{})
				}
			}

			resp.Tasks = append(resp.Tasks, mdashboard.Task{
				CanDelete:            canDelete,
				TaskID:               task.Id,
				StatusID:             task.StatusId,
				Metadata:             task.Metadata,
				MetadataMap:          metadataMap,
				ApprovedAt:           task.ApprovedAt,
				ApprovedBy:           task.ApprovedBy,
				TypeID:               task.TypeId,
				UpdatedBy:            task.UpdatedBy,
				UpdatedAt:            task.UpdatedAt,
				CreatedAt:            task.CreatedAt,
				CreatedBy:            task.CreatedBy,
				Attachments:          sAttachment,
				Notes:                sNotes,
				Checks:               sChecks,
				Resolved:             taskResolved,
				WorkflowInstanceID:   workflowInstanceID,
				WorkflowCurrentState: workflowCurrentState,
				StatusName:           statusName,
				AssignedTo:           assignedTo,
				AssignedUserGroup:    assignedUserGroup,
				TaskPermissions:      taskPermissions,
			})
		}
		var resData = map[string]interface{}{
			"tasks": resp,
		}
		ctx = context.WithValue(ctx, "resData", resData)
		next.ServeHTTP(w, r.WithContext(ctx))

	})
}

func transformDoc(doc documentsql.DBGetDashboardDocsResponse) mdashboard.DashboardDocs {
	return mdashboard.DashboardDocs{
		DocID:             doc.DocID,
		LoanApplicationID: doc.LoanApplicationID,
		MediaID:           doc.MediaID,
		DocumentID:        doc.DocumentID,
		ReviewStatus:      constants.AdditionalKYCDocStatusText[doc.ReviewStatus],
		CreatedAt:         doc.CreatedAt,
		CreatedBy:         doc.CreatedBy,
		EntityType:        doc.EntityType,
	}
}

func transformComments(comments []documentsql.DBGetDashboardCommentsResponse) []mdashboard.DashboardDocComments {
	resp := make([]mdashboard.DashboardDocComments, 0, len(comments))
	for _, comment := range comments {
		resp = append(resp, transformComment(comment))
	}
	return resp
}

func transformComment(comment documentsql.DBGetDashboardCommentsResponse) mdashboard.DashboardDocComments {
	return mdashboard.DashboardDocComments{
		Comment:    comment.Comment,
		CreatedAt:  comment.CreatedAt,
		EntityType: comment.EntityType,
		Status:     comment.Status,
		CommentId:  comment.CommentId,
	}
}

func (dsr *DashboardServiceRepository) GetSoftApproveStatusCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		attributes := ctx.Value("attributes").(map[string]interface{})
		req := attributes["req"].(mdashboard.GetSoftApproveStatusParam)

		var (
			status     mdashboard.SoftApproved
			visibility bool
		)
		loanRepo := loansql.NewLoanDBRepository(psql.Database)
		la, err := loanRepo.DBGetLoanApplicationByParams(ctx, loansql.DBGetLoanApplicationParam{LoanApplicationID: req.LoanApplicationID})
		if err != nil {
			logger.WithContext(ctx).Errorf("[GetTasksByLenderAndTypeCont] error in getting loan application for id : %s, error : %v", req.LoanApplicationID, err)
			errorHandler.HandleCustomErrorBeforeSentry(w, err)
			return
		}

		var taskTypeName mdashboard.TaskType
		if la.LenderID == constants.ABFLID {
			taskTypeName = mdashboard.Sanctions
		} else {
			taskTypeName = mdashboard.MFL_Sanctions
		}

		taskType, err := dsr.TaskManagementDBRepositoryProvider.DBGetTaskTypesByParams(ctx, &taskmanagementsql.DBGetTaskTypesParam{Name: &taskTypeName}, nil)
		if err != nil {
			logger.WithContext(ctx).Errorf("[GetSoftApproveStatusCont]error in getting task type for loanApplication:%s, err:%v", req.LoanApplicationID, err)
			errorHandler.HandleCustomErrorBeforeSentry(w, err)
			return
		}

		task, err := taskmanagementsql.DBGetTasksByParams(ctx, &taskmanagementsql.DBGetTasksParam{
			IdentifierID:   &req.LoanApplicationID,
			IdentifierType: &[]string{mdashboard.LoanApplicationID.String()}[0],
			TypeID:         &taskType.Id,
		}, nil)
		if err != nil && !errors.Is(err, sql.ErrNoRows) {
			logger.WithContext(ctx).Errorf("[GetSoftApproveStatusCont]error in getting task for loanApplication:%s, err:%v", req.LoanApplicationID, err)
			errorHandler.HandleCustomErrorBeforeSentry(w, err)
			return
		}

		if task != nil {
			visibility = true
		}

		md := map[string]interface{}{}
		err = json.Unmarshal([]byte(la.Metadata), &md)
		if err != nil {
			logger.WithContext(ctx).Errorf("[GetTasksByLenderAndTypeCont] error in getting loan application for id : %s, error : %v", req.LoanApplicationID, err)
			errorHandler.HandleCustomErrorBeforeSentry(w, err)
			return
		}

		if isSoftApproved, ok := md["softApproved"]; ok {
			data, err := json.Marshal(isSoftApproved)
			if err != nil {
				fmt.Println("Error marshaling data:", err)
				return
			}

			err = json.Unmarshal(data, &status)
			if err != nil {
				logger.WithContext(ctx).Errorf("[GetTasksByLenderAndTypeCont] error in unmarshalling data, err:%v", err)
			}
		}

		res := map[string]interface{}{
			"status":     status,
			"visibility": visibility,
		}

		ctx = context.WithValue(ctx, "resData", res)
		next.ServeHTTP(w, r.WithContext(ctx))

	})
}

func (dsr *DashboardServiceRepository) GetTasksByLenderAndTypeCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		attributes := ctx.Value("attributes").(map[string]interface{})
		req := attributes["req"].(mdashboard.GetTasksByLenderAndTypeParam)

		res := mdashboard.GetTasksByTypeResponse{}

		taskTypes, err := dsr.TaskManagementDBRepositoryProvider.DBListTaskTypesByParams(ctx, &taskmanagementsql.DBGetTaskTypesParam{
			NameList: &req.Types,
		}, nil, nil)
		if err != nil {
			logger.WithContext(ctx).Errorf("[GetTasksByLenderAndTypeCont] error in getting task type: %s, err: %v", req.Types, err)
			errorHandler.HandleCustomErrorBeforeSentry(w, err)
			return
		}

		for _, ttype := range taskTypes {
			taskStatus, err := dsr.TaskManagementDBRepositoryProvider.DBListTaskStatusByParams(ctx, &taskmanagementsql.DBGetTaskStatusParam{
				TaskType: &ttype.Id,
			}, nil, nil)
			if err != nil {
				logger.WithContext(ctx).Errorf("[GetTasksByLenderAndTypeCont] error in getting task status for taskTypes: %s, err: %v", req.Types, err)
				errorHandler.HandleCustomErrorBeforeSentry(w, err)
				return
			}

			for _, status := range taskStatus {
				res.Status = append(res.Status, mdashboard.Status{
					Id:       status.Id,
					Name:     status.Name,
					Sequence: status.Sequence,
					State:    status.State.String(),
				})
			}

			masterTaskList, err := taskmanagementsql.DBListTaskMasterByParams(ctx, &taskmanagementsql.DBListTaskMasterParam{
				LenderId: &req.LenderID,
				TypeId:   &ttype.Id,
			}, nil, nil)
			if err != nil {
				logger.WithContext(ctx).Errorf("[GetTasksByLenderAndTypeCont] error in getting master task for lenderID: %s, err: %v", req.LenderID, err)
				errorHandler.HandleCustomErrorBeforeSentry(w, err)
				return
			}

			res.LenderID = req.LenderID

			for _, masterTask := range masterTaskList {
				res.TaskList = append(res.TaskList, mdashboard.TaskList{
					ID:                     masterTask.Id,
					Name:                   masterTask.Name,
					TaskResourceIdentifier: masterTask.TaskResourceIdentifier,
					Type:                   mdashboard.TaskType(ttype.Name),
				})
			}

		}

		ctx = context.WithValue(ctx, "resData", res)
		next.ServeHTTP(w, r.WithContext(ctx))

	})
}

func (lds *DashboardServiceRepository) UpdateSoftApproveStatusCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		ctx := r.Context()
		attributes := ctx.Value("attributes").(map[string]interface{})
		req := attributes["req"].(mdashboard.UpdateSoftApproveStatus)

		dashboardUser, ok := ctx.Value("user").(authentication.DashboardUser)
		if !ok {
			logger.WithContext(ctx).Errorf("[UpdateDeviationCont] Missing or invalid user in context")
			errorHandler.CustomError(w, http.StatusBadRequest, "Invalid user in context")
			return
		}

		loanApplication, err := lds.LoanDBRepositoryProvider.DBGetLoanApplicationByParams(ctx, loansql.DBGetLoanApplicationParam{
			LoanApplicationID: req.LoanApplicationID,
		})
		if err != nil {
			logger.WithContext(ctx).Errorf("[UpdateSoftApproveStatusCont] Failed to fetch loan appllicationfor Identifier: %s, Error: %v", req.LoanApplicationID, err)
			return
		}

		metadata := loanApplication.Metadata

		var metadataMap map[string]interface{}

		if metadata == "" {
			metadataMap = make(map[string]interface{})
		} else {
			if err = json.Unmarshal([]byte(metadata), &metadataMap); err != nil {
				logger.WithContext(ctx).Errorf("[UpdateSoftApproveStatusCont] Failed to unmarshal metadata: %v", err)
				errorHandler.CustomError(w, http.StatusInternalServerError, "Failed to process metadata")
				return
			}
		}

		metadataMap["softApproved"] = mdashboard.SoftApproved{
			Status:    true,
			UpdatedBy: dashboardUser.GetEmail(),
			UpdatedAt: general.GetTimeStampString(),
		}

		if err != nil {
			logger.WithContext(ctx).Errorf("[UpdateSoftApproveStatusCont] Failed to marshal metadata: %v", err)
			errorHandler.CustomError(w, http.StatusInternalServerError, "Failed to update metadata")
			return
		}

		var updatedMD []byte
		updatedMD, err = json.Marshal(metadataMap)
		if err != nil {
			logger.WithContext(ctx).Errorf("[UpdateSoftApproveStatusCont] Failed to marshal metadata: %v", err)
			errorHandler.CustomError(w, http.StatusInternalServerError, "Failed to update metadata")
			return
		}

		err = lds.LoanDBRepositoryProvider.DBUpdateLoanApplication(ctx, nil, &loansql.DBUpdateLoanApplicationParam{
			Metadata: string(updatedMD),
		}, req.LoanApplicationID)
		if err != nil {
			logger.WithContext(ctx).Errorf("[UpdateSoftApproveStatusCont] Failed to update loan application metadata for LoanApplicationID: %s, Error: %v", req.LoanApplicationID, err)
			errorHandler.CustomError(w, http.StatusInternalServerError, "Failed to update loan application metadata")
			return
		}

		resData := map[string]string{
			"msg": "Status Updated successfully",
		}
		ctx = context.WithValue(r.Context(), "resData", resData)

		// Pass to the next handler if any
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func fetchDocAndS3Url(ctx context.Context, table, key, id string) (map[string]interface{}, string, error) {
	switch table {
	case "dashboard_docs":
		switch key {
		case "media_id":
			doc, err := documentsql.DBListDashboardDocsByParams(ctx, &documentsql.DBGetDashboardDocsParams{MediaID: id})
			if err != nil {
				return nil, "", err
			}
			if len(doc) == 0 {
				return nil, "", errors.New("no dashboard doc found for media id")
			}

			var mediaParam mediasql.DBGetMediaParam
			mediaParam.MediaID = doc[0].MediaID
			// NOTE: currently fetching single doc
			mediaInfo, err := mediasql.DBGetMediaByParams(ctx, &mediaParam)
			if err != nil {
				return nil, "", err
			}
			docComments, err := documentsql.DBListDashboardCommentsByParams(ctx, &documentsql.DBGetDashboardCommentsParam{DocId: &id},
				&documentsql.DBGetDashboardCommentsFields{Comment: true}, nil)
			if err != nil {
				logger.WithContext(ctx).Warnf("error in getting comments for doc_id: %s, err: %v", id, err)
			}

			url := s3.GetPresignedURLS3(mediaInfo.Path, 300)

			return map[string]interface{}{
				"doc":      transformDoc(doc[0]),
				"comments": transformComments(docComments),
			}, url, nil

		case "doc_id":
			doc, err := documentsql.DBListDashboardDocsByParams(ctx, &documentsql.DBGetDashboardDocsParams{DocID: id})
			if err != nil {
				return nil, "", err
			}
			if len(doc) == 0 {
				return nil, "", errors.New("no dashboard doc found for media id")
			}
			if !doc[0].Status {
				return nil, "", errors.New("doc is deleted")
			}

			var mediaParam mediasql.DBGetMediaParam
			mediaParam.MediaID = doc[0].MediaID
			// NOTE: currently fetching single doc
			mediaInfo, err := mediasql.DBGetMediaByParams(ctx, &mediaParam)
			if err != nil {
				return nil, "", err
			}

			docComments, err := documentsql.DBListDashboardCommentsByParams(ctx, &documentsql.DBGetDashboardCommentsParam{DocId: &id},
				&documentsql.DBGetDashboardCommentsFields{Comment: true}, nil)
			if err != nil {
				logger.WithContext(ctx).Warnf("error in getting comments for doc_id: %s, err: %v", id, err)
			}

			logger.WithContext(ctx).Info("fetch doc and s3 url ", mediaInfo)
			url := s3.GetPresignedURLS3(mediaInfo.Path, 300)
			return map[string]interface{}{
				"doc":      transformDoc(doc[0]),
				"comments": transformComments(docComments),
			}, url, nil

		default:
			return nil, "", errors.New("unsupported key for dashboard_docs")
		}
	default:
		return nil, "", errors.New("unsupported table")
	}
}

func ParseAttachment(ctx context.Context, dbattachment taskmanagementsql.DBGetTaskAttachmentsResponse) (mdashboard.Attachment, error) {
	switch dbattachment.Source {
	case mdashboard.AttachmentSourceDB:
		link := strings.Split(dbattachment.Link, "$")
		if len(link) < 3 {
			return mdashboard.Attachment{}, errors.New("invalid attachment link")
		}
		table, key, id := link[0], link[1], link[2]

		obj, url, err := fetchDocAndS3Url(ctx, table, key, id)
		if err != nil {
			logger.WithContext(ctx).Errorf("failed to fetch doc and s3 url: %v", err)
			return mdashboard.Attachment{}, err
		}
		logger.WithContext(ctx).Info("parsing attachment", obj, url, err)
		return mdashboard.Attachment{
			S3Url:  url,
			Id:     dbattachment.Id,
			Source: string(dbattachment.Source),
			Obj:    obj,
			Path:   dbattachment.Link,
		}, nil
	default:
		return mdashboard.Attachment{}, errors.New("unhandled/invalid attachment source")
	}
}

func UpdateAdditionalDocsCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusConflict)

		ctx := r.Context()
		userObj := ctx.Value("user").(authentication.LenderUserStruct)
		attributes := ctx.Value("attributes").(map[string]interface{})

		docID := attributes["documentID"].(string)
		loanApplicationID := attributes["loanApplicationID"].(string)
		mediaIDs := attributes["mediaIDs"].([]string)
		status := attributes["status"].(int)

		err := additionaldocs.UpdateAdditionalKYCDoc(docID, loanApplicationID, mediaIDs, status,
			constants.EntityTypeLenderUser, userObj.Email)
		if err != nil {
			errorHandler.LogErrorAndPanic(err)
			http.Error(w, "Failed to update document", http.StatusInternalServerError)
			return
		}

		resData := map[string]string{
			"msg": "Additional Doc Updated successfully",
		}
		ctx = context.WithValue(r.Context(), "resData", resData)

		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func (dsr *DashboardServiceRepository) FetchInvestigationDetailsCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()
		attributes := ctx.Value("attributes").(map[string]interface{})
		req := attributes["req"].(mdashboard.FetchInvestigationDetailsReq)
		var (
			err             error
			response        []mdashboard.FetchInvestigationDetailsResponse
			loanparam       loansql.DBGetLoanApplicationParam
			pdMetadata      mdashboard.PDInvestigationDetails
			taskStatusParam taskmanagementsql.DBGetTaskStatusParam
		)

		loanparam.LoanApplicationID = req.LoanApplicationID

		loanApplication, err := dsr.LoanDBRepositoryProvider.DBGetLoanApplicationByParams(ctx, loanparam)
		if err != nil {
			switch err {
			case sql.ErrNoRows:
				log.WithContext(ctx).Errorf("[FetchInvestigationDetailsCont] no data found for loan details. err: %v, loanInfoParam: %+v", err, loanparam)
				errorHandler.CustomError(w, http.StatusBadRequest, mdashboard.LoanDetailsNotFound)
			default:
				log.WithContext(ctx).Errorf("[FetchInvestigationDetailsCont] failed to loanInfo by params. err: %v, loanInfoParam: %+v", err, loanparam)
				errorHandler.CustomError(w, http.StatusInternalServerError, constants.GenericFailureMessage)
			}
			return
		}

		if !dsr.FeatureFlagDBRepositoryProvider.Get(loanApplication.LenderID, dashboardModel.FlagPDInvestigationTask) {
			log.WithContext(ctx).Warnf("[FetchInvestigationDetailsCont] Not fetching pdfeature flag false")
			errorHandler.CustomError(w, http.StatusBadRequest, "PD investigation feature is not whitelisted")
			return
		}

		identifierType := dashboardModel.PRIMARY_USER.String()
		taskParams := &taskmanagementsql.DBGetTasksListParam{
			IdentifierID:   &loanApplication.UserID,
			IdentifierType: &identifierType,
		}

		tasks, err := dsr.TaskManagementDBRepositoryProvider.DBListTasksByParams(ctx, taskParams, nil, nil)
		if err != nil {
			logger.WithContext(ctx).Errorf("[FetchInvestigationDetailsCont] Error getting tasks, loanApplicationID:%v error:%v", req.LoanApplicationID, err)
			errorHandler.CustomError(w, http.StatusInternalServerError, constants.GenericFailureMessage)
			return
		}

		for _, pdDetail := range tasks {
			pdMetadata = mdashboard.PDInvestigationDetails{}
			taskStatusParam = taskmanagementsql.DBGetTaskStatusParam{}

			err = json.Unmarshal([]byte(pdDetail.Metadata), &pdMetadata)
			if err != nil {
				logger.WithContext(ctx).Errorf("[FetchInvestigationDetailsCont] Error unmarshalling investigationResponse metadata, UserID:%v error:%v", pdDetail.Id, err)
				errorHandler.CustomError(w, http.StatusInternalServerError, "error unmarshalling investigationResponse metadata")
				return
			}

			taskStatusParam.TaskStatusID = &pdDetail.StatusId
			res, err := dsr.TaskManagementDBRepositoryProvider.DBGetTaskStatusByParams(ctx, &taskStatusParam, nil)
			if err != nil {
				logger.WithContext(ctx).Errorf("[FetchInvestigationDetailsCont] Error fetching task status loanApplicationID :%v error:%v", req.LoanApplicationID, err)
				errorHandler.CustomError(w, http.StatusInternalServerError, constants.GenericFailureMessage)
				return
			}

			notes, err := dsr.TaskManagementDBRepositoryProvider.DBListTaskNotesByParams(ctx, &taskmanagementsql.DBGetTaskNotesParam{TaskID: &pdDetail.Id}, nil, nil)
			if err != nil {
				logger.WithContext(ctx).Errorf("[FetchInvestigationDetailsCont]  Error getting task notes loanApplicationID :%v error:%v", req.LoanApplicationID, err)
			}

			if len(notes) != 0 {
				pdMetadata.Remarks = notes[0].Data
			} else {
				logger.WithContext(ctx).Errorf("[FetchInvestigationDetailsCont]  no task notes found loanApplicationID :%v error:%v", req.LoanApplicationID, err)
			}

			investigationResponse := mdashboard.FetchInvestigationDetailsResponse{
				TaskID:            pdDetail.Id,
				StatusID:          res.Id,
				Result:            res.Name,
				LoanApplicationID: req.LoanApplicationID,
				UserID:            pdDetail.IdentifierId,
				UserType:          pdDetail.IdentifierType,
				CreatedAt:         pdDetail.CreatedAt,
				CreatedBy:         pdDetail.CreatedBy,
				Status:            pdMetadata.Status,
				Details: mdashboard.InvestigationDetails{
					PDInvestigationDetails: pdMetadata,
				},
			}

			response = append(response, investigationResponse)
		}

		ctx = context.WithValue(ctx, "resData", response)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func isWorkflowPendingOrNotFound(ctx context.Context, db *sqlx.DB, loanApplicationID string) (bool, error) {
	var status string

	query := `
        SELECT status 
        FROM dashboard_workflow_status_tracker
        WHERE resource_id = $1
        AND workflow_name IN ('abfl_bre_wf', 'abfl_bre_wf_v2') 
        ORDER BY created_at DESC 
        LIMIT 1;
    `

	err := db.GetContext(ctx, &status, query, loanApplicationID)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return true, nil // No row found, return true
		}
		return false, err // Database error
	}

	return status == "processing", nil // Return true if status is "pending"
}
