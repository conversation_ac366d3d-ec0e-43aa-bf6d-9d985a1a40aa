package dashboard

import (
	"bytes"
	"context"
	"database/sql"
	"encoding/json"
	"finbox/go-api/authentication"
	"finbox/go-api/constants"
	"finbox/go-api/errorHandler"
	fgraphql "finbox/go-api/functions/graphql"
	"finbox/go-api/functions/logger"
	configsql "finbox/go-api/internal/repository/psql/configmanagement"
	configsrv "finbox/go-api/internal/service/configmanagement"
	"text/template"

	loansql "finbox/go-api/internal/repository/psql/loan"
	configmanagement "finbox/go-api/internal/service/configmanagement"
	uidashboard "finbox/go-api/internal/service/uiconfigmanagement/dashboard"
	mdashboard "finbox/go-api/models/dashboard"
	"finbox/go-api/models/lenderdropdown"
	"fmt"
	"net/http"

	"github.com/fatih/structs"
	"github.com/valyala/fasttemplate"
)

// TODO - FD-1348 (need to store below query inside db and fetch from db.)
const (
	GET_LENDER_USERS_LIST_GRAPHQL_QUERY = `{ lenderUsersList(lenderID: "{{LenderID}}", email: "{{Email}}", page: "{{Page}}", limit: "{{Limit}}", rbacGroupNames: {{RbacGroupNames}}) { name rbacGroupName email } }`
	GET_PINCODE_DATA_GRAPHQL_QUERY      = `{ pincodeData(pincode: "{{Pincode}}") { city state } }`
	GET_WORKFLOW_HISTORY_GRAPHQL_QUERY  = `{workflowInstanceHistory(workflowInstanceID: "{{WorkflowInstanceID}}"){workflowInstanceID, previousState, currentState, action, actionBy, assignedGroup, assignedTo, remarks, createdAt, updatedAt}}`

	GET_HUNTER_DATA_GRAPHQL_QUERY = `{lenderVariables(userID: "{{UserID}}"){ dynamicVariablesJSON(fields: [
		"hunterCheckResponse.totalMatchScore",
		"hunterCheckResponse.matches",
		"hunterCheckResponse.responseHeader.overallResponse.decision",
		"hunterCheckResponse.responseHeader.overallResponse.decisionReasons"
	  ])}}`
	GET_LAP_LOAN_OFFER_METADATA_QUERY = `{lapLoanOffer(userID: "{{UserID}}"){offerMetaDataJson(fields:["authority"])}}`
)

const (
	GET_LENDER_USER_LIST_GRAPHQL_ACTION_TYPE = "GetLenderUsersList"
	GET_PINCODE_DATA_GRAPHQL_ACTION_TYPE     = "GetPinCodeData"
	GET_WORKFLOW_HISTORY_ACTION_TYPE         = "GetWorkflowHistory"
	GET_HUNTER_DATA_ACTION_TYPE              = "GetHunterData"
	GET_LAP_LOAN_OFFER_METADATA_ACTION_TYPE  = "GetLapOffersData"
)

var GraphqlActionToQueryMap = map[string]string{
	GET_LENDER_USER_LIST_GRAPHQL_ACTION_TYPE: GET_LENDER_USERS_LIST_GRAPHQL_QUERY,
	GET_PINCODE_DATA_GRAPHQL_ACTION_TYPE:     GET_PINCODE_DATA_GRAPHQL_QUERY,
	GET_WORKFLOW_HISTORY_ACTION_TYPE:         GET_WORKFLOW_HISTORY_GRAPHQL_QUERY,
	GET_HUNTER_DATA_ACTION_TYPE:              GET_HUNTER_DATA_GRAPHQL_QUERY,
	GET_LAP_LOAN_OFFER_METADATA_ACTION_TYPE:  GET_LAP_LOAN_OFFER_METADATA_QUERY,
}

func GetDropdownConfigCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()
		attributes := ctx.Value("attributes").(map[string]interface{})
		req := attributes["req"].(mdashboard.GetDropdownConfigReq)

		value := lenderdropdown.GetValue(req.DropdownName, req.DropdownType, req.LenderID)

		var resData = map[string]interface{}{
			"values": value,
		}

		ctx = context.WithValue(ctx, "resData", resData)
		next.ServeHTTP(w, r.WithContext(ctx))

	})
}

func (dsr *DashboardServiceRepository) FetchUIBuilderConfig(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {

		ctx := r.Context()
		attributes := ctx.Value("attributes").(map[string]interface{})
		req := attributes["req"].(mdashboard.FetchUIBuilderConfigReq)
		dashboard := ctx.Value("dashboard").(string)
		var (
			UIconfigParam configmanagement.GetConfigInfoParam
			loanparam     loansql.DBGetLoanApplicationParam
		)

		configKeyValueMap := make(map[string]any)

		loanparam.LoanApplicationID = req.LoanApplicationID

		loanInfo, err := dsr.LoanDBRepositoryProvider.DBGetLoanApplicationByParams(ctx, loanparam)
		if err != nil {
			switch err {
			case sql.ErrNoRows:
				log.WithContext(ctx).Errorf("[FetchUIBuilderConfig] no data found for loan details. err: %v, loanInfoParam: %+v", err, loanparam)
				errorHandler.CustomError(w, http.StatusBadRequest, mdashboard.LoanDetailsNotFound)
			default:
				log.WithContext(ctx).Errorf("[FetchUIBuilderConfig] failed to loanInfo by params. err: %v, loanInfoParam: %+v", err, loanparam)
				errorHandler.ReportToSentryV2(ctx, err)
				errorHandler.CustomError(w, http.StatusInternalServerError, constants.GenericInternalIssuesMessage)
			}
			return
		}

		configKeyValueMap["service"] = dashboard

		configKeyValueMap["unique_id"] = loanInfo.LenderID
		if req.SectionName != nil {
			configKeyValueMap["section_name"] = *req.SectionName
		}
		if req.TaskName != nil {
			configKeyValueMap["task_name"] = *req.TaskName
		}

		UIconfigParam.ResourceName = fmt.Sprintf("ui_builder_config_%s", req.ResourceName)
		builderConfig, err := dsr.ConfigManagementSrvRepositoryProvider.LegacyProvider().GetConfigInfoByParam(ctx, &UIconfigParam, configKeyValueMap)
		if err != nil {
			logger.WithContext(ctx).Errorf("[FetchUIBuilderConfig] failed to fetch builder config data. err: %v, req: %+v", err, req)
			errorHandler.CustomError(w, http.StatusInternalServerError, "builder config data not found.")
			return
		}

		// Unstringify the Config field
		var configData map[string]interface{}
		if err := json.Unmarshal([]byte(builderConfig.Config), &configData); err != nil {
			logger.WithContext(ctx).Errorf("[FetchUIBuilderConfig] failed to unstringify config data. err: %v", err)
			errorHandler.CustomError(w, http.StatusInternalServerError, "Invalid config data format.")
			return
		}

		graphqlResponse, err := FetchDataByGraphql(
			mdashboard.FetchUIConfigReq{
				UserID:            loanInfo.UserID,
				LoanApplicationID: req.LoanApplicationID,
				Service:           dashboard,
				ResourceName:      req.ResourceName,
				CoApplicantUserID: req.SelectedCoApplicantUserID,
			},
			configKeyValueMap,
			ctx,
			fmt.Sprintf("ui_builder_graphql_%s", req.ResourceName),
		)
		if err != nil {
			// just logging and not returning from this flow because we have ui-config builder with no graphql response
			logger.WithContext(ctx).Warnf("[FetchUIBuilderConfig] failed to fetch graphql data. err: %v, req: %+v", err, req)
		}

		var resData = map[string]interface{}{
			"config":          configData, // Use the parsed JSON object instead of the string
			"graphqlResponse": graphqlResponse,
		}

		ctx = context.WithValue(ctx, "resData", resData)
		next.ServeHTTP(w, r.WithContext(ctx))

	})
}

func (dsr *DashboardServiceRepository) FetchUIConfig(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {

		ctx := r.Context()
		userDashboard := ctx.Value("user")
		attributes := ctx.Value("attributes").(map[string]interface{})
		req := attributes["req"].(mdashboard.FetchUIConfigReq)
		dashboard := ctx.Value("dashboard").(string)
		var (
			lenderUser          authentication.LenderUserStruct
			_                   authentication.MasterDashboardUserStruct
			UIconfigParam       configmanagement.GetConfigInfoParam
			uiConfigParam       uidashboard.ProcessUIConfigParam
			loanparam           loansql.DBGetLoanApplicationParam
			configKeyValueMap   map[string]any
			uiConfigKeyValueMap []map[string]any
		)

		switch v := userDashboard.(type) {
		case authentication.LenderUserStruct:
			lenderUser = v
		case authentication.MasterDashboardUserStruct:
			_ = v
		default:
			logger.WithContext(ctx).Errorf("[FetchUIConfig] invalid user")
			errorHandler.CustomError(w, http.StatusInternalServerError, constants.InvalidDashboardUserMessage)
			return
		}

		if dashboard == constants.LenderDashboardRef {
			loanparam.LenderID = lenderUser.LenderID
			configKeyValueMap = map[string]any{
				"service": req.Service,
			}
		} else {
			loanparam.SourceEntityID = req.SourceEntityID
			configKeyValueMap = map[string]any{
				"service": req.Service,
			}
		}

		loanparam.LoanApplicationID = req.LoanApplicationID

		loanInfo, err := dsr.LoanDBRepositoryProvider.DBGetLoanApplicationByParams(ctx, loanparam)
		if err != nil {
			switch err {
			case sql.ErrNoRows:
				log.WithContext(ctx).Errorf("[FetchUIConfig] no data found for loan details. err: %v, loanInfoParam: %+v", err, loanparam)
				errorHandler.CustomError(w, http.StatusBadRequest, mdashboard.LoanDetailsNotFound)
			default:
				log.WithContext(ctx).Errorf("[FetchUIConfig] failed to loanInfo by params. err: %v, loanInfoParam: %+v", err, loanparam)
				errorHandler.ReportToSentryV2(ctx, err)
				errorHandler.CustomError(w, http.StatusInternalServerError, constants.GenericInternalIssuesMessage)
			}
			return
		}

		configKeyValueMap["unique_id"] = loanInfo.LenderID

		// Fetch data based on resource
		responseData, err := dsr.fetchDataByResource(r, req, configKeyValueMap)
		if err != nil {
			logger.WithContext(ctx).Errorf("[FetchUIConfig] failed to fetch data. err: %v, req: %+v", err, req)
			errorHandler.CustomError(w, http.StatusNotFound, "graphql data not found.")
			return
		}

		UIconfigParam.ResourceName = fmt.Sprintf("ui_config_%s", req.ResourceName)

		uiConfigKeyValueMap = append(uiConfigKeyValueMap, configKeyValueMap)
		uiConfigKeyValueMap = append(uiConfigKeyValueMap, map[string]any{"unique_id": "default", "service": "default"})

		uiConfigs, err := dsr.ConfigManagementSrvRepositoryProvider.LegacyProvider().GetConfigListByParam(ctx, &UIconfigParam, uiConfigKeyValueMap)
		if err != nil {
			logger.WithContext(ctx).Errorf("[FetchUIConfig] failed to get UIconfigInfoParam. err: %v, req: %+v", err, req)
			errorHandler.CustomError(w, http.StatusNotFound, "ui-config data not found")
			return
		}

		config, err := uidashboard.ProcessAndMergeConfigs(uiConfigs)
		if err != nil {
			logger.WithContext(ctx).Errorf("[FetchUIConfig] failed to merge configs. err: %v, req: %+v", err, req)
			errorHandler.CustomError(w, http.StatusInternalServerError, constants.GenericInternalIssuesMessage)
			return
		}

		uiConfigParam.DashboardType = dashboard
		uiConfigParam.ResourceName = req.ResourceName
		uiConfigParam.UIConfig = config
		uiConfigParam.GraphQLResponse = responseData
		uiConfigParam.LoanInfo.LenderID = loanInfo.LenderID
		uiConfigParam.LoanInfo.LoanApplicationID = loanInfo.LoanApplicationID
		uiConfigParam.LoanInfo.UserID = loanInfo.UserID
		uiConfigParam.BearerToken = r.Header.Get("Authorization")

		uiConfigResp, err := uidashboard.ProcessUIConfig(ctx, uiConfigParam)
		if err != nil {
			logger.WithContext(ctx).Errorf("[FetchUIConfig] failed to process ui config. err: %v, req: %+v", err, req)
			errorHandler.CustomError(w, http.StatusInternalServerError, constants.GenericInternalIssuesMessage)
			return
		}

		var resData = map[string]interface{}{
			"ruleData": uiConfigResp,
		}

		ctx = context.WithValue(ctx, "resData", resData)
		next.ServeHTTP(w, r.WithContext(ctx))

	})
}

func FetchDataByGraphql(
	req mdashboard.FetchUIConfigReq,
	configKeyValueMap map[string]any,
	ctx context.Context,
	graphqlResourceName string,
) (interface{}, error) {
	graphQLConfigParam := configmanagement.GetConfigInfoParam{
		ResourceName: graphqlResourceName,
	}

	configManagementDBRepository := configsql.NewConfigManagementDBRepository(database)
	configManagementSrvRepository := configsrv.NewConfigManagementSrvRepository(configsrv.ConfigManagementSrvRepository{
		ConfigDBRepositoryProvider: configManagementDBRepository,
	})
	graphQLConfig, err := configManagementSrvRepository.LegacyProvider().GetConfigInfoByParam(ctx, &graphQLConfigParam, configKeyValueMap)
	if err != nil {
		return nil, fmt.Errorf("[fetchDataByResource] - failed to get graphql config: %v", err)
	}

	populatedConfig, err := populateValuesInGraphqlConfig(graphQLConfig.Config, req)
	if err != nil {
		return nil, fmt.Errorf("[fetchDataByResource] - failed to populate values in graphql config: %v", err)
	}

	schema, err := fgraphql.GraphQLGet(ctx, fgraphql.GraphQLGetParam{
		Query:           populatedConfig,
		QueryIdentifier: graphQLConfigParam.ResourceName,
	})
	if err != nil {
		return nil, fmt.Errorf("[fetchDataByResource] - failed to execute graphql query: %v", err)
	}
	return schema, nil
}

func (dsr *DashboardServiceRepository) fetchDataByResource(r *http.Request, req mdashboard.FetchUIConfigReq, configKeyValueMap map[string]any) (interface{}, error) {
	switch req.ResourceName {

	case BANK_DETAIL_TAB_RESOURCE_NAME, COAPPLICANT_TAB_RESOURCE_NAME, DETAILS_TAB_RESOURCE_NAME, APPLICATION_FORM_TAB_RESOURCE_NAME, COLLATERAL_TAB_RESOURCE_NAME, ENACH_TAB:
		return FetchDataByGraphql(req, configKeyValueMap, r.Context(), fmt.Sprintf("graphql_%s", req.ResourceName))
	case BUSINESS_DETAILS_TAB_RESOURCE_NAME:
		return dsr.getBusinessDetailTabData(r)
	case PARTNER_DETAILS_TAB_RESOURCE_NAME:
		return dsr.getPartnerDetailsTabData(r)

	default:
		return nil, fmt.Errorf("[fetchDataByResource] - unsupported resource name: %s", req.ResourceName)
	}
}

func populateValuesInGraphqlConfig(
	config string,
	req mdashboard.FetchUIConfigReq,
) (string, error) {
	data := structs.Map(req)

	if req.ResourceName == COAPPLICANT_TAB_RESOURCE_NAME {
		return fmt.Sprintf(config, req.LoanApplicationID), nil
	} else {
		tmpl, err := template.New("graphql").Parse(config)
		if err != nil {
			return "", fmt.Errorf("[populateValuesInGraphqlConfig] failed to parse template: %v", err)
		}

		var buf bytes.Buffer
		if err := tmpl.Execute(&buf, data); err != nil {
			return "", fmt.Errorf("[populateValuesInGraphqlConfig] failed to parse template: %v", err)
		}

		return buf.String(), nil
	}

	// TODO Need this reference do not remove - bhavnish
	/*
		if dashboard == constants.LenderDashboardRef {
			return fmt.Sprintf(config, req.LoanApplicationID), nil
		} else if dashboard == constants.MasterDashboardRef {

			if req.ResourceName == BANK_DETAIL_TAB_RESOURCE_NAME {
				return fmt.Sprintf(config, req.LoanApplicationID, dashboardutils.GetMaskingInfo(&masterUser, req.SourceEntityID).Profile), nil
			} else {
				return fmt.Sprintf(config, req.LoanApplicationID), nil
			}

		} else {
			logger.WithContext(ctx).Errorf("[populateValuesInGraphqlConfig] no default handling for dashboard type %v, SourceEntityID %v, loan id %v", dashboard, req.SourceEntityID, req.LoanApplicationID)
			return "", fmt.Errorf(constants.GenericInternalIssuesMessage)
		}*/

}

func FetchDataByConfigAndGraphQL(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()
		attributes := r.Context().Value("attributes").(map[string]interface{})
		req := attributes["req"].(mdashboard.FetchGraphQLDataReq)
		user := r.Context().Value("user")
		dashboard := r.Context().Value("dashboard").(string)

		graphqlRequestValidation(ctx, w, &req, user, dashboard)

		populateDefaultGraphqlRequestParams(&req)

		// TODO - FD-1348 (need to store below query inside db and fetch from db.)
		queryTemplate, found := GraphqlActionToQueryMap[req.GraphQLAction]
		if !found {
			logger.WithContext(ctx).Errorf("[FetchGraphQLData] invalid grahql action provided. req: %+v", req)
			errorHandler.CustomError(w, http.StatusInternalServerError, constants.InvalidGraphqlAction)
			return
		}

		query := createGraphQLQueryFromReq(req, queryTemplate)

		resData, err := fgraphql.GraphQLGet(ctx, fgraphql.GraphQLGetParam{
			Query:           query,
			QueryIdentifier: "graphql_server_query",
		})
		if err != nil {
			logger.WithContext(ctx).Errorf("[FetchGraphQLData] failed to get grahqlSchema. err: %v, req: %+v", err, req)
			errorHandler.CustomError(w, http.StatusInternalServerError, constants.GenericInternalIssuesMessage)
			return
		}

		ctx = context.WithValue(ctx, "resData", resData)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func createGraphQLQueryFromReq(req mdashboard.FetchGraphQLDataReq, queryTemplate string) string {
	data := structs.Map(req)

	if req.RbacGroupNames != nil && len(req.RbacGroupNames) > 0 {
		rbacGroupNamesJSON, _ := json.Marshal(req.RbacGroupNames)
		data["RbacGroupNames"] = string(rbacGroupNamesJSON)
	} else {
		data["RbacGroupNames"] = "[]"
	}

	tmpl := fasttemplate.New(queryTemplate, "{{", "}}")
	return tmpl.ExecuteString(data)
}

func graphqlRequestValidation(ctx context.Context, w http.ResponseWriter, req *mdashboard.FetchGraphQLDataReq, user any, dashboard string) {
	if value, exists := GraphqlActionToQueryMap[req.GraphQLAction]; !exists {
		logger.WithContext(ctx).Errorf("[FetchGraphQLData] unsupported graphql action. req: %+v", req)
		errorHandler.CustomError(w, http.StatusBadRequest, fmt.Sprintf("%v is not a supported graphql action.", value))
		return
	}

	var (
		lenderUser authentication.LenderUserStruct
		_          authentication.MasterDashboardUserStruct
	)

	switch v := user.(type) {
	case authentication.LenderUserStruct:
		lenderUser = v
	case authentication.MasterDashboardUserStruct:
		_ = v
	default:
		log.WithContext(ctx).Errorf("[FetchGraphQLData] invalid user")
		errorHandler.CustomError(w, http.StatusBadRequest, constants.InvalidDashboardUserMessage)
		return
	}

	// lender related validation
	if dashboard == constants.LenderDashboardRef {

		if req.LenderID != "" && lenderUser.LenderID != req.LenderID {
			log.WithContext(ctx).Errorf("[FetchGraphQLData] invalid user")
			errorHandler.CustomError(w, http.StatusBadRequest, constants.InvalidDashboardUserMessage)
			return
		}
	}
}

func populateDefaultGraphqlRequestParams(req *mdashboard.FetchGraphQLDataReq) {
	if req.Page == "" {
		req.Page = "1"
	}

	if req.Limit == "" {
		req.Limit = "100"
	}

	if req.RbacGroupNames == nil {
		req.RbacGroupNames = []string{}
	}
}
