package dashboard

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"strings"
	"sync"

	"finbox/go-api/authentication"
	"finbox/go-api/constants"
	cservices "finbox/go-api/controller/services"
	"finbox/go-api/errorHandler"
	"finbox/go-api/functions/logger"
	"finbox/go-api/functions/zipper"
	"finbox/go-api/infra/s3"
	loansql "finbox/go-api/internal/repository/psql/loan"
	usersql "finbox/go-api/internal/repository/psql/user"
	"finbox/go-api/internal/service/configmanagement"
	documentshelper "finbox/go-api/internal/service/documents"
	mdashboard "finbox/go-api/models/dashboard"
	"finbox/go-api/utils/kycutils"

	"github.com/google/uuid"
	"github.com/samber/lo"
)

func (dsr *DashboardServiceRepository) GetDocumentsCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusInternalServerError)

		ctx := r.Context()
		attributes := ctx.Value("attributes").(map[string]interface{})
		req := attributes["req"].(mdashboard.GetDocumentsParam)

		response, err := dsr.GetDocumentsByParams(ctx, req)
		if err != nil {
			logger.WithContext(ctx).Errorf("[GetDocumentsCont] Error: %v", err)
			errorHandler.CustomError(w, http.StatusInternalServerError, err.Error())
			return
		}

		ctx = context.WithValue(ctx, "resData", response)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func (dsr *DashboardServiceRepository) GetDocumentsByParams(ctx context.Context, req mdashboard.GetDocumentsParam) (mdashboard.CategorizedDocsResponse, error) {
	var (
		lenderUser      authentication.LenderUserStruct
		filteredDocIds  []string
		categoryMap     map[string][]string
		documentReqType string
	)

	userDashboard := ctx.Value("user")
	switch v := userDashboard.(type) {
	case authentication.LenderUserStruct:
		lenderUser = v
	case authentication.MasterDashboardUserStruct:
		// masterUser = v
	default:
		logger.WithContext(ctx).Errorf("[GetDocumentsCont] invalid user")
		return mdashboard.CategorizedDocsResponse{}, errors.New(constants.InvalidDashboardUserMessage)
	}

	loanDetails, err := dsr.LoanDBRepositoryProvider.DBGetLoanApplicationByParams(ctx, loansql.DBGetLoanApplicationParam{
		LoanApplicationID: req.LoanApplicationID,
	})
	if err != nil {
		logger.WithContext(ctx).Errorf("[GetDocumentsCont] Failed to fetch loan application for LoanApplicationID: %s, Error: %v", req.LoanApplicationID, err)
		return mdashboard.CategorizedDocsResponse{}, fmt.Errorf("no loan application exists with ID %s", req.LoanApplicationID)
	}

	// Get user employment type from loan application's primary applicant
	userDetails, err := dsr.UserDBRepositoryProvider.DBGetUserByParamsV2(ctx, usersql.DBGetUserInfoParam{
		UserID: loanDetails.UserID,
	})
	if err != nil {
		logger.WithContext(ctx).Errorf("[GetDocumentsCont] Failed to fetch user info req: %v, Error: %v", req, err)
		// TODO, send status as well from here and handle proper error message from caller
		return mdashboard.CategorizedDocsResponse{}, fmt.Errorf("no user exists with ID %s", req.UserID)
	}

	var (
		employmentType      string
		professionalDetails mdashboard.ProfessionalDetails
	)

	err = json.Unmarshal([]byte(userDetails.DynamicUserInfoStr), &professionalDetails)
	if err != nil {
		logger.WithContext(ctx).Errorf("[GetDocumentsCont] Error unmarshalling DynamicUserInfo: %+v, err %v", userDetails.DynamicUserInfoStr, err)
		return mdashboard.CategorizedDocsResponse{}, fmt.Errorf("failed to marshal dynamic user info for loanID: %s", req.LoanApplicationID)
	}

	if professionalDetails.EmploymentType == "" {
		return mdashboard.CategorizedDocsResponse{}, fmt.Errorf("empty employment type for loanID: %s", req.LoanApplicationID)
	}

	employmentType = professionalDetails.EmploymentType

	if len(req.DocIDs) > 0 {
		filteredDocIds = req.DocIDs
		documentReqType = mdashboard.DocIdsBasedDocumentReqType
	} else {
		configRes, err := dsr.ConfigManagementSrvRepositoryProvider.LegacyProvider().GetConfigInfoByParam(ctx, &configmanagement.GetConfigInfoParam{
			ResourceName: "document_dropdown",
		}, configmanagement.ConfigKeyValueMap{
			"lender_id": lenderUser.LenderID,
		})

		if err != nil {
			logger.WithContext(ctx).Errorf("[GetDocumentsCont] error in getting config for lenderID:%s, err:%v", lenderUser.LenderID, err)
			return mdashboard.CategorizedDocsResponse{}, err
		}

		resData, err := GetFilteredDropDownDataRecursive(ctx, configRes.Config, req.CustomCategories, req.ReturnAllLeafNodes)
		if err != nil {
			logger.WithContext(ctx).Errorf("[GetDocumentsCont] error parsing config for customCategory:%+v, err:%v", req.CustomCategories, err)
			return mdashboard.CategorizedDocsResponse{}, err
		}

		filteredDocIds, categoryMap, err = extractFilteredDocumentIDs(resData, employmentType)
		if err != nil {
			logger.WithContext(ctx).Errorf("[GetDocumentsCont] error extracting document IDs: %v", err)
			return mdashboard.CategorizedDocsResponse{}, err
		}

		logger.WithContext(ctx).Infof("[GetDocumentsCont] extracted document IDs: %+v", filteredDocIds)
		documentReqType = mdashboard.CategoryBasedDocumentsReqType
	}

	// Using WaitGroup for parallel database calls
	var (
		wg              sync.WaitGroup
		dashboardResult mdashboard.DocResult
		kycResult       mdashboard.DocResult
	)

	// Add two tasks to the WaitGroup
	wg.Add(2)

	// Goroutine for fetching dashboard docs
	go func() {
		defer wg.Done()
		additionalDocsResp, err := documentshelper.GetDashboardDocumentsWithMedia(ctx, loanDetails.LoanApplicationID, req.UserID)
		dashboardResult = mdashboard.DocResult{Docs: mapKYCDataListToDocRespList(additionalDocsResp), Err: err}
	}()

	// Goroutine for fetching KYC docs based on user type
	go func() {
		defer wg.Done()
		if isCoApplicant(req.UserID) {
			docs, err := kycutils.GetCoApplicantKYCDashboardDocs(req.UserID)
			kycDataResp := lo.Map(docs, func(item kycutils.CoApplicantKYCData, _ int) kycutils.KYCDataResp {
				return item.KYCDataResp
			})
			kycResult = mdashboard.DocResult{Docs: mapKYCDataListToDocRespList(kycDataResp), Err: err}
		} else {
			docs, err := kycutils.GetKYCDashboardDocs(req.LoanApplicationID)
			kycDataResp := lo.Map(docs, func(item kycutils.LoanKYCDataResp, _ int) kycutils.KYCDataResp {
				return item.KYCDataResp
			})
			kycResult = mdashboard.DocResult{Docs: mapKYCDataListToDocRespList(kycDataResp), Err: err}
		}
	}()

	// Wait for both goroutines to complete
	wg.Wait()

	// Check results for errors
	if dashboardResult.Err != nil {
		logger.WithContext(ctx).Errorf("[GetDocumentsCont] error fetching dashboard docs: %v", dashboardResult.Err)
	}

	if kycResult.Err != nil {
		logger.WithContext(ctx).Errorf("[GetDocumentsCont] error fetching KYC docs: %v", kycResult.Err)
	}

	// Combine results
	combinedDocsInfo := combineDocsInfo(dashboardResult.Docs, kycResult.Docs)

	// Filter if needed
	if len(filteredDocIds) > 0 {
		// Convert filter array to map for quick lookup
		docIDSet := lo.SliceToMap(filteredDocIds, func(id string) (string, bool) {
			return id, true
		})

		// Filter the combined docs
		combinedDocsInfo = lo.Filter(combinedDocsInfo, func(doc mdashboard.DocResp, _ int) bool {
			_, exists := docIDSet[doc.DocumentID]
			return exists
		})
	}

	response, err := makeGetDocumentsResponse(combinedDocsInfo, categoryMap, dashboardResult, documentReqType, req)
	if err != nil {
		logger.WithContext(ctx).Errorf("[GetDocumentsCont] error creating final response: err: %v", err)
		return mdashboard.CategorizedDocsResponse{}, errors.New(constants.GenericInternalIssuesMessage)
	}
	return response, nil
}

func makeGetDocumentsResponse(combinedDocsInfo []mdashboard.DocResp, categoryMap map[string][]string, dashboardDocs mdashboard.DocResult, documentReqType string, req mdashboard.GetDocumentsParam) (mdashboard.CategorizedDocsResponse, error) {

	for i := range combinedDocsInfo {
		combinedDocsInfo[i].IsEditable = false
	}
	for i := range dashboardDocs.Docs {
		dashboardDocs.Docs[i].IsEditable = false
	}

	// Create the categorized response
	var response mdashboard.CategorizedDocsResponse

	switch documentReqType {
	case mdashboard.CategoryBasedDocumentsReqType:
		response = createCategorizedResponse(combinedDocsInfo, categoryMap)
		response = handleAdditionalDocsSeparately(response, dashboardDocs, categoryMap)

		if len(req.FilteredCategories) == 0 {
			return response, nil
		}

		response = mdashboard.CategorizedDocsResponse{
			CategorisedData: lo.PickByKeys(response.CategorisedData, req.FilteredCategories),
		}

	case mdashboard.DocIdsBasedDocumentReqType:
		response = mdashboard.CategorizedDocsResponse{
			FilteredData: combinedDocsInfo,
		}
	}

	return response, nil
}

// combineDocsInfo combines two lists of documents, removing duplicates by DocID
// If a document with the same DocID exists in both lists, the one from primaryDocs is kept
func combineDocsInfo(docLists ...[]mdashboard.DocResp) []mdashboard.DocResp {
	// If there are no lists, return an empty result
	if len(docLists) == 0 {
		return []mdashboard.DocResp{}
	}

	// Use a map to track which document IDs we've already seen
	seenDocIDs := make(map[string]bool)

	// Initialize the result slice with an appropriate capacity
	totalCapacity := 0
	for _, list := range docLists {
		totalCapacity += len(list)
	}
	result := make([]mdashboard.DocResp, 0, totalCapacity)

	// Process each list in order of priority
	for _, docs := range docLists {
		for _, doc := range docs {
			// Only add the document if we haven't seen its ID before
			if !seenDocIDs[doc.DocumentID] {
				seenDocIDs[doc.DocumentID] = true
				result = append(result, doc)
			}
		}
	}

	return result
}

func extractFilteredDocumentIDs(resData interface{}, employmentType string) ([]string, map[string][]string, error) {
	// resData should be a slice of maps from the recursive result
	resultSlice, ok := resData.([]interface{})
	if !ok {
		return nil, nil, fmt.Errorf("unexpected result type: %T", resData)
	}

	docIDs := make([]string, 0, len(resultSlice))
	categoryMap := make(map[string][]string)

	for _, item := range resultSlice {
		// Each item should be a map with "value", "data", and "path" keys
		mapItem, ok := item.(map[string]interface{})
		if !ok {
			return nil, nil, fmt.Errorf("unexpected item type: %T", item)
		}

		// Extract the "value" field
		value, ok := mapItem["value"]
		if !ok {
			return nil, nil, fmt.Errorf("item does not contain 'value' field")
		}

		// Extract the "path" field
		pathInterface, ok := mapItem["path"]
		if !ok {
			return nil, nil, fmt.Errorf("item does not contain 'path' field")
		}

		// Handle different path types
		var pathSlice []string

		// Check if it's already a string slice
		if strSlice, ok := pathInterface.([]string); ok {
			pathSlice = strSlice
		} else if interfaceSlice, ok := pathInterface.([]interface{}); ok {
			// Convert interface slice to string slice
			pathSlice = make([]string, 0, len(interfaceSlice))
			for _, p := range interfaceSlice {
				if pStr, isStr := p.(string); isStr {
					pathSlice = append(pathSlice, pStr)
				}
			}
		} else {
			return nil, nil, fmt.Errorf("path is not a recognized slice type: %T", pathInterface)
		}

		// Filter by employment type if specified
		if employmentType != "" {
			// Check if employment type is in the path
			hasEmploymentType := false
			for _, p := range pathSlice {
				if p == employmentType {
					hasEmploymentType = true
					break
				}
			}

			// Skip this item if it doesn't contain the required employment type
			if !hasEmploymentType {
				continue
			}
		}

		// Convert value to string
		valueStr, ok := value.(string)
		if !ok {
			return nil, nil, fmt.Errorf("value is not a string: %T", value)
		}

		// Add to flat list
		docIDs = append(docIDs, valueStr)

		// Add to category map if we have a path
		if len(pathSlice) > 0 {
			// Get top-level category from the path
			category := pathSlice[0]
			if _, exists := categoryMap[category]; !exists {
				categoryMap[category] = make([]string, 0)
			}
			categoryMap[category] = append(categoryMap[category], valueStr)
		}
	}

	return docIDs, categoryMap, nil
}

// createCategorizedResponse organizes documents by category and filters empty categories
func createCategorizedResponse(docs []mdashboard.DocResp, categoryMap map[string][]string) mdashboard.CategorizedDocsResponse {
	response := mdashboard.CategorizedDocsResponse{
		CategorisedData: make(map[string][]mdashboard.DocResp),
	}

	// If no category map, just return empty response
	if len(categoryMap) == 0 {
		return response
	}

	// Create a map of docIDs to DocsInfo for efficient lookup
	docMap := make(map[string]mdashboard.DocResp)
	for _, doc := range docs {
		docMap[doc.DocumentID] = doc
	}

	// Organize docs by category
	for category, docIDs := range categoryMap {
		// Create an array to hold documents for this category
		categoryDocs := make([]mdashboard.DocResp, 0, len(docIDs))

		// Find matching documents
		for _, docID := range docIDs {
			if doc, exists := docMap[docID]; exists {
				categoryDocs = append(categoryDocs, doc)
			}
		}

		// Only add the category if it has at least one document
		if len(categoryDocs) > 0 {
			response.CategorisedData[category] = categoryDocs
		}
	}

	return response
}

// mapKYCDataToDocResp maps a KYCDataResp to a DocResp
func mapKYCDataToDocResp(kycData kycutils.KYCDataResp) mdashboard.DocResp {
	return mdashboard.DocResp{
		FrontMediaID:     kycData.FrontMediaID,
		BackMediaID:      kycData.BackMediaID,
		DocType:          kycData.DocType,
		DocumentName:     kycData.DocumentName,
		Status:           kycData.Status,
		StatusText:       kycData.StatusText,
		DocumentID:       kycData.DocumentID,
		RejectionReason:  kycData.RejectionReason,
		ReviewStatus:     kycData.ReviewStatus,
		ReviewStatusText: kycData.ReviewStatusText,
		IsEditable:       kycData.IsEditable,
		Password:         kycData.Password,
		DocID:            kycData.DocID,
		CreatedAt:        kycData.CreatedAt,
	}
}

// mapKYCDataListToDocRespList maps a slice of KYCDataResp to a slice of DocResp
func mapKYCDataListToDocRespList(kycDataList []kycutils.KYCDataResp) []mdashboard.DocResp {
	result := make([]mdashboard.DocResp, len(kycDataList))
	for i, kycData := range kycDataList {
		result[i] = mapKYCDataToDocResp(kycData)
	}
	return result
}

/*
 * We required this special handling for additional docs because for one documentID we can have multiple
 * media's present also, in previous we didn't store documentID in dashboard_docs and media table and we
 * need to return that data as well. Hence we require separate handling.
 * Below we are handling the scenario if the response created has "Additional Docs" key present, then we
 * return all data that is returned from the db function.
 */
func handleAdditionalDocsSeparately(response mdashboard.CategorizedDocsResponse, dashboardResult mdashboard.DocResult, categoryMap map[string][]string) mdashboard.CategorizedDocsResponse {

	// Check if "Additional Docs" category already exists in the map
	if categoryMap[constants.AdditionalDocs] == nil {
		return response
	}

	// Early return if dashboardResult has an error
	if dashboardResult.Err != nil {
		return response
	}

	// Filter to keep ONLY documents with empty DocumentID or matching additionalDocsDocumentID using lo library
	filteredDocs := lo.Filter(dashboardResult.Docs, func(doc mdashboard.DocResp, _ int) bool {
		return doc.DocumentID == "" || doc.DocumentID == constants.AdditionalDocCategoryDocumentID
	})

	// Only update if filteredDocs is not empty
	if len(filteredDocs) > 0 {
		response.CategorisedData[constants.AdditionalDocs] = filteredDocs
	} else {
		delete(response.CategorisedData, constants.AdditionalDocs)
	}

	return response
}

func (dsr *DashboardServiceRepository) DownloadDocumentsCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusInternalServerError)

		ctx := r.Context()
		attributes := ctx.Value("attributes").(map[string]interface{})
		req := attributes["req"].(mdashboard.DownloadDocumentsParam)

		// Convert the download request to a get documents request format
		getDocsReq := mdashboard.GetDocumentsParam{
			LoanApplicationID:  req.LoanApplicationID,
			UserID:             req.UserID,
			ReturnAllLeafNodes: true,
		}

		if req.FilterCategories != nil {
			getDocsReq.CustomCategories = []string{}
			getDocsReq.FilteredCategories = req.FilterCategories
		} else {
			getDocsReq.DocIDs = req.DocIDs
		}

		// Use the common function to get documents
		docsResponse, err := dsr.GetDocumentsByParams(ctx, getDocsReq)
		if err != nil {
			logger.WithContext(ctx).Errorf("[DownloadDocumentsCont] Error fetching documents for req: %v, err: %v", getDocsReq, err)
			errorHandler.CustomError(w, http.StatusInternalServerError, "Error fetching documents")
			return
		}

		// Generate unique S3 key for this download
		outputKey := fmt.Sprintf("downloads/%s/%s_%s.zip", req.LoanApplicationID, req.UserID, uuid.New().String())

		// Prepare files for zip
		filesToZip := []zipper.ZipFileStruct{}

		for category, docs := range docsResponse.CategorisedData {
			for i, doc := range docs {
				// For each document
				fileName := sanitizeFileName(doc.DocumentName)
				if fileName == "" {
					fileName = fmt.Sprintf("%s_doc_%d", category, i+1)
				}

				if doc.FrontMediaID != "" {
					frontExtension, err := getFileExtension(doc.FrontMediaID)
					if err != nil {
						logger.WithContext(ctx).Warnf("Could not determine file extension for front media doc: %+v, err: %v", doc, err)
					} else {
						filesToZip = append(filesToZip, zipper.ZipFileStruct{
							// adding uuid to avoid collision
							Name: fmt.Sprintf("%s/%s_front%s%s", category, fileName, uuid.New().String()[:5], frontExtension),
							URL:  doc.FrontMediaID,
						})
					}
				}

				// Add back media if available (removed the incorrect doc.FrontMediaID = "wer" line)
				if doc.BackMediaID != "" {
					backExtension, err := getFileExtension(doc.BackMediaID)
					if err != nil {
						logger.WithContext(ctx).Warnf("Could not determine file extension for back media doc: %+v, err: %v", doc, err)
					} else {
						filesToZip = append(filesToZip, zipper.ZipFileStruct{
							// adding uuid to avoid collision
							Name: fmt.Sprintf("%s/%s_back%s%s", category, fileName, uuid.New().String()[:5], backExtension),
							URL:  doc.BackMediaID,
						})
					}

				}
			}
		}

		// Process filtered data if present
		if len(docsResponse.FilteredData) > 0 {
			for i, doc := range docsResponse.FilteredData {
				fileName := sanitizeFileName(doc.DocumentName)
				if fileName == "" {
					fileName = fmt.Sprintf("document_%d", i+1)
				}

				// Add front media if available
				if doc.FrontMediaID != "" {
					frontExtension, err := getFileExtension(doc.FrontMediaID)
					if err != nil {
						logger.WithContext(ctx).Warnf("Could not determine file extension for front media doc: %+v, err: %v", doc, err)
					} else {
						filesToZip = append(filesToZip, zipper.ZipFileStruct{
							// adding uuid to avoid collision
							Name: fileName + "_front" + uuid.New().String()[:5] + frontExtension,
							URL:  doc.FrontMediaID,
						})
					}
				}

				// Add back media if available
				if doc.BackMediaID != "" {
					backExtension, err := getFileExtension(doc.BackMediaID)
					if err != nil {
						logger.WithContext(ctx).Warnf("Could not determine file extension for back media doc: %+v, err: %v", doc, err)
					} else {
						filesToZip = append(filesToZip, zipper.ZipFileStruct{
							// adding uuid to avoid collision
							Name: fileName + "_back" + uuid.New().String()[:5] + backExtension,
							URL:  doc.BackMediaID,
						})
					}
				}
			}
		}

		// Check if we have any files to zip
		if len(filesToZip) == 0 {
			errorHandler.CustomError(w, http.StatusNotFound, "No documents found for download")
			return
		}

		// Create and upload the zip file
		err = zipper.CreateAndUploadZipFileWithoutPassword(filesToZip, outputKey)
		if err != nil {
			logger.WithContext(ctx).Errorf("[DownloadDocumentsCont] Error creating zip file: %v", err)
			errorHandler.CustomError(w, http.StatusInternalServerError, "Error creating download package")
			return
		}

		// Generate download URL for the zip file
		downloadURL := s3.GetPresignedURLS3(outputKey, 15) // 15 minute expiry
		if downloadURL == "" {
			logger.WithContext(ctx).Errorf("[DownloadDocumentsCont] Error creating S3 link for key %s", outputKey)
		}

		// Return the download URL in the response
		response := map[string]interface{}{
			"downloadURL": downloadURL,
			"expiresIn":   "15 minutes",
		}

		ctx = context.WithValue(ctx, "resData", response)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// getFileExtension extracts the file extension from a URL
// Returns the extension with the dot prefix (e.g., ".jpg") or an error if no valid extension is found
func getFileExtension(url string) (string, error) {
	// Extract the path part of the URL (before any query parameters)
	pathPart := url
	if idx := strings.Index(url, "?"); idx > 0 {
		pathPart = url[:idx]
	}

	// Find the last occurrence of a period in the path part
	if lastDot := strings.LastIndex(pathPart, "."); lastDot >= 0 {
		// Extract everything after the last dot
		ext := pathPart[lastDot:]
		// Check if it's a valid file extension (simple check)
		if len(ext) >= 2 && len(ext) <= 5 && !strings.Contains(ext, "/") {
			return ext, nil
		}
	}

	// No valid extension found
	return "", fmt.Errorf("no valid file extension found in URL: %s", url)
}

func (dsr *DashboardServiceRepository) UploadMedia(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusInternalServerError)

		ctx := r.Context()
		userDashboard := r.Context().Value("user")

		var (
			masterUser authentication.MasterDashboardUserStruct
			email      string
			userID     string
			err        error
		)

		sourceEntityID, err := GetTypedRequestFromContext[string](ctx, "sourceEntityID")
		if err != nil {
			logger.WithContext(ctx).Errorf("[UploadMedia] failed to get sourceEntityID from context: %v", err)
			errorHandler.CustomError(w, http.StatusInternalServerError, constants.GenericInternalIssuesMessage)
			return
		}

		switch v := userDashboard.(type) {
		case authentication.LenderUserStruct:

		case authentication.MasterDashboardUserStruct:
			masterUser = v
			email = masterUser.Email
			userID = masterUser.MasterUserID
		default:
			log.WithContext(ctx).Errorf("[UploadMedia] invalid user")
			errorHandler.CustomError(w, http.StatusInternalServerError, constants.InvalidDashboardUserMessage)
			return
		}

		mediaID, httpCode, errStr, err := cservices.UploadFunc(r, "dashboardUser", userID, sourceEntityID, "", email)
		if errStr != "" {
			if httpCode != 0 {
				errorHandler.CustomError(w, httpCode, errStr)
			}
			logger.WithContext(ctx).Errorf("[UploadMedia] failed to upload media errStr: %v, email: %v", errStr, email)
			return
		}
		if err != nil {
			logger.WithContext(ctx).Errorf("[UploadMedia] error during attempt to upload media err: %v, email: %v", err, email)
			errorHandler.CustomError(w, http.StatusInternalServerError, constants.GenericInternalIssuesMessage)
			return
		}

		var resData = map[string]string{
			"mediaID": mediaID,
		}

		ctx = context.WithValue(r.Context(), "resData", resData)

		next.ServeHTTP(w, r.WithContext(ctx))
	})
}
