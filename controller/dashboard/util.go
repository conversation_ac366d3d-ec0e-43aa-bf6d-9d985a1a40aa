package dashboard

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"text/template"
)

// mapConfigPlaceHolders replaces placeholders in the config string with values from the data map.
func mapConfigPlaceHolders(
	config string,
	configName string,
	data map[string]interface{},
) (string, error) {

	tmpl, err := template.New(configName).Parse(config)
	if err != nil {
		return "", fmt.<PERSON><PERSON><PERSON>("[mapConfigPlaceHolders] failed to parse template: %v", err)
	}

	var buf bytes.Buffer
	if err := tmpl.Execute(&buf, data); err != nil {
		return "", fmt.Errorf("[mapConfigPlaceHolders] failed to parse template: %v", err)
	}

	return buf.String(), nil
}

// Helper function to sanitize file names
func sanitizeFileName(name string) string {
	name = strings.ReplaceAll(name, "/", "_")
	name = strings.ReplaceAll(name, "\\", "_")
	name = strings.ReplaceAll(name, ":", "_")
	name = strings.ReplaceAll(name, "*", "_")
	name = strings.ReplaceAll(name, "?", "_")
	name = strings.ReplaceAll(name, "\"", "_")
	name = strings.ReplaceAll(name, "<", "_")
	name = strings.ReplaceAll(name, ">", "_")
	name = strings.ReplaceAll(name, "|", "_")

	// Trim spaces
	name = strings.TrimSpace(name)

	return name
}

func GetTypedRequestFromContext[T any](ctx context.Context, key string) (T, error) {
	var zero T

	attr, ok := ctx.Value("attributes").(map[string]interface{})
	if !ok {
		return zero, fmt.Errorf("[GetTypedRequestFromContext] context attributes missing or invalid type")
	}

	val, exists := attr[key]
	if !exists {
		return zero, fmt.Errorf("[GetTypedRequestFromContext] key '%s' not found in context attributes", key)
	}

	typedVal, ok := val.(T)
	if !ok {
		return zero, fmt.Errorf("[GetTypedRequestFromContext] value under key '%s' has incorrect type", key)
	}

	return typedVal, nil
}

// SetKeysInJSON accepts a JSON input (string, []byte, or nil), sets new keys,
// and returns the modified result as json.RawMessage.
func SetKeysInJSON(jsonInput interface{}, newKeys map[string]interface{}) (json.RawMessage, error) {
	var jsonBytes []byte

	// Convert input to []byte based on type and handle empty cases
	switch v := jsonInput.(type) {
	case nil:
		jsonBytes = []byte(`{}`)
	case string:
		if v == "" {
			jsonBytes = []byte(`{}`)
		} else {
			jsonBytes = []byte(v)
		}
	case []byte:
		if len(v) == 0 {
			jsonBytes = []byte(`{}`)
		} else {
			jsonBytes = v
		}
	default:
		return nil, errors.New("jsonInput must be of type string, []byte, or nil")
	}

	// Unmarshal JSON into a map
	var data map[string]interface{}
	if err := json.Unmarshal(jsonBytes, &data); err != nil {
		return nil, fmt.Errorf("failed to parse JSON: %w", err)
	}

	// Add or overwrite keys
	for key, value := range newKeys {
		data[key] = value
	}

	// Marshal back to JSON
	modifiedJSON, err := json.Marshal(data)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal JSON: %w", err)
	}

	return json.RawMessage(modifiedJSON), nil
}
