package dashboard

import "testing"

func TestGetStringFromMap(t *testing.T) {
	tests := []struct {
		name     string
		input    map[string]interface{}
		key      string
		expected string
	}{
		{
			name:     "nil map",
			input:    nil,
			key:      "name",
			expected: "",
		},
		{
			name:     "missing key",
			input:    map[string]interface{}{"age": 30},
			key:      "name",
			expected: "",
		},
		{
			name:     "value is nil",
			input:    map[string]interface{}{"name": nil},
			key:      "name",
			expected: "",
		},
		{
			name:     "string value",
			input:    map[string]interface{}{"name": "<PERSON><PERSON>"},
			key:      "name",
			expected: "<PERSON><PERSON>",
		},
		{
			name:     "int value",
			input:    map[string]interface{}{"age": 30},
			key:      "age",
			expected: "30",
		},
		{
			name:     "int64 value",
			input:    map[string]interface{}{"score": int64(9876543210)},
			key:      "score",
			expected: "9876543210",
		},
		{
			name:     "float64 value",
			input:    map[string]interface{}{"price": 99.99},
			key:      "price",
			expected: "99.99",
		},
		{
			name:     "float32 value",
			input:    map[string]interface{}{"height": float32(5.8)},
			key:      "height",
			expected: "5.8",
		},
		{
			name:     "bool true",
			input:    map[string]interface{}{"isAdmin": true},
			key:      "isAdmin",
			expected: "true",
		},
		{
			name:     "bool false",
			input:    map[string]interface{}{"isActive": false},
			key:      "isActive",
			expected: "false",
		},
		{
			name: "fmt.Stringer value",
			input: map[string]interface{}{
				"user": stringerMock{"TestUser"},
			},
			key:      "user",
			expected: "TestUser",
		},
		{
			name: "unsupported type (slice)",
			input: map[string]interface{}{
				"tags": []string{"go", "docker"},
			},
			key:      "tags",
			expected: "[go docker]",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := GetStringFromMap(tt.input, tt.key)
			if got != tt.expected {
				t.Errorf("GetStringFromMap() = %q, want %q", got, tt.expected)
			}
		})
	}
}

// stringerMock implements fmt.Stringer for testing.
type stringerMock struct {
	value string
}

func (s stringerMock) String() string {
	return s.value
}
