package dashboard

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"reflect"
	"strings"

	"finbox/go-api/authentication"
	"finbox/go-api/constants"
	"finbox/go-api/errorHandler"
	fgraphql "finbox/go-api/functions/graphql"
	"finbox/go-api/functions/logger"
	"finbox/go-api/functions/taskmanagement"
	"finbox/go-api/functions/taskmanagement/event"
	"finbox/go-api/functions/taskmanagement/model"
	fbxerrors "finbox/go-api/internal/fbxerrors"
	configmanagementsql "finbox/go-api/internal/repository/psql/configmanagement"
	dashboardsql "finbox/go-api/internal/repository/psql/dashboard"
	taskmanagementsql "finbox/go-api/internal/repository/psql/taskmanagement"
	workflowinstancesql "finbox/go-api/internal/repository/psql/workflowinstance"
	"finbox/go-api/internal/service/bpmworkflowmanagement"
	configmanagement "finbox/go-api/internal/service/configmanagement"
	uidashboard "finbox/go-api/internal/service/uiconfigmanagement/dashboard"
	mdashboard "finbox/go-api/models/dashboard"
	"finbox/go-api/utils/general"
)

func WorkflowDataCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()
		attributes := ctx.Value("attributes").(map[string]interface{})
		req := attributes["req"].(mdashboard.WorkflowDataReq)

		var (
			masterTaskID   = req.MasterTaskID
			dashboard      = r.Context().Value("dashboard").(string)
			userType       = r.Context().Value("user")
			lenderUser     authentication.LenderUserStruct
			sourceEntityID = attributes["sourceEntityID"].(string)
			masterUser     authentication.MasterDashboardUserStruct
			res            = &mdashboard.WorkflowDataResponse{}
			rbacGroupName  string
			userEmail      string
		)
		switch v := userType.(type) {
		case authentication.LenderUserStruct:
			lenderUser = v
		case authentication.MasterDashboardUserStruct:
			masterUser = v
		default:
			log.WithContext(ctx).Errorf("[WorkflowDataCont] invalid user")
			errorHandler.CustomError(w, http.StatusInternalServerError, constants.InvalidDashboardUserMessage)
			return
		}

		if dashboard == constants.LenderDashboardRef {
			rbacGroupName = lenderUser.RBACGroupName
			userEmail = lenderUser.Email
		} else {
			rbacGroupName = masterUser.SourceEntityWithGroupMapping[sourceEntityID]
			rbacGroupName = strings.Split(masterUser.SourceEntityWithGroupMapping[sourceEntityID], "#")[1]
			userEmail = masterUser.Email
		}

		rbacGroupName = strings.TrimSpace(rbacGroupName)
		workflowInstanceRepo := workflowinstancesql.NewWorkflowInstancesRepository(database)

		workflowInstanceParam := workflowinstancesql.DBGetWorkflowInstancesParam{
			Id: req.WorkFlowInstanceID,
		}

		workflowInstanceInfo, err := workflowInstanceRepo.DBGetWorkflowInstancesByParams(ctx, &workflowInstanceParam, nil)
		if err != nil {
			log.WithContext(ctx).Errorf("[WorkflowDataCont] failed to get workflow instance by params. err: %v, workflowInstanceParam: %+v", err, workflowInstanceParam)
			errorHandler.ReportToSentryV2(ctx, err)
			errorHandler.CustomError(w, http.StatusInternalServerError, constants.GenericInternalIssuesMessage)
		}

		configManagementRepo := configmanagementsql.NewConfigManagementDBRepository(database)
		configData, err := configManagementRepo.DBGetConfigInfoLatestVersionByID(ctx, &configmanagementsql.DBGetConfigInfoParam{
			Id: workflowInstanceInfo.WorkflowID,
		})
		if err != nil {
			logger.WithContext(ctx).Errorf("[WorkflowDataCont] error getting config data. err: %v, req:%v", err, req)
			errorHandler.CustomError(w, http.StatusInternalServerError, constants.ErrorGettingConfigData)
			return
		}

		workflow, err := bpmworkflowmanagement.ParseWorkflowJSON(configData.Config)
		if err != nil {
			logger.WithContext(ctx).Errorf("[WorkflowDataCont] error parsing config data for masterTaskID:%s, err:%v", masterTaskID, err)
			errorHandler.CustomError(w, http.StatusInternalServerError, constants.GenericInternalIssuesMessage)
			return
		}

		data := workflow.States[workflowInstanceInfo.CurrentState]
		res.WorkflowInstanceID = workflowInstanceInfo.ID
		res.Reassignment = data.Reassignment.ReassignmentGroups

		res.Action = filterPermittedActions(userEmail, workflowInstanceInfo.AssignedTo, rbacGroupName, data.Actions)
		res.GroupName = workflowInstanceInfo.AssignedGroup
		ctx = context.WithValue(ctx, "resData", res)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func filterPermittedActions(email, assignedTo, rbacGroupName string, actions []bpmworkflowmanagement.Action) map[string]string {
	actionToGroupMap := make(map[string]string)

	if assignedTo != "" && assignedTo != email {
		return actionToGroupMap // return empty map
	}

	for _, action := range actions {
		if len(action.Permissions.AllowedGroups) == 0 || general.InArr(rbacGroupName, action.Permissions.AllowedGroups) {
			actionToGroupMap[action.ID] = action.Name
		}
	}

	return actionToGroupMap
}

func (dsr *DashboardServiceRepository) WorkflowActionCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {

		var (
			ctx            = r.Context()
			attributes     = r.Context().Value("attributes").(map[string]interface{})
			userType       = r.Context().Value("user")
			dashboard      = r.Context().Value("dashboard").(string)
			req            = attributes["req"].(mdashboard.WorkflowActionReq)
			lenderUser     authentication.LenderUserStruct
			masterUser     authentication.MasterDashboardUserStruct
			sourceEntityID = attributes["sourceEntityID"].(string)
			rbacGroupName  string
			email          string
			taskID         string
			token          = r.Header.Get(constants.AuthorizationToken)
			resp           *bpmworkflowmanagement.ExecuteActionFuncResp
		)

		switch v := userType.(type) {
		case authentication.LenderUserStruct:
			lenderUser = v
		case authentication.MasterDashboardUserStruct:
			masterUser = v
		default:
			log.WithContext(ctx).Errorf("[WorkflowActionCont] invalid user")
			errorHandler.CustomError(w, http.StatusInternalServerError, constants.InvalidDashboardUserMessage)
			return
		}

		if dashboard == constants.LenderDashboardRef {
			rbacGroupName = lenderUser.RBACGroupName
			email = lenderUser.GetEmail()
		} else {
			rbacGroupName = masterUser.SourceEntityWithGroupMapping[sourceEntityID]
			rbacGroupName = strings.Split(masterUser.SourceEntityWithGroupMapping[sourceEntityID], "#")[1]
			email = masterUser.GetEmail()
		}

		workflowInstanceRepo := workflowinstancesql.NewWorkflowInstancesRepository(database)

		workflowInstanceParam := workflowinstancesql.DBGetWorkflowInstancesParam{
			Id: req.WorkflowInstanceID,
		}

		workflowInstanceInfo, err := workflowInstanceRepo.DBGetWorkflowInstancesByParams(ctx, &workflowInstanceParam, nil)
		if err != nil {
			log.WithContext(ctx).Errorf("[WorkflowActionCont] failed to get workflow instance by params. err: %v, workflowInstanceParam: %+v", err, workflowInstanceParam)
			errorHandler.ReportToSentryV2(ctx, err)
			errorHandler.CustomError(w, http.StatusInternalServerError, constants.GenericInternalIssuesMessage)
		}

		if strings.ToLower(workflowInstanceInfo.IdentifierType) == workflowinstancesql.IdentifierType_Task {
			taskID = workflowInstanceInfo.IdentifierID
		}

		configManagementRepo := configmanagementsql.NewConfigManagementDBRepository(database)
		configData, err := configManagementRepo.DBGetConfigInfoLatestVersionByID(ctx, &configmanagementsql.DBGetConfigInfoParam{
			Id: workflowInstanceInfo.WorkflowID,
		})
		if err != nil {
			logger.WithContext(ctx).Errorf("[WorkflowActionCont] error getting config data. err: %v, req:%v", err, req)
			errorHandler.CustomError(w, http.StatusInternalServerError, constants.GenericInternalIssuesMessage)
			return
		}

		workflow, err := bpmworkflowmanagement.ParseWorkflowJSON(configData.Config)
		if err != nil {
			logger.WithContext(ctx).Errorf("[WorkflowActionCont] failed to parse workflow JSON: %v", err)
			errorHandler.CustomError(w, http.StatusInternalServerError, constants.GenericInternalIssuesMessage)
			return
		}

		if req.AdditionalData != nil {
			if dataMap, ok := req.AdditionalData.(map[string]interface{}); ok {
				dataMap["id"] = workflowInstanceInfo.IdentifierID
			}
			data, err := json.Marshal(req.AdditionalData)
			if err != nil {
				log.WithContext(ctx).Errorf("[WorkflowActionCont] Failed to marshal additional data: %v, error: %v", req, err)
				errorHandler.CustomError(w, http.StatusBadRequest, err.Error())
				return
			}
			taskManagementService := taskmanagement.NewTaskManagementService(event.Factory)
			err = taskManagementService.HandleEvent(ctx, email, req.IdentifierType, req.IdentifierID, model.Event{
				Event:     taskmanagementsql.UpdateTask.String(),
				EventData: data,
			})

			if err != nil {
				log.WithContext(ctx).Errorf("[WorkflowActionCont] Failed to handle event: %v, error: %v", req.Event, err)
				errorHandler.CustomError(w, http.StatusInternalServerError, err.Error())
				return
			}
		}

		actionResult, err := workflow.ValidateActionFetchNextState(workflowInstanceInfo.CurrentState, string(rbacGroupName), req.Action)
		if err != nil {
			logger.WithContext(ctx).Errorf("[WorkflowActionCont] failed to ValidateActionFetchNextState. err: %v, req: %v", err, req)
			errorHandler.CustomError(w, http.StatusInternalServerError, constants.GenericInternalIssuesMessage)
			return
		}

		if actionResult.Transition == nil {
			logger.WithContext(ctx).Errorf("[WorkflowActionCont] invalid action. err: %v, req: %v", err, req)
			errorHandler.CustomError(w, http.StatusConflict, constants.GenericInternalIssuesMessage)
			return
		}
		var assignedEmail string

		tx, err := database.Beginx()
		if err != nil {
			logger.WithContext(ctx).Errorf("[WorkflowActionCont] failed to begin tx. err: %v, req: %v", err, req)
			errorHandler.CustomError(w, http.StatusInternalServerError, constants.GenericInternalIssuesMessage)
			return
		}

		if len(actionResult.ValidateActions) != 0 {
			configPlaceHoldersMap := make(map[string]interface{})
			configPlaceHoldersMap["LoanApplicationID"] = req.LoanApplicationID
			configPlaceHoldersMap["WorkflowInstanceID"] = req.WorkflowInstanceID

			err := dsr.validateWorkflowAction(ctx, actionResult, workflow, configPlaceHoldersMap)
			if err != nil {
				logger.WithContext(ctx).Errorf("[WorkflowActionCont] failed to validate workflow action. err: %v, req: %v", err, req)
				if fbxerrors.IsValidationError(err) {
					errorHandler.CustomError(w, http.StatusConflict, err.Error())
				} else {
					errorHandler.CustomError(w, http.StatusInternalServerError, constants.GenericInternalIssuesMessage)
				}
				return
			}
		}

		var execFuncReq bpmworkflowmanagement.ActionFuncRefExecReq
		execFuncReq.WorkflowName = workflow.ID
		if actionResult.FuncRef != nil {
			execFuncReq.FuncRef = actionResult.FuncRef
			execFuncReq.FuncRef.Parameters = map[string]interface{}{
				"workflowInstanceID": req.WorkflowInstanceID,
				"group":              rbacGroupName,
				"nextGroup":          actionResult.Transition.Next.GroupName,
				"user":               userType,
				"data":               req,
				"token":              token,
				"loanApplicationID":  req.IdentifierID,
				"taskID":             taskID,
			}

			execFuncReq.Tx = tx

			resp, err = bpmworkflowmanagement.ExecuteActionFunc(ctx, &execFuncReq)
			if err != nil {
				logger.WithContext(ctx).Errorf("[WorkflowActionCont] err in validation ref action activity. err: %v, req: %v", err, req)
				errorHandler.CustomError(w, http.StatusInternalServerError, err.Error())
				return
			}
		}

		newMetadataKeys := make(map[string]interface{})
		// setting stateName in metadata
		newMetadataKeys["stateName"] = workflow.States[actionResult.Transition.Next.StateName].Name
		rawWorkflowInstanceMetadata, err := SetKeysInJSON(workflowInstanceInfo.Metadata, newMetadataKeys)
		if err != nil {
			logger.WithContext(ctx).Errorf("[WorkflowActionCont] failed to set metadat keys. err: %v, req: %v", err, req)
			errorHandler.CustomError(w, http.StatusInternalServerError, err.Error())
			return
		}

		var updateWorkflowInstanceParam workflowinstancesql.DBUpdateWorkflowInstancesParam
		updateWorkflowInstanceParam.WorkflowInstanceID = req.WorkflowInstanceID
		updateWorkflowInstanceParam.CurrentState = actionResult.Transition.Next.StateName
		updateWorkflowInstanceParam.AssignedGroup = actionResult.Transition.Next.GroupName
		updateWorkflowInstanceParam.Metadata = &rawWorkflowInstanceMetadata

		if resp != nil && resp.Data != nil {
			stateName := GetStringFromMap(resp.Data, "stateName")
			if stateName != "" {
				updateWorkflowInstanceParam.CurrentState = stateName
			}
			groupName := GetStringFromMap(resp.Data, "groupName")
			if groupName != "" {
				updateWorkflowInstanceParam.AssignedGroup = groupName
			}
		}

		if req.Parameters["AssignedEmail"] != nil {
			if _, ok := req.Parameters["AssignedEmail"].(string); !ok {
				logger.WithContext(ctx).Errorf("[WorkflowActionCont] AssignedEmail is not valid")
			}
			assignedEmail = req.Parameters["AssignedEmail"].(string)
		}
		updateWorkflowInstanceParam.AssignedTo = assignedEmail

		defer tx.Rollback()
		err = workflowInstanceRepo.DBUpdateWorkflowInstances(ctx, tx, &updateWorkflowInstanceParam)
		if err != nil {
			logger.WithContext(ctx).Errorf("[WorkflowActionCont] failed to transist workflow to next state. err: %v, req: %v", err, req)
			errorHandler.CustomError(w, http.StatusInternalServerError, constants.GenericInternalIssuesMessage)
			return
		}

		var insertWorkHistoryParam workflowinstancesql.DBInsertWorkflowInstanceHistoryParams
		insertWorkHistoryParam.WorkflowInstanceID = req.WorkflowInstanceID
		insertWorkHistoryParam.PreviousState = workflowInstanceInfo.CurrentState
		insertWorkHistoryParam.CurrentState = updateWorkflowInstanceParam.CurrentState
		insertWorkHistoryParam.Action = req.Action
		insertWorkHistoryParam.ActionBy = email
		insertWorkHistoryParam.AssignedGroup = workflowInstanceInfo.AssignedGroup
		insertWorkHistoryParam.AssignedTo = workflowInstanceInfo.AssignedTo
		insertWorkHistoryParam.Remarks = req.Remarks

		err = workflowInstanceRepo.DBInsertWorkflowInstanceHistory(ctx, tx, &insertWorkHistoryParam)
		if err != nil {
			logger.WithContext(ctx).Errorf("[WorkflowActionCont] failed to insert workflow transist history. err: %v, req: %v", err, req)
			errorHandler.CustomError(w, http.StatusInternalServerError, constants.GenericInternalIssuesMessage)
			return
		}

		if actionResult.TaskEvent != nil {
			var eventData []byte
			if actionResult.TaskEvent.AdditionalData != nil {
				eventData, err = json.Marshal(actionResult.TaskEvent.AdditionalData)
				if err != nil {
					logger.WithContext(ctx).Errorf("[WorkflowActionCont] failed to marshalling event data. err: %v, req: %v", err, req)
					errorHandler.CustomError(w, http.StatusInternalServerError, constants.GenericInternalIssuesMessage)
				}
			}

			err = dsr.TaskManagementService.HandleEvent(ctx, email, mdashboard.LoanApplicationID.String(), req.LoanApplicationID, model.Event{
				Event:     actionResult.TaskEvent.Event,
				EventData: eventData,
			})
			if err != nil {
				logger.WithContext(ctx).Errorf("[WorkflowActionCont] failed to handle task event. err: %v, req: %v", err, req)
				errorHandler.CustomError(w, http.StatusInternalServerError, constants.GenericInternalIssuesMessage)
				return
			}
		}

		err = tx.Commit()
		if err != nil {
			logger.WithContext(ctx).Errorf("[WorkflowActionCont] failed to commit during transist. err: %v, req: %v", err, req)
			errorHandler.CustomError(w, http.StatusInternalServerError, constants.GenericInternalIssuesMessage)
			return
		}

		resData := map[string]string{
			"msg": "success",
		}

		if IsActionNotAllowedForSendingMail(req.Action, lenderUser.LenderID, masterUser.OrganizationID) {

			ctx = context.WithValue(ctx, "resData", resData)
			next.ServeHTTP(w, r.WithContext(ctx))
			return
		}

		task, err := taskmanagementsql.DBGetTasksByParams(ctx, &taskmanagementsql.DBGetTasksParam{
			ID: &workflowInstanceInfo.IdentifierID,
		}, nil)
		if err != nil {
			logger.WithContext(ctx).Errorf("[SendEmailAbfl] failed to fetch task for id :%+v, err:%v", workflowInstanceInfo.IdentifierID, err)
			errorHandler.CustomError(w, http.StatusInternalServerError, constants.GenericInternalIssuesMessage)
			return
		}

		taskCreatordBGetLenderUsersResponse, err := dashboardsql.DBGetLenderUsersByParams(ctx, &dashboardsql.DBGetLenderUsersParam{Email: &task.CreatedBy}, &dashboardsql.DBGetLenderUsersFields{RbacGroupName: true})
		if err != nil {
			logger.WithContext(ctx).Errorf("[sendEmailConfirmation] Failed to get RBAC group for user email: %+v. Error: %v", task.CreatedBy, err)
			errorHandler.CustomError(w, http.StatusInternalServerError, constants.GenericInternalIssuesMessage)
			return
		}

		err = event.SendEmailAbfl(ctx, event.EmailDataInput{
			TaskName:          task.Name,
			TaskCreatorGroup:  string(taskCreatordBGetLenderUsersResponse.RbacGroupName),
			NextGroup:         updateWorkflowInstanceParam.AssignedGroup,
			LoanApplicationID: req.LoanApplicationID,
			FlowType:          event.FlowTypeAssignment,
			CurrentGroup:      rbacGroupName,
		})
		if err != nil {
			logger.WithContext(ctx).Warnf("[WorkflowActionCont] failed to send email. req: %+v, group %+v, err: %+v", req, updateWorkflowInstanceParam.AssignedGroup, err)
			errorHandler.CustomError(w, http.StatusInternalServerError, constants.GenericInternalIssuesMessage)
			return
		}

		ctx = context.WithValue(ctx, "resData", resData)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func (dsr *DashboardServiceRepository) validateWorkflowAction(ctx context.Context,
	actionResult *bpmworkflowmanagement.ActionResult,
	workflow *bpmworkflowmanagement.Workflow,
	placeHoldersMap map[string]interface{}) error {

	for _, validate := range actionResult.ValidateActions {

		configKeyValueMap := make(map[string]any)

		configKeyValueMap["dashboard_workfow_id"] = workflow.ID

		if validate.WorkflowID != "" {
			configKeyValueMap["dashboard_workfow_id"] = validate.WorkflowID
		}

		configKeyValueMap["validate_data_source"] = validate.DataSourceKey

		graphQLConfigParam := configmanagement.GetConfigInfoParam{
			ResourceName: "graphql_dashboard_workflow_hard_stop",
		}

		ValidateSourceGraphQL, err := dsr.ConfigManagementSrvRepositoryProvider.LegacyProvider().GetConfigInfoByParam(ctx, &graphQLConfigParam, configKeyValueMap)
		if err != nil {
			return fmt.Errorf("[validateWorkflowAction] - failed to get graphql config: %v", err)
		}

		finalGraphQL, err := mapConfigPlaceHolders(ValidateSourceGraphQL.Config, "graphql", placeHoldersMap)
		if err != nil {
			return fmt.Errorf("[validateWorkflowAction] - failed to map placeholders in graphql err: %v, data: %v", err, placeHoldersMap)
		}

		ValidateSourceResp, err := fgraphql.GraphQLGet(ctx, fgraphql.GraphQLGetParam{
			Query:           finalGraphQL,
			QueryIdentifier: graphQLConfigParam.ResourceName,
		})
		if err != nil {
			return fmt.Errorf("[validateWorkflowAction] - failed to execute graphql query: %v", err)
		}

		val, err := uidashboard.ExtractJSONValue(ValidateSourceResp, validate.JQ)
		if err != nil {
			logger.WithContext(ctx).Errorf("[validateWorkflowAction] failed to validate action. err: %v", err)
			return err
		}

		if val == nil {
			return fmt.Errorf("[validateWorkflowAction] invalid jq to validate")
		}

		if !reflect.DeepEqual(val, validate.ValidateValue) {
			return fbxerrors.NewValidationError(validate.FailedMessage)
		}
	}

	return nil

}

func IsActionNotAllowedForSendingMail(action, lenderID, orgID string) bool {
	notAllowedActions := []string{"approved", "rejected"}
	if lenderID == constants.MFLBLID || orgID == constants.MFLOrganizationID || lenderID == constants.MFLID {
		return true
	}

	for _, notAllowedAction := range notAllowedActions {
		if strings.Contains(notAllowedAction, action) {
			return true
		}
	}
	return false
}
