package dynamicqueue

import (
	"encoding/json"
	"net/http"
	"strconv"

	"finbox/go-api/errorHandler"
	"finbox/go-api/functions/logger"
	"finbox/go-api/internal/service/dynamicmyqueue"

	"github.com/gorilla/mux"
)

// <PERSON><PERSON> handles HTTP requests for dynamic queue operations
type Handler struct {
	service dynamicmyqueue.DynamicQueueService
}

// NewHandler creates a new handler instance
func NewHandler(service dynamicmyqueue.DynamicQueueService) *Handler {
	return &Handler{
		service: service,
	}
}

// UpdateConfig handles PUT /api/v1/dynamic-queue/configs/{clientId}/{configName}
func (h *Handler) UpdateConfig(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	vars := mux.Vars(r)
	clientID := vars["clientId"]
	configName := vars["configName"]

	var config dynamicmyqueue.MetadataConfig
	if err := json.NewDecoder(r.Body).Decode(&config); err != nil {
		logger.WithRequest(r).Errorf("Failed to decode request body: %v", err)
		errorHandler.CustomError(w, http.StatusBadRequest, "Invalid request body")
		return
	}

	if err := h.service.UpdateConfig(ctx, clientID, configName, &config); err != nil {
		logger.WithRequest(r).Errorf("Failed to update config: %v", err)
		errorHandler.CustomError(w, http.StatusInternalServerError, "Failed to update configuration")
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"success": true,
		"message": "Configuration updated successfully",
	})
}

// GetConfig handles GET /api/v1/dynamic-queue/configs/{clientId}/{configName}
func (h *Handler) GetConfig(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	vars := mux.Vars(r)
	clientID := vars["clientId"]
	configName := vars["configName"]

	config, err := h.service.GetConfig(ctx, clientID, configName)
	if err != nil {
		logger.WithRequest(r).Errorf("Failed to get config: %v", err)
		errorHandler.CustomError(w, http.StatusNotFound, "Configuration not found")
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(config)
}

// ListConfigs handles GET /api/v1/dynamic-queue/configs/{clientId}
func (h *Handler) ListConfigs(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	vars := mux.Vars(r)
	clientID := vars["clientId"]

	configs, err := h.service.ListConfigs(ctx, clientID)
	if err != nil {
		logger.WithRequest(r).Errorf("Failed to list configs: %v", err)
		errorHandler.CustomError(w, http.StatusInternalServerError, "Failed to retrieve configurations")
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"configs": configs,
		"count":   len(configs),
	})
}

// DeleteConfig handles DELETE /api/v1/dynamic-queue/configs/{clientId}/{configName}
func (h *Handler) DeleteConfig(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	vars := mux.Vars(r)
	clientID := vars["clientId"]
	configName := vars["configName"]

	if err := h.service.DeleteConfig(ctx, clientID, configName); err != nil {
		logger.WithRequest(r).Errorf("Failed to delete config: %v", err)
		errorHandler.CustomError(w, http.StatusInternalServerError, "Failed to delete configuration")
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"success": true,
		"message": "Configuration deleted successfully",
	})
}

// GetTaskCases handles POST /api/v1/dynamic-queue/task-cases
func (h *Handler) GetTaskCases(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	var request dynamicmyqueue.TaskCasesRequest
	if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
		logger.WithRequest(r).Errorf("Failed to decode request body: %v", err)
		errorHandler.CustomError(w, http.StatusBadRequest, "Invalid request body")
		return
	}

	// Set default values if not provided
	if request.Page <= 0 {
		request.Page = 1
	}
	if request.PageSize <= 0 {
		request.PageSize = 20
	}

	response, err := h.service.GetTaskCases(ctx, &request)
	if err != nil {
		logger.WithRequest(r).Errorf("Failed to get task cases: %v", err)
		errorHandler.CustomError(w, http.StatusInternalServerError, "Failed to retrieve task cases")
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// GetTaskCaseDetails handles GET /api/v1/dynamic-queue/task-cases/{clientId}/{configName}/{taskId}
func (h *Handler) GetTaskCaseDetails(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	vars := mux.Vars(r)
	clientID := vars["clientId"]
	configName := vars["configName"]
	taskID := vars["taskId"]

	details, err := h.service.GetTaskCaseDetails(ctx, clientID, configName, taskID)
	if err != nil {
		logger.WithRequest(r).Errorf("Failed to get task case details: %v", err)
		errorHandler.CustomError(w, http.StatusNotFound, "Task case not found")
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(details)
}

// ValidateConfig handles POST /api/v1/dynamic-queue/configs/validate
func (h *Handler) ValidateConfig(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	var config dynamicmyqueue.MetadataConfig
	if err := json.NewDecoder(r.Body).Decode(&config); err != nil {
		logger.WithRequest(r).Errorf("Failed to decode request body: %v", err)
		errorHandler.CustomError(w, http.StatusBadRequest, "Invalid request body")
		return
	}

	result, err := h.service.ValidateConfig(ctx, &config)
	if err != nil {
		logger.WithRequest(r).Errorf("Failed to validate config: %v", err)
		errorHandler.CustomError(w, http.StatusInternalServerError, "Failed to validate configuration")
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(result)
}

// TestQuery handles POST /api/v1/dynamic-queue/configs/{clientId}/{configName}/test
func (h *Handler) TestQuery(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	vars := mux.Vars(r)
	clientID := vars["clientId"]
	configName := vars["configName"]

	var filters map[string]interface{}
	if err := json.NewDecoder(r.Body).Decode(&filters); err != nil {
		logger.WithRequest(r).Errorf("Failed to decode request body: %v", err)
		errorHandler.CustomError(w, http.StatusBadRequest, "Invalid request body")
		return
	}

	result, err := h.service.TestQuery(ctx, clientID, configName, filters)
	if err != nil {
		logger.WithRequest(r).Errorf("Failed to test query: %v", err)
		errorHandler.CustomError(w, http.StatusInternalServerError, "Failed to test query")
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(result)
}

// GetTaskCasesWithParams handles GET /api/v1/dynamic-queue/task-cases/{clientId}/{configName}
func (h *Handler) GetTaskCasesWithParams(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	vars := mux.Vars(r)
	clientID := vars["clientId"]
	configName := vars["configName"]

	// Parse query parameters
	query := r.URL.Query()

	page := 1
	if pageStr := query.Get("page"); pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}

	pageSize := 20
	if pageSizeStr := query.Get("pageSize"); pageSizeStr != "" {
		if ps, err := strconv.Atoi(pageSizeStr); err == nil && ps > 0 {
			pageSize = ps
		}
	}

	searchQuery := query.Get("search")

	// Parse filters from query parameters
	filters := make(map[string]interface{})
	for key, values := range query {
		if key != "page" && key != "pageSize" && key != "search" && len(values) > 0 {
			if len(values) == 1 {
				filters[key] = values[0]
			} else {
				filters[key] = values
			}
		}
	}

	request := &dynamicmyqueue.TaskCasesRequest{
		ClientID:    clientID,
		ConfigName:  configName,
		Filters:     filters,
		Page:        page,
		PageSize:    pageSize,
		SearchQuery: searchQuery,
	}

	response, err := h.service.GetTaskCases(ctx, request)
	if err != nil {
		logger.WithRequest(r).Errorf("Failed to get task cases: %v", err)
		errorHandler.CustomError(w, http.StatusInternalServerError, "Failed to retrieve task cases")
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// RegisterRoutes registers all the routes for the dynamic queue handler
func (h *Handler) RegisterRoutes(router *mux.Router) {
	// Configuration management routes
	router.HandleFunc("/api/v1/dynamic-queue/configs", h.CreateConfig).Methods("POST")
	router.HandleFunc("/api/v1/dynamic-queue/configs/{clientId}", h.ListConfigs).Methods("GET")
	router.HandleFunc("/api/v1/dynamic-queue/configs/{clientId}/{configName}", h.GetConfig).Methods("GET")
	router.HandleFunc("/api/v1/dynamic-queue/configs/{clientId}/{configName}", h.UpdateConfig).Methods("PUT")
	router.HandleFunc("/api/v1/dynamic-queue/configs/{clientId}/{configName}", h.DeleteConfig).Methods("DELETE")

	// Validation and testing routes
	router.HandleFunc("/api/v1/dynamic-queue/configs/validate", h.ValidateConfig).Methods("POST")
	router.HandleFunc("/api/v1/dynamic-queue/configs/{clientId}/{configName}/test", h.TestQuery).Methods("POST")

	// Task cases routes
	router.HandleFunc("/api/v1/dynamic-queue/task-cases", h.GetTaskCases).Methods("POST")
	router.HandleFunc("/api/v1/dynamic-queue/task-cases/{clientId}/{configName}", h.GetTaskCasesWithParams).Methods("GET")
	router.HandleFunc("/api/v1/dynamic-queue/task-cases/{clientId}/{configName}/{taskId}", h.GetTaskCaseDetails).Methods("GET")
}
