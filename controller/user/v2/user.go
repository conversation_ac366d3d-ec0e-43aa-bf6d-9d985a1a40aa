// Package user contains all the handlers for user related operations
package user

import (
	"bytes"
	"context"
	"database/sql"
	"encoding/csv"
	"encoding/json"
	"errors"
	"finbox/go-api/authentication"

	"finbox/go-api/conf"
	"finbox/go-api/constants"
	"finbox/go-api/controller/kyc"
	"finbox/go-api/functions/activity"
	"finbox/go-api/functions/httpfileutil"
	"finbox/go-api/functions/journey"
	"finbox/go-api/functions/lenders/abfl"
	"finbox/go-api/functions/lenders/poonawalla"
	"finbox/go-api/functions/lenderservice"
	"finbox/go-api/functions/partner/supermoney"
	"finbox/go-api/functions/services/pincodeapi"
	"finbox/go-api/infra/s3"
	"finbox/go-api/internal/util/tags"
	"finbox/go-api/middlewares"
	"finbox/go-api/models/activitylog"
	"finbox/go-api/models/mockconfig"
	"finbox/go-api/models/personalloanoffer"
	"finbox/go-api/models/serviceablepincode"
	"finbox/go-api/temporal/temporalutility"
	seedhemock "finbox/go-api/thirdparty/seedhe-mock"
	"finbox/go-api/utils/apistack"
	"finbox/go-api/utils/calc"
	"finbox/go-api/utils/fraudcheckutils"
	"finbox/go-api/utils/general"
	"finbox/go-api/utils/journeyutils"
	"finbox/go-api/utils/offersetter"
	"finbox/go-api/utils/partnerpush"
	"finbox/go-api/utils/prequal"
	"finbox/go-api/utils/schemamapper"
	"finbox/go-api/utils/workflowutils"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"reflect"
	"strconv"
	"strings"
	"time"

	"github.com/spf13/cast"

	"finbox/go-api/common/usersutil"
	"finbox/go-api/errorHandler"
	"finbox/go-api/functions/apistack/apiresult"
	"finbox/go-api/functions/apistackbre"
	"finbox/go-api/functions/commonutils"
	"finbox/go-api/functions/downloadables"
	"finbox/go-api/functions/insuranceutils"
	"finbox/go-api/functions/loanutils"
	"finbox/go-api/functions/logger"
	"finbox/go-api/functions/services/udyamaadhar"
	"finbox/go-api/functions/sourceentity"
	"finbox/go-api/functions/structs"
	"finbox/go-api/functions/underwriting"
	"finbox/go-api/functions/userworkflow"
	wfconfigfunctions "finbox/go-api/functions/workflowconfig"
	"finbox/go-api/infra/db"
	"finbox/go-api/models/businessloanoffer"
	"finbox/go-api/models/featureflag"
	"finbox/go-api/models/insurance"
	"finbox/go-api/models/jobs"
	"finbox/go-api/models/lenderdropdown"
	"finbox/go-api/models/loanapplication"
	"finbox/go-api/models/mandatedetails"
	"finbox/go-api/models/media"
	"finbox/go-api/models/pandetails"
	"finbox/go-api/models/partnerdataschema"
	"finbox/go-api/models/partnerpushdata"
	"finbox/go-api/models/udmconfigs"
	"finbox/go-api/models/userapimodulemapping"
	"finbox/go-api/models/userbankconnectsessions"
	"finbox/go-api/models/userbusinessuan"
	"finbox/go-api/models/userdocuments"
	"finbox/go-api/models/userkycapplications"
	"finbox/go-api/models/userloandetails"
	"finbox/go-api/models/usermodulemapping"
	"finbox/go-api/models/users"

	dsa "finbox/go-api/utils/dsautils"
	"maps"
)

var log = logger.Log

var database = db.GetDB()

// CreateUserServerCont creates a new user with mobile number and customer id
func CreateUserServerCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.RecoveryV3(w, r, http.StatusConflict)
		// fetch arguments for controller
		sourceEntityID := r.Context().Value("sourceEntityID").(string)

		attributes := r.Context().Value("attributes").(map[string]interface{})
		customerID := attributes["customerID"].(string)
		mobile := attributes["mobile"].(string)
		scenarioName, _ := attributes["scenarioName"].(string)

		// If the user already exists, return a success message
		if cast.ToBool(r.Context().Value(middlewares.UserExistsKey)) {
			resData := map[string]interface{}{
				"message": "user created!",
			}

			ctx := context.WithValue(r.Context(), "resData", resData)
			ctx = context.WithValue(ctx, "statusCode", constants.APICreateUserSuccess)
			next.ServeHTTP(w, r.WithContext(ctx))
			return
		}

		userID, _, errMessage, errHTTPCode, err := sourceentity.CreateUser(sourceEntityID, customerID, mobile, "", r.RemoteAddr, constants.LeadSourceAPIStack, "", false)
		if err != nil {
			logger.WithRequest(r).Errorln(err)
			panic(err)
		}
		if errMessage != "" {
			logger.WithRequest(r).Errorln(errMessage)
			if errMessage == constants.ErrUserAlreadyExists {
				errorHandler.CustomErrorV3(w, errHTTPCode, constants.APICreateUserExists, constants.ResponseCodeMessage[constants.APICreateUserExists])
				return
			}
			panic(errMessage)
		}

		if conf.ENV != conf.ENV_PROD {
			if scenarioName != "" {
				// call mock service to recieve list of service names
				configMap, scenarioID, err := seedhemock.GetServicesForScenario(scenarioName)
				if err != nil {
					logger.Log.Errorln(fmt.Errorf("error getting services for scenarioName: %v", scenarioName), err)
				} else {
					// upsert persona in mock-service
					err = seedhemock.UpsertPersona(userID, customerID, mobile, scenarioID)
					if err != nil {
						logger.Log.Errorln(fmt.Errorf("error upserting persona for userID: %v", userID), err)
					}
					// upsert mocking config in esmc table
					err = mockconfig.UpsertESMC(userID, configMap)
					if err != nil {
						logger.Log.Errorln(fmt.Errorf("error upserting mock config for userID: %v", userID), err)
					}
				}
			}
		}

		var dsaOwnerType string
		switch {
		case journey.IsPlatformDSA(sourceEntityID):
			dsaOwnerType = constants.OwnerTypePlatform
		default:
			dsaOwnerType = constants.OwnerTypeLender
		}

		dsaID, err := dsa.GetDSAID(sourceEntityID, dsaOwnerType)
		if err != nil && err != sql.ErrNoRows {
			logger.WithUser(userID).Errorln(err)
			errorHandler.ReportToSentryWithoutRequest(err)
		}

		if dsaID != "" {
			err = dsa.TagUserToDSA(userID, dsaID, "", sourceEntityID)
			if err != nil {
				logger.WithUser(userID).Warnln("tagging failed with error: ", err.Error())
			}
		}

		usersutil.UpdateUserSource(userID, constants.LeadSourceAPIStack, nil)

		err = journeyutils.CreateJourneyWithLenders(userID, sourceEntityID, "", "", "")
		if err != nil {
			logger.WithRequest(r).Errorln(err)
			panic(err)
		}

		err = userapimodulemapping.Create(userID, userID, userapimodulemapping.ModuleCreateUser, userapimodulemapping.StatusCompleted, 1, "")
		if err != nil {
			logger.WithRequest(r).Errorln(err)
			if errorHandler.IsContextCancelledError(err) {
				errorHandler.CustomError(w, http.StatusConflict, constants.OperationTimedOutMessage)
				return
			}
			panic(err)
		}

		// if sourceEntityID == constants.KredMintABFLDSAID {
		// 	// Switch workflow
		// 	err = workflowutils.UpdateWorkFlow(userID, sourceEntityID, constants.WorkflowKredmintAPIStack)
		// 	if err != nil {
		// 		logger.WithRequest(r).Errorln(err)
		// 		panic(err)
		// 	}
		// }

		resData := map[string]interface{}{
			"message": "user created!",
		}

		ctx := context.WithValue(r.Context(), "resData", resData)
		ctx = context.WithValue(ctx, "statusCode", constants.APICreateUserSuccess)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// RestoreWorkflowFromOldWorkflowServerCont creates a new user with mobile number and customer id
func RestoreWorkflowFromOldWorkflowServerCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.RecoveryV3(w, r, http.StatusConflict)
		// fetch arguments for controller

		attributes := r.Context().Value("attributes").(map[string]interface{})
		userID := attributes["userID"].(string)
		userObj, err := users.Get(userID)
		if err != nil {
			errorHandler.CustomError(w, http.StatusConflict, err.Error())
			return
		}
		sourceEntityID := userObj.SourceEntityID
		err = workflowutils.RestoreWorkflowFromOldWorkflow(r.Context(), userID, sourceEntityID)
		if err != nil {
			errorHandler.CustomError(w, http.StatusConflict, err.Error())
			return
		}

		resData := map[string]interface{}{
			"message": "user tsm workflow reset successful created!",
		}

		ctx := context.WithValue(r.Context(), "resData", resData)
		ctx = context.WithValue(ctx, "statusCode", constants.APIRestoreTSMWorkflowSuccess)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// PushUserDataCont stores validated user data pushed by partners in partner_push_data relation
func PushUserDataCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.RecoveryV2(w, r, http.StatusConflict, constants.APIStatusUnrecoverableError)
		sourceEntityID := r.Context().Value("sourceEntityID").(string)

		b, err := io.ReadAll(r.Body)
		defer r.Body.Close()
		if err != nil {
			logger.WithRequest(r).Errorln(err)
			errorHandler.CustomErrorV2(w, http.StatusBadRequest, constants.APIStatusBadRequest, "couldn't process the request body")
			return
		}

		var reqObj map[string]interface{}
		d := json.NewDecoder(bytes.NewReader(b))
		d.UseNumber()
		if err = d.Decode(&reqObj); err != nil {
			logger.WithRequest(r).Errorln(err)
			errorHandler.CustomErrorV2(w, http.StatusBadRequest, constants.APIStatusBadRequest, "invalid JSON payload")
			return
		}

		validationErrors, statusCode, err := partnerpush.PushData(reqObj, sourceEntityID)
		if err != nil {
			logger.WithRequest(r).Errorln(err)
			errorHandler.CustomErrorV2(w, http.StatusBadRequest, constants.APIStatusBadRequest, err.Error())
			return
		}
		message := partnerpush.StatusCodeToMessage[statusCode]

		resData := map[string]interface{}{
			"data": map[string]interface{}{
				"errors": validationErrors,
			},
			"statusCode": statusCode,
			"message":    message,
		}

		switch statusCode {
		case partnerpush.StatusMandatoryFieldMissing:
			fallthrough
		case partnerpush.StatusStrictModeValidationFailed:
			errorHandler.CustomErrorResponseV2(w, resData["data"].(map[string]interface{}), http.StatusBadRequest, statusCode, message)
			return
		case partnerpush.StatusInvalidPush:
			errorHandler.CustomErrorResponseV2(w, resData["data"].(map[string]interface{}), http.StatusConflict, statusCode, message)
			return
		}

		ctx := context.WithValue(r.Context(), "resData", resData)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// GetUserDetailsCont fetches details based on module mappings with module name to keys to be auto-filled in the SDK journey
func GetUserDetailsCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.RecoveryV2(w, r, http.StatusConflict, constants.APIStatusUnrecoverableError)

		attributes := r.Context().Value("attributes").(map[string]string)
		userObj := r.Context().Value("user").(authentication.UserStruct)
		sourceEntityID := userObj.SourceEntityID
		module := attributes["module"]
		sourcedData := map[string]any{}

		config, err := udmconfigs.GetBySourceEntity(sourceEntityID)
		if err != nil {
			logger.WithRequest(r).Errorln(err)
			panic(err)
		}

		keys, ok := config.GetKeysForModule(module)
		if !ok {
			// dont throw error, return empty
			logger.WithRequest(r).Errorln("no config for module ", module)

			resData := map[string]interface{}{
				"data":       map[string]interface{}{},
				"statusCode": constants.APIGetUserDetailsSuccess,
				"message":    "success",
			}

			ctx := context.WithValue(r.Context(), "resData", resData)
			next.ServeHTTP(w, r.WithContext(ctx))
			return
		}

		partnerData, err := partnerpushdata.GetLatestByMobile(userObj.Mobile, sourceEntityID)
		if err != nil {
			logger.WithRequest(r).Errorln(err)
			panic(err)
		}

		partnerPush := map[string]interface{}{}
		if partnerData != "" {
			err = json.Unmarshal([]byte(partnerData), &partnerPush)
			if err != nil {
				logger.WithRequest(r).Errorln(err)
				panic(err)
			}
		}

		switch module {
		case "PRELOAN":
			sourcedData, err = userloandetails.GetPreloandata(r.Context(), userObj.UserID)
			if err != nil {
				logger.WithRequest(r).Errorln(err)
				if err != sql.ErrNoRows {
					panic(err)
				}
			}
		default:
			// Fetch all relevant keys for PERSONAL_INFO, PAN_INFO modules.
			userData, err := users.Get(userObj.UserID)
			if err != nil {
				logger.WithRequest(r).Errorln(err)
				panic(err)
			}

			udBytes, err := json.Marshal(userData)
			if err != nil {
				logger.WithRequest(r).Errorln(err)
				panic(err)
			}

			err = json.Unmarshal(udBytes, &sourcedData)
			if err != nil {
				logger.WithRequest(r).Errorln(err)
				panic(err)
			}
		}

		// filteredSourcedData contains information retrieved from user_loan_details or users table.
		// partnerPushData contains information retrieved from partner_push_data table.
		filteredSourcedData := schemamapper.FindValueByKeys(sourcedData, keys...)
		partnerPushData := schemamapper.FindValueByKeys(partnerPush, keys...)

		// Here we check for the relevant keys required for current module in filteredSourcedData and
		// partnerPushData. filteredSourcedData is given higher precedence over partnerPushData, so if
		// the data is found in filteredSourcedData, we add that to result map and proceed else we check
		// for the data in partnerPushData.
		resultMap := map[string]any{}
		for _, k := range keys {
			if v, ok := filteredSourcedData[k]; ok {
				// all values are parsed as string in pre_loan_data column and in above query for users table
				strValue, ok := v.(string)
				if !ok {
					log.Errorf("%s is not a string value: %v (%T)", k, v, v)
				}

				if strValue != "" {
					resultMap[k] = v
					continue
				}
			}

			if v, ok := partnerPushData[k]; ok {
				resultMap[k] = v
			}
		}

		resData := map[string]interface{}{
			"data":       resultMap,
			"statusCode": constants.APIGetUserDetailsSuccess,
			"message":    "success",
		}

		ctx := context.WithValue(r.Context(), "resData", resData)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// UserRejectCheckFilter checks if the user is rejected and returns with error
func UserRejectCheckFilter(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.RecoveryV3(w, r, http.StatusConflict)

		attributes := r.Context().Value("attributes").(map[string]any)
		customerID := attributes["customerID"].(string)
		sourceEntityID := r.Context().Value("sourceEntityID").(string)

		user, err := users.GetByUniqueID(customerID, sourceEntityID)
		if err != nil {
			logger.WithRequest(r).Errorln(err)
			if err == sql.ErrNoRows {
				errorHandler.CustomErrorV3(w, constants.ErrStringToStatusCodeMapping[constants.ErrStringUserNotFound], constants.ErrStringToStatusCodeStringMapping[constants.ErrStringUserNotFound], constants.ErrStringUserNotFound)
				return
			}
			panic(err)
		}

		if *user.Status == constants.UserStatusDisqualified {
			errorHandler.CustomErrorV3(w, constants.ErrStringToStatusCodeMapping[constants.ErrUserDisqualified], constants.ErrStringToStatusCodeStringMapping[constants.ErrUserDisqualified], constants.ErrUserDisqualified)
			return
		}

		ctx := context.WithValue(r.Context(), "user", user)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// UpdateUserDetailsCont updates user details in the database using schema to build queries
func UpdateUserDetailsCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.RecoveryV3(w, r, http.StatusConflict)

		sourceEntityID := r.Context().Value("sourceEntityID").(string)
		attributes := r.Context().Value("attributes").(map[string]any)
		user := r.Context().Value("user").(users.User)
		workflowStep := r.Context().Value("workflowStep").(int)
		isAPICalledBefore := r.Context().Value("isAPICalledBefore")
		isAPIRequestProcessed := cast.ToBool(r.Context().Value(tags.APIRequestProcessed))

		if isAPIRequestProcessed {
			logger.WithUser(user.ID).Debugf("API request already processed, skipping from UpdateUserDetailsCont")
			next.ServeHTTP(w, r)
			return
		}

		switch featureflag.Get(user.ID, journey.FlagPreventDuplicateAPIStackModuleCalls) {
		case true:
			calledBefore, err := apistack.IsModuleCalledAtLeastOnce(r.Context(), user.ID, userapimodulemapping.ModuleUpdateDetails)
			if err != nil {
				logger.WithUser(user.ID).Errorln(err)
				panic(err)
			}
			if calledBefore {
				resData := map[string]any{
					"message": "success",
				}
				ctx := context.WithValue(r.Context(), "resData", resData)
				ctx = context.WithValue(ctx, "statusCode", constants.APIStatusCodeAlreadyAccepted)
				next.ServeHTTP(w, r.WithContext(ctx))
				return
			}
		default:
			if isAPICalledBefore == true {
				resData := map[string]any{
					"message": "success",
				}
				ctx := context.WithValue(r.Context(), "resData", resData)
				ctx = context.WithValue(ctx, "statusCode", constants.APIStatusCodeAlreadyAccepted)
				next.ServeHTTP(w, r.WithContext(ctx))
				return
			}
		}

		metadata, err := apistack.GetMetadataForCurrentModule(user.ID, sourceEntityID)
		if err != nil {
			logger.WithUser(user.ID).Errorln(err)
			panic(err)
		}

		logger.WithUser(user.ID).Debug(fmt.Sprintf("Using invocation number: %d for fetching schema", metadata.Invocation))

		schema, err := schemamapper.GetSchema(sourceEntityID, partnerdataschema.TypeUserDetails, metadata.Version, metadata.Invocation)
		if err != nil && schema != nil {
			logger.WithUser(user.ID).Errorln(err)
			panic(err)
		}

		if schema == nil {
			// schema not found
			errorHandler.CustomErrorV3(w, http.StatusInternalServerError, constants.APIStatusInternalServerError, "schema not found")
			return
		}

		terminalPaths := schema.GetAllTerminalPaths()
		logger.WithUser(user.ID).Debug("Terminal Paths: ", general.AnyToJSONString(terminalPaths))

		mapper := &schemamapper.SchemaMapper{
			ValidationErrors: []schemamapper.ValidationError{},
		}

		logger.WithUser(user.ID).Debug(fmt.Sprintf("Schema: %+v\n", general.AnyToJSONString(schema)))
		userDataFields := mapper.ProcessRequest(attributes, *schema)
		if len(mapper.ValidationErrors) > 0 {
			resData := map[string]any{
				"errors": mapper.ValidationErrors,
			}
			errorHandler.CustomErrorResponseV3(w, resData, http.StatusBadRequest, constants.APIUpdateUserDetailsValidationError, "request validation failed")
			return
		}

		/* dataMap is a map[string]map[string][]FieldData where outer map's key would be TableName and innerMap would be
		   of key as ColumnName and value would be list of entries that would go in that column for that tableName
		*/
		dataMap := make(map[string]map[string][]FieldData)

		isIIFLBLSourcing := journey.IsIIFLBLSourcing(sourceEntityID)
		isABFLPLSourcing := journey.IsABFLPLSourcing(sourceEntityID)
		for _, fullPath := range terminalPaths {

			if v := schema.Find(fullPath); v != nil {
				// Don't process null values parsed by SchemaMapper for optional fields
				if userDataFields.FindValue(fullPath) == nil {
					continue
				}

				if v.TableName == nil || v.ColumnName == nil {
					logger.WithUser(user.ID).Warn("tableName or columnName is nil for schemaID with schemaPath: ", schema.SchemaID, fullPath)
					continue
				}

				schemaTableNames := *v.TableName
				schemaColumnNames := *v.ColumnName

				tableNames := splitAndTrim(schemaTableNames)
				columnNames := splitAndTrim(schemaColumnNames)

				if len(tableNames) != len(columnNames) {
					logger.WithUser(user.ID).Errorf("tableName and columnName length mismatch for table: %s, column: %s, with schemaID %s and schemaPath: %s", tableNames, columnNames, schema.SchemaID, fullPath)
					panic(errors.New("tableName and columnName length mismatch"))
				}

				for i := range tableNames {
					tableName := tableNames[i]
					columnName := columnNames[i]

					if _, ok := dataMap[tableName]; !ok {
						// If not, create a new map for the table name
						dataMap[tableName] = make(map[string][]FieldData)
					}
					if _, ok := dataMap[tableName][columnName]; !ok {
						// If not, create a new list for the column name
						dataMap[tableName][columnName] = make([]FieldData, 0)
					}
					fieldData := FieldData{
						Value: userDataFields.Find(fullPath).Value,
						Tag:   v.Tag,
					}

					if fullPath == "basicDetails.personalDetails.currentAddress.currentAddressLine2" && fieldData.Value == nil {
						// set this value to empty string if nil to prevent downstream panics
						dataMap[tableName][columnName] = append(dataMap[tableName][columnName], FieldData{
							Value: "",
							Tag:   v.Tag,
						})
						continue
					}

					dataMap[tableName][columnName] = append(dataMap[tableName][columnName], fieldData)
				}
			}
		}

		// Explicit handling for array data type
		var consents interface{}
		for _, f := range userDataFields.Fields {
			if f.Tag == "basicDetails" {
				for _, basicDetailsField := range f.Fields {
					if basicDetailsField.Tag == "consents" {
						consents = basicDetailsField.Value
						break
					}
				}
			}
			if f.Tag == "consents" {
				consents = f.Value
				break
			}
		}

		writeValidationError := func(err schemamapper.ValidationError) {
			resData := map[string]any{
				"errors": []schemamapper.ValidationError{err},
			}
			errorHandler.CustomErrorResponseV3(w, resData, http.StatusBadRequest, constants.APIUpdateUserDetailsValidationError, "request validation failed")
			return
		}

		// this logic will be made part of custom validator for schema mapper
		switch {
		case isIIFLBLSourcing:
			value := lenderdropdown.GetValue("industry_sub_industry", "iifl_bl_business_details", constants.IIFLID)

			type IndustryDetails struct {
				Industry           string `json:"Industry"`
				SubIndustry        string `json:"SubIndustry"`
				Classification     string `json:"Classification"`
				RiskCategorization string `json:"RiskCategorization"`
			}

			var industryDetails []IndustryDetails
			err = json.Unmarshal([]byte(value), &industryDetails)
			if err != nil {
				logger.WithUser(user.ID).Errorln(err)
				panic(err)
			}

			industry := userDataFields.FindValue("basicDetails.businessDetails.industry")
			subIndustry := userDataFields.FindValue("basicDetails.businessDetails.subIndustry")

			validIndustry := false
			for _, detail := range industryDetails {
				if industry == detail.Industry {
					validIndustry = true
					break
				}
			}

			if !validIndustry {
				writeValidationError(schemamapper.ValidationError{Field: "industry", Message: "invalid industry"})
				return
			}

			validSubIndustry := false
			for _, detail := range industryDetails {
				if industry == detail.Industry && subIndustry == detail.SubIndustry {
					validSubIndustry = true
					break
				}
			}

			if !validSubIndustry {
				writeValidationError(schemamapper.ValidationError{Field: "subIndustry", Message: "invalid subIndustry"})
				return
			}
		case isABFLPLSourcing:
			checkForPincode, _, listID, _ := pincodeapi.GetPincodeServiceabilityMode(sourceEntityID, constants.ABFLPLID)
			if !checkForPincode {
				err = fmt.Errorf("invalid mode for serviceable check")
				panic(err)
			}
			pincode := userDataFields.FindValue("basicDetails.personalDetails.currentAddress.pincode")
			if !serviceablepincode.IsServiceable(cast.ToString(pincode), listID) {
				writeValidationError(schemamapper.ValidationError{Field: "pincode",
					Message: fmt.Sprintf("provided pincode: %v is not serviceable", pincode)})
				return
			}
		}

		// A flag to denote whether to throw validation error if required consents not present.
		// This is by default enabled if the consents block is present, although can be explicitly enabled
		// if required, for eg. when using feature flags.
		validateConsent := false

		// add all the different required consentTypes based on client
		var requiredConsentTypes []string

		// Accept Loan Agreement consent in UUD-2 if feature flag is enabled
		if metadata.Invocation == 2 && featureflag.Get(user.ID, journey.FlagMandatoryLoanAgreementConsentType) { // Accept Loan Agreement consent in UUD - 2
			requiredConsentTypes = append(requiredConsentTypes, constants.APIStackConsentTypeLoanAgreement)
			validateConsent = true // Require the above mentioned consents to be present if feature flag enabled
		}

		// this is used to map all the consentTypes in the request,
		// later we will use this to validate the required consentTypes for different clients
		consentsInRequest := map[string]bool{}

		// If consents is there validate them
		if consents != nil {
			// Enabling the flag to validate consents, since the consents block is already present. (Default behvaviour)
			validateConsent = true
			consentFields := []string{
				"timestamp",
				"type",
			}

			isABFLBLSourcing := journey.IsABFLBLSourcing(sourceEntityID)

			// isPanPhoneUdyamToUdyamFeatureEnabled := metadata.Invocation == 1 && featureflag.Get(user.ID, journey.FlagPANPhoneToUdyam) // Only require mandatory consent in UUD-1. TODO: Move this to schemamapper

			if isABFLBLSourcing {
				consentFields = append(consentFields, "body")
			}

			switch {
			case isABFLBLSourcing:
				requiredConsentTypes = []string{constants.APIStackConsentTypeHybrid}
			case isABFLPLSourcing:
				requiredConsentTypes = []string{constants.APIStackConsentTypeHybrid, constants.APIStackConsentTypeBureau, constants.APIStackConsentTypeTNC}
			case isIIFLBLSourcing:
				requiredConsentTypes = []string{constants.APIStackConsentTypeBureau}
			case journey.IsIncredSourcing(sourceEntityID):
				requiredConsentTypes = []string{constants.APIStackConsentTypeBureau}
				// case isPanPhoneUdyamToUdyamFeatureEnabled:
				// 	requiredConsentTypes = []string{constants.APIStackConsentTypeUdyam}
			case journey.IsSuperMoneySourcing(sourceEntityID):
				requiredConsentTypes = []string{constants.APIStackConsentTypeBureauPull, constants.APIStackConsentTypeMFI, constants.APIStackConsentTypeNonPEP, constants.APIStackConsentTypeCKYC}
			case metadata.Invocation == 1 && featureflag.Get(user.ID, journey.FlagMandatoryHybridConsentType): // Accept Hybrid consent in UUD - 1
				requiredConsentTypes = append(requiredConsentTypes, constants.APIStackConsentTypeHybrid)
			}

			for _, c := range consents.([]any) {
				cc := c.(map[string]any)
				for _, field := range consentFields {
					val, ok := cc[field]
					if !ok {
						// Missing field error
						msg := fmt.Sprintf("missing field: %s", field)
						writeValidationError(schemamapper.ValidationError{Field: field, Message: msg})
						return
					}
					_, ok = val.(string)
					if !ok {
						// Invalid data type error
						msg := fmt.Sprintf("invalid field type for: %s", field)
						writeValidationError(schemamapper.ValidationError{Field: field, Message: msg})
						return
					}
					if field == "type" {
						if !general.InArr(val.(string), constants.ValidAPIStackConsentTypes) {
							// Invalid Type for consent type
							msg := fmt.Sprintf("invalid consent type: %s", val)
							writeValidationError(schemamapper.ValidationError{Field: field, Message: msg})
							return
						}
						consentType := val.(string)
						consentsInRequest[consentType] = true
					}

					if field == "body" {
						if len(val.(string)) == 0 {
							msg := "consent body cannot be empty"
							writeValidationError(schemamapper.ValidationError{Field: field, Message: msg})
							return
						}
					}
				}
			}
		}

		for _, validConsentType := range requiredConsentTypes {
			// Throw errors if need to validate consents and the required consent type is not present
			if validateConsent && !consentsInRequest[validConsentType] {
				msg := fmt.Sprintf("consent types: %v are required", requiredConsentTypes)
				writeValidationError(schemamapper.ValidationError{Field: "type", Message: msg})
				return
			}
		}

		logger.WithUser(user.ID).Debug(fmt.Sprintf("--- DATA MAP: %v\n", general.AnyToJSONString(dataMap)))

		dataMap = normalizeDataMap(dataMap)

		logger.WithUser(user.ID).Debug(fmt.Sprintf("--- Normalized DATA MAP: %v\n", general.AnyToJSONString(dataMap)))

		// -------------- BUILDING QUERY PARAMS --------------
		for tableName, columnMap := range dataMap {
			queryParams := map[string]any{}

			// columnEntries is a list of FieldData for a particular column
			for columnName, columnEntries := range columnMap {
				if strings.Contains(columnName, ".") {
					// If column has "." i.e. has nested columns, values, a nested object is created to insert in the specified JSONB column.
					// This JSONB column is available as the key of the nested object, and everything as the value is the JSON to be inserted for that column
					columnValue := columnEntries[0].Value // Safe to assume that there is only 1 item
					nestedObject := objectFromDotSeparatedStrings(columnName, columnValue)

					general.MergeMaps(queryParams, nestedObject)

				} else {
					for _, columnEntry := range columnEntries {
						if columnEntry.Value == nil {
							logger.WithUser(user.ID).Debug("field data is nil", columnEntry.Tag, columnEntry.Value)
							continue
						}
						// Have to move this check to transform
						if columnEntry.Tag == "gender" {
							columnEntry.Value = constants.GenderStrToNum[columnEntry.Value.(string)]
						}
						queryParams[columnName] = columnEntry.Value
					}
				}
			}
			// -------------- FINISH BUILDING QUERY PARAMS --------------

			logger.WithUser(user.ID).Debug(fmt.Sprintf("--- QUERY PARAMS: %v\n", general.AnyToJSONString(queryParams)))
			// TODO: Create upsert methods for these models
			switch tableName {
			case constants.TableUsers:

				query, err := buildUpdationParams(tableName, queryParams)
				if err != nil {
					logger.WithUser(user.ID).Error(err)
					panic(err)
				}

				queryParams["userID"] = user.ID

				if query != "" {
					logger.WithUser(user.ID).Debug("Query : ", query)
					_, err = database.NamedExec(query, queryParams)
					if err != nil {
						logger.WithUser(user.ID).Error(err)
						panic(err)
					}
				}
			case constants.TableMandateDetails:
				queryParams["user_id"] = user.ID
				// find loan application ID
				loanID, err := loanapplication.GetLoanIDFromUserID(user.ID)
				if err != nil {
					logger.WithUser(user.ID).Error(err)
					panic(err)
				}
				queryParams["loan_application_id"] = loanID
				queryParams["status"] = mandatedetails.StatusCompleted
				queryParams["created_at"] = "NOW()"

				query, err := buildInsertionParams(tableName, queryParams)
				if err != nil {
					logger.WithUser(user.ID).Error(err)
					panic(err)
				}

				if query != "" {
					logger.WithUser(user.ID).Debug("Query : ", query)
					_, err = database.NamedExec(query, queryParams)
					if err != nil {
						logger.WithUser(user.ID).Error(err)
						panic(err)
					}
				}
			case constants.TableUserBankDetails:
				queryParams["user_id"] = user.ID
				queryParams["status"] = constants.UserBankStatusAdded
				queryParams["user_bank_details_id"] = general.GetUUID()
				queryParams["created_at"] = "NOW()"
				queryParams["created_by"] = "ADMIN"

				query, err := buildInsertionParams(tableName, queryParams)
				if err != nil {
					logger.WithUser(user.ID).Error(err)
					panic(err)
				}

				if query != "" {
					logger.WithUser(user.ID).Debug("Query : ", query)
					_, err = database.NamedExec(query, queryParams)
					if err != nil {
						logger.WithUser(user.ID).Error(err)
						panic(err)
					}
				}
			case constants.TableUserBusinessGST:
				queryParams["journey_status"] = constants.GSTJourneyStatusSelected
				queryParams["status"] = constants.GSTStatusAdded
				queryParams["user_id"] = user.ID
				queryParams["created_at"] = "NOW()"
				gstin, pass := queryParams["gstin"].(string)
				if !pass || gstin == "" {
					// gstin is not present, skip insert
					continue
				}

				query, err := buildInsertionParams(tableName, queryParams)
				if err != nil {
					logger.WithUser(user.ID).Error(err)
					panic(err)
				}

				if query != "" {
					logger.WithUser(user.ID).Debug("Query : ", query)
					_, err = database.NamedExec(query, queryParams)
					if err != nil {
						logger.WithUser(user.ID).Error(err)
						panic(err)
					}
				}
			case constants.TableUserBusinessUAN:
				queryParams["user_id"] = user.ID
				queryParams["created_at"] = "NOW()"
				uan, pass := queryParams["uan"].(string)
				if !pass || uan == "" {
					// udyam number is not present, skip insert
					continue
				}

				query, err := buildInsertionParams(tableName, queryParams)
				if err != nil {
					logger.WithUser(user.ID).Error(err)
					panic(err)
				}

				if query != "" {
					logger.WithUser(user.ID).Debug("Query : ", query)
					_, err = database.NamedExec(query, queryParams)
					if err != nil {
						logger.WithUser(user.ID).Error(err)
						panic(err)
					}
				}
			case constants.TableUserBusiness:
				queryParams["user_id"] = user.ID
				queryParams["created_at"] = "NOW()"
				// Update user business address type
				useBusinessAddressTypeStr, pass := queryParams["business_address_type"].(string)
				if pass {
					// API stack enums value check
					useBusinessAddressType, pass := constants.ResidenceStrToNum[constants.ResidenceENUMToLendingStr[useBusinessAddressTypeStr]]
					if !pass {
						// Handling for ABFL (TODO: Migrate to enums)
						useBusinessAddressType = constants.ResidenceStrToNum[useBusinessAddressTypeStr]
					}
					queryParams["business_address_type"] = useBusinessAddressType
				}

				query, err := buildInsertionParams(tableName, queryParams)
				if err != nil {
					logger.WithUser(user.ID).Error(err)
					panic(err)
				}

				if query != "" {
					logger.WithUser(user.ID).Debug("Query : ", query)
					_, err = database.NamedExec(query, queryParams)
					if err != nil {
						logger.WithUser(user.ID).Error(err)
						panic(err)
					}
				}
			case constants.TableUserLocation:

				queryParams["user_id"] = user.ID
				queryParams["created_at"] = "NOW()"
				queryParams["step"] = constants.UpdateUserDetails
				// TODO: add "location_type" using config, assign const value if not passed from client
				queryParams["location_type"] = constants.UserBusinessLocation

				query, err := buildInsertionParams(tableName, queryParams)
				if err != nil {
					logger.WithUser(user.ID).Error(err)
					panic(err)
				}

				if query != "" {
					logger.WithUser(user.ID).Debug("Query : ", query)
					_, err = database.NamedExec(query, queryParams)
					if err != nil {
						logger.WithUser(user.ID).Error(err)
						panic(err)
					}
				}
			case constants.TableInsurance:

				// TODO: Validations on float type values
				vendor, ok := queryParams["vendor"].(string)
				if !ok {
					logger.WithUser(user.ID).Errorln("vendor not found in queryParams")
					panic("vendor not found in queryParams")
				}

				vendorID := constants.InsuranceVendorToVendorID[vendor]
				premium, err := queryParams["premium"].(json.Number).Float64()
				if err != nil {
					logger.WithUser(user.ID).Errorln(err)
					panic(err)
				}
				gstApplicable := queryParams["gstApplicable"].(bool)
				gstRate, err := queryParams["gstRate"].(json.Number).Float64()
				if err != nil {
					logger.WithUser(user.ID).Errorln(err)
					panic(err)
				}
				loanApp, err := loanapplication.GetLatestByUser(user.ID)
				if err != nil {
					logger.WithUser(user.ID).Errorln(err)
					panic(err)
				}

				insuranceType, err := insuranceutils.GetInsuranceTypeForSource(sourceEntityID)
				if err != nil {
					logger.WithUser(user.ID).Error(err)
					panic(err)
				}

				insuranceObj, err := loanutils.GenerateInsuranceObject(
					premium,
					insuranceType,
					vendorID,
					user.ID,
					constants.InsuranceStatusInquired,
					loanApp.Tenure,
				)
				if err != nil {
					logger.WithUser(user.ID).Error(err)
					panic(err)
				}

				insuranceObj.GSTRate = sql.NullFloat64{
					Float64: gstRate,
					Valid:   gstApplicable,
				}

				err = insurance.UpsertInsurancePremium(nil, []insurance.Insurance{*insuranceObj})
				if err != nil {
					logger.WithUser(user.ID).Error(err)
					panic(err)
				}

				now := general.GetTimeStampString()
				go func() {
					activityObj := &activity.ActivityEvent{
						UserID:         user.ID,
						SourceEntityID: sourceEntityID,
						EntityType:     constants.EntityTypeCustomer,
						EntityRef:      user.ID,
						EventType:      constants.ActivityInsuranceRequested,
						Description:    "",
					}
					activity.RegisterEvent(activityObj, now)
				}()

			default:
				panic(fmt.Errorf("table query not implemented: %s, userID: %s", tableName, user.ID))
			}
		}

		if consents != nil {
			queryParams := []map[string]any{}

			// type of consents and its contents is known and verified in advance, hence type check is skipped
			for _, c := range consents.([]any) {
				cc := c.(map[string]any)

				timestamp, err := strconv.ParseInt(cc["timestamp"].(string), 10, 64)
				if err != nil {
					logger.WithUser(user.ID).Errorln(err)
					panic(err)
				}
				consentTime, err := general.ParseMillisTimestamp(timestamp)
				if err != nil {
					logger.WithUser(user.ID).Error(err)
					panic(err)
				}

				consentText, _ := cc["body"].(string)
				consentType, _ := cc["type"].(string)

				queryParams = append(queryParams, map[string]any{
					"user_id": user.ID, "ip_address": cc["ipAddress"], "created_at": "NOW()",
					"consent_type": consentType, "accepted_at": consentTime, "consent_text": consentText,
				})
			}

			// Bulk insert into legallogs
			if len(queryParams) > 0 {
				query := `INSERT INTO legallogs (user_id, consent_text, ip_address, created_at, consent_type, accepted_at) VALUES (:user_id, :consent_text, :ip_address, :created_at, :consent_type, :accepted_at)`
				_, err = database.NamedExec(query, queryParams)
				if err != nil {
					logger.WithUser(user.ID).Errorln(err)
					panic(err)
				}
			}
		}

		err = userapimodulemapping.Create(user.ID, user.ID, userapimodulemapping.ModuleUpdateDetails, userapimodulemapping.StatusCompleted, workflowStep, "")
		if err != nil {
			logger.WithUser(user.ID).Errorln(err)
			panic(err)
		}

		if *user.Status == constants.UserStatusCreated {
			// Change to profile updated only on first call of UUD
			err = users.Update(nil, users.User{ID: user.ID, Status: general.AsPointer(constants.UserStatusProfileUpdated)})
			if err != nil {
				logger.WithUser(user.ID).Errorln(err)
				panic(err)
			}
		}
		dateTimeStr := general.GetTimeStampString()
		// added ProfileUpdated activity log to determine the correct state in dashboard
		go func() {
			activityObj := activity.ActivityEvent{
				UserID:            user.ID,
				SourceEntityID:    sourceEntityID,
				LoanApplicationID: "",
				EntityType:        constants.EntityTypeCustomer,
				EntityRef:         "",
				EventType:         constants.ActivityProfileUpdated,
				Description:       "",
			}
			activity.RegisterEvent(&activityObj, dateTimeStr)
		}()
		resData := map[string]any{
			"message": "success",
		}

		ctx := context.WithValue(r.Context(), "resData", resData)
		ctx = context.WithValue(ctx, "statusCode", constants.APIUpdateUserDetailsSuccess)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// GenerateOffersCont triggers CIBIL pull and BRE for users
// this API also accepts the Risk variables and saves them as DynamicUserInfo
func GenerateOffersCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.RecoveryV3(w, r, http.StatusConflict)
		// TODO: Add handling for userapimodules & usermodulemapping
		// Setup sentinel calls to get mock offers in workflow
		sourceEntityID := r.Context().Value("sourceEntityID").(string)

		attributes := r.Context().Value("attributes").(map[string]interface{})
		riskVariables := attributes["riskVariables"].(map[string]any)
		user := r.Context().Value("user").(users.User)
		workflowStep := r.Context().Value("workflowStep").(int)

		//TODO: fix this implementation with tsm workflow
		metadata, err := apistack.GetMetadataForCurrentModule(user.ID, sourceEntityID)
		if err != nil {
			logger.WithUser(user.ID).Errorln(err)
			panic(err)
		}

		// set job name as BRE_{invocation} to prevent "already accepted" status for multiple BRE journeys
		jobName := fmt.Sprintf("%s_%d", jobs.BRE, metadata.Invocation)

		// check if a job already exists
		prevJob, jobErr := commonutils.GetJobForClientByUserAndJobName(user.ID, jobName, sourceEntityID)
		if jobErr != nil {
			if jobErr != sql.ErrNoRows {
				logger.WithUser(user.ID).Error(jobErr)
				panic(jobErr)
			}
		}

		if prevJob.JobID != "" &&
			(prevJob.Status == jobs.StatusCompleted || prevJob.Status == jobs.StatusRunning) {
			resData := map[string]any{
				"referenceID": prevJob.JobID,
			}
			ctx := context.WithValue(r.Context(), "resData", resData)
			ctx = context.WithValue(ctx, "statusCode", constants.APIStatusCodeAlreadyAccepted)
			next.ServeHTTP(w, r.WithContext(ctx))
			return
		}

		// Check if a Temporal workflow for BRE is running
		wfID, shouldTrigger, err := userworkflow.ShouldTriggerUserWorkflow(user.ID, sourceEntityID, jobName)
		if err != nil {
			logger.WithUser(user.ID).Errorln(err)
			panic(err)
		}

		if !shouldTrigger {
			resData := map[string]any{
				"referenceID": wfID,
			}
			ctx := context.WithValue(r.Context(), "resData", resData)
			ctx = context.WithValue(ctx, "statusCode", constants.APIStatusCodeAlreadyAccepted)
			next.ServeHTTP(w, r.WithContext(ctx))
			return
		}

		// validate from possible risk variables
		acceptedRiskVariables := constants.APIStackAcceptedRiskVariables
		// Update risk variables in DynamicUserInfo
		for k, v := range riskVariables {
			if !general.InArr(k, acceptedRiskVariables) {
				continue
			}
			valueType := reflect.TypeOf(v).String()
			acceptedValues := []string{"string", "int", "float64", "bool"}
			if general.InArr(valueType, acceptedValues) {
				// update partner data fields, this is to make the risk variables visible in the dashboard
				// TODO: optimization, create bulk insert partner_data fields
				err := users.UpdatePartnerDataField(user.ID, k, fmt.Sprintf("%v", v))
				if err != nil {
					logger.WithUser(user.ID).Error(err)
					panic(err)
				}
				// TODO: optimization, create bulk insert dynamic user info fields
				err = users.UpdateDynamicUserInfoField(user.ID, k, fmt.Sprintf("%v", v))
				if err != nil {
					logger.WithUser(user.ID).Error(err)
					panic(err)
				}
			}
			logger.WithUser(user.ID).Debug(fmt.Sprintf("updated dynamic user info field: %s", k))
		}

		bre, err := apistackbre.NewInstance(user.ID, sourceEntityID)
		if err != nil {
			logger.WithUser(user.ID).Errorln(err)
			panic(err)
		}

		referenceID, err := bre.Run(context.Background(), user, jobName, workflowStep)
		if err != nil {
			logger.WithUser(user.ID).Errorln(err)
			if strings.Contains(err.Error(), constants.ErrStringInvalidUserState) {
				// panic without error instance to fail job and avoid sentry call
				// in RecoveryV3. Can occur when API is called out of sync post BC/GST modules
				panic(err.Error())
			}
			panic(err)
		}

		var resData = map[string]interface{}{
			"referenceID": referenceID,
		}

		ctx := context.WithValue(r.Context(), "resData", resData)
		ctx = context.WithValue(ctx, "statusCode", constants.APIGenerateOffersSuccess)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// GetOffersCont returns the status and data for GenerateOffer API
func GetOffersCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.RecoveryV3(w, r, http.StatusConflict)

		attributes := r.Context().Value("attributes").(map[string]interface{})
		referenceID := attributes["referenceID"].(string)
		sourceEntityID := r.Context().Value("sourceEntityID").(string)

		res, err := apiresult.GetJobResult(r.Context(), referenceID, sourceEntityID, jobs.BRE)
		if err != nil {
			var errType *structs.CustomError
			// If custom error, return API based error response
			if errors.As(err, &errType) {
				err := err.(*structs.CustomError)
				msg := err.Err.Error()
				errorHandler.CustomErrorV3(w, err.HTTPCode, constants.ErrStringToStatusCodeStringMapping[msg], msg)
				return
			}
			logger.WithRequest(r).Error(err)
			panic(err)
		}

		ctx := context.WithValue(r.Context(), "resData", res.Data)
		ctx = context.WithValue(ctx, "statusCode", res.StatusCode)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// AcceptOfferCont is the controller for Accept offer V2 API
func AcceptOfferCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.RecoveryV3(w, r, http.StatusConflict)

		sourceEntityID := r.Context().Value("sourceEntityID").(string)
		workflowStep := r.Context().Value("workflowStep").(int)

		attributes := r.Context().Value("attributes").(map[string]any)
		offerID := attributes["offerID"].(string)
		customerID, _ := attributes["customerID"].(string)
		user := r.Context().Value("user").(users.User)
		amount := attributes["amount"].(float64)
		tenure := attributes["tenure"].(int)
		interest := attributes["interest"].(float64)
		// interestType := attributes["interestType"].(string)
		// interestMethod := attributes["interestMethod"].(string)
		processingFee := attributes["processingFee"].(float64)
		// processingFeeType := attributes["processingFeeType"].(string)
		charges := attributes["charges"].([]structs.Charges)
		emi := attributes["emi"].(float64)
		experimentationFlag := attributes["experimentationFlag"].(string)

		if experimentationFlag != "" {
			err := users.UpdateDynamicUserInfoFieldV2(user.ID, "acceptOfferExperimentationFlag", experimentationFlag, nil)
			if err != nil {
				logger.WithUser(user.ID).Errorln(err)
				panic(err)
			}
		}

		// Check if accept offer validations feature flag is enabled for the user
		if featureflag.Get(user.ID, journey.FlagAcceptOfferValidations) || featureflag.Get(user.ID, journey.FlagAcceptOfferExperimentation) {

			// Perform accept offer validation
			lenderID, _ := underwriting.GetLenderID(sourceEntityID, user.ID, "")
			validationsResp, validationErr := performAcceptOfferValidations(r.Context(), AcceptOfferValidationsOptions{
				UserID:         user.ID,
				LenderID:       lenderID,
				SourceEntityID: sourceEntityID,
				Interest:       interest,
				Amount:         amount,
				ProcessingFee:  processingFee,
			})

			if validationErr != nil {
				logger.WithUser(user.ID).Errorln(validationErr)
				errorHandler.CustomErrorV3(w, constants.ErrStringToStatusCodeMapping[constants.GenericFailureMessage], constants.ErrStringToStatusCodeStringMapping[constants.GenericFailureMessage], constants.GenericFailureMessage)
				return
			}

			if validationsResp != nil && !validationsResp.IsPass {
				err := fmt.Errorf("accept offer validations failed, Message: %s", validationsResp.Message)
				logger.WithUser(user.ID).Errorln(err)
				errorHandler.CustomErrorV3(w, http.StatusBadRequest, constants.ErrStringToStatusCodeStringMapping[constants.RequestValidationFailedMessage], validationsResp.Message)
				return
			}
		}

		isABFLBLSourcing := journey.IsABFLBLSourcing(sourceEntityID)
		isIIFLBLSourcing := journey.IsIIFLBLSourcing(sourceEntityID)
		isABFLPLSourcing := journey.IsABFLPLSourcing(sourceEntityID)
		isSuperMoneySourcing := sourceEntityID == constants.SuperMoneyID

		if isABFLPLSourcing {
			dedupe, reason, err := fraudcheckutils.ABFLPLMobilePANDedupe(user.ID, user.SourceEntityID, user.PAN, user.Mobile)
			if err != nil {
				logger.WithUser(user.ID).Errorln(err)
				errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": user.ID}, err)
				errorHandler.CustomErrorV3(w, constants.ErrStringToStatusCodeMapping[constants.GenericFailureMessage], constants.ErrStringToStatusCodeStringMapping[constants.GenericFailureMessage], constants.GenericFailureMessage)
				return
			}

			if dedupe {
				usersutil.DisqualifyUser(user.ID, user.SourceEntityID, reason, "")

				var resData = map[string]interface{}{
					"message": constants.ErrDuplicateLoanApplication,
				}
				ctx := context.WithValue(r.Context(), "resData", resData)
				ctx = context.WithValue(ctx, "statusCode", constants.APIAcceptOfferDuplicateApplication)
				next.ServeHTTP(w, r.WithContext(ctx))
				return
			}

			if interest == 0 {
				err := fmt.Errorf("invalid interest sent , value: %v", interest)
				logger.WithUser(user.ID).Errorln(err)
				errorHandler.CustomErrorV3(w, http.StatusBadRequest, constants.ErrStringToStatusCodeStringMapping[constants.RequestValidationFailedMessage], "invalid interest sent")
				return
			}
			offer, err := personalloanoffer.GetLatestForUser(user.ID)
			if err != nil {
				logger.WithUser(user.ID).Errorln(err)
				panic(err)
			}
			if offer.LoanOfferID != offerID {
				errorHandler.CustomErrorV3(w, http.StatusBadRequest, constants.ErrStringToStatusCodeStringMapping[constants.RequestValidationFailedMessage], "invalid offerID")
				return
			}

			validOffersCount, err := personalloanoffer.GetValidOfferCount(user.ID)
			if err != nil {
				logger.WithUser(user.ID).Errorln(err)
				panic(err)
			}

			if validOffersCount == 0 {
				// user has no valid offer
				panic(constants.ErrStringInvalidUserState)
			}
		}

		if isABFLBLSourcing {
			// Check for any existing loan with ABFL
			dedupe, reason, err := abfl.MobilePANDedupe(user.ID, user.SourceEntityID, user.PAN, user.Mobile)
			if err != nil {
				logger.WithUser(user.ID).Errorln(err)
				errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": user.ID}, err)
				errorHandler.CustomErrorV3(w, constants.ErrStringToStatusCodeMapping[constants.GenericFailureMessage], constants.ErrStringToStatusCodeStringMapping[constants.GenericFailureMessage], constants.GenericFailureMessage)
				return
			}

			// Reject if dedupe found
			if dedupe {
				usersutil.DisqualifyUser(user.ID, user.SourceEntityID, reason, "")

				var resData = map[string]interface{}{
					"message": constants.ErrDuplicateLoanApplication,
				}
				ctx := context.WithValue(r.Context(), "resData", resData)
				ctx = context.WithValue(ctx, "statusCode", constants.APIAcceptOfferDuplicateApplication)
				next.ServeHTTP(w, r.WithContext(ctx))
				return
			}

			// hard check on offerID to prevent allowing acceptance of "tentative" offer in case booster has been generated
			offer, err := businessloanoffer.GetLatestForUser(user.ID)
			if err != nil {
				logger.WithUser(user.ID).Errorln(err)
				panic(err)
			}

			if offer.LoanOfferID != offerID {
				errorHandler.CustomErrorV3(w, http.StatusBadRequest, constants.ErrStringToStatusCodeStringMapping[constants.RequestValidationFailedMessage], "invalid offerID")
				return
			}

			hasValidOffer, err := businessloanoffer.HasValidOffer(user.ID)
			if err != nil {
				logger.WithUser(user.ID).Errorln(err)
				panic(err)
			}

			if !hasValidOffer {
				// user has no valid offer
				panic(constants.ErrStringInvalidUserState)
			}

			skipBooster, bankBooster, gstBooster := underwriting.APIStackGetBoosterOptions(user.ID, sourceEntityID)
			if !skipBooster {
				// check if BRE was completed if mandatory
				sourceType := ""

				switch {
				case bankBooster:
					sourceType = constants.SourceTypeBanking
				case gstBooster:
					sourceType = constants.SourceTypeGST
				}

				done, err := underwriting.IsBRECompleted(user.ID, sourceType)
				if err != nil {
					logger.WithUser(user.ID).Errorln(err)
					panic(err)
				}

				if !done {
					panic(constants.ErrStringInvalidUserState)
				}
			}

			wf, err := workflowutils.GetCurrentWorkFlow(user.ID)
			if err != nil {
				logger.WithUser(user.ID).Errorln(err)
				panic(err)
			}

			if !journey.IsABFLFinarkeinEntity(sourceEntityID) && !general.InArr(wf.WorkFlowName, []string{constants.WorkflowABFLAPIStackBoosterBankConnect, constants.WorkflowABFLAPIStackBoosterGst}) {
				err = workflowutils.UpdateWorkFlow(user.ID, sourceEntityID, constants.WorkflowABFLAPIStack)
				if err != nil {
					logger.WithUser(user.ID).Errorln(err)
					panic(err)
				}
			}
		}

		if isIIFLBLSourcing {
			hasValidOffer, err := businessloanoffer.HasValidOffer(user.ID)
			if err != nil {
				logger.WithUser(user.ID).Errorln(err)
				panic(err)
			}

			if !hasValidOffer {
				// user has no valid offer
				panic(constants.ErrStringInvalidUserState)
			}
		}

		if isSuperMoneySourcing {
			offer := personalloanoffer.Get(offerID)
			logger.WithUser(user.ID).Infoln("OFFER : ", offer)
			if !(offer.OfferType == "final" || offer.OfferMetadataObj.Offers[0].Segments == poonawalla.SegmentBlue) {
				logger.WithUser(user.ID).Errorln("offerType is not final for no blue segment users")
				errorHandler.CustomErrorV3(w, http.StatusBadRequest, constants.ErrStringToStatusCodeStringMapping[constants.ErrStringInvalidUserState], constants.ErrStringInvalidUserState)
				return
			} else if offer.OfferMetadataObj.Offers[0].Segments == poonawalla.SegmentBlue && offer.OfferType != "final" {
				err := personalloanoffer.UpdateOfferTypeByOfferID(nil, offerID, "final")
				if err != nil {
					logger.WithUser(user.ID).Errorln("error updating blue offer as final")
					errorHandler.CustomErrorV3(w, http.StatusBadRequest, constants.ErrStringToStatusCodeStringMapping[constants.ErrStringInvalidUserState], constants.ErrStringInvalidUserState)
					return
				}
			}
			emi, _, _ = calc.GetEMI(
				offer.Method,
				amount,
				tenure,
				interest,
				time.Now(),
				sourceEntityID,
				offer.LenderID,
				user.ID,
			)
			isValid, err := offersetter.OfferSlabValidatorPL(
				offer.OfferMetadataObj.Offers,
				offersetter.SelectedOfferStruct{
					Amount:        amount,
					Tenure:        tenure,
					Interest:      interest,
					ProcessingFee: offer.ProcessingFee,
					EMI:           emi,
				})
			if err != nil {
				logger.WithUser(user.ID).Error(err)
				errorHandler.CustomErrorV3(w, http.StatusBadRequest, constants.APIAcceptOfferInvalidInput, "invalid offer")
				return
			}
			if !isValid {
				panic(constants.ErrStringInvalidUserState)
			}
		}

		ctx := r.Context()
		res, customErr := offersetter.AcceptOffers(&ctx, offerID, user.ID, sourceEntityID, amount, interest, tenure, r.RemoteAddr, processingFee, "")
		resMessage := ""
		var sessionUrl string
		if customErr != nil {
			switch customErr.Err.Error() {
			case constants.ErrStringOfferExpired:
				errorHandler.CustomErrorV3(w, customErr.HTTPCode, customErr.ErrCode, customErr.Err.Error())
				return
			}
			switch customErr.HTTPCode {
			case http.StatusConflict:
				logger.WithUser(user.ID).Errorln(customErr.Error())
				panic(customErr.Error())
			case http.StatusBadRequest:
				errorHandler.CustomErrorV3(w, http.StatusBadRequest, constants.APIAcceptOfferInvalidInput, customErr.Error())
				return
			default:
				logger.WithUser(user.ID).Errorln(customErr)
				if !general.IsContextCancelledOrDeadline(ctx) {
					panic(customErr)
				}
				errorHandler.CustomErrorV3(w, http.StatusRequestTimeout, constants.APIAcceptOfferInvalidInput, "request cancelled by client")
				return
			}
		} else if res != "" {
			// Handle offer already accepted
			if res == offersetter.OfferAlreadyAccepted {
				var resData = map[string]interface{}{
					"message": "ok",
				}
				ctx = context.WithValue(ctx, "resData", resData)
				ctx = context.WithValue(ctx, "statusCode", constants.APIStatusCodeAlreadyAccepted)
				next.ServeHTTP(w, r.WithContext(ctx))
				return
			} else {
				errorHandler.CustomErrorV3(w, http.StatusConflict, constants.APIAcceptOfferInvalidInput, res)
				return
			}
		} else {

			// loanApplicationID
			loan, err := loanapplication.GetLatestByUser(user.ID)
			if err != nil {
				logger.WithUser(user.ID).Error(err)
				panic(err)
			}
			// TODO: Move these types of activities to a workflow
			if isSuperMoneySourcing {
				_, err = poonawalla.UpdateApplication(ctx, user.ID, loan.ID.String(), poonawalla.IntentPostOfferUpdate, map[string]interface{}{"status": "APPROVED"}, sourceEntityID)
				if err != nil {
					logger.WithUser(user.ID).Error(err)
					panic(err)
				}
				user.MapifyDynamicUserInfo()
				redirectURL := cast.ToString(user.DynamicUserInfoMap["lspRedirectURL"])
				sdkParams := structs.WebSDKParamsStruct{
					SourceEntityID: sourceEntityID,
					CustomerID:     customerID,
					RedirectURL:    redirectURL,
					TTL:            constants.DefaultSDKSessionTimeout,
					HidePoweredBy:  false,
				}
				url, _, _, errMessage, errHTTPCode, err := sourceentity.GetWebSDK(sdkParams)
				if err != nil || errMessage != "" {
					err = fmt.Errorf("Err: %s, ErrMessage: %s, errHTTPCode %d", err, errMessage, errHTTPCode)
					logger.WithUser(user.ID).Error(err)
					panic(err)
				}
				sessionUrl = url
			}
			if !isABFLBLSourcing && !isIIFLBLSourcing && !isABFLPLSourcing && !isSuperMoneySourcing {
				var dynamicCharges struct {
					Charges []structs.Charges `json:"charges"`
				}

				var dynamicChargesString string

				if len(charges) > 0 {
					dynamicCharges.Charges = charges
					dynamicChargesString = general.AnyToJSONString(dynamicCharges)
				}

				// Update processing fee
				// TODO: Check if this is required for BL
				loanStatus := constants.LoanStatusLoanApproved
				kycStatus := constants.LoanKYCStatusDocApproved
				laUpdate := loanapplication.StructForSet{
					ID:            loan.ID.String(),
					ProcessingFee: &processingFee,
					Interest:      &interest,
					Status:        &loanStatus,
					KYCStatus:     &kycStatus,
				}

				if len(dynamicChargesString) > 0 {
					laUpdate.DynamicCharges = dynamicChargesString
				}
				if emi > 0 {
					laUpdate.EMI = emi
				}

				// TODO: @kushal
				// If KYC Manual QC Feature is enabled, then check KYC status and update loan application status accordingly
				if featureflag.Get(user.ID, journey.FlagKYCManualReview) {
					// check kyc status from user kyc application, and set kyc status to manual review if kyc is not success
					app, err := userkycapplications.GetLatestByUser(user.ID, sourceEntityID)
					if err != nil {
						logger.WithUser(user.ID).Error(err)
						panic(err)
					}
					if app.Status == userkycapplications.ConditonalSuccess {
						kycStatus = constants.LoanKYCStatusUnderReview
						laUpdate.KYCStatus = &kycStatus
					}
				}
				err = loanapplication.Update(nil, laUpdate)
				if err != nil {
					logger.WithUser(user.ID).Error(err)
					panic(err)
				}

				err = copyAPIStackKYCData(r.Context(), user.ID, loan.ID.String(), user.SourceEntityID)
				if err != nil {
					logger.WithUser(user.ID).Errorln(err)
					panic(err)
				}
				logger.DebugWithUser(user.ID, "KYC data copy completed")
			}

			// mark API stack module as completed
			err = userapimodulemapping.CreateWithOptions(nil, user.ID, user.ID, userapimodulemapping.ModuleOffer, userapimodulemapping.StatusCompleted, workflowStep, "", userapimodulemapping.CreateOptions{
				SignalTSM: false,
			})
			if err != nil {
				logger.WithUser(user.ID).Errorln(err)
				panic(err)
			}
			resMessage = "ok"
		}

		var resData = map[string]interface{}{
			"message":        resMessage,
			"redirectionUrl": sessionUrl,
		}

		ctx = context.WithValue(ctx, "resData", resData)
		ctx = context.WithValue(ctx, "statusCode", constants.APIAcceptOfferSuccess)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func ApplicationStatusCont(next http.Handler) http.Handler {
	// Get the sourceEntityID from the context and check if it is empty or not
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.RecoveryV3(w, r, http.StatusConflict)

		sourceEntityID := r.Context().Value("sourceEntityID").(string)

		attributes := r.Context().Value("attributes").(map[string]interface{})
		customerID, _ := attributes["customerID"].(string)

		user, err := users.GetByUniqueID(customerID, sourceEntityID)
		if err != nil {
			errorHandler.CustomErrorV3(w, http.StatusBadRequest, constants.ErrStringToStatusCodeStringMapping[constants.ErrStringUserNotFound], constants.ErrStringUserNotFound)
			return
		}
		tags.SetTagValue(r.Context(), tags.UserObject, user)

		results, err := activitylog.GetActivityLogsByUserID(user.ID)
		if err != nil {
			if errors.Is(err, sql.ErrNoRows) {
				errorHandler.CustomErrorV3(w, http.StatusBadRequest, constants.APIGetApplicationStatusNoEventFound, "events not found")
				return
			}
			logger.WithUser(user.ID).Errorln(err)
			panic(err)
		}

		var generateRedirectionURL bool
		for _, result := range results {
			if result.EventType == "offer_accepted" {
				generateRedirectionURL = true
				break
			}
		}
		var data = map[string]interface{}{
			"events": results,
		}

		switch {
		case generateRedirectionURL && sourceEntityID == constants.SuperMoneyID && strings.Contains(r.URL.EscapedPath(), "getApplicationStatus"):

			user.MapifyDynamicUserInfo()
			redirectURL := cast.ToString(user.DynamicUserInfoMap["lspRedirectURL"])
			sdkParams := structs.WebSDKParamsStruct{
				SourceEntityID: sourceEntityID,
				CustomerID:     customerID,
				RedirectURL:    redirectURL,
				TTL:            constants.DefaultSDKSessionTimeout,
				HidePoweredBy:  false,
			}
			url, _, _, errMessage, errHTTPCode, err := sourceentity.GetWebSDK(sdkParams)
			if err != nil || errMessage != "" {
				err = fmt.Errorf("Err: %v, ErrMessage: %s, errHTTPCode %d", err, errMessage, errHTTPCode)
				logger.WithUser(user.ID).Error(err)
				errorHandler.CustomErrorV3(w, http.StatusBadRequest, constants.APIGetApplicationStatusFailed, err.Error())
				return
			}
			data["redirectionUrl"] = url

		case sourceEntityID == constants.PrefrSuperMoneyID:

			switch *user.Status {
			// Skip generation of redirect url if user is disqualified
			case constants.UserStatusDisqualified:
				data["redirectionUrl"] = ""
			default:
				resp, err := lenderservice.RedirectUser(r.Context(), &lenderservice.ApplicationReq{
					UserID:         user.ID,
					LenderID:       constants.PrefrSMLenderID,
					SourceEntityID: constants.PrefrSuperMoneyID,
				})
				if err != nil {
					logger.WithUser(user.ID).Error(err)
					errorHandler.CustomErrorV3(w, http.StatusBadRequest, constants.APIGetApplicationStatusFailed, err.Error())
					return
				}
				data["redirectionUrl"] = resp.URL
			}
		}

		lenderID, _ := underwriting.GetLenderID(sourceEntityID, user.ID, "")

		additional := map[string]interface{}{
			"customerID": customerID,
			"lenderID":   lenderID,
			"userID":     user.ID,
		}

		// Get additional loan data using the supermoney utility
		additionalLoanData, err := supermoney.GetAdditionalLoanData(r.Context(), user.ID)
		if err != nil {
			logger.WithUser(user.ID).Errorln(err)
			panic(err)
		}

		additionalLoanDataMap, err := additionalLoanData.ToStringMap()
		if err != nil {
			logger.WithUser(user.ID).Errorln(err)
			panic(err)
		}

		// Merge the additional loan data with our base additional data
		maps.Copy(additional, additionalLoanDataMap)

		data["additional"] = additional

		ctx := context.WithValue(r.Context(), "resData", data)
		ctx = context.WithValue(ctx, "statusCode", constants.APIGetApplicationStatusSuccess)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// UploadCont is the controller for Uploading user document APIs
// which are independent of Loan Applications
func UploadCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.RecoveryV3(w, r, http.StatusConflict)

		sourceEntityID := r.Context().Value("sourceEntityID").(string)

		program := r.FormValue("program")
		customerID := r.FormValue("customerID")
		documentType := r.FormValue("documentType")

		if customerID == "" {
			errorHandler.CustomErrorV3(w, http.StatusBadRequest,
				constants.ErrStringToStatusCodeStringMapping[constants.ErrCustomerIDRequired],
				constants.ErrCustomerIDRequired)
			return
		}

		if documentType == "" {
			errorHandler.CustomErrorV3(w, http.StatusBadRequest, constants.APIMissingDocumentType, constants.ResponseCodeMessage[constants.APIMissingDocumentType])
			return
		}

		if !apistack.IsValidProgram(&program) {
			errStr := "invalid program sent"
			errorHandler.CustomErrorV3(w, constants.ErrStringToStatusCodeMapping[constants.ErrStringInvalidProgram], constants.ErrStringToStatusCodeStringMapping[constants.ErrStringInvalidProgram], errStr)
			return
		}

		user, err := users.GetByUniqueID(customerID, sourceEntityID)
		if err != nil {
			logger.WithRequest(r).Errorln(err)
			if err == sql.ErrNoRows {
				errorHandler.CustomErrorV3(w, constants.ErrStringToStatusCodeMapping[constants.ErrStringUserNotFound], constants.ErrStringToStatusCodeStringMapping[constants.ErrStringUserNotFound], constants.ErrStringUserNotFound)
				return
			}
			panic(err)
		}

		if *user.Status == constants.UserStatusDisqualified {
			errorHandler.CustomErrorV3(w, constants.ErrStringToStatusCodeMapping[constants.ErrUserDisqualified], constants.ErrStringToStatusCodeStringMapping[constants.ErrUserDisqualified], constants.ErrUserDisqualified)
			return
		}

		type documentUploadRes struct {
			FileID   string `json:"fileID"`
			FileName string `json:"fileName"`
		}
		var uploadedDocs []documentUploadRes

		var documents []structs.APIStackAcceptedDocument
		jsonStr := constants.APIStackAcceptedDocuments
		err = json.Unmarshal([]byte(jsonStr), &documents)
		if err != nil {
			logger.WithUser(user.ID).Errorln(err)
			panic(err)
		}

		supportedDocs := make([]string, len(documents))
		for _, v := range documents {
			supportedDocs = append(supportedDocs, v.DocumentID)
		}

		if !general.InArr(documentType, supportedDocs) {
			errorHandler.CustomErrorV3(w, http.StatusBadRequest, constants.APIInvalidDocumentType, constants.ResponseCodeMessage[constants.APIInvalidDocumentType])
			return
		}

		for _, d := range documents {
			if d.DocumentID == documentType {
				for _, v := range d.Documents {
					file, header, err := r.FormFile(v.Key)
					if err != nil {
						logger.WithUser(user.ID).Errorln(err)
						errorHandler.CustomErrorV3(w, http.StatusBadRequest, constants.APIMissingFileParameter, constants.ResponseCodeMessage[constants.APIMissingFileParameter])
						return
					}

					if header == nil {
						logger.WithUser(user.ID).Errorln("header not sent in request")
						errorHandler.CustomErrorV3(w, http.StatusBadRequest, constants.ErrStringToStatusCodeStringMapping[constants.ErrInvalidFile], constants.ErrInvalidFile)
						return
					}

					fileNameArr := strings.Split(header.Filename, ".")
					if len(fileNameArr) < 2 {
						logger.WithUser(user.ID).Errorln("missing file extension")
						errorHandler.CustomErrorV3(w, http.StatusBadRequest, constants.ErrStringToStatusCodeStringMapping[constants.ErrInvalidFile], constants.ErrInvalidFile)
						return
					}

					buff := make([]byte, 512)
					_, err = file.Read(buff)
					if err != nil {
						logger.WithUser(user.ID).Errorln(err)
						errorHandler.CustomErrorV3(w, http.StatusBadRequest, constants.ErrStringToStatusCodeStringMapping[constants.ErrInvalidFile], constants.ErrInvalidFile)
						return
					}

					contentType := http.DetectContentType(buff)
					if !general.InArr(contentType, v.MIMETypes) {
						logger.WithUser(user.ID).Errorln("invalid file mimetype")
						errorHandler.CustomErrorV3(w, http.StatusBadRequest, constants.ErrStringToStatusCodeStringMapping[constants.ErrInvalidFile], constants.ErrInvalidFile)
						return
					}

					_, err = file.Seek(0, 0)
					if err != nil {
						logger.WithUser(user.ID).Errorln(err)
						panic(err)
					}

					fileKey := fmt.Sprintf("%s/%s_%s.%s", user.ID, v.Key, general.GetUUID(), strings.ToLower(fileNameArr[len(fileNameArr)-1]))
					if _, ok := s3.ReadFromMultipartFileAndUploadFileS3(file, fileKey); !ok {
						err = fmt.Errorf("could not upload file to s3: %s", fileKey)
						logger.WithUser(user.ID).Errorln(err)
						panic(err)
					}

					// If documentID was found save in Media Table
					documentID, ok := constants.MediaTypeToDocumentIDMapping[v.MediaType]
					if !ok || documentID == "" {
						err = fmt.Errorf("unhandled document uploaded, MediaType: %s", v.MediaType)
						logger.WithUser(user.ID).Warn(err)
					} else {
						// save this file in Media Table
						mediaObj := media.UploadMediaDataStruct{
							UserID:     user.ID,
							Path:       fileKey,
							MediaID:    general.GetUUID(),
							MediaType:  v.MediaType,
							DocumentID: documentID,
						}
						//TODO: do the complete flow in transaction.
						// marking the previous media as inactive
						err = media.SetMediaTypeStatus(nil, r.Context(), mediaObj, constants.MediaStatusInactive)
						if err != nil {
							logger.WithUser(user.ID).Errorln(err)
							panic(err)
						}
						err = media.Insert(nil, r.Context(), mediaObj, user.Email)
						if err != nil {
							logger.WithUser(user.ID).Errorln(err)
							panic(err)
						}
					}

					// TODO: move this in transaction
					documentReferenceID := general.GetUUID()
					err = userdocuments.Insert(r.Context(), userdocuments.UserDocument{
						ID:           documentReferenceID,
						UserID:       user.ID,
						Program:      program,
						FileKey:      fileKey,
						Status:       userdocuments.StatusActive,
						DocumentType: documentType,
						FileName:     v.Key,
					})
					if err != nil {
						logger.WithUser(user.ID).Errorln(err)
						panic(err)
					}
					uploadedDocs = append(uploadedDocs, documentUploadRes{
						FileID:   documentReferenceID,
						FileName: v.Key,
					})
				}
			}
		}

		resData := map[string]any{
			"message":   "success",
			"documents": uploadedDocs,
		}

		ctx := context.WithValue(r.Context(), "resData", resData)
		ctx = context.WithValue(ctx, "statusCode", constants.APIDocumentUploadSuccess)

		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// GetAPIStackModuleCont is the controller for APIStack APIs
func GetAPIStackModuleCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.RecoveryV3(w, r, http.StatusConflict)

		attributes := r.Context().Value("attributes").(map[string]any)
		customerID := attributes["customerID"].(string)
		sourceEntityID := r.Context().Value("sourceEntityID").(string)

		user, err := users.GetByUniqueID(customerID, sourceEntityID)
		if err != nil {
			logger.WithRequest(r).Errorln(err)
			if err == sql.ErrNoRows {
				errorHandler.CustomErrorV3(w, constants.ErrStringToStatusCodeMapping[constants.ErrStringUserNotFound], constants.ErrStringToStatusCodeStringMapping[constants.ErrStringUserNotFound], constants.ErrStringUserNotFound)
				return
			}
			panic(err)
		}
		userID := user.ID

		// if user is Disqualified then don't send next modules
		isUserDisqualified := *user.Status == constants.UserStatusDisqualified
		if isUserDisqualified {
			resData := map[string]any{}
			resData["upcoming"] = []map[string]any{}
			ctx := context.WithValue(r.Context(), "resData", resData)
			ctx = context.WithValue(ctx, "statusCode", constants.APINextAPISuccess)
			next.ServeHTTP(w, r.WithContext(ctx))
			return
		}

		lastActivity, err := userapimodulemapping.GetLast(userID)
		if err != nil {
			logger.WithUser(userID).Error(err)
			panic(err)
		}
		apiStackWorkflow, err := apistack.GetAPIStackWorkflowForUser(r.Context(), userID, sourceEntityID)
		if err != nil {
			logger.WithUser(userID).Error(err)
			panic(err)
		}

		cur := lastActivity.WorkflowStep
		if lastActivity.ModuleStatus == userapimodulemapping.StatusCompleted {
			cur += 1
		}
		for k := range apiStackWorkflow {
			if k < cur {
				delete(apiStackWorkflow, k)
			}
		}
		resData := map[string]any{}
		currentAPIModule, pass := apiStackWorkflow[cur]
		if pass {
			resData["upcoming"] = []map[string]any{{
				"endpoint": currentAPIModule.Endpoint,
				"method":   currentAPIModule.Method,
				"optional": currentAPIModule.Optional,
			}}
		} else {
			resData["upcoming"] = []map[string]any{}
		}

		ctx := context.WithValue(r.Context(), "resData", resData)
		ctx = context.WithValue(ctx, "statusCode", constants.APINextAPISuccess)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// SessionServerCont returns web sdk url to the server
func SessionServerCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.RecoveryV3(w, r, http.StatusConflict)

		sourceEntityID := r.Context().Value("sourceEntityID").(string)

		attributes := r.Context().Value("attributes").(map[string]interface{})
		customerID := attributes["customerID"].(string)
		redirectURL := attributes["redirectURL"].(string)
		withdrawAmount := attributes["withdrawAmount"].(float64)
		source := attributes["source"].(string)
		hideClose := attributes["hideClose"].(bool)
		hidePoweredBy := attributes["hidePoweredBy"].(bool)
		hideFaq := attributes["hideFaq"].(bool)
		programName := attributes["programName"].(string)

		sdkParams := structs.WebSDKParamsStruct{
			SourceEntityID: sourceEntityID,
			CustomerID:     customerID,
			RedirectURL:    redirectURL,
			WithdrawAmount: withdrawAmount,
			HideClose:      hideClose,
			OrderName:      "",
			SdkType:        "",
			TTL:            constants.DefaultSDKSessionTimeout,
			UserToken:      "",
			ProgramName:    programName,
			HidePoweredBy:  hidePoweredBy,
			HideFaq:        hideFaq,
		}
		url, userID, _, errMessage, errHTTPCode, err := sourceentity.GetWebSDK(sdkParams)
		if err != nil {
			panic(err)
		}
		if errHTTPCode == http.StatusForbidden {
			errHTTPCode = http.StatusConflict
		}
		if errMessage != "" {
			errorHandler.CustomErrorV3(w, errHTTPCode, constants.APIStatusBadRequest, errMessage)
			return
		}

		if source != "" {
			usersutil.UpdateUserSource(userID, source, map[string]interface{}{})
		}

		var resData = map[string]interface{}{
			"url": url,
		}

		ctx := context.WithValue(r.Context(), "resData", resData)
		ctx = context.WithValue(ctx, "statusCode", constants.APIGetServerSessionSuccess)
		next.ServeHTTP(w, r.WithContext(ctx))

	})
}

func InitiateBankConnectCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.RecoveryV3(w, r, http.StatusConflict)

		sourceEntityID := r.Context().Value("sourceEntityID").(string)

		attributes := r.Context().Value("attributes").(map[string]interface{})
		customerID := attributes["customerID"].(string)
		redirectURL := attributes["redirectURL"].(string)
		withdrawAmount := attributes["withdrawAmount"].(float64)
		workflowStep, _ := r.Context().Value("workflowStep").(int)
		// source := attributes["source"].(string)
		hideClose := attributes["hideClose"].(bool)
		hidePoweredBy := attributes["hidePoweredBy"].(bool)
		programName := attributes["programName"].(string)

		if conf.ENV != conf.ENV_PROD {
			log.Println(attributes)
		}

		user, err := users.GetByUniqueID(customerID, sourceEntityID)
		if err != nil {
			logger.WithRequest(r).Errorln(err)
			if err == sql.ErrNoRows {
				panic(constants.ErrStringUserNotFound)
			}
			panic(err)
		}

		signalBoostAttempt, signalName, signalData, err := ShouldSignalBoostAttemp(user.ID)
		if err != nil {
			logger.WithUser(user.ID).Errorln(err)
			panic(err)
		}

		// NOTE: Signal BC workflow for boost attempt if a running wf is found
		if signalBoostAttempt {
			_, err = temporalutility.SignalWorkflow(context.Background(), user.ID, usermodulemapping.BankConnect, signalName, "api_stack_initiate_bankconnect", nil, signalData)
			if err != nil {
				logger.WithUser(user.ID).Errorln(err)
				panic(err)
			}
		} else {
			err = userbankconnectsessions.ExpireAllActiveSessions(user.ID)
			if err != nil && !errors.Is(err, sql.ErrNoRows) {
				logger.WithUser(user.ID).Errorln(err)
				panic(err)
			}
		}

		err = featureflag.SetV2(featureflag.SetStruct{Flag: journey.FlagBankConnectAPIStack, Key: user.ID, Value: true}, nil)
		if err != nil {
			logger.WithUser(user.ID).Errorln(err)
			panic(err)
		}
		if journey.IsABFLBLSourcing(sourceEntityID) {
			err = workflowutils.UpdateWorkFlow(user.ID, sourceEntityID, constants.WorkflowABFLAPIStackBoosterBankConnect)
			if err != nil {
				logger.WithUser(user.ID).Errorln(err)
				panic(err)
			}
		}
		var sessionURL string
		if featureflag.Get(user.ID, journey.FlagTemporalBankConnectApiStack) {
			// Execute BC workflow and set referenceID
			_, shouldTrigger, err := userworkflow.ShouldTriggerUserWorkflow(user.ID, sourceEntityID, usermodulemapping.BankConnect)
			if err != nil {
				logger.WithUser(user.ID).Errorln(err)
				panic(err)
			}

			if !shouldTrigger {
				url, _, err := temporalutility.FetchOutputKeyFromWorkflow(r.Context(), user.ID, usermodulemapping.BankConnect, constants.OutputKeyBankingRedirectURL, nil, 500, 20)
				if err != nil {
					logger.WithUser(user.ID).Errorln(err)
				}
				sessionURL, _ = url.(string)
				logger.DebugWithUser(user.ID, "session URL generated: ", url)
				// Dont re-trigger if already running
				var resData = map[string]interface{}{
					"url": sessionURL,
				}
				ctx := context.WithValue(r.Context(), "resData", resData)
				ctx = context.WithValue(ctx, "statusCode", constants.APIStatusCodeAlreadyAccepted)
				next.ServeHTTP(w, r.WithContext(ctx))
				return
			}

			_, err = wfconfigfunctions.StartWorkflowFromConfig(r.Context(), wfconfigfunctions.StartWorkflowOption{
				Data:              map[string]any{"userObj": user, "lenderID": constants.PoonawallaFincorpID, "req": attributes, "workflowStep": workflowStep},
				SourceEntityID:    sourceEntityID,
				UserID:            user.ID,
				WorkflowType:      usermodulemapping.BankConnect,
				UserWorkflowRowID: general.GetUUID(),
			})
			if err != nil {
				logger.WithUser(user.ID).Errorln(err)
				panic(err)
			}

			url, _, err := temporalutility.FetchOutputKeyFromWorkflow(r.Context(), user.ID, usermodulemapping.BankConnect, constants.OutputKeyBankingRedirectURL, nil, 500, 20)
			if err != nil {
				logger.WithUser(user.ID).Errorln(err)
				panic(err)
			}
			logger.DebugWithUser(user.ID, "session URL generated: ", sessionURL)
			if urlStr, ok := url.(string); !ok || url == "" {
				logger.WithUser(user.ID).Errorln("no session url found")
				err = fmt.Errorf("no session url found")
				panic(err)
			} else {
				sessionURL = urlStr
			}
		} else {
			err = usermodulemapping.Create(nil, user.ID, user.ID, usermodulemapping.BankConnect, constants.UserModuleStatusPending, "")
			if err != nil {
				logger.WithUser(user.ID).Errorln(err)
				panic(err)
			}

			ttl := constants.DefaultSDKSessionTimeout
			sdkParams := structs.WebSDKParamsStruct{
				SourceEntityID: sourceEntityID,
				CustomerID:     customerID,
				RedirectURL:    redirectURL,
				WithdrawAmount: withdrawAmount,
				HideClose:      hideClose,
				OrderName:      "",
				SdkType:        "",
				TTL:            ttl,
				UserToken:      "",
				ProgramName:    programName,
				HidePoweredBy:  hidePoweredBy,
			}

			url, _, _, errMessage, errHTTPCode, err := sourceentity.GetWebSDK(sdkParams)
			if err != nil {
				panic(err)
			}
			if errHTTPCode == http.StatusForbidden {
				errHTTPCode = http.StatusConflict
			}
			if errMessage != "" {
				errorHandler.CustomErrorV3(w, errHTTPCode, constants.APIStatusBadRequest, errMessage)
				return
			}
			sessionURL = url
		}
		var resData = map[string]interface{}{
			"url": sessionURL,
		}

		ctx := context.WithValue(r.Context(), "resData", resData)
		ctx = context.WithValue(ctx, "statusCode", constants.APIGetServerSessionSuccess)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func InitiateGSTCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.RecoveryV3(w, r, http.StatusConflict)

		sourceEntityID := r.Context().Value("sourceEntityID").(string)

		attributes := r.Context().Value("attributes").(map[string]interface{})
		customerID := attributes["customerID"].(string)
		redirectURL := attributes["redirectURL"].(string)
		withdrawAmount := attributes["withdrawAmount"].(float64)
		// source := attributes["source"].(string)
		hideClose := attributes["hideClose"].(bool)
		hidePoweredBy := attributes["hidePoweredBy"].(bool)
		programName := attributes["programName"].(string)

		if conf.ENV != conf.ENV_PROD {
			log.Println(attributes)
		}

		user, err := users.GetByUniqueID(customerID, sourceEntityID)
		if err != nil {
			logger.WithRequest(r).Errorln(err)
			if err == sql.ErrNoRows {
				panic(constants.ErrStringUserNotFound)
			}
			panic(err)
		}

		err = usermodulemapping.Create(nil, user.ID, user.ID, usermodulemapping.GST, constants.UserModuleStatusPending, "")
		if err != nil {
			logger.WithUser(user.ID).Errorln(err)
			panic(err)
		}
		err = featureflag.SetV2(featureflag.SetStruct{Flag: journey.FlagGSTAPIStack, Key: user.ID, Value: true}, nil)
		if err != nil {
			logger.WithUser(user.ID).Errorln(err)
			panic(err)
		}
		if journey.IsABFLBLSourcing(sourceEntityID) {
			err = workflowutils.UpdateWorkFlow(user.ID, sourceEntityID, constants.WorkflowABFLAPIStackBoosterGst)
			if err != nil {
				logger.WithUser(user.ID).Errorln(err)
				panic(err)
			}
		}

		ttl := constants.DefaultSDKSessionTimeout
		sdkParams := structs.WebSDKParamsStruct{
			SourceEntityID: sourceEntityID,
			CustomerID:     customerID,
			RedirectURL:    redirectURL,
			WithdrawAmount: withdrawAmount,
			HideClose:      hideClose,
			OrderName:      "",
			SdkType:        "",
			TTL:            ttl,
			UserToken:      "",
			ProgramName:    programName,
			HidePoweredBy:  hidePoweredBy,
		}

		url, _, _, errMessage, errHTTPCode, err := sourceentity.GetWebSDK(sdkParams)
		if err != nil {
			panic(err)
		}
		if errHTTPCode == http.StatusForbidden {
			errHTTPCode = http.StatusConflict
		}
		if errMessage != "" {
			errorHandler.CustomErrorV3(w, errHTTPCode, constants.APIStatusBadRequest, errMessage)
			return
		}

		var resData = map[string]interface{}{
			"url": url,
		}

		ctx := context.WithValue(r.Context(), "resData", resData)
		ctx = context.WithValue(ctx, "statusCode", constants.APIGetServerSessionSuccess)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// SaveKYCEvaluationsCont TODO: to be removed; temp route for running migrations in-order to save kyc evaluations in db
func SaveKYCEvaluationsCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.RecoveryV3(w, r, http.StatusConflict)

		attributes := r.Context().Value("attributes").(map[string]interface{})
		userID := attributes["userID"].(string)
		applicationID := attributes["applicationID"].(string)

		err := kyc.SaveKYCEvaluations(userID, applicationID)
		if err != nil {
			logger.WithUser(userID).Error(err)
			panic(err.Error())
		}

		var resData = map[string]interface{}{
			"message": "success",
		}

		ctx := context.WithValue(r.Context(), "resData", resData)
		ctx = context.WithValue(ctx, "statusCode", constants.APIStatusSuccess)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func TriggerPrequalificationsCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.RecoveryV3(w, r, http.StatusConflict)

		sourceEntityID := r.Context().Value("sourceEntityID").(string)
		user := r.Context().Value("user").(users.User)
		workflowStep := cast.ToInt(r.Context().Value("workflowStep"))

		rowID, shouldTrigger, err := userworkflow.ShouldTriggerUserWorkflow(user.ID, sourceEntityID, userapimodulemapping.ModulePrequalification)
		if err != nil {
			logger.WithUser(user.ID).Error(err)
			panic(err)
		}
		if !shouldTrigger {
			resData := map[string]any{
				"referenceID": rowID,
			}
			ctx := context.WithValue(r.Context(), "referenceID", rowID)
			ctx = context.WithValue(ctx, "resData", resData)
			ctx = context.WithValue(ctx, "statusCode", constants.APIStatusCodeAlreadyAccepted)
			next.ServeHTTP(w, r.WithContext(ctx))
			return
		}

		lenderID, _ := underwriting.GetLenderID(sourceEntityID, user.ID, "")

		userWorkflowID := general.GetUUID()
		data := map[string]interface{}{
			"userObj":                  user,
			"lenderID":                 lenderID,
			"userWorkflowID":           userWorkflowID,
			"workflowStep":             fmt.Sprintf("%v", workflowStep),
			prequal.TemporalPersistKey: true,
		}
		_, err = wfconfigfunctions.StartWorkflowFromConfig(r.Context(), wfconfigfunctions.StartWorkflowOption{
			Data:              data,
			SourceEntityID:    sourceEntityID,
			LenderID:          lenderID,
			UserID:            user.ID,
			WorkflowType:      userapimodulemapping.ModulePrequalification,
			UserWorkflowRowID: userWorkflowID,
		})
		if err != nil {
			logger.WithUser(user.ID).Error(err)
			panic(err)
		}

		resData := map[string]any{
			"referenceID": userWorkflowID,
		}

		ctx := context.WithValue(r.Context(), "referenceID", userWorkflowID)
		ctx = context.WithValue(ctx, "resData", resData)
		ctx = context.WithValue(ctx, "statusCode", constants.APITriggerPrequalSuccess)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func GetPrequalificationResultsCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.RecoveryV3(w, r, http.StatusConflict)

		attributes := r.Context().Value("attributes").(map[string]any)
		referenceID := attributes["referenceID"].(string)
		sourceEntityID := r.Context().Value("sourceEntityID").(string)

		res, err := apiresult.GetJobResult(r.Context(), referenceID, sourceEntityID, jobs.Prequalification)
		if err != nil {
			var errType *structs.CustomError
			// If custom error, return API based error response
			if errors.As(err, &errType) {
				err := err.(*structs.CustomError)
				msg := err.Err.Error()
				errorHandler.CustomErrorV3(w, err.HTTPCode, constants.ErrStringToStatusCodeStringMapping[msg], msg)
				return
			}
			logger.WithRequest(r).Error(err)
			panic(err)
		}

		ctx := context.WithValue(r.Context(), "resData", res.Data)
		ctx = context.WithValue(ctx, "statusCode", res.StatusCode)
		next.ServeHTTP(w, r.WithContext(ctx))

	})
}

// PollPrequalificationResultsCont : Returns the results of the prequalification by polling the job results
func PollPrequalificationResultsCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.RecoveryV3(w, r, http.StatusConflict)

		user := r.Context().Value("user").(users.User)
		referenceID := cast.ToString(r.Context().Value("referenceID"))
		sourceEntityID := cast.ToString(r.Context().Value("sourceEntityID"))

		// Poll for the results of the prequalification
		var res apiresult.APIResult
		var err error

		// Poll up to 45 times with 400ms delay between attempts
		for range 100 {
			res, err = apiresult.GetJobResult(r.Context(), referenceID, sourceEntityID, jobs.Prequalification)
			if err != nil {
				logger.WithUser(user.ID).Error(err)
				panic(err)
			}

			if general.InArr(res.StatusCode, []string{constants.APIGetPreqaulStatusSuccess, constants.APIGetPrequalStatusFailed}) {
				break
			}

			time.Sleep(400 * time.Millisecond)
		}

		// Check if we got expected result after polling
		if !general.InArr(res.StatusCode, []string{constants.APIGetPreqaulStatusSuccess, constants.APIGetPrequalStatusFailed}) {
			err := fmt.Errorf("polling exhausted without getting expected result for referenceID: %s", referenceID)
			logger.WithUser(user.ID).Error(err)
			panic(err)
		}

		if res.StatusCode == constants.APIGetPrequalStatusFailed {
			err = errors.New("request timed out")
			logger.WithUser(user.ID).Error(err)
			panic(err)
		}

		ctx := context.WithValue(r.Context(), "resData", res.Data)
		ctx = context.WithValue(ctx, "statusCode", res.StatusCode)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func SaveUdhyamDetails(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.RecoveryV3(w, r, http.StatusConflict)

		file, _, err := r.FormFile("file")
		if err != nil {
			errorHandler.CustomError(w, http.StatusBadRequest, "Error reading uploaded file")
			return
		}

		defer file.Close()

		reader := csv.NewReader(file)
		headersSkipped := false
		failedUsers := []string{}
		successUsers := []string{}

		// process each row in the csv
		for {
			record, err := reader.Read()
			if err == io.EOF {
				break
			}

			if err != nil {
				logger.WithRequest(r).Error("error reading CSV file, err: ", err)
				errorHandler.CustomError(w, http.StatusInternalServerError, "error reading CSV file")
				return

			}

			// skip headers in the CSV
			if !headersSkipped {
				headersSkipped = true
				continue
			}

			// userID is expected in the CSV, if not then the file is invalid
			if len(record) < 1 {
				err := fmt.Errorf("invalid input CSV")
				logger.WithRequest(r).Error(err)
				errorHandler.CustomError(w, http.StatusBadRequest, err.Error())
				return
			}

			userID := record[0]
			userDetails, err := users.Get(userID)
			if err != nil {
				logger.WithUser(userID).Error("error in get user, err: ", err)
				failedUsers = append(failedUsers, userID)
				continue
			}

			panDetailsName, err := pandetails.GetLastVerifiedName(userID)
			if err != nil {
				logger.WithUser(userID).Error("error in get name from panDetails, err: ", err)
				failedUsers = append(failedUsers, userID)
				continue
			}

			_, err = udyamaadhar.FetchAndSaveDecentroUdyamDetails(userID, userDetails.SourceEntityID, userDetails.PAN, panDetailsName, userDetails.Mobile, userDetails.Pincode)
			if err != nil {
				logger.WithUser(userID).Error("error in Fetch And Save Udyam details, err: ", err)
				failedUsers = append(failedUsers, userID)
				continue
			}
			successUsers = append(successUsers, userID)
		}

		var resData = map[string]interface{}{
			"failed_user_ids":  failedUsers,
			"success_user_ids": successUsers,
		}

		ctx := context.WithValue(r.Context(), "resData", resData)
		ctx = context.WithValue(ctx, "statusCode", constants.APIGetUserDetailsSuccess)
		next.ServeHTTP(w, r.WithContext(ctx))

	})
}

// TODO: Remove this
func ChangeUserBusinessUANPdfURLToDownloadableURL(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.RecoveryV3(w, r, http.StatusConflict)

		attributes := r.Context().Value("attributes").(map[string]any)
		userIDs := attributes["userIDs"].([]string)

		type tempStruct struct {
			UserID      string `json:"userID"`
			UAN         string `json:"uan"`
			ErrorString string `json:"errorString"`
		}
		type result struct {
			OverallSuccess   bool         `json:"overallSuccess"`
			SuccessFullCases []tempStruct `json:"successfullCases"`
			FailedCases      []tempStruct `json:"failedCases"`
		}

		var res result

		for _, userID := range userIDs {

			uans, err := userbusinessuan.GetUserBusinessUANList(userID)
			if err != nil {
				logger.WithUser(userID).Errorln(err)
				res.FailedCases = append(res.FailedCases, tempStruct{
					UserID:      userID,
					ErrorString: err.Error(),
				})
				continue
			}

			downloadExpiryMins1Year := 1 * 60 * 24 * 365 // 1 year

			for _, uan := range uans {
				uanInfo, err := userbusinessuan.GetUserBusinessUAN(userID, uan)
				if err != nil {
					logger.WithUser(userID).Errorln(err)
					res.FailedCases = append(res.FailedCases, tempStruct{
						UserID:      userID,
						UAN:         uan,
						ErrorString: err.Error(),
					})
					continue
				}

				if uanInfo.PDFURL != "" { // Change to downloadable URL only if PDFURL is present
					objectKey, err := general.ExtractS3ObjectKey(uanInfo.PDFURL)
					if err != nil {
						logger.WithUser(userID).Errorln(err)
						res.FailedCases = append(res.FailedCases, tempStruct{
							UserID:      userID,
							UAN:         uan,
							ErrorString: err.Error(),
						})
						continue
					}

					downloadableID, err := downloadables.CreateAlongWithMediaInsertion(r.Context(), userID, media.UploadMediaDataStruct{
						MediaType: constants.MediaTypeUdyam,
						Path:      objectKey,
						UserID:    userID,
						MediaID:   general.GetUUID(),
					}, downloadables.Options{
						ExpiresInMinutes: &downloadExpiryMins1Year,
					})
					if err != nil {
						logger.WithUser(userID).Errorln(err)
						res.FailedCases = append(res.FailedCases, tempStruct{
							UserID:      userID,
							UAN:         uan,
							ErrorString: err.Error(),
						})
						continue
					}

					pdfURL := downloadables.GetDownloadURL(r.Context(), downloadableID)

					if err := userbusinessuan.Update(nil, userID, uan, "", "", "", "", "", "", pdfURL); err != nil {
						logger.WithUser(userID).Errorln(err)
						res.FailedCases = append(res.FailedCases, tempStruct{
							UserID:      userID,
							UAN:         uan,
							ErrorString: err.Error(),
						})
						continue
					}
				}

				res.SuccessFullCases = append(res.SuccessFullCases, tempStruct{
					UserID: userID,
					UAN:    uan,
				})
			}
		}

		res.OverallSuccess = len(res.FailedCases) == 0

		var resData map[string]interface{}

		json.Unmarshal([]byte(general.AnyToJSONString(res)), &resData)

		ctx := context.WithValue(r.Context(), "resData", resData)
		ctx = context.WithValue(ctx, "statusCode", constants.APIStatusSuccess)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func TriggerBankAnalysisCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.RecoveryV3(w, r, http.StatusConflict)

		sourceEntityID := r.Context().Value("sourceEntityID").(string)
		user := r.Context().Value("user").(users.User)
		attributes := r.Context().Value("attributes").(map[string]any)

		mode := attributes["mode"].(string)

		rowID, shouldTrigger, err := userworkflow.ShouldTriggerUserWorkflow(user.ID, sourceEntityID, userapimodulemapping.ModuleBankConnect)
		if err != nil {
			logger.WithUser(user.ID).Error(err)
			panic(err)
		}

		if !shouldTrigger {
			resData := map[string]any{
				"referenceID": rowID,
			}
			ctx := context.WithValue(r.Context(), "resData", resData)
			ctx = context.WithValue(ctx, "statusCode", constants.APIStatusCodeAlreadyAccepted)
			next.ServeHTTP(w, r.WithContext(ctx))
			return
		}

		// Create required data for workflow
		userWorkflowID := general.GetUUID()
		wfData := map[string]interface{}{
			"userObj":        user,
			"userID":         user.ID,
			"uniqueID":       user.UniqueID,
			"sourceEntityID": sourceEntityID,
			"userWorkflowID": userWorkflowID,
			"apiMode":        mode,
		}

		switch mode {
		case constants.BankConnectModeStatement:
			var statements []*multipart.FileHeader
			bankName := attributes["bankName"].(string)
			pdfPassword := attributes["pdfPassword"].(string)

			if fileHeaders := r.MultipartForm.File["files"]; len(fileHeaders) > 0 {
				statements = fileHeaders
			}

			wfData["bankName"] = bankName
			wfData["uploadType"] = constants.BankConnectUploadTypePDF
			wfData["mode"] = constants.BankConnectModePDF
			wfData["pdfPassword"] = pdfPassword

			medias := []media.MediaDataInsert{}
			fileMediaIDs := []string{}
			documentID := constants.MediaTypeToDocumentIDMapping[constants.MediaTypeBankStatementPDF]
			documentIDParam := sql.NullString{Valid: true, String: documentID}

			for _, statement := range statements {
				file, err := httpfileutil.ValidateFile(statement, []string{constants.BankConnectStatementFileExt}, []string{constants.BankConnectStatementMIMEType})
				if err != nil {
					logger.WithUser(user.ID).Errorln(err)
					errorHandler.CustomErrorV3(w, http.StatusBadRequest, constants.ErrStringToStatusCodeStringMapping[constants.RequestValidationFailedMessage], err.Error())
					return
				}

				fileKey := fmt.Sprintf("%s/%s_%s_bank_statement.pdf", user.ID, general.GetUUID(), bankName)
				if _, ok := s3.ReadFromMultipartFileAndUploadFileS3(file, fileKey); !ok {
					err = fmt.Errorf("could not upload file to s3: %s", fileKey)
					logger.WithUser(user.ID).Errorln(err)
					panic(err)
				}

				mediaID := general.GetUUID()
				medias = append(medias, media.MediaDataInsert{
					MediaID:    mediaID,
					MediaType:  constants.MediaTypeBankStatementPDF,
					DocumentID: documentIDParam,
					CreatedBy:  user.ID,
					UserID:     user.ID,
					Path:       fileKey,
					Email:      user.Email,
					Status:     constants.MediaStatusActive,
				})
				fileMediaIDs = append(fileMediaIDs, mediaID)
			}

			tx, err := database.Beginx()
			if err != nil {
				logger.WithUser(user.ID).Errorln(err)
				panic(err)
			}
			defer tx.Rollback()

			err = media.SetMediaTypeStatus(tx, r.Context(), media.UploadMediaDataStruct{UserID: user.ID, MediaType: constants.MediaTypeBankStatementPDF}, constants.MediaStatusInactive)
			if err != nil {
				logger.WithUser(user.ID).Errorln(err)
				panic(err)
			}

			err = media.BulkInsert(tx, r.Context(), medias)
			if err != nil {
				logger.WithUser(user.ID).Errorln(err)
				panic(err)
			}

			err = tx.Commit()
			if err != nil {
				logger.WithUser(user.ID).Errorln(err)
				panic(err)
			}

			wfData["bankStatementFileMediaIDs"] = fileMediaIDs
		case constants.BankConnectModeAA:
			aaEntity := attributes["aaEntity"].(string)
			aaData := attributes["aaData"].(map[string]any)

			wfData["aaEntityID"] = aaEntity
			wfData["aaDataBody"] = aaData
			wfData["initProcessing"] = false // explicitly initialise processing in workflow
		}

		lenderID, _ := underwriting.GetLenderID(sourceEntityID, user.ID, "")
		_, err = wfconfigfunctions.StartWorkflowFromConfig(context.Background(), wfconfigfunctions.StartWorkflowOption{
			Data:              wfData,
			SourceEntityID:    sourceEntityID,
			LenderID:          lenderID,
			UserID:            user.ID,
			WorkflowType:      jobs.BankAnalysis,
			UserWorkflowRowID: userWorkflowID,
			Metadata:          map[string]any{},
		})
		if err != nil {
			logger.WithUser(user.ID).Error(err)
			panic(err)
		}

		resData := map[string]any{
			"referenceID": userWorkflowID,
		}

		ctx := context.WithValue(r.Context(), "resData", resData)
		ctx = context.WithValue(ctx, "statusCode", constants.APITriggerBankAnalysisSuccess)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func GetBankAnalysisResult(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.RecoveryV3(w, r, http.StatusConflict)

		attributes := r.Context().Value("attributes").(map[string]any)
		referenceID := attributes["referenceID"].(string)
		sourceEntityID := r.Context().Value("sourceEntityID").(string)

		res, err := apiresult.GetJobResult(r.Context(), referenceID, sourceEntityID, jobs.BankAnalysis)
		if err != nil {
			var errType *structs.CustomError
			// If custom error, return API based error response
			if errors.As(err, &errType) {
				err := err.(*structs.CustomError)
				msg := err.Err.Error()
				errorHandler.CustomErrorV3(w, err.HTTPCode, constants.ErrStringToStatusCodeStringMapping[msg], msg)
				return
			}
			logger.WithRequest(r).Error(err)
			panic(err)
		}

		ctx := context.WithValue(r.Context(), "resData", res.Data)
		ctx = context.WithValue(ctx, "statusCode", res.StatusCode)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}
