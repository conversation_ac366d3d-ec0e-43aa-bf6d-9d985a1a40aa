package user

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"finbox/go-api/authentication"
	"finbox/go-api/common/usersutil"
	"finbox/go-api/constants"
	"finbox/go-api/functions/activity"
	"finbox/go-api/functions/journey"
	"finbox/go-api/functions/legallogs"
	"finbox/go-api/functions/lenders/abfl"
	"finbox/go-api/functions/logger"
	"finbox/go-api/functions/services/pannumber"
	"finbox/go-api/functions/services/pincodeapi"
	"finbox/go-api/models/companydetails"
	"finbox/go-api/models/featureflag"
	"finbox/go-api/models/lenderdropdown"
	"finbox/go-api/models/pandetails"
	"finbox/go-api/models/userbusiness"
	"finbox/go-api/models/userbusinessuan"
	"finbox/go-api/models/userjourney"
	"finbox/go-api/models/userlocation"
	"finbox/go-api/models/users"
	"finbox/go-api/utils/apischemamapper"
	"finbox/go-api/utils/general"
	"fmt"
	"strings"
	"time"
)

func PFLPersonalInfoSubmit(ctx context.Context, userObj authentication.UserStruct, data map[string]interface{}, updateRedis bool, token string) error {

	occupationType, _ := data["occupationType"].(string)
	employerName, _ := data["employerName"].(string)
	if strings.ToLower(occupationType) == constants.OccupationTypeSalaried {
		if strings.ToLower(employerName) == "other" {
			data["employerName"] = ""
		} else {
			sourceEntityID := userObj.SourceEntityID
			journey.GetParentSourceEntityID("", &sourceEntityID)
			companyDetails, err := companydetails.FetchCompanyDetails(data["employerName"].(string), sourceEntityID)
			if err != nil {
				logger.WithUser(userObj.UserID).Errorln(err)
				return err
			}
			data["companyDomain"] = companyDetails.CompanyDomain
			data["companyCategory"] = companyDetails.CompanyCategory
			data["segment"] = companyDetails.Segment
		}
	}

	currentDynamicUserInfo, err := users.GetDynamicUserInfoMap(userObj.UserID)
	if err != nil {
		logger.WithUser(userObj.UserID).Errorln(err)
		return err

	}

	// if journey type is topup or preApproved then set segment as empty to move user to normal flow in journey... as prime checks are present in workflow based on segment
	journeyType, _ := currentDynamicUserInfo[constants.PFLDynamicUserInfoKeyJourneyType].(string)
	if journeyType == constants.PFLJourneyTopUP || journeyType == constants.PFLJourneyPreApproved {
		data[constants.PFLDynamicUserInfoKeyJourneyType] = journeyType
		data["segment"] = ""
	}

	dynamicUserInfoStr, err := json.Marshal(data)
	if err != nil {
		logger.WithUser(userObj.UserID).Errorln(err)
		return err
	}
	gender := constants.GenderStrToNum[data["gender"].(string)]
	if gender == -1 {
		// default to male for now
		gender = constants.GenderStrToNum["Male"]
	}

	deviceID, _ := data["deviceID"].(string)

	userStatus := constants.UserStatusProfileUpdated
	err = users.Update(nil, users.User{
		ID:              userObj.UserID,
		Email:           data["email"].(string),
		PAN:             data["pan"].(string),
		DeviceID:        deviceID,
		Pincode:         data["permanentAddressPincode"].(string),
		Status:          &userStatus,
		Name:            data["name"].(string),
		DOB:             data["dob"].(string),
		Gender:          &gender,
		DynamicUserInfo: string(dynamicUserInfoStr),
	})
	if err != nil {
		logger.WithUser(userObj.UserID).Errorln(err)
		return err
	}

	if updateRedis {
		userRedisObj := authentication.UserStruct{
			UserID:          userObj.UserID,
			Email:           data["email"].(string),
			Status:          userStatus,
			SourceEntityID:  userObj.SourceEntityID,
			Mobile:          userObj.Mobile,
			UniqueID:        userObj.UniqueID,
			PAN:             data["pan"].(string),
			JourneyConfigID: userObj.JourneyConfigID,
		}

		err = usersutil.UpdateUserToken(userRedisObj, ctx, token)
		if err != nil {
			logger.WithUser(userObj.UserID).Errorln(err)
			return err
		}
	}

	dateTimeNowString := general.GetTimeStampString()
	go activity.ActivityLogger(userObj.UserID, userObj.SourceEntityID, userObj.UserID, constants.EntityTypeCustomer, constants.ActivityProfileUpdated, "", "", dateTimeNowString, false)

	dateTimeNowString = general.GetTimeStampString()
	segment, ok := data["segment"].(string)
	if !ok || segment == "" {
		if journeytype, ok2 := data["journeyType"].(string); ok2 && journeytype != "" {
			segment = journeytype
		} else {
			segment = "STPL"
		}
	}
	description := fmt.Sprintf("segment: %s", segment)
	go activity.ActivityLogger(userObj.UserID, userObj.SourceEntityID, userObj.UserID, constants.EntityTypeCustomer, constants.ActivityUserFormSubmitted, description, "", dateTimeNowString, false)

	return err
}

func PFLChangePANData(ctx context.Context, userObj authentication.UserStruct, data map[string]interface{}, updateRedis bool, token string) error {
	pan := data["pan"].(string)
	name := data["name"].(string)
	dob := data["dob"].(string)
	gender := constants.GenderStrToNum[data["gender"].(string)]
	if gender == -1 {
		// default to male for now
		gender = constants.GenderStrToNum["Male"]
	}

	err := users.Update(nil, users.User{
		ID:     userObj.UserID,
		PAN:    pan,
		Name:   name,
		DOB:    dob,
		Gender: &gender,
	})
	if err != nil {
		logger.WithUser(userObj.UserID).Errorln(err)
		return err
	}

	// activity
	err = pandetails.UpdateStatus(userObj.UserID, pandetails.StatusInactive)
	if err != nil {
		logger.WithUser(userObj.UserID).Error(err)
		return err
	}

	if updateRedis {
		var userRedisObj = authentication.UserStruct{
			UserID:          userObj.UserID,
			Email:           userObj.Email,
			Status:          constants.UserStatusProfileUpdated,
			SourceEntityID:  userObj.SourceEntityID,
			Mobile:          userObj.Mobile,
			UniqueID:        userObj.UniqueID,
			PAN:             pan,
			JourneyConfigID: userObj.JourneyConfigID,
		}

		err = usersutil.UpdateUserToken(userRedisObj, ctx, token)
		if err != nil {
			logger.WithUser(userObj.UserID).Errorln(err)
			return err
		}
	}

	dateTimeNowString := general.GetTimeStampString()
	go activity.ActivityLogger(userObj.UserID, userObj.SourceEntityID, userObj.UserID, constants.EntityTypeCustomer, constants.ActivityPANInfoUpdated, "", "", dateTimeNowString, false)

	return err
}

func PFLConfirmPANDetails(ctx context.Context, userObj authentication.UserStruct, data map[string]interface{}, updateRedis bool, token string) error {

	lat, _ := data["lat"].(string)
	lon, _ := data["lon"].(string)
	accuracy, _ := data["accuracy"].(string)
	height, _ := data["height"].(string)
	if lat != "" || lon != "" || accuracy != "" || height != "" {
		err := userlocation.Insert(userlocation.Struct{UserID: userObj.UserID, Lat: lat, Long: lon, Accuracy: accuracy, Height: height, Step: "PAN_INFO", LocationType: "current_location"})
		if err != nil {
			logger.WithUser(userObj.UserID).Error(err)
			return err
		}
	}

	if updateRedis {
		var userRedisObj = authentication.UserStruct{
			UserID:          userObj.UserID,
			Name:            userObj.Name,
			Email:           userObj.Email,
			Status:          userObj.Status,
			SourceEntityID:  userObj.SourceEntityID,
			Mobile:          userObj.Mobile,
			UniqueID:        userObj.UniqueID,
			PAN:             userObj.PAN,
			JourneyConfigID: userObj.JourneyConfigID,
		}

		err := usersutil.UpdateUserToken(userRedisObj, ctx, token)
		if err != nil {
			logger.WithUser(userObj.UserID).Errorln(err)
			return err
		}
	}

	dateTimeNowString := general.GetTimeStampString()
	go activity.ActivityLogger(userObj.UserID, userObj.SourceEntityID, userObj.UserID, constants.EntityTypeCustomer, constants.ActivityPANVerified, "", "", dateTimeNowString, false)

	return nil
}

// setKYCServiceWorkflowFlow is the helper function to set a user to kyc service
// NTODO: Include this in main PR
func setKYCServiceWorkflowFlow(primaryUserID string) (err error) {
	// NTODO: refactor this to directly assign kyc flow number 12
	err = featureflag.SetV2(featureflag.SetStruct{
		Flag:  journey.FlagUseKYCServiceWorkflow,
		Key:   primaryUserID,
		Value: true,
	}, nil)
	if err != nil {
		logger.WithUser(primaryUserID).Errorln(err)
		return err
	}
	return nil
}

func ABFLPersonalInfoSubmitProprietorship(ctx context.Context, userObj authentication.UserStruct, data map[string]interface{}, updateRedis bool, token string) error {

	dynamicUserInfoStr, err := json.Marshal(data)
	if err != nil {
		logger.WithUser(userObj.UserID).Errorln(err)
		return err
	}

	deviceID, _ := data["deviceID"].(string)

	userStatus := constants.UserStatusProfileUpdated
	err = users.Update(nil, users.User{
		ID:              userObj.UserID,
		Email:           data["email"].(string),
		PAN:             data["pan"].(string),
		DeviceID:        deviceID,
		Pincode:         data["permanentAddressPincode"].(string),
		Status:          &userStatus,
		DynamicUserInfo: string(dynamicUserInfoStr),
	})
	if err != nil {
		logger.WithUser(userObj.UserID).Errorln(err)
		return err
	}

	err = userjourney.UpdateMetadata(userObj.UserID, []general.JSONData{
		{
			Key:   userjourney.MetadataConstitution,
			Value: constants.Proprietorship,
		},
	})
	if err != nil {
		err = fmt.Errorf("error setting coApplicant flag in userjourney: %s", err.Error())
		logger.WithUser(userObj.UserID).Errorln(err)
		return err
	}

	rogbID, _ := data["roBGID"].(string)
	isAssisted, _ := data["isAssisted"].(bool)
	if isAssisted {

		err = abfl.UpdateRelationManagerDetails(userObj.UserID, abfl.RelationManagerDetails{
			ROBGID: rogbID,
			SalesManager1: abfl.SalesManagerDetails{
				BGID: data["salesManager1BGID"].(string),
				Name: data["salesManager1Name"].(string),
			},
			SalesManager2: abfl.SalesManagerDetails{
				BGID: data["salesManager2BGID"].(string),
				Name: data["salesManager2Name"].(string),
			},
			SourcingTeam:    data["sourcingTeam"].(string),
			SourcingChannel: data["sourcingChannel"].(string),
		})
		if err != nil {
			logger.WithUser(userObj.UserID).Errorln(err)
			return err
		}

	}

	if updateRedis {
		userRedisObj := authentication.UserStruct{
			UserID:          userObj.UserID,
			Email:           data["email"].(string),
			Status:          userStatus,
			SourceEntityID:  userObj.SourceEntityID,
			Mobile:          userObj.Mobile,
			UniqueID:        userObj.UniqueID,
			PAN:             data["pan"].(string),
			JourneyConfigID: userObj.JourneyConfigID,
		}

		err = usersutil.UpdateUserToken(userRedisObj, ctx, token)
		if err != nil {
			logger.WithUser(userObj.UserID).Errorln(err)
			return err
		}
	}

	dateTimeNowString := general.GetTimeStampString()
	go activity.ActivityLogger(userObj.UserID, userObj.SourceEntityID, userObj.UserID, constants.EntityTypeCustomer, constants.ActivityUserFormSubmitted, "", "", dateTimeNowString, false)

	dateTimeNowString = general.GetTimeStampString()
	go activity.ActivityLogger(userObj.UserID, userObj.SourceEntityID, userObj.UserID, constants.EntityTypeCustomer, constants.ActivityProfileUpdated, "", "", dateTimeNowString, false)

	return err
}

func ABFLPersonalInfoSubmitPreApproved(ctx context.Context, userObj authentication.UserStruct, data map[string]interface{}, updateRedis bool, token string) error {

	dynamicUserInfoStr, err := json.Marshal(data)
	if err != nil {
		logger.WithUser(userObj.UserID).Errorln(err)
		return err
	}

	deviceID, _ := data["deviceID"].(string)

	userStatus := constants.UserStatusProfileUpdated
	err = users.Update(nil, users.User{
		ID:       userObj.UserID,
		Email:    data["email"].(string),
		PAN:      data["pan"].(string),
		DeviceID: deviceID,
		Status:   &userStatus,

		DynamicUserInfo: string(dynamicUserInfoStr),
	})
	if err != nil {
		logger.WithUser(userObj.UserID).Errorln(err)
		return err
	}

	err = userjourney.UpdateMetadata(userObj.UserID, []general.JSONData{
		{
			Key:   userjourney.MetadataConstitution,
			Value: constants.Proprietorship,
		},
	})
	if err != nil {
		err = fmt.Errorf("error setting coApplicant flag in userjourney: %s", err.Error())
		logger.WithUser(userObj.UserID).Errorln(err)
		return err
	}

	rogbID, _ := data["roBGID"].(string)
	isAssisted, _ := data["isAssisted"].(bool)

	if isAssisted {

		err = abfl.UpdateRelationManagerDetails(userObj.UserID, abfl.RelationManagerDetails{
			ROBGID: rogbID,
			SalesManager1: abfl.SalesManagerDetails{
				BGID: data["salesManager1BGID"].(string),
				Name: data["salesManager1Name"].(string),
			},
			SalesManager2: abfl.SalesManagerDetails{
				BGID: data["salesManager2BGID"].(string),
				Name: data["salesManager2Name"].(string),
			},
			SourcingTeam:    data["sourcingTeam"].(string),
			SourcingChannel: data["sourcingChannel"].(string),
		})
		if err != nil {
			logger.WithUser(userObj.UserID).Errorln(err)
			return err
		}

	}

	if updateRedis {
		userRedisObj := authentication.UserStruct{
			UserID:          userObj.UserID,
			Email:           data["email"].(string),
			Status:          userStatus,
			SourceEntityID:  userObj.SourceEntityID,
			Mobile:          userObj.Mobile,
			UniqueID:        userObj.UniqueID,
			PAN:             data["pan"].(string),
			JourneyConfigID: userObj.JourneyConfigID,
		}

		err = usersutil.UpdateUserToken(userRedisObj, ctx, token)
		if err != nil {
			logger.WithUser(userObj.UserID).Errorln(err)
			return err
		}
	}

	dateTimeNowString := general.GetTimeStampString()
	go activity.ActivityLogger(userObj.UserID, userObj.SourceEntityID, userObj.UserID, constants.EntityTypeCustomer, constants.ActivityUserFormSubmitted, "", "", dateTimeNowString, false)

	dateTimeNowString = general.GetTimeStampString()
	go activity.ActivityLogger(userObj.UserID, userObj.SourceEntityID, userObj.UserID, constants.EntityTypeCustomer, constants.ActivityProfileUpdated, "", "", dateTimeNowString, false)

	return err
}

func ABFLPersonalInfoSubmitPvtLtd(ctx context.Context, userObj authentication.UserStruct, data map[string]interface{}, updateRedis bool, token string) error {

	dynamicUserInfoStr, err := json.Marshal(data)
	if err != nil {
		logger.WithUser(userObj.UserID).Errorln(err)
		return err
	}

	deviceID, _ := data["deviceID"].(string)

	userStatus := constants.UserStatusProfileUpdated
	err = users.Update(nil, users.User{
		ID:              userObj.UserID,
		PAN:             data["pan"].(string),
		Email:           data["email"].(string),
		DeviceID:        deviceID,
		Status:          &userStatus,
		DynamicUserInfo: string(dynamicUserInfoStr),
	})
	if err != nil {
		logger.WithUser(userObj.UserID).Errorln(err)
		return err
	}

	err = userjourney.UpdateMetadata(userObj.UserID, []general.JSONData{
		{
			Key:   userjourney.MetadataConstitution,
			Value: constants.Companies,
		},
	})
	if err != nil {
		err = fmt.Errorf("error setting coApplicant flag in userjourney: %s", err.Error())
		logger.WithUser(userObj.UserID).Errorln(err)
		return err
	}

	rogbID, _ := data["roBGID"].(string)
	isAssisted, _ := data["isAssisted"].(bool)
	if isAssisted {

		err = abfl.UpdateRelationManagerDetails(userObj.UserID, abfl.RelationManagerDetails{
			ROBGID: rogbID,
			SalesManager1: abfl.SalesManagerDetails{
				BGID: data["salesManager1BGID"].(string),
				Name: data["salesManager1Name"].(string),
			},
			SalesManager2: abfl.SalesManagerDetails{
				BGID: data["salesManager2BGID"].(string),
				Name: data["salesManager2Name"].(string),
			},
			SourcingTeam:    data["sourcingTeam"].(string),
			SourcingChannel: data["sourcingChannel"].(string),
		})
		if err != nil {
			logger.WithUser(userObj.UserID).Errorln(err)
			return err
		}

	}

	if updateRedis {
		userRedisObj := authentication.UserStruct{
			UserID:          userObj.UserID,
			Email:           data["email"].(string),
			Status:          userStatus,
			SourceEntityID:  userObj.SourceEntityID,
			Mobile:          userObj.Mobile,
			UniqueID:        userObj.UniqueID,
			PAN:             data["pan"].(string),
			JourneyConfigID: userObj.JourneyConfigID,
		}

		err = usersutil.UpdateUserToken(userRedisObj, ctx, token)
		if err != nil {
			logger.WithUser(userObj.UserID).Errorln(err)
			return err
		}
	}

	err = setKYCServiceWorkflowFlow(userObj.UserID)
	if err != nil {
		logger.WithUser(userObj.UserID).Errorln(err)
		return err
	}

	dateTimeNowString := general.GetTimeStampString()
	go activity.ActivityLogger(userObj.UserID, userObj.SourceEntityID, userObj.UserID, constants.EntityTypeCustomer, constants.ActivityUserFormSubmitted, "", "", dateTimeNowString, false)

	dateTimeNowString = general.GetTimeStampString()
	go activity.ActivityLogger(userObj.UserID, userObj.SourceEntityID, userObj.UserID, constants.EntityTypeCustomer, constants.ActivityProfileUpdated, "", "", dateTimeNowString, false)

	return err
}

func ABFLPersonalInfoSubmitPartnership(ctx context.Context, userObj authentication.UserStruct, data map[string]interface{}, updateRedis bool, token string) error {

	dynamicUserInfoStr, err := json.Marshal(data)
	if err != nil {
		logger.WithUser(userObj.UserID).Errorln(err)
		return err
	}

	deviceID, _ := data["deviceID"].(string)

	userStatus := constants.UserStatusProfileUpdated
	err = users.Update(nil, users.User{
		ID:              userObj.UserID,
		PAN:             data["pan"].(string),
		Email:           data["email"].(string),
		DeviceID:        deviceID,
		Status:          &userStatus,
		DynamicUserInfo: string(dynamicUserInfoStr),
	})
	if err != nil {
		logger.WithUser(userObj.UserID).Errorln(err)
		return err
	}

	err = userjourney.UpdateMetadata(userObj.UserID, []general.JSONData{
		{
			Key:   userjourney.MetadataConstitution,
			Value: constants.Partnership,
		},
	})
	if err != nil {
		err = fmt.Errorf("error setting constitution flag in userjourney: %s", err.Error())
		logger.WithUser(userObj.UserID).Errorln(err)
		return err
	}

	rogbID, _ := data["roBGID"].(string)
	if data["isAssisted"].(bool) {

		err = abfl.UpdateRelationManagerDetails(userObj.UserID, abfl.RelationManagerDetails{
			ROBGID: rogbID,
			SalesManager1: abfl.SalesManagerDetails{
				BGID: data["salesManager1BGID"].(string),
				Name: data["salesManager1Name"].(string),
			},
			SalesManager2: abfl.SalesManagerDetails{
				BGID: data["salesManager2BGID"].(string),
				Name: data["salesManager2Name"].(string),
			},
			SourcingTeam:    data["sourcingTeam"].(string),
			SourcingChannel: data["sourcingChannel"].(string),
		})
		if err != nil {
			logger.WithUser(userObj.UserID).Errorln(err)
			return err
		}

	}

	if updateRedis {
		userRedisObj := authentication.UserStruct{
			UserID:          userObj.UserID,
			Status:          userStatus,
			Email:           data["email"].(string),
			SourceEntityID:  userObj.SourceEntityID,
			Mobile:          userObj.Mobile,
			UniqueID:        userObj.UniqueID,
			PAN:             data["pan"].(string),
			JourneyConfigID: userObj.JourneyConfigID,
		}

		err = usersutil.UpdateUserToken(userRedisObj, ctx, token)
		if err != nil {
			logger.WithUser(userObj.UserID).Errorln(err)
			return err
		}
	}

	err = setKYCServiceWorkflowFlow(userObj.UserID)
	if err != nil {
		logger.WithUser(userObj.UserID).Errorln(err)
		return err
	}

	dateTimeNowString := general.GetTimeStampString()
	go activity.ActivityLogger(userObj.UserID, userObj.SourceEntityID, userObj.UserID, constants.EntityTypeCustomer, constants.ActivityUserFormSubmitted, "", "", dateTimeNowString, false)

	dateTimeNowString = general.GetTimeStampString()
	go activity.ActivityLogger(userObj.UserID, userObj.SourceEntityID, userObj.UserID, constants.EntityTypeCustomer, constants.ActivityProfileUpdated, "", "", dateTimeNowString, false)

	return err
}

func ABFLConfirmPANDetails(ctx context.Context, userObj authentication.UserStruct, data map[string]interface{}, updateRedis bool, token string) error {

	dateTimeNowString := general.GetTimeStampString()
	go activity.ActivityLogger(userObj.UserID, userObj.SourceEntityID, userObj.UserID, constants.EntityTypeCustomer, constants.ActivityPANVerified, "", "", dateTimeNowString, false)

	return nil
}

func ABFLBusinessAddressSubmit(ctx context.Context, userObj authentication.UserStruct, data map[string]interface{}, updateRedis bool, token string) error {

	addressMap := map[string]interface{}{
		"line1":         data["line1"].(string),
		"line2":         data["line2"].(string),
		"city":          data["city"].(string),
		"state":         data["state"].(string),
		"area":          data["area"].(string),
		"pincode":       data["pincode"].(string),
		"residenceType": data["residenceType"].(string),
	}

	addressBytes, err := json.Marshal(addressMap)
	if err != nil {
		logger.WithUser(userObj.UserID).Errorln(err)
		return err
	}
	addressType := constants.ResidenceStrToNum[data["residenceType"].(string)]

	err = userbusiness.InsertOrUpdateV2(nil, userbusiness.UpsertBusinessData{
		UserID:             userObj.UserID,
		UserBusinessEdited: sql.NullBool{Valid: true, Bool: true},
		AddressString:      sql.NullString{Valid: true, String: string(addressBytes)},
		AddressType:        sql.NullInt64{Valid: true, Int64: int64(addressType)},
	})
	if err != nil {
		logger.WithUser(userObj.UserID).Errorln(err)
		return err
	}
	dateTimeNowString := general.GetTimeStampString()
	go activity.ActivityLogger(userObj.UserID, userObj.SourceEntityID, userObj.UserID, constants.EntityTypeCustomer, constants.ActivityBusinessAddressUpdated, "", "", dateTimeNowString, false)

	return nil
}

func MoneyControlPersonalInfoSubmit(ctx context.Context, userObj authentication.UserStruct, data map[string]interface{}, updateRedis bool, token string) (err error) {
	dynamicUserInfoStr, err := json.Marshal(data)
	if err != nil {
		logger.WithUser(userObj.UserID).Errorln(err)
		return err
	}

	dynamicUserInfoStr = []byte(strings.ReplaceAll(string(dynamicUserInfoStr), `"ipAddr"`, `"ipAddress"`))

	deviceID, _ := data["deviceID"].(string)

	userStatus := constants.UserStatusProfileUpdated
	err = users.Update(nil, users.User{
		ID:              userObj.UserID,
		Email:           data["email"].(string),
		PAN:             data["pan"].(string),
		DeviceID:        deviceID,
		Status:          &userStatus,
		DynamicUserInfo: string(dynamicUserInfoStr),
	})
	if err != nil {
		logger.WithUser(userObj.UserID).Errorln(err)
		return err
	}

	if updateRedis {
		userRedisObj := authentication.UserStruct{
			UserID:          userObj.UserID,
			Email:           data["email"].(string),
			Status:          userStatus,
			SourceEntityID:  userObj.SourceEntityID,
			Mobile:          userObj.Mobile,
			UniqueID:        userObj.UniqueID,
			PAN:             data["pan"].(string),
			JourneyConfigID: userObj.JourneyConfigID,
		}

		err = usersutil.UpdateUserToken(userRedisObj, ctx, token)
		if err != nil {
			logger.WithUser(userObj.UserID).Errorln(err)
			return err
		}
	}

	dateTimeNowString := general.GetTimeStampString()
	go activity.ActivityLogger(userObj.UserID, userObj.SourceEntityID, userObj.UserID, constants.EntityTypeCustomer, constants.ActivityPANInfoUpdated, "", "", dateTimeNowString, false)

	dateTimeNowString = general.GetTimeStampString()
	go activity.ActivityLogger(userObj.UserID, userObj.SourceEntityID, userObj.UserID, constants.EntityTypeCustomer, constants.ActivityProfileUpdated, "", "", dateTimeNowString, false)

	return err
}

func ChangePANDataHandler(ctx context.Context, userObj authentication.UserStruct, data map[string]interface{}, updateRedis bool, token string) error {
	pan := data["pan"].(string)
	err := users.Update(nil, users.User{
		ID:  userObj.UserID,
		PAN: pan,
	})
	if err != nil {
		logger.WithUser(userObj.UserID).Errorln(err)
		return err
	}

	err = pandetails.UpdateStatus(userObj.UserID, pandetails.StatusInactive)
	if err != nil {
		logger.WithUser(userObj.UserID).Error(err)
		return err
	}

	if updateRedis {
		var userRedisObj = authentication.UserStruct{
			UserID:          userObj.UserID,
			Email:           userObj.Email,
			Status:          constants.UserStatusProfileUpdated,
			SourceEntityID:  userObj.SourceEntityID,
			Mobile:          userObj.Mobile,
			UniqueID:        userObj.UniqueID,
			PAN:             pan,
			JourneyConfigID: userObj.JourneyConfigID,
		}

		err = usersutil.UpdateUserToken(userRedisObj, ctx, token)
		if err != nil {
			logger.WithUser(userObj.UserID).Errorln(err)
			return err
		}
	}

	dateTimeNowString := general.GetTimeStampString()
	go activity.ActivityLogger(userObj.UserID, userObj.SourceEntityID, userObj.UserID, constants.EntityTypeCustomer, constants.ActivityPANInfoUpdated, "", "", dateTimeNowString, false)

	return err
}

func MoneyControlChangeAdditionalPANData(ctx context.Context, userObj authentication.UserStruct, data map[string]interface{}, updateRedis bool, token string) (err error) {
	pan := data["pan"].(string)
	name := data["name"].(string)
	dob := data["dob"].(string)
	gender := constants.GenderStrToNum[data["gender"].(string)]
	if gender == -1 {
		// default to male for now
		gender = constants.GenderStrToNum["Male"]
	}

	err = users.Update(nil, users.User{
		ID:     userObj.UserID,
		PAN:    pan,
		Name:   name,
		DOB:    dob,
		Gender: &gender,
	})
	if err != nil {
		logger.WithUser(userObj.UserID).Errorln(err)
		return err
	}

	err = pandetails.UpdateStatus(userObj.UserID, pandetails.StatusInactive)
	if err != nil {
		logger.WithUser(userObj.UserID).Error(err)
		return err
	}

	if updateRedis {
		var userRedisObj = authentication.UserStruct{
			UserID:          userObj.UserID,
			Email:           userObj.Email,
			Status:          constants.UserStatusProfileUpdated,
			SourceEntityID:  userObj.SourceEntityID,
			Mobile:          userObj.Mobile,
			UniqueID:        userObj.UniqueID,
			PAN:             pan,
			JourneyConfigID: userObj.JourneyConfigID,
		}

		err = usersutil.UpdateUserToken(userRedisObj, ctx, token)
		if err != nil {
			logger.WithUser(userObj.UserID).Errorln(err)
			return err
		}
	}

	dateTimeNowString := general.GetTimeStampString()
	go activity.ActivityLogger(userObj.UserID, userObj.SourceEntityID, userObj.UserID, constants.EntityTypeCustomer, constants.ActivityPANInfoUpdated, "", "", dateTimeNowString, false)

	return err
}

func MoneyControlSubmitAdditionalPANData(ctx context.Context, userObj authentication.UserStruct, data map[string]interface{}, updateRedis bool, token string) (err error) {
	name := data["name"].(string)
	dob := data["dob"].(string)
	gender := constants.GenderStrToNum[data["gender"].(string)]
	if gender == -1 {
		// default to male for now
		gender = constants.GenderStrToNum["Male"]
	}

	err = users.Update(nil, users.User{
		ID:     userObj.UserID,
		Name:   name,
		DOB:    dob,
		Gender: &gender,
	})
	if err != nil {
		logger.WithUser(userObj.UserID).Errorln(err)
		return err
	}

	dateTimeNowString := general.GetTimeStampString()
	go activity.ActivityLogger(userObj.UserID, userObj.SourceEntityID, userObj.UserID, constants.EntityTypeCustomer, constants.ActivityPANInfoUpdated, "", "", dateTimeNowString, false)

	return err
}

func MoneyControlConfirmPANDetails(ctx context.Context, userObj authentication.UserStruct, data map[string]interface{}, updateRedis bool, token string) (err error, customErr error) {
	pincode, _ := data["pincode"].(string)
	err = users.Update(nil, users.User{
		ID:      userObj.UserID,
		Pincode: pincode,
	})

	dynamicUserInfoMap := make(map[string]interface{})
	dynamicUserInfoJSONStr, _ := users.GetDynamicUserInfo(userObj.UserID)
	if err != nil {
		logger.WithUser(userObj.UserID).Errorln(err)
		return err, nil
	}

	err = json.Unmarshal([]byte(dynamicUserInfoJSONStr), &dynamicUserInfoMap)
	if err != nil {
		logger.WithUser(userObj.UserID).Errorln(err)
		return err, nil
	}

	city, state := pincodeapi.GetCityState(pincode)

	if city == "" || state == "" {
		return nil, fmt.Errorf("invalid pincode")
	}

	dynamicUserInfoMap["permanentAddressPincode"] = pincode
	dynamicUserInfoMap["permanentAddressCity"] = city
	dynamicUserInfoMap["permanentAddressState"] = state

	err = users.UpdateDynamicUserInfo(userObj.UserID, dynamicUserInfoMap)
	if err != nil {
		logger.WithUser(userObj.UserID).Error(err)
		return err, nil
	}

	dateTimeNowString := general.GetTimeStampString()
	go activity.ActivityLogger(userObj.UserID, userObj.SourceEntityID, userObj.UserID, constants.EntityTypeCustomer, constants.ActivityPANVerified, "", "", dateTimeNowString, false)

	return err, nil
}

func MoneyControlProfessionalInfoSubmit(ctx context.Context, userObj authentication.UserStruct, data map[string]interface{}, updateRedis bool, token string) (err error, customErr error) {

	// adding already present dynamic info
	dynamicUserInfoMap := make(map[string]interface{})
	dynamicUserInfoJSONStr, _ := users.GetDynamicUserInfo(userObj.UserID)

	err = json.Unmarshal([]byte(dynamicUserInfoJSONStr), &dynamicUserInfoMap)
	if err != nil {
		logger.WithUser(userObj.UserID).Errorln(err)
		return err, nil
	}
	lat, _ := data["lat"].(string)
	lon, _ := data["lon"].(string)
	accuracy, _ := data["accuracy"].(string)
	height, _ := data["height"].(string)
	if lat != "" || lon != "" || accuracy != "" || height != "" {
		err := userlocation.Insert(userlocation.Struct{UserID: userObj.UserID, Lat: lat, Long: lon, Accuracy: accuracy, Height: height, Step: "PROFESSIONAL_INFO", LocationType: "current_location"})
		if err != nil {
			logger.WithUser(userObj.UserID).Error(err)
		}
	}

	salariedCompanyName, _ := data["salariedCompanyName"].(string)

	if strings.ToLower(salariedCompanyName) == "other" {
		data["companyCategory"] = constants.LandTOthersCompanyCategory
	} else if salariedCompanyName != "" {
		category, err := companydetails.GetCategory(salariedCompanyName, userObj.SourceEntityID)
		if err != nil {
			logger.WithUser(userObj.UserID).Errorln(fmt.Errorf("company category not found, companyCategory: %s, err: %s", salariedCompanyName, err))
			return nil, fmt.Errorf("company category not found")
		}
		data["companyCategory"] = strings.TrimSpace(category)
	}

	companyName, ok := data["selfEmployedCompanyName"].(string)
	if !ok {
		companyName, ok = data["salariedCompanyName"].(string)
		if !ok {
			logger.WithUser(userObj.UserID).Errorln("invalid companyName")
			return nil, fmt.Errorf("invalid companyName")
		}
	}

	data["companyName"] = companyName
	delete(data, "selfEmployedCompanyName")
	delete(data, "salariedCompanyName")

	// Check if monthlyIncomeRange exists in the data map
	monthlyIncomeRange, ok := data["monthlyIncomeRange"].(string)
	if !ok {
		logger.WithUser(userObj.UserID).Errorln("monthlyIncomeRange is missing in the data map")
	}
	data["monthlyIncome"] = constants.MonthlyIncomeRangeMap[monthlyIncomeRange]

	// Check if loanAmountRange exists in the data map
	loanAmountRange, ok := data["loanAmountRange"].(string)
	if !ok {
		logger.WithUser(userObj.UserID).Errorln("loanAmountRange is missing in the data map")
	}
	data["loanAmount"] = constants.LoanAmountRangeMap[loanAmountRange]

	for k, v := range data {
		dynamicUserInfoMap[k] = v
	}

	err = users.UpdateDynamicUserInfo(userObj.UserID, dynamicUserInfoMap)
	if err != nil {
		logger.WithUser(userObj.UserID).Error(err)
		return err, nil
	}

	dateTimeNowString := general.GetTimeStampString()
	go activity.ActivityLogger(userObj.UserID, userObj.SourceEntityID, userObj.UserID, constants.EntityTypeCustomer, constants.ActivityUserFormSubmitted, "", "", dateTimeNowString, false)

	dateTimeNowString = general.GetTimeStampString()
	go activity.ActivityLogger(userObj.UserID, userObj.SourceEntityID, userObj.UserID, constants.EntityTypeCustomer, constants.ActivityProfileUpdated, "", "", dateTimeNowString, false)

	return err, nil
}

func ABCDProfessionalInfoSubmit(ctx context.Context, userObj authentication.UserStruct, data map[string]interface{}, updateRedis bool, token string) (err error, customErr error) {
	// adding already present dynamic info
	dynamicUserInfoMap := make(map[string]interface{})
	dynamicUserInfoJSONStr, _ := users.GetDynamicUserInfo(userObj.UserID)

	err = json.Unmarshal([]byte(dynamicUserInfoJSONStr), &dynamicUserInfoMap)
	if err != nil {
		logger.WithUser(userObj.UserID).Errorln(err)
		return err, nil
	}
	lat, _ := data["lat"].(string)
	lon, _ := data["lon"].(string)
	accuracy, _ := data["accuracy"].(string)
	height, _ := data["height"].(string)
	if lat != "" || lon != "" || accuracy != "" || height != "" {
		err := userlocation.Insert(userlocation.Struct{UserID: userObj.UserID, Lat: lat, Long: lon, Accuracy: accuracy, Height: height, Step: "PROFESSIONAL_INFO", LocationType: "current_location"})
		if err != nil {
			logger.WithUser(userObj.UserID).Error(err)
		}
	}

	salariedCompanyName, _ := data["salariedCompanyName"].(string)

	if strings.ToLower(salariedCompanyName) == "other" {
		data["companyCategory"] = constants.LandTOthersCompanyCategory
	} else if salariedCompanyName != "" {
		category, err := companydetails.GetCategory(salariedCompanyName, userObj.SourceEntityID)
		if err != nil {
			logger.WithUser(userObj.UserID).Errorln(fmt.Errorf("company category not found, companyCategory: %s, err: %s", salariedCompanyName, err))
			return nil, fmt.Errorf("company category not found")
		}
		data["companyCategory"] = strings.TrimSpace(category)
	}

	companyName, ok := data["selfEmployedCompanyName"].(string)
	if !ok {
		companyName, ok = data["salariedCompanyName"].(string)
		if !ok {
			logger.WithUser(userObj.UserID).Errorln("invalid companyName")
			return nil, fmt.Errorf("invalid companyName")
		}
	}

	data["companyName"] = companyName
	delete(data, "selfEmployedCompanyName")
	delete(data, "salariedCompanyName")

	for k, v := range data {
		dynamicUserInfoMap[k] = v
	}

	err = users.UpdateDynamicUserInfo(userObj.UserID, dynamicUserInfoMap)
	if err != nil {
		logger.WithUser(userObj.UserID).Error(err)
		return err, nil
	}

	dateTimeNowString := general.GetTimeStampString()
	go activity.ActivityLogger(userObj.UserID, userObj.SourceEntityID, userObj.UserID, constants.EntityTypeCustomer, constants.ActivityUserFormSubmitted, "", "", dateTimeNowString, false)

	dateTimeNowString = general.GetTimeStampString()
	go activity.ActivityLogger(userObj.UserID, userObj.SourceEntityID, userObj.UserID, constants.EntityTypeCustomer, constants.ActivityProfileUpdated, "", "", dateTimeNowString, false)

	return err, nil
}

func ABFLPLPersonalInfoSubmit(ctx context.Context, userObj authentication.UserStruct, data map[string]interface{}, updateRedis bool, token string) (err error) {

	dynamicUserInfoStr, err := json.Marshal(data)
	if err != nil {
		logger.WithUser(userObj.UserID).Errorln(err)
		return err
	}
	deviceID, _ := data["deviceID"].(string)

	gender := constants.GenderStrToNum[data["gender"].(string)]
	if gender == -1 {
		// default to male for now
		gender = constants.GenderStrToNum["Male"]
	}

	userStatus := constants.UserStatusProfileUpdated
	err = users.Update(nil, users.User{
		ID:              userObj.UserID,
		Email:           data["email"].(string),
		PAN:             data["pan"].(string),
		DeviceID:        deviceID,
		Pincode:         data["permanentAddressPincode"].(string),
		Status:          &userStatus,
		Name:            data["name"].(string),
		DOB:             data["dob"].(string),
		Gender:          &gender,
		DynamicUserInfo: string(dynamicUserInfoStr),
	})
	if err != nil {
		logger.WithUser(userObj.UserID).Errorln(err)
		return err
	}

	if updateRedis {
		userRedisObj := authentication.UserStruct{
			UserID:          userObj.UserID,
			Status:          userStatus,
			Email:           data["email"].(string),
			SourceEntityID:  userObj.SourceEntityID,
			Mobile:          userObj.Mobile,
			UniqueID:        userObj.UniqueID,
			PAN:             data["pan"].(string),
			JourneyConfigID: userObj.JourneyConfigID,
		}

		err = usersutil.UpdateUserToken(userRedisObj, ctx, token)
		if err != nil {
			logger.WithUser(userObj.UserID).Errorln(err)
			return err
		}
	}

	dateTimeNowString := general.GetTimeStampString()
	go activity.ActivityLogger(userObj.UserID, userObj.SourceEntityID, userObj.UserID, constants.EntityTypeCustomer, constants.ActivityProfileUpdated, "", "", dateTimeNowString, false)

	dateTimeNowString = general.GetTimeStampString()
	go activity.ActivityLogger(userObj.UserID, userObj.SourceEntityID, userObj.UserID, constants.EntityTypeCustomer, constants.ActivityUserFormSubmitted, "", "", dateTimeNowString, false)

	return err
}

func ABFLPLChangePanData(ctx context.Context, userObj authentication.UserStruct, data map[string]interface{}, updateRedis bool, token string) (err error) {
	pan := data["pan"].(string)
	name := data["name"].(string)
	dob := data["dob"].(string)
	gender := constants.GenderStrToNum[data["gender"].(string)]
	if gender == -1 {
		// default to male for now
		gender = constants.GenderStrToNum["Male"]
	}

	err = users.Update(nil, users.User{
		ID:     userObj.UserID,
		PAN:    pan,
		Name:   name,
		DOB:    dob,
		Gender: &gender,
	})
	if err != nil {
		logger.WithUser(userObj.UserID).Errorln(err)
		return err
	}

	err = pandetails.UpdateStatus(userObj.UserID, pandetails.StatusInactive)
	if err != nil {
		logger.WithUser(userObj.UserID).Error(err)
		return err
	}

	if updateRedis {
		var userRedisObj = authentication.UserStruct{
			UserID:          userObj.UserID,
			Email:           userObj.Email,
			Status:          constants.UserStatusProfileUpdated,
			SourceEntityID:  userObj.SourceEntityID,
			Mobile:          userObj.Mobile,
			UniqueID:        userObj.UniqueID,
			PAN:             pan,
			JourneyConfigID: userObj.JourneyConfigID,
		}

		err = usersutil.UpdateUserToken(userRedisObj, ctx, token)
		if err != nil {
			logger.WithUser(userObj.UserID).Errorln(err)
			return err
		}
	}

	dateTimeNowString := general.GetTimeStampString()
	go activity.ActivityLogger(userObj.UserID, userObj.SourceEntityID, userObj.UserID, constants.EntityTypeCustomer, constants.ActivityPANInfoUpdated, "", "", dateTimeNowString, false)

	return err
}

func ABFLPLProfessionalInfoSubmit(ctx context.Context, userObj authentication.UserStruct, data map[string]interface{}, updateRedis bool, token string) (err error, customErr error) {

	// adding already present dynamic info
	dynamicUserInfoMap := make(map[string]interface{})
	dynamicUserInfoJSONStr, _ := users.GetDynamicUserInfo(userObj.UserID)

	err = json.Unmarshal([]byte(dynamicUserInfoJSONStr), &dynamicUserInfoMap)
	if err != nil {
		logger.WithUser(userObj.UserID).Errorln(err)
		return err, nil
	}

	salariedCompanyName, _ := data["salariedCompanyName"].(string)
	selfEmployedCompanyName, _ := data["selfEmployedCompanyName"].(string)
	employmentType, _ := data["employmentType"].(string)
	companyCategory := "NC"

	if strings.ToLower(employmentType) == constants.OccupationTypeSalaried && strings.ToLower(salariedCompanyName) != "other" {
		companyCategory, err = companydetails.GetCategory(salariedCompanyName, constants.ABFLPLID)
		if err != nil {
			logger.WithUser(userObj.UserID).Errorln(fmt.Errorf("company category not found, companyCategory: %s, err: %s", salariedCompanyName, err))
			return nil, fmt.Errorf("company category not found")
		}
	}

	data["companyCategory"] = strings.TrimSpace(companyCategory)

	companyName := general.Coalesce(salariedCompanyName, selfEmployedCompanyName)
	if companyName == "" {
		logger.WithUser(userObj.UserID).Errorln(fmt.Errorf("invalid companyName"))
		return nil, fmt.Errorf("invalid companyName")
	}

	data["companyName"] = companyName
	delete(data, "selfEmployedCompanyName")
	delete(data, "salariedCompanyName")

	for k, v := range data {
		dynamicUserInfoMap[k] = v
	}

	err = users.UpdateDynamicUserInfo(userObj.UserID, dynamicUserInfoMap)
	if err != nil {
		logger.WithUser(userObj.UserID).Error(err)
		return err, nil
	}

	dateTimeNowString := general.GetTimeStampString()
	go activity.ActivityLogger(userObj.UserID, userObj.SourceEntityID, userObj.UserID, constants.EntityTypeCustomer, constants.ActivityProfessionalInfoUpdated, "", "", dateTimeNowString, false)

	return err, nil
}

func IIFLPersonalInfoSubmit(ctx context.Context, userObj authentication.UserStruct, data map[string]interface{}, updateRedis bool, token string) error {

	dynamicUserInfoStr, err := json.Marshal(data)
	if err != nil {
		logger.WithUser(userObj.UserID).Errorln(err)
		return err
	}
	dynamicUserInfoStr = []byte(dynamicUserInfoStr)
	gender := constants.GenderStrToNum[data["gender"].(string)]
	if gender == -1 {
		err = errors.New("invalid gender")
		logger.WithUser(userObj.UserID).Errorln(err)
		return err
	}
	deviceID, _ := data["deviceID"].(string)

	userStatus := constants.UserStatusProfileUpdated
	err = users.Update(nil, users.User{
		ID:              userObj.UserID,
		Email:           data["email"].(string),
		PAN:             data["pan"].(string),
		DeviceID:        deviceID,
		Pincode:         data["pincode"].(string),
		Status:          &userStatus,
		DOB:             data["dob"].(string),
		Gender:          &gender,
		DynamicUserInfo: string(dynamicUserInfoStr),
	})
	if err != nil {
		logger.WithUser(userObj.UserID).Errorln(err)
		return err
	}

	if updateRedis {
		userRedisObj := authentication.UserStruct{
			UserID:          userObj.UserID,
			Email:           data["email"].(string),
			Status:          userStatus,
			SourceEntityID:  userObj.SourceEntityID,
			Mobile:          userObj.Mobile,
			UniqueID:        userObj.UniqueID,
			PAN:             data["pan"].(string),
			JourneyConfigID: userObj.JourneyConfigID,
		}

		err = usersutil.UpdateUserToken(userRedisObj, ctx, token)
		if err != nil {
			logger.WithUser(userObj.UserID).Errorln(err)
			return err
		}
	}

	dateTimeNowString := general.GetTimeStampString()
	go activity.ActivityLogger(userObj.UserID, userObj.SourceEntityID, userObj.UserID, constants.EntityTypeCustomer, constants.ActivityPANInfoUpdated, "", "", dateTimeNowString, false)

	dateTimeNowString = general.GetTimeStampString()
	go activity.ActivityLogger(userObj.UserID, userObj.SourceEntityID, userObj.UserID, constants.EntityTypeCustomer, constants.ActivityProfileUpdated, "", "", dateTimeNowString, false)

	return err
}

func IIFLChangePANData(ctx context.Context, userObj authentication.UserStruct, data map[string]interface{}, updateRedis bool, token string) error {
	pan := data["pan"].(string)

	err := users.Update(nil, users.User{
		ID:  userObj.UserID,
		PAN: pan,
	})
	if err != nil {
		logger.WithUser(userObj.UserID).Errorln(err)
		return err
	}

	err = pandetails.UpdateStatus(userObj.UserID, pandetails.StatusInactive)
	if err != nil {
		logger.WithUser(userObj.UserID).Error(err)
		return err
	}

	if updateRedis {
		var userRedisObj = authentication.UserStruct{
			UserID:          userObj.UserID,
			Email:           userObj.Email,
			Status:          constants.UserStatusProfileUpdated,
			SourceEntityID:  userObj.SourceEntityID,
			Mobile:          userObj.Mobile,
			UniqueID:        userObj.UniqueID,
			PAN:             pan,
			JourneyConfigID: userObj.JourneyConfigID,
		}

		err = usersutil.UpdateUserToken(userRedisObj, ctx, token)
		if err != nil {
			logger.WithUser(userObj.UserID).Errorln(err)
			return err
		}
	}

	dateTimeNowString := general.GetTimeStampString()
	go activity.ActivityLogger(userObj.UserID, userObj.SourceEntityID, userObj.UserID, constants.EntityTypeCustomer, constants.ActivityPANInfoUpdated, "", "", dateTimeNowString, false)

	return err
}

func IIFLConfirmPANDetails(ctx context.Context, userObj authentication.UserStruct, data map[string]interface{}, updateRedis bool, token string) error {

	lat, _ := data["lat"].(string)
	lon, _ := data["lon"].(string)
	accuracy, _ := data["accuracy"].(string)
	height, _ := data["height"].(string)

	if lat != "" || lon != "" || accuracy != "" || height != "" {
		err := userlocation.Insert(userlocation.Struct{UserID: userObj.UserID, Lat: lat, Long: lon, Accuracy: accuracy, Height: height, Step: "PERSONAL_INFO", LocationType: "current_location"})
		if err != nil {
			logger.WithUser(userObj.UserID).Error(err)
			return err
		}
	}

	if updateRedis {
		var userRedisObj = authentication.UserStruct{
			UserID:          userObj.UserID,
			Name:            userObj.Name,
			Email:           userObj.Email,
			Status:          userObj.Status,
			SourceEntityID:  userObj.SourceEntityID,
			Mobile:          userObj.Mobile,
			UniqueID:        userObj.UniqueID,
			PAN:             userObj.PAN,
			JourneyConfigID: userObj.JourneyConfigID,
		}

		err := usersutil.UpdateUserToken(userRedisObj, ctx, token)
		if err != nil {
			logger.WithUser(userObj.UserID).Errorln(err)
			return err
		}
	}

	dateTimeNowString := general.GetTimeStampString()
	go activity.ActivityLogger(userObj.UserID, userObj.SourceEntityID, userObj.UserID, constants.EntityTypeCustomer, constants.ActivityPANVerified, "", "", dateTimeNowString, false)

	return nil
}

func IIFLUpdateBusinessInfo(ctx context.Context, userObj authentication.UserStruct, data map[string]interface{}, updateRedis bool, token string) error {

	if !general.InArr(data["constitution"].(string), constants.ConstitutionList) {
		return errors.New("invalid constitution")
	}

	if !general.InArr(data["last12MonthsSale"].(string), constants.IncomeRangeList) {
		return errors.New("invalid date of incorporation")
	}

	businessPan, _ := data["businessPan"].(string)
	// In case of these constitutions, we get verify the firm name from pan verify api.
	if general.InArr(data["constitution"].(string), []string{constants.PrivateLimited, constants.PublicLimited, constants.LimitedLiability, constants.Partnership}) {

		if businessPan == "" {
			return errors.New("invalid business pan")
		}

		panName, _, errString, err := pannumber.Verify(businessPan, userObj.UserID, userObj.Name, true)

		if errString != "" || err != nil {
			if errString != "" {
				errString = "something went wrong"
			}
			logger.WithUser(userObj.UserID).Debug("Failed to fetch pan from Name: ", errString, err)
			return errors.New(errString)
		}

		data["firmName"] = panName

	}

	industry := data["industry"].(string)
	subIndustry := data["subIndustry"].(string)

	type IndustrySubindustryValues struct {
		Industry           string `json:"Industry"`
		SubIndustry        string `json:"SubIndustry"`
		Classification     string `json:"Classification"`
		RiskCategorization string `json:"RiskCategorization"`
	}
	var industrySubindustryValues IndustrySubindustryValues
	value, err := lenderdropdown.GetValueFromIndustryAndSubIndustry("iifl_bl_business_details", "industry_sub_industry", constants.IIFLID, industry, subIndustry)
	logger.WithUser(userObj.UserID).Debug("Industry Sub Industry drop down value", value)

	if err != nil {

		logger.WithUser(userObj.UserID).Errorln("error in getting value from industry and subindustry: ", err)
		return err
	}

	err = json.Unmarshal([]byte(value), &industrySubindustryValues)
	if err != nil {
		logger.WithUser(userObj.UserID).Errorln("error in unmarshaling industry and subindustry: ", err)
		return err
	}

	userBusinessData := userbusiness.BusinessData{
		UserID:              userObj.UserID,
		Name:                data["firmName"].(string),
		Constitution:        data["constitution"].(string),
		Last12MonthSale:     data["last12MonthsSale"].(string),
		DateOfIncorporation: data["dateOfIncorporation"].(string),
		IndustryType:        industrySubindustryValues.Industry,
		SubIndustryType:     industrySubindustryValues.SubIndustry,
		BusinessPAN:         businessPan,
	}

	metadata := map[string]interface{}{
		"risk_classification": industrySubindustryValues.Classification,
		"risk_score":          industrySubindustryValues.RiskCategorization,
	}

	// Convert the map to a JSON string
	metadataJSON, err := json.Marshal(metadata)
	if err != nil {
		logger.WithUser(userObj.UserID).Errorln("error in marshaling metadata: ", err)
		return err
	}
	userBusinessData.Metadata = sql.NullString{Valid: true, String: string(metadataJSON)}

	err = userbusiness.InsertOrUpdate(nil, userBusinessData)
	if err != nil {
		logger.WithUser(userObj.UserID).Errorln("error in insert or update user business data: ", err)
		return err

	}

	err = users.Update(nil, users.User{
		FirmName: data["firmName"].(string),
		ID:       userObj.UserID,
	})
	if err != nil {
		logger.WithUser(userObj.UserID).Errorln("error in updating user", err)
		return err
	}

	dateTimeNowString := general.GetTimeStampString()
	go func() {
		activityObj := activity.ActivityEvent{
			UserID:            userObj.UserID,
			SourceEntityID:    userObj.SourceEntityID,
			LoanApplicationID: "",
			EntityType:        constants.EntityTypeCustomer,
			EntityRef:         userObj.UserID,
			EventType:         constants.ActivityBusinessInfoUpdated,
			Description:       "",
		}
		activity.RegisterEvent(&activityObj, dateTimeNowString)
	}()

	return nil
}

func GenericSubmit(ctx context.Context, userObj authentication.UserStruct, data map[string]interface{}, updateRedis bool, token string, actions []apischemamapper.Action, activityLogs []activity.ActivityEvent) error {

	dynMap, err := NewDynamicDataMap(data)
	if err != nil {
		logger.WithContext(ctx).Errorln(err)
		return err
	}
	dynMap.ExecuteTriggers(userObj.UserID, userObj.LoanApplicationID, actions)

	dynamicDataObject := NewDynamicDataObject(userObj.UserID, userObj.SourceEntityID, *dynMap)
	err = dynamicDataObject.Populate()
	if err != nil {
		logger.WithContext(ctx).Errorln(err)
		return err
	}

	err = SaveUserDetails(ctx, userObj.UserID, dynamicDataObject, dynMap)
	if err != nil {
		logger.WithContext(ctx).Errorln(err)
		return err
	}

	if updateRedis {
		var userRedisObj = authentication.UserStruct{
			UserID:          userObj.UserID,
			Name:            userObj.Name,
			Email:           userObj.Email,
			Status:          userObj.Status,
			SourceEntityID:  userObj.SourceEntityID,
			Mobile:          userObj.Mobile,
			UniqueID:        userObj.UniqueID,
			PAN:             userObj.PAN,
			JourneyConfigID: userObj.JourneyConfigID,
		}
		if dynamicDataObject.Name.Valid {
			userRedisObj.Name = dynamicDataObject.Name.String
		}
		if dynamicDataObject.Email.Valid {
			userRedisObj.Email = dynamicDataObject.Email.String
		}
		if dynamicDataObject.PersonalPAN.Valid {
			userRedisObj.PAN = dynamicDataObject.PersonalPAN.String
		}
		err := usersutil.UpdateUserToken(userRedisObj, ctx, token)
		if err != nil {
			logger.WithUser(userObj.UserID).Errorln(err)
			return err
		}
	}

	for _, activityObj := range activityLogs {
		activity.ActivityLogger(userObj.UserID,
			userObj.SourceEntityID,
			activityObj.EntityRef,
			activityObj.EntityType,
			activityObj.EventType,
			activityObj.Description,
			"",
			general.GetTimeStampString(),
			false,
		)
	}

	return nil
}

func MFLBLPersonalInfoSubmit(ctx context.Context, userObj authentication.UserStruct, data map[string]interface{}, updateRedis bool, token string) (err error, customErr error) {

	dynamicUserInfoMap, err := users.GetDynamicUserInfoMap(userObj.UserID)
	if err != nil {
		logger.WithUser(userObj.UserID).Errorln(err)
	}
	// merge existing dynamic info with new data
	for k, v := range data {
		dynamicUserInfoMap[k] = v
	}
	dynamicUserInfoStr, err := json.Marshal(dynamicUserInfoMap)
	if err != nil {
		logger.WithUser(userObj.UserID).Errorln(err)
		return err, nil
	}
	dynamicUserInfoStr = []byte(dynamicUserInfoStr)
	gender := constants.GenderStrToNum[data["gender"].(string)]
	if gender == -1 {
		err = errors.New("invalid gender")
		logger.WithUser(userObj.UserID).Errorln(err)
		return nil, err
	}
	deviceID, _ := data["deviceID"].(string)

	userStatus := constants.UserStatusProfileUpdated
	err = users.Update(nil, users.User{
		ID:              userObj.UserID,
		Email:           data["email"].(string),
		PAN:             data["pan"].(string),
		DeviceID:        deviceID,
		Pincode:         data["pincode"].(string),
		Status:          &userStatus,
		DOB:             data["dob"].(string),
		Gender:          &gender,
		Name:            data["name"].(string),
		DynamicUserInfo: string(dynamicUserInfoStr),
	})
	if err != nil {
		logger.WithUser(userObj.UserID).Errorln(err)
		return err, nil
	}

	if updateRedis {
		userRedisObj := authentication.UserStruct{
			UserID:          userObj.UserID,
			Email:           data["email"].(string),
			Status:          userStatus,
			SourceEntityID:  userObj.SourceEntityID,
			Mobile:          userObj.Mobile,
			UniqueID:        userObj.UniqueID,
			PAN:             data["pan"].(string),
			JourneyConfigID: userObj.JourneyConfigID,
		}

		err = usersutil.UpdateUserToken(userRedisObj, ctx, token)
		if err != nil {
			logger.WithUser(userObj.UserID).Errorln(err)
			return err, nil
		}
	}

	dateTimeNowString := general.GetTimeStampString()
	go activity.ActivityLogger(userObj.UserID, userObj.SourceEntityID, userObj.UserID, constants.EntityTypeCustomer, constants.ActivityProfileUpdated, "", "", dateTimeNowString, false)

	return err, nil
}

func MFLBLSaveInitialPanInfo(ctx context.Context, userObj authentication.UserStruct, data map[string]interface{}, updateRedis bool, token string) (err error, customErr error) {

	pan, _ := data["pan"].(string)

	err = users.Update(nil, users.User{
		ID:  userObj.UserID,
		PAN: pan,
	})
	if err != nil {
		logger.WithUser(userObj.UserID).Errorln(err)
		return err, nil
	}

	userStatus := constants.UserStatusTokenIssued

	if updateRedis {
		userRedisObj := authentication.UserStruct{
			UserID:          userObj.UserID,
			UniqueID:        userObj.UniqueID,
			PAN:             pan,
			SourceEntityID:  userObj.SourceEntityID,
			JourneyConfigID: userObj.JourneyConfigID,
			Status:          userStatus,
		}

		err = usersutil.UpdateUserToken(userRedisObj, ctx, token)
		if err != nil {
			logger.WithUser(userObj.UserID).Errorln(err)
			return err, nil
		}
	}

	dateTimeNowString := general.GetTimeStampString()
	go activity.ActivityLogger(userObj.UserID, userObj.SourceEntityID, userObj.UserID, constants.EntityTypeCustomer, constants.ActivityPanSubmitted, "", "", dateTimeNowString, false)

	return err, nil
}

func MFLBLSavePersonalInfoNTM(ctx context.Context, userObj authentication.UserStruct, data map[string]interface{}, updateRedis bool, token string) (err error, customErr error) {

	dynamicUserInfoMap, err := users.GetDynamicUserInfoMap(userObj.UserID)
	if err != nil {
		logger.WithUser(userObj.UserID).Errorln(err)
	}
	// merge existing dynamic info with new data
	for k, v := range data {
		dynamicUserInfoMap[k] = v
	}
	dynamicUserInfoStr, err := json.Marshal(dynamicUserInfoMap)
	if err != nil {
		logger.WithUser(userObj.UserID).Errorln(err)
		return err, nil
	}
	deviceID, _ := data["deviceID"].(string)

	userStatus := constants.UserStatusProfileUpdated
	err = users.Update(nil, users.User{
		ID:              userObj.UserID,
		Email:           data["email"].(string),
		PAN:             data["pan"].(string),
		DeviceID:        deviceID,
		Pincode:         data["pincode"].(string),
		Status:          &userStatus,
		DynamicUserInfo: string(dynamicUserInfoStr),
	})
	if err != nil {
		logger.WithUser(userObj.UserID).Errorln(err)
		return err, nil
	}

	if updateRedis {
		userRedisObj := authentication.UserStruct{
			UserID:          userObj.UserID,
			Email:           data["email"].(string),
			Status:          userStatus,
			SourceEntityID:  userObj.SourceEntityID,
			Mobile:          userObj.Mobile,
			UniqueID:        userObj.UniqueID,
			PAN:             data["pan"].(string),
			JourneyConfigID: userObj.JourneyConfigID,
		}

		err = usersutil.UpdateUserToken(userRedisObj, ctx, token)
		if err != nil {
			logger.WithUser(userObj.UserID).Errorln(err)
			return err, nil
		}
	}

	dateTimeNowString := general.GetTimeStampString()
	go activity.ActivityLogger(userObj.UserID, userObj.SourceEntityID, userObj.UserID, constants.EntityTypeCustomer, constants.ActivityProfileUpdated, "", "", dateTimeNowString, false)

	return err, nil
}

func MFLBLSaveUdyamNumber(ctx context.Context, userObj authentication.UserStruct, data map[string]interface{}, updateRedis bool, token string) (err error, customErr error) {

	uan, ok := data["uan"].(string)
	if !ok {
		logger.Log.Errorln("error converting uan to string: ", err)
		return err, nil
	}

	err = userbusinessuan.InsertUAN(nil, userObj.UserID, uan)
	if err != nil {
		logger.Log.Errorln("error inserting uan value for user: ", err)
		return err, nil
	}
	if updateRedis {
		userRedisObj := authentication.UserStruct{
			UserID:          userObj.UserID,
			Email:           userObj.Email,
			Status:          constants.UserStatusProfileUpdated,
			SourceEntityID:  userObj.SourceEntityID,
			Mobile:          userObj.Mobile,
			UniqueID:        userObj.UniqueID,
			PAN:             userObj.PAN,
			JourneyConfigID: userObj.JourneyConfigID,
		}

		err = usersutil.UpdateUserToken(userRedisObj, ctx, token)
		if err != nil {
			logger.WithUser(userObj.UserID).Errorln(err)
			return err, nil
		}
	}
	dateTimeNowString := general.GetTimeStampString()
	go activity.ActivityLogger(userObj.UserID, userObj.SourceEntityID, userObj.UserID, constants.EntityTypeCustomer, constants.ActivityUANAdded, "", "", dateTimeNowString, false)

	return err, nil
}

func MFLBLSaveProfessionalInfo(ctx context.Context, userObj authentication.UserStruct, data map[string]interface{}, updateRedis bool, token string) (err error) {
	dynamicUserInfoMap, err := users.GetDynamicUserInfoMap(userObj.UserID)
	if err != nil {
		logger.WithUser(userObj.UserID).Errorln(err)
		panic(err)
	}

	for key, value := range data {
		dynamicUserInfoMap[key] = value
	}

	dynamicUserInfoStr, err := json.Marshal(dynamicUserInfoMap)
	if err != nil {
		logger.WithUser(userObj.UserID).Errorln(err)
		return err
	}

	if err := users.Update(nil, users.User{
		ID:              userObj.UserID,
		DynamicUserInfo: string(dynamicUserInfoStr),
	}); err != nil {
		logger.WithUser(userObj.UserID).Errorln(err)
		return err
	}
	dateTimeNowString := general.GetTimeStampString()
	go activity.ActivityLogger(userObj.UserID, userObj.SourceEntityID, userObj.UserID, constants.EntityTypeCustomer, constants.ActivityProfessionalInfoSubmitted, "", "", dateTimeNowString, false)

	return err
}

func MFLBLSavePanInfo(ctx context.Context, userObj authentication.UserStruct, data map[string]interface{}, updateRedis bool, token string) (err error, customErr error) {
	gender := constants.GenderStrToNum[data["gender"].(string)]
	if gender == -1 {
		err = errors.New("invalid gender")
		logger.WithUser(userObj.UserID).Errorln(err)
		return nil, err
	}

	if err := users.Update(nil, users.User{
		ID:     userObj.UserID,
		Name:   data["name"].(string),
		Gender: &gender,
		DOB:    data["dob"].(string),
	}); err != nil {
		logger.WithUser(userObj.UserID).Errorln(err)
		return err, nil
	}
	if updateRedis {
		userRedisObj := authentication.UserStruct{
			UserID:          userObj.UserID,
			Name:            data["name"].(string),
			Email:           userObj.Email,
			Status:          constants.UserStatusProfileUpdated,
			SourceEntityID:  userObj.SourceEntityID,
			Mobile:          userObj.Mobile,
			UniqueID:        userObj.UniqueID,
			PAN:             userObj.PAN,
			JourneyConfigID: userObj.JourneyConfigID,
		}

		err = usersutil.UpdateUserToken(userRedisObj, ctx, token)
		if err != nil {
			logger.WithUser(userObj.UserID).Errorln(err)
			return err, nil
		}
	}

	dateTimeNowString := general.GetTimeStampString()
	go activity.ActivityLogger(userObj.UserID, userObj.SourceEntityID, userObj.UserID, constants.EntityTypeCustomer, constants.ActivityPANInfoUpdated, "", "", dateTimeNowString, false)

	return err, nil
}

func TDLPanForm1DataSubmit(ctx context.Context, userObj authentication.UserStruct, data map[string]interface{}, updateRedis bool, token string) error {

	pan := data["pan"].(string)
	dob := data["dob"].(string)
	firstName := data["firstName"].(string)
	lastName := data["lastName"].(string)

	gender := constants.GenderStrToNum[data["gender"].(string)]

	fullName := fmt.Sprintf("%s %s", firstName, lastName)

	var dynamicUserInfoMap users.DynamicUserInfo
	dynamicUserInfoJSONStr, _ := users.GetDynamicUserInfo(userObj.UserID)

	err := json.Unmarshal([]byte(dynamicUserInfoJSONStr), &dynamicUserInfoMap)
	if err != nil {
		logger.WithUser(userObj.UserID).Errorln(err)
		return err
	}

	dynamicUserInfoMap.FirstName = firstName
	dynamicUserInfoMap.LastName = lastName

	byteData, err := json.Marshal(dynamicUserInfoMap)
	if err != nil {
		logger.WithUser(userObj.UserID).Errorln(err)
		return err
	}

	userStatus := constants.UserStatusProfileUpdated
	userData := users.User{
		ID:              userObj.UserID,
		Name:            fullName,
		PAN:             pan,
		DOB:             dob,
		Gender:          &gender,
		Status:          &userStatus,
		DynamicUserInfo: string(byteData),
	}
	if err := users.Update(nil, userData); err != nil {
		logger.WithUser(userObj.UserID).Errorln(err)
		return err
	}

	if updateRedis {
		userRedisObj := authentication.UserStruct{
			UserID:            userObj.UserID,
			Name:              fullName,
			Status:            userObj.Status,
			SourceEntityID:    userObj.SourceEntityID,
			Mobile:            userObj.Mobile,
			UniqueID:          userObj.UniqueID,
			PAN:               pan,
			ProgramName:       userObj.ProgramName,
			UserProgramStatus: userStatus,
			JourneyConfigID:   userObj.JourneyConfigID,
			OrganizationID:    userObj.OrganizationID,
		}

		err := usersutil.UpdateUserToken(userRedisObj, ctx, token)
		if err != nil {
			logger.WithUser(userObj.UserID).Errorln(err)
			return err
		}
	}

	dateTimeNowString := general.GetTimeStampString()
	go activity.ActivityLogger(userObj.UserID, userObj.SourceEntityID, userObj.UserID, constants.EntityTypeCustomer, constants.ActivityPANInfoUpdated, "", "", dateTimeNowString, false)

	return nil
}

func TDLPanForm2DataSubmit(ctx context.Context, userObj authentication.UserStruct, data map[string]interface{}, updateRedis bool, token string) (err error, customErr error) {

	occupation := data["occupation"].(string)
	companyCode := data["companyCode"].(string)
	natureOfBusiness, _ := data["natureOfBusiness"].(string)

	salariedCompanyName, _ := data["salariedCompanyName"].(string)
	selfEmployedCompanyName, _ := data["selfEmployedCompanyName"].(string)
	companyName := general.Coalesce(salariedCompanyName, selfEmployedCompanyName)
	if companyName == "" {
		logger.WithUser(userObj.UserID).Errorln(fmt.Errorf("invalid companyName"))
		return nil, fmt.Errorf("invalid companyName")
	}

	monthlyIncome, err := data["monthlyIncome"].(json.Number).Float64()
	if err != nil {
		logger.WithUser(userObj.UserID).Errorln(err)
		return err, nil
	}

	var dynamicUserInfoMap users.DynamicUserInfo
	dynamicUserInfoJSONStr, _ := users.GetDynamicUserInfo(userObj.UserID)

	err = json.Unmarshal([]byte(dynamicUserInfoJSONStr), &dynamicUserInfoMap)
	if err != nil {
		logger.WithUser(userObj.UserID).Errorln(err)
		return err, nil
	}

	dynamicUserInfoMap.Occupation = occupation
	dynamicUserInfoMap.CompanyName = companyName
	dynamicUserInfoMap.MonthlyIncome = monthlyIncome
	dynamicUserInfoMap.CompanyCode = companyCode
	dynamicUserInfoMap.NatureOfBusiness = natureOfBusiness

	byteData, err := json.Marshal(dynamicUserInfoMap)
	if err != nil {
		logger.WithUser(userObj.UserID).Errorln(err)
		return err, nil
	}

	if err = users.Update(nil, users.User{
		ID:              userObj.UserID,
		DynamicUserInfo: string(byteData),
	}); err != nil {
		logger.WithUser(userObj.UserID).Errorln(err)
		return err, nil
	}

	dateTimeNowString := general.GetTimeStampString()
	go activity.ActivityLogger(userObj.UserID, userObj.SourceEntityID, userObj.UserID, constants.EntityTypeCustomer, constants.ActivityPanForm2Submitted, "", "", dateTimeNowString, false)

	return nil, nil
}

func TDLPanForm3DataSubmit(ctx context.Context, userObj authentication.UserStruct, data map[string]interface{}, updateRedis bool, token string) error {

	emailID := data["emailID"].(string)
	companyEmail := data["companyEmail"].(string)
	loanPurpose := data["purpose"].(string)
	pincode := data["pincode"].(string)

	var dynamicUserInfoMap users.DynamicUserInfo
	dynamicUserInfoJSONStr, _ := users.GetDynamicUserInfo(userObj.UserID)

	err := json.Unmarshal([]byte(dynamicUserInfoJSONStr), &dynamicUserInfoMap)
	if err != nil {
		logger.WithUser(userObj.UserID).Errorln(err)
		return err
	}

	dynamicUserInfoMap.CompanyEmail = companyEmail
	dynamicUserInfoMap.LoanPurpose = loanPurpose
	dynamicUserInfoMap.UserDeclaredPincode = pincode

	byteData, err := json.Marshal(dynamicUserInfoMap)
	if err != nil {
		logger.WithUser(userObj.UserID).Errorln(err)
		return err
	}

	if err = users.Update(nil, users.User{
		ID:              userObj.UserID,
		Email:           emailID,
		Pincode:         pincode,
		DynamicUserInfo: string(byteData),
	}); err != nil {
		logger.WithUser(userObj.UserID).Errorln(err)
		return err
	}

	if updateRedis {
		userRedisObj := authentication.UserStruct{
			UserID:            userObj.UserID,
			Name:              userObj.Name,
			Status:            constants.UserStatusProfileUpdated,
			SourceEntityID:    userObj.SourceEntityID,
			Mobile:            userObj.Mobile,
			UniqueID:          userObj.UniqueID,
			PAN:               userObj.PAN,
			ProgramName:       userObj.ProgramName,
			UserProgramStatus: constants.UserStatusQualified,
			JourneyConfigID:   userObj.JourneyConfigID,
			Email:             emailID,
		}

		err := usersutil.UpdateUserToken(userRedisObj, ctx, token)
		if err != nil {
			logger.WithUser(userObj.UserID).Errorln(err)
			return err
		}
	}

	dateTimeNowString := general.GetTimeStampString()
	go activity.ActivityLogger(userObj.UserID, userObj.SourceEntityID, userObj.UserID, constants.EntityTypeCustomer, constants.ActivityProfileUpdated, "", "", dateTimeNowString, false)

	return nil
}

func TDLRepeatLoanPanFormDataSubmit(ctx context.Context, userObj authentication.UserStruct, data map[string]interface{}, updateRedis bool, token string) (err error, customErr error) {

	occupation, _ := data["occupation"].(string)
	companyCode, _ := data["companyCode"].(string)

	salariedCompanyName, _ := data["salariedCompanyName"].(string)
	selfEmployedCompanyName, _ := data["selfEmployedCompanyName"].(string)
	companyName := general.Coalesce(salariedCompanyName, selfEmployedCompanyName)
	if companyName == "" {
		logger.WithUser(userObj.UserID).Errorln(fmt.Errorf("invalid companyName"))
		return nil, fmt.Errorf("invalid companyName")
	}

	monthlyIncome, err := data["monthlyIncome"].(json.Number).Float64()
	if err != nil {
		logger.WithUser(userObj.UserID).Errorln(err)
		return err, nil
	}

	companyEmail, _ := data["companyEmail"].(string)
	pincode, _ := data["pincode"].(string)

	var dynamicUserInfoMap users.DynamicUserInfo
	dynamicUserInfoJSONStr, _ := users.GetDynamicUserInfo(userObj.UserID)

	err = json.Unmarshal([]byte(dynamicUserInfoJSONStr), &dynamicUserInfoMap)
	if err != nil {
		logger.WithUser(userObj.UserID).Errorln(err)
		return err, nil
	}

	dynamicUserInfoMap.Occupation = occupation
	dynamicUserInfoMap.CompanyName = companyName
	dynamicUserInfoMap.MonthlyIncome = monthlyIncome
	dynamicUserInfoMap.CompanyCode = companyCode
	dynamicUserInfoMap.CompanyEmail = companyEmail
	dynamicUserInfoMap.UserDeclaredPincode = pincode

	byteData, err := json.Marshal(dynamicUserInfoMap)
	if err != nil {
		logger.WithUser(userObj.UserID).Errorln(err)
		return err, nil
	}

	if err = users.Update(nil, users.User{
		ID:              userObj.UserID,
		Pincode:         pincode,
		DynamicUserInfo: string(byteData),
	}); err != nil {
		logger.WithUser(userObj.UserID).Errorln(err)
		return err, nil
	}

	dateTimeNowString := general.GetTimeStampString()
	go activity.ActivityLogger(userObj.UserID, userObj.SourceEntityID, userObj.UserID, constants.EntityTypeCustomer, constants.ActivityMultiLoanPanFormUpdated, "", "", dateTimeNowString, false)

	return nil, nil
}

func ABCDPersonalInfoSubmit(ctx context.Context, userObj authentication.UserStruct, data map[string]interface{}, updateRedis bool, token string) (err error) {

	lat, _ := data["lat"].(string)
	lon, _ := data["lon"].(string)
	accuracy, _ := data["accuracy"].(string)
	height, _ := data["height"].(string)
	if lat != "" || lon != "" || accuracy != "" || height != "" {
		err := userlocation.Insert(userlocation.Struct{UserID: userObj.UserID, Lat: lat, Long: lon, Accuracy: accuracy, Height: height, Step: "PROFESSIONAL_INFO", LocationType: "current_location"})
		if err != nil {
			logger.WithUser(userObj.UserID).Error(err)
		}
	}

	dynamicUserInfoStr, err := json.Marshal(data)
	if err != nil {
		logger.WithUser(userObj.UserID).Errorln(err)
		return err
	}

	deviceID, _ := data["deviceID"].(string)

	userStatus := constants.UserStatusProfileUpdated
	err = users.Update(nil, users.User{
		ID:              userObj.UserID,
		Email:           data["email"].(string),
		PAN:             data["pan"].(string),
		DeviceID:        deviceID,
		Status:          &userStatus,
		DynamicUserInfo: string(dynamicUserInfoStr),
	})
	if err != nil {
		logger.WithUser(userObj.UserID).Errorln(err)
		return err
	}

	if updateRedis {
		userRedisObj := authentication.UserStruct{
			UserID:          userObj.UserID,
			Email:           data["email"].(string),
			Status:          userStatus,
			SourceEntityID:  userObj.SourceEntityID,
			Mobile:          userObj.Mobile,
			UniqueID:        userObj.UniqueID,
			PAN:             data["pan"].(string),
			JourneyConfigID: userObj.JourneyConfigID,
		}

		err = usersutil.UpdateUserToken(userRedisObj, ctx, token)
		if err != nil {
			logger.WithUser(userObj.UserID).Errorln(err)
			return err
		}
	}

	dateTimeNowString := general.GetTimeStampString()
	go activity.ActivityLogger(userObj.UserID, userObj.SourceEntityID, userObj.UserID, constants.EntityTypeCustomer, constants.ActivityPANInfoUpdated, "", "", dateTimeNowString, false)

	dateTimeNowString = general.GetTimeStampString()
	go activity.ActivityLogger(userObj.UserID, userObj.SourceEntityID, userObj.UserID, constants.EntityTypeCustomer, constants.ActivityProfileUpdated, "", "", dateTimeNowString, false)

	return err
}

func GenericSubmitV2(ctx context.Context, userObj authentication.UserStruct, data map[string]interface{}, token string, schema apischemamapper.Schema, moduleName, ipAddress string) (map[string]any, DebugStruct, error) {
	debugObj := NewDebugObject()
	dynMap, err := NewDynamicDataMap(data)
	if err != nil {
		logger.WithContext(ctx).Errorln(err)
		debugObj.SetError(err)
		return nil, *debugObj, err
	}
	debugObj.SetInitialMap(*dynMap)

	err = dynMap.ExecuteTriggers(userObj.UserID, userObj.LoanApplicationID, schema.LookupConfig)
	if err != nil {
		logger.WithContext(ctx).Errorln(err)
		debugObj.SetError(err)
		return nil, *debugObj, err
	}
	debugObj.SetMapWithLookupResolved(*dynMap)

	err = dynMap.DeriveFields(userObj.UserID, schema.DerivedDynamicDataConfig)
	if err != nil {
		logger.WithContext(ctx).Errorln(err)
		debugObj.SetError(err)
		return nil, *debugObj, err
	}
	debugObj.SetMapWithDerivedFields(*dynMap)

	dynamicDataObject := NewDynamicDataObject(userObj.UserID, userObj.SourceEntityID, *dynMap)
	err = dynamicDataObject.Populate()
	if err != nil {
		logger.WithContext(ctx).Errorln(err)
		debugObj.SetError(err)
		return nil, *debugObj, err
	}
	debugObj.SetDataSaverObject(*dynamicDataObject)

	mapWithDerivedFields := general.CloneMap(*dynMap)
	dynMap.Cleanup(schema.CleanupKeys)
	debugObj.SetMapWithPostCleanup(*dynMap)

	err = saveData(ctx, dynamicDataObject, dynMap, userObj, ipAddress, moduleName)
	if err != nil {
		logger.WithContext(ctx).Errorln(err)
		debugObj.SetError(err)
		return nil, *debugObj, err
	}

	if schema.RefreshToken {
		var userRedisObj = authentication.UserStruct{
			UserID:          userObj.UserID,
			Name:            userObj.Name,
			Email:           userObj.Email,
			Status:          userObj.Status,
			SourceEntityID:  userObj.SourceEntityID,
			Mobile:          userObj.Mobile,
			UniqueID:        userObj.UniqueID,
			PAN:             userObj.PAN,
			JourneyConfigID: userObj.JourneyConfigID,
		}
		if dynamicDataObject.Name.Valid {
			userRedisObj.Name = dynamicDataObject.Name.String
		}
		if dynamicDataObject.Email.Valid {
			userRedisObj.Email = dynamicDataObject.Email.String
		}
		if dynamicDataObject.PersonalPAN.Valid {
			userRedisObj.PAN = dynamicDataObject.PersonalPAN.String
		}
		err := usersutil.UpdateUserToken(userRedisObj, ctx, token)
		if err != nil {
			logger.WithUser(userObj.UserID).Errorln(err)
			debugObj.SetError(err)
			return nil, *debugObj, err
		}
	}

	for _, activityObj := range schema.ActivityLogConfig {
		activity.ActivityLogger(userObj.UserID,
			userObj.SourceEntityID,
			activityObj.EntityRef,
			activityObj.EntityType,
			activityObj.EventType,
			activityObj.Description,
			userObj.LoanApplicationID,
			general.GetTimeStampString(),
			false,
		)
	}

	return mapWithDerivedFields, *debugObj, nil
}

func saveData(ctx context.Context, dynamicDataObject *DynamicDataStruct, dynMap *DynamicDataMap, userObj authentication.UserStruct, ipAddress string, moduleName string) error {
	var err error
	if userObj.LoanApplicationID == "" {
		err = SaveUserDetails(ctx, userObj.UserID, dynamicDataObject, dynMap)
		if err != nil {
			logger.WithContext(ctx).Errorln(err)
			return err
		}
	} else {
		// TODO: move this to models
		query := `UPDATE user_loan_details SET pre_loan_data = $1, fathers_name = $2, ref1_name = $3, ref2_name = $4, marital_status = $5, ref2_phone = $6, ref1_phone = $7, updated_at=NOW() 
					WHERE loan_application_id = $8 
					AND user_id = $9
					AND status = $10;`
		result, err := database.ExecContext(ctx, query, dynMap.ToString(), dynamicDataObject.FathersName, dynamicDataObject.Reference1Name, dynamicDataObject.Reference2Name, dynamicDataObject.MaritalStatusCode, dynamicDataObject.Reference2Contact, dynamicDataObject.Reference1Contact, userObj.LoanApplicationID, userObj.UserID, constants.LoanDetailsStatusActive)
		if err != nil {
			logger.WithContext(ctx).Errorln(err)
			return err
		}
		rowsAffected, _ := result.RowsAffected()
		if rowsAffected == 0 {
			query = `insert into user_loan_details(user_loan_details_id, user_id, loan_application_id, status, created_by, created_at, pre_loan_data, fathers_name, ref1_name, ref2_name, marital_status, ref2_phone, ref1_phone) 
					values (uuid_generate_v4(), $1, $2, $3, $4, NOW(), $5, $6, $7, $8, $9, $10, $11)`
			_, err = database.ExecContext(ctx, query, userObj.UserID, userObj.LoanApplicationID, constants.LoanDetailsStatusActive, userObj.UserID, dynMap.ToString(), dynamicDataObject.FathersName, dynamicDataObject.Reference1Name, dynamicDataObject.Reference2Name, dynamicDataObject.MaritalStatusCode, dynamicDataObject.Reference2Contact, dynamicDataObject.Reference1Contact)
			if err != nil {
				logger.WithContext(ctx).Errorln(err)
				return err
			}
		}
	}
	if len(dynamicDataObject.Consents) > 0 {
		for idx := range dynamicDataObject.Consents {
			if consentData, isMap := dynamicDataObject.Consents[idx].(map[string]interface{}); isMap {
				logger.Log.Infoln(consentData, isMap)
				consentText, _ := consentData["consentText"].(string)
				consentType, _ := consentData["consentType"].(string)
				lat, _ := consentData["lat"].(string)
				lon, _ := consentData["lon"].(string)
				accuracy, _ := consentData["accuracy"].(string)
				height, _ := consentData["height"].(string)
				_ = legallogs.SaveWithConsentTypeV2(ctx, userObj.UserID, consentText, consentType, lat, lon, height, accuracy, ipAddress, time.Now())
			}

		}
	}

	if dynamicDataObject.Lat.Valid || dynamicDataObject.Lon.Valid || dynamicDataObject.Height.Valid || dynamicDataObject.Accuracy.Valid {
		err := userlocation.Insert(userlocation.Struct{UserID: userObj.UserID, Lat: dynamicDataObject.Lat.String, Long: dynamicDataObject.Lon.String, Accuracy: dynamicDataObject.Accuracy.String, Height: dynamicDataObject.Height.String, Step: moduleName, LocationType: "current_location"})
		if err != nil {
			logger.WithUser(userObj.UserID).Error(err)
		}
	}
	return nil
}
