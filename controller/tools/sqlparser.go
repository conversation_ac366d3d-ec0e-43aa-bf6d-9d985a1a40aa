package tools

import (
	"bytes"
	"encoding/json"
	"finbox/go-api/models/registry"
	"fmt"
	"reflect"
	"strconv"
	"strings"

	"github.com/xwb1989/sqlparser"
)

type APIRequest struct {
	ModelName  string         `json:"modelName"`
	Conditions map[string]any `json:"conditions,omitempty"`
	Updates    map[string]any `json:"updates,omitempty"`
	Data       map[string]any `json:"data,omitempty"` // for insert
}

type BatchAPIRequest struct {
	Operations []APIRequest `json:"operations"`
}

// ConvertSQLToAPIRequest converts a single SQL query to an API request
func ConvertSQLToAPIRequest(query string) (*APIRequest, error) {
	stmt, err := sqlparser.Parse(query)
	if err != nil {
		return nil, fmt.Errorf("failed to parse SQL: %w", err)
	}

	var result *APIRequest
	switch stmt := stmt.(type) {
	case *sqlparser.Select:
		result, err = parseSelect(stmt)
	case *sqlparser.Update:
		result, err = parseUpdate(stmt)
	case *sqlparser.Insert:
		result, err = parseInsert(stmt)
	case *sqlparser.Delete:
		result, err = parseDelete(stmt)
	default:
		return nil, fmt.Errorf("unsupported SQL type: %T", stmt)
	}

	if err != nil {
		return nil, err
	}

	// Validate the request against the model
	if err := validateAPIRequest(result); err != nil {
		return nil, fmt.Errorf("validation failed: %w", err)
	}

	return result, nil
}

// ConvertBatchSQLToAPIRequest converts multiple SQL queries to a batch API request
func ConvertBatchSQLToAPIRequest(queries []string) (*BatchAPIRequest, error) {
	if len(queries) == 0 {
		return nil, fmt.Errorf("no queries provided")
	}

	batch := &BatchAPIRequest{
		Operations: make([]APIRequest, 0, len(queries)),
	}

	for i, query := range queries {
		req, err := ConvertSQLToAPIRequest(query)
		if err != nil {
			return nil, fmt.Errorf("query %d failed: %w", i+1, err)
		}
		batch.Operations = append(batch.Operations, *req)
	}

	return batch, nil
}

// validateAPIRequest validates that all fields in the request exist in the GORM model
func validateAPIRequest(req *APIRequest) error {
	// Get model instance
	model, err := registry.NewModelInstance(req.ModelName)
	if err != nil {
		return fmt.Errorf("invalid model %s: %w", req.ModelName, err)
	}

	// Get all valid fields from the model
	validFields := getModelFields(model)

	// Validate conditions
	if req.Conditions != nil {
		for field := range req.Conditions {
			if !isValidField(field, validFields) {
				return fmt.Errorf("invalid field in conditions: %s", field)
			}
		}
	}

	// Validate updates
	if req.Updates != nil {
		for field := range req.Updates {
			if !isValidField(field, validFields) {
				return fmt.Errorf("invalid field in updates: %s", field)
			}
		}
	}

	// Validate data (for insert)
	if req.Data != nil {
		for field := range req.Data {
			if !isValidField(field, validFields) {
				return fmt.Errorf("invalid field in data: %s", field)
			}
		}
	}

	return nil
}

// getModelFields extracts all valid JSON field names from a GORM model
func getModelFields(model interface{}) map[string]bool {
	fields := make(map[string]bool)

	modelType := reflect.TypeOf(model)
	if modelType.Kind() == reflect.Ptr {
		modelType = modelType.Elem()
	}

	for i := 0; i < modelType.NumField(); i++ {
		field := modelType.Field(i)

		// Skip unexported fields
		if field.PkgPath != "" {
			continue
		}

		// Get JSON tag
		jsonTag := strings.Split(field.Tag.Get("json"), ",")[0]
		if jsonTag == "" || jsonTag == "-" {
			jsonTag = field.Name
		}

		fields[jsonTag] = true
	}

	return fields
}

// isValidField checks if a field name is valid for the model
func isValidField(fieldName string, validFields map[string]bool) bool {
	return validFields[fieldName]
}

func parseSelect(stmt *sqlparser.Select) (*APIRequest, error) {
	tableName, err := GetTableNameFromStmt(stmt)
	if err != nil {
		return nil, err
	}

	modelName, err := registry.GetModelNameByTable(tableName)
	if err != nil {
		return nil, err
	}

	conditions := map[string]any{}
	if stmt.Where != nil {
		if err := extractConditions(stmt.Where.Expr, conditions); err != nil {
			return nil, fmt.Errorf("parsing WHERE clause: %w", err)
		}
	}

	return &APIRequest{
		ModelName:  modelName,
		Conditions: conditions,
	}, nil
}

func parseUpdate(stmt *sqlparser.Update) (*APIRequest, error) {
	tableName, err := GetTableNameFromStmt(stmt)
	if err != nil {
		return nil, err
	}

	modelName, err := registry.GetModelNameByTable(tableName)
	if err != nil {
		return nil, err
	}

	updates := map[string]any{}
	for _, expr := range stmt.Exprs {
		key := expr.Name.Name.String()
		val := inferValue(expr.Expr)
		updates[key] = val
	}

	conditions := map[string]any{}
	if stmt.Where != nil {
		if err := extractConditions(stmt.Where.Expr, conditions); err != nil {
			return nil, fmt.Errorf("parsing WHERE clause: %w", err)
		}
	}

	return &APIRequest{
		ModelName:  modelName,
		Conditions: conditions,
		Updates:    updates,
	}, nil
}

func parseInsert(stmt *sqlparser.Insert) (*APIRequest, error) {
	tableName, err := GetTableNameFromStmt(stmt)
	if err != nil {
		return nil, err
	}

	modelName, err := registry.GetModelNameByTable(tableName)
	if err != nil {
		return nil, err
	}

	if len(stmt.Columns) != len(stmt.Rows.(sqlparser.Values)[0]) {
		return nil, fmt.Errorf("mismatched columns and values in INSERT")
	}

	data := map[string]any{}
	for i, col := range stmt.Columns {
		valExpr := stmt.Rows.(sqlparser.Values)[0][i]
		key := col.String()
		data[key] = inferValue(valExpr)
	}

	return &APIRequest{
		ModelName: modelName,
		Data:      data,
	}, nil
}

func parseDelete(stmt *sqlparser.Delete) (*APIRequest, error) {
	tableName, err := GetTableNameFromStmt(stmt)
	if err != nil {
		return nil, err
	}

	modelName, err := registry.GetModelNameByTable(tableName)
	if err != nil {
		return nil, err
	}

	conditions := map[string]any{}
	if stmt.Where != nil {
		if err := extractConditions(stmt.Where.Expr, conditions); err != nil {
			return nil, fmt.Errorf("parsing WHERE clause: %w", err)
		}
	}

	return &APIRequest{
		ModelName:  modelName,
		Conditions: conditions,
	}, nil
}

// GetTableNameFromStmt extracts the main table name from any sqlparser.Statement.
// Supports SELECT, INSERT, UPDATE, DELETE statements.
func GetTableNameFromStmt(stmt sqlparser.Statement) (string, error) {
	switch stmt := stmt.(type) {
	case *sqlparser.Select:
		if len(stmt.From) == 0 {
			return "", fmt.Errorf("no tables in FROM clause")
		}
		if aliased, ok := stmt.From[0].(*sqlparser.AliasedTableExpr); ok {
			return sqlparser.GetTableName(aliased.Expr).String(), nil
		}

	case *sqlparser.Insert:
		return stmt.Table.Name.String(), nil

	case *sqlparser.Update:
		if len(stmt.TableExprs) == 0 {
			return "", fmt.Errorf("no tables in UPDATE clause")
		}
		if aliased, ok := stmt.TableExprs[0].(*sqlparser.AliasedTableExpr); ok {
			return sqlparser.GetTableName(aliased.Expr).String(), nil
		}

	case *sqlparser.Delete:
		if len(stmt.TableExprs) == 0 {
			return "", fmt.Errorf("no tables in DELETE clause")
		}
		if aliased, ok := stmt.TableExprs[0].(*sqlparser.AliasedTableExpr); ok {
			return sqlparser.GetTableName(aliased.Expr).String(), nil
		}
	}

	return "", fmt.Errorf("unsupported or malformed SQL statement")
}

func extractConditions(expr sqlparser.Expr, out map[string]any) error {
	switch cond := expr.(type) {
	case *sqlparser.ComparisonExpr:
		key := cond.Left.(*sqlparser.ColName).Name.String()
		switch cond.Operator {
		case "=", "==":
			out[key] = inferValue(cond.Right)
		case "in", "IN":
			tuple, ok := cond.Right.(sqlparser.ValTuple)
			if !ok {
				return nil
			}
			var arr []any
			for _, val := range tuple {
				arr = append(arr, inferValue(val))
			}
			out[key] = arr
		default:
			// Could store under $gt, $lt, etc. in future
			out[key+"__"+cond.Operator] = inferValue(cond.Right)
		}
	case *sqlparser.AndExpr:
		if err := extractConditions(cond.Left, out); err != nil {
			return err
		}
		if err := extractConditions(cond.Right, out); err != nil {
			return err
		}
	case *sqlparser.ParenExpr:
		return extractConditions(cond.Expr, out)
	default:
		// skip unsupported expression
	}
	return nil
}

func inferValue(expr sqlparser.Expr) any {
	switch val := expr.(type) {
	case *sqlparser.SQLVal:
		s := string(val.Val)

		// Try int first
		if i, err := strconv.Atoi(s); err == nil {
			return i
		}

		// Try to float next
		if f, err := strconv.ParseFloat(s, 64); err == nil {
			return f
		}

		// Try bool last
		lower := s
		if lower == "true" || lower == "false" {
			if b, err := strconv.ParseBool(lower); err == nil {
				return b
			}
		}

		return s

	case *sqlparser.NullVal:
		return nil

	default:
		return sqlparser.String(expr)
	}
}

func EncodeJSONBody(input json.RawMessage) (string, error) {
	var buf bytes.Buffer
	if err := json.Compact(&buf, input); err != nil {
		return "", err
	}

	escapedBytes, err := json.Marshal(buf.String())
	if err != nil {
		return "", err
	}

	return string(escapedBytes), nil
}

// ConvertBatchAPIRequestToOperations converts a BatchAPIRequest to the format expected by batch controllers
func ConvertBatchAPIRequestToOperations(batch *BatchAPIRequest) []map[string]interface{} {
	operations := make([]map[string]interface{}, len(batch.Operations))

	for i, op := range batch.Operations {
		operation := map[string]interface{}{
			"modelName": op.ModelName,
		}

		// Determine operation type based on what fields are present
		if op.Data != nil {
			operation["operation"] = "create"
			operation["data"] = op.Data
		} else if op.Updates != nil {
			operation["operation"] = "update"
			operation["updates"] = op.Updates
			if op.Conditions != nil {
				operation["conditions"] = op.Conditions
			}
		} else if op.Conditions != nil {
			operation["operation"] = "delete"
			operation["conditions"] = op.Conditions
		} else {
			// Default to select if only conditions are present
			operation["operation"] = "select"
			operation["conditions"] = op.Conditions
		}

		operations[i] = operation
	}

	return operations
}
