package tools

import (
	"context"
	"encoding/json"
	"finbox/go-api/authentication"
	auth "finbox/go-api/authentication"
	"finbox/go-api/controller/tools/constants"
	"finbox/go-api/controller/tools/operations"
	"finbox/go-api/errorHandler"
	"finbox/go-api/internal/repository/gorm"
	"finbox/go-api/models/registry"
	"fmt"
	"net/http"
	"reflect"
	"strings"
	"time"

	"finbox/go-api/models/approvalrequest"

	"github.com/google/uuid"
	gormio "gorm.io/gorm"
)

// GetCont Get Controller
func GetCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusInternalServerError)
		ctx := r.Context()

		var body struct {
			ModelName  string         `json:"modelName" validate:"required"`
			Conditions map[string]any `json:"conditions" validate:"required"`
		}

		if err := DecodeAndValidate(r, &body); err != nil {
			errMsg := fmt.Sprintf("failed to decode JSON body: %s", err.Error())
			errorHandler.CustomError(w, http.StatusBadRequest, errMsg)
			return
		}

		model, err := registry.NewModelInstance(body.ModelName)
		if err != nil {
			errMsg := fmt.Sprintf("failed to create model instance: %s", err.Error())
			errorHandler.CustomError(w, http.StatusBadRequest, errMsg)
			return
		}

		repo := gorm.GetRepository()
		if err := repo.Get(model, body.Conditions); err != nil {
			errMsg := fmt.Sprintf("failed to get record: %s", err.Error())
			errorHandler.CustomError(w, http.StatusNotFound, errMsg)
			return
		}

		ctx = context.WithValue(ctx, "resData", model)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// GetAllCont handles fetching multiple records with filters, pagination, and ordering.
func GetAllCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusInternalServerError)
		ctx := r.Context()

		var body struct {
			ModelName  string                 `json:"modelName" validate:"required"`
			Conditions map[string]interface{} `json:"conditions"`
			Filters    []gorm.Filter          `json:"filters"` // Optional: supports advanced operators like field, op, value
			OrderBy    string                 `json:"orderBy"` // Optional: e.g., "created_at desc"
			Limit      int                    `json:"limit"`   // Optional
			Offset     int                    `json:"offset"`  // Optional
		}

		if err := DecodeAndValidate(r, &body); err != nil {
			errorHandler.CustomError(w, http.StatusBadRequest, fmt.Sprintf("failed to decode JSON body: %s", err.Error()))
			return
		}

		model, err := registry.NewModelInstance(body.ModelName)
		if err != nil {
			errorHandler.CustomError(w, http.StatusBadRequest, fmt.Sprintf("failed to create model instance: %s", err.Error()))
			return
		}

		result, err := registry.NewModelSlicePointer(model)
		if err != nil {
			errorHandler.CustomError(w, http.StatusInternalServerError, fmt.Sprintf("failed to create slice pointer: %s", err.Error()))
			return
		}

		repo := gorm.GetRepository()
		total, err := repo.GetAll(result, body.Conditions, body.Filters, body.Limit, body.Offset, body.OrderBy)
		if err != nil {
			errorHandler.CustomError(w, http.StatusInternalServerError, fmt.Sprintf("failed to get records: %s", err.Error()))
			return
		}

		resp := map[string]interface{}{
			"result":     result,
			"pagination": map[string]int{"total": int(total), "limit": body.Limit, "offset": body.Offset},
		}

		ctx = context.WithValue(ctx, "resData", resp)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// CreateCont Create Controller
func CreateCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusInternalServerError)

		var body struct {
			ModelName     string         `json:"modelName" validate:"required"`
			Data          map[string]any `json:"data" validate:"required"`
			ApproverEmail *string        `json:"approverEmail,omitempty"`
		}

		if err := DecodeAndValidate(r, &body); err != nil {
			errorHandler.CustomError(w, http.StatusBadRequest, "failed to decode JSON body: "+err.Error())
			return
		}

		ctx := r.Context()
		adminEmail, _ := auth.GetAdminEmail(ctx)
		if adminEmail == "" {
			errorHandler.CustomError(w, http.StatusForbidden, "Missing admin email in context")
			return
		}

		// Use OOP Operation type
		op := &operations.CreateOperation{ModelName: body.ModelName, Data: body.Data}
		if authentication.IsSuperAdmin(ctx) {
			repo := gorm.GetRepository()
			var result interface{}
			err := repo.DB().Transaction(func(tx *gormio.DB) error {
				var execErr error
				result, execErr = op.Run(ctx, tx)
				return execErr
			})
			if err != nil {
				errorHandler.CustomError(w, http.StatusInternalServerError, "Failed to create record: "+err.Error())
				return
			}
			// Log as approval request (trace super admin action)
			now := time.Now().UTC()
			approval := &approvalrequest.ApprovalRequestGorm{
				ID:          uuid.New(),
				Operation:   constants.OpTypeCreate,
				ModelName:   body.ModelName,
				Data:        mustMarshal(body.Data),
				Status:      "approved",
				RequestedBy: adminEmail,
				RequestedAt: now,
				ApprovedBy:  &adminEmail,
				ApprovedAt:  &now,
				Executed:    true,
			}
			_ = repo.DB().Create(approval)
			ctx = context.WithValue(ctx, "resData", map[string]interface{}{
				"message": "Record created by super admin.",
				"result":  result,
			})
			next.ServeHTTP(w, r.WithContext(ctx))
			return
		}

		// Submit for approval
		approvalBody := map[string]interface{}{
			"operation":     constants.OpTypeCreate,
			"modelName":     body.ModelName,
			"data":          body.Data,
			"conditions":    nil,
			"approverEmail": body.ApproverEmail,
		}
		approvalReq, _ := json.Marshal(approvalBody)
		r2 := r.Clone(r.Context())
		r2.Body = NopCloserFromBytes(approvalReq)
		SubmitApprovalRequestCont(next).ServeHTTP(w, r2)
	})
}

// UpdateCont Update Controller
func UpdateCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusInternalServerError)

		var body struct {
			ModelName     string                 `json:"modelName" validate:"required"`
			Conditions    map[string]interface{} `json:"conditions" validate:"required"`
			Updates       map[string]interface{} `json:"updates" validate:"required"`
			Lock          bool                   `json:"lock"`
			ApproverEmail *string                `json:"approverEmail,omitempty"`
		}

		if err := DecodeAndValidate(r, &body); err != nil {
			errorHandler.CustomError(w, http.StatusBadRequest, "failed to decode JSON body: "+err.Error())
			return
		}

		ctx := r.Context()
		adminEmail, _ := auth.GetAdminEmail(ctx)
		if adminEmail == "" {
			errorHandler.CustomError(w, http.StatusForbidden, "Missing admin email in context")
			return
		}

		// For non-super-admins, require all primary key fields in conditions
		if !authentication.IsSuperAdmin(ctx) {
			pkFields, err := registry.GetPrimaryKeyFields(body.ModelName)
			if err != nil {
				errorHandler.CustomError(w, http.StatusBadRequest, "Could not determine primary key fields: "+err.Error())
				return
			}
			var missing []string
			for _, pk := range pkFields {
				if _, ok := body.Conditions[pk]; !ok {
					missing = append(missing, pk)
				}
			}
			if len(missing) > 0 {
				errorHandler.CustomError(w, http.StatusBadRequest, "Update requires all primary key fields in conditions: "+strings.Join(missing, ", "))
				return
			}
		}

		// Use OOP Operation type
		op := &operations.UpdateOperation{ModelName: body.ModelName, Conditions: body.Conditions, Updates: body.Updates}
		if authentication.IsSuperAdmin(ctx) {
			repo := gorm.GetRepository()
			var result interface{}
			err := repo.DB().Transaction(func(tx *gormio.DB) error {
				var execErr error
				result, execErr = op.Run(ctx, tx)
				return execErr
			})
			if err != nil {
				errorHandler.CustomError(w, http.StatusInternalServerError, "Failed to update record: "+err.Error())
				return
			}
			// Log as approval request (trace super admin action)
			now := time.Now().UTC()
			approval := &approvalrequest.ApprovalRequestGorm{
				ID:          uuid.New(),
				Operation:   constants.OpTypeUpdate,
				ModelName:   body.ModelName,
				Data:        mustMarshal(body.Updates),
				Conditions:  mustMarshal(body.Conditions),
				Status:      "approved",
				RequestedBy: adminEmail,
				RequestedAt: now,
				ApprovedBy:  &adminEmail,
				ApprovedAt:  &now,
				Executed:    true,
			}
			_ = repo.DB().Create(approval)
			ctx = context.WithValue(ctx, "resData", map[string]interface{}{
				"message": "Record updated by super admin.",
				"result":  result,
			})
			next.ServeHTTP(w, r.WithContext(ctx))
			return
		}

		// Submit for approval
		approvalBody := map[string]interface{}{
			"operation":     constants.OpTypeUpdate,
			"modelName":     body.ModelName,
			"data":          body.Updates,
			"conditions":    body.Conditions,
			"approverEmail": body.ApproverEmail,
		}
		approvalReq, _ := json.Marshal(approvalBody)
		r2 := r.Clone(r.Context())
		r2.Body = NopCloserFromBytes(approvalReq)
		SubmitApprovalRequestCont(next).ServeHTTP(w, r2)
	})
}

// ListModelNamesCont List Model Names
func ListModelNamesCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusInternalServerError)
		ctx := r.Context()

		modelNames := registry.GetModelList()
		var modelsWithAccess []map[string]any
		for _, model := range modelNames {
			access := false
			// Check if user has the create or update access to the model
			if err := auth.CheckModelAccess(ctx, model, constants.OpTypeCreate); err == nil {
				access = true
			} else if err := auth.CheckModelAccess(ctx, model, constants.OpTypeUpdate); err == nil {
				access = true
			}
			modelsWithAccess = append(modelsWithAccess, map[string]any{
				"model":  model,
				"access": access,
			})
		}
		ctx = context.WithValue(ctx, "resData", map[string]any{
			"models": modelsWithAccess,
		})
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// DescribeCont Describes the model
func DescribeCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusInternalServerError)
		ctx := r.Context()

		var body struct {
			ModelName string `json:"modelName" validate:"required"`
		}

		if err := DecodeAndValidate(r, &body); err != nil {
			errMsg := fmt.Sprintf("failed to decode JSON body: %s", err.Error())
			errorHandler.CustomError(w, http.StatusBadRequest, errMsg)
			return
		}

		model, err := registry.NewModelInstance(body.ModelName)
		if err != nil {
			errMsg := fmt.Sprintf("failed to create model instance: %s", err.Error())
			errorHandler.CustomError(w, http.StatusBadRequest, errMsg)
			return
		}

		modelType := reflect.TypeOf(model)
		if modelType.Kind() == reflect.Ptr {
			modelType = modelType.Elem()
		}

		var fields []map[string]string
		for i := 0; i < modelType.NumField(); i++ {
			field := modelType.Field(i)

			// Skip unexported fields
			if field.PkgPath != "" {
				continue
			}

			jsonTag := strings.Split(field.Tag.Get("json"), ",")[0]
			if jsonTag == "" || jsonTag == "-" {
				jsonTag = field.Name
			}

			isPrimaryKey := false
			if field.Tag.Get("primaryKey") == "true" || strings.Contains(field.Tag.Get("gorm"), "primaryKey") {
				isPrimaryKey = true
			}
			fields = append(fields, map[string]string{
				"field":         jsonTag,
				"goField":       field.Name,
				"type":          field.Type.String(),
				"updateAllowed": fmt.Sprintf("%v", field.Tag.Get("update") != "false"),
				"required":      fmt.Sprintf("%v", strings.Contains(field.Tag.Get("validate"), "required")),
				"gorm":          field.Tag.Get("gorm"),
				"validate":      field.Tag.Get("validate"),
				"primaryKey":    fmt.Sprintf("%v", isPrimaryKey),
			})
		}

		ctx = context.WithValue(ctx, "resData", map[string]any{
			"model":  body.ModelName,
			"fields": fields,
		})
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// SQLToRequestCont SQL to Request
func SQLToRequestCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusInternalServerError)
		ctx := r.Context()

		var body struct {
			Query   string   `json:"query,omitempty"`   // Single query
			Queries []string `json:"queries,omitempty"` // Batch queries
		}

		if err := DecodeAndValidate(r, &body); err != nil {
			errorHandler.CustomError(w, http.StatusBadRequest, fmt.Sprintf("invalid request body: %v", err))
			return
		}

		// Check if it's a single query or batch
		if body.Query != "" && len(body.Queries) > 0 {
			errorHandler.CustomError(w, http.StatusBadRequest, "cannot provide both 'query' and 'queries' fields")
			return
		}

		if body.Query == "" && len(body.Queries) == 0 {
			errorHandler.CustomError(w, http.StatusBadRequest, "must provide either 'query' or 'queries' field")
			return
		}

		var response interface{}

		if body.Query != "" {
			// Single query
			payload, err := ConvertSQLToAPIRequest(body.Query)
			if err != nil {
				errorHandler.CustomError(w, http.StatusBadRequest, fmt.Sprintf("SQL parse error: %v", err))
				return
			}

			// Determine operation type based on what fields are present
			request := map[string]interface{}{
				"modelName": payload.ModelName,
			}
			if payload.Data != nil {
				request["operation"] = "create"
				request["data"] = payload.Data
			} else if payload.Updates != nil {
				request["operation"] = "update"
				request["updates"] = payload.Updates
				if payload.Conditions != nil {
					request["conditions"] = payload.Conditions
				}
			} else if payload.Data == nil && payload.Updates == nil && payload.Conditions != nil && strings.HasPrefix(strings.ToLower(strings.TrimSpace(body.Query)), "delete") {
				// If the original query is DELETE, set operation to delete
				request["operation"] = "delete"
				request["conditions"] = payload.Conditions
			} else {
				// Default to select
				request["operation"] = "select"
				if payload.Conditions != nil {
					request["conditions"] = payload.Conditions
				}
			}

			response = map[string]interface{}{
				"message": "Query converted successfully",
				"request": request,
			}
		} else {
			// Batch queries
			batchPayload, err := ConvertBatchSQLToAPIRequest(body.Queries)
			if err != nil {
				errorHandler.CustomError(w, http.StatusBadRequest, fmt.Sprintf("SQL parse error: %v", err))
				return
			}

			ops := make([]map[string]interface{}, len(batchPayload.Operations))
			for i, op := range batchPayload.Operations {
				operation := map[string]interface{}{
					"modelName": op.ModelName,
				}
				query := ""
				if i < len(body.Queries) {
					query = body.Queries[i]
				}
				if op.Data != nil {
					operation["operation"] = "create"
					operation["data"] = op.Data
				} else if op.Updates != nil {
					operation["operation"] = "update"
					operation["updates"] = op.Updates
					if op.Conditions != nil {
						operation["conditions"] = op.Conditions
					}
				} else if op.Data == nil && op.Updates == nil && op.Conditions != nil && strings.HasPrefix(strings.ToLower(strings.TrimSpace(query)), "delete") {
					operation["operation"] = "delete"
					operation["conditions"] = op.Conditions
				} else {
					operation["operation"] = "select"
					if op.Conditions != nil {
						operation["conditions"] = op.Conditions
					}
				}
				ops[i] = operation
			}

			response = map[string]interface{}{
				"message":    "Batch queries converted successfully",
				"operations": ops,
			}
		}

		// Response includes parsed request structure
		ctx = context.WithValue(ctx, "resData", response)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// EncodeJSONBodyCont Encodes JSON
func EncodeJSONBodyCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusInternalServerError)
		ctx := r.Context()

		var body struct {
			Data json.RawMessage `json:"data" validate:"required"`
		}

		if err := DecodeAndValidate(r, &body); err != nil {
			errorHandler.CustomError(w, http.StatusBadRequest, fmt.Sprintf("invalid request body: %v", err))
			return
		}

		encoded, err := EncodeJSONBody(body.Data)
		if err != nil {
			errorHandler.CustomError(w, http.StatusInternalServerError, fmt.Sprintf("failed to encode JSON: %v", err))
			return
		}

		ctx = context.WithValue(ctx, "resData", map[string]any{
			"message": "JSON encoded successfully",
			"encoded": encoded,
		})
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

// DeleteCont Delete Controller
func DeleteCont(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		defer errorHandler.Recovery(w, r, http.StatusInternalServerError)

		var body struct {
			ModelName     string                 `json:"modelName" validate:"required"`
			Conditions    map[string]interface{} `json:"conditions" validate:"required"`
			ApproverEmail *string                `json:"approverEmail,omitempty"`
		}

		if err := DecodeAndValidate(r, &body); err != nil {
			errorHandler.CustomError(w, http.StatusBadRequest, "failed to decode JSON body: "+err.Error())
			return
		}

		if err := auth.CheckModelAccess(r.Context(), body.ModelName, constants.OpTypeDelete); err != nil {
			errorHandler.CustomError(w, http.StatusForbidden, "Unauthorized for model "+body.ModelName)
			return
		}

		ctx := r.Context()
		adminEmail, _ := auth.GetAdminEmail(ctx)
		if adminEmail == "" {
			errorHandler.CustomError(w, http.StatusForbidden, "Missing admin email in context")
			return
		}

		// Use OOP Operation type
		op := &operations.DeleteOperation{ModelName: body.ModelName, Conditions: body.Conditions}
		if authentication.IsSuperAdmin(ctx) {
			repo := gorm.GetRepository()
			var result interface{}
			err := repo.DB().Transaction(func(tx *gormio.DB) error {
				var execErr error
				result, execErr = op.Run(ctx, tx)
				return execErr
			})
			if err != nil {
				errorHandler.CustomError(w, http.StatusInternalServerError, "Failed to delete record: "+err.Error())
				return
			}
			ctx = context.WithValue(ctx, "resData", map[string]interface{}{
				"message": "Record deleted by super admin.",
				"result":  result,
			})
			next.ServeHTTP(w, r.WithContext(ctx))
			return
		}

		// Submit for approval
		approvalBody := map[string]interface{}{
			"operation":     constants.OpTypeDelete,
			"modelName":     body.ModelName,
			"data":          nil,
			"conditions":    body.Conditions,
			"approverEmail": body.ApproverEmail,
		}
		approvalReq, _ := json.Marshal(approvalBody)
		r2 := r.Clone(r.Context())
		r2.Body = NopCloserFromBytes(approvalReq)
		SubmitApprovalRequestCont(next).ServeHTTP(w, r2)
	})
}
