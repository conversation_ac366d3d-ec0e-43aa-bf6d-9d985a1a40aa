package general

import (
	"context"
	"errors"
	"finbox/go-api/constants"
	"fmt"
	"math"
	"net/http"
	"net/http/httptest"
	"net/url"
	"reflect"
	"regexp"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestRandomIntInRange(t *testing.T) {
	type testStruct struct {
		arg1, arg2    int
		errorExpected bool
	}
	var testCases = []testStruct{
		{-23, 5, true},
		{0, 0, true},
		{-45, 0, true},
		{0, 23, true},
		{5, 230, false},
		{10_00_000, 99_99_999, false},
		{-56, -23, true},
		{-23, -56, true},
		{22, 12, true},
	}
	for index, test := range testCases {
		output, err := RandomIntInRange(test.arg1, test.arg2)
		if test.errorExpected {
			if err == nil {
				t.<PERSON>rrorf("Case %d: Error is expected for this case %d, %d", index+1, test.arg1, test.arg2)
			}
		} else {
			// non error expected cases
			if output < test.arg1 {
				t.<PERSON><PERSON>("Case %d: Output %d is less than %d", index+1, output, test.arg1)
			}
			if output > test.arg2 {
				t.Errorf("Case %d: Output %d is more than %d", index+1, output, test.arg2)
			}
		}
	}
}

func TestGetUUID(t *testing.T) {
	generated := GetUUID()
	if !ValidateUUID(generated) {
		t.Errorf("Output %v is not a valid uuid", generated)
	}
}

func TestCountTrue(t *testing.T) {
	type testStruct struct {
		args     []bool
		expected int
	}
	var testCases = []testStruct{
		{[]bool{}, 0},
		{[]bool{false}, 0},
		{[]bool{true}, 1},
		{[]bool{true, false}, 1},
		{[]bool{false, true}, 1},
		{[]bool{false, false}, 0},
		{[]bool{false, true, false}, 1},
		{[]bool{false, true, true}, 2},
		{[]bool{true, true, true}, 3},
	}
	for index, test := range testCases {
		if output := CountTrue(test.args...); output != test.expected {
			t.Errorf("Case %d: Output %v not equal to expected %v", index+1, output, test.expected)
		}
	}
}

func TestGenerateRandomString(t *testing.T) {
	regexTemplate := "[0-9a-zA-Z]{%d}"
	for count := 0; count <= 10; count++ {
		generated := GenerateRandomString(count)
		regexString := fmt.Sprintf(regexTemplate, count)
		compiledRegex := regexp.MustCompile(regexString)
		if !compiledRegex.MatchString(generated) {
			t.Errorf("Case %d: Output: %s doesn't satisfy regex %s", count, generated, regexString)
		}
	}
}

func TestInMapKeyStrValStr(t *testing.T) {
	type testStruct struct {
		arg1     string
		arg2     map[string]string
		expected bool
	}
	var testCases = []testStruct{
		{"", map[string]string{}, false},
		{"hello", map[string]string{}, false},
		{"", map[string]string{"": "abc"}, true},
		{"hello", map[string]string{"": "abc"}, false},
		{"hello", map[string]string{"hello": "abc"}, true},
		{"world", map[string]string{"hello": "def"}, false},
		{"hello", map[string]string{"hello": "false", "FinBox": "false"}, true},
		{"world", map[string]string{"hello": "false", "FinBox": "false"}, false},
		{"FinBox", map[string]string{"hello": "true", "FinBox": "true", "from": "true", "dev": "true"}, true},
		{"finbox", map[string]string{"hello": "true", "FinBox": "true", "from": "true", "dev": "true"}, false}, // case mismatch
	}
	for index, test := range testCases {
		if output := InMapKeyStrValStr(test.arg1, test.arg2); output != test.expected {
			t.Errorf("Case %d: Output %v not equal to expected %v", index+1, output, test.expected)
		}
	}
}

func TestGetValueFromMap(t *testing.T) {
	tests := []struct {
		name           string
		mapData        map[string]interface{}
		key            string
		defaultValue   interface{}
		expectedResult interface{}
	}{
		{
			name:           "Key_exists_with_matching_type",
			mapData:        map[string]interface{}{"amount": 100.5},
			key:            "amount",
			defaultValue:   0.0,
			expectedResult: 100.5,
		},
		{
			name:           "Key_exists_with_different_type_int_to_float64",
			mapData:        map[string]interface{}{"amount": 100.0},
			key:            "amount",
			defaultValue:   0.0,
			expectedResult: 100.0, // Ensure conversion is handled outside the function if necessary
		},
		{
			name:           "Key_exists_with_type_match_for_int",
			mapData:        map[string]interface{}{"age": 25},
			key:            "age",
			defaultValue:   0,
			expectedResult: 25,
		},
		{
			name:           "Key_exists_with_type_match_for_float64",
			mapData:        map[string]interface{}{"amount": 100.5},
			key:            "amount",
			defaultValue:   0.0,
			expectedResult: 100.5,
		},
		{
			name:           "Key_does_not_exist",
			mapData:        map[string]interface{}{"age": 25},
			key:            "salary",
			defaultValue:   0,
			expectedResult: 0,
		},
		{
			name:           "Key_exists_with_type_match_for_string",
			mapData:        map[string]interface{}{"name": "John"},
			key:            "name",
			defaultValue:   "",
			expectedResult: "John",
		},
		{
			name:           "Key_exists_with_different_type_float64_to_int",
			mapData:        map[string]interface{}{"amount": 100.5},
			key:            "amount",
			defaultValue:   0.0,
			expectedResult: 100.5,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := GetValueFromMap(tt.mapData, tt.key, tt.defaultValue)
			if result != tt.expectedResult {
				t.Errorf("expected %v, got %v", tt.expectedResult, result)
			}
		})
	}
}

func TestInArr(t *testing.T) {
	// tests
	type testStruct[T string | int | int16 | int32 | int64 | bool] struct {
		arg1     T
		arg2     []T
		expected bool
	}
	var testStrCases = []testStruct[string]{
		{"", []string{}, false},
		{"hello", []string{}, false},
		{"", []string{""}, true},
		{"hello", []string{""}, false},
		{"hello", []string{"hello"}, true},
		{"world", []string{"hello"}, false},
		{"hello", []string{"hello", "FinBox"}, true},
		{"world", []string{"hello", "FinBox"}, false},
		{"FinBox", []string{"hello", "FinBox", "from", "dev"}, true},
		{"finbox", []string{"hello", "FinBox", "from", "dev"}, false}, // case mismatch
	}
	for index, test := range testStrCases {
		if output := InArr(test.arg1, test.arg2); output != test.expected {
			t.Errorf("Case string - %d: Output %v not equal to expected %v", index+1, output, test.expected)
		}
	}
	var testIntCases = []testStruct[int]{
		{0, []int{}, false},
		{23, []int{}, false},
		{0, []int{0}, true},
		{45, []int{0}, false},
		{65, []int{65}, true},
		{123, []int{45}, false},
		{-12, []int{-12, 25}, true},
		{2352, []int{12, -233}, false},
		{2352, []int{12312, 2352, -23, 123}, true},
		{2352, []int{12312, -2352, -23, 123}, false},
	}
	for index, test := range testIntCases {
		if output := InArr(test.arg1, test.arg2); output != test.expected {
			t.Errorf("Case int - %d: Output %v not equal to expected %v", index+1, output, test.expected)
		}
	}
	var testInt16Cases = []testStruct[int16]{
		{0, []int16{}, false},
		{23, []int16{}, false},
		{0, []int16{0}, true},
		{45, []int16{0}, false},
		{65, []int16{65}, true},
		{123, []int16{45}, false},
		{-12, []int16{-12, 25}, true},
		{2352, []int16{12, -233}, false},
		{2352, []int16{12312, 2352, -23, 123}, true},
		{2352, []int16{12312, -2352, -23, 123}, false},
	}
	for index, test := range testInt16Cases {
		if output := InArr(test.arg1, test.arg2); output != test.expected {
			t.Errorf("Case int16 - %d: Output %v not equal to expected %v", index+1, output, test.expected)
		}
	}
	var testInt32Cases = []testStruct[int32]{
		{0, []int32{}, false},
		{23, []int32{}, false},
		{0, []int32{0}, true},
		{45, []int32{0}, false},
		{65, []int32{65}, true},
		{123, []int32{45}, false},
		{-12, []int32{-12, 25}, true},
		{2352, []int32{12, -233}, false},
		{2352, []int32{12312, 2352, -23, 123}, true},
		{2352, []int32{12312, -2352, -23, 123}, false},
	}
	for index, test := range testInt32Cases {
		if output := InArr(test.arg1, test.arg2); output != test.expected {
			t.Errorf("Case int32 - %d: Output %v not equal to expected %v", index+1, output, test.expected)
		}
	}
	var testInt64Cases = []testStruct[int64]{
		{0, []int64{}, false},
		{23, []int64{}, false},
		{0, []int64{0}, true},
		{45, []int64{0}, false},
		{65, []int64{65}, true},
		{123, []int64{45}, false},
		{-12, []int64{-12, 25}, true},
		{2352, []int64{12, -233}, false},
		{2352, []int64{12312, 2352, -23, 123}, true},
		{2352, []int64{12312, -2352, -23, 123}, false},
	}
	for index, test := range testInt64Cases {
		if output := InArr(test.arg1, test.arg2); output != test.expected {
			t.Errorf("Case int64 - %d: Output %v not equal to expected %v", index+1, output, test.expected)
		}
	}
	var testBoolCases = []testStruct[bool]{
		{true, []bool{}, false},
		{false, []bool{}, false},
		{false, []bool{false}, true},
		{false, []bool{true}, false},
		{true, []bool{false}, false},
		{true, []bool{true}, true},
		{true, []bool{true, false}, true},
		{false, []bool{false, true}, true},
		{false, []bool{false, true, false}, true},
		{false, []bool{true, true, true}, false},
	}
	for index, test := range testBoolCases {
		if output := InArr(test.arg1, test.arg2); output != test.expected {
			t.Errorf("Case bool - %d: Output %v not equal to expected %v", index+1, output, test.expected)
		}
	}
}

func TestMin(t *testing.T) {
	// tests
	type testStruct[T int | int16 | int32 | int64 | float32 | float64] struct {
		arg1     T
		arg2     T
		expected T
	}

	var testIntCases = []testStruct[int]{
		{23, 45, 23},
		{230, 450, 230},
		{2300, 4500, 2300},
		{23000, 45000, 23000},
		{45, 23, 23},
		{450, 230, 230},
		{4500, 2300, 2300},
		{45000, 23000, 23000},
	}
	for index, test := range testIntCases {
		if output := Min(test.arg1, test.arg2); output != test.expected {
			t.Errorf("Case int - %d: Output %v not equal to expected %v", index+1, output, test.expected)
		}
	}
	var testInt16Cases = []testStruct[int16]{
		{23, 45, 23},
		{230, 450, 230},
		{2300, 4500, 2300},
	}
	for index, test := range testInt16Cases {
		if output := Min(test.arg1, test.arg2); output != test.expected {
			t.Errorf("Case int16 - %d: Output %v not equal to expected %v", index+1, output, test.expected)
		}
	}
	var testInt32Cases = []testStruct[int32]{
		{23, 45, 23},
		{230, 450, 230},
		{2300, 4500, 2300},
		{-23000, 45000, -23000},
		{-230000, 450000, -230000},
		{230000000, 450000000, 230000000},
	}
	for index, test := range testInt32Cases {
		if output := Min(test.arg1, test.arg2); output != test.expected {
			t.Errorf("Case int32 - %d: Output %v not equal to expected %v", index+1, output, test.expected)
		}
	}
	var testInt64Cases = []testStruct[int64]{
		{23, 45, 23},
		{230, 450, 230},
		{2300, 4500, 2300},
		{-23000, 45000, -23000},
		{-230000, 450000, -230000},
		{230000000, 450000000, 230000000},
		{230000000000, 450000000000, 230000000000},
	}
	for index, test := range testInt64Cases {
		if output := Min(test.arg1, test.arg2); output != test.expected {
			t.Errorf("Case int64 - %d: Output %v not equal to expected %v", index+1, output, test.expected)
		}
	}
	var testFloat32Cases = []testStruct[float32]{
		{23.5, 45.9, 23.5},
		{230.1, 450.1, 230.1},
		{230.1, 230.2, 230.1},
	}
	for index, test := range testFloat32Cases {
		if output := Min(test.arg1, test.arg2); output != test.expected {
			t.Errorf("Case float32 - %d: Output %v not equal to expected %v", index+1, output, test.expected)
		}
	}
	var testFloat64Cases = []testStruct[float64]{
		{23.5, 45.9, 23.5},
		{230.1, 450.1, 230.1},
		{230.1, 230.2, 230.1},
		{23.555555, 23.555556, 23.555555},
		{23.55555558, 23.55555559, 23.55555558},
	}
	for index, test := range testFloat64Cases {
		if output := Min(test.arg1, test.arg2); output != test.expected {
			t.Errorf("Case float64 - %d: Output %v not equal to expected %v", index+1, output, test.expected)
		}
	}
}

func TestRemoveExtraSpaces(t *testing.T) {
	type testStruct struct {
		arg      string
		expected string
	}
	var testCases = []testStruct{
		{"", ""},
		{" abc def", "abc def"},
		{" abc def ", "abc def"},
		{"abc def ", "abc def"},
		{"  abc   def ", "abc def"},
		{"  abc		def ", "abc def"},
		{"  abc ", "abc"},
		{"  abc def    gh2    ", "abc def gh2"},
	}
	for index, test := range testCases {
		if output := RemoveExtraSpaces(test.arg); output != test.expected {
			t.Errorf("Case %d: Output %v not equal to expected %v", index+1, output, test.expected)
		}
	}
}

func TestRemoveAllSpaces(t *testing.T) {
	type testStruct struct {
		arg      string
		expected string
	}
	var testCases = []testStruct{
		{"", ""},
		{" abc def", "abcdef"},
		{" abc def ", "abcdef"},
		{"abc def ", "abcdef"},
		{"  abc   def ", "abcdef"},
		{"  abc		def ", "abcdef"},
		{"  abc ", "abc"},
		{"  abc def    gh2    ", "abcdefgh2"},
	}
	for index, test := range testCases {
		if output := RemoveAllSpaces(test.arg); output != test.expected {
			t.Errorf("Case %d: Output %v not equal to expected %v", index+1, output, test.expected)
		}
	}
}

func TestRemoveEmptyElements(t *testing.T) {
	type testStruct struct {
		arg      []string
		expected []string
	}
	var testCases = []testStruct{
		{[]string{}, []string{}},
		{[]string{""}, []string{}},
		{[]string{"", "abc def", ""}, []string{"abc def"}},
		{[]string{"", "abc def", "", "def ghi 123 $", ""}, []string{"abc def", "def ghi 123 $"}},
	}
	for index, test := range testCases {
		output := RemoveEmptyElements(test.arg)
		if len(output) == len(test.expected) {
			if len(output) > 0 && !reflect.DeepEqual(output, test.expected) { // since its equal in length, any one can be checked
				// do deep equal check only if slices are non-empty
				t.Errorf("Case %d: Output %v not equal to expected %v", index+1, output, test.expected)
			}
		} else {
			t.Errorf("Case %d: Output %v not equal to expected %v", index+1, output, test.expected)
		}
	}
}

func TestRemoveDuplicatesAndEmpty(t *testing.T) {
	type testStruct struct {
		arg      []string
		expected []string
	}
	var testCases = []testStruct{
		{[]string{}, []string{}},
		{[]string{""}, []string{}},
		{[]string{"", "abc def", ""}, []string{"abc def"}},
		{[]string{"", "abc def", "", "def ghi 123 $", ""}, []string{"abc def", "def ghi 123 $"}},
		{[]string{"", "abc def", "", "abc def", ""}, []string{"abc def"}},
		{[]string{"", "abc def", "", "abc def    ", ""}, []string{"abc def"}},
		{[]string{"", "abc def", "", "abc def$", "abc def"}, []string{"abc def", "abc def$"}},
	}
	for index, test := range testCases {
		output := RemoveDuplicatesAndEmpty(test.arg)
		if len(output) == len(test.expected) {
			if len(output) > 0 && !reflect.DeepEqual(output, test.expected) { // since its equal in length, any one can be checked
				// do deep equal check only if slices are non-empty
				t.Errorf("Case %d: Output %v not equal to expected %v", index+1, output, test.expected)
			}
		} else {
			t.Errorf("Case %d: Output %v not equal to expected %v", index+1, output, test.expected)
		}
	}
}

func TestRemoveElement(t *testing.T) {
	type testStruct[T comparable] struct {
		arg1     T
		arg2     []T
		expected []T
	}
	testCases := []testStruct[string]{
		{"", []string{}, []string{}},
		{"a", []string{}, []string{}},
		{"a", []string{"a"}, []string{}},
		{"a", []string{"a", "b", "c"}, []string{"b", "c"}},
		{"b", []string{"a", "b", "c"}, []string{"a", "c"}},
		{"c", []string{"a", "b", "c"}, []string{"a", "b"}},
		{"d", []string{"a", "b", "c"}, []string{"a", "b", "c"}},
	}
	for index, test := range testCases {
		if output := RemoveElement(test.arg1, test.arg2); !reflect.DeepEqual(output, test.expected) {
			t.Errorf("Case %d: Output %v not equal to expected %v\n", index+1, output, test.expected)
		}
	}
}

func TestCleanAddressLine(t *testing.T) {
	type testStruct struct {
		arg      string
		expected string
	}
	var testCases = []testStruct{
		{"", ""},
		{"a-zA-Z-,.   @#()/01239	\n", "a-zA-Z-,. @#()/01239"},
		{"a-zA-Z-,.   नमूना पता@#()/01239	\n", "a-zA-Z-,. @#()/01239"},
	}
	for index, test := range testCases {
		if output := CleanAddressLine(test.arg); output != test.expected {
			t.Errorf("Case %d: Output %v not equal to expected %v", index+1, output, test.expected)
		}
	}
}

func TestCleanColumn(t *testing.T) {
	type testStruct struct {
		arg      string
		expected string
	}
	var testCases = []testStruct{
		{"", ""},
		{"a-zA-Z-,._   @#()/01239	\n", "a-zA-Z-_01239"},
		{"a-zA-Z-,._   नमूना पता@#()/01239	\n", "a-zA-Z-_01239"},
	}
	for index, test := range testCases {
		if output := CleanColumn(test.arg); output != test.expected {
			t.Errorf("Case %d: Output %v not equal to expected %v", index+1, output, test.expected)
		}
	}
}

func TestKeepOnlyAlphaSpace(t *testing.T) {
	type testStruct struct {
		arg      string
		expected string
	}
	var testCases = []testStruct{
		{"", ""},
		{"a-z   A-Z-,._   @#()/01239	\n", "az AZ"},
		{"a-z   A-Z-,._   नमूना पता@#()/01239	\n", "az AZ"},
		{`
		Shourya Pratap Singh`, "Shourya Pratap Singh"},
	}
	for index, test := range testCases {
		if output := KeepOnlyAlphaSpace(test.arg); output != test.expected {
			t.Errorf("Case %d: Output %v not equal to expected %v", index+1, output, test.expected)
		}
	}
}

func TestGetOnlyAlphaNumUpper(t *testing.T) {
	type testStruct struct {
		arg      string
		expected string
	}
	var testCases = []testStruct{
		{"", ""},
		{"a-z   A-Z-,._   @#()/01239	\n", "AZAZ01239"},
		{"a-z   A-Z-,._   नमूना पता@#()/01239	\n", "AZAZ01239"},
	}
	for index, test := range testCases {
		if output := GetOnlyAlphaNumUpper(test.arg); output != test.expected {
			t.Errorf("Case %d: Output %v not equal to expected %v", index+1, output, test.expected)
		}
	}
}

func TestGetOnlyAlphaNumSpace(t *testing.T) {
	type testStruct struct {
		arg      string
		expected string
	}
	var testCases = []testStruct{
		{"", ""},
		{"a-z   A-Z-,._   @#()/01239	\n", "az AZ 01239"},
		{"a-z   A-Z-,._   नमूना पता@#()/01239	\n", "az AZ 01239"},
	}
	for index, test := range testCases {
		if output := GetOnlyAlphaNumSpace(test.arg); output != test.expected {
			t.Errorf("Case %d: Output %v not equal to expected %v", index+1, output, test.expected)
		}
	}
}

func TestGetOnlyNumSpace(t *testing.T) {
	type testStruct struct {
		arg      string
		expected string
	}
	var testCases = []testStruct{
		{"", ""},
		{"a-z   A-Z-,._   @#()/01239	\n", "01239"},
		{"a-z   A-Z-,._   नमूना पता@#()/01239	\n", "01239"},
		{"9920941512\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t\t\t\t", "9920941512"},
	}
	for index, test := range testCases {
		if output := GetOnlyNum(test.arg); output != test.expected {
			t.Errorf("Case %d: Output %v not equal to expected %v", index+1, output, test.expected)
		}
	}
}

func TestGetOnlyAlphaSpace(t *testing.T) {
	type testStruct struct {
		arg      string
		expected string
	}
	var testCases = []testStruct{
		{"", ""},
		{"a-z   A-Z-,._   @#()/01239	\n", "az AZ"},
		{"a-z   A-Z-,._   नमूना पता@#()/01239	\n", "az AZ"},
	}
	for index, test := range testCases {
		if output := GetOnlyAlphaSpace(test.arg); output != test.expected {
			t.Errorf("Case %d: Output %v not equal to expected %v", index+1, output, test.expected)
		}
	}
}

func TestRemoveCountryCode(t *testing.T) {
	type testStruct struct {
		arg      string
		expected string
	}
	var testCases = []testStruct{
		{"", ""},
		{"+91 91919 19191", "9191919191"},
		{"+91 9191919191", "9191919191"},
		{"+91 9191 919 191", "9191919191"},
	}
	for index, test := range testCases {
		if output := RemoveCountryCode(test.arg); output != test.expected {
			t.Errorf("Case %d: Output %v not equal to expected %v", index+1, output, test.expected)
		}
	}
}

func TestGetStringFromTemplate(t *testing.T) {
	type testStruct struct {
		arg1     string
		arg2     map[string]interface{}
		expected string
	}
	var testCases = []testStruct{
		{"{{.Something}} is the placeholder replaced", map[string]interface{}{
			"Something": "hello world",
		}, "hello world is the placeholder replaced"},
		{"{{.Something}} is the placeholder replaced", map[string]interface{}{
			"Something": "<script>alert('1')</script>",
		}, "<script>alert('1')</script> is the placeholder replaced"}, // not HTML safe, as not escaping
		{"{{.Something}} is the placeholder replaced", map[string]interface{}{
			"Something": "hello world",
			"Other":     "ignored value",
		}, "hello world is the placeholder replaced"},
		{"{{.Something}} is the placeholder replaced", map[string]interface{}{
			"something": "hello world",
		}, "<no value> is the placeholder replaced"},
		{"", nil, ""},
		{"", map[string]interface{}{
			"something": "hello world",
		}, ""},
		{"{{.3Something}}}", map[string]interface{}{"Something": "hello world"}, ""}, // error in parse case
	}
	for index, test := range testCases {
		if output := GetStringFromTemplate(test.arg1, test.arg2); output != test.expected {
			t.Errorf("Case %d: Output %v not equal to expected %v", index+1, output, test.expected)
		}
	}
}

func TestGetHTMLFromTemplate(t *testing.T) {
	type testStruct struct {
		arg1     string
		arg2     map[string]interface{}
		expected string
	}
	var testCases = []testStruct{
		{"{{.Something}} is the placeholder replaced", map[string]interface{}{
			"Something": "hello world",
		}, "hello world is the placeholder replaced"},
		{"{{.Something }} is the placeholder replaced", map[string]interface{}{
			"Something": "https://finbox.in/PrivacyPolicy/",
		}, "https://finbox.in/PrivacyPolicy/ is the placeholder replaced"}, // URL should not get escaped
		{"{{.Something}} is the placeholder replaced", map[string]interface{}{
			"Something": "<script>alert('1')</script>",
		}, "&lt;script&gt;alert(&#39;1&#39;)&lt;/script&gt; is the placeholder replaced"}, // HTML safe
		{"{{.Something}} is the placeholder replaced", map[string]interface{}{
			"Something": "hello world",
			"Other":     "ignored value",
		}, "hello world is the placeholder replaced"},
		{"{{.Something}} is the placeholder replaced", map[string]interface{}{
			"something": "hello world",
		}, " is the placeholder replaced"},
		{"", nil, ""},
		{"", map[string]interface{}{
			"something": "hello world",
		}, ""},
		{"{{.3Something}}}", map[string]interface{}{"Something": "hello world"}, ""}, // error in parse case
	}
	for index, test := range testCases {
		if output := GetHTMLFromTemplate(test.arg1, test.arg2); output != test.expected {
			t.Errorf("Case %d: Output %v not equal to expected %v", index+1, output, test.expected)
		}
	}
}

func TestIsNumber(t *testing.T) {
	type testStruct struct {
		arg      string
		expected bool
	}
	var testCases = []testStruct{
		{"ABC123", false},
		{"ABC 123", false},
		{"ABCDEF", false},
		{"123 345", false},
		{"123345", true},
		{"-123345", false},
		{"-1233.45", false},
		{"-1233.45", false},
		{"- 123345", false},
		{"0", true},
		{"000", true},
	}
	for index, test := range testCases {
		if output := IsNumber(test.arg); output != test.expected {
			t.Errorf("Case %d: Output %v not equal to expected %v", index+1, output, test.expected)
		}
	}
}

func TestGetTimeStampString(t *testing.T) {
	expectedFormat := "2006-01-02 15:04:05.000000"
	for i := 0; i < 10; i++ {
		// generate 10 timestamps and see if format is correct
		output := GetTimeStampString()
		_, err := time.Parse(expectedFormat, output)
		if err != nil {
			t.Errorf("Case %d: Output %v not in expected format", i+1, output)
		}
	}
}

func TestGetTimeStampPair(t *testing.T) {
	expectedFormat := "2006-01-02 15:04:05.000000"
	for i := 0; i < 10; i++ {
		// generate 10 timestamps and see if format is correct for string
		outputDt, outputStr := GetTimeStampPair()
		_, err := time.Parse(expectedFormat, outputStr)
		if err != nil {
			t.Errorf("Case %d: Output %v not in expected format", i+1, outputStr)
		}
		// match output date converted in string format with string returned
		outputConverted := outputDt.Format(expectedFormat)
		if outputConverted != outputStr {
			t.Errorf("Case %d: 1st output date in string format %v not matching with second output string %v", i+1, outputConverted, outputStr)
		}
	}
}

func TestValidatePincode(t *testing.T) {
	type testStruct struct {
		arg      string
		expected bool
	}
	var testCases = []testStruct{
		{"", false},
		{"560068", true},
		{"462022", true},
		{"1560068", false},
		{"-1560068", false},
		{"56.0068", false},
	}
	for index, test := range testCases {
		if output := ValidatePincode(test.arg); output != test.expected {
			t.Errorf("Case %d: Output %v not equal to expected %v", index+1, output, test.expected)
		}
	}
}

func TestValidateGSTIN(t *testing.T) {
	type testStruct struct {
		arg      string
		expected bool
	}
	var testCases = []testStruct{
		{"", false},
		{"06AAJCM8593L1ZO", true},
		{"06AAJCM8593L1A1", false},
		{"ABC123", false},
		{"0BAAJCM8593L1ZB", false},
	}
	for index, test := range testCases {
		if output := ValidateGSTIN(test.arg); output != test.expected {
			t.Errorf("Case %d: Output %v not equal to expected %v", index+1, output, test.expected)
		}
	}
}

func TestGetJSONFromTemplate(t *testing.T) {
	type testStruct struct {
		arg1     string
		arg2     map[string]interface{}
		expected string
	}
	var testCases = []testStruct{
		{"{{.Something}} is the placeholder replaced", map[string]interface{}{
			"Something": "hello world",
		}, "hello world is the placeholder replaced"},
		{"{{.Something}} is the placeholder replaced", map[string]interface{}{
			"Something": "<script>alert('1')</script>",
		}, "<script>alert('1')</script> is the placeholder replaced"}, // not HTML safe, as not escaping
		{"{{.Something}} is the placeholder replaced", map[string]interface{}{
			"Something": "hello world",
			"Other":     "ignored value",
		}, "hello world is the placeholder replaced"},
		{"{{.Something}} is the placeholder replaced", map[string]interface{}{
			"something": "hello world",
		}, "<no value> is the placeholder replaced"},
		{"", nil, ""},
		{"", map[string]interface{}{
			"something": "hello world",
		}, ""},
		{"{{.3Something}}}", map[string]interface{}{"Something": "hello world"}, ""}, // error in parse case
	}
	for index, test := range testCases {
		if output, _ := GetJSONFromTemplate(test.arg1, test.arg2); output != test.expected {
			t.Errorf("Case %d: Output %v not equal to expected %v", index+1, output, test.expected)
		}
	}
}

func TestValidateCINLLPIN(t *testing.T) {
	type testStruct struct {
		arg      string
		expected bool
	}
	var testCases = []testStruct{
		{"", false},
		{"U17120MH2008PLC180384", true},
		{"u17120MH2008PLC180384", false},
		{"ABC123", false},
	}
	for index, test := range testCases {
		if output := ValidateCINLLPIN(test.arg); output != test.expected {
			t.Errorf("Case %d: Output %v not equal to expected %v", index+1, output, test.expected)
		}
	}
}

func TestValidatePAN(t *testing.T) {
	type testStruct struct {
		arg      string
		expected bool
	}
	var testCases = []testStruct{
		{"", false},
		{"*********0", false},
		{"**********", true},
		{"**********", true},
	}
	for index, test := range testCases {
		if output := ValidatePAN(test.arg); output != test.expected {
			t.Errorf("Case %d: Output %v not equal to expected %v", index+1, output, test.expected)
		}
	}
}

func TestValidatePersonalPAN(t *testing.T) {
	type testStruct struct {
		arg      string
		expected bool
	}
	var testCases = []testStruct{
		{"", false},
		{"*********0", false},
		{"**********", true},
		{"**********", false},
	}
	for index, test := range testCases {
		if output := ValidatePersonalPAN(test.arg); output != test.expected {
			t.Errorf("Case %d: Output %v not equal to expected %v", index+1, output, test.expected)
		}
	}
}

func TestValidateNonPersonalPAN(t *testing.T) {
	type testStruct struct {
		arg      string
		expected bool
	}
	var testCases = []testStruct{
		{"", false},
		{"*********0", false},
		{"**********", false},
		{"**********", true},
	}
	for index, test := range testCases {
		if output := ValidateNonPersonalPAN(test.arg); output != test.expected {
			t.Errorf("Case %d: Output %v not equal to expected %v", index+1, output, test.expected)
		}
	}
}

func TestValidateEmail(t *testing.T) {
	type testStruct struct {
		arg1     string
		expected bool
	}
	var testCases = []testStruct{
		{"", false},
		{"<EMAIL>", true},
		{"email@123.123.123.123", true},
		{"++@abc.com", false},
		{".+@abc.com", false},
		{"<EMAIL>", false},
		{"<EMAIL>", false},
		{"<EMAIL>", true},
		{"<EMAIL>", true},
		{"#@%^%#$@#$@#.com", false},
		{".<EMAIL>", false},
		{"<EMAIL> (Joe Smith)", false},
		{"<EMAIL>", false},
		{"just\"not\"<EMAIL>", false},
		{"this\\ is\"really\"not\\<EMAIL>", false},
		{"<EMAIL>", true},
		{"abc@gmailcom", false},
		{"<EMAIL>", true},
		{"<EMAIL>", true},
		{"<EMAIL>", false},
		{"test23@sd+.com", false},
		{"test23@+sdsdf.com", false},
		{"test23@sds+df.com", false},
		{"test23@sds+df.com", false},
		{"+<EMAIL>", false},
		{"<EMAIL>", false},
	}
	for index, test := range testCases {
		if output := ValidateEmail(test.arg1); output != test.expected {
			t.Errorf("Case %d: Output %v not equal to expected %v", index+1, output, test.expected)
		}
	}
}

func TestValidateURL(t *testing.T) {
	type testStruct struct {
		arg1     string
		expected bool
	}
	var testCases = []testStruct{
		{"", false},
		{" ", false},
		{"https://finbox.in", true},
		{"https://www.finbox.in", true},
		{"http://finbox.in", true},
		{"finbox.in", false},     // without protocol is invalid
		{"www.finbox.in", false}, // without protocol is invalid
		{"finbox", false},
		{"https://www.guru99.com", true},
		{"https://www.in", true},
		{"https://finbox@.in", true},
		{"https://fin box.in", false},
		{"https://23348.com", true},
		{"https://finbox-cdn.s3.ap-south-1.amazonaws.com/assets/dashboard/finbox.svg", true},
		{"https://finbox-cdn.s3.ap-south-1.amazonaws.com/assets/finbox-logo.svg?response-content-disposition=inline&X-Amz-Security-Token=IQoJb3JpZ2luX2VjEHkaCmFwLXNvdXRoLTEiSDBGAiEAjWNrzHkGV59kcWHi8eqXXJwWEoJJf2deMbUMGD5UaBgCIQC9apyoR3LopZ52NhYEZWyaf7%2FZ5saGGRqor1IUtOwY1CqIAwii%2F%2F%2F%2F%2F%2F%2F%2F%2F%2F8BEAIaDDY2MTkwMDU2NDIwOSIMWJUoZiGqiRv2F6exKtwCoHSPQ5KC%2FRbsHXq6tS5RZcaheLLij9WtK33ehas0qyPnIeoP%2F5Dh6%2BoVVqzzFek5HDsyqE0Wja%2B%2BUzIf2oFHTZi1GSueBQI5tZLvcpg%2BIPIAC%2FBUTFsG2%2FwkwJ%2FlzK0e2B1nmevuDTEfFG0mRy7wLfwQty48k2P7fpfxwBxWE2FOfSnrleefH2e%2FA00CsrngKFm2wXLMWdPdHUbNbhtjlmrdHApu9pTNm0s271EKif7InrlzUSZU88Eic5rhFNoitzTWkrMTR8kr9a85zkZt0iH%2BOIe76BTwHcW1R%2F2Ztb0h1nPz5T2o94COthTlyS8TsN1%2B7DF6Zr4pRhNaC4yQIJ81%2FFuUnvPSzjd9g27TahKUEvw1TlvtV7KxZny40uj4pyfoEffGhP8OVzYfbscO8FtFSAGCM7KbPw0roBlt7xQwMmZgGyo2UlR7%2FAFngwFtzDoSG158VtrAEWbwMNnCj5YGOrICOXQyMJvbtPdaImK2n9jbl92m1vTQna%2BlwMPCW%2BX1alWcmlOODV0DLGD%2BltyrBGHD5ZsHWLXfMEMzCe72ztCohsjYKWNh0P7nrEnS8tRtDKHvmFl4ME1NA9Esn%2FIDkP1FMg7HxUAzGbmNogrhQE26uRF%2BPN%2B2IPQtDfny3BTKQQ51cu4otlMm%2B2VDcAkay3Te8tZYhkJV8SKa0q1PcUaFjqIu2rimd5mTLDOPfYL731n4iE54XmMUMbdTiAU7qUhQ4yrna8qa1ZyAD%2FcozUH4q%2BKdpONxlneqKo3qxJuew9iZ7mg0ttFWXdD22LsgdPe5eO1SkR3VjsCoEPM2MdoHpiXGzmnP3FQO1ukWkzd3Ib8%2FwZC189EGqwktxDQFDU5JKr2IOfhHKGlk3NsGmOutxFhL&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Date=20220705T090736Z&X-Amz-SignedHeaders=host&X-Amz-Expires=120&X-Amz-Credential=ASIAZUHCZC3YXG7DBT57%2F20220705%2Fap-south-1%2Fs3%2Faws4_request&X-Amz-Signature=3e990d34a63513165f9aeeeb6ac0bd0dff48158738851feff0b441de384de307", true},
	}
	for index, test := range testCases {
		if output := ValidateURL(test.arg1); output != test.expected {
			t.Errorf("Case %d: Output %v not equal to expected %v", index+1, output, test.expected)
		}
	}
}

func TestGetFirstSurName(t *testing.T) {
	type testStruct struct {
		arg       string
		expected1 string
		expected2 string
	}
	var testCases = []testStruct{
		{"", "", ""},
		{"HUCHAVEERAJANNAPALYA BHUJANGABHUSHANAARADHYA DIVAKARA ARADHYA", "HUCHAVEERAJANNAPALYA BHUJANGABHUSHANAARADHYA DIVAKARA", "ARADHYA"},
		{"RAMANJANEYA NAGASATYA VENKATESWARLU GUPTA VUTUKURU HEMA KRISHNA", "RAMANJANEYA NAGASATYA VENKATESWARLU GUPTA VUTUKURU HEMA", "KRISHNA"},
		{"SHOURYA PRATAP SINGH", "SHOURYA PRATAP", "SINGH"},
		{"ROHIT SHARMA", "ROHIT", "SHARMA"},
		{"ROHIT    	SHARMA", "ROHIT", "SHARMA"},
		{"OM PANDA", "OM", "PANDA"},
		{"J. VISHVAS", "VISHVAS", "J"}, // reverse order of last and first name if single letter
		{"J.VISHVAS", "VISHVAS", "J"},
		{"J VISHVAS", "VISHVAS", "J"},
		{"J 123 VISHVAS", "VISHVAS", "J"},
		{"MUJEEB", "MUJEEB", ""},
	}
	for index, test := range testCases {
		if output1, output2 := GetFirstSurName(test.arg); !(output1 == test.expected1 && output2 == test.expected2) {
			t.Errorf("Case %d: Output %v, %v not equal to expected %v, %v", index+1, output1, output2, test.expected1, test.expected2)
		}
	}
}

func TestGetFirstMiddleLastName(t *testing.T) {
	type testStruct struct {
		arg1      string
		arg2      bool
		expected1 string
		expected2 string
		expected3 string
	}
	var testCases = []testStruct{
		{"", false, "", "", ""},
		{"HUCHAVEERAJANNAPALYA BHUJANGABHUSHANAARADHYA DIVAKARA ARADHYA", false, "HUCHAVEERAJANNAPALYA", "BHUJANGABHUSHANAARADHYA DIVAKARA", "ARADHYA"},
		{"RAMANJANEYA NAGASATYA VENKATESWARLU GUPTA VUTUKURU HEMA KRISHNA", false, "RAMANJANEYA", "NAGASATYA VENKATESWARLU GUPTA VUTUKURU HEMA", "KRISHNA"},
		{"SHOURYA PRATAP SINGH", false, "SHOURYA", "PRATAP", "SINGH"},
		{"ROHIT SHARMA", false, "ROHIT", "", "SHARMA"},
		{"ROHIT    	SHARMA", false, "ROHIT", "", "SHARMA"},
		{"OM PANDA", false, "OM", "", "PANDA"},
		{"J. VISHVAS", false, "VISHVAS", "", "J"}, // reverse order of last and first name if single letter
		{"J.VISHVAS", false, "VISHVAS", "", "J"},
		{"J VISHVAS", false, "VISHVAS", "", "J"},
		{"J 123 VISHVAS", false, "VISHVAS", "", "J"},
		{"MUJEEB", false, "MUJEEB", "", ""},
		{"J KUMAR VISHVAS", false, "KUMAR", "J", "VISHVAS"},
		{"Mr. ROHIT SHARMA", true, "rohit", "", "sharma"},
		{"ROHIT SHARMA", true, "rohit", "", "sharma"},
		{"Mr. ROHIT KUMAR SHARMA", true, "rohit", "kumar", "sharma"},
	}
	for index, test := range testCases {
		if output1, output2, output3 := GetFirstMiddleLastName(test.arg1, test.arg2); !(output1 == test.expected1 && output2 == test.expected2 && output3 == test.expected3) {
			t.Errorf("Case %d: Output %v, %v, %v not equal to expected %v, %v, %v", index+1, output1, output2, output3, test.expected1, test.expected2, test.expected3)
		}
	}
}

func TestAmountInWords(t *testing.T) {
	type testStruct struct {
		arg      float64
		expected string
	}
	var testCases = []testStruct{
		{0, "zero"},
		{000, "zero"},
		{-0, "zero"},
		{5, "five"},
		{-5, "negative five"},
		{52, "fifty-two"},
		{552, "five hundred fifty-two"},
		{1552, "one thousand five hundred fifty-two"},
		{1002, "one thousand two"},
		{1000, "one thousand"},
		{52000, "fifty-two thousand"},
		{102000, "one lakh two thousand"},
		{202000, "two lakhs two thousand"},
		{202000.23, "two lakhs two thousand and twenty-three paisa"},
		{202000.232, "two lakhs two thousand and twenty-three paisa"},
		{202000.236, "two lakhs two thousand and twenty-four paisa"}, // round if more than two decimal points present
		{1000000, "ten lakhs"},
		{2202000, "twenty-two lakhs two thousand"},
		{22_02_000, "twenty-two lakhs two thousand"}, // trying the same with underscores :)
		{10000000, "one crore"},
		{10000302, "one crore three hundred two"},
		{300000302, "thirty crores three hundred two"},
		{-300000302, "negative thirty crores three hundred two"},
		{990000000, "ninety-nine crores"},
		{-990000000, "negative ninety-nine crores"},
		{3200000302, ""},  // can't process more than 100 crore
		{-3200000302, ""}, // can't process less than -100 crore
	}
	for index, test := range testCases {
		if output := AmountInWords(test.arg); output != test.expected {
			t.Errorf("Case %d: Output %v not equal to expected %v", index+1, output, test.expected)
		}
	}
}

func TestFileFromURLtoBase64(t *testing.T) {

	// start a test server for testing failed reader case
	srv := httptest.NewServer(
		http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			// says the response is longer than what we're actually returning
			w.Header().Add("Content-Length", "50")
			// return less bytes, which will result in an "unexpected EOF" from io.ReadAll()
			_, _ = w.Write([]byte("a"))
		}),
	)
	defer srv.Close()

	type testStruct struct {
		arg      string
		expected string
	}
	var testCases = []testStruct{
		{"", ""},
		{"finbox.in", ""},                  // invalid url
		{"http://localhost:*********", ""}, // case with invalid port, http.Get will fail
		{srv.URL, ""},                      // case with response body read failing
		{"https://finbox-cdn.s3.ap-south-1.amazonaws.com/base64example.png", "iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAGJSURBVHgBzZRNUsJAEIW7m2CJbnIEPIF4AsMJwBMYt1YohoVV7AxV/rBjCss93oAj4Am8AkfIRjfgtD3RxBSBMEveKum8fNP0awJw6EJXo1IT3xwf9RE5lNsEeXWlx4Plps/bB4qGL0GNqM9gAgT2s7phT9lzwKVD2813oxYSUEccAWxXgl+rM60HSbFI25ym4WlkbArMh93yzYlX6nAr0HZFsNYMOIcKyaEdNyDAjMn7kJnFUEmElpIZ7wXS0VqLu+rn5mLE+53A6O80HcugGRbgIhmPDbEEVMNJk+g/UWQegaOK4eRAg16cXd/evbb0uLcQ7BIchID9ElCKl9l1vQ6pQZb5DdzkZ+GkQDWchkJsZk8F1LaGNByExIVoAFUONETdsgG6NhxmduqS8DdEKt4UhYTXNj0yULncDPyOxG39HOkcqB8jbYvy8qLglf9zPUzD2VwhGYMs/UjmfjF96gX6oZc/z782xSIxKEMcy8fhPH0fcWDQzAScyCLPa5+nM61vnGZ7ePoBoyWHNYs5/7cAAAAASUVORK5CYII="}, // valid url
	}
	for index, test := range testCases {
		if output := FileFromURLtoBase64(test.arg); output != test.expected {
			t.Errorf("Case %d: Output %v not equal to expected %v", index+1, output, test.expected)
		}
	}
}

func TestFormatCurrency(t *testing.T) {
	type testStruct struct {
		arg1     float64
		arg2     bool
		expected string
	}
	var testCases = []testStruct{
		{0, true, "0.00"},
		{0, false, "0"},
		{122, false, "122"},
		{122, true, "122.00"},
		{1222, false, "1,222"},
		{1222, true, "1,222.00"},
		{12222, false, "12,222"},
		{12220, true, "12,220.00"},
		{122222, false, "1,22,222"},
		{100000.2, true, "1,00,000.20"},
		{-100000.2, true, "-1,00,000.20"},
		{-100000.2, false, "-1,00,000"},
		{100000.22, true, "1,00,000.22"},
		{100000.223, true, "1,00,000.22"},
		{100000.226, true, "1,00,000.23"}, // round the values in case of extra decimal
		{100000.226, false, "1,00,000"},
		{100000.626, false, "1,00,001"}, // round the value if decimal point is to be ignored
		{*********, true, "10,00,00,230.00"},
		{*********, false, "10,00,00,230"},
	}
	for index, test := range testCases {
		if output := FormatCurrency(test.arg1, test.arg2); output != test.expected {
			t.Errorf("Case %d: Output %v not equal to expected %v", index+1, output, test.expected)
		}
	}
}

func TestValidateAccountNumber(t *testing.T) {
	type testStruct struct {
		arg      string
		expected bool
	}
	// should pass all numeric values with length [9, 18]
	var testCases = []testStruct{
		{"1234", false},
		{"ABCDEF123", false},
		{"-12345", false},
		{"*********", true},
		{"A11111111", false},
		{"******************", true},
		{"*********12111111", true},
		{"*********1121111111", false},
	}
	for index, test := range testCases {
		if output := ValidateAccountNumber(test.arg); output != test.expected {
			t.Errorf("Case %d: Output %v not equal to expected %v", index+1, output, test.expected)
		}
	}
}

func TestCalculateMedian(t *testing.T) {
	type testStruct struct {
		arg      []float64
		expected float64
	}
	// should pass all numeric values with length [9, 18]
	var testCases = []testStruct{
		{[]float64{}, 0},                     // no element
		{[]float64{0}, 0},                    // single element, zero
		{[]float64{7}, 7},                    // single element, non zero
		{[]float64{6, 0}, 3},                 // two elements with zero
		{[]float64{5, 1}, 3},                 // two elements without zero
		{[]float64{6, 1, 4, 2, 3, 5}, 3.5},   // even count
		{[]float64{6, -1, 4, 2, 3, -5}, 2.5}, // with negative numbers
		{[]float64{2, 5, 1, 3, 4}, 3},        // odd count
		{[]float64{3, 5, 1, 4, 3, 2}, 3},     // repeated left
		{[]float64{4, 2, 4, 3, 1, 5}, 3.5},   // repeated right
		{[]float64{2, 3, 3, 4, 1, 3}, 3},     // repeated both
		{[]float64{2, 1, 4, 2, 3}, 2},        // repeated left
		{[]float64{2, 1, 3, 4, 3}, 3},        // repeated right
		{[]float64{2, 3, 2, 2, 1}, 2},        // repeated both
	}
	for index, test := range testCases {
		if output := CalculateMedian(test.arg); output != test.expected {
			t.Errorf("Case %d: Output %v not equal to expected %v", index+1, output, test.expected)
		}
	}
}

func TestIsFreeMail(t *testing.T) {
	type testStruct struct {
		arg      string
		expected bool
	}
	var testCases = []testStruct{
		{"<EMAIL>", true},
		{"<EMAIL>", true},
		{"<EMAIL>", true},
		{"<EMAIL>", false},
		{"<EMAIL>", false},
		{"<EMAIL>", true},
		{"<EMAIL>", true},
		{"<EMAIL>", true},
		{"something123", false},         // ignores invalid email
		{"something123gmail", false},    // ignores invalid email
		{"something123gmailcom", false}, // ignores invalid email
	}
	for index, test := range testCases {
		if output := IsFreeMail(test.arg); output != test.expected {
			t.Errorf("Case %d: Output %v not equal to expected %v", index+1, output, test.expected)
		}
	}
}

func TestCheckWithinNDays(t *testing.T) {
	type testStruct struct {
		generateDays int
		checkDays    int
		expected     bool
	}
	var testCases = []testStruct{
		{1, 3, true},
		{2, 3, true},
		{0, 3, true},
		{3, 3, true},
		{4, 3, false},
		{5, 3, false},
		{4, -3, false}, // invalid n days
		{-2, 3, true},  // future date
		{-3, 3, true},  // future date
		{-4, 3, true},  // future date
	}
	for index, test := range testCases {
		generatedDate := time.Now().AddDate(0, 0, -test.generateDays).Format("2006-01-02")
		if output := CheckWithinNDays(generatedDate, test.checkDays); output != test.expected {
			t.Errorf("Case %d: Output %v not equal to expected %v", index+1, output, test.expected)
		}
	}
}

func TestGetDifferenceInMonths(t *testing.T) {
	type testStruct struct {
		dateA    string
		dateB    string
		expected int
	}
	var testCases = []testStruct{
		{"2022-08-01", "2022-08-01", 0},   // a = b
		{"2022-08-01", "2022-05-30", 3},   // a > b
		{"2022-05-30", "2022-08-01", 0},   // a < b
		{"2022-08-29", "2022-08-01", 0},   // a > b, but difference less
		{"2034-09-29", "2022-08-01", 145}, // a > b, but difference large
	}
	for index, test := range testCases {

		inputA, _ := time.Parse(constants.DateFormat, test.dateA)
		inputB, _ := time.Parse(constants.DateFormat, test.dateB)

		if output := GetDifferenceInMonths(inputA, inputB); output != test.expected {
			t.Errorf("Case %d: Output %v not equal to expected %v", index+1, output, test.expected)
		}
	}
}

func TestSDKVersionGreaterThan(t *testing.T) {
	type testStruct struct {
		sdkVersion       string
		thresholdVersion string
		expected         bool
	}
	var testCases = []testStruct{
		{"1.8.4", "1.8.4", false}, // cases with same values
		{"1.9.3.4", "1.9.3.4", false},
		{"1.8.4", "1.9.3.4", false}, // cases with different number of sublevels
		{"1.9.3.4", "1.8.4", true},
		{"1.8.3.4", "1.8.4", false},
		{"1.8.4.4", "1.8.4", true},
		{"1.8.5", "1.8.4", true}, // case with same levels
		{"1.9.4", "1.8.4", true},
		{"1.8.4", "1.9.4", false},
		{"2.9.4", "1.9.4", true},
		{"1.9.4", "2.9.4", false},
		{"2.9.4", "2.9.5", false},
		{"1.8.4", constants.SDKVersionWeb, false}, // case with web / hybrid involved
		{"1.8.4", "hybrid:2.2.7.7", false},
		{constants.SDKVersionWeb, "1.8.4", true},
		{constants.SDKVersionWeb, "1.9.3.4", true},
		{constants.SDKVersionWeb, "hybrid:2.2.7.7", true},
		{"hybrid:2.2.7.7", "1.8.4", true},
		{"hybrid:2.2.7.7", "1.9.3.4", true},
		{"hybrid:2.2.7.7", constants.SDKVersionWeb, true},
		{"hybrid:2.2.7.7", "hybrid:2.2.7.7", true},
		{constants.SDKVersionWeb, constants.SDKVersionWeb, true},
	}
	for index, test := range testCases {
		if output := SDKVersionGreaterThan(test.sdkVersion, test.thresholdVersion); output != test.expected {
			t.Errorf("Case %d: Output %v not equal to expected %v", index+1, output, test.expected)
		}
	}
}

func TestGetInitial(t *testing.T) {
	type testStruct struct {
		arg      string
		expected string
	}
	var testCases = []testStruct{
		{"Hello World", "HW"},
		{"'Hello World'", "HW"},
		{"Hello world", "HW"},
		{"Hello from FinBox", "HF"},
		{"Hello 123 FinBox", "HF"},
		{"Hello 123", "HE"},
		{"こんにちは hello", "HE"}, // hello in japanese :)
		{"नमस्ते", ""},
		{"नमस्ते from FinBox", "FF"},
		{"Hello", "HE"},
		{"hello World", "HW"},
	}
	for index, test := range testCases {
		if output := GetInitial(test.arg); output != test.expected {
			t.Errorf("Case %d: Output %v not equal to expected %v", index+1, output, test.expected)
		}
	}
}

func TestValidateIFSC(t *testing.T) {
	type testStruct struct {
		arg      string
		expected bool
	}
	var testCases = []testStruct{
		{"SBIN0125620", true},
		{"'SBIN0125620'", false},
		{"\"SBIN0125620\"", false},
		{"SBIN0125", false},
		{"1234SBIN012", false},
		{"JNUN012562B", true},
		{"KAUS0195", false},
		{"PUNB0150100", true},
		{"ZXCN798562x", true},
		{"BARB0VJNRRO", true},
	}
	for index, test := range testCases {
		if output := ValidateIFSC(test.arg); output != test.expected {
			t.Errorf("Case %d: Output %v not equal to expected %v", index+1, output, test.expected)
		}
	}
}

func TestValidateUUID(t *testing.T) {
	type testStruct struct {
		arg      string
		expected bool
	}
	var testCases = []testStruct{
		{"abcdef123", false},
		{"3a0d84e3-13d5-4805-9ccb-6336a68982b5", true},
		{"'3a0d84e3-13d5-4805-9ccb-6336a68982b5'", false},
		{"*3a0d84e3-13d5-4805-9ccb-6336a68982b5*", false},
		{"\"3a0d84e3-13d5-4805-9ccb-6336a68982b5\"", false},
		{"3a0d84e313d548059ccb6336a68982b5", false},
	}
	for index, test := range testCases {
		if output := ValidateUUID(test.arg); output != test.expected {
			t.Errorf("Case %d: Output %v not equal to expected %v", index+1, output, test.expected)
		}
	}
}

func TestMatchAccountNumber(t *testing.T) {
	type testStruct struct {
		arg1, arg2 string
		expected   bool
	}
	var testCases = []testStruct{
		{"", "", true},
		{"", "123", false},
		{"123", "", false},
		{"123", "0", false},
		{"0", "123", false},
		{"*************", "*************", true},
		{"0000*************", "*************", true},
		{"1668XXXXXXXX8", "*************", false},  // pattern mismatch
		{"001668XXXXXXXX8", "*************", true}, // trailing 0 at start and pattern match
		{"1668XXXXXXXX8", "*************8", true},  // length mismatch, but start and end matching
		{"001668XXXXXXXX8", "*************8", true},
		{"XXXX9407", "**************", true},
		{"01668XXXXXXXX8", "000*************", true}, // different trailing 0 counts at start and pattern match
		{"1668XXXXXXXX8", "1668XXXXXXXX8", true},     // pattern match
		{"XXXXXXXXXXXX8", "*************", true},     // pattern match
		{"XXXXXXXXX3278", "*************", true},     // pattern match
		{"*************", "XXXXXXXXX8538", true},
		{"112", "0012", false},
		{"112", "012", false},
		{"112", "X12", true},
		{"112", "XX12", true},
		{"112", "XXX112", true},
		{"XXX000112", "XXXXXX112", true},
		{"XXX000112", "*********", true},
		{"XXX000112", "*********", true},
		{"000XXX112", "*********", true},
		{"000X7X112", "*********", false},
		{"000X2X112", "*********", true},
		{"000X7X4X112", "***********", false},
		{"XXX00A112", "XXXXXX112", false}, // use of invalid character A, will never match
	}
	for index, test := range testCases {
		if output := MatchAccountNumber(test.arg1, test.arg2); output != test.expected {
			t.Errorf("Case %d: Output %v not equal to expected %v", index+1, output, test.expected)
		}
	}
}

func TestSplitSliceInEqualParts(t *testing.T) {
	type testStruct struct {
		arg1     int
		arg2     int
		expected []int
	}

	var testCases = []testStruct{
		{0, 100, []int{}},
		{0, -1, []int{}},
		{-1, 0, []int{}},
		{3, 1, []int{1, 1, 1}},
		{-1, -1, []int{}},
		{500, 100, []int{100, 100, 100, 100, 100}},
		{500, 600, []int{500}},
		{500, 120, []int{120, 120, 120, 120, 20}},
		{25000, 4000, []int{4000, 4000, 4000, 4000, 4000, 4000, 1000}},
	}

	for index, test := range testCases {
		if output := SplitSliceInEqualParts(test.arg1, test.arg2); !reflect.DeepEqual(output, test.expected) {
			t.Errorf("Case %d: Output %v not equal to expected %v", index+1, output, test.expected)
		}
	}
}

func TestValidMAC(t *testing.T) {
	type testStruct struct {
		arg1     string
		arg2     string
		arg3     string
		expected bool
	}
	var testCases = []testStruct{
		{"3JYyXa2", "c5btmiJGFpM0u7qpqWkiLrqKNAXXC5ju", "5def1f8df94173520aef3f62d2f4d1d8efe639c8", true},
		{"", "c5btmiJGFpM0u7qpqWkiLrqKNAXXC5ju", "be71d678ed2d4c5c0caf2f65a55429bdef52d297", true},
		{"3JYyXa2", "", "ab87083e2a8f6cb411d76cb1137943a988af309f", true},
		{"", "", "fbdb1d1b18aa6c08324b7d64b71fb76370690e1d", true},
		{"", "", "5def1f8df94173520aef3f62d2f4d1d8efe639c8", false},
	}
	for index, test := range testCases {
		if output := ValidMAC(test.arg1, test.arg2, test.arg3); output != test.expected {
			t.Errorf("Case %d: Output %v not equal to expected %v", index+1, output, test.expected)
		}
	}
}

func TestRoundFloat(t *testing.T) {
	type testStruct struct {
		arg1     float64
		arg2     int
		expected float64
	}
	var testCases = []testStruct{
		{32.456, 2, 32.46},
		{178.4567, 3, 178.457},
		{765.45679, 3, 765.457},
		{897.45679, 4, 897.4568},
		{0.00, 4, 0},
		{68.197884, 5, 68.19788},
		{68.943, 2, 68.94},
		{7.7, 0, 8},
		{-118.78789, 4, -118.7879},
		{217.787899, 5, 217.7879},
		{6.7, 0, 7},
		{0.00, 0, 0},
		{4.123, 2, 4.12},
		{4.124, 2, 4.12},
		{4.125, 2, 4.13},
		{4.136, 2, 4.14},
	}
	for index, test := range testCases {
		if output := RoundFloat(test.arg1, test.arg2); output != test.expected {
			t.Errorf("Case %d: Output %v not equal to expected %v", index+1, output, test.expected)
		}
	}
}

func TestMaskAadhaar(t *testing.T) {
	type testStruct struct {
		given        string
		expected     string
		expectedBool bool
	}
	testCases := []testStruct{
		{"", "", false},
		{"*********012345", "*********012345", false},
		{"*********", "*********", false},
		{"*********012", "XXXXXXXX9012", true},
		{"XXXXXXXXXXXX", "XXXXXXXXXXXX", true},
		{"XXXXXXX45XXX", "XXXXXXXX5XXX", true},
	}

	for ind, testCase := range testCases {
		if maskedAadhaar, success := MaskAadhaar(testCase.given); maskedAadhaar != testCase.expected || success != testCase.expectedBool {
			t.Errorf("Case %d: Output %v,%v not equal to expected %v,%v\n", ind+1, maskedAadhaar, success, testCase.expected, testCase.expectedBool)
		}
	}
}

func TestUnmarshalXML(t *testing.T) {
	type personStruct struct { // with xml tagging
		FName string `xml:"firstName"`
		LName string `xml:"lastName"`
	}
	type xmlStruct struct {
		Person personStruct // without xml tagging
	}

	type testStruct struct {
		given         string
		expected      xmlStruct
		errorExpected bool
	}
	testCases := []testStruct{
		{``, xmlStruct{}, true}, // blank string
		{`{"person":{"firstName":"Shourya Pratap","lastName":"Singh"}}`, xmlStruct{}, true},                                                                                                                                          // invalid XML
		{`<?xml version="1.0" encoding="UTF-8"?><person><firstName>Shourya Pratap</firstName><lastName>Singh</lastName></person>`, xmlStruct{Person: personStruct{FName: "Shourya Pratap", LName: "Singh"}}, false},                  // exact match in xml and receiving end
		{`<?xml version="1.0" encoding="UTF-16"?><person><firstName>ß ü</firstName><lastName>ä Öä</lastName></person>`, xmlStruct{Person: personStruct{FName: "ß ü", LName: "ä Öä"}}, false},                                         // utf-16 case
		{`<?xml version="1.0" encoding="UTF-8"?><person><firstName>Shourya Pratap</firstName></person>`, xmlStruct{Person: personStruct{FName: "Shourya Pratap"}}, false},                                                            // missing fields in input
		{`<?xml version="1.0" encoding="UTF-8"?><person><firstName>Shourya</firstName><middleName>Pratap</middleName><lastName>Singh</lastName></person>`, xmlStruct{Person: personStruct{FName: "Shourya", LName: "Singh"}}, false}, // missing fields in output
	}

	for ind, testCase := range testCases {
		var output xmlStruct
		err := UnmarshalXML([]byte(testCase.given), &output)
		if testCase.errorExpected {
			if err == nil {
				t.Errorf("Case %d: expected error, got: %+v", ind+1, output)
			}
		} else {
			if err != nil {
				t.Errorf("Case %d: didn't expected error, got: %v", ind+1, err.Error())
			} else if reflect.DeepEqual(output, testCase.expected) {
				t.Errorf("Case %d: Output %+v not equal to expected %+v", ind+1, output, testCase.expected)
			}
		}
	}
}

func TestTitle(t *testing.T) {
	type testStruct struct {
		given    string
		expected string
	}
	testCases := []testStruct{
		{"", ""},
		{"her royal highness", "Her Royal Highness"},
		{"loud noises", "Loud Noises"},
		{"хлеб", "Хлеб"}, // Russian for bread :)
	}

	for ind, testCase := range testCases {
		if output := Title(testCase.given); output != testCase.expected {
			t.Errorf("Case %d: Output %s not equal to expected %s\n", ind+1, output, testCase.expected)
		}
	}
}

func TestValidateMobile(t *testing.T) {
	type testStruct struct {
		given    string
		expected bool
	}
	testCases := []testStruct{
		{"", false},
		{"9013683901", true},
		{"9999999999", true},
		{"9142", false},
		{"+919013683901", false},
		{"901-3683-901", false},
		{"09013683901", false},
		{"5093683901", false},
		{"0332672769", false},
		{"6093683901", true},
		{"7093683901", true},
		{"8093683901", true},
	}

	for ind, testCase := range testCases {
		if output := ValidateMobile(testCase.given); output != testCase.expected {
			t.Errorf("Case %d: Output %t not equal to expected %t\n", ind+1, output, testCase.expected)
		}
	}
}

func TestCoalesce(t *testing.T) {
	type testStruct struct {
		args     []string
		expected string
	}
	testCases := []testStruct{
		{[]string{"", "", ""}, ""},
		{[]string{""}, ""},
		{[]string{"hello world", "", "world hello"}, "hello world"},
		{[]string{"", "hello world", "world hello"}, "hello world"},
		{[]string{"", "", "hello world", "", "world hello"}, "hello world"},
		{[]string{}, ""},
	}

	for ind, testCase := range testCases {
		if output := Coalesce(testCase.args...); output != testCase.expected {
			t.Errorf("Case %d: Output %s not equal to expected %s\n", ind+1, output, testCase.expected)
		}
	}
}

func TestValidateAgentCode(t *testing.T) {
	type testStruct struct {
		given    string
		expected bool
	}
	testCases := []testStruct{
		{"", false},
		{"test", true},
		{"test_agent", true},
		{"test-agent", true},
		{"test01", true},
		{"test_agent-01", true},
		{"test@agent", false},
		{"test&agent", false},
		{"test$agent", false},
		{"test+agent", false},
		{"test agent", false},
		{"test,agent", false},
		{"test:agent", false},
		{"test;agent", false},
		{"test=agent", false},
		{"test#agent", false},
		{"test<>agent", false},
		{"test{}agent", false},
		{"test[]agent", false},
		{"test//agent", false},
		{"test|agent", false},
		{"test^agent", false},
		{"test%agent", false},
		{"test?agent", false},
		{"b2b9a111-32a7-4001-bb1d-216f46ab8a93", true},
		{"b2b9a111-32a7-4001-bb1d-216f46ab8a931222", false},
	}

	for ind, testCase := range testCases {
		if output := ValidateAgentCode(testCase.given); output != testCase.expected {
			t.Errorf("Case %d: Output %t not equal to expected %t\n", ind+1, output, testCase.expected)
		}
	}
}

func TestAgeAt(t *testing.T) {
	type testStruct struct {
		arg1     time.Time
		arg2     time.Time
		expected int
	}
	testCases := []testStruct{
		{time.Date(2000, 3, 14, 0, 0, 0, 0, time.UTC), time.Date(2010, 3, 14, 0, 0, 0, 0, time.UTC), 10},
		{time.Date(2001, 3, 14, 0, 0, 0, 0, time.UTC), time.Date(2009, 3, 14, 0, 0, 0, 0, time.UTC), 8},
		{time.Date(1999, 3, 14, 0, 0, 0, 0, time.UTC), time.Date(2004, 3, 14, 0, 0, 0, 0, time.UTC), 5},
		{time.Date(1900, 2, 28, 0, 0, 0, 0, time.UTC), time.Date(2004, 3, 14, 0, 0, 0, 0, time.UTC), 104},
		{time.Date(2004, 6, 18, 0, 0, 0, 0, time.UTC), time.Date(2005, 5, 12, 0, 0, 0, 0, time.UTC), 0},
		{time.Date(2004, 6, 18, 0, 0, 0, 0, time.UTC), time.Date(2000, 5, 12, 0, 0, 0, 0, time.UTC), 0}, // case of past date
	}

	for ind, testCase := range testCases {
		if output := AgeAt(testCase.arg1, testCase.arg2); output != testCase.expected {
			t.Errorf("Case %d: Output %d not equal to expected %d\n", ind+1, output, testCase.expected)
		}
	}
}

func TestIsValidName(t *testing.T) {
	type testStruct struct {
		input    string
		expected bool
	}
	testCases := []testStruct{
		{"Hermione Granger", true},
		{"D@rth Vader", false},
		{"12345 ", false},
		{"Palpatine Хлеб", false},
		{"Abc", true},
		{"Abc9", false},
		{"Luke Skywalker", true},
		{"Sachin Ramesh Tendulkar", true},
		{"Abc$", false},
		{"Smriti Mandana", true},
		{"Om", true},
		{"abcefghijklmnopqrstuvwxyzabcefghijklmnopqrstuvwxyzpqrstuvwxyz", false},
		{"abcefghijklmnopqrstuvwxyzabcefghijklmnopqrstuvwxyzpqrstuvwxy", true},
	}

	for ind, testCase := range testCases {
		if output := IsValidName(testCase.input); output != testCase.expected {
			t.Errorf("Case %d: Output %v not equal to expected %v %v\n", ind+1, output, testCase.input, testCase.expected)
		}
	}
}
func TestGetArrayIntersection(t *testing.T) {
	type testStruct[T comparable] struct {
		arg1     []T
		arg2     []T
		expected []T
	}
	testCases := []testStruct[string]{
		{[]string{}, []string{}, []string{}},
		{[]string{"a", "b", "c"}, []string{"b", "c"}, []string{"b", "c"}},
		{[]string{"b", "c"}, []string{"a", "b", "c"}, []string{"b", "c"}},
		{[]string{"b"}, []string{"c"}, []string{}},
	}
	for index, testCase := range testCases {
		if output := GetArrayIntersection(testCase.arg1, testCase.arg2); !reflect.DeepEqual(output, testCase.expected) {
			t.Errorf("Case %d: Output %v not equal to expected %v\n", index+1, output, testCase.expected)
		}
	}
}

func TestValidateSalesRange(t *testing.T) {
	type testStruct struct {
		arg      string
		expected bool
	}
	var testCases = []testStruct{
		{"", true}, // allow blank string
		{"<script>alert('compromised');</script>", false},
		{"#include<iostream>", false},
		{"&78089234829034*", false},
	}
	for _, value := range constants.IncomeRangeList {
		testCases = append(testCases, testStruct{
			arg:      value,
			expected: true,
		})
	}
	for _, value := range constants.IncomeRangeListNexArc {
		testCases = append(testCases, testStruct{
			arg:      value,
			expected: true,
		})
	}
	for index, test := range testCases {
		if output := ValidateSalesRange(test.arg); output != test.expected {
			t.Errorf("Case %d: Output %v not equal to expected %v", index+1, output, test.expected)
		}
	}
}

func TestCalculateFlag(t *testing.T) {
	type testStruct struct {
		key        string
		flag       string
		percentage int
		isFlagTrue bool
	}
	var testValues = []testStruct{
		{"f6b07806-6cbf-48e7-9ca9-c563d72a951d", "flag1", 41, false},
		{"f6b07806-6cbf-48e7-9ca9-c563d72a951d", "flag2", 8, true},
		{"35f21e7a-163f-4771-9daf-cc1cb1496ae2", "flag3", 1, true},
		{"f6b07806-6cbf-48e7-9ca9-c563d72a951d", "flag2", 0, false},
		{"f6b07806-6cbf-48e7-9ca9-c563d72a951d", "flag2", -12, false},
		{"f6b07806-6cbf-48e7-9ca9-c563d72a951d", "flag2", 100, true},
		{"f6b07806-6cbf-48e7-9ca9-c563d72a951d", "flag2", 120, true},
	}
	for index, test := range testValues {
		for i := 1; i <= 100; i++ {
			if output := CalculateFlag(test.key, test.flag, test.percentage); output != test.isFlagTrue {
				t.Errorf("Case %d: Output %v not equal to expected %v", index+1, output, test.isFlagTrue)
			}
		}
	}
}

func TestFindMapInMapArray(t *testing.T) {
	type testStruct struct {
		key    string
		val    interface{}
		arr    []map[string]interface{}
		index  int
		result bool
	}
	mapArr := []map[string]interface{}{
		{"name": "Some", "age": 10, "gender": "male"},
		{"name": "None", "age": 12, "gender": "female"},
		{"name": "Test", "age": 15, "gender": "male"},
	}
	var testValues = []testStruct{
		{"name", "abhishek", mapArr, -1, false},
		{"name", "Some", mapArr, 0, true},
		{"name", "Test", mapArr, 2, true},
		{"age", 45, mapArr, -1, false},
		{"age", 15, mapArr, 2, true},
		{"gender", "none", mapArr, -1, false},
	}
	for index, test := range testValues {
		_, ind, res := FindMapInMapArray(test.key, test.val, test.arr)
		if res != test.result || ind != test.index {
			t.Errorf("Case %d: not as expected", index+1)
		}
	}
}

func TestHaversine(t *testing.T) {
	testCases := []struct {
		lat1, lon1, lat2, lon2 float64
		expectedDistance       float64
	}{
		{37.4267861, -122.0806032, 34.052235, -118.243683, 488.82},
		{40.712776, -74.005974, 34.052235, -118.243683, 3944.92},
	}

	for _, tc := range testCases {
		calculatedDistance := HaverSineDistanceKms(tc.lat1, tc.lon1, tc.lat2, tc.lon2)
		if math.Abs(calculatedDistance-tc.expectedDistance) > 0.01 { // Allow a small margin of error
			t.Errorf("For coordinates (%f, %f) and (%f, %f), expected %.2f km, but got %.2f km",
				tc.lat1, tc.lon1, tc.lat2, tc.lon2, tc.expectedDistance, calculatedDistance)
		}
	}
}

func TestExtractRequest(t *testing.T) {
	type testStruct struct {
		urlParams    url.Values
		outputParams map[string]string
	}

	var testValues = []testStruct{
		{urlParams: url.Values{"key": []string{"value"}}, outputParams: map[string]string{"key": "value"}},
		{urlParams: url.Values{}, outputParams: map[string]string{}},
	}

	type TypeWithJSONTag struct {
		Key []string `json:"key" validate:"required"`
	}

	type TypeWithoutJSONTag struct {
		Key []string
	}

	if output, err := ExtractRequestParams[TypeWithJSONTag](testValues[0].urlParams); err != nil {
		t.Errorf("Case 1 and Type %s: Output %v not equal to expected %v because %s", "TypeWithJSONTag", output, testValues[0].outputParams, err)
	}
	if output, err := ExtractRequestParams[TypeWithoutJSONTag](testValues[0].urlParams); err == nil {
		t.Errorf("Case 1 and Type %s: Output %v and expected %v should result in an error", "TypeWithoutJSONTag", output, testValues[0].outputParams)
	}
	if output, err := ExtractRequestParams[TypeWithoutJSONTag](testValues[1].urlParams); err == nil {
		t.Errorf("Case 1 and Type %s: Output %v and expected %v should result in a validation error", "TypeWithoutJSONTag", output, testValues[0].outputParams)
	}
}

func TestWeightedRandomness(t *testing.T) {
	weightedVendors := []WeightedEntities[string]{
		{Type: "HYPERVERGE", Weight: 50},
		{Type: "GRIDLINE", Weight: 50},
	}
	hypervergeCount := 0
	gridlineCount := 0
	for i := 0; i < 1000; i++ {
		vendor := WeightedRandomness[string](weightedVendors)
		if vendor == "HYPERVERGE" {
			hypervergeCount++
		} else if vendor == "GRIDLINE" {
			gridlineCount++
		}
	}
	expectedMinHyperCount := 400
	expectedMinGridCount := 400
	if hypervergeCount < expectedMinHyperCount {
		t.Errorf("Expected minimum Hyperverge count %d, got %d", expectedMinHyperCount, hypervergeCount)
	}
	if gridlineCount < expectedMinGridCount {
		t.Errorf("Expected minimum Gridline count %d, got %d", expectedMinGridCount, gridlineCount)
	}

}

func TestDaysInMonth(t *testing.T) {
	type testStruct struct {
		timestamp           time.Time
		expectedDaysOfMonth int
	}

	d1String := "2023-12-08"
	d1Time, _ := time.Parse(constants.DateFormat, d1String)
	d2String := "2023-02-08"
	d2Time, _ := time.Parse(constants.DateFormat, d2String)
	d3String := "2024-02-08"
	d3Time, _ := time.Parse(constants.DateFormat, d3String)
	d4String := "2024-04-08"
	d4Time, _ := time.Parse(constants.DateFormat, d4String)

	var testCases = []testStruct{
		{d1Time, 31},
		{d2Time, 28},
		{d3Time, 29},
		{d4Time, 30},
	}

	for index, test := range testCases {
		daysOfMonth := GetDaysInMonth(test.timestamp)
		if daysOfMonth != test.expectedDaysOfMonth {
			t.Errorf("Case %d: Output daysOfMonth: %d, not equal to expected expected: %.d ", index+1, daysOfMonth, test.expectedDaysOfMonth)
		}
	}
}

func TestFindOneOf(t *testing.T) {
	type testStruct struct {
		input  map[string]any
		keys   []string
		output any
	}

	testCases := []testStruct{
		{
			input: map[string]any{
				"name": "Emilian Iokaste",
			},
			keys:   nil,
			output: nil,
		},
		// Priority case
		{
			input: map[string]any{
				"name":      "Emilian Iokaste",
				"aliasName": "Holly Anacletus",
			},
			keys:   []string{"name", "aliasName"},
			output: "Emilian Iokaste",
		},
		{
			input: map[string]any{
				"name":      "Emilian Iokaste",
				"aliasName": "Holly Anacletus",
			},
			keys:   []string{"aliasName"},
			output: "Holly Anacletus",
		},
		{
			input: map[string]any{
				"aliasName": "Holly Anacletus",
			},
			keys:   []string{"name"},
			output: nil,
		},
		{
			input: map[string]any{
				"name": "Monika Ereshkigal",
			},
			keys:   []string{"name"},
			output: "Monika Ereshkigal",
		},
		{
			input: map[string]any{
				"name":      "Monika Ereshkigal",
				"aliasName": "Holly Anacletus",
			},
			keys:   []string{"age", "gender"},
			output: nil,
		},
	}

	for index, test := range testCases {
		o := FindOneOf(test.input, test.keys...)
		if o != test.output {
			t.Errorf("Case %d: Output FindOneOf: %v not equal to expected: %v", index+1, o, test.output)
		}
	}
}

func TestFlatten(t *testing.T) {
	type testStruct struct {
		input       map[string]any
		excludeKeys []string
		output      map[string]any
	}

	testCases := []testStruct{
		{
			input: map[string]any{
				"name": "Patricija Zacharias",
				"age":  50,
				"address": map[string]any{
					"line1":   "Suite 980 578",
					"line2":   "Bogan Highway",
					"city":    "Powlowskibury",
					"state":   "OR",
					"zipcode": "94602",
				},
			},
			excludeKeys: nil,
			output: map[string]any{
				"name":    "Patricija Zacharias",
				"age":     50,
				"line1":   "Suite 980 578",
				"line2":   "Bogan Highway",
				"city":    "Powlowskibury",
				"state":   "OR",
				"zipcode": "94602",
			},
		},
		{
			input: map[string]any{
				"name": "Patricija Zacharias",
				"age":  50,
				"address": map[string]any{
					"line1":   "Suite 980 578",
					"line2":   "Bogan Highway",
					"city":    "Powlowskibury",
					"state":   "OR",
					"zipcode": "94602",
				},
			},
			excludeKeys: []string{"address"},
			output: map[string]any{
				"name": "Patricija Zacharias",
				"age":  50,
				"address": map[string]any{
					"line1":   "Suite 980 578",
					"line2":   "Bogan Highway",
					"city":    "Powlowskibury",
					"state":   "OR",
					"zipcode": "94602",
				},
			},
		},
		{
			input: map[string]any{
				"name": "Patricija Zacharias",
				"age":  50,
				"address": map[string]any{
					"line1":   "Suite 980 578",
					"line2":   "Bogan Highway",
					"city":    "Powlowskibury",
					"state":   "OR",
					"zipcode": "94602",
				},
				"phone_numbers": []string{
					"7898645678",
					"2784907657",
					"0936785624",
				},
			},
			excludeKeys: nil,
			output: map[string]any{
				"name":    "Patricija Zacharias",
				"age":     50,
				"line1":   "Suite 980 578",
				"line2":   "Bogan Highway",
				"city":    "Powlowskibury",
				"state":   "OR",
				"zipcode": "94602",
				"phone_numbers": []string{
					"7898645678",
					"2784907657",
					"0936785624",
				},
			},
		},
	}

	for idx, test := range testCases {
		output := map[string]any{}
		Flatten(test.input, output, test.excludeKeys...)
		if !reflect.DeepEqual(output, test.output) {
			t.Errorf("Case %d: Output Flatten: %v not equal to expected: %v", idx+1, output, test.output)
		}
	}
}

func TestParseISO8601(t *testing.T) {
	testCases := []struct {
		input    string
		expected ISO8601Duration
		valid    bool
	}{
		{"P1Y2M3W4DT5H6M7S", ISO8601Duration{1, 2, 3, 4, 5, 6, 7}, true},
		{"P2W3DT4H", ISO8601Duration{0, 0, 2, 3, 4, 0, 0}, true},
		{"P1Y", ISO8601Duration{1, 0, 0, 0, 0, 0, 0}, true},
		{"PT2M", ISO8601Duration{0, 0, 0, 0, 0, 2, 0}, true},
		{"P1Y2M", ISO8601Duration{1, 2, 0, 0, 0, 0, 0}, true},
		{"P1Y2M3W4DT5H6M7S", ISO8601Duration{1, 2, 3, 4, 5, 6, 7}, true},
		{"invalid", ISO8601Duration{}, false},
	}

	for _, tc := range testCases {
		t.Run(tc.input, func(t *testing.T) {
			result, err := ParseISO8601(tc.input)

			if tc.valid && err != nil {
				t.Errorf("Unexpected error parsing valid input: %v", err)
			}

			if !tc.valid && err == nil {
				t.Error("Expected error but got nil")
			}

			if result != tc.expected {
				t.Errorf("Unexpected result. Expected %v, got %v", tc.expected, result)
			}
		})
	}
}

func TestIsValidMillisTimestamp(t *testing.T) {
	tests := []struct {
		name      string
		timestamp int64
		expected  bool
	}{
		{"Valid Timestamp", 1611925200000, true}, // January 29, 2021
		{"Invalid Timestamp", -1, false},         // Negative timestamp
		{"Invalid Timestamp", 0, false},          // Zero timestamp
		{"Valid Timestamp", 1630454400000, true}, // September 1, 2021
		{"Valid Timestamp", 1643481600000, true}, // January 29, 2022
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			result := isValidMillisTimestamp(test.timestamp)
			if result != test.expected {
				t.Errorf("Expected %v for timestamp %d, but got %v", test.expected, test.timestamp, result)
			}
		})
	}
}

func TestParseMillisTimestamp(t *testing.T) {
	// Set the location to Asia/Calcutta
	loc, err := time.LoadLocation("Asia/Calcutta")
	if err != nil {
		t.Fatalf("failed to load location: %v", err)
	}
	tests := []struct {
		name         string
		milliseconds int64
		expected     time.Time
		expectedErr  error
	}{
		{"Valid Timestamp", 1611925200000, time.Date(2021, time.January, 29, 18, 30, 0, 0, loc), nil}, // January 29, 2021
		{"Invalid Timestamp", -1, time.Time{}, errors.New("invalid Timestamp passed")},                // Negative timestamp
		{"Invalid Timestamp", 0, time.Time{}, errors.New("invalid Timestamp passed")},                 // Zero timestamp
		{"Valid Timestamp", 1630454400000, time.Date(2021, time.September, 1, 5, 30, 0, 0, loc), nil}, // September 1, 2021
		{"Valid Timestamp", 1643481600000, time.Date(2022, time.January, 30, 0, 10, 0, 0, loc), nil},  // January 29, 2022
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			result, err := ParseMillisTimestamp(test.milliseconds)
			if err != nil && err.Error() != test.expectedErr.Error() {
				t.Errorf("Expected error '%v' for timestamp %d, but got '%v'", test.expectedErr, test.milliseconds, err)
			} else if !result.Equal(test.expected) {
				t.Errorf("Expected %v for timestamp %d, but got %v", test.expected, test.milliseconds, result)
			}
		})
	}
}

func TestUnderscoresToCamelCase(t *testing.T) {
	tests := []struct {
		input    string
		expected string
	}{
		{"hello_world", "helloWorld"},
		{"HELLO_WORLD", "helloWorld"},
		{"snake_case_example", "snakeCaseExample"},
		{"camelCaseExample", "camelcaseexample"},
		{"alreadyCamelCase", "alreadycamelcase"},
	}

	for _, test := range tests {
		t.Run(test.input, func(t *testing.T) {
			result := UnderscoresToCamelCase(test.input)
			if result != test.expected {
				t.Errorf("Expected %s, but got %s", test.expected, result)
			}
		})
	}
}

func TestTimeDuration(t *testing.T) {
	testCases := []struct {
		duration ISO8601Duration
		expected time.Duration
	}{
		{ISO8601Duration{0, 0, 0, 0, 0, 2, 0}, 120 * time.Second},
		{ISO8601Duration{0, 0, 0, 0, 1, 0, 0}, 1 * time.Hour},
		{ISO8601Duration{0, 0, 0, 0, 0, 10, 0}, 10 * time.Minute},
		{ISO8601Duration{0, 0, 0, 0, 5, 30, 0}, 5*time.Hour + 30*time.Minute},
		{ISO8601Duration{0, 0, 0, 0, 0, 0, 2}, 2 * time.Second},
	}

	for _, tc := range testCases {
		t.Run(fmt.Sprintf("%+v", tc.duration), func(t *testing.T) {
			result := tc.duration.TimeDuration()

			if result != tc.expected {
				t.Errorf("Unexpected result. Expected %v, got %v", tc.expected, result)
			}
		})
	}
}

func TestConvertMapToCamelCase(t *testing.T) {
	tests := []struct {
		name     string
		input    map[string]interface{}
		expected map[string]interface{}
	}{
		{
			"Simple Map Conversion",
			map[string]interface{}{
				"hello_world": "value1",
				"foo_bar":     "value2",
			},
			map[string]interface{}{
				"helloWorld": "value1",
				"fooBar":     "value2",
			},
		},
		{
			"Nested Map Conversion",
			map[string]interface{}{
				"nested_data": map[string]interface{}{
					"nested_key": "nested_value",
				},
				"another_nested": map[string]interface{}{
					"nested_key": "nested_value",
				},
			},
			map[string]interface{}{
				"nestedData": map[string]interface{}{
					"nestedKey": "nested_value",
				},
				"anotherNested": map[string]interface{}{
					"nestedKey": "nested_value",
				},
			},
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			result := ConvertMapToCamelCase(test.input)
			if !reflect.DeepEqual(result, test.expected) {
				t.Errorf("Expected %v, but got %v", test.expected, result)
			}
		})
	}
}

func TestEscapeQuotes(t *testing.T) {
	tests := []struct {
		input    string
		expected string
	}{
		{"This is a \"test\"", "This is a \\\"test\\\""},     // Double quotes present
		{"Escape \\ backslashes", "Escape \\\\ backslashes"}, // Backslashes present
		{"No quotes or backslashes", "No quotes or backslashes"},
	}

	for _, test := range tests {
		t.Run(test.input, func(t *testing.T) {
			result := EscapeQuotes(test.input)
			if result != test.expected {
				t.Errorf("Expected %s, but got %s", test.expected, result)
			}
		})
	}
}

func TestParseValueJQ(t *testing.T) {
	// Sample input map data
	mapData := map[string]interface{}{
		"person": map[string]interface{}{
			"name":  "John Doe",
			"age":   30,
			"email": "<EMAIL>",
		},
		"nullValue": nil,
		"items": []interface{}{
			map[string]interface{}{
				"name":  "item1",
				"price": 10,
			},
			map[string]interface{}{
				"name":  "item2",
				"price": 20,
			},
		},
	}

	tests := []struct {
		name        string
		path        string
		expected    interface{}
		expectedErr error
	}{
		{
			"Valid Path - Person Name",
			".person.name",
			"John Doe",
			nil,
		},
		{
			"Finding Nil Value",
			".nullValue",
			nil,
			nil,
		},
		{
			"Valid Path - Items",
			".items",
			[]interface{}{
				map[string]interface{}{"name": "item1", "price": 10},
				map[string]interface{}{"name": "item2", "price": 20},
			},
			nil,
		},
		{
			"Invalid Path",
			".nonexistent",
			nil,
			nil,
		},
		{
			"Calculation",
			"3 + 4",
			7,
			nil,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			result, err := ParseValueJQ(context.Background(), test.path, mapData)

			if err != test.expectedErr {
				t.Errorf("Expected error: %v, but got: %v", test.expectedErr, err)
			}

			if !reflect.DeepEqual(result, test.expected) {
				t.Errorf("Expected result: %v, but got: %v", test.expected, result)
			}
		})
	}
}

func TestConcatNonEmpty(t *testing.T) {

	type testStruct struct {
		args     []string
		expected string
	}

	var testCases = []testStruct{
		{[]string{"", "", ""}, ""},
		{[]string{"hello", "", "world"}, "hello,world"},
		{[]string{"foo", "bar", "baz"}, "foo,bar,baz"},
		{[]string{"foo", "bar", "baz"}, "foo,bar,baz"},
		{[]string{"", "", "", "test"}, "test"},
	}
	for index, test := range testCases {
		if output := ConcatNonEmpty(test.args...); output != test.expected {
			t.Errorf("Case %d: Output %v not equal to expected %v", index+1, output, test.expected)
		}
	}
}

func TestBuildURL(t *testing.T) {
	type testStruct struct {
		baseURL  string
		params   []string
		expected string
	}

	var testCases = []testStruct{
		{"https://example.com", []string{}, "https://example.com"},
		{"https://example.com", []string{"param1"}, "https://example.com"},
		{"https://example.com", []string{"param1", "value1", "param2", "value2"}, "https://example.com?param1=value1&param2=value2"},
		{"https://example.com", []string{"param1", "value1", "param2", "value2", "param3", "value3"}, "https://example.com?param1=value1&param2=value2&param3=value3"},
	}

	for index, test := range testCases {
		resultURL, err := BuildURL(test.baseURL, test.params...)
		if err != nil {
			t.Errorf("Case %d: Error building URL: %v", index+1, err)
		}

		if resultURL != test.expected {
			t.Errorf("Case %d: URL %v not equal to expected %v", index+1, resultURL, test.expected)
		}
	}
}

func TestGenerateJSONBSQLUpdateQuerySuccessCase(t *testing.T) {

	type testStruct struct {
		input    GenerateJSONBSQLUpdateQueryInput
		expected string
	}

	var testCases = []testStruct{
		{
			GenerateJSONBSQLUpdateQueryInput{
				ColumnName: "test_column",
				JSONData: []JSONData{
					{
						Key:   "k1",
						Value: "v1",
					},
				},
			}, `jsonb_set(coalesce(test_column, '{}')::jsonb, '{k1}', '"v1"')`,
		},
		{
			GenerateJSONBSQLUpdateQueryInput{
				ColumnName: "test_column",
				JSONData: []JSONData{
					{

						Key:   "k1",
						Value: "v1",
					},
					{

						Key:   "k2",
						Value: "v2",
					},
					{

						Key:   "k3",
						Value: "v3",
					},
				},
			}, `jsonb_set(coalesce(jsonb_set(coalesce(jsonb_set(coalesce(test_column, '{}')::jsonb, '{k1}', '"v1"'), '{}')::jsonb, '{k2}', '"v2"'), '{}')::jsonb, '{k3}', '"v3"')`,
		},
	}

	for index, test := range testCases {
		result, err := GenerateJSONBSQLUpdateQuery(test.input)
		if err != nil {
			t.Errorf("Case %d: Error Generating Jsonb update Query: %v", index+1, err)
		}

		if result != test.expected {
			t.Errorf("Case %d: result %v not equal to expected %v", index+1, result, test.expected)
		}
	}
}

func TestGenerateJSONBSQLUpdateQueryErrorCase(t *testing.T) {

	type testStruct struct {
		input GenerateJSONBSQLUpdateQueryInput
	}

	var testCases = []testStruct{
		{
			GenerateJSONBSQLUpdateQueryInput{
				ColumnName: "test_column",
			},
		},
	}

	for index, test := range testCases {
		result, err := GenerateJSONBSQLUpdateQuery(test.input)
		if err == nil {
			t.Errorf("Case %d: Expecting error, but got result: %v", index+1, result)
		}

	}
}

func TestMergeStructs(t *testing.T) {

	type Person struct {
		Name  string
		Age   int
		Email string
	}

	type testStruct struct {
		dest        *Person
		src         interface{}
		expected    *Person // Added expected result for validation
		expectError bool    // Whether an error is expected in this case
	}

	var testCases = []testStruct{
		// Case 1: Both structs are valid, should merge without errors
		{
			dest:        &Person{Name: "Alice", Age: 25, Email: "<EMAIL>"},
			src:         &Person{Name: "Bob", Age: 30},
			expected:    &Person{Name: "Bob", Age: 30, Email: "<EMAIL>"}, // Name and Age should change, Email stays the same
			expectError: false,
		},
		// Case 2: Source is nil, should return an error
		{
			dest:        &Person{Name: "Alice", Age: 25, Email: "<EMAIL>"},
			src:         nil,
			expected:    &Person{Name: "Alice", Age: 25, Email: "<EMAIL>"}, // No change since src is nil
			expectError: true,
		},
		// Case 3: Destination is nil, should return an error
		{
			dest:        nil,
			src:         &Person{Name: "Bob", Age: 30},
			expected:    nil, // Destination is nil
			expectError: true,
		},
		// Case 4: Source is not a pointer, should return an error
		{
			dest:        &Person{Name: "Alice", Age: 25, Email: "<EMAIL>"},
			src:         Person{Name: "Bob", Age: 30},                                // Should return an error
			expected:    &Person{Name: "Alice", Age: 25, Email: "<EMAIL>"}, // No change since src is not a pointer
			expectError: true,
		},
	}

	for index, test := range testCases {
		err := MergeStructs(test.dest, test.src)
		if test.expectError && err == nil {
			t.Errorf("Case %d: Expected error but got none", index+1)
		}
		if !test.expectError && err != nil {
			t.Errorf("Case %d: Unexpected error: %v", index+1, err)
		}

		// Check if the result matches the expected struct after merge
		if test.dest != nil && !test.expectError && *test.dest != *test.expected {
			t.Errorf("Case %d: Expected %+v but got %+v", index+1, *test.expected, *test.dest)
		}
	}

}

func TestRemoveLeadingZero(t *testing.T) {
	type testStruct struct {
		name string
		req  string
		res  string
	}

	testCases := []testStruct{
		{
			name: "removed leading zero successfully for negative value",
			req:  "0000-9",
			res:  "-9",
		},
		{
			name: "removed leading zero successfully for postivite value",
			req:  "00082",
			res:  "82",
		},
		{
			name: "not imapct traling zeros",
			req:  "82000",
			res:  "82000",
		},
		{
			name: "string will all zeros",
			req:  "0000",
			res:  "",
		},
	}

	for index, test := range testCases {
		result := RemoveLeadingZero(test.req)
		if result != test.res {
			t.Errorf("Case %d: Expecting error, but got result: %v", index+1, result)
		}
	}
}

func TestValidateIpv4(t *testing.T) {
	type testStruct struct {
		ipv4   string
		result bool
	}

	testCases := []testStruct{
		{
			ipv4:   "*************",
			result: true,
		},
		{
			ipv4:   "**********",
			result: true,
		},
		{
			ipv4:   "*************",
			result: true,
		},
		{
			ipv4:   "000.392.144.33",
			result: false,
		},
		{
			ipv4:   "whats-my-ip",
			result: false,
		},
	}

	for index, test := range testCases {
		result := ValidateIPv4(test.ipv4)
		if result != test.result {
			t.Errorf("Case %d: Expecting error, but got result: %v", index+1, result)
		}
	}
}

func TestMergeMaps(t *testing.T) {
	type testStruct struct {
		name     string
		dst      map[string]any
		src      map[string]any
		expected map[string]any
	}

	testCases := []testStruct{
		{
			name: "Merging with empty source map",
			dst: map[string]any{
				"a": 1,
				"b": map[string]any{
					"ba": 21,
				},
				"c": 3,
			},
			src: map[string]any{},
			expected: map[string]any{
				"a": 1,
				"b": map[string]any{
					"ba": 21,
				},
				"c": 3,
			},
		},
		{
			name: "Merging with empty destination map",
			dst:  map[string]any{},
			src: map[string]any{
				"a": 1,
				"b": map[string]any{
					"ba": 21,
				},
				"c": 3,
			},
			expected: map[string]any{
				"a": 1,
				"b": map[string]any{
					"ba": 21,
				},
				"c": 3,
			},
		},
		{
			name:     "Merging two empty maps",
			dst:      map[string]any{},
			src:      map[string]any{},
			expected: map[string]any{},
		},
		{
			name: "Overwriting non-map value",
			dst: map[string]any{
				"a": 1,
			},
			src: map[string]any{
				"a": map[string]any{
					"nested": "value",
				},
			},
			expected: map[string]any{
				"a": map[string]any{
					"nested": "value",
				},
			},
		},
		{
			name: "Deep merge with nested maps",
			dst: map[string]any{
				"x": map[string]any{
					"y": map[string]any{
						"z": 10,
					},
				},
			},
			src: map[string]any{
				"x": map[string]any{
					"y": map[string]any{
						"w": 20,
					},
					"v": 30,
				},
			},
			expected: map[string]any{
				"x": map[string]any{
					"y": map[string]any{
						"z": 10,
						"w": 20,
					},
					"v": 30,
				},
			},
		},
		{
			name: "Non-overlapping keys",
			dst: map[string]any{
				"a": 1,
				"b": 2,
			},
			src: map[string]any{
				"c": 3,
				"d": 4,
			},
			expected: map[string]any{
				"a": 1,
				"b": 2,
				"c": 3,
				"d": 4,
			},
		},
	}

	for _, tt := range testCases {
		t.Run(tt.name, func(t *testing.T) {
			MergeMaps(tt.dst, tt.src)
			if !reflect.DeepEqual(tt.dst, tt.expected) {
				t.Errorf("got %v, want %v", tt.dst, tt.expected)
			}
		})
	}
}

func TestDurationUntilMidnight(t *testing.T) {
	testStruct := []struct {
		currentTime time.Time
		expected    time.Duration
	}{
		{
			currentTime: time.Date(2024, time.June, 18, 23, 59, 59, 0, time.UTC),
			expected:    time.Second,
		},
		{
			currentTime: time.Date(2024, time.June, 18, 0, 0, 0, 0, time.UTC),
			expected:    24 * time.Hour,
		},
		{
			currentTime: time.Date(2024, time.June, 18, 12, 0, 0, 0, time.UTC),
			expected:    12 * time.Hour,
		},
		{
			currentTime: time.Date(2024, time.June, 18, 20, 30, 0, 0, time.UTC),
			expected:    3*time.Hour + 30*time.Minute,
		},
	}

	for index, test := range testStruct {
		if output := DurationUntilMidnight(test.currentTime); output != test.expected {
			t.Errorf("Case %d: Output %v not equal to expected %v", index+1, output, test.expected)
		}
	}
}

func TestArrayHasIntersection(t *testing.T) {
	type testCase[T any] struct {
		name     string
		arr1     []T
		arr2     []T
		expected bool
	}

	// String test cases
	stringTests := []testCase[string]{
		{name: "MatchingStrings", arr1: []string{"PAN", "Aadhaar", "Passport"}, arr2: []string{"DriverLicense", "Aadhaar", "VoterID"}, expected: true},
		{name: "NoMatchingStrings", arr1: []string{"PAN", "Passport"}, arr2: []string{"DriverLicense", "VoterID"}, expected: false},
		{name: "EmptyArrays", arr1: []string{}, arr2: []string{}, expected: false},
	}

	// Integer test cases
	intTests := []testCase[int]{
		{name: "MatchingIntegers", arr1: []int{1, 2, 3}, arr2: []int{4, 5, 3}, expected: true},
		{name: "NoMatchingIntegers", arr1: []int{6, 7, 8}, arr2: []int{1, 2, 3, 4, 5}, expected: false},
	}

	// Run string tests
	for _, tt := range stringTests {
		t.Run(tt.name, func(t *testing.T) {
			result := ArrayHasIntersection(tt.arr1, tt.arr2)
			if result != tt.expected {
				t.Errorf("Expected %v but got %v", tt.expected, result)
			}
		})
	}

	// Run integer tests
	for _, tt := range intTests {
		t.Run(tt.name, func(t *testing.T) {
			result := ArrayHasIntersection(tt.arr1, tt.arr2)
			if result != tt.expected {
				t.Errorf("Expected %v but got %v", tt.expected, result)
			}
		})
	}

}

func TestMaskedPAN(t *testing.T) {
	type testStruct struct {
		maskedPan    string
		unMaskedPan  string
		expectedBool bool
		expectedErr  error
	}
	testCases := []testStruct{
		{"", "", false, errors.New("invalid pan")},
		{"AXXXXX234F", "**********", true, nil},
		{"AXXXX234F", "**********", false, errors.New("invalid pan")},
		{"AXXXXX234F", "**********  ", true, nil},
		{"AXXXXX235F", "**********  ", false, nil},
		{"      ", "**********", false, errors.New("invalid pan")},
	}

	for ind, testCase := range testCases {
		isMatched, err := MaskedPANMatch(testCase.maskedPan, testCase.unMaskedPan)
		if err != nil && err.Error() != testCase.expectedErr.Error() {
			t.Errorf("Expected error '%v' but got '%v'", testCase.expectedErr, err)
		} else if isMatched != testCase.expectedBool {
			t.Errorf("Case %d: %v not equal to expected %v\n", ind+1, isMatched, testCase.expectedBool)
		}

	}
}

func TestExtractS3ObjectKey(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected string
		wantErr  bool
	}{
		{
			name:     "Valid S3 URL",
			input:    "https://ef-middleware-prod.s3.ap-south-1.amazonaws.com/56c5b3a1-b7aa-4ea8-8db8-84024ec59eff/decentro-udyam/UDYAM-UP-03-0068150.pdf?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIAYEGHKHYBRKNAN75Z%2F20240930%2Fap-south-1%2Fs3%2Faws4_request&X-Amz-Date=20240930T054318Z&X-Amz-Expires=604800&X-Amz-SignedHeaders=host&X-Amz-Signature=3d396fd316c064c114154d8a47eef8b20d633114c11ce8e2bae794b50b296d9e",
			expected: "56c5b3a1-b7aa-4ea8-8db8-84024ec59eff/decentro-udyam/UDYAM-UP-03-0068150.pdf",
			wantErr:  false,
		},
		{
			name:     "S3 URL without query parameters",
			input:    "https://my-bucket.s3.amazonaws.com/path/to/my/file.txt",
			expected: "path/to/my/file.txt",
			wantErr:  false,
		},
		{
			name:     "S3 URL with different region",
			input:    "https://my-bucket.s3.us-west-2.amazonaws.com/another/path/file.jpg",
			expected: "another/path/file.jpg",
			wantErr:  false,
		},
		{
			name:     "S3 URL with subdirectory and no file extension",
			input:    "https://my-bucket.s3-eu-west-1.amazonaws.com/subdirectory/myfile",
			expected: "subdirectory/myfile",
			wantErr:  false,
		},
		{
			name:     "Invalid URL",
			input:    "not-a-valid-url",
			expected: "",
			wantErr:  true,
		},
		{
			name:     "Empty URL",
			input:    "",
			expected: "",
			wantErr:  true,
		},
		{
			name:     "Non-S3 URL (Google)",
			input:    "https://www.google.com/search?q=s3+url",
			expected: "",
			wantErr:  true,
		},
		{
			name:     "Non-S3 AWS URL (EC2)",
			input:    "https://ec2.amazonaws.com/something",
			expected: "",
			wantErr:  true,
		},
		{
			name:     "Non-S3 Downloadable URL",
			input:    "https://lendingapis.finbox.in/v1/services/document/download?id=5965bc1c-9693-48a8-8381-6c03a334c0cf",
			expected: "",
			wantErr:  true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := ExtractS3ObjectKey(tt.input)
			if (err != nil) != tt.wantErr {
				t.Errorf("extractS3ObjectKey() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.expected {
				t.Errorf("extractS3ObjectKey() = %v, want %v", got, tt.expected)
			}
		})
	}
}

func TestEqualSlices(t *testing.T) {
	t.Run("Equal integer slices", func(t *testing.T) {
		a := [][]int{{1, 2}, {3, 4}}
		b := [][]int{{1, 2}, {3, 4}}
		if !EqualSlices(a, b) {
			t.Error("Expected slices to be equal")
		}
	})

	t.Run("Equal but different order", func(t *testing.T) {
		a := [][]int{{1, 2}, {3, 4}}
		b := [][]int{{3, 4}, {1, 2}}
		if !EqualSlices(a, b) {
			t.Error("Expected slices to be equal regardless of order")
		}
	})

	t.Run("Different lengths", func(t *testing.T) {
		a := [][]int{{1, 2}, {3, 4}}
		b := [][]int{{1, 2}}
		if EqualSlices(a, b) {
			t.Error("Expected slices of different lengths to be unequal")
		}
	})

	t.Run("Different content", func(t *testing.T) {
		a := [][]int{{1, 2}, {3, 4}}
		b := [][]int{{1, 2}, {3, 5}}
		if EqualSlices(a, b) {
			t.Error("Expected slices with different content to be unequal")
		}
	})

	t.Run("Empty slices", func(t *testing.T) {
		a := [][]int{}
		b := [][]int{}
		if !EqualSlices(a, b) {
			t.Error("Expected empty slices to be equal")
		}
	})

	t.Run("String slices", func(t *testing.T) {
		a := [][]string{{"a", "b"}, {"c", "d"}}
		b := [][]string{{"c", "d"}, {"a", "b"}}
		if !EqualSlices(a, b) {
			t.Error("Expected string slices to be equal regardless of order")
		}
	})

	t.Run("Custom struct slices", func(t *testing.T) {
		type Person struct {
			Name string
			Age  int
		}
		a := [][]Person{
			{{Name: "Alice", Age: 25}, {Name: "Bob", Age: 30}},
			{{Name: "Charlie", Age: 35}, {Name: "Dave", Age: 40}},
		}
		b := [][]Person{
			{{Name: "Charlie", Age: 35}, {Name: "Dave", Age: 40}},
			{{Name: "Alice", Age: 25}, {Name: "Bob", Age: 30}},
		}
		if !EqualSlices(a, b) {
			t.Error("Expected struct slices to be equal regardless of order")
		}
	})

	t.Run("Nested different lengths", func(t *testing.T) {
		a := [][]int{{1, 2}, {3, 4}}
		b := [][]int{{1, 2, 3}, {4, 5, 6}}
		if EqualSlices(a, b) {
			t.Error("Expected slices with different inner lengths to be unequal")
		}
	})

	t.Run("Nil slices", func(t *testing.T) {
		var a [][]int
		var b [][]int
		if !EqualSlices(a, b) {
			t.Error("Expected nil slices to be equal")
		}
	})

	t.Run("One nil, one empty", func(t *testing.T) {
		var a [][]int
		b := [][]int{}
		if !EqualSlices(a, b) {
			t.Error("Expected nil slice to be equal to empty slice")
		}
	})
}

// TestPermutations contains all test cases for the Permutations function
func TestPermutations(t *testing.T) {
	// Helper function to check if two slices of slices are equal

	t.Run("Integer permutations", func(t *testing.T) {
		input := []int{1, 2, 3}
		expected := [][]int{
			{1, 2, 3},
			{2, 1, 3},
			{3, 1, 2},
			{1, 3, 2},
			{2, 3, 1},
			{3, 2, 1},
		}
		result := GeneratePermutations(input)
		if !EqualSlices(result, expected) {
			t.Errorf("Expected %v, got %v", expected, result)
		}
		// Check length
		if len(result) != 6 { // 3! = 6
			t.Errorf("Expected 6 permutations, got %d", len(result))
		}
	})

	t.Run("String permutations", func(t *testing.T) {
		input := []string{"a", "b"}
		expected := [][]string{
			{"a", "b"},
			{"b", "a"},
		}
		result := GeneratePermutations(input)
		if !EqualSlices(result, expected) {
			t.Errorf("Expected %v, got %v", expected, result)
		}
		// Check length
		if len(result) != 2 { // 2! = 2
			t.Errorf("Expected 2 permutations, got %d", len(result))
		}
	})

	t.Run("Empty slice", func(t *testing.T) {
		input := []int{}
		result := GeneratePermutations(input)
		if len(result) != 0 {
			t.Errorf("Expected empty result for empty input, got %v", result)
		}
	})

	t.Run("Single element", func(t *testing.T) {
		input := []int{1}
		expected := [][]int{{1}}
		result := GeneratePermutations(input)
		if !EqualSlices(result, expected) {
			t.Errorf("Expected %v, got %v", expected, result)
		}
	})

	t.Run("Custom struct permutations", func(t *testing.T) {
		type Person struct {
			Name string
		}
		input := []Person{{Name: "Alice"}, {Name: "Bob"}}
		result := GeneratePermutations(input)
		if len(result) != 2 { // 2! = 2
			t.Errorf("Expected 2 permutations, got %d", len(result))
		}
	})
}

func TestExtractJSONTags(t *testing.T) {
	// Test case structs
	type BasicStruct struct {
		Name    string `json:"name"`
		Age     int    `json:"age"`
		Private string `json:"-"`
		Ignored string
	}

	type WithOptions struct {
		Required string `json:"required,omitempty"`
		Optional string `json:"optional,omitempty"`
	}

	type EmbeddedStruct struct {
		ID string `json:"id"`
	}

	type WithEmbedded struct {
		EmbeddedStruct
		Description string `json:"description"`
	}

	type NestedStruct struct {
		Nested struct {
			Field string `json:"nested_field"`
		} `json:"nested"`
	}

	tests := []struct {
		name    string
		input   interface{}
		want    []string
		wantErr bool
	}{
		{
			name:    "basic struct",
			input:   BasicStruct{},
			want:    []string{"name", "age"},
			wantErr: false,
		},
		{
			name:    "pointer to struct",
			input:   &BasicStruct{},
			want:    []string{"name", "age"},
			wantErr: false,
		},
		{
			name:    "struct with tag options",
			input:   WithOptions{},
			want:    []string{"required", "optional"},
			wantErr: false,
		},
		{
			name:    "struct with embedded struct",
			input:   WithEmbedded{},
			want:    []string{"id", "description"},
			wantErr: false,
		},
		{
			name:    "nested struct",
			input:   NestedStruct{},
			want:    []string{"nested"},
			wantErr: false,
		},
		{
			name:    "non-struct input",
			input:   "not a struct",
			want:    nil,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := ExtractJSONTags(tt.input)

			// Check error cases
			if (err != nil) != tt.wantErr {
				t.Errorf("ExtractJSONTags() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			// For error cases, we don't need to check the results
			if tt.wantErr {
				return
			}

			// Sort both slices to ensure consistent comparison
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ExtractJSONTags() = %v, want %v", got, tt.want)
			}
		})
	}
}
func TestRemoveSpecialCharacters(t *testing.T) {
	type testStruct struct {
		input    string
		expected string
	}
	testCases := []testStruct{
		{"", ""},
		{"hello123", "hello123"},
		{"hello@123", "hello123"},
		{"hello#$%123", "hello123"},
		{"@#$%", ""},
		{"abc!@#$%^&*()def", "abcdef"},
		{"test-case", "testcase"},
		{"first_name", "firstname"},
		{"<EMAIL>", "emaildomaincom"},
		{"phone: ************", "phone*********0"},
		{"   sp a ces   ", "spaces"},
		{"αβγδε12345", "12345"}, // Only keeps alphanumeric ASCII
		{"123.456.789", "*********"},
		{"Mixed_Case_123", "MixedCase123"},
		{"!@#$%Special^&*()", "Special"},
	}

	for index, test := range testCases {
		if output := RemoveSpecialCharacters(test.input); output != test.expected {
			t.Errorf("Case %d: Output %v not equal to expected %v", index+1, output, test.expected)
		}
	}
}

func TestCheckAndCastInt64(t *testing.T) {
	tests := []struct {
		name     string
		input    any
		expected int64
		ok       bool
	}{
		{"int", int(42), 42, true},
		{"int8", int8(42), 42, true},
		{"int16", int16(42), 42, true},
		{"int32", int32(42), 42, true},
		{"int64", int64(42), 42, true},
		{"uint", uint(42), 42, true},
		{"uint8", uint8(42), 42, true},
		{"uint16", uint16(42), 42, true},
		{"uint32", uint32(42), 42, true},
		{"uint64", uint64(42), 42, true},
		{"float32", float32(42.5), 0, false},
		{"float64", float64(42.5), 0, false},
		{"string", "42", 0, false},
		{"bool", true, 0, false},
		{"nil", nil, 0, false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, ok := CheckAndCastInt64(tt.input)
			if ok != tt.ok {
				t.Errorf("CheckAndCastInt64() ok = %v, want %v", ok, tt.ok)
				return
			}
			if ok && got != tt.expected {
				t.Errorf("CheckAndCastInt64() = %v, want %v", got, tt.expected)
			}
		})
	}
}

func TestCheckAndCastFloat64(t *testing.T) {
	tests := []struct {
		name     string
		input    any
		expected float64
		ok       bool
	}{
		{"float32", float32(42.5), 42.5, true},
		{"float64", float64(42.5), 42.5, true},
		{"int", int(42), 0, false},
		{"int8", int8(42), 0, false},
		{"int16", int16(42), 0, false},
		{"int32", int32(42), 0, false},
		{"int64", int64(42), 0, false},
		{"uint", uint(42), 0, false},
		{"uint8", uint8(42), 0, false},
		{"uint16", uint16(42), 0, false},
		{"uint32", uint32(42), 0, false},
		{"uint64", uint64(42), 0, false},
		{"string", "42.5", 0, false},
		{"bool", true, 0, false},
		{"nil", nil, 0, false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, ok := CheckAndCastFloat64(tt.input)
			if ok != tt.ok {
				t.Errorf("CheckAndCastFloat64() ok = %v, want %v", ok, tt.ok)
				return
			}
			if ok && got != tt.expected {
				t.Errorf("CheckAndCastFloat64() = %v, want %v", got, tt.expected)
			}
		})
	}
}

func TestSetMapDataInPathAndCreateMap(t *testing.T) {
	tests := []struct {
		name     string
		givenMap map[string]any
		path     string
		value    any
		want     map[string]any
	}{
		{
			name:     "simple path with single level",
			givenMap: make(map[string]any),
			path:     "key",
			value:    "value",
			want: map[string]any{
				"key": "value",
			},
		},
		{
			name:     "nested path with two levels",
			givenMap: make(map[string]any),
			path:     "parent.child",
			value:    "value",
			want: map[string]any{
				"parent": map[string]any{
					"child": "value",
				},
			},
		},
		{
			name:     "nested path with three levels",
			givenMap: make(map[string]any),
			path:     "a.b.c",
			value:    "value",
			want: map[string]any{
				"a": map[string]any{
					"b": map[string]any{
						"c": "value",
					},
				},
			},
		},
		{
			name: "existing map with nested path",
			givenMap: map[string]any{
				"existing": map[string]any{
					"key": "old_value",
				},
			},
			path:  "existing.new_key",
			value: "new_value",
			want: map[string]any{
				"existing": map[string]any{
					"key":     "old_value",
					"new_key": "new_value",
				},
			},
		},
		{
			name:     "empty path",
			givenMap: make(map[string]any),
			path:     "",
			value:    "value",
			want:     make(map[string]any),
		},
		{
			name:     "path with single dot",
			givenMap: make(map[string]any),
			path:     ".",
			value:    "value",
			want:     make(map[string]any),
		},
		{
			name:     "path with multiple consecutive dots",
			givenMap: make(map[string]any),
			path:     "a..b",
			value:    "value",
			want:     make(map[string]any),
		},
		{
			name: "non-map value in path",
			givenMap: map[string]any{
				"parent": "not_a_map",
			},
			path:  "parent.child",
			value: "value",
			want: map[string]any{
				"parent": "not_a_map",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			SetMapDataInPathAndCreateMap(tt.givenMap, tt.path, tt.value)

			// Deep comparison of maps
			if AnyToJSONString(tt.givenMap) != AnyToJSONString(tt.want) {
				t.Errorf("setMapDataInPathAndCreateMap() = %v, want %v", tt.givenMap, tt.want)
			}
		})
	}
}

func TestGetPathAndValueFromMap(t *testing.T) {
	tests := []struct {
		name        string
		givenMap    map[string]any
		storageMap  map[string]any
		prefix      string
		wantStorage map[string]any
	}{
		{
			name: "simple map with no nesting",
			givenMap: map[string]any{
				"key1": "value1",
				"key2": "value2",
			},
			storageMap: make(map[string]any),
			prefix:     "",
			wantStorage: map[string]any{
				"key1": "value1",
				"key2": "value2",
			},
		},
		{
			name: "nested map with one level",
			givenMap: map[string]any{
				"parent": map[string]any{
					"child1": "value1",
					"child2": "value2",
				},
			},
			storageMap: make(map[string]any),
			prefix:     "",
			wantStorage: map[string]any{
				"parent.child1": "value1",
				"parent.child2": "value2",
			},
		},
		{
			name: "mixed nested and flat values",
			givenMap: map[string]any{
				"flat": "value1",
				"nested": map[string]any{
					"child": "value2",
				},
			},
			storageMap: make(map[string]any),
			prefix:     "",
			wantStorage: map[string]any{
				"flat":         "value1",
				"nested.child": "value2",
			},
		},
		{
			name: "deeply nested map",
			givenMap: map[string]any{
				"level1": map[string]any{
					"level2": map[string]any{
						"level3": "value",
					},
				},
			},
			storageMap: make(map[string]any),
			prefix:     "",
			wantStorage: map[string]any{
				"level1.level2.level3": "value",
			},
		},
		{
			name:        "empty map",
			givenMap:    make(map[string]any),
			storageMap:  make(map[string]any),
			prefix:      "",
			wantStorage: make(map[string]any),
		},
		{
			name:        "nil map",
			givenMap:    nil,
			storageMap:  make(map[string]any),
			prefix:      "",
			wantStorage: make(map[string]any),
		},
		{
			name: "map with non-map values",
			givenMap: map[string]any{
				"string": "value",
				"int":    42,
				"bool":   true,
				"slice":  []string{"a", "b"},
			},
			storageMap: make(map[string]any),
			prefix:     "",
			wantStorage: map[string]any{
				"string": "value",
				"int":    42,
				"bool":   true,
				"slice":  []string{"a", "b"},
			},
		},
		{
			name: "map with existing storage map",
			givenMap: map[string]any{
				"key1": "value1",
				"key2": "value2",
			},
			storageMap: map[string]any{
				"existing": "value",
			},
			prefix: "",
			wantStorage: map[string]any{
				"existing": "value",
				"key1":     "value1",
				"key2":     "value2",
			},
		},
		{
			name: "map with prefix",
			givenMap: map[string]any{
				"key1": "value1",
				"nested": map[string]any{
					"key2": "value2",
				},
			},
			storageMap: make(map[string]any),
			prefix:     "prefix",
			wantStorage: map[string]any{
				"prefix.key1":        "value1",
				"prefix.nested.key2": "value2",
			},
		},
		{
			name: "complex nested structure",
			givenMap: map[string]any{
				"user": map[string]any{
					"profile": map[string]any{
						"name": "John",
						"contact": map[string]any{
							"email": "<EMAIL>",
							"phone": "*********0",
						},
					},
					"settings": map[string]any{
						"theme": "dark",
					},
				},
			},
			storageMap: make(map[string]any),
			prefix:     "",
			wantStorage: map[string]any{
				"user.profile.name":          "John",
				"user.profile.contact.email": "<EMAIL>",
				"user.profile.contact.phone": "*********0",
				"user.settings.theme":        "dark",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			GetPathAndValueFromMap(tt.givenMap, tt.storageMap, tt.prefix)

			// Compare storage map
			if AnyToJSONString(tt.storageMap) != AnyToJSONString(tt.wantStorage) {
				t.Errorf("getPathAndValueFromMap() storage map = %v, want %v", tt.storageMap, tt.wantStorage)
			}

		})
	}
}

// Dummy struct for testing
type DummyStruct struct {
	Name    string `json:"name"`
	Email   string `json:"email"`
	Address struct {
		Street string `json:"street"`
		City   string `json:"city"`
	} `json:"address"`
	PhoneNumbers []string `json:"phoneNumbers"`
}

// TestTrimStringFields tests whether all string fields in a dummy struct are trimmed correctly.
func TestTrimStringFields(t *testing.T) {
	dummy := DummyStruct{
		Name:  " John Doe  ",
		Email: "  <EMAIL> ",
		Address: struct {
			Street string `json:"street"`
			City   string `json:"city"`
		}{
			Street: " 123 Main St  ",
			City:   " New York  ",
		},
		PhoneNumbers: []string{"  *********0  ", "  9876543210"},
	}

	expected := DummyStruct{
		Name:  "John Doe",
		Email: "<EMAIL>",
		Address: struct {
			Street string `json:"street"`
			City   string `json:"city"`
		}{
			Street: "123 Main St",
			City:   "New York",
		},
		PhoneNumbers: []string{"*********0", "9876543210"},
	}

	// Apply trimming function
	TrimStringFields(&dummy)

	// Compare results
	if !reflect.DeepEqual(dummy, expected) {
		t.Errorf("TrimStringFields() failed.\nExpected: %+v\nGot: %+v", expected, dummy)
	}
}

func TestParsePercentage(t *testing.T) {
	type args struct {
		percent string
	}
	tests := []struct {
		name string
		args args
		want float64
	}{
		{
			name: "successWithPercentSymbol",
			args: args{percent: "54%"},
			want: 54,
		},
		{
			name: "successWithOutPercentSymbol",
			args: args{percent: "54"},
			want: 54,
		},
		{
			name: "successWithFloat",
			args: args{percent: "54.25"},
			want: 54.25,
		},
		{
			name: "successWithFloatWithPercent",
			args: args{percent: "54.25%"},
			want: 54.25,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := ParsePercentage(tt.args.percent); got != tt.want {
				t.Errorf("ParsePercentage() = %v, want %v", got, tt.want)
			}
		})
	}
}

type testStruct struct {
	CompanyName     string   `json:"companyName"`
	CompanyCode     string   `json:"companyCode"`
	CompanyCategory string   `json:"companyCategory"`
	CompanyDomain   []string `json:"companyDomain"`
	Segment         string   `json:"segment"`
}

func TestRemoveDuplicatesByKey(t *testing.T) {
	type args[T any] struct {
		input []T
		key   string
	}
	type testCase[T any] struct {
		name string
		args args[T]
		want []T
	}
	tests := []testCase[testStruct]{
		{
			name: "Remove duplicates by CompanyName",
			args: args[testStruct]{
				input: []testStruct{
					{
						CompanyName:     "Acme Corp",
						CompanyCode:     "AC123",
						CompanyCategory: "Tech",
						CompanyDomain:   []string{"acme.com"},
						Segment:         "B2B",
					},
					{
						CompanyName:     "Beta Ltd",
						CompanyCode:     "BL456",
						CompanyCategory: "Finance",
						CompanyDomain:   []string{"beta.com"},
						Segment:         "B2C",
					},
					{
						CompanyName:     "Acme Corp", // Duplicate
						CompanyCode:     "AC789",
						CompanyCategory: "Tech",
						CompanyDomain:   []string{"acmecorp.com"},
						Segment:         "B2B",
					},
				},
				key: "CompanyName",
			},
			want: []testStruct{
				{
					CompanyName:     "Acme Corp",
					CompanyCode:     "AC123",
					CompanyCategory: "Tech",
					CompanyDomain:   []string{"acme.com"},
					Segment:         "B2B",
				},
				{
					CompanyName:     "Beta Ltd",
					CompanyCode:     "BL456",
					CompanyCategory: "Finance",
					CompanyDomain:   []string{"beta.com"},
					Segment:         "B2C",
				},
			},
		},
		{
			name: "No duplicates",
			args: args[testStruct]{
				input: []testStruct{
					{CompanyName: "One"}, {CompanyName: "Two"}, {CompanyName: "Three"},
				},
				key: "CompanyName",
			},
			want: []testStruct{
				{CompanyName: "One"}, {CompanyName: "Two"}, {CompanyName: "Three"},
			},
		},
		{
			name: "All duplicates",
			args: args[testStruct]{
				input: []testStruct{
					{CompanyName: "X"}, {CompanyName: "X"}, {CompanyName: "X"},
				},
				key: "CompanyName",
			},
			want: []testStruct{
				{CompanyName: "X"},
			},
		},
		{
			name: "Empty input",
			args: args[testStruct]{
				input: []testStruct{},
				key:   "CompanyName",
			},
			want: []testStruct{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := RemoveDuplicatesByKey(tt.args.input, tt.args.key); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("RemoveDuplicatesByKey() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestGetBestFuzzyMatch(t *testing.T) {
	type testStruct struct {
		name          string
		target        string
		choices       []string
		expectedMatch string
		expectedScore int
		minScore      int // minimum acceptable score for cases where exact score may vary
	}

	tests := []testStruct{
		// Test case: Empty choices slice
		{
			name:          "empty choices slice",
			target:        "hello",
			choices:       []string{},
			expectedMatch: "",
			expectedScore: 0,
		},
		// Test case: Exact match
		{
			name:          "exact match",
			target:        "apple",
			choices:       []string{"banana", "apple", "orange"},
			expectedMatch: "apple",
			expectedScore: 100,
		},
		// Test case: Close match
		{
			name:          "close match - single typo",
			target:        "appel",
			choices:       []string{"banana", "apple", "orange"},
			expectedMatch: "apple",
			minScore:      80, // fuzzy matching should score high for single typo
		},
		// Test case: Case insensitive match
		{
			name:          "case insensitive match",
			target:        "APPLE",
			choices:       []string{"banana", "apple", "orange"},
			expectedMatch: "apple",
			minScore:      90, // case differences should still score high
		},
		// Test case: Partial match
		{
			name:          "partial match",
			target:        "app",
			choices:       []string{"banana", "apple", "orange", "application"},
			expectedMatch: "apple", // or "application" depending on fuzzy algorithm
			minScore:      60,      // partial matches should have reasonable scores
		},
		// Test case: No good match
		{
			name:          "no good match",
			target:        "xyz",
			choices:       []string{"banana", "apple", "orange"},
			expectedMatch: "",
			minScore:      0, // score should be very low
		},
		// Test case: Multiple similar matches
		{
			name:          "multiple similar matches",
			target:        "john",
			choices:       []string{"jon", "johnny", "johnson", "jane"},
			expectedMatch: "jon", // closest match
			minScore:      80,
		},
		// Test case: Empty target string
		{
			name:          "empty target string",
			target:        "",
			choices:       []string{"banana", "apple", "orange"},
			expectedMatch: "",
			minScore:      0, // score should be low for empty target
		},
		// Test case: Numbers and special characters
		{
			name:          "numbers and special characters",
			target:        "user@123",
			choices:       []string{"user@124", "admin@123", "user123"},
			expectedMatch: "user@124", // closest match
			minScore:      70,
		},
		// Test case: Single choice
		{
			name:          "single choice exact match",
			target:        "test",
			choices:       []string{"test"},
			expectedMatch: "test",
			expectedScore: 100,
		},
		// Test case: Single choice no match
		{
			name:          "single choice no match",
			target:        "hello",
			choices:       []string{"world"},
			expectedMatch: "",
			minScore:      0, // should return the only choice with low score
		},
		// Test case: Long strings
		{
			name:          "long strings",
			target:        "the quick brown fox jumps over the lazy dog",
			choices:       []string{"the quick brown fox jumps over the lazy cat", "hello world", "completely different text"},
			expectedMatch: "the quick brown fox jumps over the lazy cat",
			minScore:      90, // should be very similar
		},
		// Test case: Unicode characters
		{
			name:          "unicode characters",
			target:        "café",
			choices:       []string{"cafe", "coffee", "tea"},
			expectedMatch: "cafe", // closest match to unicode version
			minScore:      80,
		},
		// Test case: Mixed case and spaces
		{
			name:          "mixed case and spaces",
			target:        "New York",
			choices:       []string{"new york", "NEW YORK", "Los Angeles", "newyork"},
			expectedMatch: "new york", // should handle case and space differences
			minScore:      90,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotMatch, gotScore := GetBestFuzzyMatch(tt.target, tt.choices)

			// Check the returned match
			if tt.expectedScore != 0 {
				// If we have an exact expected score, check it
				if gotMatch != tt.expectedMatch || gotScore != tt.expectedScore {
					t.Errorf("GetBestFuzzyMatch() = (%v, %v), want (%v, %v)", gotMatch, gotScore, tt.expectedMatch, tt.expectedScore)
				}
			} else {
				// If we have a minimum score threshold, check it
				if gotScore < tt.minScore {
					t.Errorf("GetBestFuzzyMatch() score = %v, want >= %v", gotScore, tt.minScore)
				}

				// For cases where we expect a specific match, verify it
				if tt.expectedMatch != "" && gotMatch != tt.expectedMatch {
					// For some cases, we might accept any reasonable match if score is good enough
					if tt.minScore > 0 && gotScore < tt.minScore {
						t.Errorf("GetBestFuzzyMatch() match = %v, want %v (or score >= %v)", gotMatch, tt.expectedMatch, tt.minScore)
					}
				}
			}

			// Verify score is within valid range
			if gotScore < 0 || gotScore > 100 {
				t.Errorf("GetBestFuzzyMatch() score = %v, want score between 0 and 100", gotScore)
			}

			// For empty choices, verify specific behavior
			if len(tt.choices) == 0 {
				if gotMatch != "" || gotScore != 0 {
					t.Errorf("GetBestFuzzyMatch() with empty choices = (%v, %v), want (\"\", 0)", gotMatch, gotScore)
				}
			}
		})
	}
}

// TestGetBestFuzzyMatchEdgeCases tests edge cases and error conditions
func TestGetBestFuzzyMatchEdgeCases(t *testing.T) {
	type testStruct struct {
		name          string
		target        string
		choices       []string
		expectedMatch string
		expectedScore int
	}

	tests := []testStruct{
		// Test with nil choices (should be handled same as empty slice)
		{
			name:          "nil choices",
			target:        "test",
			choices:       nil,
			expectedMatch: "",
			expectedScore: 0,
		},
		// Test with choices containing empty strings
		{
			name:          "choices with empty strings",
			target:        "test",
			choices:       []string{"", "test", ""},
			expectedMatch: "test",
			expectedScore: 100,
		},
		// Test with all empty choices
		{
			name:          "all empty choices",
			target:        "test",
			choices:       []string{"", "", ""},
			expectedMatch: "", // should match one of the empty strings
			expectedScore: 0,  // score should be 0 for empty vs non-empty
		},
		// Test with very long target and choices
		{
			name:   "very long strings",
			target: strings.Repeat("a", 1000),
			choices: []string{
				strings.Repeat("a", 999) + "b",
				strings.Repeat("a", 1000),
				strings.Repeat("b", 1000),
			},
			expectedMatch: strings.Repeat("a", 1000),
			expectedScore: 100,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotMatch, gotScore := GetBestFuzzyMatch(tt.target, tt.choices)

			if gotMatch != tt.expectedMatch || gotScore != tt.expectedScore {
				t.Errorf("GetBestFuzzyMatch() = (%v, %v), want (%v, %v)", gotMatch, gotScore, tt.expectedMatch, tt.expectedScore)
			}
		})
	}
}

func Test_GetTypedValueFromJSON(t *testing.T) {
	t.Run("Returns value for top-level key", func(t *testing.T) {
		jsonStr := `{"name": "Ajay"}`
		val, err := GetTypedValueFromJSON[string](jsonStr, ".name")
		assert.NoError(t, err)
		assert.Equal(t, "Ajay", val)
	})

	t.Run("Returns value for nested key", func(t *testing.T) {
		jsonStr := `{"user": {"email": "<EMAIL>"}}`
		val, err := GetTypedValueFromJSON[string](jsonStr, ".user.email")
		assert.NoError(t, err)
		assert.Equal(t, "<EMAIL>", val)
	})

	t.Run("Returns error for missing key", func(t *testing.T) {
		jsonStr := `{"name": "Ajay"}`
		_, err := GetTypedValueFromJSON[string](jsonStr, ".age")
		assert.Error(t, err)
	})

	t.Run("Returns error for invalid JSON", func(t *testing.T) {
		jsonStr := `{"name": "Ajay"`
		_, err := GetTypedValueFromJSON[string](jsonStr, ".name")
		assert.Error(t, err)
	})

	t.Run("Handles numeric values correctly", func(t *testing.T) {
		jsonStr := `{"age": 30}`
		val, err := GetTypedValueFromJSON[string](jsonStr, ".age")
		assert.NoError(t, err)
		assert.Equal(t, "30", val)
	})

	t.Run("Handles float values correctly", func(t *testing.T) {
		jsonStr := `{"height": 175.0}`
		val, err := GetTypedValueFromJSON[string](jsonStr, ".height")
		assert.NoError(t, err)
		assert.Equal(t, "175", val)
	})

	t.Run("Handles boolean values correctly", func(t *testing.T) {
		jsonStr := `{"active": true}`
		val, err := GetTypedValueFromJSON[string](jsonStr, ".active")
		assert.NoError(t, err)
		assert.Equal(t, "true", val)
	})

	t.Run("Returns error for invalid nested path", func(t *testing.T) {
		jsonStr := `{"user": "ajay"}`
		_, err := GetTypedValueFromJSON[string](jsonStr, ".user.email")
		assert.Error(t, err)
	})

	t.Run("Handles int return type", func(t *testing.T) {
		jsonStr := `{"score": 42}`
		val, err := GetTypedValueFromJSON[int](jsonStr, ".score")
		assert.NoError(t, err)
		assert.Equal(t, 42, val)
	})

	t.Run("Handles float64 return type", func(t *testing.T) {
		jsonStr := `{"amount": 99.99}`
		val, err := GetTypedValueFromJSON[float64](jsonStr, ".amount")
		assert.NoError(t, err)
		assert.Equal(t, 99.99, val)
	})

	t.Run("Handles slice return type", func(t *testing.T) {
		jsonStr := `{"items": [1, 2, 3]}`
		val, err := GetTypedValueFromJSON[[]int](jsonStr, ".items")
		assert.NoError(t, err)
		assert.Equal(t, []int{1, 2, 3}, val)
	})

	t.Run("Handles map return type", func(t *testing.T) {
		jsonStr := `{"meta": {"id": 123, "valid": true}}`
		val, err := GetTypedValueFromJSON[map[string]interface{}](jsonStr, ".meta")
		assert.NoError(t, err)
		assert.Equal(t, float64(123), val["id"]) // JSON numbers decode to float64
		assert.Equal(t, true, val["valid"])
	})
}
func TestIsValidJQTemplate(t *testing.T) {
	type testStruct struct {
		name     string
		input    string
		expected bool
	}

	tests := []testStruct{
		{
			name:     "valid JQ template",
			input:    "${.name}",
			expected: true,
		},
		{
			name:     "valid complex JQ template",
			input:    "${.user.profile.name}",
			expected: true,
		},
		{
			name:     "missing opening brace",
			input:    "name}",
			expected: false,
		},
		{
			name:     "missing closing brace",
			input:    "${name",
			expected: false,
		},
		{
			name:     "empty string",
			input:    "",
			expected: false,
		},
		{
			name:     "only braces",
			input:    "${}",
			expected: true,
		},
		{
			name:     "wrong order of braces",
			input:    "}{",
			expected: false,
		},
		{
			name:     "missing dollar sign",
			input:    "{.name}",
			expected: false,
		},
		{
			name:     "plain text",
			input:    "hello world",
			expected: false,
		},
		{
			name:     "nested template",
			input:    "${.user.${.name}}",
			expected: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := IsValidJQTemplate(tt.input)
			if result != tt.expected {
				t.Errorf("IsValidJQTemplate(%q) = %v, want %v", tt.input, result, tt.expected)
			}
		})
	}
}

func TestExtractJQTemplate(t *testing.T) {
	type testStruct struct {
		name          string
		input         string
		expectedPath  string
		expectedError bool
	}

	tests := []testStruct{
		{
			name:          "valid simple template",
			input:         "${.name}",
			expectedPath:  ".name",
			expectedError: false,
		},
		{
			name:          "valid complex template",
			input:         "${.user.profile.name}",
			expectedPath:  ".user.profile.name",
			expectedError: false,
		},
		{
			name:          "empty template",
			input:         "${}",
			expectedPath:  "",
			expectedError: false,
		},
		{
			name:          "missing opening brace",
			input:         "name}",
			expectedPath:  "",
			expectedError: true,
		},
		{
			name:          "missing closing brace",
			input:         "${name",
			expectedPath:  "",
			expectedError: true,
		},
		{
			name:          "empty string",
			input:         "",
			expectedPath:  "",
			expectedError: true,
		},
		{
			name:          "wrong order of braces",
			input:         "}{",
			expectedPath:  "",
			expectedError: true,
		},
		{
			name:          "missing dollar sign",
			input:         "{.name}",
			expectedPath:  "",
			expectedError: true,
		},
		{
			name:          "plain text",
			input:         "hello world",
			expectedPath:  "",
			expectedError: true,
		},
		{
			name:          "nested template",
			input:         "${.user.${.name}}",
			expectedPath:  ".user.${.name}",
			expectedError: false,
		},
		{
			name:          "template with spaces",
			input:         "${  .name  }",
			expectedPath:  "  .name  ",
			expectedError: false,
		},
		{
			name:          "template with special characters",
			input:         "${.user[0].name}",
			expectedPath:  ".user[0].name",
			expectedError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := ExtractJQTemplate(tt.input)
			if tt.expectedError {
				if err == nil {
					t.Errorf("ExtractJQTemplate(%q) expected error but got nil", tt.input)
				}
			} else {
				if err != nil {
					t.Errorf("ExtractJQTemplate(%q) unexpected error: %v", tt.input, err)
				}
				if result != tt.expectedPath {
					t.Errorf("ExtractJQTemplate(%q) = %q, want %q", tt.input, result, tt.expectedPath)
				}
			}
		})
	}
}
