// Package general contains general utility functions to be used across project
package general

import (
	"bytes"
	"context"
	"crypto/hmac"
	"crypto/sha1"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"encoding/xml"
	"errors"
	"fmt"
	htmltemplate "html/template"
	"image"
	"io"
	"math"
	"math/rand"
	"net/http"
	"net/url"
	"os"
	"reflect"
	"regexp"
	"sort"
	"strconv"
	"strings"
	texttemplate "text/template"
	"time"
	"unicode"

	"finbox/go-api/constants"
	"finbox/go-api/functions/logger"

	"github.com/araddon/dateparse"
	"github.com/cespare/xxhash/v2"
	"github.com/clbanning/mxj"
	"github.com/go-playground/validator/v10"
	"github.com/google/uuid"
	"github.com/itchyny/gojq"
	"github.com/mitchellh/mapstructure"
	"github.com/wamuir/go-xslt"
	"go.temporal.io/api/enums/v1"
	"golang.org/x/text/cases"
	"golang.org/x/text/language"
	"golang.org/x/text/message"

	"path/filepath"

	fuzzy "github.com/paul-mannino/go-fuzzywuzzy"
)

var log = logger.Log
var xxhash64 = xxhash.New()

// RandomIntInRange returns a random number between min and max value specified (inclusive)
func RandomIntInRange(min, max int) (int, error) {
	if max <= min || max <= 0 || min <= 0 {
		return 0, errors.New("required condition: max > min AND max > 0 AND min > 0")
	}
	rand.New(rand.NewSource(time.Now().UnixNano()))
	return min + rand.Intn(max-min), nil
}

// GetUUID generates and returns a new UUID v4 string
func GetUUID() string {
	return uuid.New().String()
}

// ParsePercentage Helper function to parse percentages
func ParsePercentage(percent string) float64 {
	val, err := strconv.ParseFloat(strings.TrimSuffix(percent, "%"), 64)
	if err != nil {
		return 0.0
	}
	return val
}

// GenerateRandomString generates and returns a random alphanumeric string (with mixed case) of size n
func GenerateRandomString(n int) string {
	rand.New(rand.NewSource(time.Now().UnixNano()))
	sb := strings.Builder{}
	sb.Grow(n)
	// A rand.Int63() generates 63 random bits, enough for letterIdxMax letters!
	for i, cache, remain := n-1, rand.Int63(), letterIdxMax; i >= 0; {
		if remain == 0 {
			cache, remain = rand.Int63(), letterIdxMax
		}
		if idx := int(cache & letterIdxMask); idx < len(letterBytes) {
			sb.WriteByte(letterBytes[idx])
			i--
		}
		cache >>= letterIdxBits
		remain--
	}
	return sb.String()
}

// InMapKeyStrValStr is func that checks for key in map of type of string
func InMapKeyStrValStr(key string, mapObj map[string]string) bool {
	if _, ok := mapObj[key]; ok {
		return true
	}
	return false
}

// GetValueFromMap retrieves a value of type T from a map, with fallback to defaultValue.
// It handles basic type conversions for int, float32, etc., to match T.
func GetValueFromMap[T any](m map[string]interface{}, key string, defaultValue T) T {
	val, ok := m[key]
	if !ok {
		return defaultValue
	}

	switch v := val.(type) {
	case T:
		return v
	case int:
		if any(defaultValue) == any(int64(0)) {
			return any(int64(v)).(T)
		}
	case int32:
		if any(defaultValue) == any(int64(0)) {
			return any(int64(v)).(T)
		}
	case int16:
		if any(defaultValue) == any(int64(0)) {
			return any(int64(v)).(T)
		}
	case float32:
		if any(defaultValue) == any(float64(0)) {
			return any(float64(v)).(T)
		}
	case float64, string, bool:
		if casted, ok := val.(T); ok {
			return casted
		}
	}

	return defaultValue
}

// InArr checks for value present in a slice / array
func InArr[T string | int | int16 | int32 | int64 | bool | enums.WorkflowExecutionStatus](val T, arr []T) bool {
	for _, v := range arr {
		if v == val {
			return true
		}
	}
	return false
}

// ArrayHasIntersection returns true if at least one element from arr1 is present in arr2.
func ArrayHasIntersection[T string | int | int16 | int32 | int64 | bool | enums.WorkflowExecutionStatus](arr1, arr2 []T) bool {
	for _, val := range arr1 {
		if InArr(val, arr2) {
			return true
		}
	}
	return false
}

// FindMapInMapArray finds a map in an array of map with a specific key value pair in it
func FindMapInMapArray(key string, val interface{}, arr []map[string]interface{}) (map[string]interface{}, int, bool) {
	for i, v := range arr {
		localVal, ok := v[key]
		if ok && localVal == val {
			return v, i, true
		}
	}
	return make(map[string]interface{}), -1, false
}

// RemoveExtraSpaces removes excess spaces from a string
func RemoveExtraSpaces(input string) string {
	return strings.TrimSpace(spaceRegex.ReplaceAllString(input, " "))
}

// RemoveAllSpaces removes all spaces from a string
func RemoveAllSpaces(input string) string {
	return strings.TrimSpace(spaceRegex.ReplaceAllString(input, ""))
}

// RemoveEmptyElements removes empty values from list
func RemoveEmptyElements(s []string) []string {
	var r []string
	for _, str := range s {
		str = RemoveExtraSpaces(str)
		if str != "" {
			r = append(r, str)
		}
	}
	return r
}

// RemoveDuplicatesAndEmpty removes duplicate strings from list
// also removes extra spaces in elements before dedup
func RemoveDuplicatesAndEmpty(s []string) []string {
	var r []string
	uniqueValues := make(map[string]bool)
	for _, str := range s {
		str = RemoveExtraSpaces(str)
		if str != "" {
			found := uniqueValues[str]
			if !found {
				uniqueValues[str] = true
				r = append(r, str)
			}
		}
	}
	return r
}

// RemoveDuplicatesByKey removes duplicates from a slice of structs based on a field name.
func RemoveDuplicatesByKey[T any](input []T, key string) []T {
	seen := make(map[interface{}]struct{})
	result := make([]T, 0)

	for _, val := range input {
		v := reflect.ValueOf(val)
		if v.Kind() == reflect.Ptr {
			v = v.Elem()
		}
		if v.Kind() != reflect.Struct {
			continue
		}

		field := v.FieldByName(key)
		if !field.IsValid() || !field.CanInterface() {
			continue
		}

		fieldVal := field.Interface()

		if _, exists := seen[fieldVal]; !exists {
			seen[fieldVal] = struct{}{}
			result = append(result, val)
		}
	}

	return result
}

// RemoveElement removes the element from array of type T passed to it
func RemoveElement[T comparable](element T, l []T) []T {
	for i, val := range l {
		if val == element {
			return append(l[:i], l[i+1:]...)
		}
	}
	return l
}

// CleanAddressLine removes unallowed characters from address
func CleanAddressLine(addressLine string) string {
	return RemoveExtraSpaces(cleanAddressRegex.ReplaceAllString(addressLine, ""))
}

// CleanColumn removes unallowed characters from address
func CleanColumn(column string) string {
	return RemoveExtraSpaces(cleanColumnRegex.ReplaceAllString(column, ""))
}

// KeepOnlyAlphaSpace removes everything except alphabets and space
func KeepOnlyAlphaSpace(text string) string {
	return RemoveExtraSpaces(keepOnlyAlphaSpaceRegex.ReplaceAllString(text, ""))
}

// GetOnlyAlphaNumUpper removes everything except alpha numeric characters and turn to upper case
func GetOnlyAlphaNumUpper(input string) string {
	return strings.ToUpper(alphabetNumericExcludeRegex.ReplaceAllString(input, ""))
}

// GetOnlyAlphaNumSpace removes everything except alpha numeric characters and space
// it also removes extra spaces, and keeps case intact
func GetOnlyAlphaNumSpace(input string) string {
	return RemoveExtraSpaces(alphabetNumSpaceExcludeRegex.ReplaceAllString(input, ""))
}

// GetOnlyNum removes everything except numeric characters.
// it also removes extra spaces
func GetOnlyNum(input string) string {
	return RemoveExtraSpaces(NumSpaceExcludeRegex.ReplaceAllString(input, ""))
}

// GetOnlyAlphaSpace removes everything except alpha characters and space
// it also removes extra spaces, and keeps case intact
func GetOnlyAlphaSpace(input string) string {
	return RemoveExtraSpaces(alphabetSpaceExcludeRegex.ReplaceAllString(input, ""))
}

// RemoveCountryCode removes the country code and spaces from input mobile string
func RemoveCountryCode(mobile string) string {
	return countryCodeRegex.ReplaceAllString(RemoveAllSpaces(mobile), "")
}

// GetStringFromTemplate constructs a string by replacing placeholders using a map and returns it
// this doesn't escapes in html, for use in HTML prefer GetHTMLFromTemplate
func GetStringFromTemplate(templateString string, data map[string]interface{}) string {
	t, err := texttemplate.New("temptemplate").Parse(templateString)
	if err != nil {
		return ""
	}
	builder := &strings.Builder{}
	_ = t.Execute(builder, data)
	return builder.String()
}

// GetHTMLFromTemplate constructs a string by replacing placeholders using a map and returns it
// this escapes in HTML so is mostly safe for rendering in browsers
func GetHTMLFromTemplate(templateString string, data map[string]interface{}) string {
	t, err := htmltemplate.New("temptemplate").Parse(templateString)
	if err != nil {
		return ""
	}
	builder := &strings.Builder{}
	_ = t.Execute(builder, data)
	return builder.String()
}

// IsNumber returns true if all chars in s are digits
// does not qualifies decimal and negative numbers
func IsNumber(s string) bool {
	for _, c := range s {
		if !unicode.IsDigit(c) {
			return false
		}
	}
	return true
}

// GetTimeStampString returns the current time in YYYY-MM-DD HH:MM:SS.MMMMMM format
func GetTimeStampString() string {
	return time.Now().Format("2006-01-02 15:04:05.000000")
}

// GetCurrentDate returns the current date in YYYY-MM-DD format
func GetCurrentDate() string {
	return time.Now().Format(constants.DateFormat)
}

// GetTimeStampPair returns the current time object as well as YYYY-MM-DD HH:MM:SS.MMMMMM format
func GetTimeStampPair() (time.Time, string) {
	date := time.Now()
	return date, date.Format("2006-01-02 15:04:05.000000")
}

// ValidatePincode validates input string for being a valid pincode
func ValidatePincode(pincode string) bool {
	return pincodeRegex.MatchString(pincode)
}

// ValidateGSTIN validates input capitalised string for being a valid GSTIN
func ValidateGSTIN(gstin string) bool {
	return gstinRegex.MatchString(gstin)
}

// ValidateCINLLPIN validates input capitalised string for being a valid CIN/LLPIN
func ValidateCINLLPIN(cinLlpin string) bool {
	return cinLLPINRegex.MatchString(cinLlpin)
}

// ValidatePAN validates input capitalised string for being a valid PAN
func ValidatePAN(pan string) bool {
	return panRegex.MatchString(pan)
}

// ValidatePersonalPAN validates input capitalised string for being a valid PAN and if its personal
func ValidatePersonalPAN(pan string) bool {
	return personalPANRegex.MatchString(pan)
}

// ValidateNonPersonalPAN validates for business PAN
func ValidateNonPersonalPAN(pan string) bool {
	isPAN := panRegex.MatchString(pan)
	if isPAN {
		return pan[3:4] != "P"
	}
	return false
}

// ValidateEmail validates an email
func ValidateEmail(email string) bool {
	return emailRegex.MatchString(email)
}

// ValidateURL validates input string for being a valid URL
func ValidateURL(str string) bool {
	u, err := url.Parse(str)
	return err == nil && u.Scheme != "" && u.Host != ""
}

// GetFirstSurName returns first and last name
func GetFirstSurName(name string) (string, string) {
	name = strings.ReplaceAll(name, ".", " ")
	name = GetOnlyAlphaSpace(name)
	var firstName string
	var surName string
	spaceIndex := strings.LastIndex(name, " ")
	if spaceIndex == -1 {
		firstName = name
		surName = ""
	} else {
		firstName = name[:spaceIndex]
		surName = name[spaceIndex+1:]
	}
	if len(firstName) < 2 && len(surName) >= 2 {
		// swap first and last name
		firstName, surName = surName, firstName
	}
	return firstName, surName
}

// GetFirstMiddleLastName is used to get first middle and last name from full name
func GetFirstMiddleLastName(name string, stripSalutation bool) (string, string, string) {
	if stripSalutation {
		name = strings.ToLower(name)
		salutationPrefixes := []string{"mr.", "mrs.", "ms.", "miss.", "mr", "mrs", "ms", "miss"}
		for _, salutation := range salutationPrefixes {
			name = strings.TrimPrefix(name, salutation+" ")
		}
	}
	name = strings.ReplaceAll(name, ".", " ")
	name = GetOnlyAlphaSpace(name)
	var firstName string
	var middleName string
	var lastName string
	lastIndex := strings.LastIndex(name, " ")
	firstIndex := strings.Index(name, " ")
	if firstIndex == -1 {
		firstName = name
		middleName = ""
		lastName = ""
	} else {
		firstName = name[:firstIndex]
		if lastIndex > firstIndex {
			middleName = name[firstIndex+1 : lastIndex]
		}
		lastName = name[lastIndex+1:]
	}
	if len(firstName) < 2 && len(middleName) >= 2 {
		// swap first and middle name
		firstName, middleName = middleName, firstName
	}
	if len(firstName) < 2 && len(lastName) > 0 {
		// swap first and last name
		firstName, lastName = lastName, firstName
	}
	return firstName, middleName, lastName
}

// AmountInWords returns the amount in words upto 100 crores (+ and -ve)
func AmountInWords(amount float64) string {
	if amount == 0 {
		return "zero"
	} else if amount < 0 {
		output := RemoveExtraSpaces(spellFloat(-amount))
		if output != "" {
			return fmt.Sprintf("negative %s", output)
		}
		return ""
	}
	return RemoveExtraSpaces(spellFloat(amount))
}

// FileFromURLtoBase64 retrieves file using url and converts to base64
func FileFromURLtoBase64(url string) string {
	if !ValidateURL(url) {
		return ""
	}
	resp, err := http.Get(url)
	if err != nil {
		return ""
	}
	defer resp.Body.Close()
	bytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return ""
	}
	return base64.StdEncoding.EncodeToString(bytes)
}

// FormatCurrency returns a formatted string with commas for given amount
func FormatCurrency(amount float64, showDecimal bool) string {
	p := message.NewPrinter(language.MustParse("en-IN"))
	if showDecimal {
		return p.Sprintf("%.2f", amount)
	}
	return p.Sprintf("%.0f", amount)
}

// ValidateAccountNumber checks for valid account number
func ValidateAccountNumber(accountNumber string) bool {
	if !IsNumber(accountNumber) {
		return false
	}
	return len(accountNumber) >= 9 && len(accountNumber) <= 18
}

// CalculateMedian returns median of numbers
func CalculateMedian(n []float64) float64 {
	if len(n) == 0 {
		return 0
	}
	if len(n) == 1 {
		return n[0]
	}
	sort.Float64s(n) // sort the numbers
	mNumber := len(n) / 2
	if len(n)%2 != 0 { // odd
		return n[mNumber]
	}

	return (n[mNumber-1] + n[mNumber]) / 2
}

// IsFreeMail checks whether a given email is from a free service or not
func IsFreeMail(email string) bool {
	lowerEmail := strings.ToLower(email)
	isFreeEmail, _ := regexp.MatchString(`@(live|hotmail|outlook|aol|yahoo|rocketmail|gmail|gmx|mail|inbox|icloud|aim|zoho|zohomail|yandex|rediffmail)\.`, lowerEmail)
	// also check for indian university emails
	if isFreeEmail || strings.HasSuffix(lowerEmail, ".ac.in") || strings.HasSuffix(lowerEmail, ".edu") || strings.HasSuffix(lowerEmail, ".edu.in") || strings.HasSuffix(lowerEmail, ".org") {
		return true
	}
	return false
}

// CheckWithinNDays takes input string in YYYY-MM-DD format returns true if currentDate is within +/- n days of the given date
func CheckWithinNDays(inputDateStr string, n int) bool {
	if n < 0 {
		return false
	}
	diff := GetDifferenceInDays(inputDateStr)
	if diff < 0 {
		diff = -diff // take abs
	}
	return diff <= n
}

func GetDifferenceInDays(inputDateStr string) int {
	currentDate := time.Now()
	inputDate, _ := time.ParseInLocation(
		"2006-01-02 15:04:05",
		fmt.Sprintf("%s %s", inputDateStr, time.Now().Format("15:04:05")),
		currentDate.Location())
	diff := int(currentDate.Sub(inputDate).Hours() / 24.0)
	return diff
}

// ParseStringToTime is use to convert time string to time.Time
func ParseStringToTime(timeStr string) (time.Time, error) {
	parsedTime, err := dateparse.ParseLocal(timeStr)
	if err != nil {
		return time.Time{}, err
	}

	return parsedTime, nil
}

// DaysBetween calculates the number of days between time instances startDate & endDate.
// Returns result of endDate-startDate in days.
func DaysBetween(startDate, endDate time.Time) int {
	diff := int(endDate.Sub(startDate).Hours() / 24.0)
	return diff
}

// GetDifferenceInMonths finds difference between two time objects in number of months
// assumes a >= b, in case a < b, returns 0 months
func GetDifferenceInMonths(a, b time.Time) int {
	months := 0
	month := b.Month()
	for b.Before(a) {
		b = b.Add(time.Hour * 24)
		nextMonth := b.Month()
		if nextMonth != month {
			months++
		}
		month = nextMonth
	}

	return months
}

// SDKVersionGreaterThan returns true if first argument is greater than threshold
func SDKVersionGreaterThan(sdkVersion string, thresholdVersion string) bool {
	if sdkVersion == constants.SDKVersionWeb || strings.HasPrefix(sdkVersion, "hybrid") {
		// web / hybrid is always latest
		return true
	}
	if thresholdVersion == constants.SDKVersionWeb || strings.HasPrefix(thresholdVersion, "hybrid") {
		// web / hybrid is always latest
		return false
	}
	sdkParts := strings.Split(sdkVersion, ".")
	thParts := strings.Split(thresholdVersion, ".")
	i := 0
	for i < len(sdkParts) && i < len(thParts) {
		sdkPart, _ := strconv.ParseInt(sdkParts[i], 10, 32)
		thPart, _ := strconv.ParseInt(thParts[i], 10, 32)
		if sdkPart > thPart {
			return true
		} else if sdkPart < thPart {
			return false
		}
		i++
	}
	return len(sdkParts) > len(thParts)
}

// GetInitial returns two letter initials from a word
func GetInitial(word string) string {
	word = KeepOnlyAlphaSpace(word)

	words := []rune(word)
	if len(words) < 2 {
		return ""
	}
	firstLetter := words[0]
	secondLetter := words[1]
	for i := 2; i < len(words); i++ {
		if fmt.Sprintf("%c", words[i]) == " " {
			secondLetter = words[i+1]
			break
		}
	}
	return strings.ToUpper(fmt.Sprintf("%c%c", firstLetter, secondLetter))
}

// ValidateIFSC validates if IFSC specified is valid
func ValidateIFSC(ifsc string) bool {
	return ifscRegex.MatchString(strings.ToUpper(ifsc))
}

// ValidateUUID validates a UUID string with dashes
func ValidateUUID(inputStr string) bool {
	if len(inputStr) != 36 {
		return false
	}
	_, err := uuid.Parse(inputStr)
	return err == nil
}

// CountTrue counts number of trues
func CountTrue(values ...bool) int {
	count := 0
	for _, value := range values {
		if value {
			count++
		}
	}
	return count
}

// MatchAccountNumber matches the masked account numbers with actual account number
func MatchAccountNumber(accA string, accB string) bool {
	// upper case change, and remove starting 0s
	accA = strings.TrimLeft(strings.TrimSpace(strings.ToUpper(accA)), "0")
	accB = strings.TrimLeft(strings.TrimSpace(strings.ToUpper(accB)), "0")
	if len(accA) == len(accB) {
		// direct match, including both blank string
		if accA == accB {
			return true
		}
	}
	if accA == "" || accB == "" {
		return false
	}
	regexA, wcA, invalidA := createRegexFromAcc(accA)
	regexB, wcB, invalidB := createRegexFromAcc(accB)
	if invalidA || invalidB {
		// case of use of characters other than numbers or X
		return false
	}
	switch {
	case wcA && !wcB:
		matched, _ := regexp.MatchString(regexA, accB)
		return matched
	case !wcA && wcB:
		matched, _ := regexp.MatchString(regexB, accA)
		return matched
	default:
		return regexA == regexB
	}
}

// helper function that returns a regex, whether regex contains wild card and whether input was invalid
func createRegexFromAcc(acc string) (string, bool, bool) {
	var (
		wildCard = false
		xPenDown = false
		sb       strings.Builder
	)
	accRune := []rune(acc)
	for i := 0; i < len(accRune); i++ {
		switch {
		case accRune[i] == 'X':
			if !xPenDown {
				sb.WriteString(".*")
				xPenDown = true
				wildCard = true // since wild card is present in regex
			}
		case unicode.IsDigit(accRune[i]):
			if xPenDown {
				if accRune[i] == '0' {
					continue
				}
				xPenDown = false
			}
			sb.WriteRune(accRune[i])
		default:
			// declare the string as invalid
			// also avoids regex injection
			return "", false, true
		}
	}
	return sb.String(), wildCard, false
}

// SplitSliceInEqualParts splits rowsCount into equal chunk of cutOff value provided in the input.
// example: rowsCount: 5, cutOff: 2, output: [2,2,1]
func SplitSliceInEqualParts(rowsCount int, cutOff int) []int {
	finalSlice := []int{}

	if rowsCount < 1 || cutOff < 1 {
		return []int{}
	}

	if rowsCount <= cutOff {
		finalSlice = append(finalSlice, rowsCount)
		return finalSlice
	}

	hasMore := true
	upperLimit := cutOff
	totalCount := rowsCount

	for hasMore {
		diff := totalCount - upperLimit
		if diff <= 0 {
			hasMore = false
			finalSlice = append(finalSlice, totalCount)
			totalCount = 0
		} else if diff > 0 {
			hasMore = true
			totalCount = diff
			finalSlice = append(finalSlice, upperLimit)
		}
	}
	return finalSlice
}

// ValidMAC computes the value of MAC by applying HMAC-SHA1 on message using key and compares it with provided MAC
func ValidMAC(message, key, messageMAC string) bool {
	mac := hmac.New(sha1.New, []byte(key))
	mac.Write([]byte(message))
	expectedMAC := mac.Sum(nil)
	return hex.EncodeToString(expectedMAC) == messageMAC
}

// RoundFloat does the round of float x to d digit
func RoundFloat(x float64, d int) float64 {
	return math.Round(x*(math.Pow(10, float64(d)))) / (math.Pow(10, float64(d)))
}

// MaskAadhaar masks the aadhaar number returns masked aadhaar
// and boolean indiciating whether masking was successful or not
func MaskAadhaar(aadhaar string) (string, bool) {
	if len(aadhaar) != 12 {
		return aadhaar, false
	}
	aadhaar = "XXXXXXXX" + aadhaar[8:12]
	return aadhaar, true
}

// ValidateMobile validates a moible number
func ValidateMobile(mobile string) bool {
	return mobileRegex.MatchString(mobile)
}

// UnmarshalXML unmarhals xml byte array to the struct (passed as pointer in second argument)
// unlike xml.Unmarshal, it supports utf-16 as well
func UnmarshalXML(data []byte, v any) error {
	d := xml.NewDecoder(bytes.NewReader(data))
	d.CharsetReader = identReader
	err := d.Decode(v)
	return err
}

// Title takes an input string and returns capitalised version of each words' first letter
func Title(input string) string {
	return cases.Title(language.Und, cases.NoLower).String(input)
}

// Coalesce returns first non empty string passed through parameters, if no non empty string found then it returns empty string
func Coalesce(s ...string) string {
	for i := range s {
		if s[i] != "" {
			return s[i]
		}
	}
	return ""
}

// IsValidName returns if the name contains only alphabets and spaces
func IsValidName(name string) bool {
	isValid, _ := regexp.MatchString(constants.NameRegex, name)
	return isValid
}

// ValidateAgentCode validates agentCode / partnerCode input, allows only alphabets, numbers, underscore(_) and dash(-)
func ValidateAgentCode(agentCode string) bool {
	return agentCodeRegex.MatchString(agentCode)
}

// AgeAt returns age of person as per birth day and provided date
// returns 0 if atDate passed is older than birthDate
func AgeAt(birthDate time.Time, atDate time.Time) int {
	years := atDate.Year() - birthDate.Year() // get difference in years
	birthDay := adjustedBirthday(birthDate, atDate)
	// if atDate is before birth day, then reduce the number of years calculated
	if atDate.YearDay() < birthDay {
		years -= 1
	}
	// return 0, if years become negative. Cases with atDate older than birthDate
	if years < 0 {
		return 0
	}
	return years
}

// GetJSONFromTemplate constructs a string by replacing placeholders using a map and returns it
// this escapes in HTML so is mostly safe for rendering in browsers. This V2 returns error also.
func GetJSONFromTemplate(templateString string, data map[string]interface{}) (string, error) {
	t, err := texttemplate.New("NewJSONTemplate").Parse(templateString)
	if err != nil {
		return "", err
	}
	// Apply the parameters to the template
	var populatedConfig bytes.Buffer
	err = t.Execute(&populatedConfig, data)
	if err != nil {
		return "", err
	}

	return populatedConfig.String(), nil
}

// GetArrayIntersection gets the intersection between two arrays of type T passed to it
func GetArrayIntersection[T comparable](arr1 []T, arr2 []T) []T {
	firstArrayMap := make(map[T]bool)
	result := []T{}
	for _, val := range arr1 {
		firstArrayMap[val] = true
	}
	for _, val := range arr2 {
		if firstArrayMap[val] {
			result = append(result, val)
		}
	}
	return result
}

// ValidateSalesRange returns whether input string is a valid sales range
func ValidateSalesRange(input string) bool {
	return salesRangeRegex.MatchString(input)
}

func ValidateAadharNumber(input string) bool {
	return aadharRegex.MatchString(input)
}

// AsPointer returns passed value as a pointer
func AsPointer[T int | string | bool](val T) *T {
	ptr := &val
	return ptr
}

// Min returns minimum of two values
func Min[T int | int16 | int32 | int64 | float32 | float64](a T, b T) T {
	if a < b {
		return a
	}
	return b
}

// Max returns maximum of two values
func Max[T int | int16 | int32 | int64 | float32 | float64](a T, b T) T {
	if a > b {
		return a
	}
	return b
}

// CalculateFlag takes a key (typically user id), a flag and a percentage rollout value, and returns a bool
// returns true if the feature is ramped up as per the key and ramp value
// This function will give same output for multiple calls coming with same key and ramp value
// and works good while testing a feature with large datasets
func CalculateFlag(key string, flag string, percentage int) bool {
	switch {
	case percentage <= 0:
		return false
	case percentage >= 100:
		return true
	}

	xxhash64.Reset()
	_, _ = xxhash64.WriteString(key + flag)
	hashValue := xxhash64.Sum64()
	value := hashValue % 100
	return int(value) < percentage
}

type WeightedEntities[T any] struct {
	Type   T   `json:"type"`
	Weight int `json:"weight"`
}

func WeightedRandomness[T any](weightedEntities []WeightedEntities[T]) T {
	//Roulette wheel selection algorithm
	source := rand.NewSource(time.Now().UnixNano())
	r := rand.New(source)
	totalWeight := 0

	for _, entity := range weightedEntities {
		totalWeight += entity.Weight
	}

	//Generates a random number between 0 and 1 i.e. probability of picking something
	randomNumber := r.Float32()

	probabilities := make([]float32, len(weightedEntities))

	for i, entity := range weightedEntities {
		probabilities[i] = float32(entity.Weight) / float32(totalWeight)
	}

	//Cumulative distribution probability
	cumulativeProbability := make([]float32, len(weightedEntities))
	cumulativeProbability[0] = probabilities[0]

	for i := 1; i < len(weightedEntities); i++ {
		cumulativeProbability[i] = cumulativeProbability[i-1] + probabilities[i]
	}

	//Picks the entity with probability greater than randomly generated probability
	for i, value := range cumulativeProbability {
		if value >= randomNumber {
			return weightedEntities[i].Type
		}
	}
	var zeroValue T
	return zeroValue
}

func stringToUUIDHookFunc() mapstructure.DecodeHookFunc {
	return func(f reflect.Type, t reflect.Type, data interface{}) (interface{}, error) {
		if f.Kind() != reflect.String || t != reflect.TypeOf(uuid.UUID{}) {
			return data, nil
		}

		// Convert it by parsing
		parsedUUID, err := uuid.Parse(data.(string))
		if err != nil {
			return nil, err
		}

		return parsedUUID, nil
	}
}

func DecodeToStruct(input interface{}, output interface{}) error {
	decoderConfig := &mapstructure.DecoderConfig{
		TagName: "json",
		DecodeHook: mapstructure.ComposeDecodeHookFunc(
			mapstructure.StringToTimeHookFunc(time.RFC3339Nano),
			stringToUUIDHookFunc(),
		),
		Result: &output,
	}
	decoder, err := mapstructure.NewDecoder(decoderConfig)
	if err != nil {
		log.Println(err)
		return err
	}
	err = decoder.Decode(input)
	if err != nil {
		log.Println(err)
		return err
	}
	return nil
}

func DecodeToMap(data interface{}) (map[string]interface{}, error) {
	var result map[string]interface{}

	config := &mapstructure.DecoderConfig{
		Result:  &result,
		TagName: "json", // Use json tags
	}

	decoder, err := mapstructure.NewDecoder(config)
	if err != nil {
		return nil, err
	}

	err = decoder.Decode(data)
	if err != nil {
		return nil, err
	}

	return result, nil
}

// HaverSineDistanceKms this functions is used to calculate distance between two pairs of (lat, long) values in kms.
// We assume the earth's radius as 6371.0 km
func HaverSineDistanceKms(lat1, lon1, lat2, lon2 float64) float64 {
	earthRadiusKm := 6371.0   // TODO : Move this to constants package
	lat1Rad := degToRad(lat1) // Convert latitude 1 from degrees to radians
	lon1Rad := degToRad(lon1) // Convert longitude 1 from degrees to radians
	lat2Rad := degToRad(lat2) // Convert latitude 2 from degrees to radians
	lon2Rad := degToRad(lon2) // Convert longitude 2 from degrees to radians

	deltaLat := lat2Rad - lat1Rad // Difference in latitude in radians
	deltaLon := lon2Rad - lon1Rad // Difference in longitude in radians

	// Haversine formula to calculate the central angle between two points
	a := math.Pow(math.Sin(deltaLat/2), 2) + math.Cos(lat1Rad)*math.Cos(lat2Rad)*math.Pow(math.Sin(deltaLon/2), 2)

	// Calculate the great-circle distance (along the surface of the sphere)
	c := 2 * math.Atan2(math.Sqrt(a), math.Sqrt(1-a)) // Central angle in radians

	return earthRadiusKm * c // Calculate the distance using Earth's radius and central angle
}

func ConvertStrToInt(input string) (int, error) {
	num, err := strconv.Atoi(input)

	if err != nil {
		return num, err
	}

	return num, nil
}

func extractRequestAttributes(reqObj any) (map[string]string, error) {
	attributes := make(map[string]string)
	v := validator.New()

	err := v.Struct(reqObj)
	if err != nil {
		return nil, err
	}

	types := reflect.TypeOf(reqObj)
	values := reflect.ValueOf(reqObj)

	for i := 0; i < values.NumField(); i++ {
		key := types.Field(i).Tag.Get("json")
		if key == "" {
			return nil, errors.New("JSON tag not found in given type")
		}
		log.Info(values.Field(i))
		if values.Field(i).Len() == 0 {
			continue
		}
		attributes[key] = values.Field(i).Index(0).String()
	}

	return attributes, nil
}

// ExtractRequestParams extracts the request params from URL using a struct of type T
func ExtractRequestParams[T any](params url.Values) (map[string]string, error) {

	bytes, err := json.Marshal(params)
	if err != nil {
		log.Println(err)
		panic(err)
	}

	var reqObj T
	err = json.Unmarshal(bytes, &reqObj)
	if err != nil {
		log.Println(err)
		panic(err)
	}

	return extractRequestAttributes(reqObj)
}

// ExtractRequestBody extracts the request body into a struct of type T
func ExtractRequestBody[T any](r *http.Request) (*T, error) {
	decoder := json.NewDecoder(r.Body)
	defer r.Body.Close()
	var reqObj T
	err := decoder.Decode(&reqObj)
	if err != nil {
		logger.WithRequest(r).Error(err)
		return nil, err
	}

	v := validator.New()

	err = v.Struct(reqObj)
	if err != nil {
		return nil, err
	}

	return &reqObj, err
}

var quoteEscaper = strings.NewReplacer("\\", "\\\\", `"`, "\\\"")

// EscapeQuotes escapes quotes in the given string.
// It replaces double quotes (") with \" and backslashes (\) with \\
func EscapeQuotes(s string) string {
	return quoteEscaper.Replace(s)
}

func ReplaceExceptLastFour(s string) string {
	length := len(s)
	if length <= 4 {
		return s
	}
	// Replace all characters with * except for the last 4
	return strings.Repeat("*", length-4) + s[length-4:]
}

// ParseValueJQ parses the given jq expression and returns a value using the given map
// for the non existent and nil value fields the function returns both result and error as nil
// context is mandatory for this function, as the iterator may emit infinite number of values
func ParseValueJQ(ctx context.Context, path string, mapData map[string]any) (v any, err error) {
	jqQuery, err := gojq.Parse(path)
	if err != nil {
		return nil, err
	}
	iter := jqQuery.RunWithContext(ctx, mapData)
	for {
		newVal, ok := iter.Next()
		if !ok {
			break
		}
		if err, ok = newVal.(error); ok {
			break
		}
		v = newVal
	}
	return v, err
}

// ConvertMapToCamelCase converts the map's keys from snake cases to camelCase
func ConvertMapToCamelCase(mapData map[string]any) map[string]any {
	m := map[string]any{}
	for k, v := range mapData {
		if reflect.ValueOf(v).Kind().String() == reflect.Map.String() {
			m[UnderscoresToCamelCase(k)] = ConvertMapToCamelCase(v.(map[string]any))
		} else {
			m[UnderscoresToCamelCase(k)] = mapData[k]
		}
	}
	return m
}

// UnderscoresToCamelCase converts the given string with underscore to camel case
// If the string does not contain camel case, It converts the entire string to lowercase.
func UnderscoresToCamelCase(str string) string {
	words := strings.Split(strings.ToLower(str), "_")
	var camelCaseStr strings.Builder

	for i, w := range words {
		if i == 0 {
			camelCaseStr.WriteString(w)
			continue
		}

		cappedWord := cases.Title(language.English).String(w)
		camelCaseStr.WriteString(cappedWord)
	}

	return camelCaseStr.String()
}

// ValidateUAN validates if the uan is valid
func ValidateUAN(uan string) bool {
	return uanRegex.MatchString(uan)
}

// ValidateIPv4 validates if ipv4 is valid
func ValidateIPv4(ip string) bool {
	return ipv4Regex.MatchString(ip)
}

func GenerateRandomInt(length int) int {

	rand.New(rand.NewSource(time.Now().UnixNano()))

	// Calculate the minimum and maximum values for the specified length
	min := 1
	max := 10
	for i := 1; i < length; i++ {
		min *= 10
		max *= 10
	}

	// Generate a random integer within the specified range
	return rand.Intn(max-min) + min
}

func IsValidMfaStatus(mfaStatus int, dashboardType string) bool {
	if dashboardType == constants.LenderDashboardRef {
		return InArr(mfaStatus, []int{constants.LenderDashboardMFADisabled, constants.LenderDashboardMFAEnabled, constants.LenderDashboardMFAAuthenticated, constants.LenderDashboardMFAOTPAuthenticated})
	}
	if dashboardType == constants.MasterDashboardRef {
		return InArr(mfaStatus, []int{constants.MFAStatusDisabled, constants.MFAStatusEnabled, constants.MFAStatusAuthenticated, constants.MFAStatusEmailOTPAuthenticated, constants.MFAStatusMobileOTPAuthenticated})
	}

	return false
}

func ExtractRequestParamsWithStruct[T any](params url.Values) (*T, error) {
	bytes, err := json.Marshal(params)
	if err != nil {
		log.Errorln(err)
		return nil, err
	}

	var reqObj T
	err = json.Unmarshal(bytes, &reqObj)
	if err != nil {
		log.Errorln(err)
		return nil, err
	}
	return &reqObj, nil
}

//	func convertJSONXMLBytes(jsonBytes []byte) ([]byte, error) {
//		mapVal, err := mxj.NewMapJson(jsonBytes)
//		if err != nil {
//			log.Errorln("error parsing JSON", err)
//			return nil, err
//		}
//		xmlVal, err := mapVal.XmlIndent(" ", "  ", "Root")
//		if err != nil {
//			log.Errorln("error converting JSON to XML", err)
//		}
//		return xmlVal, err
//	}
func convertJSONXMLBytes(jsonBytes []byte, rootElementRequiredInXml bool) ([]byte, error) {
	var (
		xmlVal []byte
		err    error
	)

	mapVal, err := mxj.NewMapJson(jsonBytes)
	if err != nil {
		log.Errorln("error parsing JSON", err)
		return nil, err
	}
	if rootElementRequiredInXml {
		xmlVal, err = mapVal.XmlIndent(" ", "  ", "Root")
	} else {
		xmlVal, err = mapVal.XmlIndent(" ", "  ")
	}
	if err != nil {
		log.Errorln("error converting JSON to XML", err)
	}
	return xmlVal, err
}

func convertXMLToHTML(xmlBytes []byte, templateBytes []byte) ([]byte, error) {
	// style is an XSLT 1.0 stylesheet, as []byte.
	xs, err := xslt.NewStylesheet(templateBytes)
	if err != nil {
		log.Errorln("error parsing XML template", err)
		return nil, err
	}
	defer xs.Close()

	// doc is an XML document to be transformed and res is the result of
	// the XSL transformation, both as []byte.
	res, err := xs.Transform(xmlBytes)
	if err != nil {
		log.Errorln("error using XML template", err)
	}
	return res, err
}

func ConvertJSONToHTML(jsonObj []byte, template []byte, rootElementRequiredInXML bool) (string, error) {
	var jsonMap interface{}
	err := json.Unmarshal(jsonObj, &jsonMap)
	if err != nil {
		log.Errorln("Error parsing json", err)
		return "", err
	}

	newMap := make(map[string]interface{})
	switch v := jsonMap.(type) {
	case map[string]interface{}:
		newMap["root"] = v
	case []interface{}:
		newMap["root"] = map[string]interface{}{
			"consumerCreditData": v,
		}
	default:
		log.Errorln("type - ", v)
		return "", errors.New("invalid type")
	}

	mapBytes, err := json.Marshal(newMap)
	if err != nil {
		log.Errorln("error handling json", err)
		return "", err
	}

	xmlBytes, err := convertJSONXMLBytes(mapBytes, rootElementRequiredInXML)
	if err != nil {
		log.Errorln("error parsing JSON", err)
		return "", err
	}

	htmlBytes, err := convertXMLToHTML(xmlBytes, template)
	if err != nil {
		log.Errorln("error converting XML to HTML", err)
		return "", err
	}

	return string(htmlBytes), nil
}

// AnyToJSONString returns the json string for a given input
// WARNING: this function ignores the error so use it with in mind.
func AnyToJSONString(a any) string {
	b, _ := json.Marshal(a)
	return string(b)
}

func DetermineImageFormat(path string) (string, error) {

	ext := filepath.Ext(path)

	switch strings.ToLower(ext) {
	case ".jpg", ".jpeg":
		return "jpeg", nil
	case ".png":
		return "png", nil
	default:
		return "", fmt.Errorf("unsupported image format: %s", ext)
	}
}

func RotateImage(img image.Image, degrees int) *image.RGBA {
	bounds := img.Bounds()
	width, height := bounds.Max.X, bounds.Max.Y

	var rotated *image.RGBA

	switch degrees {
	case 90:
		rotated = image.NewRGBA(image.Rect(0, 0, height, width))
		for x := 0; x < width; x++ {
			for y := 0; y < height; y++ {
				rotated.Set(y, width-x-1, img.At(x, y))
			}
		}
	case 180:
		rotated = image.NewRGBA(image.Rect(0, 0, width, height))
		for x := 0; x < width; x++ {
			for y := 0; y < height; y++ {
				rotated.Set(width-x-1, height-y-1, img.At(x, y))
			}
		}
	case 270:
		rotated = image.NewRGBA(image.Rect(0, 0, height, width))
		for x := 0; x < width; x++ {
			for y := 0; y < height; y++ {
				rotated.Set(height-y-1, x, img.At(x, y))
			}
		}
	default:
		// Handle other degrees if needed
	}

	return rotated

}

// GetDaysInMonth returns the no. of days of the month, given a timestamp
func GetDaysInMonth(timestamp time.Time) int {
	currentMonth := timestamp.Month()
	currentYear := timestamp.Year()

	firstOfCurrentMonth := time.Date(currentYear, currentMonth, 1, 0, 0, 0, 0, timestamp.Location())
	daysOfCurrentMonth := firstOfCurrentMonth.AddDate(0, 1, -1)

	return daysOfCurrentMonth.Day()
}

// GetDaysInNextMonth returns the number of days in the next month, given a timestamp
func GetDaysInNextMonth(timestamp time.Time) int {
	nextMonth := timestamp.Month() + 1
	nextYear := timestamp.Year()

	// Handle year increment when moving from December to January
	if nextMonth > 12 {
		nextMonth = 1
		nextYear++
	}

	firstOfNextMonth := time.Date(nextYear, nextMonth, 1, 0, 0, 0, 0, timestamp.Location())
	daysOfNextMonth := firstOfNextMonth.AddDate(0, 1, -1)

	return daysOfNextMonth.Day()
}

// Flatten converts a nested map into a single level map with all key-value pairs.
// Result is stored in `dest` parameter. `excludeKeys` are names of keys to ignore during flatten operation, i.e.
// the field and its value will be stored in dest as is from src, along with any list/array fields.
func Flatten(src map[string]any, dest map[string]any, exlcudeKeys ...string) {
	for k, v := range src {
		if InArr(k, exlcudeKeys) {
			dest[k] = v
			continue
		}

		switch child := v.(type) {
		case map[string]any:
			Flatten(child, dest, exlcudeKeys...)
		default:
			dest[k] = v
		}
	}
}

// FindOneOf takes a map and a list of possible keys for which to return a value.
// Returns the value of the first encountered key. `keys` should be passed in order of priority.
func FindOneOf(m map[string]any, keys ...string) any {
	for _, k := range keys {
		if m[k] != nil {
			return m[k]
		}
	}

	return nil
}

// ConvertMapToStruct converts a map[string]interface to a struct
func ConvertMapToStruct[T any](m map[string]interface{}) *T {
	jsonData, _ := json.Marshal(m)

	// Convert the JSON to a struct
	var structData T
	if err := json.Unmarshal(jsonData, &structData); err != nil {
		log.Errorln("There was an error mapping to struct: ", err)
		return nil
	}
	return &structData
}

// ParseISO8601 parses an ISO8601 duration string.
func ParseISO8601(duration string) (ISO8601Duration, error) {
	var match []string
	var d ISO8601Duration

	if pattern.MatchString(duration) {
		match = pattern.FindStringSubmatch(duration)
	} else {
		return d, errors.New("could not parse duration string")
	}

	for i, name := range pattern.SubexpNames() {
		part := match[i]
		if i == 0 || name == "" || part == "" {
			continue
		}

		val, err := strconv.Atoi(part)
		if err != nil {
			return d, err
		}
		switch name {
		case "year":
			d.Y = val
		case "month":
			d.M = val
		case "week":
			d.W = val
		case "day":
			d.D = val
		case "hour":
			d.TH = val
		case "minute":
			d.TM = val
		case "second":
			d.TS = val
		default:
			return d, fmt.Errorf("unknown field %s", name)
		}
	}

	return d, nil
}

func (d ISO8601Duration) TimeDuration() time.Duration {
	var dur time.Duration
	dur += (time.Duration(d.TH) * time.Hour)
	dur += (time.Duration(d.TM) * time.Minute)
	dur += (time.Duration(d.TS) * time.Second)
	return dur
}
func isValidMillisTimestamp(timestamp int64) bool {
	// Convert the timestamp to time.Time
	parsedTime := time.Unix(timestamp, 0)

	// Check for errors in parsing
	return parsedTime.After(time.Unix(0, 0))
}

// ParseMillisTimestamp converts timestamp in milliseconds to Golang time.Time
func ParseMillisTimestamp(milliseconds int64) (time.Time, error) {
	if !isValidMillisTimestamp(milliseconds) {
		return time.Time{}, errors.New("invalid Timestamp passed")
	}
	// Convert milliseconds to seconds and nanoseconds
	seconds := milliseconds / 1000
	nanoseconds := (milliseconds % 1000) * 1e6
	// Create a time.Time value
	parsedTime := time.Unix(seconds, nanoseconds)
	return parsedTime, nil
}

func SimpleDownloadToBytes(url string) (fileByte []byte, err error) {
	c := http.Client{}
	r, err := c.Get(url)
	if err != nil {
		logger.Log.Error(err, "error getting file")
		return fileByte, err
	}
	fileByte, err = io.ReadAll(r.Body)
	if err != nil {
		logger.Log.Error(err, "error reading bytes")
		return fileByte, err
	}
	return fileByte, err
}

// ConcatNonEmpty combines non-empty strings into a single string
func ConcatNonEmpty(strs ...string) string {
	result := ""
	for _, s := range strs {
		if s != "" {
			if result != "" {
				result += ","
			}
			result += s
		}
	}
	return result
}

func BuildURL(baseURL string, params ...string) (string, error) {

	u, err := url.Parse(baseURL)
	if err != nil {
		return "", err
	}

	q := u.Query()
	for i := 0; i < len(params); i += 2 {
		if i+1 < len(params) {
			q.Set(params[i], params[i+1])
		}
	}
	u.RawQuery = q.Encode()

	return u.String(), nil
}

/*
	 GenerateJSONBSQLUpdateQuery :  This generates the update query for JSONB fields which is required for updating only necessary fields in json column.
	   Eg:
		Input : ColumnName  = 'test_column'
				Value = {
					Key : "k1",
					Value: "v1"
				}
		Output: jsonb_set(test_column, '{k1}', '"v1"')

		Note: Refer test cases, for more examples
*/
func GenerateJSONBSQLUpdateQuery(input GenerateJSONBSQLUpdateQueryInput) (query string, err error) {

	query = input.ColumnName

	if len(input.JSONData) < 1 {
		return query, errors.New("invalid Json Data")
	}

	for _, metadata := range input.JSONData {

		query = fmt.Sprintf(`jsonb_set(%s, '{%s}', '"%s"')`, fmt.Sprintf(`coalesce(%s, '{}')::jsonb`, query), metadata.Key, metadata.Value)

	}

	return query, nil
}

// RemoveLeadingZero will remove all the leding zeros from a string
// like: 00089 will change to 89
func RemoveLeadingZero(s string) string {
	output := strings.TrimLeft(s, "0")

	return output
}

// DurationUntilMidnight Calculates and returns the time duration until midnight (12AM)
func DurationUntilMidnight(t time.Time) time.Duration {
	tomorrow := t.AddDate(0, 0, 1)
	targetTime := time.Date(tomorrow.Year(), tomorrow.Month(), tomorrow.Day(), 0, 0, 0, 0, time.UTC)
	durationUntilMidnight := targetTime.Sub(t)
	return durationUntilMidnight
}

/*
MergeMaps - This function merges dstination map with source map.
It merges iterativerly
eg:

	dst - {
		a: {
			b: 1,
			c: 2,
			m : {
				x : 4
			}
		}
	}

	src - {
		a: {
			b: 1,
			f: 5,
			m : {
				x : 4
			}
		}
		}
		b: {
			d : 3
		}
	}

output:

	{
		a: {
			b: 1,
			c: 2,
			f: 5,
			m : {
				x : 4
			}
		},
		b: {
			d : 3
		}
	}
*/
func MergeMaps(dst, src map[string]any) {
	for key, srcVal := range src {
		if dstVal, exists := dst[key]; exists {
			dstMap, dstIsMap := dstVal.(map[string]any)
			srcMap, srcIsMap := srcVal.(map[string]any)
			if dstIsMap && srcIsMap {
				MergeMaps(dstMap, srcMap)
			} else {
				dst[key] = srcVal
			}
		} else {
			dst[key] = srcVal
		}
	}
}

// MergeStructs -  Generic function to merge two structs
func MergeStructs(dest interface{}, src interface{}) error {

	if dest == nil || src == nil {
		return fmt.Errorf("source or destination is nil")
	}

	// Use reflect.Value to check if dest is a pointer
	destVal := reflect.ValueOf(dest)
	srcVal := reflect.ValueOf(src)

	// Ensure dest is a pointer to a struct
	if destVal.Kind() != reflect.Ptr || destVal.Elem().Kind() != reflect.Struct {
		return errors.New("destination must be a pointer to a struct")
	}

	// Ensure src is a pointer to a struct
	if srcVal.Kind() != reflect.Ptr || srcVal.Elem().Kind() != reflect.Struct {
		return errors.New("source must be a pointer to a struct")
	}

	destVal = destVal.Elem()
	srcVal = srcVal.Elem()

	for i := 0; i < srcVal.NumField(); i++ {
		srcField := srcVal.Field(i)
		srcFieldName := srcVal.Type().Field(i).Name
		destField := destVal.FieldByName(srcFieldName)

		// Copy value only if dest struct has the same field and it's not zero in the source
		if destField.IsValid() && !srcField.IsZero() {
			destField.Set(srcField)
		}
	}

	return nil
}

// IsFuzzyNameMatch ... does fuzzy match of two names on some threshold and returns false if score is less than threshold else returns true
func IsFuzzyNameMatch(name1, name2 string, threshold int) (bool, error) {
	cleanedName1 := strings.ToLower(RemoveExtraSpaces(name1))
	cleanedName2 := strings.ToLower(RemoveExtraSpaces(name2))
	if cleanedName1 == "" || cleanedName2 == "" {
		err := errors.New("name cannot be empty")
		log.Errorln(err)
		return false, err
	}
	if fuzzy.TokenSetRatio(cleanedName1, cleanedName2) < threshold {
		return false, nil
	}
	return true, nil
}

// MaskedPANMatch ... matches the first char and last 4 char of both pan and returns true if matches else returns false
func MaskedPANMatch(pan1, pan2 string) (bool, error) {
	pan1 = strings.ToUpper(RemoveExtraSpaces(pan1))
	pan2 = strings.ToUpper(RemoveExtraSpaces(pan2))

	if len(pan1) != 10 || len(pan2) != 10 {
		err := errors.New("invalid pan")
		log.Errorln(err)
		return false, err
	}

	return pan1[0] == pan2[0] && pan1[6:] == pan2[6:], nil
}

func FuzzyMatchStrings(matchWith, matchAgainst string) int {
	score := fuzzy.TokenSetRatio((strings.ToLower(RemoveExtraSpaces(matchWith))), strings.ToLower(RemoveExtraSpaces(matchAgainst)))
	return score
}

// IsContextCancelled checks with the given context weather it was cancelled or not
func IsContextCancelled(ctx context.Context) bool {
	return ctx.Err() == context.Canceled
}

// IsContextDeadline checks with the given context weather the deadline was Exceeded
func IsContextDeadline(ctx context.Context) bool {
	return ctx.Err() == context.DeadlineExceeded
}

// IsContextCancelledOrDeadline checks with the given context weather the context was cancelled
// or met with DeadlineExceeded error
func IsContextCancelledOrDeadline(ctx context.Context) bool {
	return ctx.Err() == context.Canceled || ctx.Err() == context.DeadlineExceeded
}

func CheckExpiryOnDate(expiresAt, format string) (bool, error) {
	expiryTime, err := time.Parse(format, expiresAt)
	if err != nil {
		log.Errorln(err)
		return false, err
	}
	currentDate, err := time.Parse("2006-01-02", time.Now().Format("2006-01-02"))
	if err != nil {
		log.Errorln(err)
		return false, err
	}
	if currentDate.After(expiryTime) {
		return true, nil
	}

	return false, nil

}

// GetValidateUUIDList checks if all strings in the list are valid UUIDs.
func GetValidateUUIDList(ctx context.Context, uuidList []string) []string {
	var validUUIDs []string

	for _, id := range uuidList {
		id = strings.TrimSpace(id) // Remove any extra spaces
		if _, err := uuid.Parse(id); err != nil {
			logger.WithContext(ctx).Warnf("[ValidateUUIDList] invalid UUID err: %v, id: %s", err, id)
			continue
		}
		validUUIDs = append(validUUIDs, id)
	}

	return validUUIDs
}

// ExtractS3ObjectKey extracts the object key from an S3 URL
func ExtractS3ObjectKey(s3URL string) (string, error) {
	// Parse the URL
	parsedURL, err := url.Parse(s3URL)
	if err != nil {
		return "", fmt.Errorf("failed to parse URL: %v", err)
	}

	// Check if it's an S3 URL
	if !IsS3URL(parsedURL) {
		return "", fmt.Errorf("not a valid S3 URL")
	}

	// The path part of the URL is the object key
	objectKey := parsedURL.Path

	// Remove the leading '/' if present
	objectKey = strings.TrimPrefix(objectKey, "/")

	return objectKey, nil
}

func IsS3URL(parsedURL *url.URL) bool {
	// Check if the host ends with amazonaws.com
	if !strings.HasSuffix(parsedURL.Host, ".amazonaws.com") {
		return false
	}

	// Check if the host contains s3 or is a known S3 endpoint
	return strings.Contains(parsedURL.Host, "s3") ||
		strings.Contains(parsedURL.Host, "s3-") ||
		strings.HasPrefix(parsedURL.Host, "s3.")
}

func CloneMap(m map[string]any) map[string]any {
	clonedMap := make(map[string]any)
	for key := range m {
		clonedMap[key] = m[key]
	}
	return clonedMap
}

// GeneratePermutations generates all possible permutations of a slice of any comparable type
// https://stackoverflow.com/questions/30226438/generate-all-permutations-in-go
func GeneratePermutations[T comparable](arr []T) [][]T {
	var helper func([]T, int)
	res := [][]T{}

	helper = func(arr []T, n int) {
		if n == 1 {
			tmp := make([]T, len(arr))
			copy(tmp, arr)
			res = append(res, tmp)
		} else {
			for i := 0; i < n; i++ {
				helper(arr, n-1)
				if n%2 == 1 {
					arr[i], arr[n-1] = arr[n-1], arr[i]
				} else {
					arr[0], arr[n-1] = arr[n-1], arr[0]
				}
			}
		}
	}
	helper(arr, len(arr))
	return res
}

// ArrayEquals checks if values in two arrays are equal
func ArrayEquals[T comparable](a []T, b []T) bool {
	if len(a) != len(b) {
		return false
	}
	for i, v := range a {
		if v != b[i] {
			return false
		}
	}
	return true
}

// EqualSlices checks if two slices of slices are equal.
// The order of inner slices does not matter.
func EqualSlices[T comparable](a, b [][]T) bool {
	if len(a) != len(b) {
		return false
	}
	seen := make(map[string]bool)
	// Convert each slice to string for comparison
	for _, slice := range a {
		seen[fmt.Sprintf("%v", slice)] = true
	}
	for _, slice := range b {
		if !seen[fmt.Sprintf("%v", slice)] {
			return false
		}
	}
	return true
}

// ExtractJSONTags extracts JSON tag names from any struct
// It accepts an interface{} but will return an error if the passed value is not a struct
func ExtractJSONTags(v interface{}) ([]string, error) {
	tags := make([]string, 0)

	if v == nil {
		return nil, fmt.Errorf("input is nil")
	}

	// Get the type information of the input
	t := reflect.TypeOf(v)

	// Check for nil pointer
	if t == nil {
		return nil, fmt.Errorf("input is nil")
	}

	// If a pointer is passed, get the underlying type
	if t.Kind() == reflect.Ptr {
		if v == nil {
			return nil, fmt.Errorf("nil pointer provided")
		}
		t = t.Elem()
	}

	// Check if it's a struct
	if t.Kind() != reflect.Struct {
		return nil, fmt.Errorf("input must be a struct or pointer to struct, got %v", t.Kind())
	}

	// Iterate through all fields
	for i := 0; i < t.NumField(); i++ {
		field := t.Field(i)

		// Handle embedded structs recursively
		if field.Anonymous && field.Type.Kind() == reflect.Struct {
			embeddedTags, err := ExtractJSONTags(reflect.New(field.Type).Interface())
			if err != nil {
				continue // Skip if there's an error with embedded struct
			}
			tags = append(tags, embeddedTags...)
			continue
		}

		// Get the json tag
		tag := field.Tag.Get("json")

		// Skip if no json tag
		if tag == "" {
			continue
		}

		// Handle cases where the tag has additional options (like omitempty)
		// Split by comma and take the first part which is the actual name
		tagName := strings.Split(tag, ",")[0]

		// Skip if it's a "-" (meaning the field should be ignored in JSON)
		if tagName == "-" {
			continue
		}

		tags = append(tags, tagName)
	}

	return tags, nil
}

func ParseFloat(value string) float64 {
	result, _ := strconv.ParseFloat(value, 64)
	return result
}

func ParseNullableFloat(value string) *float64 {
	parsedValue, err := strconv.ParseFloat(value, 64)
	if err != nil {
		return nil
	}
	return &parsedValue
}

func ParseInt(value string) int {
	result, _ := strconv.Atoi(value)
	return result
}

// RemoveSpecialCharacters removes all special characters and returns the cleaned string
func RemoveSpecialCharacters(input string) string {
	// Use regex to keep only alphanumeric chars
	reg := regexp.MustCompile("[^a-zA-Z0-9]+")
	return reg.ReplaceAllString(input, "")
}

// CheckAndCastInt64 ... checks if the given value is integer and returns the value as int64
func CheckAndCastInt64(value any) (int64, bool) {
	switch v := value.(type) {
	case int:
		return int64(v), true
	case int8:
		return int64(v), true
	case int16:
		return int64(v), true
	case int32:
		return int64(v), true
	case int64:
		return v, true
	case uint:
		return int64(v), true
	case uint8:
		return int64(v), true
	case uint16:
		return int64(v), true
	case uint32:
		return int64(v), true
	case uint64:
		return int64(v), true
	default:
		return 0, false
	}
}

// CheckAndCastFloat64 ... checks if the given value is float and returns the value as float64
func CheckAndCastFloat64(value any) (float64, bool) {
	switch v := value.(type) {
	case float32:
		return float64(v), true
	case float64:
		return v, true
	default:
		return 0, false
	}
}

// GetPathAndValueFromMap flattens a nested map structure by creating dot-separated paths for each value.
// It recursively traverses the map and stores all non-map values in the storageMap with their full paths as keys.
//
// Parameters:
//   - givenMap: The input map to be flattened. Can contain nested maps and various value types.
//   - storageMap: The map where flattened key-value pairs will be stored. Existing values in this map are preserved.
//   - prefix: The prefix to be added to all paths. If empty, paths start from the root level.
//
// Example:
//
//	Input givenMap:
//	{
//	  "user": {
//	    "profile": {
//	      "name": "John",
//	      "contact": {
//	        "email": "<EMAIL>"
//	      }
//	    }
//	  }
//	}
//	Result storageMap:
//	{
//	  "user.profile.name": "John",
//	  "user.profile.contact.email": "<EMAIL>"
//	}
//
// Note: The function uses reflection to check if a value is a map, which allows it to handle
// various types of map implementations while preserving the original value types.
func GetPathAndValueFromMap(givenMap map[string]any, storageMap map[string]any, prefix string) {
	buildeKey := func(prefix, key string) string {
		if prefix != "" {
			return fmt.Sprintf("%s.%s", prefix, key)
		}
		return key
	}
	for key, value := range givenMap {
		// we need to replace this check with reflection here
		reflectValue := reflect.ValueOf(value)
		if reflectValue.Kind() == reflect.Map {
			nextMap := value.(map[string]any)
			GetPathAndValueFromMap(nextMap, storageMap, buildeKey(prefix, key))
		} else {
			storageMap[buildeKey(prefix, key)] = value
		}
	}
}

// SetMapDataInPathAndCreateMap sets a value at a specified path in a map, creating nested maps as needed.
// It splits the path by dots and traverses/creates the nested structure to place the value at the final location.
//
// Parameters:
//   - givenMap: The map where the value should be set. Will be modified to include the new value.
//   - path: The dot-separated path where the value should be set (e.g., "user.profile.name").
//   - value: The value to be set at the specified path.
//
// Example:
//
//	Input:
//	givenMap = {}
//	path = "user.profile.name"
//	value = "John"
//	Result:
//	{
//	  "user": {
//	    "profile": {
//	      "name": "John"
//	    }
//	  }
//	}
//
// Edge Cases:
//   - Empty path or invalid path (contains empty segments) - function returns without modification
//   - Path segment exists but is not a map - function returns without modification
//   - Non-existent path segments - creates new maps as needed
//
// Note: The function modifies the givenMap in place and creates new maps only when necessary.
func SetMapDataInPathAndCreateMap(givenMap map[string]any, path string, value any) {
	pathSegements := strings.Split(path, ".")

	// return on empty path or invalid path
	if strings.TrimSpace(path) == "" || len(pathSegements) == 0 || InArr("", pathSegements) {
		return
	}

	firstPathSegement := pathSegements[0]
	otherPathSegements := pathSegements[1:]

	if len(otherPathSegements) == 0 {
		givenMap[firstPathSegement] = value
		return
	}

	pathValue, exists := givenMap[firstPathSegement]
	nestedMap, isMap := pathValue.(map[string]any)
	if exists && !isMap {
		return
	}
	if !exists {
		nestedMap = make(map[string]any)
		givenMap[firstPathSegement] = nestedMap
	}

	SetMapDataInPathAndCreateMap(nestedMap, strings.Join(otherPathSegements, "."), value)
}

// Trim all string fields in a struct recursively
func TrimStringFields(s any) {
	v := reflect.ValueOf(s).Elem()
	trimRecursive(v)
}

func trimRecursive(val reflect.Value) {
	switch val.Kind() {
	case reflect.Ptr:
		if !val.IsNil() {
			trimRecursive(val.Elem())
		}
	case reflect.Struct:
		for i := 0; i < val.NumField(); i++ {
			trimRecursive(val.Field(i))
		}
	case reflect.String:
		if val.CanSet() {
			val.SetString(strings.TrimSpace(val.String()))
		}
	case reflect.Slice:
		if val.Type().Elem().Kind() == reflect.String {
			// Create a new slice with trimmed values
			newSlice := make([]string, val.Len())
			for i := 0; i < val.Len(); i++ {
				newSlice[i] = strings.TrimSpace(val.Index(i).String())
			}
			val.Set(reflect.ValueOf(newSlice)) // Replace slice with trimmed values
		}
	}
}

func CamelToSnakeCase(input string) string {
	// Check if it's camelCase (contains lowercase followed by uppercase)
	matched, _ := regexp.MatchString(`[a-z][A-Z]`, input)
	if !matched {
		return input
	}

	// Convert camelCase to snake_case
	snake := regexp.MustCompile(`([a-z])([A-Z])`).ReplaceAllString(input, `${1}_${2}`)
	return snake
}

// CleanUpFiles: To be used with precaution
func CleanUpFiles(files ...string) {
	for _, file := range files {
		_ = os.Remove(file)
	}
}

// GetBestFuzzyMatch takes a string and a list of strings to match against.
// It returns the best matching string and its match score (0-100).
// If the choices slice is empty, it returns an empty string and 0.
func GetBestFuzzyMatch(target string, choices []string) (bestMatch string, score int) {
	if len(choices) == 0 {
		return "", 0
	}

	pair, err := fuzzy.ExtractOne(target, choices)
	if err != nil {
		return "", 0
	}

	return pair.Match, pair.Score
}
