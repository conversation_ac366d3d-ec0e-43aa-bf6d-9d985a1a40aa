package journeyutils

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"finbox/go-api/constants"
	"finbox/go-api/errorHandler"
	"finbox/go-api/functions/journey"
	"finbox/go-api/functions/lenders/abfl"
	"finbox/go-api/functions/logger"
	"finbox/go-api/functions/structs"
	wfconfigfunctions "finbox/go-api/functions/workflowconfig"
	"finbox/go-api/infra/temporalclient"
	"finbox/go-api/models/bankconnectdetails"
	"finbox/go-api/models/configgroups"
	"finbox/go-api/models/featureflag"
	"finbox/go-api/models/multiuserloanrelations"
	sourceentitymodel "finbox/go-api/models/sourceentity"
	"finbox/go-api/models/sourcegrouprollouts"
	"finbox/go-api/models/userjourney"
	"finbox/go-api/models/usermodulemapping"
	"finbox/go-api/models/users"
	"finbox/go-api/utils/general"
	"finbox/go-api/utils/moduleutils"
	"finbox/go-api/utils/workflowutils/tsm"
	"fmt"
	"log"

	runnerconstants "github.com/finbox-in/road-runner/constants"
	runnerutils "github.com/finbox-in/road-runner/utils"
)

// CreateJourneyWithLenders creates an user_journey with active lenders
func CreateJourneyWithLenders(userID, sourceEntityID, journeyConfigID, segment, stagingGroupID string, leadSource string) error {
	platformActiveLenders := journey.GetActiveLenders(sourceEntityID, "")
	if len(platformActiveLenders) == 0 {
		err := fmt.Errorf("no active lenders found for source: %s and userID: %s", sourceEntityID, userID)
		logger.WithUser(userID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
	}
	lenderStatusMap := make(map[string]userjourney.LenderMetadata, len(platformActiveLenders))
	for index := range platformActiveLenders {
		lenderStatusMap[platformActiveLenders[index]] = userjourney.LenderMetadata{
			Status: constants.LenderStatusActive,
		}
	}

	isUserCoApplicant, err := multiuserloanrelations.IsUserCoApplicant(userID)
	if err != nil {
		logger.WithUser(userID).Errorln(err)
		return err
	}

	var groupID string
	if stagingGroupID != "" {
		groupID = stagingGroupID

	} else if isUserCoApplicant {
		parentUserID, err := multiuserloanrelations.GetParentUserIDFromCoApplicant(userID)
		if err != nil {
			logger.WithUser(userID).Errorln(err)
			return err
		}

		groupID = userjourney.GetGroupID(parentUserID)

	} else {
		groupID = fetchGroupID(sourceEntityID, userID, segment)
		if groupID == "" {
			parentSourceID := sourceEntityID
			journey.GetParentSourceEntityID(userID, &parentSourceID)
			groupID = fetchGroupID(parentSourceID, userID, segment)
		}
	}

	logger.WithUser(userID).Printf("========> [CreateJourneyWithLenders] userID: %s, stagingGroupID: %s, groupID: %s", userID, stagingGroupID, groupID)

	err = userjourney.Create(userID, sourceEntityID, journeyConfigID, lenderStatusMap, true, groupID)
	if err != nil {
		logger.WithUser(userID).Errorln(err)
		return err
	}

	if general.ValidateUUID(groupID) {
		err = userjourney.UpdateGroupID(context.TODO(), userID, groupID)
		if err != nil {
			logger.WithUser(userID).Errorln(err)
			return err
		}
	}

	logger.WithUser(userID).Printf("[CreateJourneyWithLenders] just before TSM workflow")

	if err := startTSMWorkflowIfNeeded(context.TODO(), userID, sourceEntityID, leadSource); err != nil {
		logger.WithUser(userID).Errorln(err)
		return err
	}

	return nil
}

// startTSMWorkflowIfNeeded starts the TSM workflow if it is configured for the user and if it's not already running
func startTSMWorkflowIfNeeded(ctx context.Context, userID, sourceEntityID, leadSource string) error {
	if !tsm.ShouldInitializeTSM(userID) {
		logger.WithUser(userID).Debug("Skipping TSM Workflow initialization as it's not configured")
		return nil
	}

	logger.WithUser(userID).Infof("Starting TSM Workflow")
	_, isNewWorklow, err := wfconfigfunctions.StartTSMWorkflowFromConfig(
		ctx,
		wfconfigfunctions.StartWorkflowOption{
			Data:              make(map[string]interface{}),
			SourceEntityID:    sourceEntityID,
			UserID:            userID,
			WorkflowType:      tsm.TSM,
			UserWorkflowRowID: general.GetUUID(),
			WorkflowStatus:    constants.TemporalStatusRunning,
		},
	)
	if err != nil {
		logger.WithUser(userID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{
			"userID":         userID,
			"sourceEntityID": sourceEntityID,
			"leadSource":     leadSource,
		}, err)
		return err
	}

	// If the workflow is already running, safe to assume that module entry was done.
	if !isNewWorklow {
		logger.WithUser(userID).Infof("TSM Workflow already running, skipping module entry")
		return nil
	}

	logger.WithUser(userID).Infof("Creating module entry for user")

	// Determine the source of the user to signal the TSM workflow
	var source moduleutils.Source
	switch leadSource {
	case constants.LeadSourceAPIStack:
		source = moduleutils.SourceTypeAPIStack
	default:
		source = moduleutils.SourceTypeJourney
	}

	if err := moduleutils.CreateUserModuleMapping(ctx, nil, moduleutils.CreateUserModuleMappingOptions{
		UserID:       userID,
		ModuleName:   usermodulemapping.UserCreate,
		ModuleStatus: constants.UserModuleStatusCompleted,
		Source:       source,
	}); err != nil {
		logger.WithUser(userID).Errorln(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{
			"userID":         userID,
			"sourceEntityID": sourceEntityID,
			"leadSource":     leadSource,
		}, err)
		return err
	}

	return nil
}

func fetchGroupID(sourceEntityID, userID, segment string) string {
	// Fetch rollouts for the given sourceEntityID
	rollouts, err := sourcegrouprollouts.GetRollouts(sourceEntityID)
	if err != nil {
		logger.WithUser(userID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{
			"userID":         userID,
			"sourceEntityID": sourceEntityID,
			"segment":        segment,
		}, err)
	}

	groupID := ""
	if segment != "" {
		groupID, err = configgroups.GetGroupID(segment)
		if err != nil && err != sql.ErrNoRows {
			errorHandler.ReportToSentryWithFields(map[string]interface{}{
				"userID":  userID,
				"segment": segment,
			}, err)
		}
	} else if len(rollouts) > 0 {
		weightedEntities := make([]general.WeightedEntities[string], len(rollouts))
		builerRolloutPerc := 0
		for i, rollout := range rollouts {
			weightedEntities[i].Type = rollout.GroupID
			weightedEntities[i].Weight = rollout.RolloutPercentage
			builerRolloutPerc += rollout.RolloutPercentage
		}
		legacyRolloutPerc := 100 - builerRolloutPerc
		if legacyRolloutPerc > 0 {
			weightedEntities = append(weightedEntities, general.WeightedEntities[string]{
				Type:   "LEGACY",
				Weight: legacyRolloutPerc,
			})
		}
		groupID = general.WeightedRandomness(weightedEntities)
		if groupID == "LEGACY" {
			groupID = "" // if the groupID is LEGACY, then we don't need to set it to anything
		}
	}
	return groupID
}

// GetActiveLendersInJourney returns active lenders for a user journey
func GetActiveLendersInJourney(ctx context.Context, sourceEntityID string) map[string]userjourney.LenderMetadata {
	platformActiveLenders := journey.GetActiveLenders(sourceEntityID, "")
	if len(platformActiveLenders) == 0 {
		errorHandler.ReportToSentryWithoutRequest(fmt.Errorf("no active lenders found for source: %s", sourceEntityID))
		logger.WithContext(ctx).Warnf("[GetActiveLendersInJourney] no active lenders found for source: %s", sourceEntityID)
	}
	lenderStatusMap := make(map[string]userjourney.LenderMetadata, len(platformActiveLenders))
	for index := range platformActiveLenders {
		lenderStatusMap[platformActiveLenders[index]] = userjourney.LenderMetadata{
			Status: constants.LenderStatusActive,
		}
	}

	return lenderStatusMap
}

// GetLenders returns a map of lenders:metadata for a user journey
func GetLenders(userID string) (map[string]userjourney.LenderMetadata, error) {
	lenders, err := userjourney.GetLenders(userID)
	if err != nil {
		logger.WithUser(userID).Errorln(err)
		return lenders, err
	}
	return lenders, nil
}

// GetActiveLenders returns a list of active lenders for a user journey
func GetActiveLenders(userID string) ([]string, error) {
	activeLenders := []string{}
	lenders, err := GetLenders(userID)
	if err != nil {
		logger.WithUser(userID).Errorln(err)
		return activeLenders, err
	}
	for lender := range lenders {
		if lenders[lender].Status == constants.LenderStatusActive {
			activeLenders = append(activeLenders, lender)
		}
	}
	return activeLenders, nil
}

// RouteToLender routes a journey to a given lender & rejects other active lenders
func RouteToLender(userID, lenderID, assignmentReason string) error {
	lenderMap, err := GetLenders(userID)
	if err != nil {
		logger.WithUser(userID).Errorln(err)
		return err
	}
	for lender := range lenderMap {
		if lender != lenderID && lenderMap[lender].Status == constants.LenderStatusActive {
			lenderMap[lender] = userjourney.LenderMetadata{
				Status:          constants.LenderStatusRejected,
				RejectedAt:      general.GetTimeStampString(),
				RejectionReason: assignmentReason,
			}
		}
	}
	err = userjourney.SetLenders(userID, lenderMap)
	if err != nil {
		logger.WithUser(userID).Errorln(err)
		return err
	}
	return nil
}

// EliminateLender eliminates a given lender for a user journey
func EliminateLender(userID, lenderID, eliminationReason string) error {
	err := userjourney.SetLenderMetadata(userID, lenderID, userjourney.LenderMetadata{
		Status:          constants.LenderStatusRejected,
		RejectedAt:      general.GetTimeStampString(),
		RejectionReason: eliminationReason,
	})
	if err != nil {
		logger.WithUser(userID).Errorln(err)
		return err
	}
	return nil
}

// SelectLender selects a lender for a user journey & marks other active lenders as inactive
func SelectLender(userID, lenderID, selectionReason string) error {
	lenderMap, err := GetLenders(userID)
	if err != nil {
		logger.WithUser(userID).Errorln(err)
		return err
	}
	for lender := range lenderMap {
		if lender != lenderID && lenderMap[lender].Status == constants.LenderStatusActive {
			lenderMap[lender] = userjourney.LenderMetadata{
				Status:             constants.LenderStatusInactive,
				InactivatedAt:      general.GetTimeStampString(),
				InactivationReason: selectionReason,
			}
		}
	}
	err = userjourney.SetLenders(userID, lenderMap)
	if err != nil {
		logger.WithUser(userID).Errorln(err)
		return err
	}
	return nil
}

// ReassignFeatureFlags - reassiging feature flags whenever a user's journey is re-initiated
func ReassignFeatureFlags(userID, sourceEntityID string) {
	var flags []featureflag.SetStruct

	// New offer flow
	var offerFlowFlagValue bool
	if enable := journey.ReassignOfferFlowV2(sourceEntityID); enable {
		offerFlowFlagValue = true
		flags = append(flags, featureflag.SetStruct{
			Flag:  journey.FlagOfferFlowV2,
			Key:   userID,
			Value: offerFlowFlagValue,
		})
	}

	// Current Module flow
	// This flag should be rolled out if the offer flow v2 flag is also true as offer flow have different workflow as compared to the older flow
	if enable := journey.ReassignCurrentModuleFlow(sourceEntityID); enable {
		if offerFlowFlagValue {
			flags = append(flags, featureflag.SetStruct{
				Flag:  journey.FlagCurrentModule,
				Key:   userID,
				Value: true,
			})
		}
	}

	// KYC Service flags
	// before reassigning kyc service flags, we first unset both flags and then set them as applicable.
	// TODO: Improvise this to not rely on kyc_service_workflow feature flags.
	err := featureflag.UnsetBulk(userID, []string{journey.FlagUseKYCServiceWorkflow})
	if err != nil {
		err = fmt.Errorf("[ReassignFeatureFlags] unable to unset kyc service flags (%s, %s) for userID: %s, err: %s", journey.FlagUseKYCService, journey.FlagUseKYCServiceWorkflow, userID, err.Error())
		logger.WithUser(userID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
	}

	// KYC service flow
	if enable := journey.ReassignUseKYCService(sourceEntityID); enable {
		flags = append(flags, featureflag.SetStruct{
			Flag:  journey.FlagUseKYCService,
			Key:   userID,
			Value: true,
		})
	}

	// KYC Service workflow flow
	if enable := journey.ReassignUseKYCServiceWorkflow(userID, sourceEntityID); enable {
		flags = append(flags, featureflag.SetStruct{
			Flag:  journey.FlagUseKYCServiceWorkflow,
			Key:   userID,
			Value: true,
		})
	}

	// Digilocker as Main KYC Flow - IIFL
	if enable := journey.ReassignUseDigilockerAsMainKYC(sourceEntityID); enable {
		flags = append(flags, featureflag.SetStruct{
			Flag:  journey.FlagDigilockerMainKYC,
			Key:   userID,
			Value: true,
		})
	}

	// mdp feature flag for ABFL BL
	if enable := journey.IsABFLBLSourcing(sourceEntityID); enable {
		flags = append(flags, featureflag.SetStruct{
			Flag:  journey.FlagABFLMDPFlow,
			Key:   userID,
			Value: true,
		})
	}

	// Temporal flow
	// check this for all the source entities are tested
	temporalFlags, err := journey.GetTemporalFlags(userID, sourceEntityID, "", true)
	if err != nil {
		logger.WithUser(userID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(fmt.Errorf("error setting temporal flows for userID %s: %s", userID, err.Error()))
	}

	if enable := journey.ReassignFeatureFlagSettings(userID, sourceEntityID); enable {

		sourceEntityFeatureFlags, _ := journey.CreateAllFeatureFlagsFromSourceEntity(userID, sourceEntityID)
		if len(sourceEntityFeatureFlags) > 0 {
			flags = append(flags, sourceEntityFeatureFlags...)
		}

		// DSA's feature flag settings
		if journey.AssignParentSourceFeatureFlags(sourceEntityID) {
			parentSourceID := sourceEntityID
			journey.GetParentSourceEntityID(userID, &parentSourceID)
			if parentSourceID != sourceEntityID {
				parentSourceEntityFeatureFlags, _ := journey.CreateAllFeatureFlagsFromSourceEntity(userID, parentSourceID)
				if len(parentSourceEntityFeatureFlags) > 0 {
					flags = append(flags, parentSourceEntityFeatureFlags...)
				}
			}
		}
	}
	flags = append(flags, temporalFlags...)

	err = featureflag.SetV2(flags, nil)
	if err != nil {
		logger.WithUser(userID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(fmt.Errorf("error setting feature flags for userID %s: %s", userID, err.Error()))
	}
}

func AssignFeatureFlags(userID, leadSource, sourceEntityID string) {
	// set feature flags here
	var flags []featureflag.SetStruct
	// multi offer flow
	if enable, percentage := journey.MultiOfferRolloutPerc(sourceEntityID); enable {
		multiOfferEnabled := general.CalculateFlag(userID, journey.FlagMultiLoanOffer, percentage)
		flags = append(flags, featureflag.SetStruct{
			Flag:  journey.FlagMultiLoanOffer,
			Key:   userID,
			Value: multiOfferEnabled,
		})
	}

	// UPI AutoPay flow
	if enable, percentage := journey.UPIAutoPayRolloutPerc(sourceEntityID); enable {
		flags = append(flags, featureflag.SetStruct{
			Flag:  journey.FlagUPIAutoPay,
			Key:   userID,
			Value: general.CalculateFlag(userID, journey.FlagUPIAutoPay, percentage),
		})
	}

	// updated pan flow
	if enable, percentage := journey.UpdatedPANRolloutPerc(sourceEntityID); enable {
		flags = append(flags, featureflag.SetStruct{
			Flag:  journey.FlagUpdatedPANFlow,
			Key:   userID,
			Value: general.CalculateFlag(userID, journey.FlagUpdatedPANFlow, percentage),
		})
	}

	if journey.UseKYCServiceWorkflowFromStart(sourceEntityID) {
		flags = append(flags, featureflag.SetStruct{
			Flag:  journey.FlagUseKYCServiceWorkflow,
			Key:   userID,
			Value: true,
		})
	} else {
		// KYC service flow
		if enable, percentage := journey.UseKYCService(sourceEntityID); enable {
			flags = append(flags, featureflag.SetStruct{
				Flag:  journey.FlagUseKYCService,
				Key:   userID,
				Value: general.CalculateFlag(userID, journey.FlagUseKYCService, percentage),
			})
		}
	}

	// Digilocker as Main KYC Flow - IIFL
	if enable, percentage := journey.UseDigilockerAsMainKYC(sourceEntityID); enable {
		flags = append(flags, featureflag.SetStruct{
			Flag:  journey.FlagDigilockerMainKYC,
			Key:   userID,
			Value: general.CalculateFlag(userID, journey.FlagDigilockerMainKYC, percentage),
		})
	}

	// New sentinel BRE flow - Bureau
	if enable, percentage := journey.BureauSentinelEvalRolloutPerc(sourceEntityID); enable {
		flags = append(flags, featureflag.SetStruct{
			Flag:  journey.FlagSentinelEvalFlowBureau,
			Key:   userID,
			Value: general.CalculateFlag(userID, journey.FlagSentinelEvalFlowBureau, percentage),
		})
	}

	// New sentinel BRE flow - Bank + gst
	if enable, percentage := journey.BankingSentinelEvalRolloutPerc(sourceEntityID); enable {
		flags = append(flags, featureflag.SetStruct{
			Flag:  journey.FlagSentinelEvalFlowBank,
			Key:   userID,
			Value: general.CalculateFlag(userID, journey.FlagSentinelEvalFlowBank, percentage),
		})
	}

	// New offer flow
	var offerFlowFlagValue bool
	if enable, percentage := journey.OfferFlowV2(sourceEntityID); enable {
		offerFlowFlagValue = general.CalculateFlag(userID, journey.FlagOfferFlowV2, percentage)
		flags = append(flags, featureflag.SetStruct{
			Flag:  journey.FlagOfferFlowV2,
			Key:   userID,
			Value: offerFlowFlagValue,
		})
	}

	// Current Module flow
	// This flag should be rolled out if the offer flow v2 flag is also true as offer flow have different workflow as compared to the older flow
	if enable, percentage := journey.CurrentModuleFlow(sourceEntityID); enable {
		if offerFlowFlagValue || journey.IsMuthootCLPartner(sourceEntityID) {
			flags = append(flags, featureflag.SetStruct{
				Flag:  journey.FlagCurrentModule,
				Key:   userID,
				Value: general.CalculateFlag(userID, journey.FlagCurrentModule, percentage),
			})
		}
	}

	// New sentinel BRE flow with Multi bank (for IIFL)
	if enable, percentage := journey.SentinelEvalMultiBankRolloutPerc(sourceEntityID); enable {
		flags = append(flags, featureflag.SetStruct{
			Flag:  journey.FlagSentinelEvalMultiBankFlow,
			Key:   userID,
			Value: general.CalculateFlag(userID, journey.FlagSentinelEvalMultiBankFlow, percentage),
		})
	}

	// Temporal flow
	temporalFlags, err := journey.GetTemporalFlags(userID, sourceEntityID, "", false)
	if err != nil {
		logger.WithUser(userID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(fmt.Errorf("error setting temporal flows for userID %s: %s", userID, err.Error()))
	}
	flags = append(flags, temporalFlags...)

	// API stack journey
	if enable, percentage := journey.APIStackJourney(leadSource, sourceEntityID); enable {
		flags = append(flags, featureflag.SetStruct{
			Flag:  journey.FlagABFLAPIStack,
			Key:   userID,
			Value: general.CalculateFlag(userID, journey.FlagABFLAPIStack, percentage),
		})
	}

	if enable, percentage := journey.IsMDPFlow(sourceEntityID); enable {
		flags = append(flags, featureflag.SetStruct{
			Flag:  journey.FlagABFLMDPFlow,
			Key:   userID,
			Value: general.CalculateFlag(userID, journey.FlagABFLMDPFlow, percentage),
		})
	}

	if enable, percentage := journey.S3SyncRollout(sourceEntityID); enable {
		flags = append(flags, featureflag.SetStruct{
			Flag:  journey.FlagS3Sync,
			Key:   userID,
			Value: general.CalculateFlag(userID, journey.FlagS3Sync, percentage),
		})
	}

	// API stack: add all features enabled for the source entity
	sourceEntityFeatureFlags, _ := journey.CreateAllFeatureFlagsFromSourceEntity(userID, sourceEntityID)
	if len(sourceEntityFeatureFlags) > 0 {
		flags = append(flags, sourceEntityFeatureFlags...)
	}

	// DSA's feature flag settings
	if journey.AssignParentSourceFeatureFlags(sourceEntityID) {
		parentSourceID := sourceEntityID
		journey.GetParentSourceEntityID(userID, &parentSourceID)
		if parentSourceID != sourceEntityID {
			parentSourceEntityFeatureFlags, _ := journey.CreateAllFeatureFlagsFromSourceEntity(userID, parentSourceID)
			if len(parentSourceEntityFeatureFlags) > 0 {
				flags = append(flags, parentSourceEntityFeatureFlags...)
			}
		}
	}
	// set flags for abfl bl 1 to 15 lakhs source/dsa.
	if journey.IsABFLBLSourcing(sourceEntityID) {
		if resp, err := abfl.IsOneLakhFifteenLakhDsa(userID, sourceEntityID); err == nil && resp {
			flags = append(flags, featureflag.SetStruct{
				Flag:  journey.FlagAbflBLNonHigherLimitDSA,
				Key:   userID,
				Value: true,
			})
		}
	}

	err = featureflag.Set(flags, nil)
	if err != nil {
		logger.WithUser(userID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(fmt.Errorf("error setting feature flags for userID %s: %s", userID, err.Error()))
	}
}

// ResetUserJourney resets wait status and updates default workflow ID
func ResetUserJourney(ctx context.Context, userID string, sourceEntityID string) error {
	lenderStatusMap := GetActiveLendersInJourney(ctx, sourceEntityID)

	if !journey.IsABFLBLSourcing(sourceEntityID) && !journey.IsIIFLBLSourcing(sourceEntityID) {
		journey.GetParentSourceEntityID(userID, &sourceEntityID)
	}

	// update default workflow id
	workflowID, err := sourceentitymodel.GetDefaultWorkflowID(sourceEntityID)
	if err != nil {
		logger.WithUser(userID).Errorln(err)
		return err
	}

	lenderStatusMapBytes, err := json.Marshal(lenderStatusMap)
	if err != nil {
		logger.WithUser(userID).Errorln(err)
		return err
	}

	metadata, err := userjourney.GetUserJourneyMetadata(userID)
	if err != nil {
		logger.WithUser(userID).Errorln(err)
		return err
	}

	err = userjourney.Upsert(userID, workflowID, string(lenderStatusMapBytes))
	if err != nil {
		logger.WithUser(userID).Errorln(err)
		return err
	}

	if isPreApproved, _ := journey.IsPreApprovedJourney(sourceEntityID); isPreApproved {
		err := AssignPreapprovedWorkflows(userID, sourceEntityID)
		if err != nil {
			errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userID, "errorText": "unable to assign pre approved wf"}, err)
			return err
		}
	}

	if sourceEntityID == constants.MoneyControlID {
		err = userjourney.UpdateMetadata(userID, []general.JSONData{
			{
				Key:   userjourney.VerifiedEmail,
				Value: metadata.VerifiedEmail,
			},
			{
				Key:   userjourney.MetadataLsqLeadID,
				Value: metadata.MetadataLsqLeadID,
			},
		})
		if err != nil {
			logger.WithUser(userID).Errorln(err)
			errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userID, "errorText": "unable to update metadata"}, err)
			return err
		}
	}

	return nil
}

// ShouldWorkflowUpdate returns true for all non API stack cases. Returns false for API stack cases unless source entity is excluded.
func ShouldWorkflowUpdate(userID, sourceEntityID string) bool {
	if journey.IsAPIStackFlow(userID, sourceEntityID) {
		return general.InArr(sourceEntityID, []string{constants.ABFLUdyogPlusWebsiteBLPROD})
	}
	return true
}

func GetWorkflowIDABFL(userID, sourceEntityID string) string {
	if journey.IsAPIStackFlow(userID, sourceEntityID) && sourceEntityID == constants.ABFLUdyogPlusWebsiteBLPROD {
		return constants.WorkflowIDSkipOfferSelectionWhatsApp
	}
	return constants.WorkflowIDSkipOfferSelection
}

func GetCoAppWorkflowIDABFL(userID, sourceEntityID string, offerGenerated bool) string {
	if journey.IsAPIStackFlow(userID, sourceEntityID) && sourceEntityID == constants.ABFLUdyogPlusWebsiteBLPROD {
		if !offerGenerated {
			return constants.WorkflowIDCoApplicantNoOfferBoosterWhatsApp
		}
		return constants.WorkflowIDCoApplicantOfferBoosterWhatsApp
	}

	if !offerGenerated {
		return constants.WorkFlowIDCoApplicantNoOfferBooster
	}
	return constants.WorkflowIDCoApplicantOfferBooster
}

func GetCurrentSubmodule(userID string, workflowID, runID string) (string, error) {

	module, err := runnerutils.FindKeyByPoll(temporalclient.Client, workflowID, runID, runnerconstants.WorkflowOutput, runnerconstants.WorkflowModuleKey, 1000, 4)
	if err != nil {
		logger.WithUser(userID).Errorln(err)
		return "", err
	}
	moduleMap, _ := module.(map[string]interface{})
	subModule, _ := moduleMap[runnerconstants.WorkflowSubModuleNameTag].(string)
	return subModule, nil

}

func GetMarketplaceCurrentModulesWorkflow(ctx context.Context, parentUserID string, sourceEntityID string) string {
	bankingCompletedCount := bankconnectdetails.GetCompletedCount(parentUserID)
	if sourceEntityID == constants.ABFLMarketplaceID {
		if bankingCompletedCount > 0 {
			return constants.WorkflowABFLMarketplaceDefaultJourneyID
		}
		return constants.WorkflowABFLMarketplaceJourneyWithBankingID
	}
	return ""
}

// GetValidModuleForSignal return valid module & submodule for a given workflow signal.
// This validation also restricts a signal to be used in one given workflow only
func GetValidModuleForSignal(sourceEntityID string, signal string) structs.Module {
	switch signal {
	case constants.SignalRetriggerBoostBRE:
		switch sourceEntityID {
		case constants.ABFLMarketplaceID, constants.MoneyControlID:
			return structs.Module{
				ModuleName:    usermodulemapping.OfferSelection,
				SubModuleName: []string{constants.BankConnectSubModuleRetriggerBRE},
			}
		default:
			return structs.Module{
				ModuleName:    usermodulemapping.BankConnect,
				SubModuleName: []string{constants.BankConnectSubModuleRetriggerBRE},
			}
		}
	case constants.SignalSkipBankConnect:
		switch {
		case sourceEntityID == constants.MoneyControlID:
			return structs.Module{
				ModuleName:    usermodulemapping.OfferSelection,
				SubModuleName: []string{constants.BankConnectSubModuleBooster},
			}
		case journey.IsPFLEDUSourcing(sourceEntityID):
			return structs.Module{
				ModuleName:    usermodulemapping.BankConnect,
				SubModuleName: []string{constants.BankConnectSubModuleLandingPage, constants.BankConnectSubModuleRetry},
			}
		case journey.IsPFLSourcing(sourceEntityID):
			return structs.Module{
				ModuleName:    usermodulemapping.BankConnect,
				SubModuleName: []string{constants.BankConnectSubModuleBooster, constants.BankConnectSubModuleRetry},
			}
		default:
			return structs.Module{
				ModuleName:    usermodulemapping.BankConnect,
				SubModuleName: []string{constants.BankConnectSubModuleBooster},
			}
		}
	case constants.SignalRetryBankConnect:
		switch sourceEntityID {
		case constants.MoneyControlID:
			return structs.Module{
				ModuleName:    usermodulemapping.OfferSelection,
				SubModuleName: []string{constants.BankConnectSubModuleBCRetry},
			}
		default:
			return structs.Module{
				ModuleName:    usermodulemapping.BankConnect,
				SubModuleName: []string{constants.BankConnectSubModuleRetry},
			}
		}
	case constants.SignalRetriggerBureauBRE:
		switch {
		case sourceEntityID == constants.ABFLMarketplaceID:
			return structs.Module{
				ModuleName:    usermodulemapping.Prequalification,
				SubModuleName: []string{constants.BankConnectSubModuleRetriggerBRE},
			}
		case journey.IsABFLBLSourcing(sourceEntityID):
			return structs.Module{
				ModuleName:    usermodulemapping.Bureau,
				SubModuleName: []string{constants.BureauSubModulePreBureauRetry},
			}
		default:
			return structs.Module{
				ModuleName:    usermodulemapping.Bureau,
				SubModuleName: []string{constants.BankConnectSubModuleRetriggerBRE},
			}
		}
	case constants.SignalBoostInititated:
		switch {
		case sourceEntityID == constants.MoneyControlID:
			return structs.Module{
				ModuleName:    usermodulemapping.OfferSelection,
				SubModuleName: []string{constants.BankConnectSubModuleBooster},
			}
		default:
			return structs.Module{
				ModuleName:    usermodulemapping.BankConnect,
				SubModuleName: []string{constants.BankConnectSubModuleBooster},
			}
		}
	case constants.SignalBankConnectUploaded:
		switch {
		case sourceEntityID == constants.MoneyControlID:
			return structs.Module{
				ModuleName:    usermodulemapping.OfferSelection,
				SubModuleName: []string{constants.BankConnectSubModuleBooster, constants.BankConnectSubModuleMultiBankMandatory, constants.BankConnectSubModuleLandingPage},
			}
		default:
			return structs.Module{
				ModuleName:    usermodulemapping.BankConnect,
				SubModuleName: []string{constants.BankConnectSubModuleBooster, constants.BankConnectSubModuleLandingPage, constants.BankConnectSubModuleMultiBankMandatory},
			}
		}
	default:
		return structs.Module{}
	}
}

func AssignPreapprovedWorkflows(userID, sourceEntityID string) error {
	var newWorkflowID string
	switch sourceEntityID {
	case constants.DSAPreApprovedABFLID:
		isKYCRequired, err := users.GetPartnerDataField(userID, "kycRequired")
		if err != nil {
			log.Println(err)
		}
		if isKYCRequired == "TRUE" {
			if journey.IsTemporalFlow(userID, sourceEntityID, usermodulemapping.PersonalInfo) {
				newWorkflowID = constants.WorkflowIDPreApprovedWithKYCV2
			} else {
				newWorkflowID = constants.WorkflowIDPreApprovedWithKYC
			}
			if err = userjourney.AssignWorkflow(newWorkflowID, userID); err != nil {
				logger.WithUser(userID).Errorln(err)
				return err

			}
		} else {
			if journey.IsTemporalFlow(userID, sourceEntityID, usermodulemapping.PersonalInfo) {
				newWorkflowID = constants.WorkflowIDPreApprovedWithoutKYCV2
			} else {
				newWorkflowID = constants.WorkflowIDPreApprovedWithoutKYC
			}
			if err = userjourney.AssignWorkflow(newWorkflowID, userID); err != nil {
				logger.WithUser(userID).Errorln(err)
				return err
			}
		}
	case constants.DSAPreApprovedABFLPartnerABCID, constants.DSAPreApprovedABFLPartnerABCIDV2, constants.DSAPreApprovedABFLPartnerDSTID, constants.DSAPreApprovedABFLABG:
		if journey.IsTemporalFlow(userID, sourceEntityID, usermodulemapping.PersonalInfo) {
			newWorkflowID = constants.WorkflowIDPreApprovedABCPersonalInfo
		} else {
			newWorkflowID = constants.WorkflowIDPreApprovedABC
		}
		if err := userjourney.AssignWorkflow(newWorkflowID, userID); err != nil {
			logger.WithUser(userID).Errorln(err)
			return err
		}
	}
	return nil
}

func GetLenderStatusByUser(userID, lenderID string) (string, error) {
	var (
		err            error
		lenderMetaData map[string]userjourney.LenderMetadata
	)

	lenderMetaData, err = userjourney.GetLenders(userID)
	if err != nil {
		logger.WithUser(userID).Error(err)
		return "", err
	}

	if metaData, ok := lenderMetaData[lenderID]; ok {
		return metaData.Status, nil
	}

	return "", errors.New("lender is not assigned to this user")
}
