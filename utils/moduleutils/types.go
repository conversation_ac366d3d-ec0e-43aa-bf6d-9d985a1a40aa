package moduleutils

import (
	"database/sql"
	"finbox/go-api/models/users"
)

type Source string

const (
	SourceTypeJourney  Source = "JOURNEY"
	SourceTypeAPIStack Source = "API_STACK"
)

type CreateUserModuleMappingOptions struct {
	// User object if present will be used, otherwise will be fetched from the database
	User *users.User

	// If user object is not present, this will be used
	UserID string

	// Module name
	ModuleName string

	// Module status
	ModuleStatus int

	// Loan application ID
	LoanApplicationID sql.NullString

	// Source of the module
	Source Source
}

type startNextModuleOptions struct {
	// User object if present will be used, otherwise will be fetched from the database
	User *users.User

	// If user object is not present, this will be used
	UserID string

	// Source of the module
	Source Source
}
