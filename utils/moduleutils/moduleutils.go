// Package moduleutils contains utility functions for modules
package moduleutils

import (
	"context"
	"database/sql"
	"finbox/go-api/constants"
	"finbox/go-api/errorHandler"
	"finbox/go-api/functions/journey"
	"finbox/go-api/functions/logger"
	"finbox/go-api/functions/workflowconfig"
	"finbox/go-api/infra/db"
	"finbox/go-api/models/preselectedlender"
	"finbox/go-api/models/usermodulemapping"
	"finbox/go-api/models/users"
	"finbox/go-api/utils/general"
	"finbox/go-api/utils/workflowutils/tsm"
	"time"

	"github.com/jmoiron/sqlx"
)

var database = db.GetDB()
var log = logger.Log

// UpdateKYC or inserts KYC module based on state passed, returns error if any
func UpdateKYC(tx *sqlx.Tx, state int, userID string, loanApplicationID string, sourceEntityID string) error {
	logger.WithUser(userID).Printf("====> Inside UpdateKYC userID: %s, state: %d", userID, state)
	var moduleStatus int
	var moduleSubStatus int
	status := 1
	switch state {
	case constants.ModuleKYCRejected:
		moduleStatus = 3
		moduleSubStatus = 2
	case constants.ModuleKYCSuccess:
		moduleStatus = 1
		moduleSubStatus = 2
	case constants.ModuleKYCCompleted:
		moduleStatus = 1
		moduleSubStatus = 1
	case constants.ModuleKYCWait:
		moduleStatus = 2
		moduleSubStatus = 2
	}

	logger.WithUser(userID).Printf("====> Inside UpdateKYC userID: %s, moduleStatus: %d, moduleSubStatus: %d", userID, moduleStatus, moduleSubStatus)

	if tsm.IsTSMFlow(userID) {
		logger.WithUser(userID).Printf("====> Inside  UpdateKYC IsTSM flow userID: %s, moduleStatus: %d, moduleSubStatus: %d", userID, moduleStatus, moduleSubStatus)
		signalData := map[string]interface{}{
			"loanApplicationID": loanApplicationID,
			"subStatus":         moduleSubStatus,
			"moduleStatus":      moduleStatus,
		}
		tsmModuleStatus := moduleStatus
		skipUpdate := false
		if moduleStatus == 1 && moduleSubStatus == 2 {
			tsmModuleStatus = 2
			workflow, err := tsm.GetLastExecutedModuleForTSMWorkflow(context.Background(), userID, "")
			if err != nil {
				return err
			}
			if workflow.ModuleName == usermodulemapping.KYC &&
				workflow.ModuleStatus == tsm.TSMModuleStatusPending {
				//skipUpdate = true
				logger.WithUser(userID).Printf("====> enter this skip condition")
			}
		}
		if !skipUpdate {
			err := tsm.SendSignalWithStatus(
				context.Background(),
				userID, usermodulemapping.KYC,
				tsmModuleStatus,
				moduleSubStatus,
				"JOURNEY",
				signalData,
				nil,
			)
			if err != nil {
				log.Error(err)
				return err
			}
		}
	}

	lastModule, err := usermodulemapping.GetLast(userID)
	if err != nil && err != sql.ErrNoRows {
		log.Error(err)
		return err
	}

	if lastModule.ModuleName != "KYC" {
		query := `insert into user_module_mapping(umm_id, user_id, module_id, module_name, module_status, module_sub_status, status, created_at, created_by, loan_application_id) select uuid_generate_v4(), $1, module_id, module_name, $2, $3, $4, current_timestamp, $5, $6 from modules where module_name = $7`
		if tx != nil {
			_, err = tx.Exec(query, userID, moduleStatus, moduleSubStatus, status, userID, loanApplicationID, "KYC")
		} else {
			_, err = database.Exec(query, userID, moduleStatus, moduleSubStatus, status, userID, loanApplicationID, "KYC")
		}
		if err != nil {
			log.Error(err)
			return err
		}
	} else {
		query := "update user_module_mapping set module_status = $1, module_sub_status = $2, status = $3, updated_at = now() where user_id = $4 and module_name = $5 ;"
		if tx != nil {
			_, err = tx.Exec(query, moduleStatus, moduleSubStatus, status, userID, "KYC")
		} else {
			_, err = database.Exec(query, moduleStatus, moduleSubStatus, status, userID, "KYC")
		}
		if err != nil {
			log.Error(err)
			return err
		}
	}

	if state == constants.ModuleKYCSuccess && journey.EnablePvtLtdJourney(sourceEntityID) {
		// TODO: in future remove EnablePvtLtdJourney check to enable clients with pvt ltd journey disabled but going to kotak / tcap
		lenderID := preselectedlender.Get(userID)
		if general.InArr(lenderID, []string{constants.KotakID, constants.TataCapitalID, constants.LendingKartID}) {
			// end journey here
			err := usermodulemapping.Create(tx, userID, userID, constants.EndModule, constants.UserModuleStatusCompleted, loanApplicationID)
			if err != nil {
				logger.WithUser(userID).Error(err)
				return err
			}
		}
	}

	return nil
}

/*
startNextModuleWorkflowForAPIStack is the function which is called to start the next module workflow when the user is in a API Stack supported TSM flow
1. It is called after we have marked a module entry in TSM (sent a signal to TSM workflow)
2. We wait for that signal to be applied by checking that TSM is not processing state (lock is not acquired)
3. Once the TSM is not in processing state, we query the next modules to be executed by apistack
4. We start the next module workflow for all the next modules (currently only for API Stack since journey workflow are anyway handled using /moduleConfig API)
*/
func startNextModuleWorkflowForAPIStack(ctx context.Context, options startNextModuleOptions) error {

	if err := tsm.VerifyTSMModuleMovement(ctx, options.UserID); err != nil {
		logger.WithUser(options.UserID).Errorln(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{
			"userID": options.UserID,
		}, err)
		return err
	}

	// Denotes the filters of the next modules
	// Query all the next modules which are of transition type module and are either in pending state or completed state
	defaultFilters := tsm.NewFilterBuilder().
		WithTransitionType(tsm.TransitionTypeModule).
		WithStatus([]string{tsm.TSMModuleStatusCompleted, tsm.TSMModuleStatusPending}).
		Build()

	// // Check the next modules to be executed by journey
	// journeyFilters := tsm.NewFilterBuilder(defaultFilters...).
	// 	WithJourney().
	// 	Build()

	// Check the next modules to be executed by apistack
	apiStackFilters := tsm.NewFilterBuilder(defaultFilters...).
		WithAPIStack().
		Build()

	// _, journeyNextModules, err := tsm.GetNextTSMModuleForFilters(ctx, options.UserID, journeyFilters...)
	// if err != nil {
	// 	logger.WithUser(options.UserID).Errorln(err)
	// 	errorHandler.ReportToSentryWithFields(map[string]interface{}{
	// 		"userID":       options.UserID,
	// 		"moduleName":   options.ModuleName,
	// 		"moduleStatus": options.ModuleStatus,
	// 	}, err)
	// 	return err
	// }
	// _ = journeyNextModules

	_, apistackNextModules, err := tsm.GetNextTSMModuleForFilters(ctx, options.UserID, apiStackFilters...)
	if err != nil {
		logger.WithUser(options.UserID).Errorln(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{
			"userID": options.UserID,
		}, err)
		return err
	}

	// Start module workflows for all the next modules. We need to start different type of workflow (GenericWorkflowV4 for Journey module and GenericWorkflow for apistack modules)

	// // TODO: Skipping starting journey workflows for now, as that is already handled in `GetModuleConfigCont`
	// _ = journeyNextModules

	for _, nextModule := range apistackNextModules {
		userWorkflowID := general.GetUUID()

		if _, err := workflowconfig.StartWorkflowFromConfig(ctx, workflowconfig.StartWorkflowOption{
			UserID: options.UserID,
			Data: map[string]interface{}{
				"moduleName":     nextModule.ModuleName,
				"moduleType":     nextModule.ModuleType,
				"moduleSource":   options.Source,
				"userWorkflowID": userWorkflowID,
				"userObj":        options.User,
			},
			Metadata: map[string]interface{}{
				"moduleSource": options.Source,
			},
			SourceEntityID:    options.User.SourceEntityID,
			WorkflowType:      nextModule.ModuleName,
			UserWorkflowRowID: userWorkflowID,
			CustomTimeout:     time.Hour * 24 * 365, // 1 year
		}); err != nil {
			logger.WithUser(options.UserID).Errorln(err)
			errorHandler.ReportToSentryWithFields(map[string]interface{}{
				"userID": options.UserID,
			}, err)
			return err
		}
	}

	return nil
}

func CreateUserModuleMapping(ctx context.Context, tx *sqlx.Tx, options CreateUserModuleMappingOptions) error {
	// If user is not present, fetch it
	if options.User == nil {
		user, err := users.Get(options.UserID)
		if err != nil {
			logger.WithUser(options.UserID).Errorln(err)
			return err
		}
		options.User = &user
	}

	options.UserID = options.User.ID
	// Defaults to journey if not specified
	if options.Source == "" {
		options.Source = SourceTypeJourney
	}

	var opts []usermodulemapping.Option
	opts = append(opts, usermodulemapping.WithSignalSource(string(options.Source)))

	if err := usermodulemapping.Create(tx, options.UserID, options.UserID, options.ModuleName, options.ModuleStatus, options.LoanApplicationID.String, opts...); err != nil {
		logger.WithUser(options.UserID).Errorln(err)
		return err
	}

	if tsm.IsAPIStackSupportedTSMFlow(options.UserID) {
		if err := startNextModuleWorkflowForAPIStack(ctx, startNextModuleOptions{
			UserID: options.UserID,
			Source: options.Source,
			User:   options.User,
		}); err != nil {
			logger.WithUser(options.UserID).Errorln(err)
			errorHandler.ReportToSentryWithFields(map[string]interface{}{
				"userID":       options.UserID,
				"moduleName":   options.ModuleName,
				"moduleStatus": options.ModuleStatus,
			}, err)
			return err
		}
	}

	return nil
}
