package convert

import (
	"bytes"
	"context"
	"finbox/go-api/errorHandler"
	"finbox/go-api/functions/logger"
	"finbox/go-api/infra/s3"
)

/*
HTMLToPDF converts a given html file (specified by a target s3 object key)
to pdf file and uploads to s3 at specified target object key
and returns whether the call was successful
*/
func HTMLToPDF(sourceObjectKey string, targetObjectKey string) bool {
	// get byte array from s3
	buf := new(bytes.Buffer)
	reader := s3.GetFileStream(sourceObjectKey)
	buf.ReadFrom(*reader)

	// call gotenberg service (this also uploads to s3)
	return gotenberg791ConvertHTMLToPDF(buf.Bytes(), targetObjectKey)
}

/*
HTMLStringToPDF converts a given html string using gotenberg 7.9.1
to pdf file and uploads to s3 at specified target object key
and returns whether the call was successful
*/
func HTMLStringToPDF(htmlString string, targetObjectKey string) bool {
	return gotenberg791ConvertHTMLToPDF([]byte(htmlString), targetObjectKey)
}

/*
HTMLToPDF851 converts a given html file using gotenberg 8.5.1
(specified by a target s3 object key) to pdf file and uploads
to s3 at specified target object key
*/
func HTMLToPDF851(ctx context.Context, userID string, inputOpts InputOptions, targetPDFObjectKey string, opts HTMLToPDFOptions) bool {
	// get byte array from s3
	buf := new(bytes.Buffer)
	reader := s3.GetFileStream(inputOpts.MainHTMLObjectKey)
	if _, err := buf.ReadFrom(*reader); err != nil {
		logger.WithUser(userID).Errorln(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{
			"userID": "userID",
			"opts":   inputOpts,
		}, err)
		return false
	}

	if inputOpts.HeaderHTMLObjectKey != "" {
		headerBuf := new(bytes.Buffer)
		headerReader := s3.GetFileStream(inputOpts.HeaderHTMLObjectKey)
		if _, err := headerBuf.ReadFrom(*headerReader); err != nil {
			logger.WithUser(userID).Errorln(err)
			errorHandler.ReportToSentryWithFields(map[string]interface{}{
				"userID": "userID",
				"opts":   inputOpts,
			}, err)
			return false
		}

		opts.headerHTML = headerBuf.String()
	}

	if inputOpts.FooterHTMLObjectKey != "" {
		footerBuf := new(bytes.Buffer)
		footerReader := s3.GetFileStream(inputOpts.FooterHTMLObjectKey)
		if _, err := footerBuf.ReadFrom(*footerReader); err != nil {
			logger.WithUser(userID).Errorln(err)
			errorHandler.ReportToSentryWithFields(map[string]interface{}{
				"userID": "userID",
				"opts":   inputOpts,
			}, err)
			return false
		}

		opts.footerHTML = footerBuf.String()
	}

	// call gotenberg service (this also uploads to s3)
	return gotenberg851ConvertHTMLToPDF(ctx, userID, buf.Bytes(), targetPDFObjectKey, opts)
}
