package convert

import (
	"fmt"
	"mime/multipart"
)

func defaultHTMLToPDFOptions() HTMLToPDFOptions {
	return HTMLToPDFOptions{
		PaperSize:        PaperSizeLetter,
		PrintBackground:  false,
		OmitBackground:   false,
		Scale:            1.0,
		NativePageRanges: "",
		MarginTop:        0.39,
		MarginBottom:     0.39,
		MarginLeft:       0.39,
		MarginRight:      0.39,
		Landscape:        false,
		headerHTML:       "",
		footerHTML:       "",
	}
}

func applyHTMLToPDFOptions(options HTMLToPDFOptions) HTMLToPDFOptions {
	defaults := defaultHTMLToPDFOptions()

	// Only override non-zero values
	if options.PaperSize != "" {
		defaults.PaperSize = options.PaperSize
	}

	// For float64, compare with 0
	if options.Scale != 0 {
		defaults.Scale = options.Scale
	}

	if options.NativePageRanges != "" {
		defaults.NativePageRanges = options.NativePageRanges
	}

	if options.MarginTop != 0 {
		defaults.MarginTop = options.MarginTop
	}

	if options.MarginBottom != 0 {
		defaults.MarginBottom = options.MarginBottom
	}

	if options.MarginLeft != 0 {
		defaults.MarginLeft = options.MarginLeft
	}

	if options.MarginRight != 0 {
		defaults.MarginRight = options.MarginRight
	}

	// For bools, we need to use the input value if it's explicitly set to true
	// since false is the zero value
	if options.PrintBackground {
		defaults.PrintBackground = true
	}

	if options.OmitBackground {
		defaults.OmitBackground = true
	}

	if options.Landscape {
		defaults.Landscape = true
	}

	if options.headerHTML != "" {
		defaults.headerHTML = options.headerHTML
	}

	if options.footerHTML != "" {
		defaults.footerHTML = options.footerHTML
	}

	return defaults
}

func (o *HTMLToPDFOptions) addOptionsToGotenbergRequest(w *multipart.Writer) {
	_ = w.WriteField("scale", fmt.Sprintf("%f", o.Scale))
	_ = w.WriteField("paperWidth", fmt.Sprintf("%f", paperSizeToDimensionsInInches[o.PaperSize].width))
	_ = w.WriteField("paperHeight", fmt.Sprintf("%f", paperSizeToDimensionsInInches[o.PaperSize].height))
	_ = w.WriteField("marginTop", fmt.Sprintf("%f", o.MarginTop))
	_ = w.WriteField("marginBottom", fmt.Sprintf("%f", o.MarginBottom))
	_ = w.WriteField("marginLeft", fmt.Sprintf("%f", o.MarginLeft))
	_ = w.WriteField("marginRight", fmt.Sprintf("%f", o.MarginRight))
	_ = w.WriteField("landscape", fmt.Sprintf("%t", o.Landscape))
	_ = w.WriteField("printBackground", fmt.Sprintf("%t", o.PrintBackground))
	_ = w.WriteField("omitBackground", fmt.Sprintf("%t", o.OmitBackground))
	_ = w.WriteField("nativePageRanges", o.NativePageRanges)
}
