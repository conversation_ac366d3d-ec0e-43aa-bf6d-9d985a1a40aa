package convert

import (
	"bytes"
	"context"
	"errors"
	"finbox/go-api/conf"
	"finbox/go-api/constants"
	"finbox/go-api/errorHandler"
	"finbox/go-api/functions/logger"
	"finbox/go-api/functions/requestutils"
	"finbox/go-api/functions/retry"
	"finbox/go-api/functions/serviceslib"
	"finbox/go-api/functions/tracer"
	"finbox/go-api/infra/s3"
	"finbox/go-api/utils/general"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"os"
	"time"
)

var log = logger.Log

/*
gotenberg791ConvertHTMLToPDF Converts a given html code (in byte[]) to pdf file and uploads to s3 at specified target object key
and returns whether the call was successful using the gotenberg service version 7.9.1
*/
func gotenberg791ConvertHTMLToPDF(html []byte, targetObjectKey string) bool {
	url := fmt.Sprintf("%s/forms/chromium/convert/html", conf.GetDefaultGotenbergBaseURL())

	var b bytes.Buffer
	w := multipart.NewWriter(&b)
	// this service explicitly requires input html to be of the file name index.html
	fw, err := w.CreateFormFile("files", "index.html")
	if err != nil {
		log.Errorln(err)
		return false
	}
	fw.Write(html)
	w.Close()

	client := tracer.GetTraceableHTTPClient(nil, "pdf_service_html_pdf")
	var body []byte
	err = retry.CustomRetry(3, 1000*time.Millisecond, func() error {
		req, err := requestutils.GetMockableHTTPRequest("", "pdf_service_html_pdf", "POST", url, &b)
		if err != nil {
			log.Errorln(err)
			return err
		}
		req.Header.Set("Content-Type", w.FormDataContentType())

		res, err := client.Do(req)
		if err != nil {
			log.Errorln(err)
			return err
		}
		defer res.Body.Close()

		if res.StatusCode != http.StatusOK {
			err := fmt.Errorf("error response from gotenberg, status-code received from gotenberg: %v", res.StatusCode)
			log.Errorln(err)
			return err
		}
		body, err = io.ReadAll(res.Body)
		if err != nil {
			log.Errorln("error in reading gotenberg response: ", err)
			return err
		}
		if len(body) == 0 {
			err = errors.New("empty response body")
			log.Errorln(err)
			return err
		}
		return nil
	})

	if err != nil {
		log.Errorln("error from gotenberg: ", err)
		return false
	}
	_, uploaded := s3.UploadRawFileS3(bytes.NewReader(body), targetObjectKey)
	if !uploaded {
		log.Errorln("error in uploading to s3")
		return false
	}

	return uploaded
}

/*
gotenberg851ConvertHTMLToPDFgotenbergService851 Converts a given html code (in byte[]) to pdf file and uploads to s3 at specified target object key
and returns whether the call was successful using the gotenberg service version 8.5.1
*/
func gotenberg851ConvertHTMLToPDF(ctx context.Context, userID string, html []byte, targetS3PDFkey string, opts HTMLToPDFOptions) bool {
	var (
		serviceName = "pdf_service_html_pdf_8.5.1"
		url         = fmt.Sprintf("%s/forms/chromium/convert/html", conf.GetGotenberg8_5_1BaseURL())
	)

	externalServiceLogger := struct {
		URL               string
		RequestString     string
		ResponseString    string
		UserID            string
		ServiceName       string
		ExternalServiceID string
		RequestHeaders    http.Header
		ResponseHeaders   http.Header
		HTTPStatusCode    int
	}{
		UserID:         userID,
		ServiceName:    serviceName,
		RequestString:  "<Request not logged completely due to size constraints>",
		ResponseString: "<Response not logged completely due to size constraints>",
		URL:            url,
	}

	opts = applyHTMLToPDFOptions(opts)

	var b bytes.Buffer
	w := multipart.NewWriter(&b)
	// this service explicitly requires input html to be of the file name index.html
	fw, err := w.CreateFormFile("files", "index.html")
	if err != nil {
		logger.WithUser(userID).Errorln(err)
		return false
	}
	if _, err = fw.Write(html); err != nil {
		logger.WithUser(userID).Errorln(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{
			"userID":  userID,
			"options": opts,
		}, err)
		return false
	}

	if opts.headerHTML != "" {
		headerFw, err := w.CreateFormFile("files", "header.html")
		if err != nil {
			logger.WithUser(userID).Errorln(err)
			return false
		}
		if _, err = headerFw.Write([]byte(opts.headerHTML)); err != nil {
			logger.WithUser(userID).Errorln(err)
			errorHandler.ReportToSentryWithFields(map[string]interface{}{
				"userID":  userID,
				"options": opts,
			}, err)
			return false
		}
	}
	if opts.footerHTML != "" {
		footerFw, err := w.CreateFormFile("files", "footer.html")
		if err != nil {
			logger.WithUser(userID).Errorln(err)
			return false
		}
		if _, err = footerFw.Write([]byte(opts.footerHTML)); err != nil {
			logger.WithUser(userID).Errorln(err)
			errorHandler.ReportToSentryWithFields(map[string]interface{}{
				"userID":  userID,
				"options": opts,
			}, err)
			return false
		}
	}

	opts.addOptionsToGotenbergRequest(w)
	w.Close()

	client := tracer.GetTraceableHTTPClient(nil, serviceName)
	var body []byte
	err = retry.CustomRetry(3, 1000*time.Millisecond, func() (err error) {
		externalServiceLogger.ExternalServiceID = general.GetUUID()

		defer func() {
			status := serviceslib.SuccessStatusCode
			errStr := ""
			if err != nil {
				status = serviceslib.ErrorStatusCode
				errStr = err.Error()
			}
			serviceslib.WriteToDBWithHeaders(
				externalServiceLogger.ExternalServiceID,
				externalServiceLogger.UserID,
				externalServiceLogger.ServiceName,
				externalServiceLogger.URL,
				externalServiceLogger.RequestString,
				externalServiceLogger.ResponseString,
				errStr,
				status,
				externalServiceLogger.HTTPStatusCode,
				externalServiceLogger.RequestHeaders,
				externalServiceLogger.ResponseHeaders,
			)
		}()

		req, err := requestutils.GetMockableHTTPRequest(userID, serviceName, "POST", url, &b)
		if err != nil {
			logger.WithUser(userID).Errorln(err)
			return err
		}
		req.Header.Set("Content-Type", w.FormDataContentType())
		// TODO: Using the same authentication as Octopus for now, should have service specific auth tokens
		if conf.OctopusConfig["BearerAuth"] != "" {
			req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", conf.OctopusConfig["BearerAuth"]))
		}

		externalServiceLogger.RequestHeaders = req.Header

		res, err := client.Do(req)
		if err != nil {
			logger.WithUser(userID).Errorln(err)
			return err
		}
		defer res.Body.Close()

		externalServiceLogger.ResponseHeaders = res.Header
		externalServiceLogger.HTTPStatusCode = res.StatusCode

		if res.StatusCode != http.StatusOK {
			err := fmt.Errorf("error response from gotenberg, status-code received from gotenberg: %v", res.StatusCode)
			logger.WithUser(userID).Errorln(err)
			return err
		}

		body, err = io.ReadAll(res.Body)
		if err != nil {
			logger.WithUser(userID).Errorln("error in reading gotenberg response: ", err)
			return err
		}

		if len(body) == 0 {
			err = errors.New("empty response body")
			logger.WithUser(userID).Errorln(err)
			return err
		}
		return nil
	})

	if err != nil {
		logger.WithUser(userID).Errorln("error from gotenberg: ", err)
		return false
	}
	_, uploaded := s3.UploadRawFileS3(bytes.NewReader(body), targetS3PDFkey)
	if !uploaded {
		logger.WithUser(userID).Errorln("error in uploading to s3")
		return false
	}

	return uploaded
}

// gotenbergAPICallAndS3Upload... Converts html byte string to image file and uploads to s3 at targetFilePath
// returns uploaded successfully to s3
func gotenbergConvertAndSaveToS3(htmlToImageStruct HTMLToImageUsingGotenberg) (err error) {

	respBody, err := gotenbergConvertHTML(htmlToImageStruct.UserID, htmlToImageStruct.Format, []byte(htmlToImageStruct.HTMLString))
	if err != nil {
		logger.WithUser(htmlToImageStruct.UserID).Errorln(err)
		return err
	}

	// Convert []byte to io.Reader
	bodyReader := bytes.NewReader(respBody)

	// Upload the response to S3 and check if it's successful
	_, uploaded := s3.UploadRawFileS3(bodyReader, htmlToImageStruct.TargetFilePath)
	if !uploaded {
		err = errors.New("failed to upload to s3")
		logger.WithUser(htmlToImageStruct.UserID).Errorln(err)
		return err
	}
	return err
}

func gotenbergConvertHTML(userID, format string, html []byte) (respBody []byte, err error) {
	// Perform the API call to Gotenberg service for HTML To Image conversion

	baseURL := conf.GetGotenberg8_5_1BaseURL()

	baseURL += "/forms/chromium/screenshot/html"

	// Create a new multipart writer
	body := &bytes.Buffer{}
	writer := multipart.NewWriter(body)

	// Add the file to the request
	part, err := writer.CreateFormFile("files", "index.html")
	if err != nil {
		logger.WithUser(userID).Errorln(err)
		return
	}

	_, err = part.Write(html)
	if err != nil {
		logger.WithUser(userID).Errorln(err)
		return
	}

	// Add additional form fields
	err = writer.WriteField("format", format)
	if err != nil {
		logger.WithUser(userID).Errorln(err)
		return
	}

	err = writer.WriteField("quality", "100")
	if err != nil {
		logger.WithUser(userID).Errorln(err)
		return
	}

	err = writer.WriteField("optimizeForSpeed", "true")
	if err != nil {
		logger.WithUser(userID).Errorln(err)
		return
	}

	// Close multipart writer
	writer.Close()

	client := tracer.GetTraceableHTTPClient(nil, constants.GotenbergHTMLScreenshot)

	// Create the HTTP request
	req, err := http.NewRequest("POST", baseURL, body)
	if err != nil {
		logger.WithUser(userID).Errorln(err)
		return
	}

	// Set Headers
	req.Header.Set("Content-Type", writer.FormDataContentType())
	if conf.OctopusConfig["BearerAuth"] != "" {
		req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", conf.OctopusConfig["BearerAuth"]))
	}

	gobj := map[string]string{
		"url":    baseURL,
		"strReq": fmt.Sprintf("%+v", req.Body),
		"strRes": "",
		"userID": userID,
		"id":     general.GetUUID(),
	}
	httpResCode := 0

	defer func() {
		status := serviceslib.SuccessStatusCode
		errStr := ""
		if err != nil {
			status = serviceslib.ErrorStatusCode
			errStr = err.Error()
		}

		serviceslib.WriteToDBWithHeaders(gobj["id"], userID, constants.GotenbergHTMLScreenshot, baseURL, gobj["strReq"], gobj["strRes"], errStr, status, httpResCode, req.Header, http.Header{})
	}()

	resp, err := client.Do(req)
	if err != nil {
		logger.WithUser(userID).Errorln(err)
		return
	}
	defer resp.Body.Close()

	respBody, err = io.ReadAll(resp.Body)
	if err != nil {
		logger.WithUser(userID).Errorln(err)
		return
	}

	httpResCode = resp.StatusCode

	if resp.StatusCode < 200 || resp.StatusCode > 299 {
		err = errors.New("no 200 response")
		logger.WithUser(userID).Errorln(err, " , body: ", string(respBody))
		return
	}

	return respBody, err

}

// gotenbergConvertAndSaveToFilePath... Converts html byte string to given file path
// returns error from operation
func gotenbergConvertAndSaveToFilePath(htmlToImageStruct HTMLToImageUsingGotenberg) (err error) {
	// Perform the API call to Gotenberg service for HTML To Image conversion

	respBody, err := gotenbergConvertHTML(htmlToImageStruct.UserID, htmlToImageStruct.Format, []byte(htmlToImageStruct.HTMLString))
	if err != nil {
		logger.WithUser(htmlToImageStruct.UserID).Errorln(err)
		return err
	}

	// Write data to file
	err = os.WriteFile(htmlToImageStruct.TargetFilePath, respBody, 0600)
	if err != nil {
		logger.WithUser(htmlToImageStruct.UserID).Errorln(err)
		return err
	}

	return err
}
