package export

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"finbox/go-api/authentication"
	"finbox/go-api/constants"
	"finbox/go-api/errorHandler"
	"finbox/go-api/functions/commonutils"
	"finbox/go-api/functions/emaillib"
	"finbox/go-api/functions/journey"
	"finbox/go-api/functions/logger"
	"finbox/go-api/functions/masterDashboardUtils"
	"finbox/go-api/infra/db"
	"finbox/go-api/infra/s3"
	"finbox/go-api/models/exportlogs"
	"finbox/go-api/models/workflow"
	"finbox/go-api/utils/calc"
	"finbox/go-api/utils/general"
	"finbox/go-api/utils/mask"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/jmoiron/sqlx"
	"github.com/lib/pq"
)

var log = logger.Log
var database = db.GetDB()

// GetUserDataDump takes in a context with timeout defined and returns user profile data row by row
func GetUserDataDump(ctx context.Context, userObj interface{}, params map[string]interface{}, sourceEntityIDs []string, organizationId string) (*sqlx.Rows, error) {
	sourceEntityCondition := ""
	dsaCondition := ""
	dsaQuery := ""
	cityStateSelect := ""
	baseSchemeSelect := ""
	extraJoin := ""
	statusList := params["statusList"].([]int)
	switch userObj := userObj.(type) {
	case *authentication.MasterDashboardUserStruct:
		sourceEntityCondition = ` u.source_entity_id in ('` + strings.Join(sourceEntityIDs, "' , '") + `') `
		if masterDashboardUtils.IsLoggedInUserDSA(userObj.UserType) {
			if params["dsaID"] == "" {
				params["dsaIDList"] = userObj.DSAList
				dsaQuery = " JOIN unnest(ARRAY[:dsaIDList]::::uuid[]) AS dsaid ON dsaid = u.dsa_id"
			} else {
				dsaCondition = " AND u.dsa_id = :dsaID"
			}
		}
	default:
		return nil, errors.New("unknown requestor")
	}

	if organizationId == constants.MFLOrganizationID {
		cityStateSelect += ` coalesce(pd.division, '') AS city, coalesce(pd.state, '') AS state, `
		baseSchemeSelect += `,
		coalesce(uj.metadata::::jsonb->>'journeyBase', '') as base, 
        CASE
            WHEN coalesce(uj.metadata::::jsonb->>'installmentProgramme', '') = 'EMI' THEN 'Bandhan Plus' 
            WHEN coalesce(uj.metadata::::jsonb->>'installmentProgramme', '') = 'EDI' THEN 'Vyapar Mitra' 
			ELSE ''
        END as scheme
		`
		extraJoin += ` left join pincode_db pd on pd.pincode = u.pincode `
	}

	params["cibilstatus"] = "completed"
	params["experianstatus"] = "completed"
	query := `
			SELECT DISTINCT ON (u.user_id) coalesce(u.unique_id, '') AS customerid,
			u.status,
			u.source_entity_id as sourceentityid,
			coalesce(u.source, '') AS source,
			to_char(u.created_at AT TIME ZONE 'utc' AT TIME ZONE 'Asia/Calcutta', 'YYYY-MM-DD HH24::MI::SS') AS createdat,
			coalesce(u.name, '') AS name,
			coalesce(u.mobile, '') AS mobile,
			coalesce(u.email, '') AS email,
			coalesce(dynamic_user_info::::jsonb->>'monthlyIncome', '0') AS monthlyincome,
			coalesce(dynamic_user_info::::jsonb->>'occupation', '') AS occupationtype,
			coalesce(dynamic_user_info::::jsonb->>'companyName','') as companyname,
			coalesce(dynamic_user_info::::jsonb->>'educationalQualification','')as educationalqualification,
			coalesce(dynamic_user_info::::jsonb->>'employmentType','')as employmenttype,
			coalesce(dynamic_user_info::::jsonb->>'loanAmount','')as requestedloanamount,
			coalesce(dynamic_user_info::::jsonb->>'officeEmail','')as officeemail,
			coalesce(dynamic_user_info::::jsonb->>'officePincode','')as officepincode,
			coalesce(dynamic_user_info::::jsonb->>'residenceType','')as residencetype,
			coalesce(dynamic_user_info::::jsonb->>'employmentWaiverFlag','')as employmentwaiverflag,
			coalesce(to_char(u.dob, 'YYYY-MM-DD')::::TEXT, '') AS dob,
			coalesce(u.gender, -1) AS gender,
			coalesce(u.pincode, '') AS pincode, ` +
			cityStateSelect +
			`coalesce(u.pan, '') AS pan,
			coalesce(u.partner_code, '') AS partnercode,
			coalesce(u.partner_data::::jsonb->>'appflyer_id', '') AS appflyerid,
			coalesce(u.partner_data::::jsonb->>'crm_id', '') AS crmid, 
			u.user_id AS userID,
			coalesce(u.firm_name, '') AS firmname,
			coalesce(bs.experian_score, -1) AS experianscore,
			coalesce(bs.experian_status, '') AS experianstatus,
			coalesce(bs.experian_error, '') AS experianerror,
			coalesce(bs.cibil_score, -1) AS cibilscore,
			coalesce(bs.cibil_status, 'not_fetched') AS cibilstatus,
			coalesce(to_char(l.created_at AT TIME ZONE 'utc' AT TIME ZONE 'Asia/Calcutta', 'YYYY-MM-DD HH24::MI::SS'), '') AS applicationcreatedat,
			coalesce(l.loan_application_no, '') AS loanapplicationno,
			coalesce(l.status, -1) AS loanstatus,
			coalesce(l.kyc_status, 0) AS kycstatus,
			coalesce(bc.status, -1) AS bankstatementstatus,
			coalesce(bc.failure_reason, '') AS failurereason,
			to_char(u.updated_at AT TIME ZONE 'utc' AT TIME ZONE 'Asia/Calcutta','YYYY-MM-DD HH24::MI::SS') AS updatedat,
			coalesce(se.source_entity_name, '') as platform,
			coalesce(u.dsa_id::::TEXT, '') AS dsaid,
			coalesce(bc.extracted_name, '') AS bankaccountholdername,
			coalesce(bc.account_type, '') AS bankaccounttype,
			coalesce(bc.bank, '') AS bankname,
			(CASE 
				WHEN (bs.cibil_status = :cibilstatus OR bs.experian_status = :experianstatus) 
				THEN coalesce(to_char(bs.cibil_updated_at AT TIME ZONE 'utc' AT TIME ZONE 'Asia/Calcutta', 'YYYY-MM-DD HH24::MI::SS'), to_char(bs.experian_updated_at AT TIME ZONE 'utc' AT TIME ZONE 'Asia/Calcutta', 'YYYY-MM-DD HH24::MI::SS'), '') 
				ELSE '' 
			END) AS bureaulastupdatedat,
			coalesce(ue.eligible_amount, plo.max_amount, 0) AS eligibleamount,
			coalesce(to_char(ue.created_at AT TIME ZONE 'utc' AT TIME ZONE 'Asia/Calcutta', 'YYYY-MM-DD HH24::MI::SS'), to_char(plo.created_at AT TIME ZONE 'utc' AT TIME ZONE 'Asia/Calcutta', 'YYYY-MM-DD HH24::MI::SS'), '') AS usereligibilitycalculatedat,
			coalesce(ub.constitution, '') as constitution,
			coalesce(ub.business_pan, '') as businesspan,
      		coalesce(ubg.gstin, '') as gstin,
			coalesce(uj.metadata::::jsonb->>'multiBankFlowType', '') as isEligibleForMultibanking,
			coalesce(bs.cibil_hard_pull_score, -1) AS cibilhardscore ` +
			baseSchemeSelect +
			` FROM users u
			left join multi_user_loan_relations mslr on mslr.user_id=u.user_id ` +
			dsaQuery +
			` left join lender_variables lv on u.user_id = lv.user_id
			left join bureau_score bs ON u.user_id = bs.user_id
			left join (select user_id, status, kyc_status, loan_application_no, created_at
				from loan_application) l on l.user_id = u.user_id
			left join (select user_id, status, failure_reason, extracted_name, account_type, bank, created_at from bank_connect_details) bc on bc.user_id = u.user_id
			left join source_entity se
					on se.source_entity_id = u.source_entity_id
			left join user_eligibility ue on u.user_id = ue.user_id and ue.created_by = 'ADMIN' and ue.is_eligible = true
			LEFT JOIN personal_loan_offer plo ON u.user_id = plo.user_id
			left join user_business ub on u.user_id = ub.user_id
      		left join user_business_gst ubg on u.user_id = ubg.user_id
			left join user_journey uj on u.user_id = uj.user_id ` +
			extraJoin +
			` WHERE ` + sourceEntityCondition + dsaCondition +
			` and ((mslr.loan_variant is null) or (mslr.loan_variant = 'marketplace') or (mslr.loan_variant = 'topup'))`
	if len(statusList) > 0 {
		query += ` AND u.status in (`
		for index, status := range statusList {
			if index > 0 {
				query += fmt.Sprintf(`, %d`, status)
			} else {
				query += fmt.Sprintf(`%d`, status)
			}
		}
		query += `)`
	} else {
		params["archived"] = constants.UserStatusArchived
		query += ` AND u.status != :archived`
	}

	if len(params["source"].(string)) > 0 {
		query += ` AND u.source = :source`
	}

	switch params["filter"].(string) {
	case "updated_at":
		query += ` AND to_char(u.updated_at at time zone 'utc' at time zone 'Asia/Calcutta', 'YYYY-MM-DD') >= :from AND to_char(u.updated_at at time zone 'utc' at time zone 'Asia/Calcutta', 'YYYY-MM-DD') <= :to
				ORDER BY u.user_id,
				l.created_at DESC,
				bc.created_at DESC,
                lv.created_at DESC,
				uj.created_at DESC`
	default:
		query += ` AND to_char(u.created_at at time zone 'utc' at time zone 'Asia/Calcutta', 'YYYY-MM-DD') >= :from AND to_char(u.created_at at time zone 'utc' at time zone 'Asia/Calcutta', 'YYYY-MM-DD') <= :to
				ORDER BY u.user_id,
				l.created_at DESC,
				bc.created_at DESC,
 				lv.created_at DESC,
				uj.created_at DESC`
	}

	query, args, err := sqlx.Named(query, params)
	if err != nil {
		log.Errorln(err)
		return nil, err
	}
	query, args, err = sqlx.In(query, args...)
	if err != nil {
		log.Errorln(err)
		return nil, err
	}

	query = database.Rebind(query)
	rows, err := database.QueryxContext(ctx, query, args...)
	if err != nil {
		if err == sql.ErrNoRows {
			log.Errorln("no users found")
			return nil, errors.New("no users found")
		} else {
			log.Errorln(err)
			return nil, err
		}
	}

	return rows, nil
}

// GetLoanDataDump takes in a context with timeout defined and returns loan data row by row
func GetLoanDataDump(ctx context.Context, userObj interface{}, params map[string]interface{}, sourceEntityIDs []string, lenderID string, organizationID string) (*sqlx.Rows, error) {
	statusList := params["statusList"].([][]int)
	loanTypeList := params["loanTypeList"].([]string)
	extraCondition := ""
	sourceEntityCondition := ""
	dsaQuery := ""
	extraFilter := ""
	creditLineSelect := ""
	extraSelect := ""
	creditLineJoin := ""
	extraJoin := ""
	sourceCondition := ""
	switch userObj := userObj.(type) {
	case *authentication.MasterDashboardUserStruct:
		if masterDashboardUtils.IsLoggedInUserDSA(userObj.UserType) {
			if params["dsaID"] == "" {
				params["dsaIDList"] = userObj.DSAList
				dsaQuery = " JOIN unnest(ARRAY[:dsaIDList]::::uuid[]) AS dsaid ON dsaid = u.dsa_id"
			} else {
				dsaQuery = " AND u.dsa_id = :dsaID"
			}
		}
		sourceEntityCondition = ` la.source_entity_id in ('` + strings.Join(sourceEntityIDs, "' , '") + `') `
		sourceCondition = ` AND source = :source`
		if lenderID != "" {
			extraCondition += ` AND la.lender_id = :lenderid`
			params["lenderid"] = lenderID
		}
		extraSelect = `, coalesce(l.lender_name, '') AS lendername,
		coalesce(u.source, '') as source
		`
		extraJoin = ` LEFT JOIN lender l on l.lender_id = la.lender_id
		`
		if general.InArr(constants.PFLEducationLoanSEID, sourceEntityIDs) {
			extraSelect += `, COALESCE(u.dynamic_user_info::::jsonb->>'expected_loan_amount', '') as appliedloanamount,
			COALESCE(u.dynamic_user_info::::jsonb->>'course_details_country', '') as countryofstudy,
			COALESCE(u.dynamic_user_info::::jsonb->>'university', '') as university,
			COALESCE(u.dynamic_user_info::::jsonb->>'institute_name', '') as institutename,
			COALESCE(u.dynamic_user_info::::jsonb->>'course_name', '') as coursename,
			COALESCE(u.dynamic_user_info::::jsonb->>'are_you_being_assisted', '') as journeyassisted,
			COALESCE(u.dynamic_user_info::::jsonb->>'emp_code_consultant', '') as empcodeconsultant,
			COALESCE(u.dynamic_user_info::::jsonb->>'crm_id_of_consultant', '') as crmidofconsultant,
			COALESCE(
					NULLIF(u.dynamic_user_info::::jsonb->>'pfl_sales_executive_emp_id', ''),
					COALESCE(u.dynamic_user_info::::jsonb->>'pfl_sales_emp_id_if_not_in_list', '')
			) AS executivecode,
			COALESCE(
					NULLIF(u.dynamic_user_info::::jsonb->>'dsa_name', ''),
					COALESCE(u.dynamic_user_info::::jsonb->>'dsa_name_if_not_in_list', '')
			) AS dsaname,
			COALESCE(
					NULLIF(u.dynamic_user_info::::jsonb->>'dsa_code', ''),
					COALESCE(u.dynamic_user_info::::jsonb->>'dsa_code_if_not_in_list', '')
			) AS dsacode,
			COALESCE(
					NULLIF(u.dynamic_user_info::::jsonb->>'pfl_sales_executive_name', ''),
					COALESCE(u.dynamic_user_info::::jsonb->>'pfl_sales_executive_name_if_not_in_list', '')
			) AS executivename
			`
		}
	case *authentication.LenderUserStruct:
		params["lenderid"] = lenderID
		sourceEntityCondition = ` la.lender_id = :lenderid and dr.lender_id=:lenderid `

		if len(sourceEntityIDs) > 0 {
			sourceEntityCondition += `AND la.source_entity_id in ('` + strings.Join(sourceEntityIDs, "' , '") + `') `
		}
		extraFilter = `, dr.created_at DESC
		, de.updated_at DESC`
		sourceCondition = ` AND se.source_entity_id = :source`
		extraSelect = `, coalesce(dr.rule_version, '') AS ruleversion
		,coalesce(to_char(dr.created_at AT TIME ZONE 'utc' AT TIME ZONE 'Asia/Calcutta', 'YYYY-MM-DD'), '') AS rundate
		,coalesce(de.status, -1) as nachstatus
		`
		extraJoin = `
		LEFT JOIN decision_engine_response dr ON dr.user_id = la.user_id AND dr.created_at <= (CASE
					WHEN la.updated_at IS NULL THEN la.created_at
					ELSE la.updated_at
					END)
		LEFT JOIN digio_enach de ON de.loan_application_id = la.loan_application_id
		`
		// Hack. TODO: Remove
		if lenderID == constants.ABFLID {
			params["sourceentitytobehidden"] = constants.TataPLID
			extraCondition += ` AND u.source_entity_id <> :sourceentitytobehidden`
		}
	default:
		return nil, errors.New("unknown requestor")
	}

	params["archived"] = constants.UserStatusArchived
	extraCondition += ` AND u.status != :archived`

	if len(statusList) > 0 {
		extraCondition += ` AND (`
		for index, statusPair := range statusList {
			statusQuery := fmt.Sprintf("la.status = %d", statusPair[0])
			if statusPair[1] > 0 {
				statusQuery += fmt.Sprintf(" AND la.kyc_status = %d", statusPair[1])
			} else if statusPair[1] == 0 {
				statusQuery += " AND (la.kyc_status = 0 OR la.kyc_status is NULL)"
			}
			if index > 0 {
				extraCondition += fmt.Sprintf(" OR (%s)", statusQuery)
			} else {
				extraCondition += fmt.Sprintf(`(%s)`, statusQuery)
			}
		}
		extraCondition += `)`
	}

	segments, exists := params["segments"].([]string)
	if exists && len(segments) > 0 {
		extraCondition += ` and u.dynamic_user_info::::jsonb ->> 'segment' IN (`
		for index, segment := range segments {
			if index > 0 {
				extraCondition += fmt.Sprintf(", '%s'", segment)
			} else {
				extraCondition += fmt.Sprintf("'%s'", segment)
			}
		}
		extraCondition += `)`
	}

	if len(loanTypeList) > 0 {
		extraCondition += ` and la.loan_type in (`
		for index, loanType := range loanTypeList {
			if index > 0 {
				extraCondition += fmt.Sprintf(", '%s'", loanType)
			} else {
				extraCondition += fmt.Sprintf("'%s'", loanType)
			}
		}
		extraCondition += `)`
	}

	if general.InArr(constants.LoanTypeCreditLine, loanTypeList) {
		creditLineJoin = ` left join credit_line cl on la.loan_application_id=cl.parent_loan_id 
							and cl.credit_line_type='cl' `
		creditLineSelect = `, coalesce(cl.max_limit, 0.0) as maxlimit,
			coalesce(cl.available_limit, 0.0) as availablelimit,
			coalesce(to_char(cl.created_at at time zone 'utc' at time zone 'Asia/Calcutta', 'YYYY-MM-DD HH24::MI::SS'), '') as activatedon,
			coalesce(cl.status, 0) as creditlinestatus `
	}

	if len(params["source"].(string)) > 0 {
		extraCondition += sourceCondition
	}

	switch params["filter"].(string) {
	case "updated_at":
		extraCondition += ` AND to_char(la.updated_at at time zone 'utc' at time zone 'Asia/Calcutta', 'YYYY-MM-DD') >= :from AND to_char(la.updated_at at time zone 'utc' at time zone 'Asia/Calcutta', 'YYYY-MM-DD') <= :to
		ORDER BY
		la.loan_application_id,
		loan_offer.created_at DESC,
		personal_loan_offer.created_at DESC,
		business_loan_offer.created_at DESC,
		user_eligibility.created_at DESC,
		bc.created_at DESC` + extraFilter + ` , lv.created_at DESC
		) t ORDER BY updatedat
		`
	case "disbursed_date":
		extraCondition += ` AND la.disbursed_date >= :from AND la.disbursed_date <= :to
		ORDER BY
		la.loan_application_id,
		loan_offer.created_at DESC,
		personal_loan_offer.created_at DESC,
		business_loan_offer.created_at DESC,
		user_eligibility.created_at DESC,
		bc.created_at DESC` + extraFilter + ` , lv.created_at DESC
		) t ORDER BY disburseddate`
	default:
		extraCondition += ` AND to_char(la.created_at at time zone 'utc' at time zone 'Asia/Calcutta', 'YYYY-MM-DD') >= :from AND to_char(la.created_at at time zone 'utc' at time zone 'Asia/Calcutta', 'YYYY-MM-DD') <= :to
		ORDER BY
		la.loan_application_id,
		loan_offer.created_at DESC,
		personal_loan_offer.created_at DESC,
		business_loan_offer.created_at DESC,
		user_eligibility.created_at DESC,
		bc.created_at DESC` + extraFilter + ` , lv.created_at DESC
		) t ORDER BY applicationcreationdate`
	}

	userBusinessLatLongSelect := `, coalesce((ub.metadata::::json)->>'latitude', '') as latitude,
		coalesce((ub.metadata::::json)->>'longitude', '') as longitude `
	userBusinessLatLongJoin := ""
	alternateMobileSelect := ""

	if lenderID == constants.MuthootCLID || organizationID == constants.MuthootCLOrganizationID {
		params["location_type"] = "business_location"
		userBusinessLatLongSelect = `, coalesce(ul.lat, '') as latitude,
			coalesce(ul.lon, '') as longitude `

		alternateMobileSelect = `, coalesce(uld.ref1_phone, '') as alternateMobile `
		userBusinessLatLongJoin = ` left join user_location ul on ul.user_id = la.user_id and ul.location_type=:location_type `
	}

	if lenderID == constants.MFLBLID {
		extraSelect += `, CASE 
			WHEN COALESCE(uj.metadata::::jsonb->>'installmentProgramme', '') = 'EDI' THEN 'Vyapar Mitra' 
			WHEN COALESCE(uj.metadata::::jsonb->>'installmentProgramme', '') = 'EMI' THEN 'Bandhan Plus' 
			ELSE '' 
		END AS scheme,
		COALESCE(u.dynamic_user_info::::jsonb->>'occupationType', '') as employementtype
		`

	}

	if lenderID == constants.ABFLID || lenderID == constants.ABFLPLID {
		extraSelect += `, coalesce(aggregated_insurance.insurancepremium * (1 + la.gst / 100) , 0) as insurancePremiumWithGST `
	}

	insuranceCTE := insuranceDumpCTE(lenderID, organizationID)
	query := fmt.Sprintf(`WITH ` + insuranceCTE +
		`SELECT * FROM (
		SELECT DISTINCT ON (la.loan_application_id) la.loan_application_no as loanapplicationno,
		la.loan_application_id as loanapplicationid,
		la.source_entity_id AS sourceentityid,
		la.status,
		coalesce(lv.dynamic_variables::::jsonb->>'platform_lender','') as platformlender,
		CASE WHEN aggregated_insurance.insuranceid IS NOT NULL THEN 'Y' ELSE 'N' END AS insuranceoptdfor,
		coalesce(aggregated_insurance.insurancestatus, '') as insurancestatus,
		coalesce(aggregated_insurance.insurancepremium, 0) as insurancepremium,
		coalesce(aggregated_insurance.insurancetype, '') as insurancetype,
		coalesce(aggregated_insurance.insurancereferenceno, '') as insurancereferenceno,
		coalesce(aggregated_insurance.insurancecustomername, '') as insurancecustomername,
		coalesce(aggregated_insurance.insuranceplantype, '') as insuranceplantype,
		coalesce(aggregated_insurance.insurancepolicyid, '') as insurancepolicyid,
		coalesce(aggregated_insurance.insurancematuritydate, '') as insurancematuritydate,
		coalesce(la.kyc_status, 0) as kycstatus,
		u.mobile,
		coalesce(u.partner_code, '') as partnercode,
		u.unique_id as uniqueid,
		coalesce(u.name, '') as name,
		to_char(u.created_at at time zone 'utc' at time zone 'Asia/Calcutta', 'YYYY-MM-DD HH24::MI::SS') as usercreationdate,
		to_char(la.created_at at time zone 'utc' at time zone 'Asia/Calcutta', 'YYYY-MM-DD HH24::MI::SS') as applicationcreationdate,
		la.loan_type as loantype,
		coalesce(u.pan, '') as pan,
		coalesce(u.gender, -1) as gender,
		coalesce(to_char(u.dob, 'YYYY-MM-DD'), '') as dob,
		coalesce(u.email, '') as email,
		coalesce(uld.permanent_address, '') as permanentaddress,
		coalesce(uld.current_address, '') as currentaddress,
		coalesce(la.amount, loan_offer.amount, personal_loan_offer.max_amount, user_eligibility.eligible_amount, 0) as loanamount,
		coalesce(la.tenure, loan_offer.tenure, personal_loan_offer.max_tenure, 0) AS tenure,
		coalesce(la.interest, loan_offer.interest, personal_loan_offer.interest, 0) AS interest,
		coalesce(es.amount, 0) ediamount,
		coalesce(la.processing_fee, 0) as processingfee,
		coalesce(la.advance_emi, 0) as advanceemi,
		coalesce(la.gst, 0) as gst,
		coalesce(u.dynamic_user_info::::TEXT, '{}') as dynamicuserinfo,
		coalesce(ubd.account_number, '') as bankaccountnumber,
		coalesce(ubd.ifsc_code, '') as ifsccode,
		coalesce(to_char(la.disbursed_date, 'YYYY-MM-DD'), '') as disburseddate,
		coalesce(to_char(la.updated_at at time zone 'utc' at time zone 'Asia/Calcutta', 'YYYY-MM-DD HH24::MI::SS'), '') as updatedat,
		coalesce(to_char(la.sign_date at time zone 'utc' at time zone 'Asia/Calcutta', 'YYYY-MM-DD'), '') as signdate,
		coalesce(la.lender_transactin_number, '') as utrno,
		u.user_id as userid,
		coalesce(u.firm_name, '') as firmname,
		coalesce(se.source_entity_name, '') as platform,
		coalesce(bc.status, -1) AS bankstatementstatus,
		coalesce(bc.failure_reason, '') AS failurereason,
		coalesce(u.dsa_id::::TEXT, '') AS dsaid,
		coalesce(at.tags, '') as tags` + userBusinessLatLongSelect + alternateMobileSelect + creditLineSelect + extraSelect + ` , coalesce(lv.reference_id, '') as referenceid,
		coalesce(to_char(ub.date_of_incorporation at time zone 'utc' at time zone 'Asia/Calcutta', 'YYYY-MM-DD HH24::MI::SS'), '') as dateofincorporation,
	    coalesce(ub.industry_type, '') as industrytype,
        coalesce(ub.sub_industry_type, '') as subindustrytype,
		coalesce(ub.business_address_type, -1) as businessaddresstype,
		coalesce(ub.business_address::::TEXT, '') as businessaddress,
		coalesce(ub.business_category, '') as businesscategory,
		coalesce(u.dynamic_user_info::::jsonb->>'managerDetails', '') as assistedby,
		coalesce(uj.metadata::::jsonb->>'journeyBase', '') as journeyBase
		from loan_application la
		join users u on u.user_id = la.user_id` + dsaQuery + `
		left join lender_variables lv on la.user_id = lv.user_id and la.lender_id=lv.lender_id
		join source_entity se
		on se.source_entity_id = u.source_entity_id
		left join user_business ub on ub.user_id = la.user_id 
		left join user_loan_details uld on la.loan_application_id = uld.loan_application_id
		left join user_bank_details ubd on ubd.user_bank_details_id = la.user_bank_details_id
		LEFT JOIN loan_offer ON loan_offer.loan_id = la.loan_application_id
		LEFT JOIN personal_loan_offer ON personal_loan_offer.loan_offer_id = la.loan_offer_id
		LEFT JOIN business_loan_offer ON business_loan_offer.loan_offer_id = la.loan_offer_id
		left join user_eligibility ON user_eligibility.user_id = la.user_id
		LEFT JOIN aggregated_tags at ON at.loan_application_id = la.loan_application_id
		LEFT JOIN (select user_id, status, failure_reason, created_at from bank_connect_details) bc ON bc.user_id = la.user_id
		LEFT JOIN edi_schedule es ON es.loan_application_id = la.loan_application_id
		LEFT JOIN user_journey uj on uj.user_id = u.user_id
		LEFT JOIN aggregated_insurance on la.loan_application_id = aggregated_insurance.loan_application_id` +
		creditLineJoin + userBusinessLatLongJoin + extraJoin + `
		where  ` + sourceEntityCondition + ` ` + extraCondition)

	query, args, err := sqlx.Named(query, params)
	if err != nil {
		logger.WithContext(ctx).Errorf("[GetLoanDataDump] failed to sql named error: %v, query: %v", err, query)
		return nil, err
	}

	query, args, err = sqlx.In(query, args...)
	if err != nil {
		logger.WithContext(ctx).Errorf("[GetLoanDataDump] failed to sql In error: %v, query: %v", err, query)
		return nil, err
	}

	query = database.Rebind(query)

	rows, err := database.QueryxContext(ctx, query, args...)
	if err != nil {
		if err == sql.ErrNoRows {
			log.Errorln("no loan found")
			return nil, errors.New("no loan found")
		} else {
			logger.WithContext(ctx).Errorf("[GetLoanDataDump] failed to fetch loan dump. error: %v, query: %v", err, query)
			return nil, err
		}
	}

	if general.InArr(lenderID, []string{constants.MuthootCLID, constants.MFLBLID}) {
		newRows, err := getUserActivityInfo(ctx, rows, lenderID)
		if err != nil {
			logger.WithContext(ctx).Errorf("[GetLoanDataDump] getManualQCInfo failed. error: %v", err)
			return nil, err
		}
		rows = newRows
	}

	return rows, nil
}

func insuranceDumpCTE(lenderID string, organizationID string) string {
	conditions := fmt.Sprintf("WHERE status in (%d,%d) ", constants.InsuranceStatusUnpaid, constants.InsuranceStatusPaid)
	if lenderID == constants.MuthootCLID || organizationID == constants.MuthootCLOrganizationID {
		conditions = ""
	}
	return `
	aggregated_insurance AS (
	  SELECT
		loan_application_id,
		SUM(premium) AS insurancepremium,
	    ARRAY_TO_STRING(ARRAY_AGG(insurance_id), ',') AS insuranceid,
		ARRAY_TO_STRING(ARRAY_AGG(insurance_type), ',') AS insurancetype,
		ARRAY_TO_STRING(ARRAY_AGG(reference_no), ',') AS insurancereferenceno,
		ARRAY_TO_STRING(ARRAY_AGG(metadata::::jsonb ->> 'customerName'), ',') AS insurancecustomername,
		ARRAY_TO_STRING(ARRAY_AGG(metadata::::jsonb ->> 'planType'), ',') AS insuranceplantype,
		ARRAY_TO_STRING(ARRAY_AGG(metadata::::jsonb ->> 'policyID'), ',') AS insurancepolicyid,
		ARRAY_TO_STRING(ARRAY_AGG(metadata::::jsonb ->> 'endDate'), ',') AS insurancematuritydate,
		CASE
		  WHEN COALESCE(COUNT(insurance_id), 0) > 0 THEN 'Y'
		  ELSE 'N'
		END AS insuranceoptdfor,
		ARRAY_TO_STRING(ARRAY_AGG(
		  CASE
			WHEN status = 2 THEN 'Success'
			WHEN status = 5 THEN 'Failed'
			ELSE 'Unknown'
		  END
		), ',') AS insurancestatus
	  FROM insurance ` + conditions +
		`GROUP BY loan_application_id
	) `
}

// MISLoan Range Dump
func GetMisLoanDataDump(ctx context.Context, attributes map[string]interface{}, lenderID string) (*sqlx.Rows, error) {
	statusList := attributes["statusList"].([][]int)
	loanTypeList := attributes["loanTypeList"].([]string)
	extraCondition := ""

	extraCondition += `la.lender_id = :lenderid`
	attributes["lenderid"] = lenderID

	// Handling status filters
	if len(statusList) > 0 {
		extraCondition += ` AND (`
		for index, statusPair := range statusList {
			statusQuery := fmt.Sprintf("la.status = %d", statusPair[0])
			if statusPair[1] > 0 {
				statusQuery += fmt.Sprintf(" AND la.kyc_status = %d", statusPair[1])
			} else if statusPair[1] == 0 {
				statusQuery += " AND (la.kyc_status = 0 OR la.kyc_status IS NULL)"
			}
			if index > 0 {
				extraCondition += fmt.Sprintf(" OR (%s)", statusQuery)
			} else {
				extraCondition += fmt.Sprintf(`(%s)`, statusQuery)
			}
		}
		extraCondition += `)`
	}

	// Handling loan type filters
	if len(loanTypeList) > 0 {
		extraCondition += ` AND la.loan_type IN (`
		for index, loanType := range loanTypeList {
			if index > 0 {
				extraCondition += fmt.Sprintf(", '%s'", loanType)
			} else {
				extraCondition += fmt.Sprintf("'%s'", loanType)
			}
		}
		extraCondition += `)`
	}

	// Handling date filters
	dateFilter := ""
	switch attributes["filter"].(string) {
	case "updated_at":
		dateFilter = `AND to_char(la.updated_at at time zone 'utc' at time zone 'Asia/Calcutta', 'YYYY-MM-DD') >= :from AND to_char(la.updated_at at time zone 'utc' at time zone 'Asia/Calcutta', 'YYYY-MM-DD') <= :to`
	case "disbursed_date":
		dateFilter = `AND to_char(la.disbursed_date at time zone 'utc' at time zone 'Asia/Calcutta', 'YYYY-MM-DD') >= :from AND to_char(la.disbursed_date at time zone 'utc' at time zone 'Asia/Calcutta', 'YYYY-MM-DD') <= :to`
	default:
		dateFilter = `AND to_char(la.created_at at time zone 'utc' at time zone 'Asia/Calcutta', 'YYYY-MM-DD') >= :from AND to_char(la.created_at at time zone 'utc' at time zone 'Asia/Calcutta', 'YYYY-MM-DD') <= :to`
	}

	query := fmt.Sprintf(`
        SELECT 
            la.loan_application_no as loanApplicationNo,
            u.unique_id as customerID,
            la.status,
			coalesce(la.kyc_status, 0) as kycstatus,
            COALESCE(u.dynamic_user_info::::jsonb->>'employmentType','') as employmentType,
            COALESCE(MAX(CASE WHEN t.type_id = '8f4996d0-940b-4be9-8b77-aa55c82337e1' THEN wi.current_state END), '') AS creditdecision,
            COALESCE(MAX(CASE WHEN t.type_id = '3c943a39-4a3b-41fe-b13c-61acce180a54' THEN wi.current_state END), '') AS rcudecision,
            COALESCE(MAX(CASE WHEN t.type_id = '8f4996d0-940b-4be9-8b77-aa55c82337e1' THEN t.approved_by END), '') AS creditdecisionby,
            COALESCE(MAX(CASE WHEN t.type_id = '3c943a39-4a3b-41fe-b13c-61acce180a54' THEN t.approved_by END), '') AS rcudecisionby,
            COALESCE(MAX(CASE WHEN t.type_id = '3c943a39-4a3b-41fe-b13c-61acce180a54' THEN t.metadata->>'subStatus' END), '') AS rcusubstatus,
            COALESCE(MAX(CASE WHEN t.type_id = '3c943a39-4a3b-41fe-b13c-61acce180a54' THEN t.metadata->>'comment' END), '') AS rcucomment,
            COALESCE(MAX(CASE WHEN t.type_id = '8f4996d0-940b-4be9-8b77-aa55c82337e1' THEN t.metadata->>'comment' END), '') AS creditcomment,
            COALESCE(STRING_AGG(
                CASE WHEN t.type_id = 'cf8e1416-4fac-4a26-8ca2-5c3db4c70b08' THEN wi.current_state ELSE NULL END,
                ', ' ORDER BY t.created_at DESC
            ), '') AS creditconditions,
            COALESCE(STRING_AGG(
                CASE WHEN t.type_id = '32f33cc5-3bbe-41ae-b8b0-8655cc02caa7' THEN wi.current_state ELSE NULL END,
                ', ' ORDER BY t.created_at DESC
            ), '') AS rcuconditions
        FROM loan_application la
        JOIN users u ON la.user_id = u.user_id
        LEFT JOIN tasks t ON la.loan_application_id = t.identifier_id
        LEFT JOIN workflow_instances wi ON wi.identifier_id = t.id
        WHERE %s %s
        GROUP BY la.loan_application_id, u.unique_id, u.dynamic_user_info
        ORDER BY la.loan_application_id;
    `, extraCondition, dateFilter)

	query, args, err := sqlx.Named(query, attributes)
	if err != nil {
		logger.WithContext(ctx).Errorf("[GetMisLoanDataDump] failed to sql named error: %v, query: %v", err, query)
		return nil, err
	}

	query, args, err = sqlx.In(query, args...)
	if err != nil {
		logger.WithContext(ctx).Errorf("[GetMisLoanDataDump] failed to sql In error: %v, query: %v", err, query)
		return nil, err
	}

	query = database.Rebind(query)

	rows, err := database.QueryxContext(ctx, query, args...)
	if err != nil {
		logger.WithContext(ctx).Errorf("[GetMisLoanDataDump] failed to execute query error: %v, query: %v", err, query)
		return nil, err
	}
	return rows, nil
}

// To pick activity info from activity log table if source entity is muthoot
func getUserActivityInfo(ctx context.Context, rows *sqlx.Rows, lenderID string) (*sqlx.Rows, error) {

	columns, err := rows.Columns()
	if err != nil {
		return nil, fmt.Errorf("error getting columns: %v", err)
	}

	var loanIDs []string
	var originalData [][]interface{}

	for rows.Next() {
		columnPointers := make([]interface{}, len(columns))
		rowData := make([]interface{}, len(columns))

		for i := range columns {
			columnPointers[i] = new(interface{})
		}

		if err := rows.Scan(columnPointers...); err != nil {
			return nil, fmt.Errorf("error scanning row: %w", err)
		}

		var loanAppID string
		for i, colName := range columns {
			rowData[i] = *(columnPointers[i].(*interface{}))
			if colName == "loanapplicationid" {
				if loanAppIDBytes, ok := rowData[i].([]uint8); ok {
					loanAppID = string(loanAppIDBytes)
				}
			}
		}

		if loanAppID != "" {
			loanIDs = append(loanIDs, loanAppID)
		}

		originalData = append(originalData, rowData)
	}

	rows.Close()

	if len(loanIDs) == 0 {
		return rows, nil
	}

	query := ""

	if lenderID == constants.MFLBLID {
		query = fmt.Sprintf(`
		WITH activity_cte AS (
			SELECT DISTINCT ON (loan_application_id) 
        		loan_application_id, 
				COALESCE(entity_ref, '') AS kycapprovedby
			FROM activity_log
			WHERE event_type = 'kyc_verified'
			AND loan_application_id = ANY(ARRAY[%s]::uuid[])
		)
		SELECT o.*, 
			COALESCE(a.kycapprovedby, '') AS kycapprovedby,
			COALESCE(l.name, '') AS kycapprovername
		FROM (VALUES %s) AS o(%s) 
		LEFT JOIN activity_cte a ON o.loanapplicationid::uuid = a.loan_application_id
		LEFT JOIN lender_users l ON l.email = a.kycapprovedby
	`, "'"+strings.Join(loanIDs, "'::uuid, '")+"'::uuid", generateSQLValues(originalData), strings.Join(columns, ", "))
	}

	if lenderID == constants.MuthootCLID {
		query = fmt.Sprintf(`
		WITH activity_cte AS (
			SELECT loan_application_id, 
				COALESCE(TO_CHAR(logged_at, 'YYYY-MM-DD'), '') AS actionedon, 
				COALESCE(event_description, '') AS remark
			FROM activity_log
			WHERE entity_type = 'lender_agent'
			AND event_type = 'kyc_manual_success'
			AND loan_application_id = ANY(ARRAY[%s]::uuid[])
		)
		SELECT o.*, 
			COALESCE(a.actionedon, '') AS actionedon, 
			COALESCE(a.remark, '') AS remark
		FROM (VALUES %s) AS o(%s) 
		LEFT JOIN activity_cte a ON o.loanapplicationid::uuid = a.loan_application_id
	`, "'"+strings.Join(loanIDs, "'::uuid, '")+"'::uuid", generateSQLValues(originalData), strings.Join(columns, ", "))
	}

	return database.QueryxContext(ctx, query)
}

func generateSQLValues(data [][]interface{}) string {
	var valueStrings []string

	for _, row := range data {
		var formattedValues []string
		for _, col := range row {
			switch v := col.(type) {
			case string:
				formattedValues = append(formattedValues, fmt.Sprintf("'%s'", strings.ReplaceAll(v, "'", "''")))
			case []byte:
				formattedValues = append(formattedValues, fmt.Sprintf("'%s'", strings.ReplaceAll(string(v), "'", "''")))
			case int, int8, int16, int32, int64:
				formattedValues = append(formattedValues, fmt.Sprintf("%d", v))
			case float32, float64:
				formattedValues = append(formattedValues, fmt.Sprintf("%g", v))
			case nil:
				formattedValues = append(formattedValues, "NULL")
			default:
				formattedValues = append(formattedValues, fmt.Sprintf("%v", v))
			}
		}
		valueStrings = append(valueStrings, fmt.Sprintf("(%s)", strings.Join(formattedValues, ", ")))
	}

	return strings.Join(valueStrings, ", ")
}

func GetFileDetails(exportID string, fileExtension string) (string, string) {
	fileName := ""
	filePath := ""
	if fileExtension == "xlsx" {
		fileName = exportID + "-dump.xlsx"
		filePath = "/tmp/" + fileName
	} else {
		fileName = exportID + "-dump.csv"
		filePath = "/tmp/" + fileName
	}
	return fileName, filePath
}

// CreateExportLog creates a new export table entry
func CreateExportLog(email string, filter map[string]interface{}, api string, dashboardType string, agentIdentifier string, agentUserID string, fileExtension string) (string, *os.File, error) {

	filterBytes, err := json.Marshal(filter)
	if err != nil {
		logger.WithDefault().Errorf("[CreateExportLog] failed to marshal json err: %v, filter: %+v", err, filter)
		return "", nil, err
	}

	exportID := general.GetUUID()
	_, filePath := GetFileDetails(exportID, fileExtension)
	file, err := os.Create(filepath.Clean(filePath))
	if err != nil {
		logger.WithDefault().Errorf("[CreateExportLog] failed to create os file err: %v, filter: %+v", err, filter)
		return "", nil, err
	}
	err = exportlogs.Insert(exportID, email, api, dashboardType, agentIdentifier, string(filterBytes), agentUserID)
	if err != nil {
		return "", nil, err
	}
	return exportID, file, nil
}

// SendExportMail is used to send email(s) and mark export task as completed
func SendExportMail(exportID string, nameHeading string, api string, fromDate string, toDate string, filter string, email string, fileExtension string) error {
	fileName, filePath := GetFileDetails(exportID, fileExtension)

	f, err := os.Open(filepath.Clean(filePath))
	if err != nil {
		log.Println(err)
		return err
	}
	fi, err := f.Stat()
	if err != nil {
		log.Println(err)
		return err
	}
	emailData := map[string]interface{}{
		"Type":        api,
		"FromDate":    fromDate,
		"ToDate":      toDate,
		"Filter":      filter,
		"NameHeading": nameHeading,
		"Link":        "",
	}
	s3Path := "exports/" + fileName
	_, flag := s3.UploadFileS3(filePath, s3Path)
	if !flag {
		log.Println("unable to upload file to s3")
	}
	attachments := []emaillib.EmailAttachment{{FileName: fileName, Path: filePath, Type: "file"}}
	fileSize := fi.Size()
	if fileSize > 20971520 { // 20 MB
		// don't send attachments
		attachments = []emaillib.EmailAttachment{}
		link := s3.GetPresignedURLS3(s3Path, 24*60)
		emailData["Link"] = fmt.Sprintf(`<a href = "%s">Download FILE (Valid for 24 hours)</a>`, link)
	}
	subject := general.GetStringFromTemplate(emaillib.ExportDumpEmailSubject, emailData)
	htmlBody := general.GetStringFromTemplate(emaillib.ExportDumpEmailHTML, emailData)
	done, failReason := emaillib.SendMail([]string{email}, []string{"User"}, subject, htmlBody,
		attachments, false)
	status := exportlogs.StatusFailed
	if done {
		status = exportlogs.StatusCompleted
	}
	// delete file if exists
	_ = os.Remove(filePath)
	err = exportlogs.Update(status, failReason, s3Path, exportID)
	if err != nil {
		log.Errorln(err)
		return err
	}

	return nil
}

// SendAdminPageExportMail is used to send email(s) and mark export task as completed
func SendAdminPageExportMail(exportID string, api string, email string, fileExtension string) error {
	fileName, filePath := GetFileDetails(exportID, fileExtension)

	f, err := os.Open(filepath.Clean(filePath))
	if err != nil {
		log.Println(err)
		return err
	}
	fi, err := f.Stat()
	if err != nil {
		log.Println(err)
		return err
	}
	emailData := map[string]interface{}{
		"Type": api,
		"Link": "",
	}
	s3Path := "exports/" + fileName
	_, flag := s3.UploadFileS3(filePath, s3Path)
	if !flag {
		log.Println("unable to upload file to s3")
	}
	attachments := []emaillib.EmailAttachment{{FileName: fileName, Path: filePath, Type: "file"}}
	fileSize := fi.Size()
	if fileSize > 20971520 { // 20 MB
		// don't send attachments
		attachments = []emaillib.EmailAttachment{}
		link := s3.GetPresignedURLS3(s3Path, 24*60)
		emailData["Link"] = fmt.Sprintf(`<a href = "%s">Download FILE (Valid for 24 hours)</a>`, link)
	}
	subject := general.GetStringFromTemplate(emaillib.AdminPageEmailSubject, emailData)
	htmlBody := general.GetStringFromTemplate(emaillib.AdminPageExportDumpEmailHTML, emailData)
	done, failReason := emaillib.SendMail([]string{email}, []string{"User"}, subject, htmlBody,
		attachments, false)
	status := exportlogs.StatusFailed
	if done {
		status = exportlogs.StatusCompleted
	}
	// delete file if exists
	_ = os.Remove(filePath)
	err = exportlogs.Update(status, failReason, s3Path, exportID)
	if err != nil {
		log.Errorln(err)
		return err
	}

	return nil
}

func SendPaymentExportMail(exportID string, nameHeading string, api string, requestID string, filter string, email string, fileExtension string) error {
	fileName, filePath := GetFileDetails(exportID, fileExtension)
	emailData := map[string]interface{}{
		"Type":        api,
		"RequestID":   requestID,
		"Filter":      filter,
		"NameHeading": nameHeading,
	}

	subject := general.GetStringFromTemplate(emaillib.ExportPaymentDumpEmailSubject, emailData)
	htmlBody := general.GetStringFromTemplate(emaillib.ExportPaymentDumpEmailHTML, emailData)
	done, failReason := emaillib.SendMail([]string{email}, []string{"User"}, subject, htmlBody,
		[]emaillib.EmailAttachment{{FileName: fileName, Path: filePath, Type: "file"}}, false)
	s3Path := "exports/" + fileName
	_, flag := s3.UploadFileS3(filePath, s3Path)
	if !flag {
		log.Println("unable to upload file to s3")
	}
	// delete file if exists
	_ = os.Remove(filePath)
	status := "failed"
	if done {
		status = "completed"
	}
	err := exportlogs.Update(status, failReason, s3Path, exportID)
	if err != nil {
		log.Errorln(err)
		return err
	}
	return nil
}

func PFLCustomLeadDump(ctx context.Context, filter string, fromDate string, toDate string, sourceEntityID string, leadStageFilters []int, loanStageFilters [][]int) ([]PFLCustomLeadDumpRow, error) {
	var result []PFLCustomLeadDumpRow

	params := map[string]interface{}{
		"fromdate":         fromDate,
		"todate":           toDate,
		"source_entity_id": sourceEntityID,
		"user_status":      constants.UserStatusArchived,
	}

	switch filter {
	case "created_at":
		filter = "u.created_at"
	case "updated_at":
		filter = "u.updated_at"
	case "disbursed_at":
		filter = "la.disbursed_date"
	}

	query := fmt.Sprintf(`
		select distinct on(u.user_id)
      u.user_id as user_id,
      coalesce(u.crm_id,'') as lead_id,
      coalesce(u.unique_id,'') as customer_id,
      coalesce(u.source,'') as source,
      u.status as status,
      coalesce(to_char(u.created_at at time zone 'utc' at time zone 'Asia/Calcutta', 'YYYY-MM-DD HH24::MI::SS'), '') as user_created_at,
      coalesce(u.name,'') as name,
      coalesce(u.email,'') as email,
	  coalesce(u.pan,'') as pan,
	  coalesce(u.mobile,'') as mobile,
	  coalesce(NULLIF(uld.permanent_address, '')::::json->>'pincode', '') as permanent_address_pincode,
	  coalesce(la.loan_application_id::::TEXT, '') as loan_application_id,
      coalesce(la.loan_application_no,'') as loan_application_no,
      coalesce(la.status, -5) as loan_status,
      coalesce(to_char(la.created_at at time zone 'utc' at time zone 'Asia/Calcutta', 'YYYY-MM-DD HH24::MI::SS'), '') as application_created_at,
      la.applied_amount as loan_amount,
	  la.amount as amount,
      coalesce(la.applied_tenure::::TEXT,'') as tenure,
      coalesce(la.interest::::TEXT,'') as annual_interest,
      la.emi as emi_amount,
      la.processing_fee as processing_fee,
      la.gst as gst,
      la.advance_emi as advance_emi,
      case when la.status=7 then la.amount else null end as disbursal_amount,
      coalesce(to_char(la.sign_date AT TIME ZONE 'utc' at time zone 'Asia/Calcutta', 'YYYY-MM-DD HH24::MI::SS'), '') as sign_agreement_date,
	  coalesce(la.kyc_status,-5) as loan_kyc_status,
      coalesce(u.dynamic_user_info::::json->>'breLatitude',ul.lat::::TEXT, '') as latitude,
      coalesce(u.dynamic_user_info::::json->>'breLongitude',ul.lon::::TEXT, '') as longitude,
	  coalesce(plo.max_amount, ue.eligible_amount, 0.0) as eligible_amount,
	  coalesce(plo.max_tenure::::TEXT, ue.tenure::::TEXT, '') as eligible_tenure,
	  coalesce(u.dynamic_user_info::::json->'hypervergeFatchMatch'->>'faceMatchScore','') as face_match_score,
      coalesce(u.dynamic_user_info::::json->'hypervergeFatchMatch'->>'faceMatchResult','') as face_match_status,
      coalesce(u.dynamic_user_info::::json->'hypervergeFatchMatch'->>'faceMatchConfidenceScore','') as face_match_confidence_score,
      case
        when u.dynamic_user_info::::json->'distanceFromCurrentAddressCoordinates' is null or
             u.dynamic_user_info::::json->>'distanceFromCurrentAddressCoordinates'='-1' then 'CAN''T DECIDE'
        when (der.output_variables::::json->'distance_flag')::::text='true' then 'PASS'
        when (der.output_variables::::json->'distance_flag')::::text='false' then 'FAIL'
        else ''
      END as radius_flag_result,
      case
        when u.dynamic_user_info::::json->'distanceFromCurrentAddressCoordinates' is null or
             u.dynamic_user_info::::json->>'distanceFromCurrentAddressCoordinates'='-1' then ''
        else u.dynamic_user_info::::json->>'distanceFromCurrentAddressCoordinates'
      END as radius_value_kms,
      coalesce(u.dynamic_user_info::::json->>'blockUserDueToDistance','') as block_user_due_to_distance,
	  coalesce(u.dynamic_user_info::::json->>'occupationType','') as occupation_type,
      coalesce(u.dynamic_user_info::::json->>'permanentAddressCity','') as city,
      coalesce(u.dynamic_user_info::::json->>'permanentAddressState','') as state,
      coalesce(u.dynamic_user_info::::json->>'permanentAddressPincode','') as pincode,
      coalesce(i.insurance_type, '') as vas_type,
      i.premium as vas_charges
      from
      users u
      left join user_loan_details uld on (u.user_id = uld.user_id)
      left join loan_application la on (u.user_id = la.user_id)
      left join user_location ul on (u.user_id = ul.user_id)
      left join decision_engine_response der on (u.user_id = der.user_id)
	  left join user_eligibility ue on (u.user_id = ue.user_id)
	  left join personal_loan_offer plo on (u.user_id = plo.user_id)
      left join insurance i on (la.loan_application_id=i.loan_application_id)      
      where u.source_entity_id=:source_entity_id and u.status<>:user_status and 
      date(%s at time zone 'utc' at time zone 'Asia/Calcutta') >= :fromdate and 
      date(%s at time zone 'utc' at time zone 'Asia/Calcutta') <= :todate
  `, filter, filter)

	extraCondition := []string{}
	if len(leadStageFilters) != 0 {
		extraCondition = append(extraCondition, `u.status in (:user_statuses)`)
		params["user_statuses"] = leadStageFilters
	}

	if len(loanStageFilters) > 0 {
		for _, statusPair := range loanStageFilters {
			statusQuery := fmt.Sprintf("la.status = %d", statusPair[0])
			if statusPair[1] > 0 {
				statusQuery += fmt.Sprintf(" AND la.kyc_status = %d", statusPair[1])
			} else if statusPair[1] == 0 {
				statusQuery += " AND (la.kyc_status = 0 OR la.kyc_status is NULL)"
			}
			extraCondition = append(extraCondition, fmt.Sprintf("(%s)", statusQuery))
		}
	}

	if len(extraCondition) != 0 {
		query += fmt.Sprintf(" and (%s)", strings.Join(extraCondition, " or "))
	}
	query += ` order by u.user_id, la.created_at DESC, der.created_at DESC, ue.created_at DESC, plo.created_at, ul.created_at DESC, i.created_at DESC `

	query, args, err := sqlx.Named(query, params)
	if err != nil {
		return nil, err
	}
	query, args, err = sqlx.In(query, args...)
	if err != nil {
		return nil, err
	}
	query = database.Rebind(query)
	err = database.SelectContext(ctx, &result, query, args...)
	if err != nil {
		return nil, err
	}

	rejectedLeadIds := []string{}
	rejectedLeads := map[string]*PFLCustomLeadDumpRow{}
	softRejectedLeadIds := []string{}
	disbursedLeads := []string{}
	disbursedLeadsMap := map[string]*PFLCustomLeadDumpRow{}
	for index := range result {
		rowPtr := &result[index]
		rowPtr.Platform = "Poonawalla Fincorp - PL"
		if rowPtr.LoanStatus == constants.LoanStatusDisbursed || rowPtr.LoanStatus == constants.LoanStatusClosed {
			disbursedLeads = append(disbursedLeads, rowPtr.UserID)
			disbursedLeadsMap[rowPtr.UserID] = rowPtr
			t := calc.CalculateDisbursalAmount(rowPtr.Amount.Float64, rowPtr.ProcessingFee.Float64,
				rowPtr.GST.Float64, rowPtr.AdvanceEMI.Float64, 0, commonutils.OtherCharges(constants.PoonawallaFincorpID, rowPtr.UserID), "")
			if rowPtr.Amount.Valid {
				rowPtr.NetDisbursalAmount = sql.NullFloat64{Float64: t, Valid: t > 0}
			}

		} else if rowPtr.Status == constants.UserStatusDisqualified || rowPtr.LoanStatus == constants.LoanStatusLoanRejected {
			rejectedLeadIds = append(rejectedLeadIds, rowPtr.UserID)
			rejectedLeads[rowPtr.UserID] = rowPtr
		} else if general.InArr(constants.GetLoanStatusText(rowPtr.LoanStatus, rowPtr.LoanKYCStatus),
			[]string{"BANK_FAILED", "KYC_REJECTED"}) {
			softRejectedLeadIds = append(softRejectedLeadIds, rowPtr.UserID)
			rejectedLeads[rowPtr.UserID] = rowPtr
		}
	}

	disbursalTimestamps, err := getDisbursalTimestamp(ctx, disbursedLeads)
	if err != nil {
		log.Error(err)
		return nil, err
	}
	for _, r := range disbursalTimestamps {
		disbursedLeadsMap[r.UserID].DisbursedOrActivationDate = r.DisbursalTimestamp
	}

	if len(rejectedLeadIds) != 0 || len(softRejectedLeadIds) != 0 {
		rejectionReasons, err := getRejectionReasonForUsers(ctx, rejectedLeadIds, softRejectedLeadIds)

		if err != nil {
			log.Debug("Error: ", err)
			return nil, err
		}

		for _, row := range rejectionReasons {
			rejectedLeads[row.UserID].RejectionReason = row.RejectionReason
		}
	}
	return result, nil

}

func PFLCustomLeadDumpV2(ctx context.Context, filter string, fromDate string, toDate string, sourceEntityID string, leadStageFilters []int, loanStageFilters [][]int, segments []string) (*sqlx.Rows, error) {
	params := map[string]interface{}{
		"fromdate":         fromDate,
		"todate":           toDate,
		"source_entity_id": sourceEntityID,
		"user_status":      constants.UserStatusArchived,
	}

	switch filter {
	case "created_at":
		filter = "u.created_at"
	case "updated_at":
		filter = "u.updated_at"
	case "disbursed_at":
		filter = "la.disbursed_date"
	}

	query := fmt.Sprintf(`
		select distinct on(u.user_id)
      u.user_id as user_id,
      coalesce(u.crm_id,'') as lead_id,
      coalesce(u.unique_id,'') as customer_id,
      coalesce(u.source,'') as source,
      u.status as status,
      coalesce(to_char(u.created_at at time zone 'utc' at time zone 'Asia/Calcutta', 'YYYY-MM-DD HH24::MI::SS'), '') as user_created_at,
      coalesce(u.name,'') as name,
      coalesce(u.email,'') as email,
	  coalesce(u.pan,'') as pan,
	  coalesce(u.mobile,'') as mobile,
	  coalesce(NULLIF(uld.permanent_address, '')::::json->>'pincode', '') as permanent_address_pincode,
	  coalesce(la.loan_application_id::::TEXT, '') as loan_application_id,
      coalesce(la.loan_application_no,'') as loan_application_no,
      coalesce(la.status, -5) as loan_status,
      coalesce(to_char(la.created_at at time zone 'utc' at time zone 'Asia/Calcutta', 'YYYY-MM-DD HH24::MI::SS'), '') as application_created_at,
      la.applied_amount as loan_amount,
	  la.amount as amount,
      coalesce(la.applied_tenure::::TEXT,'') as tenure,
      coalesce(la.interest::::TEXT,'') as annual_interest,
      la.emi as emi_amount,
      la.processing_fee as processing_fee,
      la.gst as gst,
      la.advance_emi as advance_emi,
      case when la.status=7 then la.amount else null end as disbursal_amount,
      coalesce(to_char(la.sign_date AT TIME ZONE 'utc' at time zone 'Asia/Calcutta', 'YYYY-MM-DD HH24::MI::SS'), '') as sign_agreement_date,
	  coalesce(la.kyc_status,-5) as loan_kyc_status,
      coalesce(u.dynamic_user_info::::json->>'breLatitude',ul.lat::::TEXT, '') as latitude,
      coalesce(u.dynamic_user_info::::json->>'breLongitude',ul.lon::::TEXT, '') as longitude,
	  coalesce(plo.max_amount, ue.eligible_amount, 0.0) as eligible_amount,
	  coalesce(plo.max_tenure::::TEXT, ue.tenure::::TEXT, '') as eligible_tenure,
	  coalesce(u.dynamic_user_info::::json->'hypervergeFatchMatch'->>'faceMatchScore','') as face_match_score,
      coalesce(u.dynamic_user_info::::json->'hypervergeFatchMatch'->>'faceMatchResult','') as face_match_status,
      coalesce(u.dynamic_user_info::::json->'hypervergeFatchMatch'->>'faceMatchConfidenceScore','') as face_match_confidence_score,
      coalesce(rl.rejection_reason,'') as rejection_reason,
      case
        when u.dynamic_user_info::::json->'distanceFromCurrentAddressCoordinates' is null or
             u.dynamic_user_info::::json->>'distanceFromCurrentAddressCoordinates'='-1' then 'CAN''T DECIDE'
        when (der.output_variables::::json->'distance_flag')::::text='true' then 'PASS'
        when (der.output_variables::::json->'distance_flag')::::text='false' then 'FAIL'
        else ''
      END as radius_flag_result,
      case
        when u.dynamic_user_info::::json->'distanceFromCurrentAddressCoordinates' is null or
             u.dynamic_user_info::::json->>'distanceFromCurrentAddressCoordinates'='-1' then ''
        else u.dynamic_user_info::::json->>'distanceFromCurrentAddressCoordinates'
      END as radius_value_kms,
      coalesce(u.dynamic_user_info::::json->>'blockUserDueToDistance','') as block_user_due_to_distance,
	  coalesce(u.dynamic_user_info::::json->>'occupationType','') as occupation_type,
      coalesce(u.dynamic_user_info::::json->>'permanentAddressCity','') as city,
      coalesce(u.dynamic_user_info::::json->>'permanentAddressState','') as state,
      coalesce(u.dynamic_user_info::::json->>'permanentAddressPincode','') as pincode,
	  coalesce(dynamic_user_info::::jsonb->>'workExperience','')as work_experience,
	  coalesce(dynamic_user_info::::jsonb->>'segment','')as segment,
	  coalesce(dynamic_user_info::::jsonb->>'officialEmail','')as official_email,
	  coalesce(dynamic_user_info::::jsonb->>'monthlyIncome', '0') as monthly_income,
	  coalesce(dynamic_user_info::::jsonb->>'employerName', '') as employer_name,
      coalesce(i.insurance_type, '') as vas_type,
      i.premium as vas_charges
      from
      users u
      left join user_loan_details uld on (u.user_id = uld.user_id)
      left join loan_application la on (u.user_id = la.user_id)
      left join user_location ul on (u.user_id = ul.user_id)
      left join decision_engine_response der on (u.user_id = der.user_id)
	  left join user_eligibility ue on (u.user_id = ue.user_id)
	  left join personal_loan_offer plo on (u.user_id = plo.user_id)
      left join rejection_logs rl on (u.user_id = rl.user_id)
      left join insurance i on (la.loan_application_id=i.loan_application_id)      
      where u.source_entity_id=:source_entity_id and u.status<>:user_status and 
      date(%s at time zone 'utc' at time zone 'Asia/Calcutta') >= :fromdate and 
      date(%s at time zone 'utc' at time zone 'Asia/Calcutta') <= :todate
  `, filter, filter)

	extraCondition := []string{}
	if len(leadStageFilters) != 0 {
		extraCondition = append(extraCondition, `u.status in (:user_statuses)`)
		params["user_statuses"] = leadStageFilters
	}

	if len(loanStageFilters) > 0 {
		for _, statusPair := range loanStageFilters {
			statusQuery := fmt.Sprintf("la.status = %d", statusPair[0])
			if statusPair[1] > 0 {
				statusQuery += fmt.Sprintf(" AND la.kyc_status = %d", statusPair[1])
			} else if statusPair[1] == 0 {
				statusQuery += " AND (la.kyc_status = 0 OR la.kyc_status is NULL)"
			}
			extraCondition = append(extraCondition, fmt.Sprintf("(%s)", statusQuery))
		}
	}

	if len(extraCondition) != 0 {
		query += fmt.Sprintf(" and (%s)", strings.Join(extraCondition, " or "))
	}

	if len(segments) != 0 {
		segmentFilter := `(u.dynamic_user_info::::jsonb ->> 'segment') IN (:segments)`
		query += fmt.Sprintf(" and (%s)", segmentFilter)
		params["segments"] = segments
	}

	query += ` order by u.user_id, la.created_at DESC, der.created_at DESC, ue.created_at DESC, plo.created_at, ul.created_at DESC, i.created_at DESC,rl.logged_at DESC `

	query, args, err := sqlx.Named(query, params)
	if err != nil {
		return nil, err
	}
	query, args, err = sqlx.In(query, args...)
	if err != nil {
		return nil, err
	}
	query = database.Rebind(query)
	rows, err := database.QueryxContext(ctx, query, args...)
	if err != nil {
		return nil, err
	}

	return rows, nil
}

func getNetDisbursedAmount(ctx context.Context, leads []PFLCustomLeadDumpRow) error {
	loanAppIDs := []string{}
	loanAppMap := map[string]*PFLCustomLeadDumpRow{}
	params := map[string]interface{}{}

	type dbObj struct {
		LoanApplicationID string          `db:"loan_application_id"`
		Premium           sql.NullFloat64 `db:"premium"`
		CreatedAt         time.Time       `db:"created_at"`
		InsuranceType     string          `db:"insurance_type"`
	}
	var result []dbObj

	for i := range leads {
		if len(leads[i].LoanApplicationID) == 0 || !general.InArr(leads[i].LoanStatus, []int{constants.LoanStatusDisbursed, constants.LoanStatusClosed}) {
			continue
		}
		// Review this condition
		if !leads[i].Amount.Valid {
			continue
		}
		loanAppIDs = append(loanAppIDs, leads[i].LoanApplicationID)
		loanAppMap[leads[i].LoanApplicationID] = &leads[i]
	}

	if len(loanAppIDs) == 0 {
		return nil
	}

	params["loan_application_ids"] = pq.Array(loanAppIDs)
	query := `select i.loan_application_id, i.premium , coalesce(i.insurance_type, '') as insurance_type
				from insurance as i
				join (select loan_application_id, max(created_at) as created_at
					from insurance
  where loan_application_id=ANY(:loan_application_ids)
					group by loan_application_id) as l
				on i.loan_application_id=l.loan_application_id and i.created_at=l.created_at`

	query, args, err := sqlx.Named(query, params)
	if err != nil {
		return err
	}
	query, args, err = sqlx.In(query, args...)
	if err != nil {
		return err
	}
	query = database.Rebind(query)
	err = database.SelectContext(ctx, &result, query, args...)
	if err != nil {
		return err
	}

	for i := range result {
		loanApp := loanAppMap[result[i].LoanApplicationID]
		t := calc.CalculateDisbursalAmount(loanApp.Amount.Float64, loanApp.ProcessingFee.Float64,
			loanApp.GST.Float64, loanApp.AdvanceEMI.Float64, result[i].Premium.Float64, commonutils.OtherCharges(constants.PoonawallaFincorpID, loanApp.UserID), result[i].InsuranceType)
		loanApp.NetDisbursalAmount = sql.NullFloat64{Float64: t, Valid: true}
		loanAppMap[result[i].LoanApplicationID] = nil
	}

	for _, loanApp := range loanAppMap {
		if loanApp == nil {
			continue
		}
		t := calc.CalculateDisbursalAmount(loanApp.Amount.Float64, loanApp.ProcessingFee.Float64,
			loanApp.GST.Float64, loanApp.AdvanceEMI.Float64, 0.0, commonutils.OtherCharges(constants.PoonawallaFincorpID, loanApp.UserID), "")
		loanApp.NetDisbursalAmount = sql.NullFloat64{Float64: t, Valid: true}
	}

	return nil
}

func getOfferDetails(ctx context.Context, leads []PFLCustomLeadDumpRow) error {
	leadIDs := []string{}
	leadMap := map[string]*PFLCustomLeadDumpRow{}
	params := map[string]interface{}{}

	type dbObj struct {
		UserID         string          `db:"user_id"`
		EligibleAmount sql.NullFloat64 `db:"eligible_amount"`
		Tenure         string          `db:"tenure"`
		CreatedAt      time.Time       `db:"created_at"`
	}
	var result []dbObj

	for i := range leads {
		leadIDs = append(leadIDs, leads[i].UserID)
		leadMap[leads[i].UserID] = &leads[i]
	}

	if len(leadIDs) == 0 {
		return nil
	}

	params["lead_ids"] = pq.Array(leadIDs)

	query := `select ue.user_id,
				coalesce(ue.eligible_amount, 0.0) as eligible_amount,
				coalesce(ue.tenure::::TEXT, '') as tenure
				from user_eligibility as ue
				join (select user_id, max(created_at) as created_at
					from user_eligibility
          where user_id=ANY(:lead_ids)
          group by user_id) as l
				on ue.user_id=l.user_id and ue.created_at=l.created_at`

	query, args, err := sqlx.Named(query, params)
	if err != nil {
		return err
	}
	query, args, err = sqlx.In(query, args...)
	if err != nil {
		return err
	}
	query = database.Rebind(query)
	err = database.SelectContext(ctx, &result, query, args...)
	if err != nil {
		return err
	}

	for i := range result {
		leadMap[result[i].UserID].EligibleAmount = result[i].EligibleAmount
		leadMap[result[i].UserID].EligibleTenure = result[i].Tenure
	}

	var resultOfferFlow []dbObj
	params["lead_ids"] = pq.Array(leadIDs)
	query = `select ue.user_id,
				coalesce(ue.max_amount, 0.0) as eligible_amount,
				coalesce(ue.max_tenure::::TEXT, '') as tenure
				from personal_loan_offer as ue
				join (select user_id, max(created_at) as created_at
					from personal_loan_offer
  where user_id=ANY(:lead_ids)
					group by user_id) as l
				on ue.user_id=l.user_id and ue.created_at=l.created_at`

	query, args, err = sqlx.Named(query, params)
	if err != nil {
		return err
	}
	query, args, err = sqlx.In(query, args...)
	if err != nil {
		return err
	}
	query = database.Rebind(query)
	err = database.SelectContext(ctx, &resultOfferFlow, query, args...)
	if err != nil {
		return err
	}

	for i := range resultOfferFlow {
		leadMap[resultOfferFlow[i].UserID].EligibleAmount = resultOfferFlow[i].EligibleAmount
		leadMap[resultOfferFlow[i].UserID].EligibleTenure = resultOfferFlow[i].Tenure
	}
	return nil
}

func getDisbursalTimestamp(ctx context.Context, leadIDs []string) ([]DisbursalRow, error) {
	if len(leadIDs) == 0 {
		return nil, nil
	}

	params := make(map[string]interface{})

	var result []DisbursalRow
	query := `select distinct on(al.user_id) al.user_id,
				coalesce(to_char(logged_at AT TIME ZONE 'utc' at time zone 'Asia/Calcutta', 'YYYY-MM-DD HH24::MI::SS'), '') as disbursal_timestamp
				from activity_log al
        JOIN unnest(ARRAY[:user_ids]::::uuid[]) AS user_ids ON user_ids= al.user_id
        where event_type in (:event_types)`

	params["user_ids"] = leadIDs
	params["event_types"] = []string{constants.ActivityLoanDisbursed}

	query, args, err := sqlx.Named(query, params)
	if err != nil {
		return nil, err
	}
	query, args, err = sqlx.In(query, args...)
	if err != nil {
		return nil, err
	}
	query = database.Rebind(query)
	err = database.SelectContext(ctx, &result, query, args...)
	if err != nil {
		return nil, err
	}

	return result, nil
}

func getRejectionReasonForUsers(ctx context.Context, rejectionUserIds []string, softRejectionUserIds []string) ([]RejectionReasonStruct, error) {
	if len(rejectionUserIds) == 0 && len(softRejectionUserIds) == 0 {
		return nil, nil
	}
	var result []RejectionReasonStruct
	queries := []string{}
	params := map[string]interface{}{}

	if len(rejectionUserIds) != 0 {
		queries = append(queries, `(SELECT  distinct on(user_id) user_id, coalesce(rejection_reason,'') as rejection_reason from rejection_logs
      where user_id=ANY(:rejection_user_ids)
  and event_type in (:user_disqualified,:loan_rejected)
  order by user_id, logged_at desc
  )`)
		params["rejection_user_ids"] = pq.Array(rejectionUserIds)
		params["user_disqualified"] = constants.ActivityUserDisqualified
		params["loan_rejected"] = constants.ActivityLoanRejected
	}

	if len(softRejectionUserIds) != 0 {
		queries = append(queries, `(SELECT  distinct on(user_id) user_id, coalesce(event_description,'') as rejection_reason from activity_log
      where user_id=ANY(:soft_rejection_user_ids)
  and event_type in (:kyc_doc_rejected,:bank_verification_failed)
  order by user_id, logged_at desc
  )`)
		params["soft_rejection_user_ids"] = pq.Array(softRejectionUserIds)
		params["kyc_doc_rejected"] = constants.ActivityKYCDocRejected
		params["bank_verification_failed"] = constants.ActivityBankVerificationFailed
	}

	query := strings.Join(queries, " UNION ")

	query, args, err := sqlx.Named(query, params)
	if err != nil {
		return nil, err
	}
	query, args, err = sqlx.In(query, args...)
	if err != nil {
		return nil, err
	}
	query = database.Rebind(query)
	err = database.SelectContext(ctx, &result, query, args...)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func PFLEnachMandateReport(ctx context.Context, fromDate string, toDate string, sourceEntityID string) ([]PFLEnachMandateReportRow, error) {
	var result []PFLEnachMandateReportRow
	params := map[string]interface{}{
		"fromdate":  fromDate,
		"todate":    toDate,
		"lender_id": sourceEntityID,
	}

	query := `
       select distinct on(de.loan_application_id)
           coalesce(lv.lsq_lead_id,'') as crm_id,
        coalesce(la.loan_application_no,'') as loan_application_no,
        la.status as loan_status,
	      coalesce(la.kyc_status,-1) as loan_kyc_status,
        coalesce(de.ifsc,'') as ifsc,
        coalesce(de.umrn,'') as umrn,
        coalesce(i.branch_code,'') as branch_code,
        coalesce(i.micr,'') as micr,
        coalesce(de.bank_name,'') as bank_name,
        coalesce(de.account_type,'') as account_type,
        coalesce(de.account_number,'') as account_no,
        coalesce(de.account_holder_name,'') as account_name,
        coalesce(de.mandate_amount,0.0) as cap_amount,
        coalesce(de.status,-1) as registration_status,
        coalesce(to_char(de.updated_at at time zone 'utc' at time zone 'Asia/Calcutta', 'YYYY-MM-DD HH24::MI::SS'), '') as registration_timestamp,
        coalesce(lv.loan_account_number, '') as loan_account_number
        from lender_variables lv
        join digio_enach de on (de.loan_application_id=lv.loan_application_id)
        left join ifsc i on (de.ifsc=i.ifsc)
        join loan_application la on (la.loan_application_id = de.loan_application_id)
        where lv.lender_id=:lender_id and
        date(de.created_at at time zone 'utc' at time zone 'Asia/Calcutta') >= :fromdate and
        date(de.created_at at time zone 'utc' at time zone 'Asia/Calcutta') <= :todate
       order by de.loan_application_id,de.created_at desc
  `
	namedStatement, err := database.PrepareNamed(query)
	if err != nil {
		return nil, err
	}
	err = namedStatement.SelectContext(ctx, &result, params)
	if err != nil {
		return nil, err
	}

	return result, nil

}

func GetMultiLoanOfferDump(ctx context.Context, userObj interface{}, sourceEntityIDs []string, filter string, fromDate string, toDate string) ([]MultiOfferRow, error) {

	query := fmt.Sprintf(`SELECT distinct on(blo.user_id, blo.lender_id) blo.user_id,
      coalesce(u.name,u.firm_name) as name,
      coalesce(u.firm_name,'') as firm_name,
      coalesce(u.mobile,'') as mobile,
      coalesce(d.agent_code,'') as agent_code,
      u.unique_id,
      coalesce(la.amount,0) as loan_amount,
      coalesce(la.tenure,0) as tenure,
      coalesce(la.interest,0) as interest,
      coalesce(la.processing_fee,0) as processing_fee,
      coalesce(la.status,-5) as loan_status,
      coalesce(blo.lender_id::::TEXT,'') as lender_id,
      to_char(la.created_at AT TIME ZONE 'utc' AT TIME ZONE 'Asia/Calcutta', 'YYYY-MM-DD HH24::MI::SS') as offer_accepted_at,
      coalesce(blo.processing_fee, 0) as in_principal_pf,
      coalesce(blo.interest, 0) as in_principal_interest,
      coalesce(blo.max_amount, 0) as in_principal_offer_amount,
      coalesce(blo.max_tenure, 0) as in_principal_tenure,
      coalesce(la.loan_type, '') as loantype,
      coalesce(blo.status, -1) as loan_offer_status,
      to_char(blo.updated_at AT TIME ZONE 'utc' AT TIME ZONE 'Asia/Calcutta', 'YYYY-MM-DD HH24::MI::SS') as offer_updated_at,
      to_char(blo.created_at AT TIME ZONE 'utc' AT TIME ZONE 'Asia/Calcutta', 'YYYY-MM-DD HH24::MI::SS') as offer_created_at
    FROM business_loan_offer blo
    LEFT JOIN loan_application la ON la.loan_offer_id = blo.loan_offer_id
    JOIN users u ON u.user_id=blo.user_id
    LEFT JOIN dsa d on d.source_entity_id in (:source_entity_ids) and d.dsa_id=u.dsa_id
    where blo.source_entity_id in (:source_entity_ids)
    AND blo.status not in (:offer_status_expired,:offer_status_inactive)
    AND to_char(blo.%s AT TIME ZONE 'utc' AT TIME ZONE 'Asia/Calcutta', 'YYYY-MM-DD') >= :from_date
    AND to_char(blo.%s AT TIME ZONE 'utc' AT TIME ZONE 'Asia/Calcutta', 'YYYY-MM-DD') <= :to_date
    order by blo.user_id,blo.lender_id, blo.created_at desc`, filter, filter)

	params := map[string]interface{}{
		"source_entity_ids":     sourceEntityIDs,
		"offer_status_expired":  constants.OfferStatusExpired,
		"offer_status_inactive": constants.OfferStatusInactive,
		"from_date":             fromDate,
		"to_date":               toDate,
	}

	var result []MultiOfferRow

	query, args, err := sqlx.Named(query, params)
	if err != nil {
		return nil, err
	}
	query, args, err = sqlx.In(query, args...)
	if err != nil {
		return nil, err
	}

	query = database.Rebind(query)

	err = database.SelectContext(ctx, &result, query, args...)
	if err != nil {
		return nil, err
	}

	return result, nil
}

func GetLenderActivityForSourceEntity(ctx context.Context, columnNameFormatter func(string, string) string, sourceEntityIDs []string, fromDate string, toDate string, filter string) (map[string]map[string]string, error) {

	query := fmt.Sprintf(`select distinct on (user_id, lender_name) blo.user_id as user_id, al.event_type as event_type,
  to_char(al.logged_at AT TIME ZONE 'utc' AT TIME ZONE 'Asia/Calcutta', 'YYYY-MM-DD HH24::MI::SS') as logged_at, blo.source_entity_id as source_entity_id,
  coalesce((case
    when al.event_type=:lender_rejected then al.event_description::::json->>'rejectReason'
    else al.event_description
   end),'') as event_description,
coalesce((case
    when al.event_type=:lender_rejected then al.event_description::::json->>'lender'
    else al.event_type
   end),'') as lender_name
  from business_loan_offer blo
  join activity_log al on (al.user_id=blo.user_id)
  where blo.source_entity_id in (:source_entity_ids)
    AND blo.status not in (:offer_status_expired, :offer_status_inactive)
    AND to_char(blo.%s AT TIME ZONE 'utc' AT TIME ZONE 'Asia/Calcutta', 'YYYY-MM-DD') >= :from_date
    AND to_char(blo.%s AT TIME ZONE 'utc' AT TIME ZONE 'Asia/Calcutta', 'YYYY-MM-DD') <= :to_date
    AND al.event_type in (:lender_rejected,:enach_completed,:bank_connect_completed,:kyc_submitted)
  order by user_id, lender_name, al.logged_at desc
 `, filter, filter)

	params := map[string]interface{}{
		"lender_rejected":        constants.ActivityLenderRejected,
		"enach_completed":        constants.ActivityENachCompleted,
		"bank_connect_completed": constants.ActivityBankConnectCompleted,
		"kyc_submitted":          constants.ActivityKYCSubmitted,
		"source_entity_ids":      sourceEntityIDs,
		"from_date":              fromDate,
		"to_date":                toDate,
		"offer_status_expired":   constants.OfferStatusExpired,
		"offer_status_inactive":  constants.OfferStatusInactive,
	}

	type LenderRejectionRow struct {
		UserID           string `db:"user_id"`
		LenderName       string `db:"lender_name"`
		EventType        string `db:"event_type"`
		LoggedAt         string `db:"logged_at"`
		EventDescription string `db:"event_description"`
		SourceEntityID   string `db:"source_entity_id"`
	}

	var result []LenderRejectionRow

	query, args, err := sqlx.Named(query, params)
	if err != nil {
		return nil, err
	}
	query, args, err = sqlx.In(query, args...)
	if err != nil {
		return nil, err
	}

	query = database.Rebind(query)

	err = database.SelectContext(ctx, &result, query, args...)
	if err != nil {
		return nil, err
	}

	mappedResult := make(map[string]map[string]string)
	for _, row := range result {
		if _, exists := mappedResult[row.UserID]; !exists {
			mappedResult[row.UserID] = make(map[string]string)
		}
		switch row.EventType {
		case constants.ActivityLenderRejected:
			rejectionReason := row.EventDescription
			if row.LenderName == constants.LenderNamesMap[constants.IIFLID] {
				rejectionReason, _ = mask.RejectionReason(rejectionReason, mask.TypeMaskCommsAndPAN, constants.IIFLID, constants.ActivityLoanRejected)
			}
			mappedResult[row.UserID][columnNameFormatter(row.LenderName, "rejection_reason")] = rejectionReason
			mappedResult[row.UserID][columnNameFormatter(row.LenderName, "rejection_date")] = row.LoggedAt
		case constants.ActivityENachCompleted:
			mappedResult[row.UserID]["Enach Completed Timestamp"] = row.LoggedAt
		case constants.ActivityBankConnectCompleted:
			mappedResult[row.UserID]["Banking Submitted Timestamp"] = row.LoggedAt
		case constants.ActivityKYCSubmitted:
			mappedResult[row.UserID]["KYC Completed Timestamp"] = row.LoggedAt
		}

	}

	return mappedResult, nil
}

// GetSalesDispReport returns the data for Sales Disposition Report
func GetSalesDispReport(params map[string]interface{}) ([]SalesDispositionReport, error) {
	var report []SalesDispositionReport
	caseStatuses := params["caseStatuses"].([]string)

	query := `select
					u.user_id,
					u.unique_id,
					coalesce(u.firm_name, '') as firm_name,
					coalesce(u.mobile, '') as mobile,
					coalesce(la.loan_application_no, '') as loan_application_no,
					u.created_at as lead_created_at,
					u.updated_at as lead_updated_at,
					u.source_entity_id,
					sdh.status,
					sdh.call_status,
					sdh.disposition,
					sdh.sub_disposition,
					sdh.disposition_date,
					sdh.follow_up_date,
					sdh.remarks,
					sdh.created_at,
					sdh.created_by
				from sales_disposition_history as sdh
				inner join (select user_id,
									source_entity_id,
									unique_id,
									firm_name,
									mobile,
									created_at,
									updated_at
								from users where created_at>:from and created_at<:to) as u
					on u.user_id=sdh.user_id `

	if len(caseStatuses) > 0 {
		query += `and sdh.status in (:caseStatuses) `
	}

	query += `left outer join loan_application as la
					on sdh.loan_application_id=la.loan_application_id
				where sdh.source_entity_id=:sourceEntityID
				order by sdh.created_at desc`

	query, args, err := sqlx.Named(query, params)
	if err != nil {
		return report, err
	}
	query, args, err = sqlx.In(query, args...)
	if err != nil {
		return report, err
	}
	query = db.GetDB().Rebind(query)

	err = db.GetDB().Select(&report, query, args...)
	return report, err
}

// GetLatestSalesDispReport returns the latest disposition for each user
func GetLatestSalesDispReport(params map[string]interface{}) ([]SalesDispositionReport, error) {
	var report []SalesDispositionReport
	caseStatuses := params["caseStatuses"].([]string)

	query := `select * from
				(select
					u.user_id,
					u.unique_id,
					coalesce(u.firm_name, '') as firm_name,
					coalesce(u.mobile, '') as mobile,
					coalesce(la.loan_application_no, '') as loan_application_no,
					u.created_at as lead_created_at,
					u.updated_at as lead_updated_at,
					u.source_entity_id,
					sdh.status,
					sdh.call_status,
					sdh.disposition,
					sdh.sub_disposition,
					sdh.disposition_date,
					sdh.follow_up_date,
					sdh.remarks,
					sdh.created_at,
					sdh.created_by
				from sales_disposition_history as sdh
				inner join (select user_id,
									source_entity_id,
									unique_id,
									firm_name,
									mobile,
									created_at,
									updated_at
								from users where created_at>:from and created_at<:to) as u
					on u.user_id=sdh.user_id `

	if len(caseStatuses) > 0 {
		query += `and sdh.status in (:caseStatuses) `
	}

	query += `left outer join loan_application as la
					on sdh.loan_application_id=la.loan_application_id
				where sdh.source_entity_id=:sourceEntityID) as filtered
				inner join (select user_id, max(created_at) as created_at from sales_disposition_history group by user_id) as g
				on filtered.user_id=g.user_id and filtered.created_at=g.created_at
				order by filtered.created_at desc`

	query, args, err := sqlx.Named(query, params)
	if err != nil {
		return report, err
	}
	query, args, err = sqlx.In(query, args...)
	if err != nil {
		return report, err
	}
	query = db.GetDB().Rebind(query)

	err = db.GetDB().Select(&report, query, args...)
	return report, err
}

func GetWorkflowTaskReport(params map[string]interface{}) ([]WorkflowTaskRow, error) {
	var report []WorkflowTaskRow
	params["terminalStates"] = constants.TerminalStates
	params["loanApplicationStatus"] = []int{constants.LoanStatusCancelled}

	query := fmt.Sprintf(`select distinct on (wf.resource_id) la.loan_application_id,
          coalesce(la.loan_application_no,'') as loan_application_no,
					coalesce(u.mobile, '') as mobile,
					coalesce(u.email, '') as email,
					u.name as name,
					wf.workflow_name as workflow_name,
					wf.service_request_id as service_request_id,
					wf.status as status,
          to_char(wf.created_at  AT TIME ZONE 'utc' at time zone 'Asia/Calcutta', 'YYYY-MM-DD HH24::MI::SS') as initiated_at,
					logs.action as action,
					coalesce(logs.action_by,'') as action_by,
					logs.priority_group as priority_group,
          to_char(logs.created_at  AT TIME ZONE 'utc' at time zone 'Asia/Calcutta', 'YYYY-MM-DD HH24::MI::SS') as created_at,
					coalesce(logs.action_to,'') as action_to,
					logs.params as params
				from loan_application as la
				inner join users as u on la.user_id=u.user_id
				inner join dashboard_workflow_status_tracker as wf on wf.resource_id=la.loan_application_id
				inner join dashboard_workflow_logs as logs on logs.service_request_id=wf.service_request_id
				where wf.workflow_name in (:workflowType)
        and date(la.%s at time zone 'utc' at time zone 'Asia/Calcutta') >= :from
        and date(la.%s at time zone 'utc' at time zone 'Asia/Calcutta') <= :to
		and la.status not in (:loanApplicationStatus)
  `, params["dateType"], params["dateType"])

	switch params["status"] {
	case "active":
		{
			query += ` and wf.status not in (:terminalStates)`
		}
	case "closed":
		{
			query += ` and wf.status in (:terminalStates)`
		}
	}

	query += ` order by wf.resource_id, logs.created_at desc`

	query, args, err := sqlx.Named(query, params)
	if err != nil {
		return report, err
	}
	query, args, err = sqlx.In(query, args...)
	if err != nil {
		return report, err
	}
	query = db.GetDB().Rebind(query)

	err = db.GetDB().Select(&report, query, args...)
	if err != nil {
		log.Error(err)
		return report, err
	}

	for _, wfType := range params["workflowType"].([]string) {
		switch wfType {
		case constants.WFTypeOfferNegotiationV2:
			fallthrough
		case constants.WFTypeOfferNegotiation:
			{
				err := getOfferNegoWFDetails(report)
				if err != nil {
					log.Error(err)
					return report, err
				}
			}
		case constants.WFTypeKYCV2:
			fallthrough
		case constants.WFTypeKYC:
			{
				err := getKYCWFDetails(report)
				if err != nil {
					log.Error(err)
					return report, err
				}
			}
		case constants.WFTypeBREV2:
			fallthrough
		case constants.WFTypeBRE:
			{
				err := getBREWFDetails(report)
				if err != nil {
					log.Error(err)
					return report, err
				}
			}
		}
	}

	return report, nil
}

func getBREWFDetails(report []WorkflowTaskRow) error {
	rowMap := make(map[string]*WorkflowTaskRow)
	var requestIDs []string
	for i := range report {
		rowMap[report[i].RequestID] = &report[i]
		requestIDs = append(requestIDs, report[i].RequestID)
	}
	requestIDString := "'" + strings.Join(requestIDs, "', '") + "'"

	type breWFStatus struct {
		RequestID string `db:"service_request_id"`
		Params    string `db:"params"`
	}
	var breWFStatusReport []breWFStatus
	query := fmt.Sprintf(`select distinct on (logs.service_request_id) logs.service_request_id, logs.params
				from dashboard_workflow_logs as logs
				inner join unnest(array[%s]) as sr on sr=logs.service_request_id
				order by logs.service_request_id, logs.created_at`, requestIDString)
	err := db.GetDB().Select(&breWFStatusReport, query)
	if err != nil {
		log.Error(err)
		return err
	}

	for _, b := range breWFStatusReport {
		var ws workflow.DashboardWorkflowDBStruct
		err := json.Unmarshal([]byte(b.Params), &ws)
		if err != nil {
			log.Error(err)
			return err
		}
		if v, ok := ws.CustomData["BRE_1_Amber"]; ok {
			rowMap[b.RequestID].BRE1Result = v.(bool)
		}
		if v, ok := ws.CustomData["BRE_2_Amber"]; ok {
			rowMap[b.RequestID].BRE1Result = v.(bool)
		}
	}

	return nil
}

func getOfferNegoWFDetails(report []WorkflowTaskRow) error {
	rowMap := make(map[string]*WorkflowTaskRow, 0)
	var requestIDs []string
	for i := range report {
		var params workflow.AbflOfferNego
		err := json.Unmarshal([]byte(report[i].Params), &params)
		if err != nil {
			log.Error(err)
			return err
		}

		report[i].LoanAmount = params.LoanAmount
		report[i].InterestRate = params.InterestRate
		report[i].Tenure = params.Tenure
		report[i].ProcessingFee = params.ProcessingFee

		if report[i].Action == constants.ABFLWFRequestRaised {
			report[i].RequestedBy = report[i].LastActionBy
			continue
		}
		rowMap[report[i].RequestID] = &report[i]
		requestIDs = append(requestIDs, report[i].RequestID)
	}
	requestIDString := "'" + strings.Join(requestIDs, "', '") + "'"

	if len(requestIDs) == 0 {
		return nil
	}

	type offerNegoStatus struct {
		RequestID string `db:"service_request_id"`
		ActionBy  string `db:"action_by"`
	}
	var offerNegoStatusReport []offerNegoStatus
	query := fmt.Sprintf(`select distinct on (logs.service_request_id) logs.service_request_id, 
					coalesce(logs.action_by, '') as action_by
				from dashboard_workflow_logs as logs
				inner join unnest(array[%s]) as sr on sr=logs.service_request_id
				order by logs.service_request_id, logs.created_at`, requestIDString)
	err := db.GetDB().Select(&offerNegoStatusReport, query)
	if err != nil {
		log.Error(err)
		return err
	}

	for _, o := range offerNegoStatusReport {
		rowMap[o.RequestID].RequestedBy = o.ActionBy
	}

	return nil
}

func getKYCWFDetails(report []WorkflowTaskRow) error {
	rowMap := make(map[string]*WorkflowTaskRow, 0)
	var requestIDs []string
	for i := range report {
		rowMap[report[i].RequestID] = &report[i]
		requestIDs = append(requestIDs, report[i].RequestID)
	}
	requestIDString := "'" + strings.Join(requestIDs, "', '") + "'"

	type vkycStatus struct {
		RequestID string `db:"service_request_id"`
		Action    string `db:"action"`
	}
	var vkycStatusReport []vkycStatus
	query := fmt.Sprintf(`select logs.service_request_id, logs.action
				from dashboard_workflow_logs as logs
				inner join unnest(array[%s]) as sr on sr=logs.service_request_id
				where logs.action='%s'`, requestIDString, constants.ABFLWFVKYCCompleteAction)
	err := db.GetDB().Select(&vkycStatusReport, query)
	if err != nil {
		log.Error(err)
		return err
	}

	for _, vkyc := range vkycStatusReport {
		rowMap[vkyc.RequestID].VKYCUploadStatus = true
	}

	return nil
}
func PFLUTMReport(ctx context.Context, filter string, fromDate string, toDate string, sourceEntityID string) ([]PFLUTMReportRow, error) {
	var result []PFLUTMReportRow

	params := map[string]interface{}{
		"fromdate":         fromDate,
		"todate":           toDate,
		"source_entity_id": sourceEntityID,
		"user_status":      constants.UserStatusArchived,
	}

	query := fmt.Sprintf(`
		select u.user_id as user_id,
      coalesce(u.crm_id,'') as lead_id,
      coalesce(u.unique_id,'') as customer_id,
      coalesce(u.source,'') as source,
      u.status as status,
      coalesce(to_char(u.created_at at time zone 'utc' at time zone 'Asia/Calcutta', 'YYYY-MM-DD HH24::MI::SS'), '') as user_created_at,
      coalesce(la.loan_application_no,'') as loan_application_no,
      coalesce(la.status, -5) as loan_status,
		coalesce(la.kyc_status,-5) as loan_kyc_status,
      coalesce(to_char(la.created_at at time zone 'utc' at time zone 'Asia/Calcutta', 'YYYY-MM-DD HH24::MI::SS'), '') as application_created_at,
	  coalesce(to_char(la.updated_at at time zone 'utc' at time zone 'Asia/Calcutta', 'YYYY-MM-DD HH24::MI::SS'), '') as application_updated_at,
      coalesce(to_char(la.sign_date AT TIME ZONE 'utc' at time zone 'Asia/Calcutta', 'YYYY-MM-DD HH24::MI::SS'), '') as sign_agreement_date,
      coalesce(us.campaign_params::::text,'') as campaign_params
      from
      users u
      left join user_source us on (us.user_id = u.user_id)
      left join loan_application la on (us.loan_application_id=la.loan_application_id)
      where u.source_entity_id=:source_entity_id and
      u.status<>:user_status and
      to_char(u.%s at time zone 'utc' at time zone 'Asia/Calcutta', 'YYYY-MM-DD') >= :fromdate AND
      to_char(u.%s at time zone 'utc' at time zone 'Asia/Calcutta', 'YYYY-MM-DD') <= :todate and
      us.source!='dashboard'
      order by u.user_id, us.created_at DESC 
  `, filter, filter)

	namedStatement, err := database.PrepareNamed(query)
	if err != nil {
		log.Error(err)
		return nil, err
	}
	err = namedStatement.SelectContext(ctx, &result, params)
	if err != nil {
		log.Error(err)
		return nil, err
	}

	disbursedLeads := []string{}
	disbursedLeadsMap := map[string]*PFLUTMReportRow{}
	for index := range result {
		rowPtr := &result[index]
		if rowPtr.LoanStatus == constants.LoanStatusDisbursed || rowPtr.LoanStatus == constants.LoanStatusClosed {
			disbursedLeads = append(disbursedLeads, rowPtr.UserID)
			disbursedLeadsMap[rowPtr.UserID] = rowPtr
		}
	}

	disbursalTimestamps, err := getDisbursalTimestamp(ctx, disbursedLeads)
	if err != nil {
		log.Error(err)
		return nil, err
	}
	for _, r := range disbursalTimestamps {
		disbursedLeadsMap[r.UserID].DisbursedOrActivationDate = r.DisbursalTimestamp
	}

	return result, nil
}

// ABFLCustomDump generates the user dump for abfl and returns the rows for the report
func ABFLCustomDump(ctx context.Context, filter string, fromDate string, toDate string, resourceIDs []string, dashboardType string) ([]ABFLCustomDumpRow, error) {

	params := map[string]interface{}{
		"from_date":              fromDate,
		"to_date":                toDate,
		"resource_ids":           resourceIDs,
		"user_status_archived":   constants.UserStatusArchived,
		"sourceentitytobehidden": constants.TataPLID,
	}

	sourceEntityJoin := ""
	if dashboardType == exportlogs.DashboardTypePlatform {
		sourceEntityJoin = "se.source_entity_id in (:resource_ids)"
	} else if dashboardType == exportlogs.DashboardTypeLender {
		sourceEntityJoin = "se.organization_id in (:resource_ids)"
	}

	result := []ABFLCustomDumpRow{}

	query := fmt.Sprintf(`
  select distinct on (u.user_id)
    u.user_id as user_id,
    coalesce(to_char(u.created_at AT TIME ZONE 'utc' at time zone 'Asia/Calcutta', 'YYYY-MM-DD HH24::MI::SS'),'') as user_created_at,
    coalesce(to_char(la.created_at AT TIME ZONE 'utc' at time zone 'Asia/Calcutta', 'YYYY-MM-DD HH24::MI::SS'),'') as application_created_at,
    coalesce(to_char(la.updated_at AT TIME ZONE 'utc' at time zone 'Asia/Calcutta', 'YYYY-MM-DD HH24::MI::SS'),'') as application_updated_at,
    coalesce(u.unique_id,'') as customer_id,
    coalesce(la.loan_application_no,'') as loan_application_no,
    coalesce(u.firm_name,'') as firm_name,
    coalesce(u.name,'') as name,
    coalesce(u.pan,'') as pan,
    coalesce(u.mobile,'') as mobile,
    coalesce(u.email,'') as email,
    coalesce(u.source,'') as source,
    coalesce(ubg.gstin,'') as gstin,
    coalesce(ubu.uan,'') as udyam,
    coalesce(bs.cibil_score::::text,'') as cibil_score,
    coalesce(der.output_variables::::json->>'bureau_grid','') as thick_thin_category,
    case
      when lv.dynamic_variables->>'hunterMatchCount'='1' then 'YES'
      else 'NO'
    end as hunter_status,
    coalesce(u.status,-5) as user_status,
    coalesce(la.status,-5) as loan_status,
    coalesce(la.kyc_status,0) as loan_kyc_status,
    (u.dynamic_user_info::::json->>'loanAmount')::::float as requested_amount,
    la.amount as loan_amount,
    la.processing_fee as processing_fee,
    la.interest as rate_of_interest,
    la.tenure as tenure,
    la.gst as gst,
    la.advance_emi as advance_emi,
    jsonb_build_object(
      'line1', u.dynamic_user_info::::json->>'permanentAddressLine1',
      'city' , u.dynamic_user_info::::json->>'permanentAddressCity',
      'state', u.dynamic_user_info::::json->>'permanentAddressState',
      'pincode', u.dynamic_user_info::::json->>'permanentAddressPincode') as current_address,
    coalesce(uld.permanent_address,'')  as permanent_address,
	  coalesce(d.agent_code, '') as agent_code,
    coalesce(d.dsa_name,'') as agent_name,
    coalesce(se.source_entity_id::::text,'') as source_entity_id,
    coalesce(la.lender_id::::text,'') as lender_id,
    coalesce(u.dynamic_user_info::::json->>'rmID','') as rm_id_journey,
	coalesce(la.metadata->'softApproved'->>'status', 'false') as soft_approved_status
   from users u
   left join multi_user_loan_relations mslr on mslr.user_id=u.user_id     
   left join source_entity se on (se.source_entity_id=u.source_entity_id)
   left join loan_application la on (la.user_id=u.user_id)
   left join user_business_gst ubg on (ubg.user_id=u.user_id)
   left join user_business_uan ubu on (ubu.user_id=u.user_id)
   left join bureau_score bs on (bs.user_id=u.user_id)
   left join decision_engine_response der on (der.user_id=u.user_id and der.rule_type='workflow'
     and lower(der.rule_version) like '%%bureau_based_eligibility%%')
   left join lender_variables lv on (lv.user_id=u.user_id)
   left join user_loan_details uld on (uld.user_id=u.user_id)
   left join dsa d on (d.dsa_id=u.dsa_id)
    where %s
     and u.status<>:user_status_archived
	 and u.source_entity_id <> :sourceentitytobehidden
     and date(u.%s at time zone 'utc' at time zone 'Asia/Calcutta') >= :from_date
     and date(u.%s at time zone 'utc' at time zone 'Asia/Calcutta') <= :to_date 
     and mslr.user_id is null  
    order by u.user_id, der.created_at desc;
    `, sourceEntityJoin, filter, filter)

	query, args, err := sqlx.Named(query, params)
	if err != nil {
		return nil, err
	}
	query, args, err = sqlx.In(query, args...)
	if err != nil {
		return nil, err
	}
	query = database.Rebind(query)
	err = database.SelectContext(ctx, &result, query, args...)
	if err != nil {
		return nil, err
	}

	disbursedLeadsMap := make(map[string]*ABFLCustomDumpRow)
	disbursedLeadIds := []string{}

	rejectedLeadsMap := make(map[string]*ABFLCustomDumpRow)
	rejectedLeadIds := []string{}

	rowsMap := make(map[string]*ABFLCustomDumpRow)

	for index := range result {
		row := &result[index]
		rowsMap[row.UserID] = row

		currentAddress, currAddressStr, err := DecodeAddress(row.CurrentAddress)
		if err == nil {
			row.CurrentAddress = currAddressStr
			row.CityCurrentAddress = currentAddress.City
			row.StateCurrentAddress = currentAddress.State
			row.PincodeCurrentAddress = currentAddress.Pincode
		}

		permanentAddress, permanentAddressStr, err := DecodeAddress(row.PermanentAddress)
		if err == nil {
			row.PermanentAddress = permanentAddressStr
			row.CityPermanentAddress = permanentAddress.City
			row.StatePermanentAddress = permanentAddress.State
			row.PincodePermanentAddress = permanentAddress.Pincode
		}

		if row.LoanStatus == constants.LoanStatusDisbursed || row.LoanStatus == constants.LoanStatusClosed {
			premium := 0.0
			if row.EmiProtectPremium.Valid {
				premium = row.EmiProtectPremium.Float64
			} else if row.PaHospitalisationPremium.Valid {
				premium = row.PaHospitalisationPremium.Float64
			}
			t := calc.CalculateDisbursalAmountBySourceEntity(row.LoanAmount.Float64, row.ProcessingFee.Float64,
				row.GST.Float64, row.AdvanceEMI.Float64, premium, commonutils.OtherCharges(row.LenderID, row.UserID), "", row.SourceEntityID, row.UserID)
			if row.LoanAmount.Valid {
				row.NetDisbursalAmount = sql.NullFloat64{Float64: t, Valid: t > 0}
			}
			disbursedLeadIds = append(disbursedLeadIds, row.UserID)
			disbursedLeadsMap[row.UserID] = row
		} else if row.UserStatus == constants.UserStatusDisqualified || row.LoanStatus == constants.LoanStatusLoanRejected {
			rejectedLeadIds = append(rejectedLeadIds, row.UserID)
			rejectedLeadsMap[row.UserID] = row
		}
	}
	disbursedTimestamps, err := getDisbursalTimestamp(ctx, disbursedLeadIds)
	if err != nil {
		log.Errorln("There was an error in getting disbursal timestamp: ", err)
	}

	for _, disbursalTimestamp := range disbursedTimestamps {
		if _, ok := disbursedLeadsMap[disbursalTimestamp.UserID]; ok {
			disbursedLeadsMap[disbursalTimestamp.UserID].DisbursedOrActivationDate = disbursalTimestamp.DisbursalTimestamp
		}
	}

	rejectionReasons, err := getRejectionReasonForUsers(ctx, rejectedLeadIds, []string{})
	if err != nil {
		log.Errorln("There was an error in getting rejection reason: ", err)
	}

	for _, row := range rejectionReasons {
		if _, ok := rejectedLeadsMap[row.UserID]; ok {
			rejectedLeadsMap[row.UserID].RejectionReason = row.RejectionReason
			rejectedLeadsMap[row.UserID].RejectionReasonCategory = journey.GetRejectionReasonCategory(row.RejectionReason)
		}
	}

	err = getAdditionalUserInfo(ctx, fromDate, toDate, filter, rowsMap, resourceIDs, dashboardType)
	if err != nil {
		log.Errorln("There was an error in getting workflow information: ", err)
	}

	return result, nil
}

func ABFLCustomDumpWithLimitAndOffset(ctx context.Context, filter string, fromDate string, toDate string, resourceIDs []string, dashboardType string, limit, offset int) ([]ABFLCustomDumpRow, error) {

	params := map[string]interface{}{
		"from_date":            fromDate,
		"to_date":              toDate,
		"resource_ids":         resourceIDs,
		"user_status_archived": constants.UserStatusArchived,
	}
	sourceEntityJoin := ""
	if dashboardType == exportlogs.DashboardTypePlatform {
		sourceEntityJoin = "se.source_entity_id in (:resource_ids)"
	} else if dashboardType == exportlogs.DashboardTypeLender {
		sourceEntityJoin = "se.organization_id in (:resource_ids)"
	}

	result := []ABFLCustomDumpRow{}

	query := fmt.Sprintf(`
  select distinct on (u.user_id)
    u.user_id as user_id,
    coalesce(to_char(u.created_at AT TIME ZONE 'utc' at time zone 'Asia/Calcutta', 'YYYY-MM-DD HH24::MI::SS'),'') as user_created_at,
    coalesce(to_char(la.created_at AT TIME ZONE 'utc' at time zone 'Asia/Calcutta', 'YYYY-MM-DD HH24::MI::SS'),'') as application_created_at,
    coalesce(to_char(la.updated_at AT TIME ZONE 'utc' at time zone 'Asia/Calcutta', 'YYYY-MM-DD HH24::MI::SS'),'') as application_updated_at,
    coalesce(u.unique_id,'') as customer_id,
    coalesce(la.loan_application_no,'') as loan_application_no,
    coalesce(u.firm_name,'') as firm_name,
    coalesce(u.name,'') as name,
    coalesce(u.pan,'') as pan,
    coalesce(u.mobile,'') as mobile,
    coalesce(u.email,'') as email,
    coalesce(u.source,'') as source,
    coalesce(ubg.gstin,'') as gstin,
    coalesce(ubu.uan,'') as udyam,
    coalesce(bs.cibil_score::::text,'') as cibil_score,
    coalesce(der.output_variables::::json->>'bureau_grid','') as thick_thin_category,
    case
      when lv.dynamic_variables->>'hunterMatchCount'='1' then 'YES'
      else 'NO'
    end as hunter_status,
    coalesce(u.status,-5) as user_status,
    coalesce(la.status,-5) as loan_status,
    coalesce(la.kyc_status,0) as loan_kyc_status,
    (u.dynamic_user_info::::json->>'loanAmount')::::float as requested_amount,
    la.amount as loan_amount,
    la.processing_fee as processing_fee,
    la.interest as rate_of_interest,
    la.tenure as tenure,
    la.gst as gst,
    la.advance_emi as advance_emi,
    jsonb_build_object(
      'line1', u.dynamic_user_info::::json->>'permanentAddressLine1',
      'city' , u.dynamic_user_info::::json->>'permanentAddressCity',
      'state', u.dynamic_user_info::::json->>'permanentAddressState',
      'pincode', u.dynamic_user_info::::json->>'permanentAddressPincode') as current_address,
    coalesce(uld.permanent_address,'')  as permanent_address,
	  coalesce(d.agent_code, '') as agent_code,
    coalesce(d.dsa_name,'') as agent_name,
    coalesce(se.source_entity_id::::text,'') as source_entity_id,
    coalesce(la.lender_id::::text,'') as lender_id,
    coalesce(u.dynamic_user_info::::json->>'rmID','') as rm_id_journey,
    coalesce(la.metadata->'softApproved'->>'status', 'false') as soft_approved_status
   from users u
   left join multi_user_loan_relations mslr on mslr.user_id=u.user_id
   left join source_entity se on (se.source_entity_id=u.source_entity_id)
   left join loan_application la on (la.user_id=u.user_id)
   left join user_business_gst ubg on (ubg.user_id=u.user_id)
   left join user_business_uan ubu on (ubu.user_id=u.user_id)
   left join bureau_score bs on (bs.user_id=u.user_id)
   left join decision_engine_response der on (der.user_id=u.user_id and der.rule_type='workflow'
     and lower(der.rule_version) like '%%bureau_based_eligibility%%')
   left join lender_variables lv on (lv.user_id=u.user_id)
   left join user_loan_details uld on (uld.user_id=u.user_id)
   left join dsa d on (d.dsa_id=u.dsa_id)
    where %s
     and u.status<>:user_status_archived
     and date(u.%s at time zone 'utc' at time zone 'Asia/Calcutta') >= :from_date
     and date(u.%s at time zone 'utc' at time zone 'Asia/Calcutta') <= :to_date
    and mslr.user_id is null
    order by u.user_id, der.created_at desc limit %d offset %d;
    `, sourceEntityJoin, filter, filter, limit, offset)

	query, args, err := sqlx.Named(query, params)
	if err != nil {
		return nil, err
	}
	query, args, err = sqlx.In(query, args...)
	if err != nil {
		return nil, err
	}
	query = database.Rebind(query)
	err = database.SelectContext(ctx, &result, query, args...)
	if err != nil {
		return nil, err
	}

	disbursedLeadsMap := make(map[string]*ABFLCustomDumpRow)
	disbursedLeadIds := []string{}

	rejectedLeadsMap := make(map[string]*ABFLCustomDumpRow)
	rejectedLeadIds := []string{}

	rowsMap := make(map[string]*ABFLCustomDumpRow)

	for index := range result {
		row := &result[index]
		rowsMap[row.UserID] = row

		currentAddress, currAddressStr, err := DecodeAddress(row.CurrentAddress)
		if err == nil {
			row.CurrentAddress = currAddressStr
			row.CityCurrentAddress = currentAddress.City
			row.StateCurrentAddress = currentAddress.State
			row.PincodeCurrentAddress = currentAddress.Pincode
		}

		permanentAddress, permanentAddressStr, err := DecodeAddress(row.PermanentAddress)
		if err == nil {
			row.PermanentAddress = permanentAddressStr
			row.CityPermanentAddress = permanentAddress.City
			row.StatePermanentAddress = permanentAddress.State
			row.PincodePermanentAddress = permanentAddress.Pincode
		}

		if row.LoanStatus == constants.LoanStatusDisbursed || row.LoanStatus == constants.LoanStatusClosed {
			premium := 0.0
			if row.EmiProtectPremium.Valid {
				premium = row.EmiProtectPremium.Float64
			} else if row.PaHospitalisationPremium.Valid {
				premium = row.PaHospitalisationPremium.Float64
			}
			t := calc.CalculateDisbursalAmountBySourceEntity(row.LoanAmount.Float64, row.ProcessingFee.Float64,
				row.GST.Float64, row.AdvanceEMI.Float64, premium, commonutils.OtherCharges(row.LenderID, row.UserID), "", row.SourceEntityID, row.UserID)
			if row.LoanAmount.Valid {
				row.NetDisbursalAmount = sql.NullFloat64{Float64: t, Valid: t > 0}
			}
			disbursedLeadIds = append(disbursedLeadIds, row.UserID)
			disbursedLeadsMap[row.UserID] = row
		} else if row.UserStatus == constants.UserStatusDisqualified || row.LoanStatus == constants.LoanStatusLoanRejected {
			rejectedLeadIds = append(rejectedLeadIds, row.UserID)
			rejectedLeadsMap[row.UserID] = row
		}
	}
	disbursedTimestamps, err := getDisbursalTimestamp(ctx, disbursedLeadIds)
	if err != nil {
		log.Errorln("There was an error in getting disbursal timestamp: ", err)
	}

	for _, disbursalTimestamp := range disbursedTimestamps {
		if _, ok := disbursedLeadsMap[disbursalTimestamp.UserID]; ok {
			disbursedLeadsMap[disbursalTimestamp.UserID].DisbursedOrActivationDate = disbursalTimestamp.DisbursalTimestamp
		}
	}

	rejectionReasons, err := getRejectionReasonForUsers(ctx, rejectedLeadIds, []string{})
	if err != nil {
		log.Errorln("There was an error in getting rejection reason: ", err)
	}

	for _, row := range rejectionReasons {
		if _, ok := rejectedLeadsMap[row.UserID]; ok {
			rejectedLeadsMap[row.UserID].RejectionReason = row.RejectionReason
			rejectedLeadsMap[row.UserID].RejectionReasonCategory = journey.GetRejectionReasonCategory(row.RejectionReason)
		}
	}

	err = getAdditionalUserInfo(ctx, fromDate, toDate, filter, rowsMap, resourceIDs, dashboardType)
	if err != nil {
		log.Errorln("There was an error in getting workflow information: ", err)
	}

	return result, nil
}

// ExportRecoveryHandler Sets status properly for async exports
func ExportRecoveryHandler(exportID string) {
	if r := recover(); r != nil {
		log.Errorln("Error: ", r)
		errorHandler.ReportToSentryWithoutRequest(r.(error))
		err := exportlogs.Update(exportlogs.StatusFailed, "", "", exportID)
		if err != nil {
			log.Errorln(err)
			return
		}
	}
}

// Decode Address
func DecodeAddress(address string) (AddrStruct, string, error) {
	var addressStruct AddrStruct
	if address == "" {
		return addressStruct, "", errors.New("Address Empty")
	}

	if err := json.Unmarshal([]byte(address), &addressStruct); err != nil {
		return addressStruct, "", errors.New("There was an error decoding struct")
	}
	address = addressStruct.Line1 + " \n " +
		addressStruct.Line2 + " \n " +
		addressStruct.City + " \n " +
		addressStruct.State + " \n " +
		addressStruct.Pincode + " \n "
	return addressStruct, address, nil

}

func getAdditionalUserInfo(ctx context.Context, from, to, filter string, rowsMap map[string]*ABFLCustomDumpRow, resourceIDs []string, dashboardType string) error {
	if len(rowsMap) == 0 {
		return nil
	}

	sourceEntityJoin := ""
	if dashboardType == exportlogs.DashboardTypePlatform {
		sourceEntityJoin = "se.source_entity_id in (:resource_ids)"
	} else if dashboardType == exportlogs.DashboardTypeLender {
		sourceEntityJoin = "se.organization_id in (:resource_ids)"
	}

	params := map[string]interface{}{
		"from_date":                from,
		"to_date":                  to,
		"resource_ids":             resourceIDs,
		"user_status_archived":     constants.UserStatusArchived,
		"workflow_terminal_states": constants.TerminalStates,
		"abfl_kyc_wf":              constants.WFTypeKYC,
		"abfl_bre_wf":              constants.WFTypeBRE,
		"abfl_kyc_wf_v2":           constants.WFTypeKYCV2,
		"abfl_bre_wf_v2":           constants.WFTypeBREV2,
		"owner_type_lender":        constants.OwnerTypeLender,
	}

	type WorkflowDetails []struct {
		WorkflowName    string      `json:"workflow_name"`
		WorkflowStatus  string      `json:"workflow_status"`
		CreatedAt       string      `json:"started_at"`
		EndedAt         string      `json:"ended_at"`
		RiskRating      string      `json:"risk_rating"`
		Comments        string      `json:"comments"`
		RejectionReason interface{} `json:"rejection_reason"`
	}
	type BankDetails struct {
		Status        int    `json:"bank_status"`
		FailureReason string `json:"failure_reason"`
	}
	type LoanOfferDetails []struct {
		OfferType string  `json:"offer_type"`
		MaxAmount float64 `json:"max_amount"`
	}

	type InsuranceDetails []struct {
		InsuranceType string  `json:"insurance_type"`
		Premium       float64 `json:"premium"`
	}

	type UMMDetails []struct {
		ModuleName   string `json:"module_name"`
		ModuleStatus int64  `json:"module_status"`
	}

	type MDSADetails struct {
		MDSAName string `json:"mdsa_name"`
		MDSACode string `json:"mdsa_code"`
	}

	var result []struct {
		UserID           string `db:"user_id"`
		WorkflowDetails  string `db:"workflow_details"`
		BankDetails      string `db:"bank_details"`
		LoanOfferDetails string `db:"loan_offer_details"`
		InsuranceDetails string `db:"insurance_details"`
		UMMDetails       string `db:"umm_details"`
		MDSADetails      string `db:"mdsa_details"`
	}
	query := fmt.Sprintf(`
      select
		u.user_id as user_id,
    	(jsonb_agg(jsonb_build_object(
			'workflow_name', dwst.workflow_name, 
			'workflow_status', dwst.status,
			'started_at', to_char(dwst.created_at AT TIME ZONE 'utc' at time zone 'Asia/Calcutta', 'YYYY-MM-DD HH24::MI::SS'), 
			'ended_at', to_char(dwl.created_at AT TIME ZONE 'utc' at time zone 'Asia/Calcutta', 'YYYY-MM-DD HH24::MI::SS'), 
			'comments', coalesce(dwl.params->'comments', dwl.params->'customData'->'comments'),
			'risk_rating', coalesce(dwl.params->'riskRating', dwl.params->'customData'->'riskRating'),
			'rejection_reason', coalesce(dwl.params->'rejectionReason', dwl.params->'customData'->'rejectionReason')
			) order by dwl.created_at desc)) as workflow_details,
	 	(jsonb_agg(jsonb_build_object('bank_status', coalesce(bcd.status,-1), 'failure_reason', bcd.failure_reason) order by bcd.created_at desc)->0) as bank_details,
		(jsonb_agg(jsonb_build_object('offer_type', blo.offer_type, 'max_amount', blo.max_amount) order by blo.created_at desc)) as loan_offer_details,
		(jsonb_agg(jsonb_build_object('insurance_type', i.insurance_type, 'premium', i.premium) order by i.created_at desc)) as insurance_details,
		(jsonb_agg(jsonb_build_object('module_name', umm.module_name, 'module_status', umm.status) order by umm.created_at desc)) as umm_details,
    (jsonb_agg(jsonb_build_object('mdsa_name', d.dsa_name, 'mdsa_code', d.agent_code))->0) as mdsa_details
      from users u
      join source_entity se on (se.source_entity_id=u.source_entity_id)
      left join dsa d on (d.source_entity_id=u.source_entity_id and d.owner_type=:owner_type_lender)
      left join loan_application la on (u.user_id=la.user_id)
      left join dashboard_workflow_status_tracker dwst on (dwst.resource_id=la.loan_application_id and dwst.workflow_name in (:abfl_kyc_wf, :abfl_bre_wf, :abfl_kyc_wf_v2, :abfl_bre_wf_v2))
      left join dashboard_workflow_logs dwl on (dwst.service_request_id=dwl.service_request_id and dwst.status in (:workflow_terminal_states))
      left join bank_connect_details bcd on (bcd.user_id=u.user_id)
      left join business_loan_offer blo on (blo.user_id=u.user_id)
      left join insurance i on (i.loan_application_id=la.loan_application_id)
	  left join user_module_mapping umm on (umm.user_id=u.user_id)
    where %s
    and u.status<>:user_status_archived
    and date(u.%s at time zone 'utc' at time zone 'Asia/Calcutta') >= :from_date
    and date(u.%s at time zone 'utc' at time zone 'Asia/Calcutta') <= :to_date
	  group by u.user_id  `, sourceEntityJoin, filter, filter)

	query, args, err := sqlx.Named(query, params)
	if err != nil {
		return err
	}
	query, args, err = sqlx.In(query, args...)
	if err != nil {
		return err
	}
	query = database.Rebind(query)
	err = database.SelectContext(ctx, &result, query, args...)
	if err != nil {
		return err
	}

	for _, row := range result {
		if _, ok := rowsMap[row.UserID]; ok {
			rowsMap[row.UserID].ABFLKYCWF = "No"
			rowsMap[row.UserID].ABFLCreditWF = "No"
			visitedStates := map[string]bool{}

			workflowDetails := WorkflowDetails{}
			loanOfferDetails := LoanOfferDetails{}
			insuranceDetails := InsuranceDetails{}
			ummDetails := UMMDetails{}
			bankDetail := BankDetails{}
			mdsaDetail := MDSADetails{}

			if err := json.Unmarshal([]byte(row.WorkflowDetails), &workflowDetails); err == nil {

				for _, workflowDetail := range workflowDetails {
					if visitedStates[constants.WFTypeKYC] && visitedStates[constants.WFTypeBRE] {
						break
					}
					if !visitedStates[constants.WFTypeKYC] {
						if general.InArr(workflowDetail.WorkflowName, []string{constants.WFTypeKYC, constants.WFTypeKYCV2}) {
							visitedStates[constants.WFTypeKYC] = true
							rowsMap[row.UserID].ABFLKYCWF = "Yes"
							if general.InArr(workflowDetail.WorkflowStatus, constants.TerminalStates) {
								rowsMap[row.UserID].ABFLKYCWF = "Closed"
							}

							if len(workflowDetail.CreatedAt) != 0 {
								rowsMap[row.UserID].ABFLKYCWFStartTime = workflowDetail.CreatedAt
							}
							if len(workflowDetail.EndedAt) != 0 {
								rowsMap[row.UserID].ABFLKYCWFEndTime = workflowDetail.EndedAt
							}
						}
					}
					if !visitedStates[constants.WFTypeBRE] {
						if general.InArr(workflowDetail.WorkflowName, []string{constants.WFTypeBRE, constants.WFTypeBREV2}) {
							visitedStates[constants.WFTypeBRE] = true
							rowsMap[row.UserID].ABFLCreditWF = "Yes"
							if general.InArr(workflowDetail.WorkflowStatus, constants.TerminalStates) {
								rowsMap[row.UserID].ABFLCreditWF = "Closed"
								rowsMap[row.UserID].CreditWorkflowRiskRating = workflowDetail.RiskRating
								rowsMap[row.UserID].CreditWorkflowRemarks = workflowDetail.Comments
								val := workflowDetail.RejectionReason
								if rejectionArr, ok := val.([]interface{}); ok {
									rejectionReasons := []string{}
									for _, rejectionReason := range rejectionArr {
										rejectionReasons = append(rejectionReasons, fmt.Sprint(rejectionReason))
									}
									rowsMap[row.UserID].CreditWorkflowRejectionReason = strings.Join(rejectionReasons, "\n")
								} else if rejectionReason, ok := val.(string); ok {
									rowsMap[row.UserID].CreditWorkflowRejectionReason = rejectionReason
								}
							}
							if len(workflowDetail.CreatedAt) != 0 {
								rowsMap[row.UserID].ABFLCreditWFStartTime = workflowDetail.CreatedAt
							}
							if len(workflowDetail.EndedAt) != 0 {
								rowsMap[row.UserID].ABFLCreditWFEndTime = workflowDetail.EndedAt
							}

						}
					}

				}
			}

			if err := json.Unmarshal([]byte(row.LoanOfferDetails), &loanOfferDetails); err == nil {
				for _, offerDetail := range loanOfferDetails {
					if !visitedStates[offerDetail.OfferType] {
						visitedStates[offerDetail.OfferType] = true
						if offerDetail.OfferType == "tentative" {
							rowsMap[row.UserID].Bre1EligibleAmount = sql.NullFloat64{Float64: offerDetail.MaxAmount, Valid: offerDetail.MaxAmount >= 0}
						} else if offerDetail.OfferType == "booster" {
							rowsMap[row.UserID].Bre2EligibleAmount = sql.NullFloat64{Float64: offerDetail.MaxAmount, Valid: offerDetail.MaxAmount >= 0}
						}
					}
				}
			}

			if err := json.Unmarshal([]byte(row.InsuranceDetails), &insuranceDetails); err == nil {
				for _, insuranceDetail := range insuranceDetails {
					if !visitedStates[insuranceDetail.InsuranceType] {
						visitedStates[insuranceDetail.InsuranceType] = true
						if insuranceDetail.InsuranceType == "PA_HOSPITALIZATION" {
							rowsMap[row.UserID].PaHospitalisationPremium = sql.NullFloat64{Float64: insuranceDetail.Premium, Valid: insuranceDetail.Premium >= 0}
						} else if insuranceDetail.InsuranceType == "EMI_PROTECT" {
							rowsMap[row.UserID].EmiProtectPremium = sql.NullFloat64{Float64: insuranceDetail.Premium, Valid: insuranceDetail.Premium >= 0}
						}
					}
				}
			}

			rowsMap[row.UserID].BizDocUpload = "No"
			rowsMap[row.UserID].OhpDocUpload = "No"
			if err := json.Unmarshal([]byte(row.UMMDetails), &ummDetails); err == nil {
				for _, ummDetail := range ummDetails {
					if !visitedStates[ummDetail.ModuleName] {
						visitedStates[ummDetail.ModuleName] = true
						if ummDetail.ModuleName == "BUSINESS_KYC" && ummDetail.ModuleStatus == 1 {
							rowsMap[row.UserID].BizDocUpload = "Yes"
						}
						if ummDetail.ModuleName == "ADDITIONAL_DOCUMENTS" && ummDetail.ModuleStatus == 1 {
							rowsMap[row.UserID].OhpDocUpload = "Yes"
						}
					}
				}
			}

			if err := json.Unmarshal([]byte(row.BankDetails), &bankDetail); err == nil {
				if bankDetail.Status != -1 {
					rowsMap[row.UserID].BankStatementStatus = constants.BankConnectStatusMapNumtoStr[bankDetail.Status]
				}
				if len(bankDetail.FailureReason) != 0 {
					rowsMap[row.UserID].BankFailureReason = bankDetail.FailureReason
				}

			}

			if err := json.Unmarshal([]byte(row.MDSADetails), &mdsaDetail); err == nil {
				if mdsaDetail.MDSAName != "" {
					rowsMap[row.UserID].SourcingPlatform = mdsaDetail.MDSAName
				}
				if mdsaDetail.MDSACode != "" {
					rowsMap[row.UserID].PartnerCode = mdsaDetail.MDSACode
				}

			}
		}
	}

	return nil
}

// ABFLUTMReport generates the utm report for abfl and returns the rows for the report
func ABFLUTMReport(ctx context.Context, filter string, fromDate string, toDate string, resourceIDs []string, dashboardType string) ([]ABFLUTMReportRow, error) {
	var result []ABFLUTMReportRow

	sourceEntityJoin := ""
	if dashboardType == exportlogs.DashboardTypePlatform {
		sourceEntityJoin = "se.source_entity_id in (:resource_ids)"
	} else if dashboardType == exportlogs.DashboardTypeLender {
		sourceEntityJoin = "se.organization_id in (:resource_ids)"
	}

	params := map[string]interface{}{
		"fromdate":               fromDate,
		"todate":                 toDate,
		"resource_ids":           resourceIDs,
		"user_status":            constants.UserStatusArchived,
		"sourceentitytobehidden": constants.TataPLID,
	}

	query := fmt.Sprintf(`
		select coalesce(u.unique_id,'') as customer_id,
      coalesce(u.source_entity_id::::text,'') as source_entity_id,
      coalesce(u.mobile,'') as mobile,
      coalesce(u.pan, '') as pan,
      coalesce(u.email, '') as email,
      coalesce(to_char(u.dob at time zone 'utc' at time zone 'Asia/Calcutta', 'YYYY-MM-DD HH24::MI::SS'), '') as dob,
      coalesce(u.partner_data->>'campaign_type','') as campaign_type,
      coalesce(us.campaign_params->>'utm-source','') as utm_source,
      coalesce(us.campaign_params->>'utm-content','') as utm_content,
      coalesce(us.campaign_params->>'utm-medium','') as utm_medium,
      coalesce(us.campaign_params->>'utm-campaign','') as utm_campaign,
	  coalesce(us.campaign_params->>'utm-term','') as utm_term,
	  coalesce(to_char(us.created_at at time zone 'utc' at time zone 'Asia/Calcutta', 'YYYY-MM-DD HH24::MI::SS'), '') as created_at
      from
      users u
      join source_entity se on (se.source_entity_id=u.source_entity_id)
      left join user_source us on (us.user_id = u.user_id)
    where %s and
      u.status<>:user_status and
	  u.source_entity_id <> :sourceentitytobehidden and
      date(u.%s at time zone 'utc' at time zone 'Asia/Calcutta') >= :fromdate AND
      date(u.%s at time zone 'utc' at time zone 'Asia/Calcutta') <= :todate and
      us.source!='dashboard'
      order by u.user_id, us.created_at DESC
  `, sourceEntityJoin, filter, filter)

	query, args, err := sqlx.Named(query, params)
	if err != nil {
		log.Error(err)
		return nil, err
	}
	query, args, err = sqlx.In(query, args...)
	if err != nil {
		log.Error(err)
		return nil, err
	}
	query = database.Rebind(query)
	err = database.SelectContext(ctx, &result, query, args...)
	if err != nil {
		log.Error(err)
		return nil, err
	}

	return result, nil
}
