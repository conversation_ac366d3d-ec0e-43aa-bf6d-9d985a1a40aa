package export

import (
	"database/sql"
	"finbox/go-api/models/salesdispositionhistory"
	"finbox/go-api/models/workflow"
	"time"
)

type UserProfileStruct struct {
	UserID                      string `json:"userID"`
	CustomerID                  string `json:"customerID"`
	Mobile                      string `json:"mobile" mask_all:"mobile" mask_except_comms:"allow" mask_comms_and_pan:"mobile"`
	CreatedAt                   string `json:"createdAt"`
	UpdatedAt                   string `json:"updatedAt"`
	Status                      int    `json:"-"`
	StatusText                  string `json:"status"`
	Name                        string `json:"name"`
	Email                       string `json:"email" mask_all:"email" mask_except_comms:"allow" mask_comms_and_pan:"email"`
	DOB                         string `json:"dob" mask_all:"dob" mask_except_comms:"dob" mask_comms_and_pan:"allow"`
	MonthlyIncome               string
	OccupationType              string
	Gender                      int    `json:"gender"`
	Pincode                     string `json:"pincode"`
	City                        string `json:"city"`
	State                       string `json:"state"`
	PAN                         string `json:"pan" mask_all:"pan" mask_except_comms:"pan" mask_comms_and_pan:"pan"`
	PartnerCode                 string `json:"partnerCode"`
	Source                      string `json:"source"`
	GSTIN                       string `json:"gstin" mask_all:"gstin" mask_except_comms:"gstin" mask_comms_and_pan:"allow"`
	FirmName                    string
	ExperianScore               int
	ExperianError               string
	ExperianStatus              string
	CibilScore                  int
	CibilStatus                 string
	ApplicationCreatedAt        string
	LoanApplicationNo           string
	LoanStatus                  int
	KYCStatus                   int
	BankStatementStatus         int
	FailureReason               string
	UserEligibilityCalculatedAt string
	EligibleAmount              float64
	SourceEntityID              string
	Platform                    string `json:"platform"`
	DSAID                       string
	BankAccountHolderName       string
	BankName                    string
	BankAccountType             string
	BureauLastUpdatedAt         string
	BusinessPAN                 string `mask_all:"pan" mask_except_comms:"pan" mask_comms_and_pan:"pan"`
	Constitution                string
	ProgramName                 string
	ReferenceID                 string
	IsEligibleForMultibanking   string `json:"isEligibleForMultibanking"`
	CibilHardScore              int
	CompanyName                 string
	EducationalQualification    string
	EmploymentType              string
	EmploymentWaiverFlag        string
	RequestedLoanAmount         string
	OfficeEmail                 string
	OfficePincode               string
	ResidenceType               string
	CRMID                       string
	AppflyerID                  string
	Base                        string `json:"base"`
	Scheme                      string `json:"scheme"`
}

type LoanDataStruct struct {
	LoanApplicationNo       string
	LoanApplicationID       string
	SourceEntityID          string
	Status                  int
	KYCStatus               int
	Mobile                  string `mask_all:"mobile" mask_except_comms:"allow" mask_comms_and_pan:"mobile"`
	UniqueID                string
	UserID                  string
	Name                    string
	UserCreationDate        string
	ApplicationCreationDate string
	LoanType                string
	Source                  string
	PAN                     string `mask_all:"pan" mask_except_comms:"pan" mask_comms_and_pan:"pan"`
	Gender                  int
	DOB                     string `mask_all:"dob" mask_except_comms:"dob" mask_comms_and_pan:"allow"`
	Email                   string `mask_all:"email" mask_except_comms:"allow" mask_comms_and_pan:"email"`
	CurrentAddress          string `mask_all:"address" mask_except_comms:"address" mask_comms_and_pan:"allow"`
	PermanentAddress        string `mask_all:"address" mask_except_comms:"address" mask_comms_and_pan:"allow"`
	LoanAmount              float64
	Tenure                  int
	Interest                float64
	EDIAmount               float64
	ProcessingFee           float64
	AdvanceEMI              float64
	GST                     float64
	BankAccountNumber       string `mask_all:"account" mask_except_comms:"account" mask_comms_and_pan:"allow"`
	IFSCCode                string `mask_all:"redacted" mask_except_comms:"redacted" mask_comms_and_pan:"allow"`
	SourceEntityName        string
	PartnerCode             string
	SignDate                string
	DisbursedDate           string
	UTRNo                   string
	UpdatedAt               string
	FirmName                string
	Platform                string
	BankStatementStatus     int
	FailureReason           string
	RuleVersion             string
	RunDate                 string
	DSAID                   string
	TagsCategory            string
	Tags                    string
	ExperianScore           int
	ExperianError           string
	ExperianStatus          string
	CibilScore              int
	CibilStatus             string
	NachStatus              int
	LenderName              string
	AlternateMobile         string
	InsuranceType           string
	ActivatedOn             string
	AvailableLimit          float64
	MaxLimit                float64
	CreditLineStatus        int
	ReferenceID             string
	DateOfIncorporation     string
	BusinessAddressType     int
	BusinessAddress         string
	BusinessCategory        string
	Latitude                string
	Longitude               string
	InsuranceReferenceNo    string
	InsuranceStatus         string
	InsurancePremium        float64
	InsurancePremiumWithGst float64
	InsuranceCustomerName   string
	InsurancePlanType       string
	InsurancePolicyID       string
	InsuranceMaturityDate   string
	InsuranceOptdFor        string
	PlatformLender          string
	DynamicUserInfo         string
	IndustryType            string
	SubIndustryType         string
	ActionedOn              string
	Remark                  string
	Scheme                  string
	EmployementType         string
	KYCApprovedBy           string
	KYCApproverName         string
	AppliedLoanAmount       string
	CountryOfStudy          string
	University              string
	DSACode                 string
	DSAName                 string
	ExecutiveCode           string
	ExecutiveName           string
	Secured                 string
	InstituteName           string
	CourseName              string
	JourneyAssisted         string
	EmpCodeConsultant       string
	CRMIDofConsultant       string
	AssistedBy              string
	JourneyBase             string
}

type MisLoanDataStruct struct {
	LoanApplicationNo string
	CustomerID        string
	Status            int
	KYCStatus         int
	EmploymentType    string
	CreditDecision    string
	RCUDecision       string
	CreditDecisionBy  string
	RCUDecisionBy     string
	RCUSubStatus      string
	CreditComment     string
	RCUComment        string
	CreditConditions  string
	RCUConditions     string
}

type PFLCustomLeadDumpRow struct {
	UserID                    string          `db:"user_id"`
	LeadID                    string          `db:"lead_id"`
	CustomerID                string          `db:"customer_id"`
	Source                    string          `db:"source"`
	Status                    int             `db:"status"`
	UserCreatedAt             string          `db:"user_created_at"`
	Name                      string          `db:"name"`
	Email                     string          `db:"email" mask_all:"allow" mask_except_comms:"allow" mask_comms_and_pan:"email"`
	Pan                       string          `db:"pan"`
	Mobile                    string          `db:"mobile"`
	OccupationType            string          `db:"occupation_type"`
	City                      string          `db:"city"`
	State                     string          `db:"state"`
	Pincode                   string          `db:"pincode"`
	RadiusFlagResult          string          `db:"radius_flag_result"`
	RadiusValueInKMS          string          `db:"radius_value_kms"`
	DistanceBlocking          string          `db:"block_user_due_to_distance"`
	PermanentAddressPincode   string          `db:"permanent_address_pincode"`
	LoanApplicationID         string          `db:"loan_application_id"`
	LoanApplicationNo         string          `db:"loan_application_no"`
	LoanStatus                int             `db:"loan_status"`
	LoanKYCStatus             int             `db:"loan_kyc_status"`
	ApplicationCreatedAt      string          `db:"application_created_at"`
	LoanAmount                sql.NullFloat64 `db:"loan_amount"`
	Amount                    sql.NullFloat64 `db:"amount"`
	EligibleAmount            sql.NullFloat64 `db:"eligible_amount"`
	Tenure                    string          `db:"tenure"`
	EligibleTenure            string          `db:"eligible_tenure"`
	AnnualInterest            string          `db:"annual_interest"`
	EMIAmount                 sql.NullFloat64 `db:"emi_amount"`
	ProcessingFee             sql.NullFloat64 `db:"processing_fee"`
	GST                       sql.NullFloat64 `db:"gst"`
	AdvanceEMI                sql.NullFloat64 `db:"advance_emi"`
	DisbursalAmount           sql.NullFloat64 `db:"disbursal_amount"`
	NetDisbursalAmount        sql.NullFloat64 `db:"net_amount"`
	SignAgreementDate         string          `db:"sign_agreement_date"`
	DisbursedOrActivationDate string          `db:"disbursed_or_activation_date"`
	RejectionReason           string          `db:"rejection_reason"`
	Platform                  string          `db:"platform"`
	Latitude                  string          `db:"latitude"`
	Longitude                 string          `db:"longitude"`
	FaceMatchScore            string          `db:"face_match_score"`
	FaceMatchStatus           string          `db:"face_match_status"`
	FaceMatchConfidenceScore  string          `db:"face_match_confidence_score"`
	VASType                   string          `db:"vas_type"`
	VASCharges                sql.NullFloat64 `db:"vas_charges"`
	WorkExperience            string          `db:"work_experience"`
	Segment                   string          `db:"segment"`
	OfficialEmail             string          `db:"official_email"`
	MonthlyIncome             string          `db:"monthly_income"`
	EmployerName              string          `db:"employer_name"`
}

type RejectionReasonStruct struct {
	UserID          string `db:"user_id"`
	RejectionReason string `db:"rejection_reason"`
}

type PFLEnachMandateReportRow struct {
	CustomerID             string  `db:"crm_id"`
	LoanApplicationNo      string  `db:"loan_application_no"`
	LoanStatus             int     `db:"loan_status"`
	LoanKYCStatus          int     `db:"loan_kyc_status"`
	IFSC                   string  `db:"ifsc"`
	UMRN                   string  `db:"umrn"`
	ExternalBranchBankCode string  `db:"branch_code"`
	MICR                   string  `db:"micr"`
	BankName               string  `db:"bank_name"`
	AccountType            string  `db:"account_type"`
	AccountNo              string  `db:"account_no"`
	AccountName            string  `db:"account_name"`
	CapAmount              float64 `db:"cap_amount"`
	RegistrationStatus     int     `db:"registration_status"`
	RegistrationTimestamp  string  `db:"registration_timestamp"`
	LoanAccountNumber      string  `db:"loan_account_number"`
}

type MultiOfferRow struct {
	UserID                   string         `db:"user_id"`
	Name                     string         `db:"name"`
	FirmName                 string         `db:"firm_name"`
	Mobile                   string         `db:"mobile"`
	AgentCode                string         `db:"agent_code"`
	CustomerID               string         `db:"unique_id"`
	LoanAmount               float64        `db:"loan_amount"`
	Tenure                   int64          `db:"tenure"`
	Interest                 float64        `db:"interest"`
	ProcessingFee            float64        `db:"processing_fee"`
	LoanStatus               int            `db:"loan_status"`
	LenderID                 string         `db:"lender_id"`
	OfferAcceptedAt          sql.NullString `db:"offer_accepted_at"`
	InPrincipalProcessingFee float64        `db:"in_principal_pf"`
	InPrincipalInterest      float64        `db:"in_principal_interest"`
	InPrincipalOfferAmount   float64        `db:"in_principal_offer_amount"`
	InPrincipalTenure        int64          `db:"in_principal_tenure"`
	LoanType                 string         `db:"loantype"`
	LoanOfferStatus          int            `db:"loan_offer_status"`
	OfferUpdatedAt           sql.NullString `db:"offer_updated_at"`
	OfferCreatedAt           sql.NullString `db:"offer_created_at"`
}

type SalesDispositionReport struct {
	UniqueID          string     `db:"unique_id"`
	LoanApplicationNo string     `db:"loan_application_no"`
	FirmName          string     `db:"firm_name"`
	Mobile            string     `db:"mobile"`
	LeadCreatedAt     *time.Time `db:"lead_created_at"`
	LeadUpdatedAt     *time.Time `db:"lead_updated_at"`
	salesdispositionhistory.SalesDisposition
}

type OfferDetails struct {
	Amount        float64
	Tenure        float64
	Interest      float64
	ProcessingFee float64
}

type OfferNegoWFDetails struct {
	LoanAmount    workflow.LoanAmt
	ProcessingFee workflow.ProcessingFee
	InterestRate  workflow.InterestRate
	Tenure        workflow.Tenure
	RequestedBy   string
}

type KYCWFDetails struct {
	VKYCUploadStatus bool
}

type BREWFDetails struct {
	BRE1Result bool
	BRE2Result bool
}

type WorkflowDetails struct {
	OfferNegoWFDetails
	KYCWFDetails
	BREWFDetails
}

type WorkflowTaskRow struct {
	LoanApplicationID  string `db:"loan_application_id"`
	LoanApplicationNo  string `db:"loan_application_no"`
	Mobile             string `db:"mobile"`
	Email              string `db:"email"`
	Name               string `db:"name"`
	WorkflowType       string `db:"workflow_name"`
	RequestID          string `db:"service_request_id"`
	Status             string `db:"status"`
	CreatedAt          string `db:"initiated_at"`
	Action             string `db:"action"`
	LastActionBy       string `db:"action_by"`
	LastActionByGroup  string `db:"priority_group"`
	LastActionAt       string `db:"created_at"`
	PendencyGroup      string `db:"action_to"`
	PendencyGroupEmail string `db:"-"`
	Params             string `db:"params"`
	WorkflowDetails
}
type PFLUTMReportRow struct {
	UserID                    string `db:"user_id"`
	LeadID                    string `db:"lead_id"`
	CustomerID                string `db:"customer_id"`
	Source                    string `db:"source"`
	Status                    int    `db:"status"`
	UserCreatedAt             string `db:"user_created_at"`
	LoanApplicationNo         string `db:"loan_application_no"`
	LoanStatus                int    `db:"loan_status"`
	LoanKYCStatus             int    `db:"loan_kyc_status"`
	ApplicationCreatedAt      string `db:"application_created_at"`
	ApplicationUpdatedAt      string `db:"application_updated_at"`
	SignAgreementDate         string `db:"sign_agreement_date"`
	DisbursedOrActivationDate string `db:"disbursed_or_activation_date"`
	CampaignParams            string `db:"campaign_params"`
}

type DisbursalRow struct {
	UserID             string `db:"user_id"`
	DisbursalTimestamp string `db:"disbursal_timestamp"`
}

type ABFLCustomDumpRow struct {
	UserID                        string          `db:"user_id"`
	UserCreatedAt                 string          `db:"user_created_at"`
	ApplicationCreatedAt          string          `db:"application_created_at"`
	ApplicationUpdatedAt          string          `db:"application_updated_at"`
	CustomerId                    string          `db:"customer_id"`
	LoanApplicationNo             string          `db:"loan_application_no"`
	FirmName                      string          `db:"firm_name"`
	Name                          string          `db:"name"`
	Pan                           string          `db:"pan" mask_all:"allow" mask_except_comms:"allow" mask_comms_and_pan:"pan"`
	Mobile                        string          `db:"mobile" mask_all:"allow" mask_except_comms:"allow" mask_comms_and_pan:"mobile"`
	Email                         string          `db:"email" mask_all:"allow" mask_except_comms:"allow" mask_comms_and_pan:"email"`
	Gstin                         string          `db:"gstin"`
	Udyam                         string          `db:"udyam"`
	CibilScore                    string          `db:"cibil_score"`
	ABFLKYCWF                     string          `db:"abfl_kyc_wf"`
	ABFLKYCWFStartTime            string          `db:"abfl_kyc_wf_start_time"`
	ABFLKYCWFEndTime              string          `db:"abfl_kyc_wf_end_time"`
	ABFLCreditWF                  string          `db:"abfl_credit_wf"`
	ABFLCreditWFStartTime         string          `db:"abfl_credit_wf_start_time"`
	ABFLCreditWFEndTime           string          `db:"abfl_credit_wf_end_time"`
	ThickThinCategory             string          `db:"thick_thin_category"`
	HunterStatus                  string          `db:"hunter_status"`
	UserStatus                    int             `db:"user_status"`
	LoanStatus                    int             `db:"loan_status"`
	LoanKYCStatus                 int             `db:"loan_kyc_status"`
	RejectionReason               string          `db:"rejection_reason"`
	RejectionReasonCategory       string          `db:"rejection_reason_category"`
	CreditWorkflowRejectionReason string          `db:"credit_workflow_rejection_reason"`
	CreditWorkflowRiskRating      string          `db:"credit_workflow_risk_rating"`
	CreditWorkflowRemarks         string          `db:"credit_workflow_remarks"`
	AgentCode                     string          `db:"agent_code"`
	AgentName                     string          `db:"agent_name"`
	DisbursedOrActivationDate     string          `db:"disbursed_or_activation_date"`
	RequestedAmount               sql.NullFloat64 `db:"requested_amount"`
	Bre1EligibleAmount            sql.NullFloat64 `db:"-"`
	Bre2EligibleAmount            sql.NullFloat64 `db:"-"`
	LoanAmount                    sql.NullFloat64 `db:"loan_amount"`
	ProcessingFee                 sql.NullFloat64 `db:"processing_fee"`
	ProcessingFeeType             string          `db:"processing_fee_type"`
	RateOfInterest                sql.NullFloat64 `db:"rate_of_interest"`
	Tenure                        sql.NullString  `db:"tenure"`
	GST                           sql.NullFloat64 `db:"gst"`
	AdvanceEMI                    sql.NullFloat64 `db:"advance_emi"`
	NetDisbursalAmount            sql.NullFloat64 `db:"net_disbursal_amount"`
	PaHospitalisationPremium      sql.NullFloat64 `db:"-"`
	EmiProtectPremium             sql.NullFloat64 `db:"-"`
	CurrentAddress                string          `db:"current_address"`
	CityCurrentAddress            string          `db:"-"`
	StateCurrentAddress           string          `db:"-"`
	PincodeCurrentAddress         string          `db:"-"`
	PermanentAddress              string          `db:"permanent_address"`
	CityPermanentAddress          string          `db:"-"`
	StatePermanentAddress         string          `db:"-"`
	PincodePermanentAddress       string          `db:"-"`
	Source                        string          `db:"source"`
	SourcingPlatform              string          `db:"-"`
	PartnerCode                   string          `db:"-"`
	BizDocUpload                  string          `db:"-"`
	OhpDocUpload                  string          `db:"-"`
	BankStatementStatus           string          `db:"-"`
	BankFailureReason             string          `db:"-"`
	SourceEntityID                string          `db:"source_entity_id"`
	LenderID                      string          `db:"lender_id"`
	RMIDJourney                   string          `db:"rm_id_journey"`
	SoftApprovedStatus            string          `db:"soft_approved_status"`
}

type AddrStruct struct {
	Line1   string `json:"line1"`
	Line2   string `json:"line2"`
	City    string `json:"city"`
	Pincode string `json:"pincode"`
	State   string `json:"state"`
}

type ABFLUTMReportRow struct {
	CustomerID     string `db:"customer_id"`
	SourceEntityID string `db:"source_entity_id"`
	Mobile         string `db:"mobile"`
	PAN            string `db:"pan"`
	Email          string `db:"email"`
	DOB            string `db:"dob"`
	CampaignType   string `db:"campaign_type"`
	UTMSource      string `db:"utm_source"`
	UTMContent     string `db:"utm_content"`
	UTMMedium      string `db:"utm_medium"`
	UTMCampaign    string `db:"utm_campaign"`
	UTMTerm        string `db:"utm_term"`
	CreatedAt      string `db:"created_at"`
}
