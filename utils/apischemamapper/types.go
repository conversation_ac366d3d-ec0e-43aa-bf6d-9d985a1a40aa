package apischemamapper

import (
	"encoding/json"

	"finbox/go-api/functions/jsonschema"
)

type Dependency struct {
	Key   string `yaml:"key"`
	Value *any   `yaml:"value"`
}

type Validation struct {
	Type  string   `yaml:"type"`
	Value []string `yaml:"value"`
}

type Item struct {
	Type       string      `yaml:"type"`
	Validation *Validation `yaml:"validation"`
	Schema     *Field      `yaml:"schema"` // Required if Items.Type == "map"
	Items      *Item       `yaml:"items"`
}

type Field struct {
	Tag        string      `yaml:"tag"`
	Type       string      `yaml:"type"`
	Fields     []Field     `yaml:"fields"`
	Required   *bool       `yaml:"required"`
	Dependency *Dependency `yaml:"dependency"`
	Validation *Validation `yaml:"validation"`
	Items      *Item       `yaml:"items"`
}

type Schema struct {
	SchemaID                 string                `yaml:"schemaID" json:"schemaID"`
	Type                     string                `yaml:"type" json:"type"`
	Tag                      string                `yaml:"tag" json:"tag"`
	Fields                   []Field               `yaml:"fields" json:"fields"`
	RefreshToken             bool                  `json:"refreshToken"`
	SignalName               string                `json:"signalName"`
	StateValidationConfig    StateConfig           `json:"stateConfig"`
	SignalDataConfig         map[string]any        `json:"signalDataConfig"`
	ActivityLogConfig        []ActivityEvent       `json:"activityLogConfig"`
	LookupConfig             []Action              `json:"lookupConfig"`
	DerivedDynamicDataConfig map[string]any        `json:"derivedDynamicDataConfig"`
	CleanupKeys              []string              `json:"cleanupKeys"`
	Docs                     []DocsConfig          `json:"docs"`
	JSONSchema               *jsonschema.FBXSchema `json:"jsonSchema"`
}

func NewAPIMapperSchema(jsonSchema string) (*Schema, error) {
	schema := Schema{}
	err := json.Unmarshal([]byte(jsonSchema), &schema)
	if err != nil {
		return nil, err
	}
	return &schema, nil
}

type DocsConfig struct {
	DocumentType        string `json:"documentType"`
	DocumentID          string `json:"documentID"`
	Required            *bool  `json:"required"`
	MultipleUploadCount *int   `json:"multipleUploadCount"`
}

type Docs struct {
	DocumentID   string `json:"documentID" db:"document_id"`
	MediaID      string `json:"mediaID" db:"media_id"`
	DocumentType string `json:"documentType" db:"document_type"`
}

type Action struct {
	FunctionRef FunctionRef `json:"functionRef,omitempty"`
}
type FunctionRef struct {
	RefName   string         `json:"refName" validate:"required"`
	Arguments map[string]any `json:"arguments,omitempty"`
}

type ActivityEvent struct {
	UserID            string                 `json:"user_id"`
	SourceEntityID    string                 `json:"source_entity_id"`
	LoanApplicationID string                 `json:"loan_application_id"`
	EntityType        string                 `json:"entity_type"`
	EntityRef         string                 `json:"entity_ref"`
	EventType         string                 `json:"event_type"`
	Description       string                 `json:"description"`
	Metadata          map[string]interface{} `json:"metadata"`
	DateTime          string                 `json:"date_time"`

	// ModuleName is an optional field which is set under rejection_logs and can be passed if a user is supposed to be rejected.
	// This signifies which module rejected the user. This can be used to make decisions based on the module the user was rejected at.
	ModuleName string `json:"moduleName"`
}

type StateConfig struct {
	State     string   `json:"state"`
	SubStates []string `json:"subStates"`
}

type Config struct {
	Schemas []Schema `yaml:"schemas"`
}
