package fraudcheckutils

import (
	"errors"
	"finbox/go-api/constants"
	"finbox/go-api/functions/journey"
	"finbox/go-api/functions/logger"
)

type PennydropCheck interface {
	PennydropFraudCheck() (bool, error)
}

func fraudCheckFactory(userID, panNumber, sourceEntityID, lenderID, accountNumber string) (PennydropCheck, error) {
	parentSourceEntityID := sourceEntityID
	journey.GetParentSourceEntityID(userID, &parentSourceEntityID)
	switch parentSourceEntityID {
	case constants.ABFLPLID:
		return &ABFLPLPennydropCheck{
			PennydropCheckStruct{
				UserID:                userID,
				SourceEntityID:        sourceEntityID,
				ParentSourceEntityID:  parentSourceEntityID,
				PAN:                   panNumber,
				LoanApplicationStatus: []int{constants.LoanStatusDisbursed, constants.LoanStatusClosed},
				AccountNumber:         accountNumber,
			},
		}, nil
	case constants.MFLBLID:
		return &MFLBLPennydropCheck{
			PennydropCheckStruct{
				UserID:                userID,
				SourceEntityID:        sourceEntityID,
				ParentSourceEntityID:  parentSourceEntityID,
				PAN:                   panNumber,
				LoanApplicationStatus: []int{constants.LoanStatusDisbursed, constants.LoanStatusClosed},
				AccountNumber:         accountNumber,
			},
		}, nil
	case constants.IIFLBLID:
		return &IIFLBLPennydropCheck{
			PennydropCheckStruct{
				UserID:               userID,
				SourceEntityID:       sourceEntityID,
				ParentSourceEntityID: parentSourceEntityID,
				AccountNumber:        accountNumber,
				LenderID:             lenderID,
			},
		}, nil
	default:
		return nil, errors.New(errPennydropFraudCheckNotConfigured)
	}
}

func FraudCheck(userID, pan, sourceEntityID, lenderID, accountNumber string) (bool, error) {
	var (
		pennydropFraudCheck PennydropCheck
		err                 error
	)
	if pennydropFraudCheck, err = fraudCheckFactory(userID, pan, sourceEntityID, lenderID, accountNumber); err != nil {
		logger.WithUser(userID).Error(err)
		return false, err
	}
	return pennydropFraudCheck.PennydropFraudCheck()
}
