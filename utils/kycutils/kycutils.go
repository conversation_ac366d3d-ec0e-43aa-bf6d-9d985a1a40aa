package kycutils

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"finbox/go-api/internal/fbxerrors"
	usersql "finbox/go-api/internal/repository/psql/user"
	"finbox/go-api/models/coapplicantkycdetails"
	dsa "finbox/go-api/models/dsa"
	"finbox/go-api/models/externalservice"
	"finbox/go-api/models/featureflag"
	"finbox/go-api/models/loankycdetails"
	"finbox/go-api/models/multiuserloanrelations"
	"finbox/go-api/models/pandetails"
	"finbox/go-api/models/userdocuments"
	"finbox/go-api/models/userkycapplications"
	"finbox/go-api/models/usermodulemapping"
	"fmt"
	"net/http"
	"strings"
	"time"

	"finbox/go-api/common/usersutil"
	"finbox/go-api/constants"
	"finbox/go-api/errorHandler"
	"finbox/go-api/functions/activity"
	"finbox/go-api/functions/commonutils"
	"finbox/go-api/functions/emaillib"
	"finbox/go-api/functions/journey"
	"finbox/go-api/functions/legallogs"
	"finbox/go-api/functions/logger"
	"finbox/go-api/functions/services/gst"
	"finbox/go-api/functions/services/hyperverge"
	"finbox/go-api/functions/services/pannumber"
	"finbox/go-api/functions/structs"
	"finbox/go-api/functions/underwriting"
	"finbox/go-api/infra/db"
	"finbox/go-api/infra/s3"
	"finbox/go-api/models/digilockerkyc"
	"finbox/go-api/models/documents"
	"finbox/go-api/models/kycmodules"
	"finbox/go-api/models/loanapplication"
	"finbox/go-api/models/preselectedlender"
	"finbox/go-api/models/sourceentity"
	"finbox/go-api/models/userloandetails"
	"finbox/go-api/models/users"
	coapplicant "finbox/go-api/utils/co_applicant"
	"finbox/go-api/utils/convert"
	"finbox/go-api/utils/dsautils"
	"finbox/go-api/utils/fraudcheckutils"
	"finbox/go-api/utils/general"
	"finbox/go-api/utils/lockutils"
	"finbox/go-api/utils/mask"
	"finbox/go-api/utils/moduleutils"
	panUtils "finbox/go-api/utils/pan"

	"github.com/jmoiron/sqlx"
)

var log = logger.Log
var database = db.GetDB()

// ManualApprove approves the KYC of a loan application from dashboard
// it takes a loan application id, source entity id, dashboard user email
// and a boolean indicating whether its a case of dual name or not (will add a segment in agreement later for this)
// returns if kyc is now success, user id, if assisted approval already happened before, an error string and error object if any
func ManualApprove(ctx context.Context, loanApplicationID string, sourceEntityID string, dashboardUserEmail string, isDoubleName bool) (bool, string, bool, string, error) {
	type dbResStruct struct {
		Status            int
		UserID            string
		LenderID          string
		AssistedApproval  bool
		KYCSubStatus      int
		KYCStatus         int
		AdditionalClauses string
	}
	obj := dbResStruct{}
	var query = `select status, user_id as userid,
					assisted_approval as assistedapproval,
					lender_id as lenderid,
					coalesce(kyc_sub_status, 0) as kycsubstatus,
					coalesce(kyc_status, 0) as kycstatus,
					coalesce(additional_clauses, '') as additionalclauses
					FROM loan_application
					where loan_application_id = $1
						and source_entity_id = $2`
	err := database.Get(&obj, query, loanApplicationID, sourceEntityID)
	if err != nil {
		if err == sql.ErrNoRows {
			return false, "", false, "Loan Application not found", nil
		} else {
			logger.WithLoanApplication(loanApplicationID).Errorln(err)
			return false, "", false, "", err
		}
	}
	if general.InArr(obj.KYCStatus, []int{constants.LoanKYCStatusBankApproved, constants.LoanKYCStatusDocApproved}) {
		return false, "", false, "kyc is already approved, refresh the page", nil
	}

	if obj.Status >= constants.LoanStatusLoanApproved {
		return false, "", false, "loan is already approved, refresh the page", nil
	}
	constitution, err := multiuserloanrelations.GetConstitution(ctx, obj.UserID)

	if journey.IsABFLBLSourcing(sourceEntityID) && constitution == constants.Proprietorship {
		hasVkycApproved, err := coapplicantkycdetails.CheckIfVkycApproved(ctx, obj.UserID)
		if err != nil || !hasVkycApproved {
			log.WithContext(ctx).Errorf("[ManualApprove] Invalid ApprovalCriteria: %v", err)
			return false, "", false, "please complete video KYC verification", err
		}
	}
	hunterFeatureFlag := featureflag.Get(constants.ABFLHunterFeatureFlagKey, constants.HunterFlowKey)
	if journey.IsABFLBLSourcing(sourceEntityID) && hunterFeatureFlag {
		hunterDetails, err := usersql.DBGetUserHunterDetails(ctx, obj.UserID)

		if err != nil {
			logger.WithContext(ctx).Errorf("[IsCoApplicantUserKYCCompletedV2] Error getting hunter status for the userID:%v", obj.UserID)
			return false, "", false, "error getting hunter status for the user", err
		}

		if hunterDetails.HunterMatchCount == 1 && hunterDetails.HunterApprovalStatus != constants.HunterClearedStatus {
			logger.WithContext(ctx).Errorf("[IsCoApplicantUserKYCCompletedV2] Hunter status is not approved for the userID:%v", obj.UserID)
			return false, "", false, "hunter status is not approved for selected users", err
		}
	}
	//
	//if journey.IsABFLBLSourcing(sourceEntityID) {
	//
	//	isRiskRatingPresentForUser, err := lenderutil.IsRiskRatingPresentForUser(ctx, obj.UserID)
	//
	//	if err != nil {
	//		logger.WithContext(ctx).Errorf("[ManualApprove] Error getting risk rating for userID:%s, err: %v", obj.UserID, err)
	//		return false, "", false, constants.ErrGettingUserRiskRating, err
	//	}
	//
	//	if !isRiskRatingPresentForUser {
	//		logger.WithContext(ctx).Errorf("[ManualApprove] Risk rating not present for userID:%s", obj.UserID)
	//		return false, "", false, constants.ErrUserRiskRatingNotAvailable, err
	//	}
	//
	//	isRiskRatingPresentForBusiness, err := lenderutil.IsRiskRatingPresentForBusiness(ctx, obj.UserID)
	//
	//	if err != nil {
	//		logger.WithContext(ctx).Errorf("[ManualApprove] Error getting risk rating for business userID:%s, err: %v", obj.UserID, err)
	//		return false, "", false, constants.ErrUserRiskRatingNotAvailable, err
	//	}
	//
	//	if !isRiskRatingPresentForBusiness {
	//		logger.WithContext(ctx).Errorf("[ManualApprove] Risk rating not present for business userID:%s", obj.UserID)
	//		return false, "", false, constants.ErrBusinessRiskRatingNotAvailable, err
	//	}
	//}

	if journey.IsABFLBLSourcing(sourceEntityID) && journey.IsMultiUserJourney(obj.UserID, sourceEntityID) {
		hasCoApplicantAttemptedKYC, err := coapplicant.IsCoApplicantUserKYCCompletedV2(ctx, loanApplicationID, obj.UserID, sourceEntityID)
		if err != nil {
			logger.WithLoanApplication(loanApplicationID).Errorln("Getting error in validating coApplicant journey", err)
			return false, obj.UserID, obj.AssistedApproval, err.Error(), nil
		}

		if !hasCoApplicantAttemptedKYC {
			return false, obj.UserID, obj.AssistedApproval, "please upload kyc documents for all selected users before approving kyc", nil
		}
	}

	addressObj, err := userloandetails.GetAddress(loanApplicationID)
	if err != nil {
		logger.WithLoanApplication(obj.UserID).Errorln("error getting address", err)
		return false, obj.UserID, obj.AssistedApproval, "please update/set the address in application form tab before approving the KYC", nil
	}

	if !journey.IsPrimaryUserKYCToBeSkipped(sourceEntityID, obj.LenderID, obj.UserID) {
		// address validations based on kyc flow
		flowID := GetKYCFlowID(sourceEntityID, obj.UserID)
		switch flowID {
		case constants.KYCFlowDigilockerCurrentAddressPL:
			if addressObj.CurrentAddress == "" || addressObj.PermanentAddress == "" {
				logger.WithUser(obj.UserID).Error("empty address")
				return false, obj.UserID, obj.AssistedApproval, "please update/set the current/permanent address in application form tab before approving the KYC", nil
			}
		default:
			var addressStr string
			validateAddress := true
			if addressObj.PermanentAddress == "" && addressObj.CurrentAddress == "" {
				logger.WithUser(obj.UserID).Error("empty address")
				return false, obj.UserID, obj.AssistedApproval, "please update/set the address in application form tab before approving the KYC", nil
			} else if addressObj.PermanentAddress == "" {
				logger.WithUser(obj.UserID).Error("permanent address is empty")
				addressStr = addressObj.CurrentAddress
			} else if addressObj.CurrentAddress == "" {
				logger.WithUser(obj.UserID).Error("current address is empty")
				addressStr = addressObj.PermanentAddress
			}
			// var address addrStruct
			if addressStr == "" {
				// parse currentAdress and permanentAddress
				addressStr = addressObj.PermanentAddress
				address, err := commonutils.ParseAddress(addressStr)
				if err != nil {
					logger.WithUser(obj.UserID).Warn("error while parsing permanent address: ", err)
				}
				_, _, _, _, _, err = ValidateAddress(address.Line1, address.Line2, address.City, address.State, address.Pincode)
				if err != nil {
					logger.WithUser(obj.UserID).Warn("permanent address fields are invalid/empty: ", err)
					addressStr = addressObj.CurrentAddress
				} else {
					validateAddress = false
				}
			}
			if validateAddress {
				address, err := commonutils.ParseAddress(addressStr)
				if err != nil {
					logger.WithUser(obj.UserID).Error("error while parsing address: ", err)
					return false, obj.UserID, obj.AssistedApproval, "please update/set the address in application form tab before approving the KYC", nil
				}
				// validate address: fields are not empty and pincode is valid
				_, _, _, _, _, err = ValidateAddress(address.Line1, address.Line2, address.City, address.State, address.Pincode)
				if err != nil {
					logger.WithUser(obj.UserID).Error("invalid address", err)
					return false, obj.UserID, obj.AssistedApproval, "please check and update/set the address line1, line2, city, state, pincode in application form tab before approving the KYC", nil
				}
			}
		}
	}

	// lock check
	defer UnlockManualApproval(loanApplicationID)
	errStr := LockManualApproval(loanApplicationID)
	if errStr != "" {
		return false, "", false, errStr, nil
	}

	// if obj.KYCStatus == constants.LoanKYCStatusAddressVerificationPending {
	// 	deveiationOBJ := deviations.Type{
	// 		UserID:            obj.UserID,
	// 		LoanApplicationID: sql.NullString{String: loanApplicationID, Valid: true},
	// 		DeviationType:     deviations.TypeAddressVerify,
	// 		Remarks:           fmt.Sprintf("Manual KYC Approval by %s", dashboardUserEmail),
	// 	}
	// 	err := deviations.Add(deveiationOBJ)
	// 	if err != nil {
	// 		errorHandler.ReportToSentryWithoutRequest(err)
	// 		logger.WithUser(obj.UserID).Error(err)
	// 	}
	// }
	tx, err := database.Beginx()
	if err != nil {
		logger.WithUser(obj.UserID).Error(err)
		return false, obj.UserID, obj.AssistedApproval, "", err
	}
	defer tx.Rollback()

	// move rejected ones to normal
	query = `UPDATE loan_kyc_details set status = $1, updated_at = NOW(), rejection_reason = NULL where loan_id = $2 and status = $3 and doc_type not in ($4,$5)`
	_, err = tx.Exec(query, constants.KYCDocStatusUploaded, loanApplicationID, constants.KYCDocStatusRejected, constants.DocTypeDigilockerAadhaar, constants.DocTypeAPIPAN)
	if err != nil {
		logger.WithUser(obj.UserID).Error(err)
		return false, obj.UserID, obj.AssistedApproval, "", err
	}

	kycStatus := constants.LoanKYCStatusDocApproved
	if journey.IsABFLBLSourcing(sourceEntityID) {
		// check current running workflow
		var count int64
		query = `select count(*) from dashboard_workflow_status_tracker where resource_id = $1 and workflow_name in ('abfl_bre_wf', 'abfl_bre_wf_v2') and status = 'processing'`
		err = database.Get(&count, query, loanApplicationID)
		if err != nil {
			logger.WithUser(obj.UserID).Error(err)
		}
		if count > 0 {
			kycStatus = constants.LoanKYCStatusUnderReview
		}
	}
	loanObjToUpdate := loanapplication.StructForSet{
		ID:        loanApplicationID,
		KYCStatus: &kycStatus,
	}

	if !journey.IsTemporalFlow(obj.UserID, sourceEntityID, usermodulemapping.KYC) {
		err = moduleutils.UpdateKYC(tx, constants.ModuleKYCSuccess, obj.UserID, loanApplicationID, sourceEntityID)
		if err != nil {
			logger.WithUser(obj.UserID).Error(err)
			return false, obj.UserID, obj.AssistedApproval, "", err
		}
	}

	if isDoubleName {
		var kycSubStatus int
		var event, description string
		if obj.KYCSubStatus == constants.LoanKYCSubStatusDOBMismatch {
			kycSubStatus = constants.LoanKYCSubStatusDOBAndNameMismatch
			loanObjToUpdate.KYCSubStatus = &kycSubStatus
			event = constants.ActivityDualNameAndDOB
			description = "Added dual name and dob clause to agreement"
		} else {
			kycSubStatus = constants.LoanKYCSubStatusNameMismatch
			loanObjToUpdate.KYCSubStatus = &kycSubStatus
			event = constants.ActivityDualName
			description = "Added dual name clause to agreement"
		}

		if clauses, added := underwriting.AppendClauses(obj.AdditionalClauses, []string{constants.NameMismatchClause}); added {
			loanObjToUpdate.AdditionalClauses = sql.NullString{String: clauses, Valid: true}
		}

		activityObj := activity.ActivityEvent{
			UserID:            obj.UserID,
			SourceEntityID:    sourceEntityID,
			LoanApplicationID: "",
			EntityType:        constants.EntityTypeDashboardUser,
			EntityRef:         dashboardUserEmail,
			EventType:         event,
			Description:       description,
		}
		activity.RegisterEvent(&activityObj, general.GetTimeStampString())
	}

	err = kycmodules.Insert(tx, loanApplicationID, constants.KYCTypeManualReview, "", constants.KYCModuleStatusSuccess, "")
	if err != nil {
		logger.WithUser(obj.UserID).Error(err)
		return false, obj.UserID, obj.AssistedApproval, "", err
	}
	err = loanapplication.Update(tx, loanObjToUpdate)
	if err != nil {
		logger.WithUser(obj.UserID).Error(err)
		return false, obj.UserID, obj.AssistedApproval, "", err
	}
	err = tx.Commit()
	if err != nil {
		logger.WithUser(obj.UserID).Error(err)
		return false, obj.UserID, obj.AssistedApproval, "", err
	}

	// moved, err := MoveToAddressQueue(sourceEntityID, obj.UserID, loanApplicationID)
	// if err != nil {
	// 	logger.WithLoanApplication(loanApplicationID).Error(err)
	// 	return false, obj.UserID, obj.AssistedApproval, "", err
	// }
	// if moved {
	// 	// don't mark as kyc success
	// 	return false, obj.UserID, obj.AssistedApproval, "", nil
	// }
	// can mark as kyc success
	dateTimeNowString := general.GetTimeStampString()
	go func() {
		defer errorHandler.RecoveryNoResponse()
		activityObj := activity.ActivityEvent{
			UserID:            obj.UserID,
			SourceEntityID:    sourceEntityID,
			LoanApplicationID: loanApplicationID,
			EntityType:        constants.EntityTypeDashboardUser,
			EntityRef:         dashboardUserEmail,
			EventType:         constants.ActivityKYCVerified,
			Description:       "",
		}
		activity.RegisterEvent(&activityObj, dateTimeNowString)
	}()
	return true, obj.UserID, obj.AssistedApproval, "", nil
}

func ManualKYCStatusUpdate(ctx context.Context, loanApplicationID, sourceEntityID, dashboardUserEmail, manualKycStatus, reason, remark string) (bool, string, string, error) {
	loan, err := loanapplication.Get(context.Background(), loanApplicationID)
	// err := database.Get(&obj, query, loanApplicationID, sourceEntityID)
	if err != nil {
		if err == sql.ErrNoRows {
			return false, "", "Loan Application not found", nil
		} else {
			logger.WithLoanApplication(loanApplicationID).Errorf("[ManualKYCStatusUpdate] failed to get loan application from DB. err: %v", err)
			return false, "", "", err
		}
	}

	app, err := userkycapplications.GetLatestByUser(loan.UserID, sourceEntityID)
	if err != nil {
		logger.WithLoanApplication(loanApplicationID).Errorf("[ManualKYCStatusUpdate] Failed to get user kyc detail.  err: %v, userID: %v", err, loan.UserID)
		return false, loan.UserID, "", err
	}

	if app.Status != userkycapplications.ConditonalSuccess {
		logger.WithLoanApplication(loanApplicationID).Errorf("[ManualKYCStatusUpdate] KYC application is not in manual Review. err: %v, userID: %v", err, loan.UserID)
		return false, loan.UserID, "KYC application is not in manual Review", err

	}

	// lock check
	defer UnlockManualApproval(loanApplicationID)
	errStr := LockManualApproval(loanApplicationID)
	if errStr != "" {
		return false, "", errStr, nil
	}
	kycStatus := constants.LoanKYCStatusBankApproved
	userKycStatus := userkycapplications.StatusComplete
	eventType := constants.ActivityKYCManualSuccess
	activityDiscription := fmt.Sprintf("Reason: %s \n Remarks: %s", reason, remark)

	if strings.ToUpper(manualKycStatus) == constants.ManualKYCStatusRejected {
		kycStatus = constants.LoanKYCStatusBankRejected
		userKycStatus = userkycapplications.StatusRejected
		eventType = constants.ActivityKYCManualFailed

	}

	tx, err := database.Beginx()
	if err != nil {
		logger.WithLoanApplication(loanApplicationID).Errorf("[ManualKYCStatusUpdate] failed to do database.Bignix. err: %v, userID: %v", err, loan.UserID)
		return false, loan.UserID, "", err
	}
	defer tx.Rollback()

	loanObjToUpdate := loanapplication.StructForSet{
		ID:        loanApplicationID,
		KYCStatus: &kycStatus,
	}

	err = loanapplication.Update(tx, loanObjToUpdate)
	if err != nil {
		logger.WithLoanApplication(loanApplicationID).Errorf("[ManualKYCStatusUpdate] failed to update loan application. err: %v, userID: %v", err, loan.UserID)
		return false, loan.UserID, "", err
	}

	err = userkycapplications.UpdateStatusByUserApplicationID(context.Background(), loan.UserID, app.ApplicationID, userKycStatus)
	if err != nil {
		logger.WithLoanApplication(loanApplicationID).Errorf("[ManualKYCStatusUpdate] KYC application already completed. err: %v, userID: %v", err, loan.UserID)
		return false, loan.UserID, "", err
	}
	err = tx.Commit()
	if err != nil {
		logger.WithLoanApplication(loanApplicationID).Errorf("[ManualKYCStatusUpdate] failed to do DB commit. err: %v, userID: %v", err, loan.UserID)
		return false, loan.UserID, "", err
	}

	dateTimeNowString := general.GetTimeStampString()
	go func() {
		defer errorHandler.RecoveryNoResponse()
		activityObj := activity.ActivityEvent{
			UserID:            loan.UserID,
			SourceEntityID:    sourceEntityID,
			LoanApplicationID: loanApplicationID,
			EntityType:        constants.EntityTypeLenderUser,
			EntityRef:         dashboardUserEmail,
			EventType:         eventType,
			Description:       activityDiscription,
		}
		activity.RegisterEvent(&activityObj, dateTimeNowString)
	}()
	return true, loan.UserID, "", nil
}

func DashboardDocs(loanApplicationID string, sourceEntityID string, lenderID string, maskType int) ([]LoanKYCDataResp, map[string]interface{}, map[string][]SourceValue, LoanStruct, string, error) {
	var docs []LoanKYCDataResp
	var ekycDetails map[string]interface{}
	var sourceValues map[string][]SourceValue
	loanObj, errStr, err := getLoanDetailsDashboard(loanApplicationID, sourceEntityID, lenderID)
	if errStr != "" {
		logger.WithLoanApplication(loanApplicationID).Errorln(errStr)
		return docs, ekycDetails, sourceValues, loanObj, errStr, nil
	}
	if err != nil {
		logger.WithLoanApplication(loanApplicationID).Errorln(err)
		return docs, ekycDetails, sourceValues, loanObj, "", err
	}

	docs, err = GetKYCDashboardDocs(loanApplicationID)
	if err != nil {
		logger.WithLoanApplication(loanApplicationID).Errorln(err)
	}

	ekycDetails = getEKYCData(loanApplicationID)
	flowID := GetKYCFlowID(loanObj.SourceEntityID, loanObj.UserID)
	sourceValues = getSourceValues(loanApplicationID, flowID, &loanObj, maskType)
	return docs, ekycDetails, sourceValues, loanObj, "", nil

}

func IsRetriggerAllowed(loanApplicationID string, userID string, sourceEntityID string, lenderID string) (KYCLoanDetailsStruct, string, error) {
	previousModuleToSelfie, _ := kycmodules.GetPrevious(constants.KYCTypeSelfie, loanApplicationID)
	if previousModuleToSelfie == constants.KYCTypeCKYC {
		return KYCLoanDetailsStruct{}, "kyc checks cannot be re run for CKYC documents", nil
	}
	dbObj, err := getKYCLoanDetails(loanApplicationID, "", sourceEntityID, lenderID)
	if err != nil {
		return dbObj, "loan not found", err
	}
	currentStatus := constants.GetLoanStatusText(dbObj.Status, dbObj.KYCStatus)

	if currentStatus != "KYC_PROCESSING" && currentStatus != "KYC_REJECTED" {
		log.Println("status should be KYC_REJECTED or KYC_PROCESSING")
		return dbObj, "status should be KYC_REJECTED or KYC_PROCESSING", nil
	}

	// move application to kyc processing first, if rejected
	if currentStatus == "KYC_REJECTED" {
		statusObj := constants.LoanStatusStrToNum["KYC_PROCESSING"]
		tx, _ := database.Begin()
		query := "update loan_application set status = $1, kyc_status = $2, updated_at = NOW() where loan_application_id = $3"
		_, err = tx.Exec(query, statusObj.Status, statusObj.KYCStatus, loanApplicationID)
		if err != nil {
			logger.WithLoanApplication(loanApplicationID).Errorln(err)
			tx.Rollback()
			return dbObj, "", err
		}
		query = "update loan_kyc_details set rejection_reason = NULL, status = $1 where loan_id = $2 and status = $3 and doc_type not in ($4, $5);"
		_, err = tx.Exec(query, constants.KYCDocStatusUploaded, loanApplicationID, constants.KYCDocStatusRejected, constants.DocTypeDigilockerAadhaar, constants.DocTypeAPIPAN)
		if err != nil {
			logger.WithLoanApplication(loanApplicationID).Errorln(err)
			tx.Rollback()
			return dbObj, "", err
		}
		tx.Commit()
	}
	return dbObj, "", nil
}

// IsDigilockerRetriggerAllowed checks whether retrigger is allowed for kyc digilocker cases. If yes, mark the rejected docs to uploaded state.
func IsDigilockerRetriggerAllowed(loanApplicationID string) (string, error) {
	dbObj, err := getKYCLoanDetails(loanApplicationID, "", "", "")
	if err != nil {
		return "loan not found", err
	}
	currentStatus := constants.GetLoanStatusText(dbObj.Status, dbObj.KYCStatus)
	if currentStatus != "LOAN_DETAILS_SUBMITTED" {
		log.Warn("status should be LOAN_DETAILS_SUBMITTED ")
		return "status should be in LOAN_DETAILS_SUBMITTED", nil
	}

	return "", nil
}

// UploadGSTMediaDoc uploads gst as a media doc, makes entry to media table and loan kyc details table, returns error if any
func UploadGSTMediaDoc(userID string, gstin string, lenderID string, sourceEntityID string, loanApplicationID string) error {
	html, err := gst.GetHTML(gstin, userID, lenderID, sourceEntityID)
	if err != nil {
		logger.WithUser(userID).Error(err)
		return err
	}
	mediaID := general.GetUUID()
	path := fmt.Sprintf("%s/%s.pdf", userID, mediaID)
	done := convert.HTMLStringToPDF(html, path)
	if !done {
		err = errors.New("error in uploading file to s3")
		logger.WithUser(userID).Error(err)
		return err
	}

	// document_category in documents table is media type
	mediaType := "Business_Registration_Proof"
	// save in DB
	query := `insert into media (media_id, media_type, path, user_id, status, created_at, created_by)
				values ( $1, $2, $3, $4, $5, current_timestamp, $6 ) `

	_, err = database.Exec(query, mediaID, mediaType, path, userID, constants.MediaStatusActive, userID)
	if err != nil {
		logger.WithUser(userID).Error(err)
		return err
	}

	gstDocumentID, err := documents.GetID("GST_CERTIFICATE", constants.DocCatBusinessProof)
	if err != nil {
		logger.WithUser(userID).Error(err)
		return err
	}

	uniqueID := general.GetUUID()
	documentID := gstDocumentID
	status := constants.KYCDocStatusUploaded
	backMediaID := sql.NullString{String: "", Valid: false}

	query = `INSERT INTO loan_kyc_details
	(loan_kyc_details_id, loan_id, media_id, doc_type, document_id, status, created_at, created_by, back_media_id)
	VALUES ($1, $2, $3, $4, $5, $6, current_timestamp, $7, $8)`

	_, err = database.Exec(query, uniqueID, loanApplicationID, mediaID, mediaType, documentID, status, userID, backMediaID)
	if err != nil {
		logger.WithUser(userID).Error(err)
		return err
	}
	return nil
}

func Submit(submittedValues SubmitStruct) (KYCLoanDetailsStruct, string, error) {
	var dbObj KYCLoanDetailsStruct
	if len(submittedValues.ReqDocuments) == 0 {
		log.Println("document list cannot be empty")
		return dbObj, "document list cannot be empty", nil
	}

	dbObj, err := getKYCLoanDetails(submittedValues.LoanApplicationID, submittedValues.UserID, submittedValues.SourceEntityID, "")
	if err != nil {
		logger.WithLoanApplication(submittedValues.LoanApplicationID).Error(err)
		return dbObj, "", err
	}

	digilockerSuccess, err := isDigilockerSuccess(submittedValues.LoanApplicationID)
	if err != nil {
		logger.WithLoanApplication(submittedValues.LoanApplicationID).Error(err)
		return dbObj, "", err
	}
	loanStatus := constants.GetLoanStatusText(dbObj.Status, dbObj.KYCStatus)
	if loanStatus != "LOAN_DETAILS_SUBMITTED" && loanStatus != "KYC_REJECTED" {
		logger.WithLoanApplication(submittedValues.LoanApplicationID).Warn("KYC already exists, status = " + loanStatus)
		return dbObj, "KYC already exists", nil
	}

	documents, photoMediaID, mediaIDs, errStr, addressProofFound, panFound := getKYCDocuments(submittedValues.ReqDocuments, dbObj.UserID, submittedValues.LoanApplicationID)
	if errStr != "" {
		logger.WithLoanApplication(submittedValues.LoanApplicationID).Info(errStr)
		return dbObj, errStr, nil
	}
	if !panFound && journey.ShowPANInKYC(submittedValues.SourceEntityID) && !digilockerSuccess {
		return dbObj, "pan missing", nil
	}
	if !addressProofFound && !submittedValues.EKYC && !digilockerSuccess {
		return dbObj, "at least one address proof or ekyc must be available", nil
	}
	mediaTempQuery := fmt.Sprintf("select count(*) from media where user_id = '%s' and status = %d and media_id in (?); ", dbObj.UserID, constants.MediaStatusActive)
	mediaQuery, args, err := sqlx.In(mediaTempQuery, mediaIDs)
	if err != nil {
		logger.WithLoanApplication(submittedValues.LoanApplicationID).Error(err)
		return dbObj, "", err
	}
	mediaQuery = database.Rebind(mediaQuery)
	var mediaCount int
	err = database.Get(&mediaCount, mediaQuery, args...)
	if err != nil {
		logger.WithLoanApplication(submittedValues.LoanApplicationID).Error(err)
		return dbObj, "", err
	}

	if mediaCount != len(mediaIDs) {
		log.Println("some media_id do not exist, please check the request")
		return dbObj, "some media_id do not exist, please check the request", nil
	}

	query := `INSERT INTO loan_kyc_details
		(loan_kyc_details_id, loan_id, media_id, doc_type, document_id, status, created_at, created_by, back_media_id)
		VALUES (:unique_id, :loan_id, :media_id, :doc_type, :document_id, :status, current_timestamp, :created_by, :back_media_id)`
	tx, _ := database.Beginx()
	_, err = tx.NamedExec(query, documents)
	if err != nil {
		logger.WithLoanApplication(submittedValues.LoanApplicationID).Error(err)
		tx.Rollback()
		return dbObj, "", err
	}

	updateLoanQuery := "update loan_application set kyc_status = $1, status = $2, sign_media_id = $3, updated_at = NOW() where loan_application_id = $4; "
	_, err = tx.Exec(updateLoanQuery, constants.LoanKYCStatusDocProcessing, constants.LoanStatusDetails, photoMediaID, submittedValues.LoanApplicationID)
	if err != nil {
		logger.WithLoanApplication(submittedValues.LoanApplicationID).Error(err)
		tx.Rollback()
		return dbObj, "", err
	}

	err = moduleutils.UpdateKYC(tx, constants.ModuleKYCWait, dbObj.UserID, submittedValues.LoanApplicationID, dbObj.SourceEntityID)
	if err != nil {
		logger.WithLoanApplication(submittedValues.LoanApplicationID).Error(err)
		tx.Rollback()
		return dbObj, "", err
	}

	tx.Commit()

	if dbObj.LoanType != constants.LoanTypePersonalLoan {
		activeGSTList, isActiveGST := usersutil.GetActiveGSTIN(dbObj.UserID)
		// Upload GST certificate if active gst found for an user and hide gst setting active for source entity
		if isActiveGST && journey.HideGSTKYCforActiveGSTIN(submittedValues.SourceEntityID) {
			GSTIN := activeGSTList[0]
			go UploadGSTMediaDoc(dbObj.UserID, GSTIN, "", submittedValues.SourceEntityID, submittedValues.LoanApplicationID)
		}
	}

	// save legal logs
	if submittedValues.Consent.ConsentText != "" {
		go legallogs.Save(dbObj.UserID, submittedValues.Consent.ConsentText, submittedValues.Consent.Lat, submittedValues.Consent.Lon, submittedValues.Consent.Height, submittedValues.Consent.Accuracy, submittedValues.RemoteAddress)
	}
	dateTimeNowString := general.GetTimeStampString()
	go func() {
		defer errorHandler.RecoveryNoResponse()
		activityObj := activity.ActivityEvent{
			UserID:            dbObj.UserID,
			SourceEntityID:    submittedValues.SourceEntityID,
			LoanApplicationID: submittedValues.LoanApplicationID,
			EntityType:        submittedValues.EntityType,
			EntityRef:         submittedValues.EntityRef,
			EventType:         constants.ActivityKYCSubmitted,
			Description:       constants.KYCSubmitModeManual,
		}
		activity.RegisterEvent(&activityObj, dateTimeNowString)

	}()
	return dbObj, "", nil
}

func Resubmit(resubmitValues ReSubmitStruct) (KYCLoanDetailsStruct, string, error) {
	var dbObj KYCLoanDetailsStruct

	if len(resubmitValues.ReqDocuments) == 0 {
		log.Println("document list cannot be empty")
		panic("document list cannot be empty")
	}
	dbObj, err := getKYCLoanDetails(resubmitValues.LoanApplicationID, resubmitValues.UserID, resubmitValues.SourceEntityID, "")
	if err != nil {
		errStr := "Loan not found"
		logger.WithLoanApplication(resubmitValues.LoanApplicationID).Errorln(errStr)
		return dbObj, errStr, nil
	}
	digilockerDone, err := isDigilockerSuccess(resubmitValues.LoanApplicationID)
	if err != nil {
		logger.WithLoanApplication(resubmitValues.LoanApplicationID).Errorln(err)
		return dbObj, "", err
	}
	loanStatus := constants.GetLoanStatusText(dbObj.Status, dbObj.KYCStatus)
	if (loanStatus != "KYC_REJECTED" && resubmitValues.EntityType == constants.EntityTypeCustomer) || (loanStatus != "KYC_REJECTED" && resubmitValues.EntityType == constants.EntityTypeDashboardUser && !digilockerDone) {
		log.Println("Loan is not in KYC rejected state, status = " + loanStatus)
		return dbObj, "Loan is not in KYC rejected state", nil
	}

	if resubmitValues.EntityType == constants.EntityTypeCustomer { // should i make this common for dashboard as well?
		var reqCount int
		query := `SELECT count(distinct doc_type) as reqcount from loan_kyc_details where loan_id = $1 and status = $2 and doc_type not in ($3,$4);`
		err = database.Get(&reqCount, query, resubmitValues.LoanApplicationID, constants.KYCDocStatusRejected, constants.DocTypeDigilockerAadhaar, constants.DocTypeAPIPAN)
		if err != nil {
			logger.WithLoanApplication(resubmitValues.LoanApplicationID).Errorln(err)
			return dbObj, "", err
		}

		submittedCount := len(resubmitValues.ReqDocuments)
		if resubmitValues.EKYC {
			submittedCount += 1
		}

		if submittedCount < reqCount {
			log.Println("missing required number of docs")
			return dbObj, "missing required number of docs", nil
		}
	}

	documents, photoMediaID, mediaIDs, errStr, _, _ := getKYCDocuments(resubmitValues.ReqDocuments, dbObj.UserID, resubmitValues.LoanApplicationID)
	if errStr != "" {
		return dbObj, errStr, nil
	}
	if len(mediaIDs) > 0 {
		var mediaCount int
		mediaTempQuery := fmt.Sprintf("select count(*) from media where user_id = '%s' and status = %d and media_id in (?); ", dbObj.UserID, constants.MediaStatusActive)
		mediaQuery, args, err := sqlx.In(mediaTempQuery, mediaIDs)
		if err != nil {
			logger.WithLoanApplication(resubmitValues.LoanApplicationID).Errorln(err)
			return dbObj, "", err
		}
		mediaQuery = database.Rebind(mediaQuery)
		err = database.Get(&mediaCount, mediaQuery, args...)
		if err != nil {
			logger.WithLoanApplication(resubmitValues.LoanApplicationID).Errorln(err)
			return dbObj, "", err
		}
		if mediaCount != len(mediaIDs) {
			log.Println("some media_id do not exist, please check the request")
			return dbObj, "some media_id do not exist, please check the request", nil
		}

	}
	tx, _ := database.Beginx()
	if len(documents) > 0 {
		query := `INSERT INTO loan_kyc_details
		(loan_kyc_details_id, loan_id, media_id, doc_type, document_id, status, created_at, created_by, back_media_id)
		VALUES (:unique_id, :loan_id, :media_id, :doc_type, :document_id, :status, current_timestamp, :created_by, :back_media_id)`
		_, err = tx.NamedExec(query, documents)
		if err != nil {
			logger.WithLoanApplication(resubmitValues.LoanApplicationID).Errorln(err)
			tx.Rollback()
			return dbObj, "", err
		}
	}

	// update loan_kyc_details now
	query := `UPDATE loan_kyc_details set status = $1, updated_at = NOW() where loan_id = $2 and status = $3 and doc_type not in($4,$5)`
	_, err = tx.Exec(query, constants.KYCDocStatusInactive, resubmitValues.LoanApplicationID, constants.KYCDocStatusRejected, constants.DocTypeDigilockerAadhaar, constants.DocTypeAPIPAN)
	if err != nil {
		logger.WithLoanApplication(resubmitValues.LoanApplicationID).Errorln(err)
		tx.Rollback()
		return dbObj, "", err
	}
	if photoMediaID != "" {
		var updateLoanQuery = `update loan_application set kyc_status = $1, updated_at = current_timestamp,
							sign_media_id = $2
						where loan_application_id = $3;`
		_, err = tx.Exec(updateLoanQuery, constants.LoanKYCStatusDocProcessing, photoMediaID, resubmitValues.LoanApplicationID)
		if err != nil {
			logger.WithLoanApplication(resubmitValues.LoanApplicationID).Errorln(err)
			tx.Rollback()
			return dbObj, "", err
		}
	} else {
		err = loanapplication.UpdateKYCStatus(tx, resubmitValues.LoanApplicationID, constants.LoanKYCStatusDocProcessing)
		if err != nil {
			logger.WithLoanApplication(resubmitValues.LoanApplicationID).Errorln(err)
			tx.Rollback()
			return dbObj, "", err
		}
	}

	err = moduleutils.UpdateKYC(tx, constants.ModuleKYCWait, dbObj.UserID, resubmitValues.LoanApplicationID, dbObj.SourceEntityID)
	if err != nil {
		logger.WithLoanApplication(resubmitValues.LoanApplicationID).Errorln(err)
		tx.Rollback()
		return dbObj, "", err
	}
	tx.Commit()

	dateTimeNowString := general.GetTimeStampString()
	go func() {
		defer errorHandler.RecoveryNoResponse()
		activityObj := activity.ActivityEvent{
			UserID:            dbObj.UserID,
			SourceEntityID:    resubmitValues.SourceEntityID,
			LoanApplicationID: resubmitValues.LoanApplicationID,
			EntityType:        resubmitValues.EntityType,
			EntityRef:         resubmitValues.EntityRef,
			EventType:         constants.ActivityKYCResubmitted,
			Description:       constants.KYCSubmitModeManual,
		}
		activity.RegisterEvent(&activityObj, dateTimeNowString)

	}()
	return dbObj, "", nil
}

func GetDetails(getDetailsValues GetDetailsStruct) (map[string]interface{}, string, error) {
	digilockerDone := false
	var rejectCount int
	resData := make(map[string]interface{})
	dbObj, err := getKYCLoanDetails(getDetailsValues.LoanApplicationID, "", getDetailsValues.SourceEntityID, "")
	if err != nil {
		log.Println(err)
		return resData, "Loan Not Found", nil
	}

	// hide business proof for specific cases
	hideSignature := journey.IsHideSignature(dbObj.SourceEntityID)
	hideBusinessRegistrationProof, errStr := isHideBusinessRegistrationProof(dbObj)
	if errStr != "" {
		logger.WithLoanApplication(getDetailsValues.LoanApplicationID).Errorln(errStr)
		return resData, errStr, nil
	}

	docStatusMap := make(map[string]DocStatusStruct)

	alreadyUploaded := false
	digilockerTried, err := isDigilockerTried(getDetailsValues.LoanApplicationID)
	if err != nil {
		logger.WithLoanApplication(getDetailsValues.LoanApplicationID).Errorln(err)
		return resData, "", err
	}
	loanStatus := constants.GetLoanStatusText(dbObj.Status, dbObj.KYCStatus)
	if loanStatus == "KYC_PROCESSING" || loanStatus == "KYC_REJECTED" || digilockerTried {
		// fetch data from DB
		docStatusMap, alreadyUploaded, digilockerDone, err = getLoanKYCDetails(getDetailsValues.LoanApplicationID)
		if err != nil {
			logger.WithLoanApplication(getDetailsValues.LoanApplicationID).Errorln(err)
			return resData, "", err
		}
	}
	// get reject count
	query := `select count(*) from loan_kyc_details where loan_id = $1 and status in ($2, $3)`
	err = database.Get(&rejectCount, query, getDetailsValues.LoanApplicationID, constants.KYCDocStatusInactive, constants.KYCDocStatusRejected)
	if err != nil {
		logger.WithLoanApplication(getDetailsValues.LoanApplicationID).Errorln(err)
		return resData, "", err
	}

	dbDocs, err := getAvailableDocs(hideBusinessRegistrationProof, hideSignature)
	if err != nil {
		logger.WithLoanApplication(getDetailsValues.LoanApplicationID).Errorln(err)
		return resData, "", err
	}
	onlyEkyc := false
	if getDetailsValues.EntityType == constants.EntityTypeCustomer && getDetailsValues.SourceEntityID == constants.IIFLID && dbObj.SDKVersion != constants.SDKVersionWeb && !strings.HasPrefix(dbObj.SDKVersion, "hybrid") {
		onlyEkyc = true
	}
	occupationType, err := users.GetDynamicUserInfoOccupation(getDetailsValues.UserID)
	if err != nil {
		logger.WithUser("error while getting occupation type")
	}
	if dbObj.LenderID == "" {
		dbObj.LenderID = preselectedlender.Get(getDetailsValues.UserID)
	}
	docDataMap := GetDocDataMap(dbDocs, getDetailsValues.SourceEntityID, alreadyUploaded, onlyEkyc, digilockerDone, false)
	flowID := GetKYCFlowID(dbObj.SourceEntityID, dbObj.UserID)
	docCats := getDocCategories(getDetailsValues.SourceEntityID, dbObj.LenderID, flowID, occupationType)

	_, isConsumer, _ := journey.GetLoanType(getDetailsValues.SourceEntityID)
	description := "Upload Address Proof, PAN Card & verify with Selfie"

	if !journey.ShowPANInKYC(getDetailsValues.SourceEntityID) {
		description = "Upload Address Proof and verify with Selfie"
	}

	plWithBusiness, err := isPLWithBusiness(getDetailsValues.SourceEntityID, dbObj.LenderID, getDetailsValues.LoanApplicationID, occupationType)
	if err != nil {
		logger.WithLoanApplication(getDetailsValues.LoanApplicationID).Errorln(err)
		return resData, "", err
	}
	if !isConsumer || plWithBusiness {
		description = "Upload KYC documents and business proof"
		docCats, err = appendDocCatsBusinessDetails(docCats, dbObj.UserID, dbObj.SourceEntityID, hideBusinessRegistrationProof)
		if err != nil {
			logger.WithLoanApplication(getDetailsValues.LoanApplicationID).Errorln(err)
			return resData, "", err
		}
	}
	documents := GenerateDocumentsFromCats(docCats, docStatusMap, digilockerDone, docDataMap)

	consentText := getConsentText(getDetailsValues.EntityType, getDetailsValues.SourceEntityID, dbObj.LenderID)

	disableAddressPhoto := journey.DisableAddressPhoto(getDetailsValues.SourceEntityID)

	if onlyEkyc && getDetailsValues.EntityType == constants.EntityTypeCustomer {
		for index, doc := range documents {
			if doc.DocumentType == constants.DocTypeAddressProof {
				if len(doc.DocumentData) == 0 {
					documents[index].DocumentData = []structs.DocumentDataStruct{}
				}
				break
			}
		}
	}
	submitButton := "Submit"
	if digilockerDone || (loanStatus == "KYC_REJECTED" && getDetailsValues.EntityType == constants.EntityTypeDashboardUser) {
		submitButton = "ReSubmit"
	}

	showManualReview := rejectCount > 2 && loanStatus == "KYC_REJECTED"
	resData = map[string]interface{}{
		"title":               "KYC Documents",
		"description":         description,
		"documents":           documents,
		"showManualReview":    showManualReview,
		"consentText":         consentText,
		"disableAddressPhoto": disableAddressPhoto,
		"showKYCSubmit":       submitButton,
	}
	return resData, "", nil

}

func GetDetailsCurrentAddress(getDetailsValues GetDetailsStruct) (map[string]interface{}, string, error) {
	description := "Upload Current Address Proof and verify address "
	var rejectCount int
	resData := make(map[string]interface{})
	dbObj, err := getKYCLoanDetails(getDetailsValues.LoanApplicationID, "", getDetailsValues.SourceEntityID, "")
	if err != nil {
		log.Println(err)
		return resData, "Loan Not Found", nil
	}

	docStatusMap := make(map[string]DocStatusStruct)
	alreadyUploaded := false
	digilockerDone := true
	loanStatus := constants.GetLoanStatusText(dbObj.Status, dbObj.KYCStatus)
	if loanStatus == "KYC_PROCESSING" || loanStatus == "KYC_REJECTED" {
		// fetch data from DB
		docStatusMap, alreadyUploaded, digilockerDone, err = getLoanKYCDetails(getDetailsValues.LoanApplicationID)
		if err != nil {
			logger.WithLoanApplication(getDetailsValues.LoanApplicationID).Errorln(err)
			return resData, "", err
		}
	}
	// get reject count
	query := `select count(*) from loan_kyc_details where loan_id = $1 and status in ($2, $3)`
	err = database.Get(&rejectCount, query, getDetailsValues.LoanApplicationID, constants.KYCDocStatusInactive, constants.KYCDocStatusRejected)
	if err != nil {
		logger.WithLoanApplication(getDetailsValues.LoanApplicationID).Errorln(err)
		return resData, "", err
	}

	dbDocs, err := getAvailableCurrenAddressDocs()
	if err != nil {
		logger.WithLoanApplication(getDetailsValues.LoanApplicationID).Errorln(err)
		return resData, "", err
	}
	onlyEkyc := false
	if getDetailsValues.EntityType == constants.EntityTypeCustomer && getDetailsValues.SourceEntityID == constants.IIFLID && dbObj.SDKVersion != constants.SDKVersionWeb && !strings.HasPrefix(dbObj.SDKVersion, "hybrid") {
		onlyEkyc = true
	}
	docDataMap := GetDocDataMap(dbDocs, getDetailsValues.SourceEntityID, alreadyUploaded, onlyEkyc, digilockerDone, false)
	docCats := []structs.DocumentCategory{{
		DocumentType:  constants.DocTypeCurrentAddressProof,
		DocumentTitle: "Current Address Proof",
	}}

	documents := GenerateDocumentsFromCats(docCats, docStatusMap, digilockerDone, docDataMap)

	consentText := getConsentText(getDetailsValues.EntityType, getDetailsValues.SourceEntityID, dbObj.LenderID)

	disableAddressPhoto := journey.DisableAddressPhoto(getDetailsValues.SourceEntityID)

	if onlyEkyc && getDetailsValues.EntityType == constants.EntityTypeCustomer {
		for index, doc := range documents {
			if doc.DocumentType == constants.DocTypeAddressProof {
				if len(doc.DocumentData) == 0 {
					documents[index].DocumentData = []structs.DocumentDataStruct{}
				}
				break
			}
		}
	}
	submitButton := "Submit"

	resData = map[string]interface{}{
		"title":               "Current Address",
		"description":         description,
		"documents":           documents,
		"showManualReview":    rejectCount > 2,
		"consentText":         consentText,
		"disableAddressPhoto": disableAddressPhoto,
		"showKYCSubmit":       submitButton,
	}
	return resData, "", nil

}

// GetSelfie returns the s3 presigned URL for a loan application
// returns blank string if no selfie uploaded yet
func GetSelfie(loanApplicationID string) string {
	var selfiePath string
	query := `select path from loan_kyc_details l join media m on m.media_id = l.media_id
					where loan_id = $1 and doc_type = $2
			order by l.created_at desc limit 1`
	err := database.Get(&selfiePath, query, loanApplicationID, constants.DocTypePhoto)
	if err != nil {
		logger.WithLoanApplication(loanApplicationID).Errorln(err)
	}
	if selfiePath != "" {
		selfiePath = s3.GetPresignedURLS3(selfiePath, 300)
	}
	return selfiePath
}

// ReplaceKYCDoc replaces a document with another media, its used for dashboard
// takes loan application id, source entity id (optional), lender id (optional),
// and input fields in form of a struct
// returns lender id, error string and error object
func ReplaceKYCDoc(loanApplicationID string, sourceEntityID string, lenderID string, input ReplaceInputStruct) (string, string, error) {
	// get required fields
	params := map[string]interface{}{
		"loanid": loanApplicationID,
	}
	queryPart := ""
	if sourceEntityID != "" {
		params["sid"] = sourceEntityID
		queryPart += " and la.source_entity_id = :sid"
	}
	if lenderID != "" {
		params["lid"] = lenderID
		queryPart += " and la.lender_id = :lid"
	}

	type loanStruct struct {
		LenderID    string
		UserID      string
		SignMediaID string
	}
	var loanObj loanStruct

	query := `SELECT
					user_id as userid,
					coalesce(lender_id::::TEXT, '') as lenderid,
					coalesce(sign_media_id::::TEXT, '') as signmediaid
		from loan_application la where loan_application_id = :loanid` + queryPart
	namedQuery, err := database.PrepareNamed(query)
	if err != nil {
		logger.WithLoanApplication(loanApplicationID).Errorln(err)
		return "", "", err
	}
	err = namedQuery.Get(&loanObj, params)
	if err != nil {
		logger.WithLoanApplication(loanApplicationID).Errorln(err)
		return "", "loan not found", nil
	}

	var docType string
	query = `SELECT doc_type from loan_kyc_details WHERE loan_kyc_details_id = $1 AND loan_id = $2`
	err = database.Get(&docType, query, input.LoanKYCDetailsID, loanApplicationID)
	if err != nil {
		logger.WithLoanApplication(loanApplicationID).Errorln(err)
		return "", "loan kyc details not found", nil
	}
	if docType == constants.DocTypePhoto && loanObj.SignMediaID != "" {
		query = `update loan_application
					set sign_media_id = $1,
					updated_at = NOW()
					where loan_application_id = $2`
		_, err = database.Exec(query, input.MediaID, loanApplicationID)
		if err != nil {
			logger.WithLoanApplication(loanApplicationID).Errorln(err)
			return "", "", err
		}
	}
	columnName := "media_id"
	if input.IsBack {
		columnName = "back_media_id"
	}
	query = fmt.Sprintf(`update loan_kyc_details
	set %s = $1,
	updated_at = NOW()
	where loan_kyc_details_id = $2`, columnName)
	_, err = database.Exec(query, input.MediaID, input.LoanKYCDetailsID)
	if err != nil {
		logger.WithLoanApplication(loanApplicationID).Errorln(err)
		return "", "", err
	}
	// TODO: add length check for changes
	dateTimeNowString := general.GetTimeStampString()
	activityObj := activity.ActivityEvent{
		UserID:            loanObj.UserID,
		SourceEntityID:    sourceEntityID,
		LoanApplicationID: loanApplicationID,
		EntityType:        input.EntityType,
		EntityRef:         input.EntityRef,
		EventType:         constants.ActivityKYCDocChanged,
		Description:       fmt.Sprintf(`{ "docType": "%s", "changesMade": "%s" }`, docType, strings.Join(input.Changes, ", ")),
	}
	activity.RegisterEvent(&activityObj, dateTimeNowString)

	return loanObj.LenderID, "", nil
}

// RejectSingleDoc rejects a single document in KYC
// returns error string and error object if any
func RejectSingleDoc(loanApplicationID string, sourceEntityID string,
	loanKYCDetailsID string, rejectionReason string, entityType string, entityRef string) (string, error) {

	type dbResStruct struct {
		Status int
		UserID string
	}
	obj := dbResStruct{}
	var query = `SELECT k.status, l.user_id as userid
		FROM loan_kyc_details k JOIN loan_application l ON k.loan_id = l.loan_application_id
		WHERE k.loan_kyc_details_id = $1
			and k.loan_id = $2
			and l.source_entity_id = $3`
	err := database.Get(&obj, query, loanKYCDetailsID, loanApplicationID, sourceEntityID)
	if err != nil {
		if err == sql.ErrNoRows {
			log.Println("no kyc doc found")
			return "no kyc doc found", nil
		} else {
			logger.WithLoanApplication(loanApplicationID).Errorln(err)
			return "", err
		}
	}

	if obj.Status != constants.KYCDocStatusUploaded {
		log.Println("kycDoc is not pending")
		return "kycDoc is not pending", nil
	}

	tx, _ := database.Beginx()
	var updateQuery = `update loan_kyc_details set status = $1, updated_at = current_timestamp,
							rejection_reason = $2
						where loan_kyc_details_id = $3;`

	_, err = tx.Exec(updateQuery, constants.KYCDocStatusRejected, rejectionReason, loanKYCDetailsID)
	if err != nil {
		logger.WithLoanApplication(loanApplicationID).Errorln(err)
		tx.Rollback()
		return "", err
	}

	err = loanapplication.UpdateKYCStatus(tx, loanApplicationID, constants.LoanKYCStatusDocRejected)
	if err != nil {
		logger.WithLoanApplication(loanApplicationID).Errorln(err)
		tx.Rollback()
		return "", err
	}

	//check if manual review reached from selfie module
	selfieCount := kycmodules.GetAttemptCount(loanApplicationID, constants.KYCTypeSelfie, "")
	kycServiceCount := kycmodules.GetAttemptCount(loanApplicationID, constants.KYCTypeExternal, "")

	nextModule := constants.KYCTypeManual
	if kycServiceCount >= 1 {
		nextModule = constants.KYCTypeExternal
	} else if selfieCount >= 1 {
		nextModule = constants.KYCTypeSelfie
	}

	err = kycmodules.Insert(tx, loanApplicationID, nextModule, "", constants.KYCModuleStatusFailure, rejectionReason)
	if err != nil {
		logger.WithLoanApplication(loanApplicationID).Errorln(err)
		tx.Rollback()
		return "", err
	}

	queryUm := `update user_module_mapping set module_status = 3, updated_at = current_timestamp where loan_application_id = $1 and user_id = $2 and status = 1 and module_name = $3`
	_, err = tx.Exec(queryUm, loanApplicationID, obj.UserID, "KYC")
	if err != nil {
		logger.WithLoanApplication(loanApplicationID).Errorln(err)
		tx.Rollback()
		return "", err
	}
	tx.Commit()

	dateTimeNowString := general.GetTimeStampString()
	go func() {
		activityObj := activity.ActivityEvent{
			UserID:            obj.UserID,
			SourceEntityID:    sourceEntityID,
			LoanApplicationID: loanApplicationID,
			EntityType:        entityType,
			EntityRef:         entityRef,
			EventType:         constants.ActivityKYCDocRejected,
			Description:       rejectionReason,
		}
		activity.RegisterEvent(&activityObj, dateTimeNowString)
	}()

	return "", nil
}

// UpdateStatusPostAutoApprove updates relevant tables based on KYC engine response status
func UpdateStatusPostAutoApprove(tx *sqlx.Tx, userID, loanApplicationID, sourceEntityID string, referenceID string, status int, failReason string) error {
	err := digilockerkyc.UpdateStatus(context.TODO(), tx, referenceID, status, nil)
	if err != nil {
		logger.WithLoanApplication(loanApplicationID).Error(err)
		return err
	}
	if status == digilockerkyc.StatusCompleted {
		fraudcheckutils.AadharFraudCheck(userID, "", "", "digilocker_completed")
		if err != nil {
			logger.WithLoanApplication(loanApplicationID).Error(err)
			return err
		}
	}
	moduleStatus := ""
	switch status {
	case digilockerkyc.StatusCompleted:
		moduleStatus = constants.KYCModuleStatusSuccess
	case digilockerkyc.StatusVerificationRejected:
		moduleStatus = constants.KYCModuleStatusFailure
		uldObj, err := userloandetails.GetAddress(loanApplicationID)
		if err != nil {
			logger.WithLoanApplication(loanApplicationID).Error(err)
			return err
		}
		if uldObj.CurrentAddress == "" {
			query := "update user_loan_details set current_address = '{}', updated_at = now() where loan_application_id = $1"
			_, err = tx.Exec(query, loanApplicationID)
			if err != nil {
				logger.WithLoanApplication(loanApplicationID).Error(err)
				return err
			}
		}

		err = moduleutils.UpdateKYC(tx, constants.ModuleKYCRejected, userID, loanApplicationID, sourceEntityID)
		if err != nil {
			logger.WithLoanApplication(loanApplicationID).Errorln(err)
			return err
		}
	default:
		return errors.New("invalid digilocker status")
	}
	err = kycmodules.Insert(tx, loanApplicationID, constants.KYCTypeWEB, constants.KYCSubModuleDL, moduleStatus, failReason)
	if err != nil {
		logger.WithLoanApplication(loanApplicationID).Errorln(err)
		return err
	}
	return nil
}

// GetLatestAadhaarRespDigilocker gets the latest aadhaar response from external service for the referenceID
func GetLatestAadhaarRespDigilocker(referenceID string) (hyperverge.HVeAadhaarDetailsAPIResp, error) {
	var responseData string
	var hvAadhaarResp hyperverge.HVeAadhaarDetailsAPIResp
	query := `select response_data from external_service where service_name=$1
				and request_data::jsonb->>'referenceId'=$2 and success=1
				order by created_at desc limit 1 ;`
	err := database.Get(&responseData, query, constants.ExternalServiceHvAadhaarDigilocker, referenceID)
	if err != nil {
		log.Error(err)
		return hvAadhaarResp, err
	}
	err = json.Unmarshal([]byte(responseData), &hvAadhaarResp)
	if err != nil {
		log.Error(err)
		return hvAadhaarResp, err
	}
	return hvAadhaarResp, nil
}

// GetLatestPANRespDigilocker gets the latest pan response(extended pan api), external service id
// and error if any from external service for a given userID
func GetLatestPANRespDigilocker(userID string) (panDetailedAPI panUtils.PANDetailedAPI, external_service_id string, erro error) {
	var panResp panUtils.PANDetailedAPI
	type extStruct struct {
		ResponseData string `db:"response_data"`
		ExtID        string `db:"external_service_id"`
	}
	var resp extStruct
	query := `select response_data, external_service_id from external_service where created_by = $1 and service_name = $2 and status = 1 order by created_at desc limit 1`
	err := database.Get(&resp, query, userID, constants.ExternalServiceHvPANDetails)
	if err != nil {
		logger.WithUser(userID).Errorln(err)
		return panResp, "", err
	}
	err = json.Unmarshal([]byte(resp.ResponseData), &panResp)
	if err != nil {
		logger.WithUser(userID).Errorln(err)
		return panResp, resp.ExtID, err
	}
	return panResp, resp.ExtID, nil
}

// GetAadhaarXMLMedia gets the aadhaarXML path from media table
func GetAadhaarXMLMedia(userID string) (string, string, error) {
	type aadhaarXMLMedia struct {
		Path    string
		MediaID string
	}
	var aadhaarXMLMediaObj aadhaarXMLMedia
	query := `select path, media_id as mediaid from media where media_type=$1 and user_id=$2 and status=1 order by created_at desc limit 1`
	err := database.Get(&aadhaarXMLMediaObj, query, constants.MediaTypeDigilockerAadhaarXML, userID)
	if err != nil {
		logger.WithUser(userID).Errorln(err)
		return aadhaarXMLMediaObj.Path, aadhaarXMLMediaObj.MediaID, err
	}
	return aadhaarXMLMediaObj.Path, aadhaarXMLMediaObj.MediaID, nil
}

// GetAddressfromDigilockerXML gets the XML data from aadhaarXML path and converts that to address json.
func GetAddressfromDigilockerXML(aadhaarXMLPath string, sourceEntityID string) ([]byte, hyperverge.Certificate, error) {
	type aadhaarDataStruct struct {
		City     string `json:"city"`
		Line1    string `json:"line1"`
		Line2    string `json:"line2"`
		Landmark string `json:"landmark"`
		State    string `json:"state"`
		Pincode  string `json:"pincode"`
	}
	kycData, err1 := hyperverge.GetXMLStructFromS3(aadhaarXMLPath)
	if err1 != nil {
		log.Println(err1)
		return []byte{}, kycData, err1
	}
	var addressObj aadhaarDataStruct
	line1 := []string{general.CleanAddressLine(kycData.CertificateData.KycRes.UIDData.Poa.House), general.CleanAddressLine(kycData.CertificateData.KycRes.UIDData.Poa.Street), general.CleanAddressLine(fmt.Sprintf("landmark: %s", kycData.CertificateData.KycRes.UIDData.Poa.Lm))}
	line2 := []string{general.CleanAddressLine(kycData.CertificateData.KycRes.UIDData.Poa.Loc), general.CleanAddressLine(kycData.CertificateData.KycRes.UIDData.Poa.Subdist), general.CleanAddressLine(kycData.CertificateData.KycRes.UIDData.Poa.Po), general.CleanAddressLine(kycData.CertificateData.KycRes.UIDData.Poa.Dist)}
	var landmark []string
	if journey.IsIIFLBLSourcing(sourceEntityID) {
		line1 = []string{general.CleanAddressLine(kycData.CertificateData.KycRes.UIDData.Poa.House), general.CleanAddressLine(kycData.CertificateData.KycRes.UIDData.Poa.Street)}
		landmark = []string{general.CleanAddressLine(fmt.Sprintf("landmark: %s", kycData.CertificateData.KycRes.UIDData.Poa.Lm))}
		landmark = general.RemoveEmptyElements(landmark)
		addressObj.Landmark = strings.Join(landmark, ", ")
	}

	line1 = general.RemoveEmptyElements(line1)
	line2 = general.RemoveEmptyElements(line2)

	addressObj.City = kycData.CertificateData.KycRes.UIDData.Poa.Vtc
	addressObj.Line1 = strings.Join(line1, ", ")
	addressObj.Line2 = strings.Join(line2, ", ")
	addressObj.State = kycData.CertificateData.KycRes.UIDData.Poa.State
	addressObj.Pincode = kycData.CertificateData.KycRes.UIDData.Poa.Pc

	addressJSON, err := json.Marshal(addressObj)
	if err != nil {
		log.Println(err)
		return []byte{}, kycData, err
	}
	return addressJSON, kycData, nil
}

// CopyPermanentToCurrentAddress copies permanent address to current address if current and permanent address are same is true
// in case of error, if transaction is passed rollback needs to be done at caller end
func CopyPermanentToCurrentAddress(tx *sql.Tx, loanApplicationID string) error {
	// copy permanent address to current address
	var err error
	query := `update user_loan_details set current_address = permanent_address , updated_at = NOW() where loan_application_id = $1`
	if tx == nil {
		_, err = database.Exec(query, loanApplicationID)
	} else {
		_, err = tx.Exec(query, loanApplicationID)
	}
	if err != nil {
		logger.WithLoanApplication(loanApplicationID).Errorf("Error while updating current address: %s", err.Error())
		return err
	}
	return nil
}

// ValidateAddress validate address line1, line2, city, state and pincode and returns the cleaned address, error if any
func ValidateAddress(line1 string, line2 string, city string, state string, pincode string) (string, string, string, string, string, error) {

	line1 = general.CleanAddressLine(line1)
	if line1 == "" {
		return "", "", "", "", "", errors.New("invalid address line 1")
	}
	line2 = general.CleanAddressLine(line2)
	if line2 == "" {
		return "", "", "", "", "", errors.New("invalid address line 2")
	}
	if !general.ValidatePincode(pincode) {
		return "", "", "", "", "", errors.New("invalid pincode")
	}
	city = general.CleanAddressLine(city)
	if city == "" {
		return "", "", "", "", "", errors.New("invalid city")
	}
	state = general.CleanAddressLine(state)
	if state == "" {
		return "", "", "", "", "", errors.New("invalid state")
	}
	return line1, line2, city, state, pincode, nil
}

// GetKYCFlowID returns the kyc flow id for the user
func GetKYCFlowID(sourceEntityID string, userID string) (flowID int) {
	var flowSet bool
	var err error

	if journey.IsKYCServiceWorkflowEnabled(userID) {
		flowID = constants.KYCFlowKYCServiceWorkflow
		flowSet = true
	} else if journey.IsKYCServiceFlowEnabled(sourceEntityID, userID) {
		flowID = constants.KYCFlowKYCService
		flowSet = true
	}

	if !flowSet {
		flowID, err = users.GetKYCFlow(userID)
		if err != nil {
			logger.WithUser(userID).Errorln(err)
			flowID = constants.KYCFLowManual
			flowSet = true
		}
	}
	if flowID != constants.KYCFlowUnassigned && !flowSet {
		return flowID
	}
	if !flowSet {
		resp := journey.IIFLAgg(sourceEntityID, false)
		tempSourceEntityID := sourceEntityID
		if resp.IsAgg && !journey.HasCustomKYCFlow(sourceEntityID) {
			switch resp.LoanType {
			case constants.LoanTypePersonalLoan:
				tempSourceEntityID = constants.IIFLID
			case constants.LoanTypeBusinessLoan:
				tempSourceEntityID = constants.IIFLBLID
			}
		}
		flowID, err = sourceentity.GetKYCFlow(tempSourceEntityID)
		if err != nil {
			logger.WithUser(userID).Errorln(err)
			flowID = constants.KYCFLowManual
		}
	}
	// update the flow id in user table
	err = users.UpdateKYCFlow(userID, flowID)
	if err != nil {
		logger.WithUser(userID).Errorln(err)
		flowID = constants.KYCFLowManual
	}
	return flowID
}

// MoveToAddressQueue moves a case to address verification queue based on a pincode logic
// returns true, if moved to address queue, else false. Also returns as second object - error (if any)
// func MoveToAddressQueue(sourceEntityID string, userID string, loanApplicationID string string) (bool, error) {
// 	lenderID := underwriting.GetLenderID(sourceEntityID, userID, "")
// 	isServiceable, rejectReason := underwriting.CheckPincodeServiceability(loanApplicationID, sourceEntityID, lenderID)
// 	if isServiceable && rejectReason == constants.ReasonAddressVerificationQueue {
// 		// move to address queue
// 		tx, _ := database.Beginx()
// 		err := loanapplication.UpdateKYCStatus(tx, loanApplicationID, constants.LoanKYCStatusAddressVerificationPending)
// 		if err != nil {
// 			logger.WithUser(userID).Error(err)
// 			tx.Rollback()
// 			return false, err
// 		}
// 		err = kycmodules.Insert(tx, loanApplicationID, constants.KYCTypeWait, "", constants.KYCModuleStatusStart, "address verification queue")
// 		if err != nil {
// 			logger.WithUser(userID).Error(err)
// 			tx.Rollback()
// 			return false, err
// 		}
// 		err = moduleutils.UpdateKYC(tx, constants.ModuleKYCSuccess, userID, programID, loanApplicationID, sourceEntityID)
// 		if err != nil {
// 			logger.WithUser(userID).Error(err)
// 			tx.Rollback()
// 			return false, err
// 		}
// 		err = tx.Commit()
// 		if err != nil {
// 			logger.WithUser(userID).Error(err)
// 			return false, err
// 		}

// 		dateTimeNowString := general.GetTimeStampString()
// 		activityObj := activity.ActivityEvent{
// 			UserID:            userID,
// 			SourceEntityID:    sourceEntityID,
// 			LoanApplicationID: loanApplicationID,
// 			EntityType:        constants.EntityTypeSystem,
// 			EntityRef:         "",
// 			EventType:         constants.ActivityKYCAddressVerificationQueue,
// 			Description:       "",
// 		}
// 		activity.RegisterEvent(&activityObj, dateTimeNowString)
// 		return true, nil
// 	}
// 	return false, nil
// }

// LockManualApproval locks loan application id for kyc manual approval, returns an error string (if any)
func LockManualApproval(loanApplicationID string) string {
	key := getManualApprovalKey(loanApplicationID)
	customErr := lockutils.LockWithTimeout(key, 5*time.Minute)
	if customErr != nil {
		logger.WithLoanApplication(loanApplicationID).Errorln(customErr)
		switch customErr.HTTPCode {
		case http.StatusConflict:
			return "a previous KYC check/approval process is in progress, please wait for some time"
		default:
			errorHandler.ReportToSentryWithoutRequest(customErr)
			return "we are looking into it, please wait for some time"
		}
	}
	return ""
}

// UnlockManualApproval unlocks if a lock was present for loan id for manual approval
func UnlockManualApproval(loanApplicationID string) {
	lockutils.UnLock(getManualApprovalKey(loanApplicationID), nil)
}

// GetUnmaskedAddressLine replaces line1 & line2 with address object line1 & line2 if the same is masked
func GetUnmaskedAddressLine(line1, line2, address string) (string, string, error) {
	permanentAddressObj, _ := commonutils.ParseAddress(address)
	if permanentAddressObj.Line1 == "" && strings.HasPrefix(line1, mask.MaskedLine) {
		return "", "", errors.New("please add line 1")
	}
	if permanentAddressObj.Line2 == "" && strings.HasPrefix(line2, mask.MaskedLine) {
		return "", "", errors.New("please add line 2")
	}
	if permanentAddressObj.Line1 != "" && line1 == mask.MaskedLine {
		line1 = permanentAddressObj.Line1
	}
	if permanentAddressObj.Line2 != "" && line2 == mask.MaskedLine {
		line2 = permanentAddressObj.Line2
	}
	return line1, line2, nil
}

func SendKYCEmail(user users.User, loanApplicationID string, isKYCSuccess bool, docType string, rejectionReason string) error {

	dsaDetails, err := dsautils.GetDSADetails(user.DSAID, true)
	if err != nil {
		logger.WithLoanApplication(loanApplicationID).Errorln(err)
		return err
	}

	var subDSADetails dsa.Agent
	if dsaDetails.OwnerType == constants.OwnerTypeDSA {
		subDSADetails = dsaDetails
		dsaDetails, err = dsautils.GetMasterDSA(subDSADetails.OwnerID)
		if err != nil {
			logger.WithLoanApplication(loanApplicationID).Errorln(err)
			return err
		}
	}

	// Create a map to get email subject and body if any other lender/platform comes with same requirement
	var emailBody, emailSubject string
	if isKYCSuccess {
		// This can be moved to a separate func or map if needed
		var emailBodyHTML, emailSubjectHTML string
		if journey.IsIIFLSourcing(user.SourceEntityID) {
			emailBodyHTML = emaillib.IIFLKYCSuccessHTML
			emailSubjectHTML = emaillib.IIFLKYCSuccessSubject
		}
		emailBody = general.GetStringFromTemplate(emailBodyHTML, map[string]interface{}{
			"Name":              user.Name,
			"Mobile":            user.Mobile,
			"LoanApplicationID": loanApplicationID,
		})
		emailSubject = general.GetStringFromTemplate(emailSubjectHTML, nil)
	} else {
		// This can be moved to a separate func or map if needed
		var emailBodyHTML, emailSubjectHTML string
		if journey.IsIIFLSourcing(user.SourceEntityID) {
			emailBodyHTML = emaillib.IIFLKYCFailureHTML
			emailSubjectHTML = emaillib.IIFLKYCFailureSubject
		}
		emailBody = general.GetStringFromTemplate(emailBodyHTML, map[string]interface{}{
			"Name":              user.Name,
			"Mobile":            user.Mobile,
			"LoanApplicationID": loanApplicationID,
			"DocType":           docType,
			"RejectionReason":   rejectionReason,
		})
		emailSubject = general.GetStringFromTemplate(emailSubjectHTML, nil)
	}

	if dsaDetails.Email != "" {
		emaillib.SendMail([]string{dsaDetails.Email}, []string{user.Name}, emailSubject, emailBody, []emaillib.EmailAttachment{}, false)
	}
	if subDSADetails.Email != "" {
		emaillib.SendMail([]string{subDSADetails.Email}, []string{user.Name}, emailSubject, emailBody, []emaillib.EmailAttachment{}, false)
	}

	return nil
}

type PAN struct {
	Err   error
	ExtID string
	Resp  panUtils.PANDetailedAPI
}

type Aadhaar struct {
	Err   error
	ExtID string
	Resp  hyperverge.HVeAadhaarDetailsAPIResp
}

// GetPANDetails fetches pan details from hyperverge api
func GetPANDetails(userID, sourceEntityID string) PAN {

	defer errorHandler.RecoveryNoResponse()
	var panEntered string

	var query string
	var err error

	query = `SELECT pan from users where user_id = $1;`
	err = database.Get(&panEntered, query, userID)
	if err != nil {
		logger.WithUser(userID).Errorln(err)
		return PAN{Err: err}
	}

	callPANExtendedHV := true
	var extServiceIDPAN string
	var panDetailsResp panUtils.PANDetailedAPI
	var respDataPAN string
	isNSDLFlow := false

	if journey.ReusePANExtentedResponseKYC(sourceEntityID) {
		extServiceIDPAN, err = pandetails.GetExtIDPAN(userID, panEntered)
		if err != nil && err != sql.ErrNoRows {
			logger.WithUser(userID).Error(err)
			return PAN{Err: err}
		} else if err == nil {
			callPANExtendedHV = false
		}

		_, err = externalservice.GetResponseData(extServiceIDPAN, "octopus-invoke-nsdl-pan-verify-poonawalla")
		if err != nil && err != sql.ErrNoRows {
			logger.WithUser(userID).Error(err)
			return PAN{Err: err}
		} else if err == nil {
			isNSDLFlow = true
		}
	}

	if !callPANExtendedHV && extServiceIDPAN != "" {
		if !isNSDLFlow {
			respDataPAN, err = externalservice.GetResponseData(extServiceIDPAN, "detailed-pan-hyperverge")
			if err != nil && err != sql.ErrNoRows {
				logger.WithUser(userID).Error(err)
				return PAN{Err: err}
			}
		} else {
			pflPANDetails, err := pandetails.GetLastVerifiedWithPAN(userID, panEntered)
			if err != nil {
				logger.WithUser(userID).Error(err)
				return PAN{Err: err}
			}
			panDetailsResp.Result.Data.PanData.PAN = pflPANDetails.PAN
			panDetailsResp.Result.Data.PanData.Name = pflPANDetails.Name
			panDetailsResp.Result.Data.PanData.FirstName = pflPANDetails.FirstName
			panDetailsResp.Result.Data.PanData.MiddleName = pflPANDetails.MiddleName
			panDetailsResp.Result.Data.PanData.LastName = pflPANDetails.LastName
			panDetailsResp.Result.Data.PanData.DateOfBirth = pflPANDetails.DOB
			panDetailsResp.Result.Data.PanData.Gender = constants.GenderNumToStr[pflPANDetails.Gender]
		}
	}

	if callPANExtendedHV || respDataPAN == "" && !isNSDLFlow {
		for i := 0; i < 2; i++ {
			panDetailsResp, extServiceIDPAN, err = pannumber.PANExtendedAPI(panEntered, userID, sourceEntityID)
			if err != nil {
				logger.WithUser(userID).Errorln(err)
				if errors.Is(err, fbxerrors.ErrVendorRateLimitExceeded) {
					time.Sleep(1 * time.Second)
				}
			} else {
				break
			}
		}
	} else if !isNSDLFlow {
		err = json.Unmarshal([]byte(respDataPAN), &panDetailsResp)
		if err != nil {
			logger.WithUser(userID).Error(err)
			return PAN{Err: err}
		}
	}

	return PAN{Err: err, ExtID: extServiceIDPAN, Resp: panDetailsResp}
}

// GetAadhaarDetails fetches aadhaar details from hyperverge api
func GetAadhaarDetails(referenceID, userID, sourceEntityID, lenderID string) Aadhaar {
	defer errorHandler.RecoveryNoResponse()
	var aadhaarResp hyperverge.HVeAadhaarDetailsAPIResp
	var extServiceIDAadhaar string
	var err error
	for i := 0; i < 2; i++ {
		aadhaarResp, extServiceIDAadhaar, err = hyperverge.HypervergeAadhaarDetailsAPI(referenceID, userID, sourceEntityID, lenderID)
		if err != nil {
			logger.WithUser(userID).Errorln(err)
			if err.Error() == "rate limit exceeded" {
				time.Sleep(1 * time.Second)
			}
		} else {
			break
		}
	}
	return Aadhaar{Err: err, ExtID: extServiceIDAadhaar, Resp: aadhaarResp}
}

func SaveUserDocumentToLoanKYC(tx *sqlx.Tx, userID, loanApplicationID string, docNames []string) error {
	// Fetch documents
	docs, err := userdocuments.GetDocsByNames(userID, docNames)
	if err != nil {
		err = fmt.Errorf("failed to fetch documents for loanApplicationID: %s, err: %s", loanApplicationID, err)
		logger.WithUser(userID).Errorln(err)
		return err
	}

	var docDetails []loankycdetails.LoanKYCDetailStruct
	for _, doc := range docs {
		docDetails = append(docDetails, loankycdetails.LoanKYCDetailStruct{
			UniqueID:    general.GetUUID(),
			LoanID:      loanApplicationID,
			MediaID:     doc.MediaID,
			DocType:     doc.DocumentType,
			DocumentID:  doc.DocumentID,
			Status:      constants.KYCDocStatusUploaded,
			CreatedBy:   userID,
			BackMediaID: "",
		})
	}

	// Insert KYC documents
	if len(docDetails) > 0 {
		if err = loankycdetails.BulkInsert(tx, docDetails); err != nil {
			err = fmt.Errorf("failed to insert user dosuments to loan KYC documents for loanApplicationID: %s, err: %s", loanApplicationID, err)
			logger.WithUser(userID).Errorln(err)
			return err
		}
	}

	return nil
}

// AttachDynamicAddressToLoan is to be used to attach address from dyanmic info into current_address of user_loan_details
func AttachDynamicAddressToLoan(tx *sqlx.Tx, userID, sourceEntityID string) (err error) {
	// check if specific pre approved journey and move permanent address to current address
	userData := struct {
		DynamicUserInfo string
		SourceEntityID  string
	}{}
	query := `select coalesce(dynamic_user_info,'') as dynamicuserinfo, source_entity_id AS sourceentityid from users where user_id=$1`

	if tx != nil {
		err = tx.Get(&userData, query, userID)
	} else {
		err = database.Get(&userData, query, userID)
	}
	if err != nil {
		logger.WithUser(userID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(fmt.Errorf("[AttachDynamicAddressToLoan] error fetching dynamic user info, userID: %s, err: %s", userID, err.Error()))
		return err
	}

	if len(userData.DynamicUserInfo) > 0 {
		type currentAddressStruct struct {
			Line1   string `json:"line1"`
			Line2   string `json:"line2"`
			City    string `json:"city"`
			Pincode string `json:"pincode"`
			State   string `json:"state"`
			Area    string `json:"area,omitempty"`
		}

		var dynamicUserInfoMap map[string]interface{}
		err = json.Unmarshal([]byte(userData.DynamicUserInfo), &dynamicUserInfoMap)
		if err != nil {
			logger.WithUser(userID).Errorln(err)
			return err
		}

		var currentAddressObj currentAddressStruct

		if journey.IsAPIStackFlow(userID, userData.SourceEntityID) {
			currentAddressObj.Line1, _ = dynamicUserInfoMap["currentAddressLine1"].(string)
			currentAddressObj.Line2, _ = dynamicUserInfoMap["currentAddressLine2"].(string)
			currentAddressObj.City, _ = dynamicUserInfoMap["currentAddressCity"].(string)
			currentAddressObj.Pincode, _ = dynamicUserInfoMap["currentAddressPincode"].(string)
			currentAddressObj.State, _ = dynamicUserInfoMap["currentAddressState"].(string)
		} else {
			currentAddressObj.Line1, _ = dynamicUserInfoMap["permanentAddressLine1"].(string)
			currentAddressObj.Line2, _ = dynamicUserInfoMap["permanentAddressLine2"].(string)
			currentAddressObj.City, _ = dynamicUserInfoMap["permanentAddressCity"].(string)
			currentAddressObj.Pincode, _ = dynamicUserInfoMap["permanentAddressPincode"].(string)
			currentAddressObj.State, _ = dynamicUserInfoMap["permanentAddressState"].(string)
			currentAddressObj.Area, _ = dynamicUserInfoMap["permanentAddressArea"].(string)
		}

		currentAddressBytes, err := json.Marshal(currentAddressObj)
		if err != nil {
			logger.WithUser(userID).Errorln(err)
			return err
		}

		query = `update user_loan_details set current_address = $2, updated_at = now() where user_id = $1`
		if tx != nil {
			_, err = tx.Exec(query, userID, string(currentAddressBytes))
		} else {
			_, err = database.Exec(query, userID, string(currentAddressBytes))
		}
		if err != nil {
			logger.WithUser(userID).Errorln(err)
			errorHandler.ReportToSentryWithoutRequest(fmt.Errorf("unable to update current address for userID: %s", userID))
			return err
		}
	}

	return nil
}
