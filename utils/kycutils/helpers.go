package kycutils

import (
	"context"
	"database/sql"
	"encoding/json"
	"finbox/go-api/common/usersutil"
	"finbox/go-api/constants"
	"finbox/go-api/errorHandler"
	"finbox/go-api/functions/activity"
	"finbox/go-api/functions/commonutils"
	"finbox/go-api/functions/experian"
	"finbox/go-api/functions/journey"
	"finbox/go-api/functions/logger"
	"finbox/go-api/functions/namematch"
	"finbox/go-api/functions/services/ekyc"
	"finbox/go-api/functions/services/ocr"
	"finbox/go-api/functions/structs"
	"finbox/go-api/infra/db"
	"finbox/go-api/infra/s3"
	"finbox/go-api/models/coapplicantkycdetails"
	dashboardModel "finbox/go-api/models/dashboard"
	"finbox/go-api/models/featureflag"
	"finbox/go-api/models/kycserviceprograms"
	"finbox/go-api/models/loanapplication"
	"finbox/go-api/models/multiuserloanrelations"
	"finbox/go-api/models/pandetails"
	"finbox/go-api/models/userjourney"
	"finbox/go-api/models/users"
	"finbox/go-api/utils/general"
	"finbox/go-api/utils/mask"
	"fmt"
	"os"
	"strings"
	"time"

	usersql "finbox/go-api/internal/repository/psql/user"

	fuzzy "github.com/paul-mannino/go-fuzzywuzzy"
	"github.com/pkg/errors"

	"github.com/jmoiron/sqlx"
)

func UpdateNameIdentifierLKD(name string, identifier string, id string) error {
	updateLKDTableQuery := `UPDATE loan_kyc_details set name = $1, identifier = $2 where loan_kyc_details_id = $3`
	_, err := database.Exec(updateLKDTableQuery, name, identifier, id)
	return err
}
func getLoanDetailsDashboard(loanApplicationID string, sourceEntityID string, lenderID string) (LoanStruct, string, error) {
	var loanObj LoanStruct

	params := map[string]interface{}{
		"loanid":   loanApplicationID,
		"ldstatus": constants.LoanDetailsStatusActive,
	}
	queryPart := ""
	if sourceEntityID != "" {
		params["sid"] = sourceEntityID
		queryPart += " and la.source_entity_id = :sid"
	}
	if lenderID != "" {
		params["lid"] = lenderID
		queryPart += " and la.lender_id = :lid"
	}

	query := `SELECT la.user_id as userid, to_char(u.dob, 'YYYY-MM-DD') as dob,
				la.source_entity_id as sourceentityid,
				u.name, u.mobile, coalesce(ld.permanent_address, '') as permanentaddress, 
				coalesce(ld.current_address, '') as currentaddress,
				coalesce(u.dynamic_user_info::::json->'hypervergeFatchMatch'->>'faceMatchScore','') as selfiefacematchscore,
				coalesce(u.dynamic_user_info::::json->'hypervergeLivenessScore'->>'livenessScore','') as selfielivelinessscore,
				coalesce(u.dynamic_user_info::::json->'panPartnerNameMatchScore'->>'score','') as panPartnerNameMatchScore,
				coalesce(u.dynamic_user_info::::json->'aadharNameMatchScore'->>'score','') as aadharNameMatchScore,
				coalesce(u.dynamic_user_info::::json->'panAadhaarNameMatchScore'->>'score','') as panAadhaarNameMatchScore
				from loan_application la join users u on u.user_id = la.user_id
					join user_loan_details ld on la.loan_application_id = ld.loan_application_id
				where la.loan_application_id = :loanid and ld.status = :ldstatus` + queryPart

	namedQuery, err := database.PrepareNamed(query)
	if err != nil {
		log.Println(err)
		return loanObj, "", err
	}
	err = namedQuery.Get(&loanObj, params)
	if err != nil {
		log.Println(err)
		return loanObj, "loan/form not found", nil
	}
	return loanObj, "", nil
}

func getParsedCurrentAddress(address string) string {
	var parsedAddress string
	if address != "" {
		addObj, err := commonutils.ParseAddress(address)
		if err != nil {
			parsedAddress = address
		} else {
			parsedAddress = addObj.Line1 + " " + addObj.Line2 + " " + addObj.City + " " + addObj.Pincode + " " + addObj.State
		}
	}
	return parsedAddress
}

func getEKYCData(loanApplicationID string) map[string]interface{} {
	ekycDetails := make(map[string]interface{})
	ekycDetails["done"] = false
	ekycDetails["imagePath"] = ""
	ekycDetails["referenceID"] = ""
	ekycDetails["data"] = map[string]interface{}{}

	ekycObj, err := ekyc.GetEKYCFullDetails(loanApplicationID)
	if err != nil {
		log.Println(err)
	} else {
		ekycDetails["done"] = true
		ekycDetails["imagePath"] = s3.GetPresignedURLS3(ekycObj.ImagePath, 60)
		ekycDetails["referenceID"] = ekycObj.ReferenceID
		ekycDetails["data"] = ekycObj.Data
	}
	return ekycDetails
}

func addMediaPaths(kycObj []*KYCDataResp) error {
	mediaIDArr := []string{}
	for _, kycSingleObj := range kycObj {
		if kycSingleObj.FrontMediaID != "" {
			mediaIDArr = append(mediaIDArr, kycSingleObj.FrontMediaID)
		}
		if kycSingleObj.BackMediaID != "" {
			mediaIDArr = append(mediaIDArr, kycSingleObj.BackMediaID)
		}
	}
	mediaIDMap := make(map[string]string)
	if len(mediaIDArr) > 0 {
		mObj := []mediaStruct{}
		mediaTempQuery := fmt.Sprintf("select path, media_id as mediaid from media where status = %d and media_id in (?); ", constants.MediaStatusActive)
		mediaQuery, args, err := sqlx.In(mediaTempQuery, mediaIDArr)
		if err != nil {
			log.Println(err)
			return err
		}
		mediaQuery = database.Rebind(mediaQuery)
		err = database.Select(&mObj, mediaQuery, args...)
		if err != nil {
			log.Println(err)
			return err
		}
		for _, mediaObj := range mObj {
			mediaIDMap[mediaObj.MediaID] = s3.GetPresignedURLS3(mediaObj.Path, 60)
		}
	}
	for index := range kycObj {
		if kycObj[index].DocumentName == constants.DocTypeDigilockerAadhaar || kycObj[index].DocumentName == constants.DocTypeAPIPAN {
			kycObj[index].IsEditable = false
		} else {
			kycObj[index].IsEditable = true
		}
		kycObj[index].FrontMediaID = mediaIDMap[kycObj[index].FrontMediaID]
		kycObj[index].BackMediaID = mediaIDMap[kycObj[index].BackMediaID]
		kycObj[index].StatusText = constants.KYCDocMapNumToStr[kycObj[index].Status]
		kycObj[index].ReviewStatusText = coapplicantkycdetails.ReviewStatusToUtilityTextMapping[kycObj[index].ReviewStatus]
	}
	return nil
}

func GetKYCDashboardDocs(loanApplicationID string) ([]LoanKYCDataResp, error) {
	loanKYCObj := []LoanKYCDataResp{}
	kycQuery := `select loan_kyc_details_id as loankycdetailsid, media_id as frontmediaid,
						coalesce(back_media_id ,'') as backmediaid, doc_type as doctype,
						k.status, k.document_id as documentid, document_name as documentname,
						coalesce(rejection_reason, '') as rejectionreason
						from loan_kyc_details k join documents d on d.document_id = k.document_id 
				where loan_id = $1 and k.status in ($2, $3) ; `
	err := database.Select(&loanKYCObj, kycQuery, loanApplicationID, constants.KYCDocStatusUploaded, constants.KYCDocStatusRejected)
	if err != nil {
		log.Println(err)
		return loanKYCObj, err
	}

	kycDocData := make([]*KYCDataResp, 0)
	for i := range loanKYCObj {
		kycDocData = append(kycDocData, &loanKYCObj[i].KYCDataResp)
	}

	//setup media paths now
	err = addMediaPaths(kycDocData)
	if err != nil {
		log.Error(err)
	}
	return loanKYCObj, err
}

func GetCoApplicantKYCDashboardDocs(userID string) ([]CoApplicantKYCData, error) {
	coapplicantKYCObj := []CoApplicantKYCData{}
	kycQuery := `select id as coapplicantKYCDetailsID, 
						media_id as frontmediaid,
						coalesce(back_media_id::TEXT,'') as backmediaid, 
						doc_type as doctype,
						k.status,
						k.document_id as documentid, 
						coalesce(name, '') as documentname,
						coalesce(rejection_reason, '') as rejectionreason,
						coalesce(k.review_status, 0) as reviewstatus
						from co_applicant_kyc_details k join documents d on d.document_id = k.document_id 
				where k.user_id = $1 and k.status in ($2, $3);`
	err := database.Select(&coapplicantKYCObj, kycQuery, userID, constants.KYCDocStatusUploaded, constants.KYCDocStatusRejected)

	if err != nil {
		log.Println(err)
		return coapplicantKYCObj, err
	}

	kycDocData := make([]*KYCDataResp, 0)
	for i := range coapplicantKYCObj {
		kycDocData = append(kycDocData, &coapplicantKYCObj[i].KYCDataResp)
	}

	//setup media paths now
	err = addMediaPaths(kycDocData)
	if err != nil {
		log.Error(err)
	}

	// populate rejection_reason from review_status if its own value is empty,
	// this is needed for blocking documents from green tick for ABFL
	for i := 0; i < len(kycDocData); i++ {
		kycDocData[i].RejectionReason = general.RemoveExtraSpaces(kycDocData[i].RejectionReason)
		if kycDocData[i].RejectionReason == "" {
			kycDocData[i].RejectionReason = kycDocData[i].ReviewStatusText
		}
	}

	return coapplicantKYCObj, err
}

func GetAllCoApplicantKYCData(loanApplicationID string) (resp []CoApplicantKYCDataResp, err error) {
	resp = make([]CoApplicantKYCDataResp, 0)
	ctx := context.Background()
	coApplicantsInfo, err := multiuserloanrelations.GetCoApplicantInfoByLoanID(loanApplicationID, true)
	if err != nil {
		log.Error(err)
		return resp, err
	}

	for _, ca := range coApplicantsInfo {
		kycDocs, err := GetCoApplicantKYCDashboardDocs(ca.UserID)
		if err != nil {
			logger.WithUser(ca.UserID).Error(err)
			continue
		}

		vKycDetails, err := usersql.DBGetLatestVKycDetailsByUserID(ctx, ca.UserID)
		if err != nil {
			log.WithContext(ctx).Errorf("[GetAllCoApplicantKYCData] not able to fetch VKYC detail. err: %v, userID: %v", err, ca.UserID)
		}

		hunterDetails, err := usersql.DBGetUserHunterDetails(ctx, ca.UserID)
		if err != nil {
			logger.WithContext(ctx).Warnf("[GetAllCoApplicantKYCData] Error fetching hunter details for the user:%v,error:%v", ca.UserID, err.Error())
		}

		userDetails, err := users.Get(ca.UserID)
		if err != nil {
			logger.WithContext(ctx).Warnf("[GetAllCoApplicantKYCData] Error fetching user details for the user:%v,error:%v", ca.UserID, err.Error())
		}

		metadata := dashboardModel.UserMetadataResponse{}
		if userDetails.DynamicUserInfoMap != nil {
			if val, ok := userDetails.DynamicUserInfoMap["riskCategory"].(string); ok {
				metadata.RiskCategory = val
			}
			if val, ok := userDetails.DynamicUserInfoMap["riskRating"].(string); ok {
				metadata.RiskRating = val
			}
		}
		panDetails, err := usersql.DBGetPanDetailsByParams(ctx, &usersql.DBGetPanDetailsReq{UserID: ca.UserID})
		if err != nil {
			logger.WithContext(ctx).Warnf("[GetAllCoApplicantKYCData] error getting pan details for the userID:%v Error:%v", ca.UserID, err.Error())
		}

		resp = append(resp, CoApplicantKYCDataResp{
			UserID:  ca.UserID,
			KYCDocs: kycDocs,
			VKycDetails: VKycDetails{
				VKycLastInitiatedAt: vKycDetails.CreatedAt,
				VKycLastInitiatedBy: vKycDetails.CreatedBy,
			},
			HunterDetails: HunterDetails{
				HunterIdentifier:     hunterDetails.HunterIdentifier,
				HunterMatchCount:     hunterDetails.HunterMatchCount,
				HunterDate:           hunterDetails.HunterDate,
				HunterApprovalStatus: hunterDetails.HunterApprovalStatus,
			},
			AadhaarLink: AadhaarLink{
				IsAadhaarLinked: panDetails.IsAadharLinked,
			},
			Metadata: metadata,
		})
	}

	return resp, err
}

func getSourceValues(loanApplicationID string, flowID int, loanObj *LoanStruct, maskType int) map[string][]SourceValue {
	var accountHolderName string
	var experianReportString string
	var panName string

	err := mask.PIIData(loanObj, maskType)
	if err != nil {
		logger.WithLoanApplication(loanApplicationID).Error("error masking PII data", err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return make(map[string][]SourceValue) // return empty map
	}

	permanentAddress := getParsedCurrentAddress(loanObj.PermanentAddress)
	currentAddress := getParsedCurrentAddress(loanObj.CurrentAddress)
	sourceValues := make(map[string][]SourceValue)

	sourceValues["name"] = []SourceValue{{Source: "loan_form", Value: []string{loanObj.Name}}}
	sourceValues["dob"] = []SourceValue{{Source: "loan_form", Value: []string{loanObj.DOB}}}
	sourceValues["mobile"] = []SourceValue{{Source: "loan_form", Value: []string{loanObj.Mobile}}}
	sourceValues["permanentAddress"] = []SourceValue{{Source: "loan_form", Value: []string{permanentAddress}}}
	if journey.IsCurrentAddressKYCFlow(flowID) {
		sourceValues["currentAddress"] = []SourceValue{{Source: "loan_form", Value: []string{currentAddress}}}
	}

	bankQuery := `SELECT ubd.name from user_bank_details ubd
					where user_bank_details_id in (
							select user_bank_details_id from loan_application
							where loan_application_id = $1 )
					and ubd.status = $2 order by ubd.created_at desc limit 1;`
	bureauAPIQuery := `SELECT report_data from experian_reports where user_id = $1 and status = 'completed' order by created_at desc limit 1`

	err = database.Get(&accountHolderName, bankQuery, loanApplicationID, constants.UserBankStatusApproved)
	if err != nil {
		log.Println(err)
	} else {
		sourceValues["name"] = append(sourceValues["name"], SourceValue{Source: "bank_details", Value: []string{accountHolderName}})
	}
	panName, err = pandetails.GetLastVerifiedName(loanObj.UserID)
	if err != nil {
		log.Println(err)
	} else {
		sourceValues["name"] = append(sourceValues["name"], SourceValue{Source: "pan_api", Value: []string{panName}})
	}

	err = database.Get(&experianReportString, bureauAPIQuery, loanObj.UserID)
	if err != nil {
		log.Println(err)
	} else {
		var bureauData experian.INProfileResponse
		jsonError := json.Unmarshal([]byte(experianReportString), &bureauData)
		if jsonError != nil {
			log.Println(jsonError)
		} else {
			// add bureau data here
			// maps to dedup data
			bureauNameMap := make(map[string]bool)
			bureauDOBMap := make(map[string]bool)
			bureauMobileMap := make(map[string]bool)
			bureauAddressMap := make(map[string]bool)
			for _, accountDetails := range bureauData.CAISAccount.CAISAccountDETAILS {
				for _, holderDetails := range accountDetails.CAISHolderDetails {
					bureauName := holderDetails.FirstNameNonNormalized
					if holderDetails.MiddleName1NonNormalized != "" {
						bureauName += " " + holderDetails.MiddleName1NonNormalized
					}
					if holderDetails.MiddleName2NonNormalized != "" {
						bureauName += " " + holderDetails.MiddleName2NonNormalized
					}
					if holderDetails.MiddleName3NonNormalized != "" {
						bureauName += " " + holderDetails.MiddleName3NonNormalized
					}
					if holderDetails.SurnameNonNormalized != "" {
						bureauName += " " + holderDetails.SurnameNonNormalized
					}
					if bureauName != "" {
						bureauNameMap[bureauName] = true
					}
					if holderDetails.DateOfBirth != "" {
						dob, dateErr := time.Parse("********", holderDetails.DateOfBirth)
						if dateErr != nil {
							log.Println(dateErr)
						} else {
							switch maskType {
							case mask.TypeAllowAll:
								bureauDOBMap[dob.Format(constants.DateFormat)] = true
							case mask.TypeMaskCommsAndPAN:
								bureauDOBMap[dob.Format(constants.DateFormat)] = true
							default:
								bureauDOBMap[fmt.Sprintf("%d-XX-XX", dob.Year())] = true // mask
							}
						}
					}
				}

				for index := range accountDetails.CAISHolderPhoneDetails {
					ptr := &accountDetails.CAISHolderPhoneDetails[index]
					err := mask.PIIData(ptr, maskType)
					if err != nil {
						logger.WithLoanApplication(loanApplicationID).Error("error masking PII data", err)
						errorHandler.ReportToSentryWithoutRequest(err)
						return sourceValues
					}

					if accountDetails.CAISHolderPhoneDetails[index].TelephoneNumber != "" {
						bureauMobileMap[accountDetails.CAISHolderPhoneDetails[index].TelephoneNumber] = true
					}
					if accountDetails.CAISHolderPhoneDetails[index].MobileTelephoneNumber != "" {
						bureauMobileMap[accountDetails.CAISHolderPhoneDetails[index].MobileTelephoneNumber] = true
					}
				}

				for index := range accountDetails.CAISHolderAddressDetails {
					ptr := &accountDetails.CAISHolderAddressDetails[index]
					err := mask.PIIData(ptr, maskType)
					if err != nil {
						log.Errorln(err)
						errorHandler.ReportToSentryWithoutRequest(err)
						return sourceValues
					}
					bureauAddress := accountDetails.CAISHolderAddressDetails[index].FirstLineOfAddressNonNormalized
					if accountDetails.CAISHolderAddressDetails[index].SecondLineOfAddressNonNormalized != "" {
						bureauAddress += ", " + accountDetails.CAISHolderAddressDetails[index].SecondLineOfAddressNonNormalized
					}
					if accountDetails.CAISHolderAddressDetails[index].ThirdLineOfAddressNonNormalized != "" {
						bureauAddress += ", " + accountDetails.CAISHolderAddressDetails[index].ThirdLineOfAddressNonNormalized
					}
					if accountDetails.CAISHolderAddressDetails[index].FifthLineOfAddressNonNormalized != "" {
						bureauAddress += ", " + accountDetails.CAISHolderAddressDetails[index].FifthLineOfAddressNonNormalized
					}
					if accountDetails.CAISHolderAddressDetails[index].CityNonNormalized != "" {
						bureauAddress += ", " + accountDetails.CAISHolderAddressDetails[index].CityNonNormalized
					}
					if accountDetails.CAISHolderAddressDetails[index].ZIPPostalCodeNonNormalized != "" {
						bureauAddress += ", " + accountDetails.CAISHolderAddressDetails[index].ZIPPostalCodeNonNormalized
					}
					if bureauAddress != "" {
						bureauAddressMap[bureauAddress] = true
					}
				}
			}
			bureauNames := []string{}
			for key := range bureauNameMap {
				bureauNames = append(bureauNames, key)
			}
			bureauDOBs := []string{}
			for key := range bureauDOBMap {
				bureauDOBs = append(bureauDOBs, key)
			}
			bureauMobiles := []string{}
			for key := range bureauMobileMap {
				bureauMobiles = append(bureauMobiles, key)
			}
			bureauAddresses := []string{}
			for key := range bureauAddressMap {
				bureauAddresses = append(bureauAddresses, key)
			}
			if len(bureauNames) > 0 {
				sourceValues["name"] = append(sourceValues["name"], SourceValue{Source: "bureau_experian", Value: bureauNames})
			}
			if len(bureauDOBs) > 0 {
				sourceValues["dob"] = append(sourceValues["dob"], SourceValue{Source: "bureau_experian", Value: bureauDOBs})
			}
			if len(bureauMobiles) > 0 {
				sourceValues["mobile"] = append(sourceValues["mobile"], SourceValue{Source: "bureau_experian", Value: bureauMobiles})
			}
			if len(bureauAddresses) > 0 {
				sourceValues["address"] = append(sourceValues["address"], SourceValue{Source: "bureau_experian", Value: bureauAddresses})
			}
		}
	}

	type OcrDataStruct struct {
		OCRData string
		DocType string
	}
	var panOCRObjs []OcrDataStruct

	panOCRquery := `SELECT mod.ocr_data as ocrdata, mod.doc_type as doctype
	FROM media_ocr_data mod
	JOIN media m ON mod.media_id = m.media_id
	where m.user_id = $1 and m.status = $2 and mod.doc_type in ($3, $4);`
	err = database.Select(&panOCRObjs, panOCRquery, loanObj.UserID, constants.MediaStatusActive, constants.DocumentNameBusinessPAN, constants.DocumentNamePAN)
	if err == nil {
		for _, data := range panOCRObjs {
			var resp ocr.RespStruct
			err = json.Unmarshal([]byte(data.OCRData), &resp)
			if err != nil {
				logger.WithLoanApplication(loanApplicationID).Error(err)
				continue
			}
			if len(resp.Result) > 0 {
				var date string
				dateObj, err := time.Parse("02/01/2006", general.RemoveExtraSpaces(resp.Result[0].Details.Date.Value))
				if err == nil {
					date = dateObj.Format("2006-01-02")
				} else {
					// try with old pan format
					dateObj, err = time.Parse("02-01-2006", general.RemoveExtraSpaces(resp.Result[0].Details.Date.Value))
					if err == nil {
						date = dateObj.Format("2006-01-02")
					}
				}
				if date != "" {
					sourceValues["dob"] = append(sourceValues["dob"], SourceValue{Source: fmt.Sprintf("OCR_%s", data.DocType), Value: []string{date}})
				}
			}
		}
	}

	// TODO: later add support for other cibil sources as well
	var cibilResp string
	query := `SELECT coalesce(response, '') as response
	from cibil_reports where user_id = $1 and source = $2 and status = $3
	order by created_at desc limit 1`
	err = database.Get(&cibilResp, query, loanObj.UserID, constants.CIBILReportSourceIIFLSoftPull, constants.BureauStatusCompleted)
	if err == nil {
		cibilResp = strings.ReplaceAll(cibilResp, `\"`, `"`)
		var jsonObj structs.CIBILDataStruct
		err = general.UnmarshalXML([]byte(cibilResp), &jsonObj)
		if err == nil {
			sourceValues["dob"] = append(sourceValues["dob"], SourceValue{Source: "bureau_cibil",
				Value: []string{fmt.Sprintf("%s-%s-%s", jsonObj.GetCustomerAssetsSuccess.Asset.TrueLinkCreditReport.Borrower.Birth.BirthDate.Year,
					jsonObj.GetCustomerAssetsSuccess.Asset.TrueLinkCreditReport.Borrower.Birth.BirthDate.Month,
					jsonObj.GetCustomerAssetsSuccess.Asset.TrueLinkCreditReport.Borrower.Birth.BirthDate.Day)}})

			var mobiles []string
			for _, data := range jsonObj.GetCustomerAssetsSuccess.Asset.TrueLinkCreditReport.Borrower.BorrowerTelephone {
				mobiles = append(mobiles, general.GetOnlyNum(data.PhoneNumber.Number.Text))
			}
			if len(mobiles) > 0 {
				sourceValues["mobile"] = append(sourceValues["mobile"], SourceValue{Source: "bureau_cibil", Value: mobiles})
			}

			var addresses []string
			for _, data := range jsonObj.GetCustomerAssetsSuccess.Asset.TrueLinkCreditReport.Borrower.BorrowerAddress {
				addresses = append(addresses, general.CleanAddressLine(fmt.Sprintf("%s %s %s %s", data.CreditAddress.StreetAddress.Text,
					data.CreditAddress.City.Text, data.CreditAddress.Region.Text, general.GetOnlyNum(data.CreditAddress.PostalCode.Text))))
			}
			sourceValues["address"] = append(sourceValues["address"], SourceValue{Source: "bureau_cibil", Value: addresses})
		}
	}

	if !journey.IsABFLBLSourcing(loanObj.SourceEntityID) {
		return sourceValues
	}

	query = `select coalesce(response, '') as response from cibil_reports 
	where user_id=$1 order by created_at desc limit 1`
	err = db.GetDB().Get(&cibilResp, query, loanObj.UserID)
	log.Error(err)
	if err == nil {
		var cibilObj []structs.CIBILReport
		aggrLines := func(lines []string, l string) []string {
			if l == "" {
				return lines
			}
			return append(lines, l)
		}

		err = json.Unmarshal([]byte(cibilResp), &cibilObj)
		log.Error(err)
		if err == nil {
			var addresses []string
			for _, obj := range cibilObj {
				for _, addr := range obj.Addresses {
					// considering on first address
					if addr.Index != "A01" {
						continue
					}
					var lines []string
					lines = aggrLines(lines, addr.Line1)
					lines = aggrLines(lines, addr.Line2)
					lines = aggrLines(lines, addr.Line3)
					lines = aggrLines(lines, addr.Line4)
					lines = aggrLines(lines, addr.Line5)
					lines = aggrLines(lines, addr.PinCode)
					addresses = append(addresses, strings.Join(lines, ", "))
				}
			}

			sourceValues["address"] = append(sourceValues["address"], SourceValue{
				Source: "cibil_address",
				Value:  addresses,
			})
		}
	}

	var dbObj struct {
		BusinessAddress            string `db:"business_address"`
		AdditionalBusinessAddress  string `db:"additional_business_address"`
		AdditionalPermanentAddress string `db:"additional_permanent_address"`
		PermanentAddress           string `db:"permanent_address"`
		CurrentAddress             string `db:"current_address"`
		GSTAddress                 string `db:"gst_address"`
		UANAddress                 string `db:"uan_address"`
		OHPAddress                 string `db:"ohp_address"`
		IsBusinessEdited           bool   `db:"is_edited"`
		GSTIN                      string `db:"gstin"`
		UAN                        string `db:"uan"`
	}

	query = `select coalesce(ub.business_address::text, '{}') as business_address,
    			coalesce(ub.metadata::jsonb->>'additionalBusinessAddress', '{}') as additional_business_address,
				coalesce(uld.permanent_address::text, '{}') as permanent_address,
				coalesce(uld.current_address::text, '{}') as current_address,
				coalesce(ubg.address::text, '') as gst_address,
				coalesce(ubu.address::text, '') as uan_address,
				coalesce(u.dynamic_user_info::jsonb->>'electricityBillAddress', '') as ohp_address,
				coalesce(u.dynamic_user_info::jsonb->>'additionalPermanentAddress', '') as additional_permanent_address,
				coalesce(ub.is_edited, false) as is_edited,
				coalesce(ubg.gstin, '') as gstin,
				coalesce(ubu.uan, '') as uan
			from users u 
			left join user_loan_details uld on uld.user_id = u.user_id
			left join user_business ub on ub.user_id = u.user_id
			left join user_business_gst ubg on ubg.user_id = u.user_id
			left join user_business_uan ubu on ubu.user_id = u.user_id
				where u.user_id = $1 order by uld.created_at desc`

	err = db.GetDB().Get(&dbObj, query, loanObj.UserID)
	if err != nil {
		log.Error(err)
		return sourceValues
	}

	addressSourceValues := make([]SourceValue, 0)

	t := getParsedCurrentAddress(dbObj.CurrentAddress)
	if t != "" {
		addressSourceValues = append(addressSourceValues, SourceValue{
			Source: "current_address",
			Value:  []string{t},
		})
	}
	t = getParsedCurrentAddress(dbObj.PermanentAddress)
	if t != "" {
		addressSourceValues = append(addressSourceValues, SourceValue{
			Source: "permanent_address",
			Value:  []string{t},
		})
	}
	t = getParsedCurrentAddress(dbObj.AdditionalPermanentAddress)
	if t != "" {
		addressSourceValues = append(addressSourceValues, SourceValue{
			Source: "additional_permanent_address",
			Value:  []string{t},
		})
	}
	t = getParsedCurrentAddress(dbObj.BusinessAddress)
	if t != "" {
		if dbObj.IsBusinessEdited {
			s := ""
			switch {
			case dbObj.GSTIN != "":
				s = "GST_address_edited"
			case dbObj.UAN != "":
				s = "udyam_address_edited"
			default:
				s = "business_address_manual_entry"
			}
			addressSourceValues = append(addressSourceValues, SourceValue{
				Source: s,
				Value:  []string{t},
			})
		} else if dbObj.GSTIN == "" && dbObj.UAN == "" {
			addressSourceValues = append(addressSourceValues, SourceValue{
				Source: "business_address_manual_entry",
				Value:  []string{t},
			})
		}
	}
	t = getParsedCurrentAddress(dbObj.AdditionalBusinessAddress)
	if t != "" {
		addressSourceValues = append(addressSourceValues, SourceValue{
			Source: "additional_business_address",
			Value:  []string{t},
		})
	}
	if dbObj.GSTAddress != "" {
		addressSourceValues = append(addressSourceValues, SourceValue{
			Source: "GST_address_API",
			Value:  []string{dbObj.GSTAddress},
		})
	}
	if dbObj.UANAddress != "" {
		addressSourceValues = append(addressSourceValues, SourceValue{
			Source: "udyam_address_API",
			Value:  []string{dbObj.UANAddress},
		})
	}
	if dbObj.OHPAddress != "" {
		addressSourceValues = append(addressSourceValues, SourceValue{
			Source: "OHP_address",
			Value:  []string{dbObj.OHPAddress},
		})
	}
	sourceValues["address"] = append(sourceValues["address"], addressSourceValues...)

	return sourceValues
}

func getKYCLoanDetails(loanApplicationID string, userID string, sourceEntityID string, lenderID string) (KYCLoanDetailsStruct, error) {
	params := map[string]interface{}{
		"loanid": loanApplicationID,
	}
	queryPart := ""
	if sourceEntityID != "" {
		params["sid"] = sourceEntityID
		queryPart += " and l.source_entity_id = :sid"
	}
	if lenderID != "" {
		params["lid"] = lenderID
		queryPart += " and l.lender_id = :lid"
	}
	if userID != "" {
		params["userid"] = userID
		queryPart += " and l.user_id = :userid"
	}
	var dbObj KYCLoanDetailsStruct
	query := `SELECT l.status, coalesce(l.kyc_status, 0) as kycstatus, u.user_id as userid,
	l.loan_type as loantype, coalesce(l.applied_amount, 0) as appliedamount, l.source_entity_id as SourceEntityID,
	coalesce(sdk_version, '') as sdkversion,
	coalesce(l.lender_id::::TEXT, '') as lenderid
	from loan_application l join users u on u.user_id = l.user_id
	where l.loan_application_id= :loanid ` + queryPart
	namedQuery, err := database.PrepareNamed(query)
	if err != nil {
		log.Println(err)
		return dbObj, err
	}
	err = namedQuery.Get(&dbObj, params)
	if err != nil {
		log.Println(err)
		return dbObj, err
	}
	return dbObj, nil

}

func getConsentText(entityType string, sourceEntityID string, lenderID string) string {
	var authoriser string
	pronoun := "my"
	lenderPart := "lender partners"
	if entityType == constants.EntityTypeDashboardUser {
		authoriser = "that I have taken consent from the customer to submit KYC on his/her behalf and for"
		pronoun = "his/her"
	}
	if sourceEntityID == constants.TataNexarcID {
		lenderPart = "the Lender"
	} else if journey.IsHCINFlow(sourceEntityID) {
		lenderPart = "Home Credit India Finance Private Limited"
	} else if lenderID == constants.IIFLID {
		lenderPart = "IIFL Finance Limited"
	}
	consentText := fmt.Sprintf("I hereby authorize %s <b>FinBox</b> to collect and share %s personal information and loan application data with <b>%s</b> to enable them to provide me the best loan offer and other related services.", authoriser, pronoun, lenderPart)

	return consentText
}

func getKYCDocuments(reqDocuments []structs.DocumentUploadStruct, userID string, loanApplicationID string) ([]KYCDocumentStruct, string, []string, string, bool, bool) {
	documents := []KYCDocumentStruct{}
	addressProofFound := false
	panFound := false
	photoMediaID := ""
	mediaIDs := []string{}
	for _, documentObj := range reqDocuments {

		if documentObj.DocumentID == constants.PhotoDocumentID {
			photoMediaID = documentObj.FrontMediaID
		}

		switch documentObj.DocumentType {
		case constants.DocTypeAddressProof:
			addressProofFound = true
		case constants.DocTypePANCard:
			panFound = true
		}

		if documentObj.FrontMediaID == "" {
			return documents, photoMediaID, mediaIDs, "frontMediaID cannot be blank string", addressProofFound, panFound
		}
		if documentObj.DocumentID == "" {
			return documents, photoMediaID, mediaIDs, "documentID cannot be blank string", addressProofFound, panFound
		}
		if documentObj.DocumentType == "" {

			return documents, photoMediaID, mediaIDs, "documentType cannot be blank string", addressProofFound, panFound
		}
		mediaIDs = append(mediaIDs, documentObj.FrontMediaID)
		var backMediaID sql.NullString
		if documentObj.BackMediaID != "" {
			mediaIDs = append(mediaIDs, documentObj.BackMediaID)
			backMediaID = sql.NullString{String: documentObj.BackMediaID, Valid: true}
		} else {
			backMediaID = sql.NullString{String: documentObj.BackMediaID, Valid: false}
		}
		documents = append(documents, KYCDocumentStruct{
			UniqueID:    general.GetUUID(),
			LoanID:      loanApplicationID,
			MediaID:     documentObj.FrontMediaID,
			DocType:     documentObj.DocumentType,
			DocumentID:  documentObj.DocumentID,
			Status:      constants.KYCDocStatusUploaded,
			CreatedBy:   userID,
			BackMediaID: backMediaID,
		})
	}
	return documents, photoMediaID, mediaIDs, "", addressProofFound, panFound
}

func getLoanKYCDetails(loanApplicationID string) (map[string]DocStatusStruct, bool, bool, error) {
	loanKYCObjs := []LoanKYCStruct{}
	alreadyUploaded := false
	digilockerDone := false
	digilockerTried := false

	docStatusMap := make(map[string]DocStatusStruct)
	query := `SELECT doc_type as doctype, document_id as documentid, status,
				coalesce(rejection_reason, '') as rejectionreason from loan_kyc_details
				where loan_id = $1 and status in ($2, $3)`
	err := database.Select(&loanKYCObjs, query, loanApplicationID,
		constants.KYCDocStatusUploaded, constants.KYCDocStatusRejected)
	if err != nil {
		log.Println(err)
		return docStatusMap, alreadyUploaded, digilockerDone, err
	}
	foundAddressProof := false

	for _, loanKYCObj := range loanKYCObjs {
		switch loanKYCObj.DocType {
		case constants.DocTypeDigilockerAadhaar:
			digilockerTried = true
			if loanKYCObj.Status == constants.KYCDocStatusUploaded {
				digilockerDone = true
			}
		case constants.DocTypeAddressProof:
			foundAddressProof = true
			if loanKYCObj.DocumentID != constants.AADHAARDocumentID {
				alreadyUploaded = true
			}
		case constants.DocTypeCurrentAddressProof:
			foundAddressProof = true
			alreadyUploaded = true
		}
		switch loanKYCObj.Status {
		case constants.KYCDocStatusUploaded:
			docStatusMap[loanKYCObj.DocType] = DocStatusStruct{
				Status:       constants.KYCDocTypeStatusUploaded,
				RejectReason: "",
			}
		case constants.KYCDocStatusRejected:
			docStatusMap[loanKYCObj.DocType] = DocStatusStruct{
				Status:       constants.KYCDocTypeStatusRejected,
				RejectReason: loanKYCObj.RejectionReason,
			}
		}
	}
	// if address proof missing implies e-kyc must have been done
	if !foundAddressProof && !digilockerTried {
		docStatusMap[constants.DocTypeAddressProof] = DocStatusStruct{
			Status:       constants.KYCDocTypeStatusUploaded,
			RejectReason: "",
		}
	}
	return docStatusMap, alreadyUploaded, digilockerDone, err
}

func isHideBusinessRegistrationProof(dbObj KYCLoanDetailsStruct) (bool, string) {
	if journey.IsMuthootCLPartner(dbObj.SourceEntityID) || journey.IsABFLBLSourcing(dbObj.SourceEntityID) {
		return true, ""
	}
	var hideBusinessRegistrationProof bool
	if dbObj.LoanType != constants.LoanTypePersonalLoan {
		_, isActiveGST := usersutil.GetActiveGSTIN(dbObj.UserID)
		// if active gst found for an user and hide gst setting active for source entity
		if isActiveGST && journey.HideGSTKYCforActiveGSTIN(dbObj.SourceEntityID) {
			hideBusinessRegistrationProof = true
		}
		// for cases where hide business proof setting active based on loan amount
		hide, loanLimitAmount := journey.HideBusinessProofBasedOnLoanAmount(dbObj.SourceEntityID)
		if hide {
			if dbObj.AppliedAmount == 0 {
				eligibleLoanAmount, err := usersutil.GetLatestEligibleAmount(dbObj.UserID)
				if err != nil {
					log.Println(err)
					return hideBusinessRegistrationProof, "Eligible amount not found"
				}
				if eligibleLoanAmount <= loanLimitAmount {
					hideBusinessRegistrationProof = true
				}
			} else if dbObj.AppliedAmount <= loanLimitAmount {
				hideBusinessRegistrationProof = true
			}
		}
	}
	return hideBusinessRegistrationProof, ""
}

func getAvailableDocs(hideBusinessRegistrationProof bool, hideSignature bool) ([]DBDocStruct, error) {

	dbDocs := []DBDocStruct{}
	queryExtension := fmt.Sprintf(` AND document_category != '%s'`, constants.DocCatBusinessProof)
	queryExtension2 := fmt.Sprintf(` AND document_category != '%s'`, constants.DocCatSignature)
	// TODO: add ocr support for driving license
	query := `SELECT document_id as documentid, document_category as documenttype,
		coalesce(document_title, '') as documentname, allow_upload as allowupload,
		both_sides as bothsides, coalesce(icon_sub_url,'') as iconsuburl FROM documents where status = 1 and document_name != 'DRIVING_LICENSE'`

	if hideBusinessRegistrationProof {
		query += queryExtension
	}

	if hideSignature {
		query += queryExtension2
	}

	err := database.Select(&dbDocs, query)
	if err != nil {
		log.Println(err)
		return dbDocs, err
	}
	return dbDocs, nil
}

// getAvailableCurrenAddressDocs returns the list of documents that can be uploaded for current address proof
func getAvailableCurrenAddressDocs() ([]DBDocStruct, error) {
	dbDocS := []DBDocStruct{}
	query := `SELECT document_id as documentid, document_category as documenttype,
	document_title as documentname, allow_upload as allowupload,
	both_sides as bothsides, coalesce(icon_sub_url, '') as iconsuburl FROM documents where status = 1 and document_category = $1`
	err := database.Select(&dbDocS, query, constants.DocTypeCurrentAddressProof)
	if err != nil {
		log.Println(err)
		return dbDocS, err
	}
	return dbDocS, nil
}

func GetDocDataMap(dbDocs []DBDocStruct, sourceEntityID string, alreadyUploaded bool, onlyEkyc bool, digilockerDone bool, isDashboard bool) map[string][]structs.DocumentDataStruct {
	docDataMap := make(map[string][]structs.DocumentDataStruct)
	for _, dbDoc := range dbDocs {
		if dbDoc.DocumentType == constants.DocTypePANCard && !journey.ShowPANInKYC(sourceEntityID) {
			continue
		}

		if general.InArr(dbDoc.DocumentID, constants.DocIDExcludedFromKYCDetails) {
			continue
		}

		if !alreadyUploaded && dbDoc.DocumentType == constants.DocTypeAddressProof && onlyEkyc {
			continue
		}

		if journey.OnlyAadhaarManual(sourceEntityID) && !alreadyUploaded {
			if dbDoc.DocumentType == constants.DocTypeAddressProof && dbDoc.DocumentName != "AADHAAR" {
				continue
			}
		}
		if digilockerDone && general.InArr(dbDoc.DocumentType, []string{constants.DocTypeAddressProof, constants.DocTypePANCard}) {
			continue
		}
		allowUpload := dbDoc.AllowUpload
		if valid, val := journey.AllowSDKKYCUpload(sourceEntityID, dbDoc.DocumentType); valid {
			allowUpload = val
		}
		captureTitle := fmt.Sprintf("Upload your %s", dbDoc.DocumentName)
		var captureDescription string
		if isDashboard {
			captureDescription = fmt.Sprintf("Make sure all the details in %s are clearly visible", dbDoc.DocumentName)
		} else {
			captureDescription = fmt.Sprintf("Make sure all the details in %s are clearly visible and are inside the box", dbDoc.DocumentName)
		}
		if dbDoc.DocumentType == constants.DocTypePhoto {
			if isDashboard {
				captureTitle = "Upload the Selfie"
				captureDescription = "Ensure that the face is clearly visible"
			} else {
				captureTitle = "Take your Selfie"
				captureDescription = "Ensure that your face is clearly visible, you are neatly dressed and in an upright position"
			}
			if valid, val := journey.AllowSDKKYCUpload(sourceEntityID, dbDoc.DocumentType); valid {
				allowUpload = val
			}
		}
		if len(docDataMap[dbDoc.DocumentType]) == 0 {
			docDataMap[dbDoc.DocumentType] = []structs.DocumentDataStruct{}
		}
		docDataMap[dbDoc.DocumentType] = append(docDataMap[dbDoc.DocumentType], structs.DocumentDataStruct{
			DocumentID:         dbDoc.DocumentID,
			DocumentName:       dbDoc.DocumentName,
			BothSides:          dbDoc.BothSides,
			CaptureTitle:       captureTitle,
			CaptureDescription: captureDescription,
			AllowUpload:        allowUpload,
			IconSubURL:         dbDoc.IconSubURL,
		})
	}
	return docDataMap
}

func getDocCategories(sourceEntityID string, lenderID string, flowID int, occupationType string) []structs.DocumentCategory {
	docCats := []structs.DocumentCategory{{
		DocumentType:  constants.DocTypePhoto,
		DocumentTitle: "Selfie",
	}, {
		DocumentType:  constants.DocTypePANCard,
		DocumentTitle: "PAN Card",
	},
	}

	docCats = append(docCats, structs.DocumentCategory{
		DocumentType:  constants.DocTypeAddressProof,
		DocumentTitle: "Address Proof",
	})

	if journey.IsMuthootCLPartner(sourceEntityID) {
		docCats = append(docCats, structs.DocumentCategory{
			DocumentType:  constants.DocTypeSignature,
			DocumentTitle: "Signature",
		})
	}

	if sourceEntityID == constants.HousingID && lenderID == constants.LoanTapID && occupationType == constants.OccupationTypeSalaried {
		docCats = append(docCats, structs.DocumentCategory{
			DocumentType:  constants.DocTypeOccupationProof,
			DocumentTitle: "Salary Slip",
		})
	}

	if journey.IsCurrentAddressKYCFlow(flowID) && !journey.IsPFLSourcing(sourceEntityID) {
		docCats = append(docCats, structs.DocumentCategory{
			DocumentType:  constants.DocTypeCurrentAddressProof,
			DocumentTitle: "Current Address Proof",
		})
	}

	if !journey.ShowPANInKYC(sourceEntityID) {
		docCats = append(docCats[0:1], docCats[2:]...)
	}
	// Show Co Applicant only for Tata Nexarc
	if journey.ShowCoApplicantInKYC(sourceEntityID) && lenderID == constants.KotakID {
		docCats = append(docCats, structs.DocumentCategory{
			DocumentType:  constants.DocTypeCoAddressProof,
			DocumentTitle: "Co Applicant Address Proof",
		}, structs.DocumentCategory{
			DocumentType:  constants.DocTypeCoPANCard,
			DocumentTitle: "Co Applicant PAN Card",
		})
	}
	return docCats
}

func isPLWithBusiness(sourceEntityID string, lenderID string, loanApplicationID string, occupationType string) (bool, error) {
	plWithBusiness := false
	switch sourceEntityID {
	case constants.GimBooksID:
		var preLoanDataStr string
		query := `SELECT coalesce(pre_loan_data::TEXT, '') as preloandatastr from user_loan_details uld
			right join loan_application la on la.loan_application_id = uld.loan_application_id
			and uld.status = $1 where la.loan_application_id = $2`
		err := database.Get(&preLoanDataStr, query, constants.LoanDetailsStatusActive, loanApplicationID)
		if err != nil {
			log.Println(err)
			return plWithBusiness, err
		}
		if preLoanDataStr != "" {
			preLoanData := make(map[string]interface{})
			err = json.Unmarshal([]byte(preLoanDataStr), &preLoanData)
			if err != nil {
				log.Println(err)
			} else {
				businessProofPresent := preLoanData["businessProofPresent"]
				if businessProofPresent == "Yes" {
					plWithBusiness = true
				}
			}
		}
	case constants.HousingID:
		if lenderID == constants.LoanTapID && occupationType == constants.OccupationTypeSelfEmployed {
			plWithBusiness = true
		}
	}
	return plWithBusiness, nil
}

func GenerateDocumentsFromCats(docCats []structs.DocumentCategory, docStatusMap map[string]DocStatusStruct, digilockerDone bool, docDataMap map[string][]structs.DocumentDataStruct) []structs.DocumentStruct {
	documents := []structs.DocumentStruct{}
	for _, docCat := range docCats {
		docStatus := constants.KYCDocTypeStatusPending
		var rejectReason string
		savedInfo, found := docStatusMap[docCat.DocumentType]
		if found {
			docStatus = savedInfo.Status
			rejectReason = savedInfo.RejectReason
		}

		if digilockerDone && general.InArr(docCat.DocumentType, []string{constants.DocTypeAddressProof, constants.DocTypePANCard}) {
			continue
		}
		docTypes := docDataMap[docCat.DocumentType]
		hint := fmt.Sprintf("Upload your %s", docCat.DocumentTitle)
		if docCat.DocumentType == constants.DocTypeSignature {
			hint = "Sign on plain white paper and upload its picture"
		}

		var iconURL string
		optional := false
		switch strings.ToLower(docCat.DocumentType) {
		case "photo":
			iconURL = constants.IconSrcSelfie
		case "pan_card":
			iconURL = constants.IconSrcPAN
		case "business_registration_proof":
			iconURL = constants.IconSrcBusinessProof
		case "business_pan":
			iconURL = constants.IconSrcBusinessPAN
		case "address_proof":
			iconURL = constants.IconSrcAddress
		case "current_address_proof":
			iconURL = constants.IconSrcAddress
		case "co_pan_card", "co_address_proof":
			iconURL = constants.IconSrcAddress
			optional = true
		case "api_aadharimage":
			iconURL = constants.IconSrcAddress
		case strings.ToLower(constants.DocTypeAdditionalDocument):
			if general.InArr(docCat.DocumentTitle, []string{"Bank Statement", "Udyam Certificate", "Employment Proof", "Salary Slip", "Employment ID Card", "Employment Certificate", "Relationship Proof", "Salary Slip Month 1", "Salary Slip Month 2", "Salary Slip Month 3"}) {
				optional = true
			}
		default:
			iconURL = constants.IconSrcAddress
		}

		documents = append(documents, structs.DocumentStruct{
			DocumentType:   docCat.DocumentType,
			DocumentTitle:  docCat.DocumentTitle,
			Status:         docStatus,
			RejectedReason: rejectReason,
			Hint: func() string {
				if docCat.Hint != "" {
					return docCat.Hint
				}
				return hint
			}(),
			DocumentData: docTypes,
			IconSrc:      iconURL,
			Optional:     optional,
		})
	}
	return documents
}

func appendDocCatsBusinessDetails(docCats []structs.DocumentCategory, userID string, sourceEntityID string, hideBusinessRegistrationProof bool) ([]structs.DocumentCategory, error) {
	var count int
	query := `SELECT count(*) from user_business where user_id = $1 AND constitution != 'Proprietorship'`
	err := database.Get(&count, query, userID)
	if err != nil {
		log.Println(err)
		return docCats, err
	}
	if count > 0 && !journey.IsMuthootCLPartner(sourceEntityID) {
		docCats = append(docCats, structs.DocumentCategory{
			DocumentType:  constants.DocTypeBusinessPAN,
			DocumentTitle: "Business PAN",
		})
	}
	if !hideBusinessRegistrationProof {
		docCats = append(docCats, structs.DocumentCategory{
			DocumentType:  constants.DocTypeBusinessProof,
			DocumentTitle: "Business Registration Proof",
		})
	}
	return docCats, nil
}

// getKYCFlowIDV2 TODO: to be deprecated in future
func getKYCFlowIDV2(sourceEntityID, userID string) int {
	var flowID int
	var flowSet bool
	var err error
	if journey.IsProgramApplicable(sourceEntityID) {
		flowID, err = userjourney.GetKYCFlow(userID)
		if err != nil {
			log.Println(err)
			flowID = constants.KYCFLowManual
			flowSet = true
		}
		if flowID != constants.KYCFlowUnassigned && !flowSet {
			return flowID
		}
		if !flowSet {
			resp := journey.IIFLAgg(sourceEntityID, false)
			tempSourceEntityID := sourceEntityID
			if resp.IsAgg && !journey.HasCustomKYCFlow(sourceEntityID) {
				switch resp.LoanType {
				case constants.LoanTypePersonalLoan:
					tempSourceEntityID = constants.IIFLID
				case constants.LoanTypeBusinessLoan:
					tempSourceEntityID = constants.IIFLBLID
				}
			}
			type dbStruct struct {
				SourceEntityKYCFlowID int
				WorkflowKYCFlowID     int
			}
			var obj dbStruct
			query := `select coalesce(se.kyc_flow, $2) as sourceentitykycflowid,
				coalesce(wf.kyc_flow, $2) as workflowkycflowid
				from source_entity se
				left join user_journey uj on uj.user_id = $3 and uj.status = $4
				left join workflow wf on uj.workflow_id = wf.workflow_id
				where se.source_entity_id = $1;`
			err = database.Get(&obj, query, tempSourceEntityID, constants.KYCFLowManual, userID, userjourney.StatusActive)
			if err != nil {
				log.Println(err)
			}
			if obj.WorkflowKYCFlowID != constants.KYCFLowManual {
				flowID = obj.WorkflowKYCFlowID
			} else {
				flowID = obj.SourceEntityKYCFlowID
			}
		}
		err = userjourney.SetKYCFlowID(nil, userID, flowID)
		if err != nil {
			log.Error(err)
			flowID = constants.KYCFLowManual
		}
	}
	return flowID
}

// GetNextKYCModule takes currentModule and whether the last state was success or not and returns next module, next subModule,
// kyc flow id and error if any
func GetNextKYCModule(currentModule, sourceEntityID string, userID string, isSuccess bool) (string, string, int, error) {
	var kycFlowID int
	//TODO: remove this logic for fetching kycFlowID. Replace with a proper one
	if journey.IsKYCServiceFlowEnabled(sourceEntityID, userID) || journey.IsKYCServiceWorkflowEnabled(userID) {
		kycFlowID = GetKYCFlowID(sourceEntityID, userID)
	} else if journey.IsProgramApplicable(sourceEntityID) {
		kycFlowID = getKYCFlowIDV2(sourceEntityID, userID)
	} else {
		kycFlowID = GetKYCFlowID(sourceEntityID, userID)
	}

	log.Info("flowID --> ", kycFlowID)
	kycFlow := constants.KYCFlows[kycFlowID]
	log.Debug("kycFlow --> ", kycFlow)

	var nextModule string
	var nextSubModule string

	if currentModule == "" {
		//Return first module from flow
		return kycFlow[0].Module, kycFlow[0].SubModule, kycFlowID, nil
	}

	for _, value := range kycFlow {
		if value.Module == currentModule {
			var idx int
			if isSuccess {
				idx = value.NextModuleIdx
			} else {
				idx = value.FallbackModuleIdx
			}

			if idx >= len(kycFlow) {
				return "", "", kycFlowID, errors.New("no next/fallback module found")
			}

			nextModule = kycFlow[idx].Module
			nextSubModule = kycFlow[idx].SubModule
			break
		}
	}
	return nextModule, nextSubModule, kycFlowID, nil
}

// CheckLastKYCModule checks if the module passed is final module or not
func CheckLastKYCModule(currentModule string, sourceEntityID string, userID string) bool {
	if currentModule == constants.KYCTypeDone {
		return true
	}
	nextModule, _, _, _ := GetNextKYCModule(currentModule, sourceEntityID, userID, true)
	return nextModule == constants.KYCTypeDone
}

// func CheckLastKYCModuleByGetKYCModules(currentModule string loanApplicationID, userID, sourceEntityID string) bool {

// }

func isDigilockerTried(loanApplicationID string) (bool, error) {
	digilockerRecordsCount := 0
	query := `select count(*) from loan_kyc_details where loan_id=$1
		and doc_type in ($2);`
	err := database.Get(&digilockerRecordsCount, query, loanApplicationID, constants.DocTypeDigilockerAadhaar)
	if err != nil {
		log.Println(err)
		return false, err
	}
	if digilockerRecordsCount > 0 {
		return true, nil
	}
	return false, nil
}

func isDigilockerSuccess(loanApplicationID string) (bool, error) {
	digilockerRecordsCount := 0
	query := `select count(*) from loan_kyc_details where loan_id=$1
		and doc_type in ($2) and status in ($3);`
	err := database.Get(&digilockerRecordsCount, query, loanApplicationID, constants.DocTypeDigilockerAadhaar, constants.KYCDocStatusUploaded)
	if err != nil {
		log.Println(err)
		return false, err
	}
	if digilockerRecordsCount > 0 {
		return true, nil
	}
	return false, nil
}

type MediaOCRMetaData struct {
	Path           string
	DocumentName   string
	UserID         string
	MediaID        string
	SourceEntityID string
	LKI            string
}

func GetMediaRelatedData(mediaID string, sourceEntityID string, lki string, loanID string) (MediaOCRMetaData, error) {
	var data MediaOCRMetaData
	data.LKI = lki
	data.SourceEntityID = sourceEntityID

	query := `select path, document_name as documentname,l.user_id as userid from loan_kyc_details lkd 
	join media m on lkd.media_id= m.media_id
	join documents d on d.document_id = lkd.document_id
	join loan_application l on lkd.loan_id= l.loan_application_id
	where m.media_id=$1 and l.loan_application_id=$2`
	err := database.Get(&data, query, mediaID, loanID)
	if err != nil {
		log.Println(err)
		return data, err
	}

	return data, nil
}
func OCRFrontFunc(ctx context.Context, data MediaOCRMetaData) error {
	filePath, err := s3.GetLocalFilePath(data.Path)
	if err != nil {
		log.WithContext(ctx).Error(err)
		return err
	}
	defer os.Remove(filePath)

	var maskedURL string
	var croppedURL string
	var OCRName string
	var docIdentifier string
	updateIdentifier := false
	switch data.DocumentName {
	case constants.DocumentNameAadhaar:
		OCRName, _, docIdentifier, maskedURL, _ = ocr.GetOCRNameDOBIdentifierFromAadhaarFront(filePath, data.UserID, data.MediaID, data.SourceEntityID)
		// save masked url
		if maskedURL != "" {
			_, _ = s3.ReadFromURLAndUploadFileS3(maskedURL, data.Path)
		}
		updateIdentifier = true
	case constants.DocumentNamePassport:
		OCRName, _, _, croppedURL, _ = ocr.GetOCRNameDOBFromPassportFront(filePath, data.UserID, data.MediaID)
		// save cropped url
		if croppedURL != "" {
			_, _ = s3.ReadFromURLAndUploadFileS3(croppedURL, data.Path)
		}
	case constants.DocumentNameVoter:
		OCRName, _, croppedURL, _ = ocr.GetOCRNameFromVoterIDFront(filePath, data.UserID, data.MediaID, data.SourceEntityID)
		// save cropped url
		if croppedURL != "" {
			_, _ = s3.ReadFromURLAndUploadFileS3(croppedURL, data.Path)
		}
	case constants.DocumentNamePAN:
		docIdentifier, OCRName, _, _, _, croppedURL, _ = ocr.GetOCRPANNameDOBFromPAN(filePath, data.UserID, data.MediaID, false)
		if croppedURL != "" {
			_, _ = s3.ReadFromURLAndUploadFileS3(croppedURL, data.Path)
		}
		updateIdentifier = true
	case constants.DocumentNameBusinessPAN:
		docIdentifier, OCRName, _, _, _, croppedURL, _ = ocr.GetOCRPANNameDOBFromPAN(filePath, data.UserID, data.MediaID, true)
		if croppedURL != "" {
			_, _ = s3.ReadFromURLAndUploadFileS3(croppedURL, data.Path)
		}
		updateIdentifier = true
	}

	if updateIdentifier {
		err = UpdateNameIdentifierLKD(OCRName, docIdentifier, data.LKI)
		if err != nil {
			log.Errorln(err)
			return err
		}
	}

	return nil
}

func getManualApprovalKey(loanApplicationID string) string {
	return fmt.Sprintf("%s_%s", constants.LockManualApprovePrefix, loanApplicationID)
}

func AddCoApplicantKYCDoc(req *coapplicantkycdetails.UpdateCoApplicantKYCDocRequest) (err error) {
	coApplicantKYCDocs := make([]coapplicantkycdetails.StructForSet, 0)

	ctx := context.Background()

	loanDetails, err := loanapplication.Get(ctx, req.LoanApplicationID)
	if err != nil {
		log.WithContext(ctx).Errorf("[AddCoApplicantKYCDoc] failed to get loan details. err: %v, req: %+v", err, req)
		return err
	}

	coAppUser, err := users.Get(req.UserID)
	if err != nil {
		logger.Log.WithContext(ctx).Errorf("[AddCoApplicantKYCDoc] user does not exit. userID: %v", req.UserID)
		errorHandler.ReportToSentryV2(ctx, err)
		return
	}

	for _, doc := range req.KYCDocs {
		// docCat, ok := constants.DocumentTypeToDocumentCategoryMapping[doc.DocumentType]
		// if !ok {
		// 	logger.WithUser(req.UserID).Errorln("invalid document type: ", doc.DocumentType)
		// 	return err
		// }

		// var docID string
		// query := `select document_id from documents where document_category=$1`
		// err := database.Get(&docID, query, docCat)
		// if err != nil {
		// 	logger.WithUser(req.UserID).Errorln("unable to find document of provided type: ", doc.DocumentID)
		// 	return err
		// }

		coApplicantKYCDocs = append(coApplicantKYCDocs, coapplicantkycdetails.StructForSet{
			ID:      general.GetUUID(),
			UserID:  req.UserID,
			MediaID: doc.FrontMediaID,
			BackMediaID: sql.NullString{
				String: doc.BackMediaID,
				Valid:  (doc.BackMediaID != ""),
			},
			DocType:    doc.DocumentType,
			DocumentID: doc.DocumentID,
			Status:     constants.KYCDocStatusUploaded,
		})
	}

	for _, coApplicantKYCDoc := range coApplicantKYCDocs {
		err = coapplicantkycdetails.Insert(nil, coApplicantKYCDoc)
		if err != nil {
			logger.WithUser(req.UserID).Errorln("unable to insert co-applicant's kyc doc: ", err)
			return err
		}
	}

	go func(dateTimeNowString string) {
		activityObj := activity.ActivityEvent{
			UserID:            loanDetails.UserID,
			SourceEntityID:    loanDetails.SourceEntityID,
			LoanApplicationID: req.LoanApplicationID,
			EntityType:        constants.EntityTypeSystem,
			EntityRef:         loanDetails.UserID,
			EventType:         constants.ActivityCoApplicantKYCSubmitted,
			Description:       fmt.Sprintf(`{ "customerID": "%s" }`, coAppUser.UniqueID),
		}
		activity.RegisterEvent(&activityObj, dateTimeNowString)
	}(general.GetTimeStampString())

	return nil
}

func UpdateCoApplicantKYCDoc(req *coapplicantkycdetails.UpdateCoApplicantKYCDocRequest) (err error) {

	ctx := context.Background()

	loanDetails, err := loanapplication.Get(ctx, req.LoanApplicationID)
	if err != nil {
		log.WithContext(ctx).Errorf("[UpdateCoApplicantKYCDoc] failed to get loan details. err: %v, req: %+v", err, req)
		return err
	}

	coAppUser, err := users.Get(req.UserID)
	if err != nil {
		logger.Log.WithContext(ctx).Errorf("[UpdateCoApplicantKYCDoc] user does not exit. userID: %v", req.UserID)
		errorHandler.ReportToSentryV2(ctx, err)
		return
	}

	for _, doc := range req.KYCDocs {
		coApplicantKYCDoc := coapplicantkycdetails.StructForSet{
			ID:      doc.ID,
			MediaID: doc.FrontMediaID,
			BackMediaID: sql.NullString{
				String: doc.BackMediaID,
				Valid:  (doc.BackMediaID != ""),
			},
		}

		err = coapplicantkycdetails.Update(nil, coApplicantKYCDoc)
		if err != nil {
			logger.WithUser(req.UserID).Errorln("unable to update co-applicant's kyc doc: ", err)
			return err
		}
	}

	go func(dateTimeNowString string) {
		activityObj := activity.ActivityEvent{
			UserID:            loanDetails.UserID,
			SourceEntityID:    loanDetails.SourceEntityID,
			LoanApplicationID: req.LoanApplicationID,
			EntityType:        constants.EntityTypeSystem,
			EntityRef:         loanDetails.UserID,
			EventType:         constants.ActivityCoApplicantKYCUpdated,
			Description:       fmt.Sprintf(`{ "customerID": "%s" }`, coAppUser.UniqueID),
		}
		activity.RegisterEvent(&activityObj, dateTimeNowString)
	}(general.GetTimeStampString())

	return nil
}

// GetLatestKYCServiceProgram gets all the programNames for a sourceEntityID, lenderID and kycType.
func GetLatestKYCServiceProgram(ctx context.Context, sourceEntityID, lenderID, kycType, loanVariant string) (kycProgramName string, err error) {

	ksPrograms, err := kycserviceprograms.GetAllPrograms(ctx, sourceEntityID, lenderID, loanVariant, kycType)
	if err != nil {
		logger.Log.WithContext(ctx).Errorln("Error getting kyc_program_name: ", err)
		return "", errors.Wrap(err, "failed to get KYC programs")
	}

	if len(ksPrograms) == 0 {
		logger.Log.WithContext(ctx).Errorln("Empty ksPrograms, length of ksPrograms: ", len(ksPrograms))
		return "", errors.New("no KYC programs found")
	}

	return ksPrograms[0].KYCProgramName, err

}

// UserPANNameDOBCheck returns dobMatch, nameMatch and error for PAN vs name check
func UserPANNameDOBCheck(user users.User, panName, panDob, name, dob string) (bool, bool) {
	panPartnerNameMatch, panAadhaarNameMatch := false, false
	panDOBMatch := false
	panName = strings.ToLower(panName)

	// name match
	panPartnerNameMatch = fuzzy.TokenSetRatio(panName, strings.ToLower(user.Name)) >= 70
	panAadhaarNameMatch = fuzzy.TokenSetRatio(panName, strings.ToLower(name)) >= 70

	// dob
	panDOBMatch = panDob == dob

	// If DOBs do not match and FlagYOBMatch feature flag enable
	if !panDOBMatch && featureflag.Get(user.ID, journey.FlagYOBMatchOnDOBMismatch) {
		aadharDOBTime, aadharDOBErr := time.Parse(constants.YYYYMMDDHyphenDateFormatConstant, dob)
		panDobTime, panDOBErr := time.Parse(constants.YYYYMMDDHyphenDateFormatConstant, panDob)

		if aadharDOBErr != nil || panDOBErr != nil {
			err := fmt.Errorf("aadhar and PAN DOB should be in YYYY-MM-DD format, aadharDOBErr: %v panDOBErr: %v", aadharDOBErr, panDOBErr)
			logger.WithUser(user.ID).Errorln(err)
			errorHandler.ReportToSentryWithFields(map[string]any{"userID": user.ID}, err)
			return panDOBMatch, (panAadhaarNameMatch && panPartnerNameMatch)
		}

		// Compare the years if full DOBs do not match
		return aadharDOBTime.Year() == panDobTime.Year(), (panAadhaarNameMatch && panPartnerNameMatch)
	}

	return (panDOBMatch),
		(panAadhaarNameMatch && panPartnerNameMatch)
}

func UserNameCheck(userID, userName, aadharName, panName string) (int, int, int, error) {
	// get scores for all
	panPartnerNameMatchScore, err := namematch.GenerateScoreAndPersist(userID, strings.ToLower(panName), strings.ToLower(userName), "panPartnerNameMatchScore")
	if err != nil {
		logger.WithUser(userID).Errorln(err)
		return 0, 0, 0, err
	}
	aadharNameMatchScore, err := namematch.GenerateScoreAndPersist(userID, strings.ToLower(userName), strings.ToLower(aadharName), "aadharNameMatchScore")
	if err != nil {
		logger.WithUser(userID).Errorln(err)
		return 0, 0, 0, err
	}
	panAadhaarNameMatchScore, err := namematch.GenerateScoreAndPersist(userID, strings.ToLower(panName), strings.ToLower(aadharName), "panAadhaarNameMatchScore")
	if err != nil {
		logger.WithUser(userID).Errorln(err)
		return 0, 0, 0, err
	}

	return panPartnerNameMatchScore, aadharNameMatchScore, panAadhaarNameMatchScore, nil

	// if all of the above scores is greater than 70, then we can consider it as a match
	// else if any of the above scores is between 50 to 70, then we can consider it as a partial match
	// if panPartnerNameMatchScore >= 70 && panAadhaarNameMatchScore >= 70 && aadharNameMatchScore >= 70 {
	// 	return kycresults.StatusSuccess, "", "", nil
	// } else if (panPartnerNameMatchScore >= 50 && panPartnerNameMatchScore < 70) || (panAadhaarNameMatchScore >= 50 && panAadhaarNameMatchScore < 70) || (aadharNameMatchScore >= 50 && aadharNameMatchScore < 70) {
	// 	return kycresults.ConditonalSuccess, fmt.Sprintf("Name match score failed: Aadhar Name %s, Pan Name: %s  Name: %s", aadharName, userName), "", nil
	// }

	// return kycresults.StatusRejected, "Name Match", "", nil
}

func IsOkycUidai(kycDetails *structs.TriggerOKYCDetail) bool {
	return kycDetails != nil && strings.EqualFold(kycDetails.Source, constants.OKYCSourceUIDAI)
}
