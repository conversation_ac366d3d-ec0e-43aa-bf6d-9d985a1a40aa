package builderutils

import (
	"context"
	"encoding/json"
	"errors"
	"finbox/go-api/authentication"
	"finbox/go-api/conf"
	"finbox/go-api/constants"
	"finbox/go-api/core/repository/auditrepository"
	"finbox/go-api/functions/journey"
	"finbox/go-api/functions/logger"
	"finbox/go-api/infra/db"
	"finbox/go-api/infra/temporalclient"
	"finbox/go-api/models/multiuserloanrelations"
	"finbox/go-api/models/partnerdataschema"
	"finbox/go-api/models/sectionconfig"
	"finbox/go-api/models/userjourney"
	"finbox/go-api/models/userworkflows"
	"finbox/go-api/models/workflowconfig"
	studioconstants "finbox/go-api/studio/constants"
	studiomodels "finbox/go-api/studio/models"
	clientrepository "finbox/go-api/studio/repository/client"
	resourcerepository "finbox/go-api/studio/repository/resources"
	stagingrepository "finbox/go-api/studio/repository/staging"
	stagingworkflowconfigrepository "finbox/go-api/studio/repository/stagingworkflowconfig"
	resourceservice "finbox/go-api/studio/services/resources"
	stagingservice "finbox/go-api/studio/services/staging"
	stagingworkflowconfigservice "finbox/go-api/studio/services/stagingworkflowconfig"
	"finbox/go-api/utils/apischemamapper"
	"fmt"
	"net/http"
	"regexp"
	"time"

	runnerconstants "github.com/finbox-in/road-runner/constants"
	runnerutils "github.com/finbox-in/road-runner/utils"
	"github.com/google/uuid"

	"golang.org/x/exp/slices"
)

var (
	database        = db.GetDB()
	resourceRepo    *resourcerepository.ResourceRepository
	stagingRepo     *stagingrepository.StagingRepository
	clientRepo      *clientrepository.ClientRepository
	workflowRepo    *stagingworkflowconfigrepository.StagingWorkflowConfigRepository
	auditRepo       *auditrepository.AuditRepository
	resourceService *resourceservice.ResourceService
	stagingService  *stagingservice.StagingService
	workflowService *stagingworkflowconfigservice.StagingWorkflowConfigBuilder
)

func initServices() {
	if resourceRepo == nil {
		resourceRepo = resourcerepository.New(database)
	}
	if stagingRepo == nil {
		stagingRepo = stagingrepository.New(database)
	}
	if clientRepo == nil {
		clientRepo = clientrepository.New(database)
	}
	if workflowRepo == nil {
		workflowRepo = stagingworkflowconfigrepository.New(database)
	}
	if auditRepo == nil {
		auditRepo = auditrepository.New(database)
	}
	if workflowService == nil {
		workflowService = stagingworkflowconfigservice.New(workflowRepo, auditRepo)
	}
	if stagingService == nil {
		stagingService = stagingservice.New(stagingRepo, resourceRepo, clientRepo, workflowService, auditRepo)
	}
	if resourceService == nil {
		resourceService = resourceservice.New(resourceRepo)
	}

}

func getResourceService() *resourceservice.ResourceService {
	initServices()
	return resourceService
}

func getStagingService() *stagingservice.StagingService {
	initServices()
	return stagingService
}

// FetchWorkflowConfig retrieves workflow configuration based on user and entity context.
// It handles both staged and published workflows, using group settings when available.
// Returns workflow config and error if any occurs during fetching.
func FetchWorkflowConfig(ctx context.Context, userID, sourceEntityID, moduleName, lenderID, parentSourceEntityID string, isDSA, forceSearchByParentID bool) (*workflowconfig.WorkflowConfig, error) {

	groupID := userjourney.GetGroupID(userID)
	if groupID == "" {
		return fetchWorkflowConfigBySourceEntity(ctx, userID, sourceEntityID, moduleName, lenderID, parentSourceEntityID, isDSA, forceSearchByParentID)
	}

	groupConfig, customErr := fetchGroup(ctx, userID, groupID)
	if customErr != nil {
		logger.WithUser(userID).Errorln(customErr)
		return nil, customErr.Err
	}

	isUserCoApplicant, err := multiuserloanrelations.IsUserCoApplicant(userID)
	if err != nil {
		return nil, err
	}

	var workflowDetails studiomodels.WorkflowConfigDetails
	if isUserCoApplicant {
		workflowDetails = groupConfig.GetChildGroupWorkflowConfigDetails(moduleName)
	} else {
		workflowDetails = groupConfig.GetWorkflowConfigDetails(moduleName)
	}

	if workflowDetails.ConfigID == "" {
		// fall back to legacy code if module not defined in group
		return fetchWorkflowConfigBySourceEntity(ctx, userID, sourceEntityID, moduleName, lenderID, parentSourceEntityID, isDSA, forceSearchByParentID)
	}

	if workflowDetails.IsStaged {
		return fetchStagedWorkflow(ctx, userID, groupID, sourceEntityID, moduleName, workflowDetails.ConfigID)
	}

	workflowConfig, err := fetchPublishedWorkflow(ctx, userID, workflowDetails.ConfigID)
	if err != nil {
		return fetchWorkflowConfigBySourceEntity(ctx, userID, sourceEntityID, moduleName, lenderID, parentSourceEntityID, isDSA, forceSearchByParentID)
	}

	return workflowConfig, nil
}

// fetchWorkflowConfigBySourceEntity retrieves workflow configuration using source entity parameters
// It adjusts lenderID based on source entity settings.
// Returns workflow config and error if any occurs during fetching.
func fetchWorkflowConfigBySourceEntity(ctx context.Context, userID, sourceEntityID, moduleName, lenderID, parentSourceEntityID string, isDSA, forceSearchByParentID bool) (*workflowconfig.WorkflowConfig, error) {
	// Adjust lenderID based on source entity settings
	if journey.SearchWorkflowConfigBasedOnSourceEntityOnly(sourceEntityID, moduleName) {
		lenderID = ""
	}

	// Fetch workflow config with optional parameters
	workflowConfig, err := workflowconfig.GetWithOptionalParameters(sourceEntityID, moduleName, lenderID,
		workflowconfig.OptionalParameters{
			ISDSA:                 isDSA,
			ParentSourceEntityID:  parentSourceEntityID,
			ForceSearchByParentID: forceSearchByParentID,
		})
	if err != nil {
		logger.WithUser(userID).Errorln("Failed to fetch workflow config:", err)
		return nil, err
	}

	return workflowConfig, nil
}

// fetchPublishedWorkflow retrieves a published workflow configuration using configID.
// Returns workflow config and error if any occurs during fetching.
func fetchPublishedWorkflow(ctx context.Context, userID, configID string) (*workflowconfig.WorkflowConfig, error) {
	workflowConfig, err := workflowconfig.GetWithConfigID(configID)
	if err != nil {
		logger.WithUser(userID).Errorln(err)
		return nil, err
	}
	return workflowConfig, nil
}

// fetchStagedWorkflow retrieves and transforms a staged workflow configuration.
// It marshals config data and constructs a workflow config object.
// Returns workflow config and error if any occurs during fetching or marshaling.
func fetchStagedWorkflow(ctx context.Context, userID, groupID, sourceEntityID, moduleName, configID string) (*workflowconfig.WorkflowConfig, error) {
	stagedConfig, customError := getStagingService().GetGroupConfigData(ctx, groupID, configID, studioconstants.ConfigTypeWorkflowConfig)
	if customError != nil {
		logger.WithUser(userID).Errorln(customError)
		return nil, customError.Err
	}

	bytes, err := json.Marshal(stagedConfig.ConfigData)
	if err != nil {
		logger.WithUser(userID).Errorln("Failed to marshal config data:", err)
		return nil, err
	}

	workflowConfig := &workflowconfig.WorkflowConfig{
		ConfigID:           uuid.MustParse(stagedConfig.ConfigID),
		WorkflowDefinition: string(bytes),
		ModuleName:         moduleName,
		SourceEntityID:     uuid.MustParse(sourceEntityID),
	}

	return workflowConfig, nil
}

// FetchSectionConfig retrieves section configuration based on user and entity context.
// It handles both staged and published sections, using group settings when available.
// Returns section config and error if any occurs during fetching.
func FetchSectionConfig(ctx context.Context, userID, sourceEntityID, moduleName, subModule, parentSourceEntityID string, isDSA bool) (*sectionconfig.SectionConfig, error) {

	groupID := userjourney.GetGroupID(userID)
	if groupID == "" {
		return fetchSectionConfigBySourceEntity(ctx, userID, sourceEntityID, moduleName, subModule, parentSourceEntityID, isDSA)
	}

	groupConfig, customErr := fetchGroup(ctx, userID, groupID)
	if customErr != nil {
		logger.WithUser(userID).Errorln(customErr)
		return nil, customErr.Err
	}

	isUserCoApplicant, err := multiuserloanrelations.IsUserCoApplicant(userID)
	if err != nil {
		return nil, err
	}

	var sectionConfigDetails studiomodels.SectionConfigDetails
	if isUserCoApplicant {
		sectionConfigDetails = groupConfig.GetChildGroupSubModuleConfigDetails(moduleName, subModule)
	} else {
		sectionConfigDetails = groupConfig.GetSubModuleConfigDetails(moduleName, subModule)
	}

	if sectionConfigDetails.ConfigID == "" {
		// fall back to legacy code if module/subModule not defined in group
		return fetchSectionConfigBySourceEntity(ctx, userID, sourceEntityID, moduleName, subModule, parentSourceEntityID, isDSA)
	}

	if sectionConfigDetails.IsStaged {
		return fetchStagedSectionConfig(ctx, userID, groupID, sourceEntityID, moduleName, subModule, sectionConfigDetails.ConfigID)
	}

	sectionConfig, err := fetchPublishedSectionConfig(ctx, userID, sectionConfigDetails.ConfigID)
	if err != nil {
		return fetchSectionConfigBySourceEntity(ctx, userID, sourceEntityID, moduleName, subModule, parentSourceEntityID, isDSA)
	}

	return sectionConfig, nil
}

// fetchStagedSectionConfig retrieves and transforms a staged section configuration.
// It unmarshals config data and constructs a section config object.
// Returns section config and error if any occurs during fetching or unmarshaling.
func fetchStagedSectionConfig(ctx context.Context, userID, groupID, sourceEntityID, moduleName, subModule, configID string) (*sectionconfig.SectionConfig, error) {
	config, customErr := getStagingService().GetGroupConfigData(ctx, groupID, configID, studioconstants.ConfigTypeSectionConfig)
	if customErr != nil {
		logger.WithUser(userID).Errorln(customErr)
		return nil, customErr.Err
	}

	var configMap map[string]interface{}
	if err := json.Unmarshal(config.ConfigData, &configMap); err != nil {
		logger.WithUser(userID).Errorln("Failed to unmarshal config data:", err)
		return nil, err
	}

	sectionConfig := &sectionconfig.SectionConfig{
		ModuleName:     moduleName,
		SubModuleName:  subModule,
		IsActive:       true,
		SourceEntityID: sourceEntityID,
		ID:             config.ConfigID,
		SectionData:    configMap,
	}

	return sectionConfig, nil
}

// fetchPublishedSectionConfig retrieves a published section configuration using configID.
// Returns section config and error if any occurs during fetching.
func fetchPublishedSectionConfig(ctx context.Context, userID, configID string) (*sectionconfig.SectionConfig, error) {
	sectionConfig, err := sectionconfig.GetWithConfigID(configID)
	if err != nil {
		logger.WithUser(userID).Errorln(err)
		return nil, err
	}
	return sectionConfig, nil
}

// fetchSectionConfigBySourceEntity retrieves section configuration using source entity parameters
// when no group context is available.
// Returns section config and error if any occurs during fetching.
func fetchSectionConfigBySourceEntity(ctx context.Context, userID, sourceEntityID, moduleName, subModule, parentSourceEntityID string, isDSA bool) (*sectionconfig.SectionConfig, error) {
	sectionConfig, err := sectionconfig.GetWithOptionalParameters(sourceEntityID, moduleName, subModule,
		sectionconfig.OptionalParameters{
			ISDSA:                isDSA,
			ParentSourceEntityID: parentSourceEntityID,
		})
	if err != nil {
		logger.WithUser(userID).Errorln("Failed to fetch section config:", err)
		return nil, err
	}

	return sectionConfig, nil
}

func FetchGroups(ctx context.Context, userID string) (studiomodels.Group, *studiomodels.CustomError) {
	groupID := userjourney.GetGroupID(userID)
	if groupID != "" {
		return fetchGroup(ctx, userID, groupID)
	}
	return studiomodels.Group{}, studiomodels.NewCustomError(http.StatusConflict, errors.New("group not enabled / assigned"))
}

func fetchGroup(ctx context.Context, userID string, groupID string) (studiomodels.Group, *studiomodels.CustomError) {

	var (
		customErr    *studiomodels.CustomError
		groupDetails studiomodels.Group
	)

	if conf.ENV == conf.ENV_PROD {
		groupDetails, customErr = getResourceService().GetGroupConfigByID(ctx, groupID)
	} else {
		groupDetails, customErr = getStagingService().GetGroupDetails(ctx, groupID)
	}

	if customErr != nil {
		logger.WithUser(userID).Errorln("Failed to fetch group config from DB:", customErr)
		return groupDetails, customErr
	}

	return groupDetails, customErr
}

func FetchSingleSubmitConfigDetails(userID, sourceEntityID, parentSourceEntityID, resourceName, moduleName, subModuleName string) (*apischemamapper.Schema, error) {
	var (
		err          error
		customErr    *studiomodels.CustomError
		groupConfig  studiomodels.Group
		schema       apischemamapper.Schema
		schemaPtr    *apischemamapper.Schema
		schemaConfig studiomodels.SingleSubmitConfigDetails
	)
	groupID := userjourney.GetGroupID(userID)
	if groupID == "" {
		logger.WithUser(userID).Error(fmt.Errorf("invalid group ID"))
		return nil, fmt.Errorf("invalid group ID")
	}

	groupConfig, customErr = fetchGroup(context.TODO(), userID, groupID)
	if customErr != nil {
		logger.WithUser(userID).Errorln(customErr)
		return nil, customErr.Err
	}

	isUserCoApplicant, err := multiuserloanrelations.IsUserCoApplicant(userID)
	if err != nil {
		return nil, err
	}

	if isUserCoApplicant {
		schemaConfig = groupConfig.GetChildGroupSingleSubmitConfigDetails(moduleName, resourceName)
	} else {
		schemaConfig = groupConfig.GetSingleSubmitConfigDetails(moduleName, resourceName)
	}

	if schemaConfig.ConfigID == "" {
		logger.WithUser(userID).Error(fmt.Errorf("invalid resourceName"))
		return nil, fmt.Errorf("invalid resourceName")
	}
	if !schemaConfig.IsStaged {
		schemaPtr, _, err = apischemamapper.GetSchema(sourceEntityID, partnerdataschema.TypeTemporalSingleSubmit, -1, -1, schemaConfig.ConfigID)
		if err != nil {
			logger.WithUser(userID).Error(err)
			return nil, err
		}
		schema = *schemaPtr
	} else {
		stagedConfig, customErr := getStagingService().GetGroupConfigData(context.TODO(), groupID, schemaConfig.ConfigID, studioconstants.ConfigTypeSingleSubmit)
		if customErr != nil {
			logger.WithUser(userID).Errorln(customErr)
			return nil, customErr.Err
		}
		err = json.Unmarshal(stagedConfig.ConfigData, &schema)
		if err != nil {
			logger.WithUser(userID).Errorln(err)
			return nil, err
		}
		schema.SchemaID = schemaConfig.ConfigID
	}
	return &schema, nil
}

func ValidateUserData(userObj authentication.UserStruct, userData map[string]interface{}, moduleName string, ctx context.Context, currentIndex int, sectionName string) (map[string]interface{}, error, bool) {
	sourceEntityID := userObj.SourceEntityID
	iiflAgg := journey.IIFLAgg(sourceEntityID, false)

	// set sourceEntityID to IIFL for PL DSAs
	// remove this when different workflow definition is needed for DSAs
	if iiflAgg.IsAgg {
		switch iiflAgg.LoanType {
		case constants.LoanTypePersonalLoan:
			sourceEntityID = constants.IIFLID
		case constants.LoanTypeBusinessLoan:
			sourceEntityID = constants.IIFLBLID
		}
	}
	userWorkflows, err := userworkflows.Get(userObj.UserID, moduleName)
	if err != nil {
		logger.WithUser(userObj.UserID).Error(err)
		return nil, err, false
	}
	module, err := runnerutils.FindKeyByPoll(temporalclient.Client, userWorkflows.WorkflowID, userWorkflows.RunID, runnerconstants.WorkflowOutput, runnerconstants.WorkflowModuleKey, 1000, 4)
	if err != nil {
		logger.WithUser(userObj.UserID).Errorln(err)
		return nil, err, false
		// workflowRun, inErr := temporalclient.Client.DescribeWorkflowExecution(ctx, userWorkflows.WorkflowID, "")
		// if inErr != nil {
		// 	logger.WithUser(userObj.UserID).Errorln("Error Describing Workflow:", inErr)
		// 	panic(inErr)
		// }

		// wfStatus := workflowRun.GetWorkflowExecutionInfo().Status
		// if wfStatus == enums.WORKFLOW_EXECUTION_STATUS_CANCELED || wfStatus == enums.WORKFLOW_EXECUTION_STATUS_TERMINATED ||
		// 	wfStatus == enums.WORKFLOW_EXECUTION_STATUS_TIMED_OUT || wfStatus == enums.WORKFLOW_EXECUTION_STATUS_UNSPECIFIED {
		// 	inErr = userworkflows.UpdateStatus(userObj.UserID, constants.TemporalStatusFailed, userWorkflows.WorkflowID, userWorkflows.RunID)
		// 	if inErr != nil {
		// 		logger.WithUser(userObj.UserID).Error(inErr)
		// 		panic(inErr)
		// 	}
		// 	return nil, err, false
		// }

	}
	moduleMap := module.(map[string]interface{})
	sectionConfig, err := sectionconfig.Get(sourceEntityID, moduleName, moduleMap[runnerconstants.WorkflowSubModuleNameTag].(string))
	if err != nil {
		logger.WithUser(userObj.UserID).Error(err)
		return nil, err, false
	}
	var builderUserDataMap BuilderUserData
	err = runnerutils.DecodeToStruct(sectionConfig.SectionData, &builderUserDataMap)
	if err != nil {
		logger.WithUser(userObj.UserID).Error(err)
		return nil, err, false
	}
	isFinalIndex := false
	validatedUserData := make(map[string]interface{})
	for i, section := range builderUserDataMap.Sections {
		if i > currentIndex {
			break
		}
		//Validation need to be performed for the current section only.
		if section.Key != sectionName {
			continue
		}
		isFinalIndex = section.IsFinal
		for _, component := range section.Components {
			for _, field := range component.Fields {
				value, ok := userData[field.Key]
				if !ok && field.Validation.Required {
					return nil, fmt.Errorf("required field %s key not found", field.Key), isFinalIndex
				}
				if !field.Validation.Required && (value.(string) == "" || !ok) {
					continue
				}
				if field.Validation.Type == "regex" {
					if len(field.Validation.Params) < 1 {
						continue
					}
					regex := field.Validation.Params[0]
					if !regexp.MustCompile(regex).MatchString(value.(string)) {
						return nil, fmt.Errorf("%s value does not match regex", field.Key), isFinalIndex
					}
				}
				if len(field.Options) > 0 {
					if field.InputType == "radio" {
						var options []int
						for _, option := range field.Options {
							var radioOption RadioOptions
							//Here we need to do this as we were not able to directly type cast option into RadioOptions.
							option, err := json.Marshal(option)
							if err != nil {
								return nil, fmt.Errorf("error marshaling for %s ", field.Key), isFinalIndex
							}
							err = json.Unmarshal(option, &radioOption)
							if err != nil {
								return nil, fmt.Errorf("error unmarshaling for %s ", field.Key), isFinalIndex
							}
							options = append(options, radioOption.Value)
						}
						if !slices.Contains(options, int(value.(float64))) {
							return nil, fmt.Errorf("%s value does not match any option", field.Key), isFinalIndex
						}
					} else {
						var options []string
						for _, option := range field.Options {
							options = append(options, option.(string))
						}
						if !slices.Contains(options, value.(string)) {
							return nil, fmt.Errorf("%s value does not match any option", field.Key), isFinalIndex
						}
					}
				}
				//TODO: Need to add date format in the section config and use that format to validate
				if field.Validation.Type == "date" {
					if _, err := time.Parse("2006-01-02", value.(string)); err != nil {
						return nil, fmt.Errorf("%s not in YYYY-MM-DD format", field.Key), isFinalIndex
					}
				}
				validatedUserData[field.Key] = value
			}
		}
		// return here as validated the data for the given section
		return validatedUserData, nil, isFinalIndex
	}
	return validatedUserData, nil, isFinalIndex
}
