package mapper

import (
	"encoding/json"
	"reflect"
	"testing"

	"github.com/test-go/testify/assert"
)

func TestJQMapper(t *testing.T) {
	type testStruct struct {
		input  map[string]any
		config map[string]any
		output map[string]any
	}
	var testCases = []testStruct{
		{
			input: map[string]any{
				"field1": "value1",
				"field2": "value2",
			},
			config: map[string]any{
				"mapped_field1": ".field1",
				"mapped_field2": ".field2",
			},
			output: map[string]any{
				"mapped_field1": "value1",
				"mapped_field2": "value2",
			},
		},
		{
			input: map[string]any{
				"field1": "value1",
				"nested": map[string]any{
					"nested_field_2": "value2",
					"nested_field_3": "value3",
				},
			},
			config: map[string]any{
				"mapped_field1": ".field1",
				"nesting_level_1": map[string]any{
					"nesting_level_2": map[string]any{
						"nesting_level_3_field_2": ".nested.nested_field_2",
						"nesting_level_3_field_3": ".nested.nested_field_3",
					},
				},
			},
			output: map[string]any{
				"mapped_field1": "value1",
				"nesting_level_1": map[string]any{
					"nesting_level_2": map[string]any{
						"nesting_level_3_field_2": "value2",
						"nesting_level_3_field_3": "value3",
					},
				},
			},
		},
		{
			input: map[string]any{
				"field1": "value1",
				"field2": "value2",
				"field3": "value3",
			},
			config: map[string]any{
				"field1": "if .field1 == \"value1\" then .field1 else null end",
				"field2": "if .field2 == \"some_value_2\" then .field2 else null end",
				"field3": "if .field3 == \"value3\" then .field3 else null end | @base64",
			},
			output: map[string]any{
				"field1": "value1",
				"field2": nil,
				"field3": "dmFsdWUz",
			},
		},
		{
			input: map[string]any{
				"field1": "value1",
				"field2": map[string]any{
					"dynamic_key_1": "dynamic_value_1",
					"dynamic_key_2": "dynamic_value_2",
					"dynamic_key_3": "dynamic_value_3",
				},
			},
			config: map[string]any{
				"field1":   ".field1",
				".dynamic": ".field2 | to_entries | .[]",
			},
			output: map[string]any{
				"field1":        "value1",
				"dynamic_key_1": "dynamic_value_1",
				"dynamic_key_2": "dynamic_value_2",
				"dynamic_key_3": "dynamic_value_3",
			},
		},
	}
	for index, test := range testCases {
		output := map[string]any{}
		if JQMapper(test.input, test.config, output); !reflect.DeepEqual(output, test.output) {
			t.Errorf("Case int - %d: Output %v not equal to expected %v", index+1, output, test.output)
		}
	}

}

func TestJQMapperParameterized(t *testing.T) {

	type testCase struct {
		Input          map[string]any
		Config         map[string]any
		Output         map[string]any
		ExpectedOutput map[string]any
		ExpectedError  error
	}

	t.Run("success case - no errors", func(t *testing.T) {

		inputMap := make(map[string]any)
		configMap := make(map[string]any)
		outputMap := make(map[string]any)
		expectedOutputMap := make(map[string]any)

		json.Unmarshal([]byte(SampleData), &inputMap)
		json.Unmarshal([]byte(SampleConfigSuccess), &configMap)
		json.Unmarshal([]byte(SampleOutputSuccess), &expectedOutputMap)

		tc := testCase{
			Input:          inputMap,
			Output:         outputMap,
			Config:         configMap,
			ExpectedError:  nil,
			ExpectedOutput: expectedOutputMap,
		}
		err := JQMapperParameterized(tc.Input, tc.Config, tc.Output)
		assert.Equal(t, tc.ExpectedError, err)
		assert.NotNil(t, tc.Output)

		// Below check failing due to data type mismatch in DeepEqual
		// if !reflect.DeepEqual(tc.ExpectedOutput, outputMap) {
		// 	for key, expectedValue := range tc.ExpectedOutput {
		// 		outputValue := outputMap[key]
		// 		if !reflect.DeepEqual(expectedValue, outputValue) {
		// 			t.Logf("Key: %s\nExpected: %#v (%T)\nGot: %#v (%T)\n\n", key, expectedValue, expectedValue, outputValue, outputValue)
		// 		}
		// 	}
		// 	t.Errorf("output %v not equal to expected %v", outputMap, tc.ExpectedOutput)
		// }
	})

	// TODO: Add error test cases

}
