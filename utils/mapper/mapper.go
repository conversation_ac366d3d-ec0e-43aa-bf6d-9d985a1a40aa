package mapper

import (
	"finbox/go-api/constants"
	"finbox/go-api/functions/logger"
	"fmt"
	"strings"

	"github.com/itchyny/gojq"
)

// JQMapper maps input to output using a mapping config
func JQMapper(input map[string]any, jqMappingConfig map[string]any, output map[string]any) error {
	for key, value := range jqMappingConfig {
		switch val := value.(type) {
		case []any:
			temp := make([]map[string]any, 0)
			var isJSONArray bool
			for index := range val {
				tempMap := make(map[string]any)
				_, ok := val[index].(map[string]any)
				if ok {
					isJSONArray = true
					err := JQMapper(input, val[index].(map[string]any), tempMap)
					if err != nil {
						return err
					}
					temp = append(temp, tempMap)
				}
			}
			if isJSONArray {
				output[key] = temp
			} else {
				output[key] = val
			}
		case map[string]any:
			output[key] = make(map[string]any)
			JQMapper(input, val, output[key].(map[string]any))
		case string:
			if val == constants.EmptyString {
				err := fmt.Errorf("invalid jq expression: %s, ", val)
				logger.Log.Errorln(err)
				return err
			}
			query, err := gojq.Parse(val)
			if err != nil {
				logger.Log.Errorln(err)
				return err
			}
			iter := query.Run(input)
			for {
				v, ok := iter.Next()
				if !ok {
					break
				}
				if err, ok := v.(error); ok {
					logger.Log.Errorln(err)
					return err
				}
				if key == ".dynamic" {
					if v == nil {
						continue
					}
					vMap, ok := v.(map[string]interface{})
					if !ok {
						err = fmt.Errorf("invalid jq expression: %s, value obtained: %v", val, v)
						logger.Log.Errorln(err)
						return err
					}
					output[vMap["key"].(string)] = vMap["value"]
				} else {
					output[key] = v
				}

			}
		default:
			return fmt.Errorf("invalid type %s received in config", val)
		}
	}
	return nil
}

// JQMapperParameterized maps input to output using a mapping config - where parameterized expressions can be passed within ${ }
func JQMapperParameterized(input map[string]any, jqMappingConfig map[string]any, output map[string]any) error {
	for key, value := range jqMappingConfig {
		switch val := value.(type) {
		case []any:
			temp := make([]map[string]any, 0)
			var isJSONArray bool
			for index := range val {
				tempMap := make(map[string]any)
				_, ok := val[index].(map[string]any)
				if ok {
					isJSONArray = true
					err := JQMapperParameterized(input, val[index].(map[string]any), tempMap)
					if err != nil {
						return err
					}
					temp = append(temp, tempMap)
				}
			}
			if isJSONArray {
				output[key] = temp
			} else {
				output[key] = val
			}
		case []map[string]any:
			temp := make([]map[string]any, 0)

			for index := range val {
				tempMap := make(map[string]any)

				err := JQMapperParameterized(input, val[index], tempMap)
				if err != nil {
					return err
				}
				temp = append(temp, tempMap)

			}
			output[key] = temp

		case map[string]any:
			output[key] = make(map[string]any)
			JQMapperParameterized(input, val, output[key].(map[string]any))
		case string:
			if !(strings.HasPrefix(val, "${") && strings.HasSuffix(val, "}")) {
				output[key] = val
				continue
			}

			val = strings.TrimSuffix(strings.TrimPrefix(val, "${"), "}")

			query, err := gojq.Parse(val)
			if err != nil {
				logger.Log.Errorln(err)
				return err
			}
			iter := query.Run(input)
			for {
				v, ok := iter.Next()
				if !ok {
					break
				}
				if err, ok := v.(error); ok {
					logger.Log.Errorln(err)
					return err
				}
				if key == ".dynamic" {
					if v == nil {
						continue
					}
					vMap, ok := v.(map[string]interface{})
					if !ok {
						err = fmt.Errorf("invalid jq expression: %s, value obtained: %v", val, v)
						logger.Log.Errorln(err)
						return err
					}
					output[vMap["key"].(string)] = vMap["value"]
				} else {
					output[key] = v
				}

			}
		default:
			output[key] = val
		}
	}
	return nil
}
