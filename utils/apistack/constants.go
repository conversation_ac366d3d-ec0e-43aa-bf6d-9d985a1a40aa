package apistack

import (
	"finbox/go-api/constants"
	"fmt"
)

const (
	APIRouteUUD         = "v2/user/details/update"
	APIRouteOffers      = "v2/user/offers"
	APIRouteAcceptOffer = "v2/user/acceptOffer"
)

var (
	APIIDTriggerBRE  = getAPIID(APIRouteOffers, "POST")
	APIIDUUUD        = getAPIID(APIRouteUUD, "POST")
	APIIDAcceptOffer = getAPIID(APIRouteAcceptOffer, "POST")
)

var APIIDToSubmoduleMapping = map[string]string{
	APIIDUUUD:        constants.SubModuleUUD,
	APIIDTriggerBRE:  constants.SubModuleBureau,
	APIIDAcceptOffer: constants.SubModuleAcceptOffer,
}

var APIIDToRouteConfigMapping = map[string]*APIRouteConfig{
	APIIDUUUD: {
		APIRoute:       APIRouteUUD,
		IsSyncAPI:      true,
		SuccessMessage: "success",
	},
	APIIDTriggerBRE: {
		APIRoute:       APIRouteOffers,
		IsSyncAPI:      false,
		SuccessMessage: "success",
	},
	APIIDAcceptOffer: {
		APIRoute:       APIRouteAcceptOffer,
		IsSyncAPI:      true,
		SuccessMessage: "ok",
	},
}

var (
	ErrSubModuleAPIMappingNotFound = fmt.Errorf("submodule to api mapping not found")
)
