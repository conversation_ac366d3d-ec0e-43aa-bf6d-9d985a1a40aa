package apistack

import (
	"context"
	"database/sql"
	"errors"
	"finbox/go-api/common/datawebhook"
	"finbox/go-api/constants"
	"finbox/go-api/errorHandler"
	"finbox/go-api/functions/apistack/apiresult"
	"finbox/go-api/functions/commonutils"
	"finbox/go-api/functions/journey"
	"finbox/go-api/functions/logger"
	"finbox/go-api/models/apistackworkflow"
	"finbox/go-api/models/datawebhookfailedtriggers"
	"finbox/go-api/models/featureflag"
	"finbox/go-api/models/jobs"
	"finbox/go-api/models/sourceentity"
	"finbox/go-api/models/userapimodulemapping"
	"finbox/go-api/models/userworkflows"
	"finbox/go-api/utils/general"
	"finbox/go-api/utils/stacktrace"
	tsmworkflowutils "finbox/go-api/utils/workflowutils/tsm"
	"fmt"
	"strings"
	"time"
)

// IsModuleCalledAtLeastOnce checks if a specific module was completed at least once before
func IsModuleCalledAtLeastOnce(ctx context.Context, userID, moduleName string) (bool, error) {
	if _, err := userapimodulemapping.GetLatestByModule(ctx, userID, moduleName); err != nil {
		logger.WithUser(userID).Errorln(err)
		if err == sql.ErrNoRows {
			return false, nil
		}
		errorHandler.ReportToSentryWithFields(map[string]interface{}{
			"user_id":     userID,
			"module_name": moduleName,
		}, err)
		return false, err
	}
	return true, nil
}

// getFirstStepInWorkflow returns the first index of given endpoint in workflow
func getFirstStepInWorkflow(endpoint string, apiStackWorkflow map[int]apistackworkflow.WorkFlowStep) int {
	minStep := -1
	for index := range apiStackWorkflow {
		step := apiStackWorkflow[index]
		if step.Endpoint == endpoint {
			if minStep == -1 {
				minStep = index
				continue
			}
			if index < minStep {
				minStep = index
			}
		}
	}
	return minStep
}

// isAPICalledBefore checks if the given endpoint in the given workflow has been called in user joruney
func isAPICalledBefore(ctx context.Context, endpoint, userID string, apiStackWorkflow map[int]apistackworkflow.WorkFlowStep) (bool, error) {
	// get the first invocation of this endpoint
	workflowStep := getFirstStepInWorkflow(endpoint, apiStackWorkflow)
	if workflowStep == -1 {
		return false, errors.New("endpoint not found in apiStackWorkflow")
	}
	// check from DB if the  invocation is completed
	isCompleted, err := userapimodulemapping.IsStepCompleted(ctx, userID, workflowStep)
	if err != nil {
		logger.WithUser(userID).Error(err)
	}
	return isCompleted, err
}

// IsValidState checks if current API is called in the right sequence
// and returns isValidAPICall, isCalledBefore, lastActivity, nextStep, err if any
func IsValidState(ctx context.Context, userID string, sourceEntityID string, escapedPath string) (bool, bool, userapimodulemapping.APIUserModuleMapping, int, error) {
	if tsmworkflowutils.IsTSMFlow(userID) {
		isValid, apiCalledBefore, workflowStep, err := tsmworkflowutils.IsValidStateWithTSMWorkflow(ctx, userID, sourceEntityID, escapedPath[3:])
		return isValid, apiCalledBefore, userapimodulemapping.APIUserModuleMapping{}, workflowStep, err
	}

	lastActivity, err := userapimodulemapping.GetLast(userID)
	if err != nil {
		return false, false, lastActivity, 0, err
	}

	apiStackWorkflow, err := GetAPIStackWorkflowForUser(ctx, userID, sourceEntityID)
	if err != nil {
		logger.WithUser(userID).Errorln(err)
		return false, false, lastActivity, 0, err
	}

	lastStep := lastActivity.WorkflowStep
	nextStep := lastStep + 1
	endPoint := escapedPath[3:] // remove "/v2" from path
	for apiStackWorkflow[nextStep].Optional && apiStackWorkflow[nextStep].Endpoint != endPoint {
		nextStep++
	}

	// Check if this api is called before
	isCalledBefore, err := isAPICalledBefore(ctx, endPoint, userID, apiStackWorkflow)
	if err != nil {
		return false, false, lastActivity, 0, err
	}
	if isCalledBefore {
		logger.DebugWithUser(userID, "API has been called before")
	}

	logger.WithUser(userID).Info("Last activity : ", lastActivity)
	logger.WithUser(userID).Info("Endpoint : ", endPoint)
	switch endPoint {
	// If the current endpoint is next step in workflow steps
	case apiStackWorkflow[nextStep].Endpoint:
		// API state is invalid if last step is still not completed
		if lastActivity.ModuleStatus != userapimodulemapping.StatusCompleted {
			return false, isCalledBefore, lastActivity, nextStep, nil
		} else {
			// API state is only valid if last step was completed and has status `StatusCompleted`
			return true, isCalledBefore, lastActivity, nextStep, nil
		}
	// If the current endpoint getting called is lastEndpoint in workflow step
	case apiStackWorkflow[lastStep].Endpoint:
		// API state is only valid if last step was failed and has user is hitting it again for retries
		if lastActivity.ModuleStatus != userapimodulemapping.StatusCompleted {
			return true, isCalledBefore, lastActivity, lastStep, nil
		}
	}

	return false, isCalledBefore, lastActivity, 0, nil
}

// GetAPIStackWorkflowForUser returns the current active API Stack Workflow for the user.
// The workflow can be different for each user depending on the feature flag.
// TODO: Remove this once moved to TSM
func GetAPIStackWorkflowForUser(ctx context.Context, userID, sourceEntityID string) (apiStackWorkflow map[int]apistackworkflow.WorkFlowStep, err error) {
	// Pick the right apistack workflow based on feature flag
	// This is done in order to prevent updating the workflow for running journeys
	// TODO: Once moved to TSM, delete this.
	if featureflag.Get(userID, journey.FlagUnsignedAgreementAPIStackWorkflow) {
		// Pick the workflow for PhonePe with unsigned agreement  module
		// TODO: This is a temporary change, we need to remove this once moved to TSM
		apiStackWorkflow, err = apistackworkflow.GetByWorkflowName("PhonePe - EDI - Unsigned Agreement")
		if err != nil {
			logger.WithUser(userID).Errorln(err)
			return nil, err
		}
	} else if featureflag.Get(userID, journey.FlagPreQualificationAPIStackWorkflow) {
		// Pick the workflow for PayTM with pre-qualification module
		sourceEntityName, err := sourceentity.GetSourceEntityName(sourceEntityID)
		if err != nil {
			logger.WithUser(userID).Errorln(err)
			return nil, err
		}
		apiWorkflowName := fmt.Sprintf("%s - EDI - Pre-Qualification", sourceEntityName)
		apiStackWorkflow, err = apistackworkflow.GetByWorkflowName(apiWorkflowName)
		if err != nil {
			logger.WithUser(userID).Errorln(err)
			return nil, err
		}
	} else {
		// Default, pick the workflow based on source entity
		apiStackWorkflow, err = apistackworkflow.GetBySourceEntity(sourceEntityID)
		if err != nil {
			logger.WithUser(userID).Errorln(err)
			return nil, err
		}
	}

	return apiStackWorkflow, nil
}

func GetMetadataForCurrentModule(userID string, sourceEntityID string) (apistackworkflow.Metadata, error) {
	lastActivity, err := userapimodulemapping.GetLast(userID)
	if err != nil {
		return apistackworkflow.Metadata{}, err
	}
	apiStackworkflow, err := GetAPIStackWorkflowForUser(context.TODO(), userID, sourceEntityID)
	if err != nil {
		return apistackworkflow.Metadata{}, err
	}

	lastStep := lastActivity.WorkflowStep
	nextStep := lastStep + 1

	return apiStackworkflow[nextStep].Metadata, err

}

func GetNextModule(userID, sourceEntityID string) (apistackworkflow.WorkFlowStep, error) {
	lastActivity, err := userapimodulemapping.GetLast(userID)
	if err != nil {
		return apistackworkflow.WorkFlowStep{}, err
	}

	wf, err := GetAPIStackWorkflowForUser(context.TODO(), userID, sourceEntityID)
	if err != nil {
		return apistackworkflow.WorkFlowStep{}, err
	}

	lastStep := lastActivity.WorkflowStep
	nextStep := lastStep + 1

	logger.DebugWithUser(userID, fmt.Sprintf("---- last step: %v\n", lastActivity))
	logger.DebugWithUser(userID, fmt.Sprintf("---- next step: %v\n", wf[nextStep]))

	return wf[nextStep], nil
}

// IsValidProgram checks if the program sent is valid, It also updates the given program name
func IsValidProgram(program *string) bool {
	*program = strings.ToUpper(*program)
	return general.InArr(*program, constants.ValidAPIStackPrograms)
}

// IsValidSubMode checks if the sub mode sent is valid
func IsValidSubMode(subMode *string) bool {
	*subMode = strings.ToUpper(*subMode)
	return general.InArr(*subMode, constants.ValidRepaymentSubModes)
}

// IsValidOKYCSource checks if the okyc source is valid
func IsValidOKYCSource(okycSource *string) bool {
	*okycSource = strings.ToUpper(*okycSource)
	return general.InArr(*okycSource, constants.ValidAPIStackOKYCSources)
}

// IsValidManualKYCStatus checks if the status sent is valid
func IsValidManualKYCStatus(status *string) bool {
	*status = strings.ToUpper(*status)
	return general.InArr(*status, constants.ValidManualKYCStatus)
}

// IsValidKey checks if the Key sent is valid
func IsValidKey(key *string) bool {
	return general.InArr(*key, constants.ValidReferenceDataKeys)
}

// RecoverWithWebhook recovers from a panic in Job goroutine
// It marks the passed job as failed and sends failure webhook to client
func RecoverWithWebhook(job jobs.Job, sourceEntityID string) {
	if e := recover(); e != nil {
		var err error
		errMsg, ok := e.(string)
		if ok {
			err = job.Fail(errMsg)
		} else {
			err = job.Fail("")
		}
		if err != nil {
			logger.WithUser(job.UserID).Error(err)
		}
		err, ok = e.(error)
		if ok {
			logger.WithUser(job.UserID).Errorln("recovered: ", e)
			logger.WithUser(job.UserID).Errorln(stacktrace.StackTrace(stacktrace.Wrap(err)))
			errorHandler.ReportToSentryWithoutRequest(err)
		}
		// Send failed webhook to client
		webhookPayload, err := apiresult.GetJobResult(context.Background(), job.JobID, sourceEntityID, job.JobName)
		if err != nil {
			logger.WithUser(job.UserID).Errorln(err, "failed to prepare webhook payload")
			datawebhookfailedtriggers.FailedToTriggerWebhooks(context.TODO(), "", sourceEntityID, job.JobID, job.UserID)
			return
		}
		err = datawebhook.Trigger(context.Background(), webhookPayload, sourceEntityID, job.JobName, job.UserID)
		if err != nil {
			logger.WithUser(job.UserID).Errorln(err, "error while triggering webhook for client")
			datawebhookfailedtriggers.FailedToTriggerWebhooks(context.TODO(), webhookPayload, sourceEntityID, job.JobID, job.UserID)
			return
		}
	}
}

func GetReferenceIDForAPIModule(apiModuleStr, userID, sourceEntityID string) (referenceID string, err error) {
	// Attempt to fetch job information
	job, err := commonutils.GetJobForClientByUserAndJobName(userID, apiModuleStr, sourceEntityID)
	if err == nil {
		// If no error, return jobID as referenceID
		return job.JobID, err
	}

	// Log the error if not related to "no rows" scenario
	if err != sql.ErrNoRows {
		logger.WithSourceEntity(sourceEntityID).Error(err)
		return "", err
	}

	// At this point, err is sql.ErrNoRows. Try to fetch workflow information
	workFlowData, err := userworkflows.Get(userID, apiModuleStr)
	if err != nil {
		// Log the error and return if unable to fetch workflow data
		logger.WithSourceEntity(sourceEntityID).Error(fmt.Errorf("[GetReferenceIDForAPIModule] unable to fetch workflow data: %w, user: %s, module: %s", err, userID, apiModuleStr))
		return "", err
	}

	// send workflow ID as referenceID
	return workFlowData.ID, nil
}

func ToAPIStackDateFormat(t time.Time) string {
	return t.Format(constants.APIStackDateFormat)
}
