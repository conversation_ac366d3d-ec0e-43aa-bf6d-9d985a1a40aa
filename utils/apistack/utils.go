package apistack

import (
	"context"
	"database/sql"
	"errors"
	"finbox/go-api/common/datawebhook"
	"finbox/go-api/constants"
	"finbox/go-api/errorHandler"
	"finbox/go-api/functions/apistack/apiresult"
	"finbox/go-api/functions/commonutils"
	"finbox/go-api/functions/journey"
	"finbox/go-api/functions/logger"
	"finbox/go-api/internal/util/tags"
	"finbox/go-api/models/apistackworkflow"
	"finbox/go-api/models/datawebhookfailedtriggers"
	"finbox/go-api/models/featureflag"
	"finbox/go-api/models/jobs"
	"finbox/go-api/models/sourceentity"
	"finbox/go-api/models/userapimodulemapping"
	"finbox/go-api/models/usersubmodulemapping"
	"finbox/go-api/models/userworkflows"
	"finbox/go-api/temporal/temporalutility"
	"finbox/go-api/utils/general"
	"finbox/go-api/utils/stacktrace"
	"finbox/go-api/utils/workflowutils/tsm"
	"fmt"
	"strings"
	"time"

	"github.com/finbox-in/road-runner/runner"
)

// IsModuleCalledAtLeastOnce checks if a specific module was completed at least once before
func IsModuleCalledAtLeastOnce(ctx context.Context, userID, moduleName string) (bool, error) {
	if _, err := userapimodulemapping.GetLatestByModule(ctx, userID, moduleName); err != nil {
		logger.WithUser(userID).Errorln(err)
		if err == sql.ErrNoRows {
			return false, nil
		}
		errorHandler.ReportToSentryWithFields(map[string]interface{}{
			"user_id":     userID,
			"module_name": moduleName,
		}, err)
		return false, err
	}
	return true, nil
}

// getFirstStepInWorkflow returns the first index of given endpoint in workflow
func getFirstStepInWorkflow(endpoint string, apiStackWorkflow map[int]apistackworkflow.WorkFlowStep) int {
	minStep := -1
	for index := range apiStackWorkflow {
		step := apiStackWorkflow[index]
		if step.Endpoint == endpoint {
			if minStep == -1 {
				minStep = index
				continue
			}
			if index < minStep {
				minStep = index
			}
		}
	}
	return minStep
}

// isAPICalledBefore checks if the given endpoint in the given workflow has been called in user joruney
func isAPICalledBefore(ctx context.Context, endpoint, userID string, apiStackWorkflow map[int]apistackworkflow.WorkFlowStep) (bool, error) {
	// get the first invocation of this endpoint
	workflowStep := getFirstStepInWorkflow(endpoint, apiStackWorkflow)
	if workflowStep == -1 {
		return false, errors.New("endpoint not found in apiStackWorkflow")
	}
	// check from DB if the  invocation is completed
	isCompleted, err := userapimodulemapping.IsStepCompleted(ctx, userID, workflowStep)
	if err != nil {
		logger.WithUser(userID).Error(err)
	}
	return isCompleted, err
}

// IsValidState checks if current API is called in the right sequence
// and returns isValidAPICall, isCalledBefore, lastActivity, nextStep, err if any
func IsValidState(ctx context.Context, userID string, sourceEntityID string, escapedPath string) (bool, bool, userapimodulemapping.APIUserModuleMapping, int, error) {
	if tsm.IsTSMFlow(userID) {
		isValid, apiCalledBefore, workflowStep, err := tsm.IsValidStateWithTSMWorkflow(ctx, userID, sourceEntityID, escapedPath[3:])
		return isValid, apiCalledBefore, userapimodulemapping.APIUserModuleMapping{}, workflowStep, err
	}

	lastActivity, err := userapimodulemapping.GetLast(userID)
	if err != nil {
		return false, false, lastActivity, 0, err
	}

	apiStackWorkflow, err := GetAPIStackWorkflowForUser(ctx, userID, sourceEntityID)
	if err != nil {
		logger.WithUser(userID).Errorln(err)
		return false, false, lastActivity, 0, err
	}

	lastStep := lastActivity.WorkflowStep
	nextStep := lastStep + 1
	endPoint := escapedPath[3:] // remove "/v2" from path
	for apiStackWorkflow[nextStep].Optional && apiStackWorkflow[nextStep].Endpoint != endPoint {
		nextStep++
	}

	// Check if this api is called before
	isCalledBefore, err := isAPICalledBefore(ctx, endPoint, userID, apiStackWorkflow)
	if err != nil {
		return false, false, lastActivity, 0, err
	}
	if isCalledBefore {
		logger.DebugWithUser(userID, "API has been called before")
	}

	logger.WithUser(userID).Info("Last activity : ", lastActivity)
	logger.WithUser(userID).Info("Endpoint : ", endPoint)
	switch endPoint {
	// If the current endpoint is next step in workflow steps
	case apiStackWorkflow[nextStep].Endpoint:
		// API state is invalid if last step is still not completed
		if lastActivity.ModuleStatus != userapimodulemapping.StatusCompleted {
			return false, isCalledBefore, lastActivity, nextStep, nil
		} else {
			// API state is only valid if last step was completed and has status `StatusCompleted`
			return true, isCalledBefore, lastActivity, nextStep, nil
		}
	// If the current endpoint getting called is lastEndpoint in workflow step
	case apiStackWorkflow[lastStep].Endpoint:
		// API state is only valid if last step was failed and has user is hitting it again for retries
		if lastActivity.ModuleStatus != userapimodulemapping.StatusCompleted {
			return true, isCalledBefore, lastActivity, lastStep, nil
		}
	}

	return false, isCalledBefore, lastActivity, 0, nil
}

// GetAPIStackWorkflowForUser returns the current active API Stack Workflow for the user.
// The workflow can be different for each user depending on the feature flag.
// TODO: Remove this once moved to TSM
func GetAPIStackWorkflowForUser(ctx context.Context, userID, sourceEntityID string) (apiStackWorkflow map[int]apistackworkflow.WorkFlowStep, err error) {
	// Pick the right apistack workflow based on feature flag
	// This is done in order to prevent updating the workflow for running journeys
	// TODO: Once moved to TSM, delete this.
	if featureflag.Get(userID, journey.FlagUnsignedAgreementAPIStackWorkflow) {
		// Pick the workflow for PhonePe with unsigned agreement  module
		// TODO: This is a temporary change, we need to remove this once moved to TSM
		apiStackWorkflow, err = apistackworkflow.GetByWorkflowName("PhonePe - EDI - Unsigned Agreement")
		if err != nil {
			logger.WithUser(userID).Errorln(err)
			return nil, err
		}
	} else if featureflag.Get(userID, journey.FlagPreQualificationAPIStackWorkflow) {
		// Pick the workflow for PayTM with pre-qualification module
		sourceEntityName, err := sourceentity.GetSourceEntityName(sourceEntityID)
		if err != nil {
			logger.WithUser(userID).Errorln(err)
			return nil, err
		}
		apiWorkflowName := fmt.Sprintf("%s - EDI - Pre-Qualification", sourceEntityName)
		apiStackWorkflow, err = apistackworkflow.GetByWorkflowName(apiWorkflowName)
		if err != nil {
			logger.WithUser(userID).Errorln(err)
			return nil, err
		}
	} else {
		// Default, pick the workflow based on source entity
		apiStackWorkflow, err = apistackworkflow.GetBySourceEntity(sourceEntityID)
		if err != nil {
			logger.WithUser(userID).Errorln(err)
			return nil, err
		}
	}

	return apiStackWorkflow, nil
}

func GetMetadataForCurrentModule(userID string, sourceEntityID string) (apistackworkflow.Metadata, error) {
	lastActivity, err := userapimodulemapping.GetLast(userID)
	if err != nil {
		return apistackworkflow.Metadata{}, err
	}
	apiStackworkflow, err := GetAPIStackWorkflowForUser(context.TODO(), userID, sourceEntityID)
	if err != nil {
		return apistackworkflow.Metadata{}, err
	}

	lastStep := lastActivity.WorkflowStep
	nextStep := lastStep + 1

	return apiStackworkflow[nextStep].Metadata, err

}

func GetNextModule(userID, sourceEntityID string) (apistackworkflow.WorkFlowStep, error) {
	lastActivity, err := userapimodulemapping.GetLast(userID)
	if err != nil {
		return apistackworkflow.WorkFlowStep{}, err
	}

	wf, err := GetAPIStackWorkflowForUser(context.TODO(), userID, sourceEntityID)
	if err != nil {
		return apistackworkflow.WorkFlowStep{}, err
	}

	lastStep := lastActivity.WorkflowStep
	nextStep := lastStep + 1

	logger.DebugWithUser(userID, fmt.Sprintf("---- last step: %v\n", lastActivity))
	logger.DebugWithUser(userID, fmt.Sprintf("---- next step: %v\n", wf[nextStep]))

	return wf[nextStep], nil
}

// IsValidProgram checks if the program sent is valid, It also updates the given program name
func IsValidProgram(program *string) bool {
	*program = strings.ToUpper(*program)
	return general.InArr(*program, constants.ValidAPIStackPrograms)
}

// IsValidSubMode checks if the sub mode sent is valid
func IsValidSubMode(subMode *string) bool {
	*subMode = strings.ToUpper(*subMode)
	return general.InArr(*subMode, constants.ValidRepaymentSubModes)
}

// IsValidOKYCSource checks if the okyc source is valid
func IsValidOKYCSource(okycSource *string) bool {
	*okycSource = strings.ToUpper(*okycSource)
	return general.InArr(*okycSource, constants.ValidAPIStackOKYCSources)
}

// IsValidManualKYCStatus checks if the status sent is valid
func IsValidManualKYCStatus(status *string) bool {
	*status = strings.ToUpper(*status)
	return general.InArr(*status, constants.ValidManualKYCStatus)
}

// IsValidKey checks if the Key sent is valid
func IsValidKey(key *string) bool {
	return general.InArr(*key, constants.ValidReferenceDataKeys)
}

// RecoverWithWebhook recovers from a panic in Job goroutine
// It marks the passed job as failed and sends failure webhook to client
func RecoverWithWebhook(job jobs.Job, sourceEntityID string) {
	if e := recover(); e != nil {
		var err error
		errMsg, ok := e.(string)
		if ok {
			err = job.Fail(errMsg)
		} else {
			err = job.Fail("")
		}
		if err != nil {
			logger.WithUser(job.UserID).Error(err)
		}
		err, ok = e.(error)
		if ok {
			logger.WithUser(job.UserID).Errorln("recovered: ", e)
			logger.WithUser(job.UserID).Errorln(stacktrace.StackTrace(stacktrace.Wrap(err)))
			errorHandler.ReportToSentryWithoutRequest(err)
		}
		// Send failed webhook to client
		webhookPayload, err := apiresult.GetJobResult(context.Background(), job.JobID, sourceEntityID, job.JobName)
		if err != nil {
			logger.WithUser(job.UserID).Errorln(err, "failed to prepare webhook payload")
			datawebhookfailedtriggers.FailedToTriggerWebhooks(context.TODO(), "", sourceEntityID, job.JobID, job.UserID)
			return
		}
		err = datawebhook.Trigger(context.Background(), webhookPayload, sourceEntityID, job.JobName, job.UserID)
		if err != nil {
			logger.WithUser(job.UserID).Errorln(err, "error while triggering webhook for client")
			datawebhookfailedtriggers.FailedToTriggerWebhooks(context.TODO(), webhookPayload, sourceEntityID, job.JobID, job.UserID)
			return
		}
	}
}

func GetReferenceIDForAPIModule(apiModuleStr, userID, sourceEntityID string) (referenceID string, err error) {
	// Attempt to fetch job information
	job, err := commonutils.GetJobForClientByUserAndJobName(userID, apiModuleStr, sourceEntityID)
	if err == nil {
		// If no error, return jobID as referenceID
		return job.JobID, err
	}

	// Log the error if not related to "no rows" scenario
	if err != sql.ErrNoRows {
		logger.WithSourceEntity(sourceEntityID).Error(err)
		return "", err
	}

	// At this point, err is sql.ErrNoRows. Try to fetch workflow information
	workFlowData, err := userworkflows.Get(userID, apiModuleStr)
	if err != nil {
		// Log the error and return if unable to fetch workflow data
		logger.WithSourceEntity(sourceEntityID).Error(fmt.Errorf("[GetReferenceIDForAPIModule] unable to fetch workflow data: %w, user: %s, module: %s", err, userID, apiModuleStr))
		return "", err
	}

	// send workflow ID as referenceID
	return workFlowData.ID, nil
}

func ToAPIStackDateFormat(t time.Time) string {
	return t.Format(constants.APIStackDateFormat)
}

func getAPIID(route string, method string) string {
	return fmt.Sprintf("%s:%s", route, method)
}

// getSubModuleForRoute returns the submodule for the given route
// from the APIStackRouteToSubmoduleMapping map, each API are mapped to a submodule
// which can be added to any module.
func getSubModuleForRoute(route string, method string) (subModule string, err error) {
	apiID := getAPIID(route, method)
	subModule, ok := APIIDToSubmoduleMapping[apiID]
	if !ok {
		return "", ErrSubModuleAPIMappingNotFound
	}
	return subModule, nil
}

// GetAPIRouteConfig checks if the given route is a sync API
func GetAPIRouteConfig(route string, method string) (apiConfig *APIRouteConfig, err error) {
	apiID := getAPIID(route, method)
	apiConfig, ok := APIIDToRouteConfigMapping[apiID]
	if !ok {
		return nil, ErrSubModuleAPIMappingNotFound
	}
	return apiConfig, nil
}

// getAllModuleStatusFromUserWorkflows polls for the currentModule status for all the given user workflows
// it returns the module status for all the user workflows, the polling for module status is done in parallel
func getAllModuleStatusFromUserWorkflows(ctx context.Context, userWorkflows []userworkflows.UserWorkflow) (workflowStatus []temporalutility.TSMAPIStackModuleInfo, err error) {
	type pollModuleResult struct {
		ModuleStatus *temporalutility.TSMAPIStackModuleInfo
		Error        error
	}

	// cencellable context to cancel the parallel operations if any of them fails
	ctx, cancel := context.WithCancel(ctx)
	defer cancel()

	// create a new channel for pushing results
	results := make(chan pollModuleResult, len(userWorkflows))

	for _, userWorkflow := range userWorkflows {
		go func(userWorkflow userworkflows.UserWorkflow) {
			moduleStatus, err := temporalutility.FetchModuleStatusFromWorkflow(ctx, temporalutility.FetchModuleStatusParams{
				UWF:                &userWorkflow,
				WaitBetweenCallsMs: 50,
				MaxAttempts:        20,
			})
			results <- pollModuleResult{
				ModuleStatus: moduleStatus,
				Error:        err,
			}
		}(userWorkflow)
	}

	for i := 0; i < len(userWorkflows); i++ {
		result := <-results
		// TODO: there could be case where one of the workflow was cancelled or something,
		// we won't want to penalize the user in case of closing of a non relevant workflow
		if result.Error != nil {
			// cancel the context to stop the parallel operations
			cancel()
			// return error for the operation
			return nil, result.Error
		}
		workflowStatus = append(workflowStatus, *result.ModuleStatus)
	}

	return workflowStatus, nil
}

// moduleNamesFromTSMMetadata is a mapper function to get the module names from the TSM metadata
// this is passed to GetLatestByUserIDStatusAndModules to get the latest user workflow instances
func moduleNamesFromTSMMetadata(nextModules []runner.TemporalStateMachineMetadata) []string {
	moduleNames := []string{}
	for _, module := range nextModules {
		moduleNames = append(moduleNames, module.ModuleName)
	}
	return moduleNames
}

// ValidTSMResult is the result of the IsValidTSMState function
type ValidTSMResult struct {
	IsValidStep          bool
	IsCalledBefore       bool
	WorkflowModuleStatus *temporalutility.TSMAPIStackModuleInfo
}

type ValidTSMStateParams struct {
	UserID         string
	SourceEntityID string
	APIPath        string
	APIMethod      string
}

// IsValidTSMState checks if the current state for the API module is valid based on TSM module mapping
func IsValidTSMState(ctx context.Context, params ValidTSMStateParams) (*ValidTSMResult, error) {

	// Get the mapping of the submodule from the current route
	currentSubModule, err := getSubModuleForRoute(params.APIPath, params.APIMethod)
	if err != nil {
		return nil, err
	}

	// Query TSM to get the all possible current module for user without any Source filter to get both journey and api stack modules
	filters := tsm.NewFilterBuilder().
		WithTransitionType(tsm.FilterTransitionType(constants.TSMTransitionTypeModule)).
		Build()
	_, nextModules, err := tsm.GetNextTSMModuleForFilters(ctx, params.UserID, filters...)
	if err != nil {
		return nil, err
	}

	if err != nil {
		return nil, err
	}
	logger.WithUser(params.UserID).Debugln("nextModules", nextModules)

	// get all the user workflow instances to poll current module status from them
	moduleNames := moduleNamesFromTSMMetadata(nextModules)
	userWorkflows, err := userworkflows.GetLatestByUserIDStatusAndModules(ctx, params.UserID, constants.TemporalStatusRunning, moduleNames)
	if err != nil {
		return nil, err
	}
	logger.WithUser(params.UserID).Debugln("userWorkflows", userWorkflows)
	// fetch the workflow status for all the user workflows
	allRunningWorkflowStatus, err := getAllModuleStatusFromUserWorkflows(ctx, userWorkflows)
	if err != nil {
		return nil, err
	}
	logger.WithUser(params.UserID).Debugln("allRunningWorkflowStatus", general.AnyToJSONString(allRunningWorkflowStatus))
	// Check if the current submodule matches the submodule we got from CurrentModule
	// if validAPI move to next middleware
	for _, workflowStatus := range allRunningWorkflowStatus {
		// API state is only valid if the submodule name matches the current submodule
		// and the status is waiting for user input
		if workflowStatus.SubModuleName == currentSubModule {
			logger.WithUser(params.UserID).Debugln("workflowStatus", general.AnyToJSONString(workflowStatus))
			// TODO: this needs to be made dynamic once the builder changes are done to pass
			// the submodule status in the serverless workflow
			isValidStep := true     // workflowStatus.SubModuleStatus == constants.SubModuleStatusPending
			isCalledBefore := false // workflowStatus.SubModuleStatus == constants.SubModuleStatusCompleted
			return &ValidTSMResult{
				IsValidStep:          isValidStep,
				IsCalledBefore:       isCalledBefore,
				WorkflowModuleStatus: &workflowStatus,
			}, nil
		}
	}

	// if invalidAPI
	// we need to check if API was called before based on submodule mappings
	submoduleMapping, err := usersubmodulemapping.GetSubmoduleByStatus(ctx, params.UserID, currentSubModule, usersubmodulemapping.StatusCompleted)
	if err != nil && err != sql.ErrNoRows {
		return nil, err
	}
	logger.WithUser(params.UserID).Debugln("submoduleMapping", general.AnyToJSONString(submoduleMapping))
	// If found entry for the current submodule, then it is valid
	if submoduleMapping != nil {
		return &ValidTSMResult{
			IsCalledBefore: true,
			WorkflowModuleStatus: &temporalutility.TSMAPIStackModuleInfo{
				SubModuleName:   submoduleMapping.SubModule,
				ModuleName:      submoduleMapping.Module,
				ModuleStatus:    constants.ModuleStatusCompleted,
				SubModuleStatus: constants.SubModuleStatusCompleted,
				UserWorkflowID:  submoduleMapping.UserWorkflowID,
			},
		}, nil
	}

	// If the submodule is not found, then it is invalid step
	return &ValidTSMResult{
		IsValidStep: false,
	}, nil
}

// SendModuleSignalForSyncAPI sends a signal to TSM and waits for the module to move.
func SendModuleSignalForSyncAPI(ctx context.Context, params SendModuleSignalForSyncAPIParams) error {
	// signal name
	signalName := params.ModuleInfo.SignalName

	if _, err := temporalutility.SignalWorkflow(ctx, params.UserID, params.ModuleInfo.ModuleName, signalName, tsm.SourceAPIStack, nil, nil); err != nil {
		logger.WithUser(params.UserID).Errorln(err)
		return err
	}

	// 2. Wait for the module to move.

	if err := tsm.VerifyTSMModuleMovement(ctx, params.UserID); err != nil {
		logger.WithUser(params.UserID).Errorln(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{
			"userID":       params.UserID,
			"moduleName":   params.ModuleInfo.ModuleName,
			"moduleStatus": constants.UserModuleStatusCompleted,
		}, err)
		return err
	}

	return nil
}

func GetSignalNameForCurrentSubModule(ctx context.Context) (signalName string, err error) {
	workflowModuleStatus, ok := tags.GetTagValue(ctx, tags.WorkflowModuleStatus).(*temporalutility.TSMAPIStackModuleInfo)
	if !ok {
		err = errors.New("workflowModuleStatus not found in context")
		logger.WithContext(ctx).Errorln(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{}, err)
		return "", err
	}

	signalName = workflowModuleStatus.SignalName
	return signalName, nil
}
