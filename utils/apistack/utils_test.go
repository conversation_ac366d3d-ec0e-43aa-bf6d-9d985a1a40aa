package apistack

import (
	"context"
	"encoding/json"
	"finbox/go-api/internal/util/tags"
	"finbox/go-api/models/apistackworkflow"
	"finbox/go-api/temporal/temporalutility"
	"testing"
)

func TestGetFirstInvocationInWorkflow(t *testing.T) {
	var workflow map[int]apistackworkflow.WorkFlowStep
	sampleWorkflow := `{ "1": { "endpoint": "/user/create", "optional": false, "method": "POST", "metadata": { "invocation": 1, "version": 1 } }, "2": { "endpoint": "/user/details/update", "optional": false, "method": "POST", "metadata": { "invocation": 1, "version": 1 } }, "3": { "endpoint": "/kyc/okyc", "optional": false, "method": "POST", "metadata": { "program": "PhonePay KYC OKYC", "version": 1 } }, "4": { "endpoint": "/kyc/identity", "optional": false, "method": "POST", "metadata": { "invocation": 1, "version": 1 } }, "5": { "endpoint": "/user/offers", "optional": false, "method": "POST", "Metadata": { "invocation": 1, "version": 1 } }, "6": { "endpoint": "/user/acceptOffer", "optional": false, "method": "POST", "Metadata": { "invocation": 1, "version": 1 } }, "7": { "endpoint": "/loan/agreement", "optional": false, "method": "POST", "metadata": { "invocation": 1, "version": 1 } }, "8": { "endpoint": "/user/details/update", "optional": false, "method": "POST", "metadata": { "invocation": 2, "version": 1 } }, "9": { "endpoint": "/loan/disburse", "optional": false, "method": "POST", "metadata": { "invocation": 1, "version": 1 } } }`
	err := json.Unmarshal([]byte(sampleWorkflow), &workflow)
	if err != nil {
		t.Fatal("fail to load sample data")
	}
	tests := []struct {
		name     string
		endpoint string
		index    int
	}{
		{name: "Only One Endpoint", endpoint: "/user/offers", index: 5},
		{name: "Two Endpoint", endpoint: "/user/details/update", index: 2},
		{name: "Non Existent Endpoint", endpoint: "/user/someUnknownEndpoint", index: -1},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			index := getFirstStepInWorkflow(test.endpoint, workflow)
			if index != test.index {
				t.Errorf("Expected index: %v, but got: %v for endpoint: %s", test.index, index, test.endpoint)
			}
		})
	}
}

func TestGetSignalNameForCurrentSubModule(t *testing.T) {
	tests := []struct {
		name             string
		setupContext     func() context.Context
		expectedSignal   string
		expectedError    bool
		expectedErrorMsg string
	}{
		{
			name: "Success - Valid WorkflowModuleStatus with SignalName",
			setupContext: func() context.Context {
				ctx := context.Background()
				moduleInfo := &temporalutility.TSMAPIStackModuleInfo{
					ModuleName:      "test-module",
					ModuleStatus:    "running",
					SubModuleName:   "test-submodule",
					SubModuleStatus: "pending",
					SignalName:      "test-signal-name",
					UserWorkflowID:  "workflow-123",
				}
				return tags.SetTagValue(ctx, tags.WorkflowModuleStatus, moduleInfo)
			},
			expectedSignal:   "test-signal-name",
			expectedError:    false,
			expectedErrorMsg: "",
		},
		{
			name: "Failure - No WorkflowModuleStatus in context",
			setupContext: func() context.Context {
				return context.Background()
			},
			expectedSignal:   "",
			expectedError:    true,
			expectedErrorMsg: "workflowModuleStatus not found in context",
		},
		{
			name: "Failure - Wrong type in context",
			setupContext: func() context.Context {
				ctx := context.Background()
				// Set a different type instead of TSMAPIStackModuleInfo
				wrongTypeValue := "this-is-not-the-right-type"
				return tags.SetTagValue(ctx, tags.WorkflowModuleStatus, wrongTypeValue)
			},
			expectedSignal:   "",
			expectedError:    true,
			expectedErrorMsg: "workflowModuleStatus not found in context",
		},
		{
			name: "Failure - Nil value in context",
			setupContext: func() context.Context {
				ctx := context.Background()
				return tags.SetTagValue(ctx, tags.WorkflowModuleStatus, nil)
			},
			expectedSignal:   "",
			expectedError:    true,
			expectedErrorMsg: "workflowModuleStatus not found in context",
		},
		{
			name: "Success - Empty SignalName",
			setupContext: func() context.Context {
				ctx := context.Background()
				moduleInfo := &temporalutility.TSMAPIStackModuleInfo{
					ModuleName:      "test-module",
					ModuleStatus:    "running",
					SubModuleName:   "test-submodule",
					SubModuleStatus: "pending",
					SignalName:      "", // Empty signal name
					UserWorkflowID:  "workflow-123",
				}
				return tags.SetTagValue(ctx, tags.WorkflowModuleStatus, moduleInfo)
			},
			expectedSignal:   "",
			expectedError:    false,
			expectedErrorMsg: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := tt.setupContext()

			signalName, err := GetSignalNameForCurrentSubModule(ctx)

			// Check error expectations
			if tt.expectedError {
				if err == nil {
					t.Errorf("Expected error but got none")
				} else if err.Error() != tt.expectedErrorMsg {
					t.Errorf("Expected error message '%s', but got '%s'", tt.expectedErrorMsg, err.Error())
				}
			} else {
				if err != nil {
					t.Errorf("Expected no error but got: %v", err)
				}
			}

			// Check signal name
			if signalName != tt.expectedSignal {
				t.Errorf("Expected signal name '%s', but got '%s'", tt.expectedSignal, signalName)
			}
		})
	}
}
