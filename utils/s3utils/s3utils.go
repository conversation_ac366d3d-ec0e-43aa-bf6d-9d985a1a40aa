package s3utils

import (
	"finbox/go-api/conf"
	"finbox/go-api/constants"
	"finbox/go-api/errorHandler"
	"finbox/go-api/functions/journey"
	"finbox/go-api/functions/logger"
	"finbox/go-api/infra/s3"
	"finbox/go-api/models/featureflag"
	"finbox/go-api/models/users"
	"strings"
)

func CopyObjectToClientS3(objectKey string) error {
	defer errorHandler.RecoveryNoResponse()

	var (
		userData                                  *users.User
		err                                       error
		parentSourceEntityID                      string
		lenderIDList                              []string
		sourceBucket, destinationBucket           string
		sourceBucketFound, destinationBucketFound bool
	)

	userData, err = getUserDataFromObjectKey(objectKey)
	if err != nil {
		logger.Log.Errorln(err)
		return err
	}

	if userData == nil {
		logger.Log.Errorln("user not found from objectKey ", objectKey)
		return nil
	}

	if !featureflag.Get(userData.ID, journey.FlagS3Sync) {
		logger.WithUser(userData.ID)
		return nil
	}

	lenderIDList, err = getLenderIDsFromUserID(userData.ID, userData.SourceEntityID)
	if err != nil {
		logger.WithUser(userData.ID).Errorln(err)
		return err
	}

	parentSourceEntityID = userData.SourceEntityID
	journey.GetParentSourceEntityID(userData.ID, &parentSourceEntityID)

	logger.WithUser(userData.ID).Info("[S3_SYNC] CopyObjectToClientS3, objectKey: ", objectKey, " parentSourceEntityID: ", parentSourceEntityID, " lenderIDList: ", lenderIDList)

	for idx := range lenderIDList {
		if s3SyncEnabled, clientS3Data := conf.S3BucketSyncDetails(parentSourceEntityID, lenderIDList[idx]); s3SyncEnabled && clientS3Data != nil {
			sourceBucket, sourceBucketFound = clientS3Data["sourceBucketName"]
			destinationBucket, destinationBucketFound = clientS3Data["destinationBucketName"]

			logger.WithUser(userData.ID).Info("[S3_SYNC] CopyObjectToClientS3, sourceBucket: ", sourceBucket, " destinationBucket: ", destinationBucket)

			if sourceBucketFound && destinationBucketFound {
				copyObject := s3.NewCopyObject(sourceBucket, destinationBucket, objectKey)

				if lenderIDList[idx] == constants.MFLBLID {
					// ToDo: Doing this hacky solution for now just to use the existing flow
					// because can't tell the client to do the changes at their end.
					// Remove the newRoot prefix if present
					const newRoot = constants.MFLDailyReportFileRoot
					trimmedObjectKey := objectKey
					if strings.HasPrefix(objectKey, newRoot) {
						trimmedObjectKey = strings.TrimPrefix(objectKey, newRoot)
						trimmedObjectKey = "mfl-daily-reports/" + trimmedObjectKey
					}
					copyObject.SetDestinationObjectKey("customer_files/" + trimmedObjectKey)
					logger.WithUser(userData.ID).Info("[S3_SYNC] CopyObjectToClientS3, objectKey: ", objectKey, " destinationObjectKey: ", copyObject.DestinationObjectKey)
				}

				logger.WithUser(userData.ID).Info("[S3_SYNC] CopyObjectToClientS3, copyObject: ", copyObject)

				if err = copyObject.Copy(); err != nil {
					logger.WithUser(userData.ID).Info("[S3_SYNC] CopyObjectToClientS3, objectKey: ", objectKey, " copy failed with error: ", err)
					logger.WithUser(userData.ID).Errorln(err)
					return err
				}

				logger.WithUser(userData.ID).Info("[S3_SYNC] CopyObjectToClientS3, objectKey: ", objectKey, " copied to client s3 bucket")
			}
		}
	}

	return nil
}
