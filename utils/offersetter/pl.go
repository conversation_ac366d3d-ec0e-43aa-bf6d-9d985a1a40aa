package offersetter

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"finbox/go-api/common/usersutil"
	"finbox/go-api/constants"
	"finbox/go-api/errorHandler"
	"finbox/go-api/functions/activity"
	"finbox/go-api/functions/journey"
	"finbox/go-api/functions/lenders/dmi"
	"finbox/go-api/functions/lenders/tatacapital"
	"finbox/go-api/functions/lenderservice"
	"finbox/go-api/functions/logger"
	"finbox/go-api/functions/retry"
	"finbox/go-api/functions/services/deviceConnect"
	"finbox/go-api/functions/services/tdl"
	"finbox/go-api/functions/structs"
	"finbox/go-api/functions/underwriting"
	"finbox/go-api/models/featureflag"
	"finbox/go-api/models/lendervariables"
	"finbox/go-api/models/loanapplication"
	"finbox/go-api/models/loanoffertemplate"
	"finbox/go-api/models/personalloanoffer"
	"finbox/go-api/models/preselectedlender"
	"finbox/go-api/models/userjourney"
	"finbox/go-api/models/userloandetails"
	"finbox/go-api/models/usermodulemapping"
	"finbox/go-api/models/users"
	"finbox/go-api/utils/calc"
	"finbox/go-api/utils/general"
	"finbox/go-api/utils/lockutils"
	"finbox/go-api/utils/workflowutils"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/jmoiron/sqlx"
)

func acceptPersonalLoanOffer(ctx *context.Context, offerID, userID, sourceEntityID string, amount float64, interest float64, tenure int, processingFee float64, ipAddress string, loanApplicationNumber string) (string, *structs.CustomError) {

	// TODO: Add max emi checks - sentinel or other logic ?

	if !personalloanoffer.Exists(userID, offerID) {
		return InvalidOfferID, nil
	}
	offer := personalloanoffer.Get(offerID)
	switch offer.OfferStatus {
	case constants.OfferStatusInactive:
		logger.WithUser(userID).Info("offer setter accept personal offer cont")
		return InactiveOffer, nil
	case constants.OfferStatusExpired:
		logger.WithUser(userID).Info("offer setter accept personal offer cont expired")
		return constants.ErrStringOfferExpired, &structs.CustomError{
			Err:      errors.New(constants.ErrStringOfferExpired),
			HTTPCode: constants.ErrStringToStatusCodeMapping[constants.ErrStringOfferExpired],
			ErrCode:  constants.ErrStringToStatusCodeStringMapping[constants.ErrStringOfferExpired],
		}
	case constants.OfferStatusIsAccepted:
		logger.WithUser(userID).Info("offer setter accept personal offer cont")
		return OfferAlreadyAccepted, nil
	}
	if amount > offer.MaxAmount || amount < offer.MinAmount {
		return InvalidOfferAmount, nil
	}
	if tenure > offer.MaxTenure || tenure < offer.MinTenure {
		return InvalidOfferTenure, nil
	}

	// Offer slab based check for tenure and amount
	if journey.IsMuthootEDIPartner(sourceEntityID) || journey.IsMFLEMIPartner(sourceEntityID) {
		latestOffer, err := personalloanoffer.GetLatestForUser(userID)
		if err != nil {
			logger.WithUser(userID).Errorln(err)
			return "", &structs.CustomError{Err: err, HTTPCode: http.StatusInternalServerError}
		}

		var offerMeta structs.APIStackOfferMetaData
		err = json.Unmarshal([]byte(latestOffer.OfferMetadata), &offerMeta)
		if err != nil {
			logger.WithUser(userID).Error(err)
			err = fmt.Errorf("unable to process offerMeta data, offerID: %s, userID: %s", latestOffer.LoanOfferID, userID)
			errorHandler.ReportToSentryWithoutRequest(err)
			return "", &structs.CustomError{Err: err, HTTPCode: http.StatusInternalServerError}
		}

		pass := offerMeta.CheckOfferSlabAmount(amount, tenure)
		if !pass {
			return InvalidAmountForTenure, nil
		}
	}

	if journey.IsABFLPLSourcing(sourceEntityID) {
		emi, _, _ := calc.GetEMI(offer.Method, amount, tenure, interest, time.Now(), sourceEntityID, offer.LenderID, userID)

		isValid, err := OfferSlabValidatorPL(offer.OfferMetadataObj.Offers, SelectedOfferStruct{
			Amount:        amount,
			Tenure:        tenure,
			Interest:      interest,
			ProcessingFee: offer.ProcessingFee,
			EMI:           emi,
		})

		if err != nil {
			logger.WithUser(userID).Error(err)
			return err.Error(), nil
		}
		if !isValid {
			return constants.ErrStringInvalidPayload, nil
		}
	}

	offerTemplate, err := loanoffertemplate.GetTemplate(*ctx, sourceEntityID, offer.LenderID)
	if err != nil {
		logger.WithUser(userID).Error(err)
		return "", &structs.CustomError{Err: err, HTTPCode: http.StatusInternalServerError}
	}

	var roi float64
	if interest > 0 {
		roi = interest
	} else {
		roi = offer.Interest
	}
	var processingfeeVal float64
	if processingFee <= 0 {
		processingfeeVal = calc.CalculateProcessingFee(amount, offer.ProcessingFee, offer.ProcessingFeeType)
	} else {
		processingfeeVal = calc.CalculateProcessingFee(amount, processingFee, offer.ProcessingFeeType)
	}
	var emi float64
	if journey.IsTdlTcapPLFlow(userID, sourceEntityID, offer.LenderID, "") {

		variables, err := tatacapital.GetOfferVariables(userID, sourceEntityID, offer.OfferMetadataObj, amount)
		if err != nil {
			logger.WithUser(userID).Error(err)
			return "", &structs.CustomError{Err: err, HTTPCode: http.StatusInternalServerError}

		}
		processingfeeVal, _ = variables["processingFee"].(float64)
		roi = offer.OfferMetadataObj.Interest
		err = personalloanoffer.Update(nil, offerID, processingFee, variables["roi"].(float64), "FLAT")
		if err != nil {
			logger.WithUser(userID).Error(err)
			return "", &structs.CustomError{Err: err, HTTPCode: http.StatusInternalServerError}
		}
		emi, _, _ = calc.GetEMI(offer.Method, amount, tenure, interest, time.Now(), sourceEntityID, offer.LenderID, userID)
	}
	loanApplicationID := general.GetUUID()
	err = preselectedlender.Set(userID, offer.LenderID)
	if err != nil {
		logger.WithUser(userID).Error(err)
		return "", &structs.CustomError{Err: err, HTTPCode: http.StatusInternalServerError}
	}

	status := constants.LoanStatusFresh
	if journey.SkipFreshLoan(sourceEntityID) {
		status = constants.LoanStatusDetails
	}

	if json.Valid([]byte(offer.OfferMetadata)) {
		err = json.Unmarshal([]byte(offer.OfferMetadata), &offer.OfferMetadataObj)
		if err != nil {
			logger.WithUser(userID).Error(err)
			return "", &structs.CustomError{Err: err, HTTPCode: http.StatusInternalServerError}
		}
	}

	dynamicChargesBytes, err := json.Marshal(offer.OfferMetadataObj.DynamicCharges)
	if err != nil {
		logger.WithUser(userID).Error(err)
		return "", &structs.CustomError{Err: err, HTTPCode: http.StatusInternalServerError}
	}

	loanApplicationNo := ""
	if sourceEntityID == constants.TataPLID {
		if general.InArr(offer.LenderID, []string{constants.TataCapitalID}) {
			dynamicVars, err := lendervariables.GetDynamicVariables(userID, constants.TataCapitalID)
			if err != nil {
				logger.WithUser(userID).Error(err)
				return "", &structs.CustomError{Err: err, HTTPCode: http.StatusInternalServerError}
			}
			if val, ok := dynamicVars["plWebtopId"]; ok {
				loanApplicationNo = val.(string)
			} else {
				loanApplicationNo = journey.GenerateLoanApplicationNo(sourceEntityID)
			}
		} else if general.InArr(offer.LenderID, []string{constants.HDFCLenderID}) {
			resp, err := lendervariables.GetByStatus(userID, offer.LenderID, lendervariables.LenderVariableStatusActive)
			if err != nil {
				logger.WithUser(userID).Error(err)
				return "", &structs.CustomError{Err: err, HTTPCode: http.StatusInternalServerError}
			}
			loanApplicationNo = resp.ReferenceID
		}
	}

	if loanApplicationNo == "" {
		if loanApplicationNumber != "" && offer.LenderID == constants.PrefrID {
			loanApplicationNo = loanApplicationNumber
		} else {
			loanApplicationNo = journey.GenerateLoanApplicationNo(sourceEntityID)
		}
	}

	// Create loan application
	loan := loanapplication.StructForSet{
		ID:                        loanApplicationID,
		UserID:                    userID,
		SourceEntityID:            sourceEntityID,
		CreatedBy:                 userID,
		LoanApplicationNo:         loanApplicationNo,
		LoanType:                  constants.LoanTypePersonalLoan,
		LoanOfferID:               offerID,
		Amount:                    amount,
		Tenure:                    tenure,
		Interest:                  &roi,
		Status:                    &status,
		LenderID:                  offer.LenderID,
		ProcessingFee:             &processingfeeVal,
		GST:                       offerTemplate.GST,
		UnsignedAgreementTemplate: offerTemplate.UnsignedAgreement,
		SignedAgreementTemplate:   offerTemplate.SignedAgreement,
		AppliedAmount:             amount, // This can be removed later as redundant to amount
		AppliedTenure:             tenure, // This can be removed later as redundant to tenure
		DynamicCharges:            string(dynamicChargesBytes),
	}

	if journey.IsCoApplicantJourney(userID, sourceEntityID) {
		loan.LoanVariant = constants.LoanVariantCoApplication
	}
	if emi > 0 {
		loan.EMI = emi
	}
	loanID, _ := loanapplication.GetActiveByLenderAndUser(userID, offer.LenderID)
	var isLoanUpdated bool

	tx, err := database.Beginx()
	if err != nil {
		logger.WithUser(userID).Error(err)
		return "", &structs.CustomError{Err: err, HTTPCode: http.StatusInternalServerError}
	}
	defer tx.Rollback()

	if sourceEntityID == constants.TataPLID && offer.LenderID == constants.TDLKreditBeeID && loanID != "" {
		loanApplicationID = loanID
		err = loanapplication.Update(tx, loanapplication.StructForSet{
			ID:                        loanApplicationID,
			UserID:                    userID,
			SourceEntityID:            sourceEntityID,
			LoanType:                  constants.LoanTypePersonalLoan,
			LoanOfferID:               offerID,
			Amount:                    amount,
			Tenure:                    tenure,
			Interest:                  &roi,
			LenderID:                  offer.LenderID,
			ProcessingFee:             &processingfeeVal,
			GST:                       offerTemplate.GST,
			UnsignedAgreementTemplate: offerTemplate.UnsignedAgreement,
			SignedAgreementTemplate:   offerTemplate.SignedAgreement,
			AppliedAmount:             amount, // This can be removed later as redundant to amount
			AppliedTenure:             tenure, // This can be removed later as redundant to tenure
			DynamicCharges:            string(dynamicChargesBytes),
		})
		if err != nil {
			logger.WithUser(userID).Error(err)
			return "", &structs.CustomError{Err: err, HTTPCode: http.StatusInternalServerError}
		}
		isLoanUpdated = true
	} else {
		err = loanapplication.Insert(ctx, tx, loan)
		if err != nil {
			logger.WithUser(userID).Error(err)
			return "", &structs.CustomError{Err: err, HTTPCode: http.StatusInternalServerError}
		}
	}

	if journey.SkipFreshLoan(sourceEntityID) {
		err = userloandetails.Insert(ctx, loanApplicationID, userID, userID, tx)
		if err != nil {
			logger.WithUser(userID).Error(err)
			return "", &structs.CustomError{Err: err, HTTPCode: http.StatusInternalServerError}
		}
	}
	err = attachOfferToLoan(tx, offerID, loanApplicationID)
	if err != nil {
		logger.WithUser(userID).Error(err)
		return "", &structs.CustomError{Err: err, HTTPCode: http.StatusInternalServerError}
	}

	err = personalloanoffer.UpdateStatus(tx, offerID, userID, constants.OfferStatusIsAccepted)
	if err != nil {
		logger.WithUser(userID).Error(err)
		return "", &structs.CustomError{Err: err, HTTPCode: http.StatusInternalServerError}
	}

	if offer.LenderID == constants.PoonawallaFincorpID {
		lenderVariablesNullable := lendervariables.LenderVariablesStructNullable{
			UserID:            userID,
			LenderID:          offer.LenderID,
			LoanApplicationID: sql.NullString{Valid: true, String: loanApplicationID},
		}
		err = lendervariables.UpdateByStatus(tx, lendervariables.LenderVariableStatusActive, lenderVariablesNullable)
		if err != nil {
			tx.Rollback()
			logger.WithUser(userID).Error(err)
			return "", &structs.CustomError{Err: err, HTTPCode: http.StatusInternalServerError}
		}
	}

	dateTimeNowString := general.GetTimeStampString()
	lenderName := constants.LenderNamesMap[offer.LenderID]
	description := journey.GetCustomDescription(userID, "", sourceEntityID, offer.LenderID, "")
	if description == "" {
		description = fmt.Sprintf(`{"lender": "%s", "amount":"%.2f"}`, lenderName, amount)
	} else {
		description = fmt.Sprintf(`%s, "amount":"%.2f"}`, strings.TrimSuffix(description, "}"), amount)
	}
	go activity.ActivityLogger(userID, sourceEntityID, userID, constants.EntityTypeCustomer, constants.ActivityOfferAccepted, description, loanApplicationID, dateTimeNowString, false)

	if offer.LenderID != constants.TDLKreditBeeID {
		err = usermodulemapping.Create(tx, userID, userID, usermodulemapping.OfferSelection, constants.UserModuleStatusCompleted, loanApplicationID)
		if err != nil {
			logger.WithUser(userID).Error(err)
			return "", &structs.CustomError{Err: err, HTTPCode: http.StatusInternalServerError}
		}
	}

	if err = tx.Commit(); err != nil {
		logger.WithUser(userID).Error(err)
		return "", &structs.CustomError{Err: err, HTTPCode: http.StatusInternalServerError}
	}

	go func(userID string) {
		userObj, err := users.Get(userID)
		if err != nil {
			errorHandler.ReportToSentryWithoutRequest(err)
		}
		usersutil.UpdateUserSource(userID, userObj.Source, map[string]interface{}{})
	}(userID)

	switch offer.LenderID {
	case constants.DMIID:
		err = workflowutils.UpdateWorkFlow(userID, sourceEntityID, constants.WorkflowDmiTdlPL)
		if err != nil {
			logger.WithUser(userID).Error(err)
			return "", &structs.CustomError{Err: err, HTTPCode: http.StatusInternalServerError}
		}
	case constants.TataCapitalID:
		newWorkflowName := constants.WorkflowTDLPLFresh
		if journey.IsTemporalFlow(userID, sourceEntityID, usermodulemapping.PersonalInfo) {
			newWorkflowName = constants.WorkflowTDLPLFreshPersonalInfo
		}
		err = workflowutils.UpdateWorkFlow(userID, sourceEntityID, newWorkflowName)
		if err != nil {
			logger.WithUser(userID).Error(err)
			return "", &structs.CustomError{Err: err, HTTPCode: http.StatusInternalServerError}
		}
	case constants.PrefrID:
		err = workflowutils.UpdateWorkFlow(userID, sourceEntityID, constants.WorkflowPrefrPL)
		if err != nil {
			logger.WithUser(userID).Error(err)
			return "", &structs.CustomError{Err: err, HTTPCode: http.StatusInternalServerError}
		}
	case constants.KisshtID:
		err = workflowutils.UpdateWorkFlow(userID, sourceEntityID, constants.WorkflowKisshtTdlPl)
		if err != nil {
			logger.WithUser(userID).Error(err)
			return "", &structs.CustomError{Err: err, HTTPCode: http.StatusInternalServerError}
		}
	}

	dateTimeString := general.GetTimeStampString()
	if isLoanUpdated {
		go activity.ActivityLogger(userID, sourceEntityID, userID, constants.EntityTypeSystem, constants.ActivityLoanApplicationUpdated, fmt.Sprintf(`{"lender": "%s"}`, lenderName), loanApplicationID, dateTimeString, false)
		return "", nil
	}
	go activity.ActivityLogger(userID, sourceEntityID, "", constants.EntityTypeSystem, constants.ActivityLoanApplicationCreated, fmt.Sprintf(`{"lender": "%s"}`, lenderName), loanApplicationID, dateTimeString, false)
	return "", nil
}

func attachOfferToLoan(tx *sqlx.Tx, offerID, loanApplicationID string) error {
	var offer personalloanoffer.PersonalLoanOffer

	// Get latest offer template
	query := `WITH latest_template AS (
    SELECT t.gst, t.signed_agreement, t.unsigned_agreement, t.source_entity_id, t.lender_id
    FROM loan_offer_template t
    WHERE (t.source_entity_id, t.lender_id, t.created_at) IN (
        SELECT source_entity_id, lender_id, MAX(created_at)
        FROM loan_offer_template
        GROUP BY source_entity_id, lender_id
    )
)
SELECT lt.gst, lt.signed_agreement, lt.unsigned_agreement
FROM personal_loan_offer p
JOIN latest_template lt
ON p.source_entity_id = lt.source_entity_id 
   AND p.lender_id = lt.lender_id
WHERE p.loan_offer_id = $1;`
	err := database.Get(&offer, query, offerID)
	if err != nil {
		return err
	}

	err = loanapplication.Update(tx, loanapplication.StructForSet{
		ID:                        loanApplicationID,
		GST:                       offer.GST,
		SignedAgreementTemplate:   offer.SignedAgreement,
		UnsignedAgreementTemplate: offer.UnsignedAgreement,
	})
	return err
}

func selectPersonalLoanOffer(ctx *context.Context, offerID, userID, sourceEntityID, ipAddress string) (string, error) {

	lockKey := lockutils.ConstructKey(userID, "tdl_offer_select")
	customErr := lockutils.Lock(lockKey)

	if customErr != nil {
		logger.WithUser(userID).Error(customErr)
		return "", customErr.Err
	}

	defer lockutils.UnLock(lockKey, nil)

	// check if offer already selected for user
	if personalloanoffer.GetOfferStatus(offerID) == constants.OfferStatusSelected {
		return "user has already accepted an offer", nil
	}

	if !personalloanoffer.Exists(userID, offerID) {
		return "invalid offerID", nil
	}

	offer := personalloanoffer.Get(offerID)
	switch offer.OfferStatus {
	case constants.OfferStatusInactive:
		return "inactive offer", nil
	case constants.OfferStatusExpired:
		return "offer is expired", nil
	case constants.OfferStatusIsAccepted:
		return "offer is already accepted", nil
	case constants.OfferStatusSelected:
		return "offer is already selected", nil
	}
	err := personalloanoffer.UpdateStatus(nil, offerID, userID, constants.OfferStatusSelected)
	if err != nil {
		log.Errorln(err)
		return "", err
	}

	lenderName := constants.LenderNamesMap[offer.LenderID]
	dateTimeNowString := general.GetTimeStampString()
	go func() {
		activityObj := activity.ActivityEvent{
			UserID:            userID,
			SourceEntityID:    sourceEntityID,
			LoanApplicationID: "",
			EntityType:        constants.EntityTypeCustomer,
			EntityRef:         offer.OfferType, //TODO: modify this event once multiple client uses /selectOffer
			EventType:         constants.ActivitytOfferSelected,
			Description:       fmt.Sprintf(`{"lender": "%s", "amount":"%.2f","offerType":"%s","tenure":"%d"}`, lenderName, offer.MaxAmount, offer.OfferType, offer.MaxTenure),
		}
		activity.RegisterEvent(&activityObj, dateTimeNowString)
	}()

	err = preselectedlender.Set(userID, offer.LenderID)
	if err != nil {
		logger.WithUser(userID).Error(err)
		return "", err
	}
	if !general.InArr(offer.LenderID, []string{constants.DMIID, constants.CasheID, constants.TataCapitalID, constants.ABFLID, constants.PrefrID, constants.KisshtID, constants.HDFCLenderID, constants.OndcFibeID, constants.OndcBajajID}) {
		// mark OFFER_HOOK as complete
		err = usermodulemapping.Create(nil, userID, userID, "OFFER_HOOK", constants.UserModuleStatusCompleted, "")
		if err != nil {
			log.Error(err)
			return "", err
		}

		log.Debugf("received offer type %s for user %s\n", offer.OfferType, userID)

		if offer.OfferType == "tentative" {
			// switch to bankconnect
			err = workflowutils.UpdateWorkFlow(userID, sourceEntityID, constants.WorkflowTDLBankconnect)
			if err != nil {
				log.Errorln(err)
				errorHandler.ReportToSentryWithoutRequest(err)
			}
		}
	}
	if offer.LenderID == constants.TataCapitalID {
		_, err = tatacapital.Eligibility(userID, sourceEntityID, false)
		if err != nil {
			logger.WithUser(userID).Error(err)
			return "", err
		}
	}

	backgroundCtx := context.Background()

	switch offer.LenderID {
	case constants.DMIID:
		err := dmi.CreateApplication(*ctx, userID, sourceEntityID, offer)
		if err != nil {
			logger.WithUser(userID).Error(err)

			err = userjourney.SetWaitStatus(nil, userID, userjourney.WaitStatusFalse, "")
			if err != nil {
				logger.WithUser(userID).Error(err)
				return "", err
			}
			description := fmt.Sprintf(`{"lender": "%s", "rejectReason":"%s"}`, lenderName, constants.ErrorLenderAPIFailed)
			_, err = tdl.RejectLenderAndBringBack(tdl.LenderReject{UserID: userID,
				SourceEntityID: sourceEntityID,
				OfferID:        offerID,
				LenderID:       offer.LenderID,
				Description:    description,
				RejectReason:   constants.ErrorLenderAPIFailed,
			})
			if err != nil {
				logger.WithUser(userID).Error(err)
				return "", err
			}
			return "", nil
		}

	case constants.CasheID:

		err = usermodulemapping.Create(nil, userID, userID, "OFFER_HOOK", constants.UserModuleStatusCompleted, "")
		if err != nil {
			log.Error(err)
			return "", err
		}

		// We need the loan application before redirection, that's why accepting offer here
		res, customErr := AcceptOffers(&backgroundCtx, offerID, userID, sourceEntityID, offer.MaxAmount, offer.Interest, offer.MaxTenure, ipAddress, 0, "")
		if res != "" {
			return res, nil
		}
		if customErr != nil {
			logger.WithUser(userID).Error(customErr)
			return res, customErr.Err
		}

		err = workflowutils.UpdateWorkFlow(userID, sourceEntityID, constants.WorkflowCashePL)
		if err != nil {
			logger.WithUser(userID).Error(err)
			return "", err
		}

		appReq := lenderservice.ApplicationReq{
			UserID:         userID,
			LenderID:       constants.CasheID,
			SourceEntityID: sourceEntityID,
		}
		_, err = lenderservice.CreateApplicant(backgroundCtx, &appReq)
		if err != nil {
			logger.WithUser(userID).Error("error creating CASHe application:", err)

			description := fmt.Sprintf(`{"lender": "%s", "rejectReason":"%s"}`, lenderName, constants.ErrorLenderAPIFailed)
			_, err = tdl.RejectLenderAndBringBack(tdl.LenderReject{UserID: userID,
				SourceEntityID: sourceEntityID,
				OfferID:        offerID,
				LenderID:       offer.LenderID,
				Description:    description,
				RejectReason:   constants.ErrorLenderAPIFailed,
			})
			if err != nil {
				logger.WithUser(userID).Error(err)
				return "", err
			}
			return "", nil
		}

	case constants.MoneyViewID:

		var createApplicantResp *lenderservice.ApplicationResp
		req := lenderservice.ApplicationReq{
			UserID:         userID,
			LenderID:       constants.MoneyViewID,
			SourceEntityID: sourceEntityID,
		}
		createApplicantResp, err = lenderservice.CreateApplicant(backgroundCtx, &req)
		if err != nil {
			logger.WithUser(userID).Error("error creating moneyview application:", err)

			description := fmt.Sprintf(`{"lender": "%s", "rejectReason":"%s"}`, lenderName, constants.ErrorLenderAPIFailed)
			_, err = tdl.RejectLenderAndBringBack(tdl.LenderReject{UserID: userID,
				SourceEntityID: sourceEntityID,
				OfferID:        offerID,
				LenderID:       offer.LenderID,
				Description:    description,
				RejectReason:   constants.ErrorLenderAPIFailed,
			})
			if err != nil {
				logger.WithUser(userID).Error(err)
				return "", err
			}
			return "", nil

		}

		// We need the loan application before redirection, that's why accepting offer here
		res, customErr := AcceptOffers(&backgroundCtx, offerID, userID, sourceEntityID, offer.MaxAmount, offer.Interest, offer.MaxTenure, ipAddress, 0, "")
		if res != "" {
			return res, nil
		}
		if customErr != nil {
			logger.WithUser(userID).Error(customErr)
			return res, customErr.Err
		}

		err = workflowutils.UpdateWorkFlow(userID, sourceEntityID, constants.WorkflowMoneyviewPL)
		if err != nil {
			logger.WithUser(userID).Error(err)
			return "", err
		}

		loanApplication, err := loanapplication.GetLatestByUser(userID)
		if err != nil && err != sql.ErrNoRows {
			logger.WithUser(userID).Error(err)
			return "", err
		}
		if err == sql.ErrNoRows {
			customErr := errors.New("no loan application found")
			logger.WithUser(userID).Error(customErr)
			return "", err
		}
		loanApplicationID := loanApplication.ID.String()

		if createApplicantResp.Response.Status == "reject" || createApplicantResp.Response.Status == "expired" {

			rejectReason := "rejected by lender"
			_, err = underwriting.RejectLoan(loanApplicationID, constants.MoneyViewID, constants.EntityTypeSystem, "", rejectReason)
			if err != nil {
				logger.WithUser(userID).Error(err)
				return "", err
			}
			return "", nil
		}

		err = lendervariables.MergeUpdateDynamicVariables(userID, constants.MoneyViewID, map[string]interface{}{
			"id":         userID,
			"crmId":      createApplicantResp.CrmID,
			"prospectNo": createApplicantResp.ProspectNo,
		})
		if err != nil {
			logger.WithUser(userID).Errorln(err)
			errorHandler.ReportToSentryWithoutRequest(err)
		}

		err = users.Update(nil, users.User{
			ID:    userID,
			CrmID: createApplicantResp.CrmID,
		})
		if err != nil {
			logger.WithUser(userID).Println(err)
			errorHandler.ReportToSentryWithoutRequest(err)
		}

		_, err = lenderservice.GetOffers(backgroundCtx, &lenderservice.GetOfferReq{
			ApplicationReq: lenderservice.ApplicationReq{
				UserID:         userID,
				LenderID:       constants.MoneyViewID,
				SourceEntityID: sourceEntityID,
				CrmID:          createApplicantResp.CrmID,
			},
			Intent: "FINAL",
		})
		if err != nil {
			logger.WithUser(userID).Errorln(err)
			rejectReason := constants.ErrorLenderAPIFailed

			_, err = underwriting.RejectLoan(loanApplicationID, offer.LenderID, constants.EntityTypeSystem, "", rejectReason)
			if err != nil {
				logger.WithUser(userID).Error(err)
				return "", err
			}
		}

	case constants.TDLKreditBeeID:
		err = workflowutils.UpdateWorkFlow(userID, sourceEntityID, constants.WorkflowTDLKreditbee)
		if err != nil {
			logger.WithUser(userID).Error("error in updating workflow", err)
			return "", err
		}
		// Assign temporal flow for this user
		flags, err := journey.GetTemporalFlags(userID, sourceEntityID, offer.LenderID, false)
		if err != nil {
			logger.WithUser(userID).Error(err)
			return "", err
		}
		err = featureflag.Set(flags, nil)
		if err != nil {
			logger.WithUser(userID).Error(err)
			return "", err
		}
	case constants.ABFLID:
		userObj, err := users.Get(userID)
		if err != nil {
			logger.WithUser(userID).Errorln(err)
			return "", err
		}

		err = users.UpdateCityStateInDynamicUserInfo(userID, constants.ABFLID, userObj)
		if err != nil {
			logger.WithUser(userID).Errorln(err)
			lenderName = constants.LenderNamesMap[constants.ABFLID]
			description := fmt.Sprintf(`{"lender": "%s", "rejectReason":"%s"}`, lenderName, constants.ErrorPincodeNotFound)
			// reject lender and bring back for update city state
			lenderReject := tdl.LenderReject{
				UserID:         userID,
				SourceEntityID: sourceEntityID,
				OfferID:        offerID,
				LenderID:       offer.LenderID,
				Description:    description,
				RejectReason:   constants.ErrorPincodeNotFound,
			}
			_, err := tdl.RejectLenderAndBringBack(lenderReject)
			if err != nil {
				return "", err
			}
		}

		err = userjourney.SetWaitStatus(nil, userID, userjourney.WaitStatusShortPeriod, userjourney.WaitStatusReasonExpectingCallback)
		if err != nil {
			logger.WithUser(userID).Error(err)
			return "", err
		}

		var appResp *lenderservice.GetOfferResp
		err = retry.CustomRetry(3, 1000*time.Millisecond, func() error {

			appResp, err = lenderservice.GetOffers(backgroundCtx, &lenderservice.GetOfferReq{
				ApplicationReq: lenderservice.ApplicationReq{
					UserID:         userID,
					LenderID:       constants.ABFLID,
					SourceEntityID: sourceEntityID,
					Pincode:        userObj.Pincode,
				},
				Intent: "FINAL",
			})
			if err != nil {
				logger.WithUser(userID).Errorln(err)
				return err
			}

			if appResp.Response.Status != "SUCCESS" {
				fmt.Println("BRE Not giving SUCCESS: ", appResp.Response.Status)
				return errors.New(appResp.Response.Status)
			}
			fmt.Println("BRE giving SUCCESS")
			return nil
		})

		// BRE FAILURE case
		if err != nil {
			description := fmt.Sprintf(`{"lender": "%s", "rejectReason":"%s"}`, lenderName, constants.ErrorBREFailed)
			lenderReject := tdl.LenderReject{
				UserID:         userID,
				SourceEntityID: sourceEntityID,
				OfferID:        offerID,
				LenderID:       offer.LenderID,
				Description:    description,
				RejectReason:   constants.ErrorLenderAPIFailed,
			}
			_, err := tdl.RejectLenderAndBringBack(lenderReject)
			if err != nil {
				return "", err
			}
			err = userjourney.SetWaitStatus(nil, userID, userjourney.WaitStatusFalse, "")
			if err != nil {
				logger.WithUser(userID).Error(err)
				return "", err
			}
		} else {
			// BRE SUCCESS case
			err := usermodulemapping.Create(nil, userID, userID, "OFFER_HOOK", constants.UserModuleStatusCompleted, "")
			if err != nil {
				log.Error(err)
				return "", err
			}

			res, customErr := AcceptOffers(&backgroundCtx, offerID, userID, sourceEntityID, offer.MaxAmount, offer.Interest, offer.MaxTenure, ipAddress, 0, "")
			if res != "" {
				return res, nil
			}
			if customErr != nil {
				logger.WithUser(userID).Error(customErr)
				return res, customErr.Err
			}

			loanApplication, err := loanapplication.GetLatestByUser(userID)
			if err != nil {
				logger.WithUser(userID).Error(err)
				return "", err
			}

			err = loanapplication.UpdateApplicationNo(backgroundCtx, userID, loanApplication.ID.String(), appResp.ApplicationID)
			if err != nil {
				logger.WithUser(userID).Error(err)
				return "", err
			}

			// Waiting for bre call back response
		}
	case constants.PrefrID:
		// Create Lead
		req := lenderservice.ApplicationReq{
			UserID:         userID,
			LenderID:       constants.PrefrID,
			SourceEntityID: sourceEntityID,
		}
		_, err = lenderservice.CreateApplicant(context.Background(), &req)
		if err != nil {
			logger.WithUser(userID).Error("error creating prefr application:", err)
			description := fmt.Sprintf(`{"lender": "%s", "rejectReason":"%s"}`, lenderName, constants.ErrorLenderAPIFailed)
			_, err = tdl.RejectLenderAndBringBack(tdl.LenderReject{
				UserID:         userID,
				SourceEntityID: sourceEntityID,
				OfferID:        offerID,
				LenderID:       offer.LenderID,
				Description:    description,
				RejectReason:   constants.ErrorLenderAPIFailed,
			})
			if err != nil {
				logger.WithUser(userID).Error(err)
				return "", err
			}
			return "", nil
		}

		var appResp *lenderservice.GetOfferResp
		var loanofferID string
		err = retry.CustomRetry(3, 1000*time.Millisecond, func() error {

			appResp, err = lenderservice.GetOffers(context.Background(), &lenderservice.GetOfferReq{
				ApplicationReq: lenderservice.ApplicationReq{
					UserID:         userID,
					LenderID:       constants.PrefrID,
					SourceEntityID: sourceEntityID,
				},
				Intent: "FINAL",
			})
			if err != nil {
				logger.WithUser(userID).Errorln(err)
				return err
			}

			if strings.ToUpper(appResp.Response.Status) != "SUCCESS" {
				return errors.New(appResp.Response.Status)
			}
			return nil

		})
		//  FAILURE case
		if err != nil {
			lenderName := constants.LenderNamesMap[constants.PrefrID]
			logger.WithUser(userID).Errorf("error from lenderservice create applicant for lenderID: %s error:%s ", constants.PrefrID, err.Error())
			description := fmt.Sprintf(`{"lender": "%s", "rejectReason":"%s"}`, lenderName, constants.ErrorLenderAPIFailed)
			_, err = tdl.RejectLenderAndBringBack(tdl.LenderReject{
				UserID:         userID,
				SourceEntityID: sourceEntityID,
				OfferID:        offerID,
				LenderID:       offer.LenderID,
				Description:    description,
				RejectReason:   constants.ErrorLenderAPIFailed,
			})
			if err != nil {
				logger.WithUser(userID).Error(err)
				return "", err
			}
			return "", nil
		}
		// Create a loan application and get the offer id
		if appResp != nil && len(appResp.Offers) > 0 && appResp.Offers[0] != nil && appResp.Offers[0].MaxAmount > 0 {
			loanofferID, err = personalloanoffer.Create(nil, userID, sourceEntityID, constants.PrefrID, general.GetUUID(),
				appResp.Offers[0].MaxAmount, appResp.Offers[0].MinAmount, appResp.Offers[0].Roi,
				appResp.Offers[0].MaxTenure, appResp.Offers[0].MinTenure, "", "", appResp.Offers[0].ProcessingFee, "FLAT", "",
				"", constants.OfferStatusSelected)
			if err != nil {
				logger.WithUser(userID).Error(err)
				return "", err
			}
		}
		if appResp != nil && appResp.RedirectionURL != "" && appResp.Offers != nil {
			// SUCCESS case
			err = usermodulemapping.Create(nil, userID, userID, usermodulemapping.OfferHook, constants.UserModuleStatusCompleted, "")
			if err != nil {
				logger.WithUser(userID).Error(err)
				return "", err
			}
			if len(appResp.Offers) == 0 {
				err := fmt.Errorf("offer not available")
				logger.WithUser(userID).Error(err)
				return "", err
			}
			res, customErr := AcceptOffers(&backgroundCtx, loanofferID, userID, sourceEntityID, appResp.Offers[0].MaxAmount, appResp.Offers[0].Roi, appResp.Offers[0].MaxTenure, ipAddress, 0, appResp.ApplicationID)
			if res != "" {
				logger.WithUser(userID).Error(err)
				return res, nil
			}
			if customErr != nil {
				logger.WithUser(userID).Error(customErr)
				return res, customErr.Err
			}
		}
		if appResp != nil && appResp.RedirectionURL != "" && appResp.Offers == nil {
			err := usermodulemapping.Create(nil, userID, userID, usermodulemapping.OfferHook, constants.UserModuleStatusCompleted, "")

			if err != nil {
				logger.WithUser(userID).Error(err)
				return "", err
			}

			res, customErr := AcceptOffers(&backgroundCtx, offerID, userID, sourceEntityID, offer.MaxAmount, offer.Interest, offer.MaxTenure, ipAddress, 0, appResp.ApplicationID)
			if res != "" {
				logger.WithUser(userID).Error(err)
				return res, nil
			}
			if customErr != nil {
				logger.WithUser(userID).Error(customErr)
				return res, customErr.Err
			}
		}

		loanApplication, err := loanapplication.GetLatestByUser(userID)
		if err != nil {
			logger.WithUser(userID).Error(err)
			return "", err
		}

		err = loanapplication.UpdateApplicationNo(backgroundCtx, userID, loanApplication.ID.String(), appResp.ApplicationID)
		if err != nil {
			logger.WithUser(userID).Error(err)
			return "", err
		}

	case constants.AxisBankID:
		err = workflowutils.UpdateWorkFlow(userID, sourceEntityID, constants.WorkflowTDLAXIS)
		if err != nil {
			logger.WithUser(userID).Error("error in updating workflow", err)
			return "", err
		}
		// Assign temporal flow for this user
		flags, err := journey.GetTemporalFlags(userID, sourceEntityID, offer.LenderID, false)
		if err != nil {
			logger.WithUser(userID).Error(err)
			return "", err
		}
		err = featureflag.Set(flags, nil)
		if err != nil {
			logger.WithUser(userID).Error(err)
			return "", err
		}

	case constants.KisshtID:

		type userStruct struct {
			SDKVersion string
			CustomerID string
		}
		var userObj userStruct
		query := `SELECT coalesce(sdk_version, '') as sdkversion, unique_id as customerid from users where user_id = $1`
		err = database.Get(&userObj, query, userID)
		if err != nil {
			logger.WithUser(userID).Errorln(err)
			errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userID}, err)
		}
		err = deviceConnect.FetchRisk(userID, userObj.CustomerID, sourceEntityID)
		if err != nil {
			logger.WithUser(userID).Errorln(err)
			errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userID, "customerID": userObj.CustomerID}, err)
		}
		var createApplicantResp *lenderservice.ApplicationResp
		err = retry.CustomRetry(3, 1000*time.Millisecond, func() error {

			req := lenderservice.ApplicationReq{
				UserID:         userID,
				LenderID:       constants.KisshtID,
				SourceEntityID: sourceEntityID,
			}
			createApplicantResp, err = lenderservice.CreateApplicant(context.Background(), &req)
			if err != nil {
				logger.WithUser(userID).Errorln(err)
				return err
			}
			return nil
		})
		if err != nil {
			logger.WithUser(userID).Errorf("error from lenderservice create applicant for lenderID: %s error:%s ", constants.KisshtID, err.Error())
			description := fmt.Sprintf(`{"lender": "%s", "rejectReason":"%s"}`, lenderName, constants.ErrorLenderAPIFailed)
			_, err = tdl.RejectLenderAndBringBack(tdl.LenderReject{
				UserID:         userID,
				SourceEntityID: sourceEntityID,
				OfferID:        offerID,
				LenderID:       offer.LenderID,
				Description:    description,
				RejectReason:   constants.ErrorLenderAPIFailed,
			})
			if err != nil {
				logger.WithUser(userID).Error(err)
				return "", err
			}
		} else {
			err = users.Update(nil, users.User{
				ID:    userID,
				CrmID: createApplicantResp.CrmID,
			})
			if err != nil {
				logger.WithUser(userID).Println(err)
				errorHandler.ReportToSentryWithoutRequest(err)
			}
		}

		var createApplicationResp *lenderservice.ApplicationResp
		err = retry.CustomRetry(3, 1000*time.Millisecond, func() error {

			applicationReq := lenderservice.ApplicationReq{
				UserID:         userID,
				LenderID:       constants.KisshtID,
				SourceEntityID: sourceEntityID,
			}
			createApplicationResp, err = lenderservice.CreateApplication(context.Background(), &applicationReq)
			if err != nil {
				logger.WithUser(userID).Errorln(err)
				return err
			}
			return nil
		})

		if err != nil {
			logger.WithUser(userID).Errorf("error from lenderservice create application for lenderID: %s error:%s ", constants.KisshtID, err.Error())
			description := fmt.Sprintf(`{"lender": "%s", "rejectReason":"%s"}`, lenderName, constants.ErrorLenderAPIFailed)
			_, err = tdl.RejectLenderAndBringBack(tdl.LenderReject{
				UserID:         userID,
				SourceEntityID: sourceEntityID,
				OfferID:        offerID,
				LenderID:       offer.LenderID,
				Description:    description,
				RejectReason:   constants.ErrorLenderAPIFailed,
			})
			if err != nil {
				logger.WithUser(userID).Error(err)
				return "", err
			}
		} else {
			err := usermodulemapping.Create(nil, userID, userID, usermodulemapping.OfferHook, constants.UserModuleStatusCompleted, "")
			if err != nil {
				logger.WithUser(userID).Error(err)
				return "", err
			}

			res, customErr := AcceptOffers(&backgroundCtx, offerID, userID, sourceEntityID, offer.MaxAmount, offer.Interest, offer.MaxTenure, ipAddress, 0, "")
			if res != "" {
				return res, nil
			}
			if customErr != nil {
				logger.WithUser(userID).Error(customErr)
				return res, customErr.Err
			}

			loanApplication, err := loanapplication.GetLatestByUser(userID)
			if err != nil {
				logger.WithUser(userID).Error(err)
				return "", err
			}

			err = loanapplication.UpdateApplicationNo(backgroundCtx, userID, loanApplication.ID.String(), createApplicationResp.LoanApplicationNumber)
			if err != nil {
				logger.WithUser(userID).Error(err)
				return "", err
			}

			// Kissht Update Transaction API Call
			err = retry.CustomRetry(3, 1000*time.Millisecond, func() error {

				req := lenderservice.UpdateApplicationReq{
					ApplicationReq: lenderservice.ApplicationReq{
						UserID:         userID,
						LenderID:       constants.KisshtID,
						SourceEntityID: sourceEntityID,
					},
				}
				_, err = lenderservice.UpdateApplication(context.Background(), &req)
				if err != nil {
					logger.WithUser(userID).Errorln(err)
					return err
				}
				return nil
			})

			if err != nil {
				logger.WithUser(userID).Errorf("error from lenderservice update application for lenderID: %s error:%s ", constants.KisshtID, err.Error())
				loanApplication, err := loanapplication.GetLatestByUser(userID)
				if err != nil {
					logger.WithUser(userID).Error(err)
					return "", err
				}

				rejectReason := "lender update transaction api failure"

				_, err = underwriting.RejectLoan(loanApplication.ID.String(), constants.KisshtID, constants.EntityTypeSystem, "", rejectReason)
				if err != nil {
					logger.WithUser(userID).Error(err)
					return "", err
				}
			}
		}
	case constants.HDFCLenderID:

		err = usermodulemapping.Create(nil, userID, userID, "OFFER_HOOK", constants.UserModuleStatusCompleted, "")
		if err != nil {
			log.Error(err)
			return "", err
		}

		// We need the loan application before redirection, that's why accepting offer here
		res, customErr := AcceptOffers(&backgroundCtx, offerID, userID, sourceEntityID, offer.MaxAmount, offer.Interest, offer.MaxTenure, ipAddress, 0, "")
		if res != "" {
			return res, nil
		}
		if customErr != nil {
			logger.WithUser(userID).Error(customErr)
			return res, customErr.Err
		}

		err = workflowutils.UpdateWorkFlow(userID, sourceEntityID, constants.WorkflowHdfcTdlPl)
		if err != nil {
			logger.WithUser(userID).Error(err)
			return "", err
		}
	case constants.OndcFibeID:

		err = usermodulemapping.Create(nil, userID, userID, "OFFER_HOOK", constants.UserModuleStatusCompleted, "")
		if err != nil {
			log.Error(err)
			return "", err
		}

		// We need the loan application before redirection, that's why accepting offer here
		res, customErr := AcceptOffers(&backgroundCtx, offerID, userID, sourceEntityID, offer.MaxAmount, offer.Interest, offer.MaxTenure, ipAddress, 0, "")
		if res != "" {
			return res, nil
		}
		if customErr != nil {
			logger.WithUser(userID).Error(customErr)
			return res, customErr.Err
		}

		err = workflowutils.UpdateWorkFlow(userID, sourceEntityID, constants.WorkflowOndcFibeTdlPl)
		if err != nil {
			logger.WithUser(userID).Error(err)
			return "", err
		}
	case constants.OndcBajajID:

		err = usermodulemapping.Create(nil, userID, userID, "OFFER_HOOK", constants.UserModuleStatusCompleted, "")
		if err != nil {
			log.Error(err)
			return "", err
		}

		// We need the loan application before redirection, that's why accepting offer here
		res, customErr := AcceptOffers(&backgroundCtx, offerID, userID, sourceEntityID, offer.MaxAmount, offer.Interest, offer.MaxTenure, ipAddress, 0, "")
		if res != "" {
			return res, nil
		}
		if customErr != nil {
			logger.WithUser(userID).Error(customErr)
			return res, customErr.Err
		}

		err = workflowutils.UpdateWorkFlow(userID, sourceEntityID, constants.WorkflowOndcBajajTdlPl)
		if err != nil {
			logger.WithUser(userID).Error(err)
			return "", err
		}
	}

	return "", nil
}
