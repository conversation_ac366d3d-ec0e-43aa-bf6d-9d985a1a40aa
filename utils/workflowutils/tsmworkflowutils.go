package workflowutils

import (
	"context"
	"encoding/json"
	"errors"
	"finbox/go-api/constants"
	"finbox/go-api/errorHandler"
	"finbox/go-api/functions/logger"
	workflowconfigfns "finbox/go-api/functions/workflowconfig"
	"finbox/go-api/infra/temporalclient"
	"finbox/go-api/models/deleterequests"
	"finbox/go-api/models/temporalsignallogging"
	"finbox/go-api/models/usermodulemapping"
	"finbox/go-api/models/users"
	"finbox/go-api/models/userworkflows"
	"finbox/go-api/models/workflow"
	"finbox/go-api/temporal/temporalutility"
	"finbox/go-api/utils/builderutils"
	"finbox/go-api/utils/general"
	"finbox/go-api/utils/workflowutils/tsm"
	"fmt"
	"time"

	"github.com/finbox-in/road-runner/runner"
)

func RestoreWorkflowFromOldWorkflow(ctx context.Context, userID string, sourceEntityID string) (err error) {
	// fetching current running workflow
	currentlyRunningWf, err := temporalutility.FetchWfByStatusAndHealthCheck(ctx, userID, tsm.TSM, []string{constants.TemporalStatusRunning})
	if err != nil {
		err = fmt.Errorf("error in fetching current running workflow, err: %v", err)
		logger.WithUser(userID).Error(err)
		return err
	}
	if currentlyRunningWf == nil {
		err = fmt.Errorf("current running workflow not found")
		logger.WithUser(userID).Error(err)
		return err
	}

	events, err := tsm.GetAllSignalEventsForWorkflow(ctx, currentlyRunningWf.WorkflowID)
	if err != nil {
		err = fmt.Errorf("error in fetching all signal events, workflowID: %v", currentlyRunningWf.WorkflowID)
		logger.WithUser(userID).Error(err)
		return err
	}

	// update user wait state true, not using func duw to cyclic dependency
	waitState := true
	if err = users.Update(nil, users.User{
		ID:        userID,
		WaitState: &waitState,
	}); err != nil {
		logger.WithUser(userID).Error(err)
		return err
	}

	// deffered update user wait state false, not using func due to cyclic dependency
	// this help in not landing user in wrong state while executing
	defer func() {
		waitState = false
		if err1 := users.Update(nil, users.User{
			ID:        userID,
			WaitState: &waitState,
		}); err1 != nil {
			logger.WithUser(userID).Error(err1)
		}
	}()

	// Starting TSM New Workflow for Old workflow Restoring
	logger.WithUser(userID).Infof("Starting TSM New Workflow")
	workflowData := map[string]interface{}{
		"sourceEntityID": sourceEntityID,
		"userID":         userID,
	}
	userWorkflow, _, err := workflowconfigfns.StartTSMWorkflowFromConfig(
		ctx,
		workflowconfigfns.StartWorkflowOption{
			Data:              workflowData,
			SourceEntityID:    sourceEntityID,
			UserID:            userID,
			WorkflowType:      tsm.TSM,
			UserWorkflowRowID: general.GetUUID(),
			WorkflowStatus:    constants.TemporalStatusNotStarted,
		})
	if err != nil {
		logger.WithUser(userID).Error(err)
		return err
	}

	for _, event := range events {
		var signalName string
		var signalDataMap map[string]interface{}
		// fetching signalName from event history and signalData from temporal_signal_logging Table
		if attributes := event.GetWorkflowExecutionSignaledEventAttributes(); attributes != nil {
			signalName = attributes.GetSignalName()
			if signalDataMap, err = temporalsignallogging.GetSignalData(userID, currentlyRunningWf.ID, signalName); err != nil {
				logger.WithUser(userID).Errorln(err, userID, userWorkflow.ID, attributes.GetSignalName())
				return err
			}
		} else {
			err = fmt.Errorf("not able to find stributes or signalName in event")
			return err
		}

		// signaling the workflow with signalName and signalDataMap
		logger.WithUser(userID).Infof("signalName: %s, signalData: %v", signalName, signalDataMap)
		err = temporalclient.Client.SignalWorkflow(
			ctx,
			userWorkflow.WorkflowID,
			"", // intentionally kept empty to pick latest execution
			signalName,
			signalDataMap)
		if err != nil {
			err = fmt.Errorf("error in sending %s signal with data %v to tsm workflow %s with error %v",
				signalName, signalDataMap, userID, err)
			logger.WithUser(userID).Error(err)
			return err
		}

		// Inserting in temporalsignallogging for new workflow will be helpful if we reset again
		err = temporalsignallogging.Insert(signalName, userID, userWorkflow.ID, signalDataMap)
		if err != nil {
			log.Errorf("error in inserting into signal logging table, signalName: %s, workflowID : %s, userID %s",
				signalName, userID, userWorkflow.ID)
		}
		time.Sleep(500 * time.Millisecond)
	}

	// Update old workflow failed and terminate the workflow
	if errWfUpdate := userworkflows.UpdateStatus(nil,
		userID, constants.TemporalStatusFailed,
		currentlyRunningWf.WorkflowID,
		currentlyRunningWf.RunID,
		userworkflows.ErrorTypeWorkflowFailed,
		"restoring it to new Workflow",
	); errWfUpdate != nil {
		return errWfUpdate
	}
	err = temporalutility.TerminateRunningWorkflow(ctx, currentlyRunningWf.WorkflowID,
		currentlyRunningWf.RunID, "restoring it to new Workflow")
	if err != nil {
		logger.WithUser(userID).Error("unable to Update old workflow status: ", err)
		return err
	}

	// update the status of new workflow as running
	if errWfUpdate := userworkflows.UpdateStatus(nil,
		userID, constants.TemporalStatusRunning,
		userWorkflow.WorkflowID,
		userWorkflow.RunID,
		"",
		"",
	); errWfUpdate != nil {
		logger.WithUser(userID).Error("unable to Update new workflow status: ", err)
		return errWfUpdate
	}

	return nil
}

func GetCurrentModuleFromTSMWorkflow(ctx context.Context, userID, sourceEntityID string) (CurrentModule, error) {
	var currentModule CurrentModule
	userStateValObj, err := GetUserStateValues(userID, sourceEntityID, "")
	if err != nil {
		logger.WithUser(userID).Error(err)
		return CurrentModule{}, err
	}

	userStateValObj.SourceEntityID = sourceEntityID
	currentModule, ok := InferModuleFromTerminalStates(userStateValObj)
	if ok {
		return currentModule, nil
	}

	if currentModule, ok = handleDeleteRequested(userID); ok {
		return currentModule, nil
	}

	currentModule, err = GetModuleFromTSMWorkflow(ctx, userID, sourceEntityID)
	if err != nil {
		logger.WithUser(userID).Error(err)
		return CurrentModule{}, err
	}
	// for disbursal may need to put some checks
	return currentModule, nil
}

func GetModuleFromTSMWorkflow(ctx context.Context, userID, sourceEntityID string) (CurrentModule, error) {
	var currentModule CurrentModule

	// fetch all the modules executed in TSM workflow(make sure temporal workflow is running state)
	allModulesExecuted, err := tsm.GetAllExecutedModulesForTSMWorkflow(ctx, userID)
	if err != nil {
		return currentModule, errors.New("error while getting the history")
	}
	// get last module
	var lastModule runner.TemporalStateMachineMetadata
	if len(allModulesExecuted) > 0 {
		lastModule = allModulesExecuted[len(allModulesExecuted)-1]
	}
	filters := []tsm.Filter{
		{Key: "source", Value: "JOURNEY"},
		{Key: "transitionType", Value: "MODULE"},
		{Key: "status", Value: []string{"COMPLETED", "STARTED"}},
	}
	// get next possible modules executed in TSM workflow(make sure temporal workflow is running state)
	_, possibleNextModules, err := tsm.GetNextTSMModuleForFilters(ctx, userID, filters...)
	if err != nil {
		return currentModule, errors.New("error while getting next module, err:" + err.Error())
	}
	if len(possibleNextModules) == 0 {
		return currentModule, errors.New("no next active module found")
	}
	if len(possibleNextModules) != 1 {
		return currentModule, errors.New("possible next modules are greater than 1")
	}
	nextModule := possibleNextModules[0]
	var pastStepperModuleType string
	var stepper []ModuleStruct

	// handling for start of workflow,
	// last module will be empty and current module should be in start state
	if len(allModulesExecuted) == 0 {
		nextWorkflowModule := ConvertTSMModuleToWorkFlowModule(nextModule)
		currentModule.Module = GenerateModule(
			nextWorkflowModule,
			constants.ModuleDetailsMapIndexFresh,
			constants.ModuleStateStart,
		)
		stepper = append(stepper, GenerateModule(
			nextWorkflowModule,
			constants.ModuleDetailsMapIndexFresh,
			constants.StepperStateOngoing,
		))
		// since we don't have any future modules handling, right now we are returning from here itself
		currentModule.Stepper = stepper
		return currentModule, nil
	}

	// HANDLING FOR IDENTIFYING STEPPER MODULE
	for _, module := range allModulesExecuted {
		// skip REDIRECTION module
		if module.ModuleName == usermodulemapping.Redirection {
			continue
		}

		if module.ModuleStatus == tsm.TSMModuleStatusCompleted {
			workflowModule := ConvertTSMModuleToWorkFlowModule(module)
			if pastStepperModuleType != module.ModuleType {
				pastStepperModuleType = module.ModuleType
				stepper = nil
			}

			stepper = append(stepper, GenerateModule(
				workflowModule,
				constants.ModuleDetailsMapIndexFresh,
				constants.ModuleStateCompleted,
			))
		}
	}

	lastWorkflowModule := ConvertTSMModuleToWorkFlowModule(lastModule)
	nextWorkflowModule := ConvertTSMModuleToWorkFlowModule(nextModule)
	switch lastModule.ModuleStatus {
	case "STARTED":
		currentModule.Module = GenerateModule(
			nextWorkflowModule,
			constants.ModuleDetailsMapIndexWait,
			constants.ModuleStateWait,
		)
		if pastStepperModuleType != currentModule.Module.ModuleType {
			pastStepperModuleType = currentModule.Module.ModuleType
			stepper = nil
		}
		stepper = append(stepper, GenerateModule(
			nextWorkflowModule,
			constants.ModuleDetailsMapIndexWait,
			constants.StepperStateOngoing,
		))
	case "COMPLETED":
		// TODO: add sub status check for kyc module it is used if sub_status = 2, then state should be wait
		if nextModule.ModuleName == "" {
			currentModule.Module = GenerateModule(
				lastWorkflowModule,
				constants.ModuleDetailsMapIndexFresh,
				constants.ModuleStateCompleted,
			)
			if pastStepperModuleType != currentModule.Module.ModuleType {
				pastStepperModuleType = currentModule.Module.ModuleType
				stepper = nil
			}
			stepper = append(stepper, GenerateModule(
				lastWorkflowModule,
				constants.ModuleDetailsMapIndexFresh,
				constants.StepperStateCompleted,
			))
		} else {
			currentModule.Module = GenerateModule(
				nextWorkflowModule,
				constants.ModuleDetailsMapIndexFresh,
				constants.ModuleStateStart,
			)
			if pastStepperModuleType != currentModule.Module.ModuleType {
				pastStepperModuleType = currentModule.Module.ModuleType
				stepper = nil
			}
			stepper = append(stepper, GenerateModule(
				nextWorkflowModule,
				constants.ModuleDetailsMapIndexFresh,
				constants.StepperStateOngoing,
			))
		}
	case "FAILED":
		currentModule.Module = GenerateModule(
			nextWorkflowModule,
			constants.ModuleDetailsMapIndexFailed,
			constants.ModuleStateFailed,
		)
		if pastStepperModuleType != currentModule.Module.ModuleType {
			pastStepperModuleType = currentModule.Module.ModuleType
			stepper = nil
		}
		stepper = append(stepper, GenerateModule(
			nextWorkflowModule,
			constants.ModuleDetailsMapIndexFailed,
			constants.StepperStateFailed,
		))
	}

	workflowConfig, err := builderutils.FetchWorkflowConfig(ctx, userID, sourceEntityID, tsm.TSM, "", "", false, false)
	if err != nil {
		customError := fmt.Errorf("[GetModuleFromTSMWorkflow] failed to get workflow Config: workflow: %s, "+
			"userID: %s, err: %v", tsm.TSM, userID, err)
		logger.WithUser(userID).Error(customError)
		errorHandler.ReportToSentryWithoutRequest(customError)
		return currentModule, customError
	}
	wf, err := runner.ParseWorkflowFromJSON(workflowConfig.WorkflowDefinition)
	if err != nil {
		log.Error(err.Error())
		return currentModule, err
	}
	visited := map[string]*tsm.Node{}
	tsmCurrentModule := tsm.CurrentModule{
		ModuleName:   currentModule.Module.ModuleName,
		ModuleStatus: lastModule.ModuleStatus,
	}
	upcomingModules := tsm.GetNextModules(wf, visited, tsmCurrentModule, tsm.SourceJourney, nil)
	log.Infof("UPCOMING MODULES - len: %d, upcomingModules: %s for curentModule %v", len(upcomingModules), upcomingModules, tsmCurrentModule)
	for _, module := range upcomingModules {
		// skip REDIRECTION module
		if module.ModuleName == usermodulemapping.Redirection {
			continue
		}

		if currentModule.Module.ModuleType != module.ModuleType {
			break
		}

		if currentModule.Module.ModuleName != module.ModuleName {
			tsmMetaData := runner.TemporalStateMachineMetadata{
				ModuleName:   module.ModuleName,
				ModuleStatus: module.ModuleStatus,
				ModuleType:   module.ModuleType,
			}
			stepper = append(stepper,
				GenerateModule(
					ConvertTSMModuleToWorkFlowModule(tsmMetaData),
					constants.ModuleDetailsMapIndexFresh,
					constants.StepperStateUpcoming,
				))
		}
	}
	currentModule.Stepper = stepper
	return currentModule, nil
}

// ............................... UTILS ....................................................
func handleDeleteRequested(userID string) (CurrentModule, bool) {
	var currentModuleDetails CurrentModule
	status, err := deleterequests.GetStatus(userID, userID)
	if err != nil {
		return CurrentModule{}, false
	}

	if general.InArr(status, []int{deleterequests.StatusRequested, deleterequests.StatusCompleted}) {
		currentModuleDetails.Module.ModuleName = "WAIT"
		currentModuleDetails.Module.State = constants.ModuleStateWait
		currentModuleDetails.Module.ActionText = ""
		currentModuleDetails.Module.Title = "Data Deletion Requested"
		currentModuleDetails.Module.Description = "We have accepted your data delete request. After review the deletion request will be completed in next 15-30 days."
		return currentModuleDetails, true
	}

	return currentModuleDetails, false
}

func ConvertTSMModuleToWorkFlowModule(tsmModule runner.TemporalStateMachineMetadata) workflow.WorkFlowModule {
	workflowModule := workflow.WorkFlowModule{
		ModuleName: tsmModule.ModuleName,
		ModuleType: tsmModule.ModuleType,
		IsActive:   true,
	}
	if tsmModule.ModuleStages == "" {
		return workflowModule
	}
	var moduleStages workflow.ModuleStage
	err := json.Unmarshal([]byte(tsmModule.ModuleStages), &moduleStages)
	if err != nil {
		return workflowModule
	}
	workflowModule.Stages = moduleStages
	return workflowModule
}
