package tsm

import (
	"context"
	"database/sql"
	"errors"
	"finbox/go-api/constants"
	"finbox/go-api/errorHandler"
	"finbox/go-api/functions/logger"
	"finbox/go-api/functions/retry"
	"finbox/go-api/infra/temporalclient"
	"finbox/go-api/models/temporalsignallogging"
	"finbox/go-api/models/userworkflows"
	"finbox/go-api/temporal/temporalutility"
	"fmt"
	"time"

	"github.com/finbox-in/road-runner/runner"

	runnerconstants "github.com/finbox-in/road-runner/constants"
	"github.com/mitchellh/mapstructure"
	"go.temporal.io/api/enums/v1"
	"go.temporal.io/api/history/v1"
	"go.temporal.io/sdk/client"
)

var log = logger.Log

// TSM is Module name for temporal workflow
const TSM = "TEMPORAL_STATE_MANAGEMENT"

// FlagTSMFlow is Feature flag for temporal state machine
const FlagTSMFlow = "temporal_state_management_flow"
const FlagAPIStackSupportedTSMFlow = "api_stack_supported_tsm_flow"
const FlagDecisionCoApplicantFlow = "decision_co_applicant_flow"
const FlagSkipOfferSelectionFlow = "skip_offer_selection_flow"

// UMM ModuleStatus
const (
	ModuleStatusCompleted = 1
	ModuleStatusPending   = 2
	ModuleStatusFailed    = 3
	ModuleStatusArchived  = 4
)

// TSM Module Status
const (
	TSMModuleStatusCompleted = "COMPLETED"
	TSMModuleStatusPending   = "STARTED"
	TSMModuleStatusFailed    = "FAILED"
	TSMModuleStatusArchived  = "ARCHIVED"
)

// ModuleStatusToTSMModuleStatusMapping is mapping of UMMStatus to TSM status
var ModuleStatusToTSMModuleStatusMapping = map[int]string{
	ModuleStatusCompleted: TSMModuleStatusCompleted,
	ModuleStatusPending:   TSMModuleStatusPending,
	ModuleStatusFailed:    TSMModuleStatusFailed,
	ModuleStatusArchived:  TSMModuleStatusArchived,
}

var TSMModuleStatusToModuleStatusMapping = map[string]int{
	TSMModuleStatusCompleted: ModuleStatusCompleted,
	TSMModuleStatusPending:   ModuleStatusPending,
	TSMModuleStatusFailed:    ModuleStatusFailed,
	TSMModuleStatusArchived:  ModuleStatusArchived,
}

const (
	MaxAttempts       = 15
	DelayBetweenCalls = 20 * time.Millisecond
)

// Valid Sources
const (
	SourceAPIStack = "API_STACK"
	SourceJourney  = "JOURNEY"
)

// TODO : need to standardise the invocations for all clients
var endpointInvocationMap = map[string]map[int]string{
	"POST:/user/create":         {1: "USER_CREATED"},
	"POST:/user/details/update": {1: "UUD"},
	"POST:/user/offers":         {1: "BUREAU", 2: "BANK_CONNECT", 3: "GST"},
	"POST:/user/acceptOffer":    {1: "FRESH"},
	"POST:/user/bank/analyse":   {1: "BANKCONNECT"},
}

func ConvertTSMModuleStatusToModuleStatusMapping(tsmModuleStatus string) (int, bool) {
	status, ok := TSMModuleStatusToModuleStatusMapping[tsmModuleStatus]
	return status, ok
}
func IsValidStateWithTSMWorkflow(ctx context.Context, userID string, sourceEntityID string, escapedPath string) (bool, bool, int, error) {
	modulesExecuted, err := GetAllExecutedModulesForTSMWorkflow(ctx, userID)
	if err != nil {
		return false, false, 0, err
	}

	//TODO: add booster Module here
	//TODO: fix invocation logic or optional module
	moduleName, err := getModuleNameForEndPointAndInvocation(ctx, "", escapedPath, 1)
	if err != nil {
		return false, false, 0, err
	}
	var isCalledBefore bool
	var isValid bool
	var workflowStep int
	// TODO: add proper logic to find workflow step in case of invocations
	for _, module := range modulesExecuted {
		if module.ModuleName == moduleName && module.ModuleStatus == TSMModuleStatusCompleted {
			isCalledBefore = true
		}
		if module.ModuleStatus == TSMModuleStatusCompleted &&
			module.SignalSource == SourceAPIStack {
			workflowStep++
		}
	}

	// TODO: Move to filter builder
	filters := []Filter{
		{Key: "source", Value: "API_STACK"},
		{Key: "transitionType", Value: "MODULE"},
	}
	_, nextModules, err := GetNextTSMModuleForFilters(ctx, userID, filters...)
	if err != nil {
		return false, isCalledBefore, 0, err
	}

	for _, module := range nextModules {
		if module.ModuleName == moduleName {
			isValid = true
			break
		}
	}

	return isValid, isCalledBefore, workflowStep, nil
}

func getModuleNameForEndPointAndInvocation(ctx context.Context, method string, escapedPath string, invocation int) (string, error) {
	if method == "" {
		method = "POST"
	}
	key := fmt.Sprintf("%s:%s", method, escapedPath)
	val, ok := endpointInvocationMap[key]
	if !ok {
		return "", fmt.Errorf("couldnt find the method with escaped path in endpointInvocationMap, method: %s, URI: %s", method, escapedPath)
	}

	moduleName, ok := val[invocation]
	if !ok {
		return "", fmt.Errorf("couldnt find the invocation in endpointInvocationMap, method: %v, URI: %v, invocation: %v", method, escapedPath, invocation)
	}

	return moduleName, nil
}

func sendSignalWithStatus(
	ctx context.Context,
	userID string,
	moduleName string,
	status int,
	_ int,
	source string,
	signalData map[string]interface{},
	userWorkflow *userworkflows.UserWorkflow,
	temporalClient client.Client,
) error {
	// sanity check
	if signalData == nil {
		signalData = map[string]interface{}{}
	}

	if userWorkflow == nil {
		workflows, err := userworkflows.Get(userID, TSM)
		if err != nil {
			err := fmt.Errorf("error fetching latest running tsm workflow for user: %s with error %v", userID, err)
			logger.WithUser(userID).Errorln(err)
			errorHandler.ReportToSentryWithFields(map[string]interface{}{
				"userID": userID,
			}, err)
			return err
		}
		userWorkflow = &workflows
	}
	if status != ModuleStatusPending {
		notStarted, err := isModuleNotStarted(ctx, userID, moduleName, source)
		if err != nil {
			logger.WithUser(userID).Errorln(err)
			return err
		}
		if notStarted {
			err = SendSignalWithStatus(ctx, userID, moduleName, ModuleStatusPending, 1, source, signalData, userWorkflow)
			if err != nil {
				return err
			}
		}
	}

	// TODO: Move to filter builder
	filters := []Filter{
		{Key: "source", Value: source},
		{Key: "transitionType", Value: "MODULE"},
		{Key: "status", Value: []string{ModuleStatusToTSMModuleStatusMapping[status]}},
	}
	signals, tsmModules, err := GetNextTSMModuleForFilters(ctx, userID, filters...)
	if err != nil {
		err = fmt.Errorf("error in getting next module from tsm workflow  %s with error %v",
			userID,
			err,
		)
		return err
	}
	if len(tsmModules) > 1 {
		err = fmt.Errorf("length of next modules > 1")
		return err
	}
	if len(tsmModules) == 0 {
		err = fmt.Errorf("no next module found for updating the status")
		return err
	}

	if tsmModules[0].ModuleName != moduleName {
		err = fmt.Errorf("%s module is not the expected next module, expected module: %s", moduleName, tsmModules[0].ModuleName)
		return err
	}

	// Acquire lock to make sure that the TSM has moved to the next stage
	if err := AcquireLock(userID); err != nil {
		logger.WithUser(userID).Errorln(err)
		return err
	}
	defer ReleaseLock(userID)

	signalData[runnerconstants.SignalDataSourceTag] = source
	err = temporalClient.SignalWorkflow(
		ctx,
		userWorkflow.WorkflowID,
		"", // intentionally kept empty to pick latest execution
		signals[0],
		signalData,
	)
	if err != nil {
		err = fmt.Errorf("error in sending %s signal to tsm workflow  %s with error %v", signals[0], userID, err)
		return err
	}

	err = temporalsignallogging.Insert(signals[0], userID, userWorkflow.ID, signalData)
	if err != nil {
		log.Errorf("error in insertting into signal logging table, signalName: %s, workflowID : %s, userID %s",
			signals[0], userID, userWorkflow.WorkflowID)
	}
	attempt := 0
	for attempt < MaxAttempts {
		attempt++
		tsmModule, err := GetLastExecutedModuleForTSMWorkflow(ctx, userID, "")
		if err != nil {
			log.Errorf("error in getting last executed module %s signal to tsm workflow  %s with error %v, attempt: %d",
				signals[0], userID, err, attempt)
		} else if tsmModule.ModuleName != moduleName {
			log.Errorf("moduleName doesnt match desired state moduleName: %s, modulestatus: %d, userId: %s",
				moduleName, status, userID)
		} else if TSMModuleStatusToModuleStatusMapping[tsmModule.ModuleStatus] != status {
			log.Errorf("moduleName doesnt match desired state moduleName: %s, modulestatus: %d, userId: %s",
				moduleName, status, userID)
		} else {
			break
		}
		time.Sleep(DelayBetweenCalls)
	}

	return nil
}

func SendSignalWithStatus(
	ctx context.Context,
	userID string,
	moduleName string,
	status int,
	subStatus int,
	source string,
	signalData map[string]interface{},
	userWorkflow *userworkflows.UserWorkflow,
) error {
	return sendSignalWithStatus(ctx, userID, moduleName, status, subStatus, source, signalData, userWorkflow, temporalclient.Client)
}

func SendSignalWithStatusAndClient(
	ctx context.Context,
	userID string,
	moduleName string,
	status int,
	subStatus int,
	source string,
	signalData map[string]interface{},
	userWorkflow *userworkflows.UserWorkflow,
	temporalClient client.Client,
) error {
	return sendSignalWithStatus(ctx, userID, moduleName, status, subStatus, source, signalData, userWorkflow, temporalClient)
}

func SendTransitionTypeSwitchSignal(
	ctx context.Context,
	userID string,
	source string,
	signalData map[string]interface{},
	userWorkflow *userworkflows.UserWorkflow,
) error {
	if userWorkflow == nil {
		workflows, err := userworkflows.Get(userID, TSM)
		if err != nil {
			err := fmt.Errorf("error fetching latest running tsm workflow for user: %s with error %v", userID, err)
			logger.WithUser(userID).Errorln(err)
			errorHandler.ReportToSentryWithFields(map[string]interface{}{
				"userID": userID,
			}, err)
			return err
		}
		userWorkflow = &workflows
	}

	// TODO: Move to filter builder
	filters := []Filter{
		{Key: "transitionType", Value: "SWITCH"},
	}
	signals, tsmModules, err := GetNextTSMModuleForFilters(ctx, userID, filters...)
	if err != nil {
		err = fmt.Errorf("error in getting next module from tsm workflow  %s with error %v",
			userID,
			err,
		)
		return err
	}
	if len(tsmModules) == 0 {
		err = fmt.Errorf("no switch module found for transitioning")
		return err
	}

	// Acquire lock to avoid race condition
	if err := AcquireLock(userID); err != nil {
		logger.WithUser(userID).Errorln(err)
		return err
	}
	defer ReleaseLock(userID)

	signalData[runnerconstants.SignalDataSourceTag] = source
	err = temporalclient.Client.SignalWorkflow(
		ctx,
		userWorkflow.WorkflowID,
		"", // intentionally kept empty to pick latest execution
		signals[0],
		signalData,
	)
	if err != nil {
		err = fmt.Errorf("error in sending %s signal to tsm workflow  %s with error %v", signals[0], userID, err)
		return err
	}

	err = temporalsignallogging.Insert(signals[0], userID, userWorkflow.ID, signalData)
	if err != nil {
		log.Errorf("error in insertting into signal logging table, signalName: %s, workflowID : %s, userID %s",
			signals[0], userID, userWorkflow.WorkflowID)
	}
	time.Sleep(200 * time.Millisecond)
	return nil
}

func GetNextTSMModuleForFilters(ctx context.Context, userID string, filters ...Filter) ([]string, []runner.TemporalStateMachineMetadata, error) {
	res, _, err := temporalutility.FetchOutputKeyFromWorkflow(
		ctx,
		userID, TSM, runnerconstants.TSMNextStepKey, nil, 200, 20)
	if err != nil {
		logger.WithUser(userID).Errorln(err)
		return nil, nil, err
	}
	if res == nil {
		return nil, nil, nil
	}
	var tsmMetadata map[string]runner.TemporalStateMachineMetadata

	if err = mapstructure.Decode(res, &tsmMetadata); err != nil {
		return nil, nil, err
	}
	temporalOutput, _, err := temporalutility.FetchOutputKeyFromWorkflow(
		ctx,
		userID, TSM, runnerconstants.TemporalStateMachineKey, nil, 200, 20)
	if err != nil {
		logger.WithUser(userID).Errorln(err)
		return nil, nil, err
	}
	signals, modules, err := FilterModules(tsmMetadata, temporalOutput, filters...)
	if err != nil {
		return nil, nil, err
	}
	if len(modules) != len(signals) {
		return nil, nil, fmt.Errorf("length of signals and modules doesnt match")
	}

	return signals, modules, nil
}

func GetLastExecutedModuleForTSMWorkflow(ctx context.Context, userID string, sourceEntityID string) (*runner.TemporalStateMachineMetadata, error) {
	tsmMetadata, err := GetAllExecutedModulesForTSMWorkflow(ctx, userID)
	if err != nil {
		return nil, err
	}
	if len(tsmMetadata) > 0 {
		return &tsmMetadata[len(tsmMetadata)-1], nil
	}

	return nil, nil
}

func GetAllExecutedModulesForTSMWorkflow(ctx context.Context, userID string) ([]runner.TemporalStateMachineMetadata, error) {
	res, _, err := temporalutility.FetchOutputKeyFromWorkflow(
		ctx,
		userID, TSM, runnerconstants.TemporalStateMachineKey, nil, 200, 20)
	if err != nil {
		logger.WithUser(userID).Errorln(err)
		return nil, err
	}

	var tsmMetadata []runner.TemporalStateMachineMetadata
	if err = mapstructure.Decode(res, &tsmMetadata); err != nil {
		return nil, err
	}

	return tsmMetadata, nil
}

// GetModuleWithLatestStatusForTSMWorkflow Gets last inserted module with status
func GetModuleWithLatestStatusForTSMWorkflow(ctx context.Context, userID string, moduleName string) (output *runner.TemporalStateMachineMetadata, err error) {
	executedModules, err := GetAllExecutedModulesForTSMWorkflow(ctx, userID)
	if err != nil {
		return output, err
	}
	for _, module := range executedModules {
		if moduleName == module.ModuleName {
			output = &module
		}
	}

	return output, nil
}

func isModuleNotStarted(
	ctx context.Context,
	userID string,
	moduleName string,
	source string,
) (bool, error) {
	// TODO: Move to filter builder
	filters := []Filter{
		{Key: "source", Value: source},
		{Key: "transitionType", Value: "MODULE"},
		{Key: "status", Value: []string{ModuleStatusToTSMModuleStatusMapping[ModuleStatusPending]}},
	}
	_, tsmModules, err := GetNextTSMModuleForFilters(ctx, userID, filters...)
	if err != nil {
		err = fmt.Errorf("error in getting next module from tsm workflow  %s with error %v", userID, err)
		return false, err
	}
	if len(tsmModules) == 0 {
		return false, nil
	}
	if len(tsmModules) > 1 {
		err = fmt.Errorf("length of next modules if greater than 1, length : %d", len(tsmModules))
		return false, err
	}
	if moduleName == tsmModules[0].ModuleName {
		return true, nil
	}
	err = fmt.Errorf("signal sent request is not for next module, requestedModule: %s, nextModule %s", moduleName, tsmModules[0].ModuleName)
	return false, err
}

func GetAllSignalEventsForWorkflow(ctx context.Context, workflowID string) ([]*history.HistoryEvent, error) {
	iter := temporalclient.Client.GetWorkflowHistory(ctx, workflowID, "", false, enums.HISTORY_EVENT_FILTER_TYPE_UNSPECIFIED)
	events := []*history.HistoryEvent{}

	for iter.HasNext() {
		event, err := iter.Next()
		if err != nil {
			logger.WithContext(ctx).Errorln("[GetAllSignalEventsForWorkflow] Error in getting next event", err)
			return nil, err
		}
		if event.GetEventType() == enums.EVENT_TYPE_WORKFLOW_EXECUTION_SIGNALED { // Signal event
			events = append(events, event)
		}
	}
	return events, nil
}

func getEventIDForSignaledModule(ctx context.Context, userID string, opts ResetToStateOptions) (eventID int64, err error) {
	executedModules, err := GetAllExecutedModulesForTSMWorkflow(ctx, userID)
	if err != nil {
		logger.WithUser(userID).Errorln("[GetEventIDForSignalledModule] Error in getting all executed modules for TSM workflow", err)
		return 0, err
	}

	currentlyRunningWf, err := temporalutility.FetchWfByStatusAndHealthCheck(ctx, userID, TSM, []string{constants.TemporalStatusRunning})
	if err != nil {
		logger.WithUser(userID).Errorln("[GetEventIDForSignalledModule] Error in fetching currently running workflow", err)
		return 0, err
	}

	if currentlyRunningWf == nil {
		return 0, fmt.Errorf("workflow not found")
	}

	events, err := GetAllSignalEventsForWorkflow(ctx, currentlyRunningWf.WorkflowID)
	if err != nil {
		logger.WithUser(userID).Errorln("[GetEventIDForSignalledModule] Error in getting all signal events for workflow", err)
		return 0, err
	}

	// Filter the event by module name and status
	var selectedModule *runner.TemporalStateMachineMetadata

	for _, module := range executedModules {
		if module.ModuleName == opts.ModuleName && module.ModuleStatus == opts.ModuleStatus {
			selectedModule = &module
			break
		}
	}

	if selectedModule == nil {
		logger.WithUser(userID).Errorln("[GetEventIDForSignalledModule] Module not found")
		return 0, fmt.Errorf("module not found")
	}

	// Get the event ID for the selected module
	// Select the event of type signal and the signal name matches the signal emitted by the selected module

	for i := len(events) - 1; i >= 0; i-- {
		event := events[i]
		if event.EventType == enums.EVENT_TYPE_WORKFLOW_EXECUTION_SIGNALED && event.GetWorkflowExecutionSignaledEventAttributes().SignalName == selectedModule.SignalName { // Signal event
			return event.GetEventId(), nil
		}
	}

	return 0, fmt.Errorf("event not found")
}

func getListOfEventsGoingToBeReset(ctx context.Context, userID string, opts ResetToStateOptions) ([]*runner.TemporalStateMachineMetadata, error) {
	executedModules, err := GetAllExecutedModulesForTSMWorkflow(ctx, userID)
	if err != nil {
		logger.WithUser(userID).Errorln("[GetListOfEventsGoingToBeReset] Error in getting all executed modules for TSM workflow", err)
		return nil, err
	}

	var eventsGoingToBeReset []*runner.TemporalStateMachineMetadata
	// Find the module in the executed modules
	selectedModuleIndex := -1
	for i, module := range executedModules {
		if module.ModuleName == opts.ModuleName && module.ModuleStatus == opts.ModuleStatus {
			selectedModuleIndex = i
			break
		}
	}

	if selectedModuleIndex == -1 {
		logger.WithUser(userID).Errorln("[GetListOfEventsGoingToBeReset] Module not found")
		return nil, fmt.Errorf("module not found")
	}

	// Select the modules (which are going to be reset - modules ahead of the current module)
	for i := selectedModuleIndex + 1; i < len(executedModules); i++ {
		eventToReset := executedModules[i]

		// Add only selected information to avoid extra data
		eventsGoingToBeReset = append(eventsGoingToBeReset, &runner.TemporalStateMachineMetadata{
			ModuleName:   eventToReset.ModuleName,
			ModuleStatus: eventToReset.ModuleStatus,
			ModuleType:   eventToReset.ModuleType,
		})
	}

	return eventsGoingToBeReset, nil
}

// ResetWorkflowToModule resets the workflow to the given module,
// it resets the state machine to the previous state and inserts the module in the user_module_mapping
func ResetWorkflowToModule(ctx context.Context, userID string, opts ResetToStateOptions) (rwr ResetWorkflowResponse, err error) {
	currentlyRunningWf, err := temporalutility.FetchWfByStatusAndHealthCheck(ctx, userID, TSM, []string{constants.TemporalStatusRunning})
	if err != nil {
		logger.WithUser(userID).Errorln("[ResetWorkflowToModule] Error in fetching currently running workflow", err)
		return rwr, err
	}

	if currentlyRunningWf == nil {
		logger.WithUser(userID).Errorln("[ResetWorkflowToModule] Workflow not found")
		return rwr, fmt.Errorf("workflow not found")
	}

	rwr.OldRunID = currentlyRunningWf.RunID

	eventID, err := getEventIDForSignaledModule(ctx, userID, opts)
	if err != nil {
		logger.WithUser(userID).Errorln("[ResetWorkflowToModule] Error in getting event ID for signalled module", err)
		return rwr, err
	}

	workflowTaskCompletedEventID, err := temporalutility.FindNextWorkflowTaskCompletedEventID(ctx, currentlyRunningWf.WorkflowID, eventID)
	if err != nil {
		logger.WithUser(userID).Errorln("[ResetWorkflowToModule] Error in finding next workflow task completed event ID", err)
		return rwr, err
	}

	eventsGoingToBeReset, err := getListOfEventsGoingToBeReset(ctx, userID, opts)
	if err != nil {
		logger.WithUser(userID).Errorln("[ResetWorkflowToModule] Error in getting list of events going to be reset", err)
		return rwr, err
	}

	rwr.ResetModules = eventsGoingToBeReset

	resetResponse, err := temporalutility.ResetWorkflowToEventID(ctx, currentlyRunningWf.WorkflowID, workflowTaskCompletedEventID, fmt.Sprintf("[TSM] Reset requested from Admin API. Reason: %s", opts.ResetReason))
	if err != nil {
		logger.WithUser(userID).Errorln("[ResetWorkflowToModule] Error in resetting workflow", err)
		return rwr, err
	}

	updatedWorkflow := currentlyRunningWf

	uniqueModules := make(map[string]struct{})

	for _, event := range eventsGoingToBeReset {
		uniqueModules[event.ModuleName] = struct{}{}
	}

	for moduleName := range uniqueModules {
		// Archive the user workflow and module mapping
		userWorkflow, err := userworkflows.Get(userID, moduleName)
		if err != nil {
			logger.WithUser(userID).Errorln("[ResetWorkflowToModule] Error in getting user workflow", err)
		}
		switch err {
		case sql.ErrNoRows:
			continue
		case nil:
			logger.WithUser(userID).Infof("[ResetWorkflowToModule] Terminating and updating user workflow for module %s with workflowID %s and runID %s", moduleName, userWorkflow.WorkflowID, userWorkflow.RunID)
			// Not handling errors for terminate and update running workflow since the workflow can be either - [Already Completed, Running, Failed]. We don't need to handle these cases explicitly.
			_ = temporalutility.TerminateAndUpdateRunningWorkflow(ctx, userID, userWorkflow.WorkflowID, userWorkflow.RunID, fmt.Sprintf("[TSM] Reset requested from Admin API. Reason: %s", opts.ResetReason))
			if err := userworkflows.ArchiveWorkflowStatusByModuleName(nil, userID, fmt.Sprintf("[TSM] Reset requested from Admin API. Reason: %s", opts.ResetReason), moduleName); err != nil {
				logger.WithUser(userID).Errorln("[ResetWorkflowToModule] Error in archiving user workflow", err)
			}
		default:
			return rwr, err
		}
	}

	// Update the user workflow entry with updated status (Just in case it was updated by some other concurrent operation)
	if err := userworkflows.UpdateStatus(nil, userID, constants.TemporalStatusRunning, currentlyRunningWf.WorkflowID, currentlyRunningWf.RunID, "", ""); err != nil {
		logger.WithUser(userID).Errorln("[ResetWorkflowToModule] Error in updating user workflow status", err)
		return rwr, err
	}
	// Update the user workflow entry with the new runID
	updatedWorkflow.RunID = resetResponse.GetRunId()
	updatedWorkflow.ErrorType.Valid = true
	updatedWorkflow.ErrorType.String = ""
	updatedWorkflow.FailureReason = ""

	if err := updatedWorkflow.UpdateUsingID(ctx, nil); err != nil {
		logger.WithUser(userID).Errorln("[ResetWorkflowToModule] Error in updating user workflow", err)
		return rwr, err
	}

	rwr.NewRunID = resetResponse.GetRunId()
	rwr.WorkflowID = currentlyRunningWf.WorkflowID

	return rwr, nil
}

func VerifyTSMModuleMovement(ctx context.Context, userID string) error {
	// We're polling for the next modules immediately after signaling that the current module has been moved
	// (e.g., completed or failed). However, this can lead to a race condition: the signal might be accepted
	// by Temporal, but the worker may not have finished updating the module's state. As a result, when we poll
	// for the next modules, we might get the same module again. To avoid this, we should wait until the module
	// has actually been moved forward.

	if err := retry.CustomExponentRetry(10, 100*time.Millisecond, 2, func() error {
		// Wait for TSM to move i.e. lock to be released
		if !IsLockAcquirable(userID) {
			logger.WithUser(userID).Warnln("Lock not available")
			return errors.New("lock not available")
		}

		return nil
	}); err != nil {
		errorHandler.ReportToSentryWithFields(map[string]interface{}{
			"userID": userID,
		}, err)
		logger.WithUser(userID).Errorln(err)
		return err
	}

	return nil
}
