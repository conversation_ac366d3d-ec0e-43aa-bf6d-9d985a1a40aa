package tsm

import (
	"errors"
	"finbox/go-api/functions/logger"
	"finbox/go-api/utils/lockutils"
	"fmt"
	"time"
)

var (
	ErrLockNotAvailable = errors.New("lock not available")
	LockTTL             = 30 * time.Second
)

func getLockKey(userID string) string {
	return fmt.Sprintf("tsmlock_%s_%s", userID, FlagTSMFlow)
}

func AcquireLock(userID string) error {
	if !lockutils.LockAcquirable(getLockKey(userID)) {

		return ErrLockNotAvailable
	}
	if err := lockutils.LockWithTimeout(getLockKey(userID), LockTTL); err != nil {
		logger.WithUser(userID).Errorln(err)
		return err
	}
	return nil
}

func ReleaseLock(userID string) error {
	lockutils.UnLock(getLockKey(userID), nil)
	return nil
}

func IsLockAcquirable(userID string) bool {
	return lockutils.LockAcquirable(getLock<PERSON>ey(userID))
}
