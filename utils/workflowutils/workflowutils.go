package workflowutils

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"finbox/go-api/constants"
	"finbox/go-api/functions/logger"
	"finbox/go-api/infra/db"
	"finbox/go-api/models/userjourney"
	"finbox/go-api/models/usermodulemapping"
	"finbox/go-api/models/users"
	"finbox/go-api/models/workflow"
	"finbox/go-api/utils/workflowutils/tsm"
	"fmt"
)

var database = db.GetDB()
var log = logger.Log

// GetCurrentWorkFlow returns workflow from user journey table and error if any
func GetCurrentWorkFlow(userID string) (workflow.WorkFlow, error) {
	var workflow workflow.WorkFlow
	type dbResp struct {
		WorkflowID   string
		Modules      string
		WorkflowName string
	}

	var dbObj dbResp
	query := `SELECT wf.workflow_id AS workflowid,
				wf.workflow_data::jsonb ->> 'modules' AS modules,
				wf.workflow_name AS workflowname
			FROM user_journey uj
			JOIN workflow wf ON uj.workflow_id = wf.workflow_id
			WHERE uj.user_id = $1;`
	err := database.Get(&dbObj, query, userID)
	if err != nil {
		log.Error(err)
		return workflow, err
	}
	workflow.WorkFlowID = dbObj.WorkflowID
	workflow.WorkFlowName = dbObj.WorkflowName
	err = json.Unmarshal([]byte(dbObj.Modules), &workflow.Modules)
	if err != nil {
		log.Error(err)
		return workflow, err
	}
	return workflow, nil
}

// GetModuleFromWorkflow returns module based on workflow
//
// It does not use any business logic e.g. checking wait status
// if no lastModuleMapping available then pass nil
// for all practical purposes use GetCurrentModule
func GetModuleFromWorkflow(currentWorkflow workflow.WorkFlow, lastModule *usermodulemapping.UserModuleMapping) (CurrentModule, error) {
	var currentModule CurrentModule
	if len(currentWorkflow.Modules) < 1 {
		return currentModule, errors.New("invalid workflow")
	}

	currentModuleState := constants.ModuleStateStart
	currentModuleMapIndex := constants.ModuleDetailsMapIndexFresh

	lastModuleFound := lastModule == nil // indicates whether last module was found in modules list from workflow yet
	currentModuleFound := false          // used in stepper to decide if we have found the current module to show

	stepper := make([]ModuleStruct, 0)
	pastStepper := make([]ModuleStruct, 0)

	var stepperState string
	var stepperMapIndex int
	var pastStepperModuleType string

	for i := 0; i < len(currentWorkflow.Modules); i++ { // iterate over the modules list

		if !currentModuleFound && currentWorkflow.Modules[i].IsActive && lastModuleFound {
			// first stepper case
			currentModule.Module = GenerateModule(currentWorkflow.Modules[i], currentModuleMapIndex, currentModuleState)
			currentModuleFound = true
		}

		if !currentModuleFound {
			// populate stepper
			// marking all the previous modules of same type as completed in stepper
			// a better approach would be to get data from db itself
			// however this is fast, although in some cases little incorrect, example: workflow was changed and some modules
			// of same type which were never completed earlier were present
			stepperMapIndex = constants.ModuleDetailsMapIndexFresh // TODO: mapIndex here could be more descriptive
			stepperState = constants.StepperStateCompleted
			switch pastStepperModuleType {
			case "", currentWorkflow.Modules[i].ModuleType:
				pastStepperModuleType = currentWorkflow.Modules[i].ModuleType
			default:
				pastStepper = nil // new module type has started
				pastStepperModuleType = currentWorkflow.Modules[i].ModuleType
			}
			pastStepper = append(pastStepper, GenerateModule(currentWorkflow.Modules[i], stepperMapIndex, stepperState))
		} else if currentModuleFound && currentWorkflow.Modules[i].IsActive && currentWorkflow.Modules[i].ModuleType == currentModule.Module.ModuleType {
			if currentModule.Module.ModuleName != currentWorkflow.Modules[i].ModuleName {
				stepperState = constants.StepperStateUpcoming
				stepperMapIndex = constants.ModuleDetailsMapIndexFresh
			} else if lastModule == nil { // current module and first module both
				stepperState = constants.StepperStateOngoing
				stepperMapIndex = constants.ModuleDetailsMapIndexFresh
			} else if pastStepperModuleType == currentModule.Module.ModuleType { // current module therefore add past stepper
				stepper = append(stepper, pastStepper...)
			}
			stepper = append(stepper, GenerateModule(currentWorkflow.Modules[i], stepperMapIndex, stepperState))
		}

		if !currentModuleFound && lastModule != nil && lastModule.ModuleName == currentWorkflow.Modules[i].ModuleName { // find current module
			lastModuleFound = true
			switch lastModule.ModuleStatus {
			case constants.UserModuleStatusCompleted:
				if lastModule.ModuleSubStatus != 1 {
					i--
					currentModuleMapIndex = constants.ModuleDetailsMapIndexWait
					currentModuleState = constants.ModuleStateWait
					stepperMapIndex = constants.ModuleDetailsMapIndexWait
					stepperState = constants.StepperStateOngoing
					pastStepper = pastStepper[:len(pastStepper)-1] // remove last added stepper
				} else {
					if i == len(currentWorkflow.Modules)-1 { // find if completed module was last module
						i--                                                          // if last module was completed module, send same module again
						currentModuleMapIndex = constants.ModuleDetailsMapIndexFresh // TODO: module details mapIndex here could be more descriptive for completed modules
						currentModuleState = constants.ModuleStateCompleted
						stepperState = constants.StepperStateCompleted
						stepperMapIndex = constants.ModuleDetailsMapIndexFresh // TODO: module details mapIndex here could be more descriptive for completed modules
						pastStepper = pastStepper[:len(pastStepper)-1]         // remove last added stepper
					} else {
						currentModuleMapIndex = constants.ModuleDetailsMapIndexFresh
						currentModuleState = constants.ModuleStateStart
						stepperMapIndex = constants.ModuleDetailsMapIndexFresh
						stepperState = constants.StepperStateOngoing
					}
				}
			case constants.UserModuleStatusPending:
				i-- // show same module in case of pending with state as wait
				currentModuleMapIndex = constants.ModuleDetailsMapIndexWait
				currentModuleState = constants.ModuleStateWait
				stepperMapIndex = constants.ModuleDetailsMapIndexWait
				stepperState = constants.StepperStateOngoing
				pastStepper = pastStepper[:len(pastStepper)-1] // remove last added stepper
			case constants.UserModuleStatusFailed:
				i-- // show same module in case of failed with state as failed
				currentModuleMapIndex = constants.ModuleDetailsMapIndexFailed
				currentModuleState = constants.ModuleStateFailed
				stepperMapIndex = constants.ModuleDetailsMapIndexFailed
				stepperState = constants.StepperStateFailed
				pastStepper = pastStepper[:len(pastStepper)-1] // remove last added stepper
			default:
				return currentModule, fmt.Errorf("invalid last module status found: %d", lastModule.ModuleStatus)
			}
		}
	}
	if currentModuleFound {
		currentModule.Stepper = stepper
		return currentModule, nil
	}
	return currentModule, errors.New("no next active module found")
}

// GetCurrentModule returns stepper and current module object
func GetCurrentModule(userID, sourceEntityID, loanApplicationID string) (CurrentModule, error) {
	// added temporal state machine workflow
	if tsm.IsTSMFlow(userID) {
		return GetCurrentModuleFromTSMWorkflow(context.Background(), userID, sourceEntityID)
	}

	var currentModule CurrentModule
	userStateValObj, err := GetUserStateValues(userID, sourceEntityID, loanApplicationID)
	if err != nil {
		logger.WithUser(userID).Error(err)
		return currentModule, err
	}

	userStateValObj.SourceEntityID = sourceEntityID
	currentModule, ok := InferModuleFromTerminalStates(userStateValObj)
	if ok {
		return currentModule, nil
	}

	lastModuleMapping, err := usermodulemapping.GetLast(userID)
	if err != nil && err != sql.ErrNoRows {
		logger.WithUser(userID).Error(err)
		return CurrentModule{}, err
	}

	if currentModule, ok = InferModuleFromLastModule(lastModuleMapping); ok {
		return currentModule, nil
	}

	currentWorkflow, err := workflow.GetByID(userStateValObj.WorkflowID)
	if err != nil {
		logger.WithUser(userID).Error(err)
		return currentModule, err
	}

	currentModule, err = GetModuleFromWorkflow(currentWorkflow, lastModuleMapping)
	if err != nil {
		logger.WithUser(userID).Error(err)
		return CurrentModule{}, err
	}
	// for disbursal may need to put some checks
	return currentModule, nil
}

// UpdateWorkFlow inserts new work flow id in user journey for a given program
func UpdateWorkFlow(userID string, sourceEntityID string, newWorkflowName string) error {
	//only update user workflow when there exists lastModuleMapping of user in new workflow
	newWorkFlow, err := workflow.GetByName(newWorkflowName)
	if err != nil {
		log.Error(err)
		return err
	}
	logger.DebugWithUser(userID, fmt.Sprintf("-- NEW WORKFLOW: %+v\n", newWorkFlow))

	lastUserModuleMapping, err := usermodulemapping.GetLast(userID)
	if err != nil {
		if err == sql.ErrNoRows {
			log.Info("no module found")
			err := userjourney.AssignWorkflow(newWorkFlow.WorkFlowID, userID)
			if err != nil {
				log.Error(err)
				return err
			}
			return nil
		} else {
			log.Error(err)
			return err
		}
	}
	logger.DebugWithUser(userID, fmt.Sprintf("-- lastUserModuleMapping: %+v\n", lastUserModuleMapping))
	isModuleExists := false
	for i := range newWorkFlow.Modules {
		if newWorkFlow.Modules[i].ModuleName == lastUserModuleMapping.ModuleName {
			isModuleExists = true
			break
		}
	}
	if !isModuleExists {
		errString := fmt.Sprintf(`user last module mapping does not exist in new workflow - %s for userID - %s, sourceEntityID - %s`, newWorkflowName, userID, sourceEntityID)
		log.Error(errString)
		return errors.New(errString)
	}
	err = userjourney.AssignWorkflow(newWorkFlow.WorkFlowID, userID)
	if err != nil {
		log.Error(err)
		return err
	}
	return nil
}

// GetCurrentModuleNameByUserID retrieves the name of the current module associated with a user.
// It fetches the current workflow of the user, then determines the last module mapping
// and the current module based on the workflow and the last module mapping.
//
// Parameters:
//
//	userID: The unique identifier of the user.
//
// Returns:
//
//	string: The name of the current module.
//	error: An error if any occurred during the operation.
func GetCurrentModuleNameByUserID(userID string) (string, error) {
	var moduleName string
	userObj, _ := users.Get(userID)
	if tsm.IsTSMFlow(userID) {
		currentModule, err := GetModuleFromTSMWorkflow(context.Background(), userID, userObj.SourceEntityID)
		if err != nil {
			log.Error(err)
			return moduleName, err
		}
		moduleName = currentModule.Module.ModuleName
	} else {
		currentWorkflow, err := GetCurrentWorkFlow(userID)
		if err != nil {
			log.Error(err)
			return moduleName, err
		}

		lastModuleMapping, err := usermodulemapping.GetLast(userID)
		if err != nil && err != sql.ErrNoRows {
			log.Error(err)
			return moduleName, err
		}
		currentModule, err := GetModuleFromWorkflow(currentWorkflow, lastModuleMapping)
		if err != nil {
			log.Error(err)
			return moduleName, err
		}
		moduleName = currentModule.Module.ModuleName
	}
	return moduleName, nil
}
