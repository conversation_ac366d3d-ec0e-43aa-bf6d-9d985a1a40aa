package digioenachtasks

import (
	"context"
	"encoding/json"
	"finbox/go-api/constants"
	"finbox/go-api/errorHandler"
	"finbox/go-api/functions/logger"
	"finbox/go-api/functions/services/digio"
	"finbox/go-api/models/adhocpresentationaudittrail"
	"finbox/go-api/thirdparty/upiautopay/digioupi"
	"net/http"
)

var log = logger.Log

func StatusPollingTask(ctx context.Context, payload map[string]string) error {
	defer func() {
		errorHandler.RecoveryNoResponse()
	}()

	err := digio.UpdateMandateUmrnFromWorker(nil, payload["mandate_id"])
	if err != nil {
		log.Println(err)
		return err
	}

	log.Errorln("polling successful for : " + payload["mandate_id"])

	return nil
}

func UPIStatusPollingTask(ctx context.Context, payload map[string]string) error {
	defer func() {
		errorHandler.RecoveryNoResponse()
	}()
	err := digioupi.FetchAndUpdateUPIUMRN(nil, payload["mandate_id"])
	if err != nil {
		log.Println(err)
		return err
	}

	log.Infoln("polling successful for : " + payload["mandate_id"])

	return nil
}

func BulkPresentationTask(ctx context.Context, data map[string]interface{}) error {
	defer func() { errorHandler.RecoveryNoResponse() }()

	log.Println("in bulk_presentation_task_async_server")

	var queueEntry digio.DigioQueueDataStruct
	jsonData, err := json.Marshal(data)
	if err != nil {
		log.Println("Error marshalling data:", err)
		return err
	}
	if err := json.Unmarshal(jsonData, &queueEntry); err != nil {
		log.Error(err)
		return err
	}

	bulkPayload, err := digio.BuildBulkPresentationPayload(ctx, queueEntry)
	if err != nil {
		log.Errorln(err)
		return err
	}
	if len(bulkPayload) == 0 {
		return nil
	}

	response, err, statusCode := digio.CreateBulkDigioPresentation(ctx, bulkPayload, queueEntry.LenderID, queueEntry.RequestID)
	if err != nil {
		if statusCode != http.StatusOK {
			digio.MarkPresentationFailureMessage(ctx, response, bulkPayload, queueEntry)
			log.Error(err)
		} else {
			errorHandler.LogErrorAndPanic(err)
		}
	} else {
		digio.MarkPresentationSuccessMessage(ctx, response, bulkPayload, queueEntry)
	}

	resp, err := adhocpresentationaudittrail.Get(queueEntry.RequestID)
	if err != nil {
		errorHandler.LogErrorAndPanic(err)
	}
	if resp.PresentedCount == (resp.SuccessCount + resp.FailedCount) {
		err = adhocpresentationaudittrail.UpdateStatus(queueEntry.RequestID, constants.AdhocNachStatusCompleted, "completed")
		if err != nil {
			errorHandler.LogErrorAndPanic(err)
		}
	}

	return nil
}
