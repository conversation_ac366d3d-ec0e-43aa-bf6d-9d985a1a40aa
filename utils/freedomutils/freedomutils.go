// Package freedomutils contains utils method for freedom routes
package freedomutils

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"finbox/go-api/authentication"
	"finbox/go-api/conf"
	"finbox/go-api/constants"
	"finbox/go-api/errorHandler"
	"finbox/go-api/functionalityModels"
	"finbox/go-api/functions/journey"
	"finbox/go-api/functions/logger"
	"finbox/go-api/functions/structs"
	"finbox/go-api/infra/db"
	"finbox/go-api/infra/redis"
	"finbox/go-api/infra/s3"
	"finbox/go-api/utils/general"
	"fmt"
	"net/http"
	"strings"
)

var log = logger.Log
var database = db.GetDB()

/* #nosec */
const creditLinkConfigSuffix = "cl_config"

type LendingPartnerStruct struct {
	Name    string `json:"name"`
	LogoURL string `json:"logoUrl"`
}

type CreditLinkConfigStruct struct {
	PartnerLogoURL     string                 `json:"partnerLogoURL"`
	PartnerName        string                 `json:"partnerName"`
	ShowLendingPartner bool                   `json:"showLendingPartner"`
	ActionText         string                 `json:"actionText"`
	PrimaryColor       string                 `json:"primaryColor"`
	SecondaryColor     string                 `json:"secondaryColor"`
	LendingPartners    []LendingPartnerStruct `json:"lendingPartners"`
	Banner             string                 `json:"banner"`
	EligibleAmount     float64                `json:"eligibleAmount"`
	ClientAPIKey       string                 `json:"clientApiKey"`
	Interest           float64                `json:"interest"`
	SourceEntityID     string                 `json:"sourceEntityID"`
	DSAID              string                 `json:"dsaID"`
	ProductType        string                 `json:"productType"`
	SkipLandingPage    bool                   `json:"skipLandingPage"`
	BureauConsentText  string                 `json:"bureauConsentText"`
	EligibleAmountText string                 `json:"eligibleAmountText"`
	DisplayTiles       interface{}            `json:"displayTiles"`
}

// AllowSourcingEntity returns a boolean flag indicating whether freedom route APIs allowed for a sourcing entity
func AllowSourcingEntity(sourceEntityID string) bool {
	if conf.ENV == conf.ENV_UAT {
		return true
	}

	var cnt int
	query := `select count(*) from source_entity where source_entity_id=$1 and partner_code is not NULL;`
	err := database.Get(&cnt, query, sourceEntityID)
	if err != nil {
		log.Println(err)
	}

	if cnt != 0 {
		return true
	}
	return journey.IIFLAgg(sourceEntityID, false).IsAgg || journey.IsMuthootCLPartner(sourceEntityID)
}

// AllowUserBasedOnMobileNumber returns a boolean flag indicating whether freedom route APIs allowed for mobile number
func AllowUserBasedOnMobileNumber(sourceEntityID string, mobile string) bool {
	var cnt int
	query := `select count(*) from users where source_entity_id = $1 and source = $2 and mobile = $3 and status != $4;`
	err := database.Get(&cnt, query, sourceEntityID, constants.LeadSourceAPIStack, mobile, constants.UserStatusArchived)
	if err != nil {
		logger.Log.Errorln(err)
		errorHandler.ReportToSentryWithFields(errorHandler.ErrorFields{"mobile": mobile, "source-entity-id": sourceEntityID}, err)
		return false // block user
	}

	if cnt == 0 {
		return true
	}
	return false
}

// GetConfig returns cached credit link response for a given sourcing entity
// if second argument update is passed as truefresh values from DB are taken, returned and updated in redis
func GetConfig(partnerCode string, update bool) (CreditLinkConfigStruct, *structs.CustomError) {

	var config CreditLinkConfigStruct
	key := fmt.Sprintf("%s%s%s", partnerCode, "-v2-", creditLinkConfigSuffix)
	valueStr, _ := redis.Get(context.TODO(), key)
	if valueStr == "" {
		update = true
	}
	if update {
		type sourceStruct struct {
			CreditLinkConfig string `db:"credit_link_config"`
			SourceEntityID   string `db:"source_entity_id"`
			ClientAPIKey     string `db:"client_api_key"`
			DSAID            string `db:"dsaid"`
			ProductType      string `db:"product_type"`
		}

		var dbResp sourceStruct

		query := `SELECT 
		se.credit_link_config, se.source_entity_id, se.client_api_key, coalesce(d.dsa_id::TEXT, '') as dsaid, coalesce(d.product_type::TEXT, '') as product_type
		from source_entity se 
		left join dsa d on d.source_entity_id = se.source_entity_id AND d.owner_type in ($1, $2) AND d.status = $3
		where se.partner_code = $4`
		err := database.Get(&dbResp, query, constants.OwnerTypeLender, constants.OwnerTypePlatform, constants.DSAStatusApproved, partnerCode)
		if err != nil {
			log.Errorln(err)
		}

		if dbResp.DSAID == "" && !general.InArr(dbResp.SourceEntityID, getNonDSACreditlinkPlatforms()) {
			return config, &structs.CustomError{Err: errors.New("redirect required"), HTTPCode: http.StatusTemporaryRedirect}
		}

		err = json.Unmarshal([]byte(dbResp.CreditLinkConfig), &config)
		if err != nil {
			log.Errorln(err)
			return config, &structs.CustomError{Err: err, HTTPCode: http.StatusInternalServerError}
		}

		if config.Interest == 0 { // get interest from latest loan offer template if interest not present in creditlink config
			loanOfferObj, err := functionalityModels.GetLatestLoanOffer(dbResp.SourceEntityID)
			if err != nil {
				log.Errorln(err)
				return config, &structs.CustomError{Err: errors.New("loan offer unavailable"), HTTPCode: http.StatusConflict}
			}
			config.Interest = loanOfferObj.Interest
		}

		if partnerCode == "MS_WOFRLP" {
			config.Interest = 0
		}

		config.DSAID = dbResp.DSAID
		config.SourceEntityID = dbResp.SourceEntityID
		config.ProductType = dbResp.ProductType
		if config.ProductType == "" {
			config.ProductType, _, _ = journey.GetLoanType(dbResp.SourceEntityID)
		}
		config.Banner = constants.BannerTypeApply
		config.ClientAPIKey = dbResp.ClientAPIKey
		switch config.ProductType {
		case constants.LoanTypeBusinessLoan:
			config.ActionText = "Apply for loan"
			config.EligibleAmount = constants.MaxEligibleAmountBL
		case constants.LoanTypePersonalLoan:
			config.ActionText = "Apply for loan"
			config.EligibleAmount = constants.MaxEligibleAmountPL
		case constants.LoanTypeCreditLine, constants.LoanTypeOverDraft:
			if journey.IsMuthootCLPartner(config.SourceEntityID) {
				config.ActionText = "Apply for loan"
			} else {
				config.ActionText = "Apply for credit"
			}
			config.EligibleAmount = constants.MaxEligibleAmountPL // for credit line, PL default is fine for now
		default:
			if config.ActionText == "" {
				config.ActionText = "Apply for loan"
			}
			if config.EligibleAmount == 0 {
				config.EligibleAmount = constants.MaxEligibleAmountPL
			}
		}

		// add source level config changes to its dsa's
		CustomCreditLineConfigBySource(config.SourceEntityID, &config)

		valueBytes, err := json.Marshal(config)
		if err != nil {
			log.Errorln(err)
		}
		err = redis.Set(key, string(valueBytes), constants.OneWeek)
		if err != nil {
			log.Errorln(err)
		}
		if !strings.HasPrefix(config.PartnerLogoURL, "https:") {
			config.PartnerLogoURL = s3.GetPresignedURLS3(config.PartnerLogoURL, 60)
		}
		return config, nil
	}

	err := json.Unmarshal([]byte(valueStr), &config)
	if err != nil {
		log.Errorln(err)
	}

	if !strings.HasPrefix(config.PartnerLogoURL, "https:") {
		config.PartnerLogoURL = s3.GetPresignedURLS3(config.PartnerLogoURL, 60)
	}

	return config, nil

}

func GetEligibleAmount(userID, sourceEntityID, loanType string) float64 {

	var eligibleAmount float64
	if userID != "" {
		query := `SELECT eligible_amount from user_eligibility where user_id = $1 order by created_at desc limit 1`
		err := database.Get(&eligibleAmount, query, userID)
		if err != nil {
			log.Println(err)
		}
	} else {
		if loanType == "" {
			loanType, _, _ = journey.GetLoanType(sourceEntityID)
		}
		switch loanType {
		case constants.LoanTypeBusinessLoan:
			eligibleAmount = constants.MaxEligibleAmountBL
		case constants.LoanTypeEducationLoan:
			eligibleAmount = constants.MaxEligibleAmountEL
		default:
			eligibleAmount = constants.MaxEligibleAmountPL
		}
	}
	return eligibleAmount
}

// getNonDSACreditlinkPlatforms returns list of source entity ids (redis cached) who have credit link enabled
// but are non DSA
func getNonDSACreditlinkPlatforms() []string {
	keyName := fmt.Sprintf("%s%s%s", "non-dsa-cl-platforms-", "v3-", creditLinkConfigSuffix)
	valueStr, _ := redis.Get(context.TODO(), keyName)
	sourceEntityIDs := []string{}
	if valueStr != "" {
		err := json.Unmarshal([]byte(valueStr), &sourceEntityIDs)
		if err == nil {
			return sourceEntityIDs
		}
	}
	query := `SELECT source_entity_id from source_entity WHERE partner_code is not NULL and source_entity_id not in 
						(SELECT distinct source_entity_id from dsa UNION SELECT distinct source_entity_id from lender_agg)`
	err := database.Select(&sourceEntityIDs, query)
	if err != nil {
		log.Errorln(err)
	}
	// save in redis
	valueBytes, err := json.Marshal(sourceEntityIDs)
	if err != nil {
		log.Errorln(err)
		return sourceEntityIDs
	}
	valueStr = string(valueBytes)
	err = redis.Set(keyName, valueStr, constants.OneWeek)
	if err != nil {
		log.Errorln(err)
	}
	return sourceEntityIDs
}

// SaveCreditLinkMetadata marshals the metadata map and updates the freedom_users table.
func SaveCreditLinkMetadata(
	ctx context.Context,
	uniqueID string,
	mobile string,
	sourceEntityID string,
	newMetadata map[string]interface{},
	existingMetadataRaw sql.NullString,
) error {
	var finalMetadata map[string]interface{}

	if existingMetadataRaw.Valid && existingMetadataRaw.String != "" {
		var existingMetadata map[string]interface{}
		if err := json.Unmarshal([]byte(existingMetadataRaw.String), &existingMetadata); err != nil {
			errorHandler.ReportToSentryWithFields(map[string]interface{}{
				"uniqueID":        uniqueID,
				"error":           err.Error(),
				"raw_db_metadata": existingMetadataRaw.String,
			}, err)
			finalMetadata = make(map[string]interface{})
		} else {
			finalMetadata = existingMetadata
		}
	} else {
		finalMetadata = make(map[string]interface{})
	}

	for k, v := range newMetadata {
		finalMetadata[k] = v
	}

	if len(finalMetadata) == 0 {
		return nil
	}

	metadataJSON, jsonErr := json.Marshal(finalMetadata)
	if jsonErr != nil {
		logger.WithContext(ctx).Errorf("ERROR_SAVE: Error marshaling final metadata for uniqueID %s: %v. FinalMetadata: %+v", uniqueID, jsonErr, finalMetadata)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{
			"uniqueID": uniqueID,
			"metadata": finalMetadata,
		}, jsonErr)
		return fmt.Errorf("failed to marshal metadata: %w", jsonErr)
	}

	updateQuery := `UPDATE freedom_users SET metadata = $1, updated_at = NOW() WHERE unique_id = $2 AND mobile = $3 AND source_entity_id = $4`
	result, dbErr := database.Exec(updateQuery, string(metadataJSON), uniqueID, mobile, sourceEntityID)
	if dbErr != nil {
		errorHandler.ReportToSentryWithFields(map[string]interface{}{
			"uniqueID": uniqueID,
			"metadata": string(metadataJSON),
			"db_error": dbErr.Error(),
		}, dbErr)
		return fmt.Errorf("failed to update freedom_users metadata: %w", dbErr)
	}

	_, _ = result.RowsAffected()
	return nil
}

// ParseAndMergeMetadata extracts metadata from request attributes and URL parameters
// Returns the merged metadata map
func ParseAndMergeMetadata(r *http.Request, attributes map[string]interface{}) map[string]interface{} {
	var incomingMetadata map[string]interface{}

	// Parse metadata from request body attributes
	if md, ok := attributes["metadata"]; ok {
		if md != nil {
			if parsedMetadata, isMap := md.(map[string]interface{}); isMap {
				incomingMetadata = parsedMetadata
			}
		}
	}

	// Parse metadata from URL parameters and merge (URL takes precedence)
	if metadataParam := r.URL.Query().Get("metadata"); metadataParam != "" {
		var urlMetadata map[string]interface{}
		if err := json.Unmarshal([]byte(metadataParam), &urlMetadata); err == nil {
			if incomingMetadata == nil {
				incomingMetadata = make(map[string]interface{})
			}
			for k, v := range urlMetadata {
				incomingMetadata[k] = v
			}
		}
	}

	return incomingMetadata
}

// HandleMetadataForMFLBL processes metadata specifically for MFL BL sourcing
func HandleMetadataForMFLBL(r *http.Request, attributes map[string]interface{}, userObj authentication.FreedomUserStruct, uniqueID string, existingMetadata sql.NullString) {
	if !journey.IsMFLBLSourcing(userObj.SourceEntityID) {
		return
	}

	incomingMetadata := ParseAndMergeMetadata(r, attributes)

	SaveCreditLinkMetadata(
		r.Context(),
		uniqueID,
		userObj.Mobile,
		userObj.SourceEntityID,
		incomingMetadata,
		existingMetadata,
	)
}

func CustomCreditLineConfigBySource(sourceEntityID string, config *CreditLinkConfigStruct) {
	parentSourceEntityID := sourceEntityID
	journey.GetParentSourceEntityID("", &parentSourceEntityID)
	switch parentSourceEntityID {
	case constants.ABFLPLID:
		config.EligibleAmount = 7_00_000
	default:
		return
	}
}
