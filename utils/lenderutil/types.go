package lenderutil

import "finbox/go-api/constants"

type LenderListResp struct {
	LenderID   string `json:"lenderID"`
	LenderName string `json:"lenderName"`
}

// LenderPasswordPolicy for lender password policy. ? SpecialCharacter is not allowed.
type LenderPasswordPolicy struct {
	SpecialCharacter         bool   `json:"specialCharacter"`
	AllowedSpecialCharacters string `json:"allowedSpecialCharacters"`
	Number                   bool   `json:"number"`
	UpperCaseLetter          bool   `json:"upperCaseLetter"`
	LowerCaseLetter          bool   `json:"lowerCaseLetter"`
	MinPasswordLength        int    `json:"minPasswordLength"`
	MaxPasswordLength        int    `json:"maxPasswordLength"`
}

// LenderUtilityMap is a map which stores lender dashboard utilities enabled for a lender
var LenderUtilityMap = map[string][]string{
	constants.MuthootCLID: {"Validate Bank Account", "Validate GSTIN"},
	constants.ABFLID:      {"Validate Bank Account", "Validate GSTIN"},
	constants.MFLBLID:     {"Validate Bank Account", "Validate GSTIN"},
}

// LenderDummyUserMap is a map which stores dummy external service users for a lender linked source entity id
var LenderDummyUserMap = map[string]string{
	constants.MuthootCLID: constants.DummyMuthootExternalServiceLogger,
	constants.ABFLID:      constants.DummyABFLExternalServiceLogger,
	constants.MFLBLID:     constants.DummyMFLExternalServiceLogger,
}
