package offergetter

import (
	"database/sql"
	"encoding/json"
	"finbox/go-api/constants"
	"finbox/go-api/errorHandler"
	"finbox/go-api/functions/journey"
	"finbox/go-api/functions/logger"
	"finbox/go-api/models/lender"
	"finbox/go-api/models/loanapplication"
	"finbox/go-api/models/personalloanoffer"
	"finbox/go-api/models/users"
	"finbox/go-api/utils/general"
	"fmt"
)

// GetPersonalLoanOffers returns all current and accepted loan offers for the user
func GetPersonalLoanOffers(userID string, sourceEntityID, getterType string, offerStatus *int, loanApplicationID string) ([]personalloanoffer.PersonalLoanOffer, int, error) {
	var loanoffer []personalloanoffer.PersonalLoanOffer
	// check if loan application exists
	var loanObj struct {
		LoanApplicationID string `db:"loan_application_id"`
		Status            int
		Interest          float64 `db:"interest"`
		LenderID          string  `db:"lender_id"`
	}
	var query string
	var err error
	if loanApplicationID == "" {
		query = "select loan_application_id, coalesce(status, -1) as status, coalesce(interest, 0.0) as interest, lender_id from loan_application where user_id = $1 and source_entity_id = $2 and is_valid = true order by created_at desc limit 1"
		err = database.Get(&loanObj, query, userID, sourceEntityID)
		if err != nil && err != sql.ErrNoRows {
			log.Error(err)
		}
	} else {
		query = `SELECT loan_application_id, status, coalesce(interest, 0.0) as interest, lender_id FROM loan_application WHERE loan_application_id = $1`
		err = database.Get(&loanObj, query, loanApplicationID)
		if err != nil {
			log.Error(err)
			return []personalloanoffer.PersonalLoanOffer{}, 0, err
		}
	}

	if loanObj.Status == constants.LoanStatusDisbursed && loanObj.LenderID == constants.TataCapitalID && sourceEntityID == constants.TataPLID {
		lenderInfo, err := lender.Get(loanObj.LenderID)
		if err != nil && err != sql.ErrNoRows {
			log.Error(err)
		}
		loanoffer = []personalloanoffer.PersonalLoanOffer{{
			LenderLogoURL: lenderInfo.LenderLogoURL,
			LenderName:    lenderInfo.LenderName,
			Interest:      loanObj.Interest,
			Status:        constants.OfferStatusMapIntToStr[constants.OfferStatusIsAccepted],
		}}
		return loanoffer, len(loanoffer), err
	}

	var args []interface{}
	if loanApplicationID != "" {
		query = `SELECT a.loan_offer_id, a.amount, coalesce(p.max_amount, 0) as max_amount, a.interest, p.lender_id, coalesce(p.offer_metadata::jsonb->'stepperData', '{}'::jsonb) as stepper_data,
				a.tenure, coalesce(p.max_tenure, 0) as max_tenure, a.created_at, p.status as offer_status, coalesce(p.min_tenure, 0) as min_tenure, coalesce(p.min_amount, 0) as min_amount,
				coalesce(p.processing_fee, 0) as processing_fee, coalesce(a.processing_fee, 0) as loan_processing_fee,
				coalesce(p.processing_fee_type, '') AS processing_fee_type, 
				coalesce (t.tags, '{}') as tags,
				COALESCE(t.gst, 0) AS gst, t.method, coalesce(p.offer_metadata, '{}') as offer_metadata,
				p.offer_type,
				lender_name, lender_logo_url, coalesce(p.ranking_score, 0) as ranking_score
					FROM 
				personal_loan_offer p
				JOIN loan_application a
					ON a.loan_offer_id = p.loan_offer_id
				JOIN loan_offer_template t 
					ON p.source_entity_id = t.source_entity_id 
						AND p.lender_id = t.lender_id 
				JOIN lender ON p.lender_id = lender.lender_id 
					WHERE
				a.loan_application_id = $1`
		args = append(args, loanApplicationID)
	} else if !general.InArr(loanObj.Status, []int{constants.LoanStatusCancelled, constants.LoanStatusLoanRejected, constants.LoanStatusClosed}) && getterType != "all" {
		query = `SELECT a.loan_offer_id, a.amount, coalesce(p.max_amount, 0) as max_amount, a.interest, p.lender_id, coalesce(p.offer_metadata::jsonb->'stepperData', '{}'::jsonb) as stepper_data,
				a.tenure, coalesce(p.max_tenure, 0) as max_tenure, a.created_at, p.status as offer_status, coalesce(p.min_tenure, 0) as min_tenure, coalesce(p.max_emi, 0) as max_emi, coalesce(p.min_amount, 0) as min_amount,
				coalesce(p.processing_fee, 0) as processing_fee, coalesce(a.processing_fee, 0) as loan_processing_fee,
				coalesce(p.processing_fee_type, '') AS processing_fee_type, 
				coalesce (t.tags, '{}') as tags,
				COALESCE(t.gst, 0) AS gst, t.method, coalesce(p.offer_metadata, '{}') as offer_metadata,
				p.offer_type,
				lender_name, lender_logo_url, coalesce(p.ranking_score, 0) as ranking_score
					FROM 
				personal_loan_offer p
				JOIN loan_application a
					ON a.loan_offer_id = p.loan_offer_id
				JOIN loan_offer_template t 
					ON p.source_entity_id = t.source_entity_id 
						AND p.lender_id = t.lender_id 
				JOIN lender ON p.lender_id = lender.lender_id 
					WHERE
				p.user_id = $1 AND p.source_entity_id = $2 AND p.status != $3`
		args = append(args, userID, sourceEntityID, constants.OfferStatusExpired)
	} else {
		query = `SELECT distinct on(p.lender_id) coalesce(p.offer_metadata::jsonb->'stepperData', '{}'::jsonb) as stepper_data,
			p.lender_id, p.loan_offer_id, p.max_amount as max_amount, coalesce(p.min_amount, 0) as min_amount, p.interest,
			p.max_tenure as max_tenure, coalesce(p.min_tenure, 0) as min_tenure, coalesce(p.max_emi, 0) as max_emi, p.created_at, p.status as offer_status,
			coalesce(p.processing_fee, 0) as processing_fee, coalesce(p.processing_fee_type, '') AS processing_fee_type, 
			COALESCE(t.gst, 0) AS gst, t.method, coalesce(p.offer_metadata, '{}') as offer_metadata,
			p.offer_type,
			coalesce (t.tags, '{}') as tags,
			lender_name, lender_logo_url, coalesce(p.ranking_score, 0) as ranking_score 
				FROM 
			personal_loan_offer p 
			JOIN loan_offer_template t 
				ON p.source_entity_id = t.source_entity_id 
					AND p.lender_id = t.lender_id 
			JOIN lender ON p.lender_id = lender.lender_id 
				WHERE
			p.user_id = $1 AND p.source_entity_id = $2 AND p.status != $3`
		args = append(args, userID, sourceEntityID, constants.OfferStatusExpired)
	}

	if getterType == "all" && loanObj.LenderID != "" {
		// TODO - change value to latest
		query += fmt.Sprintf(" AND p.lender_id = '%s'", loanObj.LenderID)
	}

	if offerStatus != nil {
		query += fmt.Sprintf(" AND p.status = %d", *offerStatus)
	}

	query += " ORDER BY p.lender_id DESC, p.created_at DESC, t.created_at desc;"
	err = database.Select(&loanoffer, query, args...)

	var course string
	if journey.IsPFLEDUSourcing(sourceEntityID) {
		course, err = users.GetDynamicUserInfoField(userID, "course_duration")
		if err != nil {
			logger.WithUser(userID).Error(err)
		}
	}
	for idx := range loanoffer {
		if loanApplicationID != "" && loanObj.Status == constants.LoanStatusDisbursed && loanoffer[idx].OfferStatus == constants.OfferStatusExpired {
			loanoffer[idx].OfferStatus = constants.OfferStatusIsAccepted
		}
		loanoffer[idx].OfferMetadataObj, err = GetOfferMetadata(loanoffer[idx].OfferMetadata)
		if err != nil {
			logger.WithUser(userID).Error(err)
		}
		loanoffer[idx].OfferMetadataObj.CourseDuration = course
		loanoffer[idx].TagsObj, err = SetTagsToOfferMetadata(loanoffer[idx].Tags, loanoffer[idx].OfferType, loanoffer[idx].OfferMetadataObj.ApprovalType)
		if err != nil {
			err = fmt.Errorf("error converting tags: %s to map, userID: %s, err: %s", loanoffer[idx].Tags, userID, err.Error())
			errorHandler.ReportToSentryWithoutRequest(err)
			logger.WithUser(userID).Errorln(err)
		}
		loanoffer[idx].LenderLoanCounts, err = loanapplication.GetNumLoansInPastDays(loanoffer[idx].LenderID, 7, nil)
		if err != nil {
			err = fmt.Errorf("error getting loan_counts: userID: %s, err: %s", userID, err.Error())
			errorHandler.ReportToSentryWithoutRequest(err)
			logger.WithUser(userID).Errorln(err)
		}
		loanoffer[idx].Status = constants.OfferStatusMapIntToStr[loanoffer[idx].OfferStatus]
		if loanoffer[idx].LenderID == constants.IIFLID {
			loanoffer[idx].IsAdvanceEMI = true
			loanoffer[idx].EmiDate = "3rd of every month"
		}
		if loanoffer[idx].LenderID == constants.DMIID {
			loanoffer[idx].EmiDate = "5th of every month"
		}
		switch loanoffer[idx].OfferStatus {
		case constants.OfferStatusIsAccepted:
			loanoffer[idx].MaxTenure = loanoffer[idx].Tenure
			loanoffer[idx].MinTenure = loanoffer[idx].Tenure
			loanoffer[idx].MaxAmount = loanoffer[idx].Amount
			loanoffer[idx].MinAmount = loanoffer[idx].Amount
		case constants.OfferStatusActive:
			loanoffer[idx].Tenure = loanoffer[idx].MaxTenure
			loanoffer[idx].Amount = loanoffer[idx].MaxAmount
		case constants.OfferStatusSelected:
			if loanoffer[idx].LenderID == constants.DMIID || loanoffer[idx].LenderID == constants.TataCapitalID {
				loanoffer[idx].OfferArray = loanoffer[idx].OfferMetadataObj.Offers
			}
			if loanoffer[idx].SourceEntityID == constants.MoneyControlID && loanoffer[idx].LenderID == constants.ABFLPLID {
				loanoffer[idx].Tenure = loanoffer[idx].MaxTenure
				loanoffer[idx].Amount = loanoffer[idx].MaxAmount
			}
		}
		if loanoffer[idx].LenderID == constants.TataCapitalID || loanoffer[idx].LenderID == constants.AxisBankID {
			loanoffer[idx].CallOfferVariables = true
		}
		if loanoffer[idx].LenderID == constants.HDFCLenderID {
			loanoffer[idx].TagsObj = make([]personalloanoffer.Tags, 1)
			loanoffer[idx].TagsObj[0] = personalloanoffer.Tags{
				Title: "Attractive interest rates",
				Image: "https://finbox-cdn.s3.ap-south-1.amazonaws.com/assets/tdl-assets/startingFrom.svg",
				Color: "#FFDEA9",
			}
		}
		// TODO - remove this logic and use common logic for proc fee
		if loanoffer[idx].LenderID == constants.TDLKreditBeeID && loanObj.LoanApplicationID != "" {
			loanoffer[idx].ProcessingFee = loanoffer[idx].LoanProcessingFee
			loanoffer[idx].ProcessingFeeType = constants.ProcessingFeeTypeFlat
			isRepeatLoan, err := users.GetDynamicUserInfoField(userID, "isRepeatLoan")
			if err != nil {
				logger.WithUser(userID).Error(err)
			} else if isRepeatLoan == "Y" {
				loanoffer[idx].IsRepeatLoan = true
			}
		}
		loanoffer[idx].IsPreApproved = loanoffer[idx].OfferMetadataObj.IsPreApproved
	}

	return loanoffer, len(loanoffer), err
}

func GetOfferMetadata(offerMetadata string) (personalloanoffer.OfferMetadata, error) {
	var offerMetada personalloanoffer.OfferMetadata
	if json.Valid([]byte(offerMetadata)) {
		err := json.Unmarshal([]byte(offerMetadata), &offerMetada)
		if err != nil {
			log.Errorln(err)
			return offerMetada, err
		}
	}
	return offerMetada, nil
}

func SetTagsToOfferMetadata(tags string, offerType, approvalType string) (tagsArr []personalloanoffer.Tags, err error) {
	tagsMap := make(map[string][]personalloanoffer.Tags, 0)
	err = json.Unmarshal([]byte(tags), &tagsMap)
	if err != nil {
		log.Errorln(err)
		return tagsArr, err
	}
	if approvalType == constants.ApprovalTypePreQualified && len(tagsMap[approvalType]) > 0 {
		tagsArr = append(tagsArr, tagsMap[approvalType]...)
	} else {
		tagsArr = append(tagsArr, tagsMap[offerType]...)
		tagsArr = append(tagsArr, tagsMap[approvalType]...)
		tagsArr = append(tagsArr, tagsMap["default"]...)
	}
	return tagsArr, nil
}

func ProcessPersonalLoanOffers(userID, offerID string, offerStatus *int) ([]personalloanoffer.PersonalLoanOffer, int, error) {

	if offerID != "" {
		return getPLOfferByOfferID(userID, offerID)

	} else if offerStatus != nil {
		return getPLOfferByOfferStatus(userID, offerStatus)

	}
	return nil, 0, nil
}

func getPLOfferByOfferID(userID, offerID string) ([]personalloanoffer.PersonalLoanOffer, int, error) {

	var loanoffer []personalloanoffer.PersonalLoanOffer

	query := `SELECT distinct on(p.lender_id) coalesce(p.offer_metadata::jsonb->'stepperData', '{}'::jsonb) as stepper_data,
		p.lender_id, p.loan_offer_id, p.max_amount as max_amount, coalesce(p.min_amount, 0) as min_amount, p.interest,
		p.max_tenure as max_tenure, coalesce(p.min_tenure, 0) as min_tenure,coalesce(p.max_emi, 0) as max_emi, p.created_at, p.status as offer_status,
		coalesce(p.processing_fee, 0) as processing_fee, coalesce(p.processing_fee_type, '') AS processing_fee_type, 
		COALESCE(t.gst, 0) AS gst, t.method, coalesce(p.offer_metadata, '{}') as offer_metadata,
		p.offer_type,
		coalesce (t.tags, '{}') as tags,
		lender_name, lender_logo_url, coalesce(p.ranking_score, 0) as ranking_score 
			FROM 
		personal_loan_offer p 
		JOIN loan_offer_template t 
			ON p.source_entity_id = t.source_entity_id 
				AND p.lender_id = t.lender_id 
		JOIN lender ON p.lender_id = lender.lender_id 
			WHERE
		p.user_id = $1 AND p.loan_offer_id = $2;`

	err := database.Select(&loanoffer, query, userID, offerID)

	for idx := range loanoffer {

		logger.WithUser(userID).Error(fmt.Sprintf("lender loan %v", loanoffer))
		loanoffer[idx].OfferMetadataObj, err = GetOfferMetadata(loanoffer[idx].OfferMetadata)
		if err != nil {
			logger.WithUser(userID).Error(err)
		}

		loanoffer[idx].Status = constants.OfferStatusMapIntToStr[loanoffer[idx].OfferStatus]
		loanoffer[idx].Tenure = loanoffer[idx].MaxTenure
		loanoffer[idx].Amount = loanoffer[idx].MaxAmount
	}

	return loanoffer, len(loanoffer), err
}

func getPLOfferByOfferStatus(userID string, offerStatus *int) ([]personalloanoffer.PersonalLoanOffer, int, error) {
	var loanoffer []personalloanoffer.PersonalLoanOffer

	query := `SELECT distinct on(p.lender_id) coalesce(p.offer_metadata::jsonb->'stepperData', '{}'::jsonb) as stepper_data,
		p.lender_id, p.loan_offer_id, p.max_amount as max_amount, coalesce(p.min_amount, 0) as min_amount, p.interest,
		p.max_tenure as max_tenure, coalesce(p.min_tenure, 0) as min_tenure,coalesce(p.max_emi, 0) as max_emi, p.created_at, p.status as offer_status,
		coalesce(p.processing_fee, 0) as processing_fee, coalesce(p.processing_fee_type, '') AS processing_fee_type, 
		COALESCE(t.gst, 0) AS gst, t.method, coalesce(p.offer_metadata, '{}') as offer_metadata,
		p.offer_type,
		coalesce (t.tags, '{}') as tags,
		lender_name, lender_logo_url, coalesce(p.ranking_score, 0) as ranking_score 
			FROM 
		personal_loan_offer p 
		JOIN loan_offer_template t 
			ON p.source_entity_id = t.source_entity_id 
				AND p.lender_id = t.lender_id 
		JOIN lender ON p.lender_id = lender.lender_id 
			WHERE
		p.user_id = $1 AND p.status = $2;`

	err := database.Select(&loanoffer, query, userID, offerStatus)

	for idx := range loanoffer {

		logger.WithUser(userID).Error(fmt.Sprintf("lender loan %v", loanoffer))
		loanoffer[idx].OfferMetadataObj, err = GetOfferMetadata(loanoffer[idx].OfferMetadata)
		if err != nil {
			logger.WithUser(userID).Error(err)
		}

		loanoffer[idx].Status = constants.OfferStatusMapIntToStr[loanoffer[idx].OfferStatus]
		loanoffer[idx].Tenure = loanoffer[idx].MaxTenure
		loanoffer[idx].Amount = loanoffer[idx].MaxAmount
	}

	return loanoffer, len(loanoffer), err
}
