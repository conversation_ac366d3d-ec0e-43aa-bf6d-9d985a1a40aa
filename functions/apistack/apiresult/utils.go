package apiresult

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"finbox/go-api/constants"
	"finbox/go-api/errorHandler"
	"finbox/go-api/functions/logger"
	"finbox/go-api/functions/structs"
	"finbox/go-api/infra/s3"
	"finbox/go-api/internal/service/pickle"
	"finbox/go-api/models/insurance"
	"finbox/go-api/models/kycresults"
	"finbox/go-api/models/lendervariables"
	"finbox/go-api/models/loanapplication"
	"finbox/go-api/models/media"
	prequalificationlogs "finbox/go-api/models/prequallogs"
	"finbox/go-api/utils/general"
	"finbox/go-api/utils/mapdecoder"
	"fmt"

	pickleErrors "github.com/finbox-in/pickle-protos/gen/go/errors"

	"time"

	configv1 "github.com/finbox-in/pickle-protos/gen/go/config/v1"
)

// PrepareKYCCategoricalDetails adds kyc details in the KYCDetails API response
func PrepareKYCCategoricalDetails(kycDetails []kycresults.KYCResult, resData map[string]any) {
	// convert details to map
	kycDetailMap := map[string]any{}
	for i := range kycDetails {
		kycDetailMap[kycDetails[i].DetailType] = kycDetails[i].Result
	}
	for _, detailCategory := range constants.APIStackKYCDetailCategories {
		detailsArray := []map[string]any{}
		// find and add the details from the result set
		for _, kycDetailsType := range constants.APIStackKYCCategoryDetailTypes[detailCategory] {
			v, ok := kycDetailMap[kycDetailsType]
			if ok {
				detailsArray = append(detailsArray, v.(structs.ScannableMap))
			}
		}
		// only added the kyc detail category if kyc detail fround from that category
		if len(detailsArray) > 0 {
			resData[detailCategory] = detailsArray
		}
	}
	// add KYC results
	resData[constants.APIStackKYCDetailCategoryResult] = kycDetailMap[constants.APIStackKYCDetailTypeKYC]
}

// getDisbursalDetails returns the calculated disbursal amount, formatted date string for a loan application
func getDisbursalDetails(userID string, loanApp loanapplication.LoanApplication) (disbursalResult *getDisbursalResult, err error) {
	disbursedDatePreFormat, err := time.Parse(constants.DisbursedDateDBFormat, loanApp.DisbursedDateTime)
	if err != nil {
		logger.WithUser(userID).Error(err)
		return nil, err
	}
	disbursedAt := disbursedDatePreFormat.Format(constants.APIStackDateTimeFormat)

	disbursalResult = &getDisbursalResult{
		DisbursedAmount: loanApp.DisbursedAmount,
		DisbursedAt:     disbursedAt,
	}

	return disbursalResult, err
}

type prequalResult struct {
	Lender        string          `json:"lender"`
	Status        string          `json:"status"`
	Message       string          `json:"message"`
	RuleDecisions map[string]bool `json:"ruleDecisions"`
}

func PreparePrequalResponse(ctx context.Context, userStatus int, referenceID string, operationFailed bool) ([]prequalResult, error) {
	pqRes := []prequalResult{}

	prequalLogs, err := prequalificationlogs.GetByReferenceID(ctx, referenceID)
	if err != nil {
		return nil, err
	}

	lenderRes := map[string]map[string]bool{}
	failureReason := ""
	for _, l := range prequalLogs {
		lenderMap, exists := lenderRes[l.LenderName]
		if !exists {
			lenderMap = map[string]bool{}
		}
		rulePass := *l.Status == prequalificationlogs.StatusPass
		lenderMap[l.RuleName] = rulePass

		if !rulePass {
			failureReason = l.Message
		}
		lenderRes[l.LenderName] = lenderMap
	}
	prequalStatus := "APPROVED"

	if operationFailed {
		prequalStatus = "FAILED"
		for lenderName := range lenderRes {
			pqRes = append(pqRes, prequalResult{
				Lender:  lenderName,
				Status:  prequalStatus,
				Message: failureReason,
			})
		}

		return pqRes, nil
	}

	if userStatus == constants.UserStatusDisqualified {
		prequalStatus = "REJECTED"
	}

	for lenderName, ruleDecisionMap := range lenderRes {
		pqRes = append(pqRes, prequalResult{
			Lender:        lenderName,
			Status:        prequalStatus,
			Message:       failureReason,
			RuleDecisions: ruleDecisionMap,
		})
	}

	return pqRes, nil
}

func getInsuranceResult(userID, loanAppID string) (res *insuranceResult, insuranceExists bool, err error) {
	insuranceObj, err := insurance.GetLatestByLoanApplicationID(loanAppID)
	if err != nil {
		switch err {
		case sql.ErrNoRows:
			return nil, false, nil
		default:
			logger.WithUser(userID).Error(err)
			errorHandler.ReportToSentryWithoutRequest(fmt.Errorf("error getting insurance for user: %s", userID))
			return nil, false, err
		}
	}

	if insuranceObj.Status == constants.InsuranceStatusInquired {
		// Insurance is just opted in, not created at the vendor end yet.
		return nil, false, nil
	}

	if insuranceObj.Status == constants.InsuranceStatusFailed {
		// Insurance was opted in, but failed to create at the vendor end. This will be treated as a non-insurance case.
		return nil, false, nil
	}

	var insuranceMetadata insurance.InsuranceMetadata
	if err = mapdecoder.JSONDecoder(insuranceObj.Metadata, &insuranceMetadata); err != nil {
		logger.WithUser(userID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return nil, true, err
	}

	var mediaKey string
	mediaKey, err = media.GetObjectKeyWithUserID(constants.MediaTypeInsuranceCertificate, userID)
	if err != nil {
		logger.WithUser(userID).Error(err)
		if errors.Is(err, sql.ErrNoRows) {
			// Insurance exists but no media found, the insurance module is not complete and will be stuck in processing.
			return nil, true, nil
		}
		errorHandler.ReportToSentryWithoutRequest(err) //TODO: better message
		return
	}

	docURL := s3.GetPresignedURLS3(mediaKey, 60)
	if docURL == "" {
		err = fmt.Errorf("error getting presigned url for user: %s", userID)
		logger.WithUser(userID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return nil, true, err
	}

	res = &insuranceResult{
		PolicyID:     insuranceMetadata.PolicyID,
		PolicyNumber: insuranceMetadata.PolicyNumber,
		Documents: []insuranceDocument{
			{
				DocumentName: constants.MediaTypeInsuranceCertificate,
				DocumentURL:  docURL,
			},
		},
	}

	return res, true, nil
}

// PrequalResponseConfig defines additional fields to include in prequalification responses
type PrequalResponseConfig struct {
	IncludeRedirectionURL bool   `json:"includeRedirectionURL"`
	LenderID              string `json:"lenderID"`
	RedirectionURLField   string `json:"redirectionURLField"`
}

// getPrequalResponseConfig returns configuration for additional response fields based on source entity
func getPrequalResponseConfig(ctx context.Context, userID, sourceEntityID string) (*PrequalResponseConfig, error) {

	var responseType PrequalResponseConfig

	client, err := pickle.GetJourneyClient(ctx)
	if err != nil {
		logger.WithUser(userID).Errorln(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{
			"source_entity_id": sourceEntityID,
		}, err)
		return nil, err
	}

	response, err := client.GetConfigClient().GetConfig(ctx, &configv1.GetConfigRequest{
		ResourceName: pickle.ResourcePrequalResponseConfigs,
		KeyValueMap: map[string]string{
			pickle.KeySourceEntityID: sourceEntityID,
		},
	})

	if err != nil {
		logger.WithUser(userID).Errorln(err)

		// If the config is not found, it means that the source entity is not supported for prequalification response extras
		if pickleErrors.IsConfigNotFound(err) {
			logger.WithUser(userID).Debugln("config not found for source entity: ", sourceEntityID)
			return nil, nil
		}
		errorHandler.ReportToSentryWithFields(map[string]interface{}{
			"source_entity_id": sourceEntityID,
		}, err)
		return nil, err
	}

	if err := json.Unmarshal([]byte(response.ConfigData), &responseType); err != nil {
		logger.WithUser(userID).Errorln(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{
			"source_entity_id": sourceEntityID,
		}, err)
		return nil, err
	}

	return &responseType, nil
}

// addPrequalificationResponseExtras adds partner-specific fields to the response
func addPrequalificationResponseExtras(ctx context.Context, resData map[string]any, userID, sourceEntityID string) error {
	config, err := getPrequalResponseConfig(ctx, userID, sourceEntityID)
	if err != nil {
		logger.WithUser(userID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return err
	}
	if config == nil {
		return nil // No extra configuration for this source entity
	}

	if config.IncludeRedirectionURL {
		lenderVariables, err := lendervariables.GetDynamicVariables(userID, config.LenderID)
		if err != nil {
			logger.WithUser(userID).Errorln(err)
			return err
		}

		var dynamicVariables struct {
			RedirectionURL string `json:"redirectionURL"`
		}
		err = json.Unmarshal([]byte(general.AnyToJSONString(lenderVariables)), &dynamicVariables)
		if err != nil {
			logger.WithUser(userID).Errorln(err)
			return err
		}
		resData[config.RedirectionURLField] = dynamicVariables.RedirectionURL
	}

	return nil
}
