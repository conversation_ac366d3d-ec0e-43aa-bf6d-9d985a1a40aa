package apiresult

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"finbox/go-api/constants"
	"finbox/go-api/errorHandler"
	"finbox/go-api/functions/commonutils"
	"finbox/go-api/functions/journey"
	"finbox/go-api/functions/kycservice"
	"finbox/go-api/functions/logger"
	"finbox/go-api/functions/retry"
	"finbox/go-api/functions/structs"
	"finbox/go-api/functions/underwriting"
	"finbox/go-api/infra/s3"
	"finbox/go-api/models/businessloanoffer"
	"finbox/go-api/models/decisionengine"
	"finbox/go-api/models/decisionrules"
	"finbox/go-api/models/expiry"
	"finbox/go-api/models/jobs"
	"finbox/go-api/models/kycresults"
	"finbox/go-api/models/lender"
	"finbox/go-api/models/loanapplication"
	"finbox/go-api/models/media"
	"finbox/go-api/models/personalloanoffer"
	"finbox/go-api/models/rejection"
	"finbox/go-api/models/userbusinessuan"
	"finbox/go-api/models/userkycapplications"
	"finbox/go-api/models/users"
	"finbox/go-api/models/userworkflows"
	"finbox/go-api/models/vendorpayments"
	"finbox/go-api/temporal/temporalutility"
	"finbox/go-api/utils/calc"
	"finbox/go-api/utils/general"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/getsentry/sentry-go"
)

// GetJobResult retrieves the result of a specific job based on the job name.
// It returns the API result and any error encountered.
//
// The jobName parameter specifies the name of the job.
// The referenceID parameter specifies the job ID.
// The sourceEntityID parameter specifies the source entity ID.
//
// GetJobResult supports the following job names:
//   - IdentityCheck
//   - OKYC
//   - BRE
//   - AgreementSignAsync
//   - Disbursal
//
// If the job name is not recognized, it returns an error with a message indicating the unknown job name.
func GetJobResult(ctx context.Context, referenceID, sourceEntityID, jobName string) (res APIResult, err error) {
	jobName = strings.ToUpper(jobName)
	switch {
	case jobName == jobs.IdentityCheck, jobName == jobs.OKYC:
		res, err = GetKYCResult(ctx, referenceID, sourceEntityID)
	case jobName == jobs.BRE, strings.HasPrefix(jobName, jobs.BRE):
		res, err = GetBREResult(ctx, referenceID, sourceEntityID)
	case jobName == jobs.AgreementSignAsync:
		res, err = GetAgreementSignResult(ctx, referenceID, sourceEntityID)
	case jobName == jobs.Disbursal:
		res, err = GetDisbursalResult(ctx, referenceID, sourceEntityID)
	case jobName == jobs.Prequalification:
		res, err = GetPrequalificationResults(ctx, referenceID, sourceEntityID)
	case jobName == jobs.CreateReceivable:
		res, err = GetCreateReceivableResult(ctx, referenceID, sourceEntityID)
	case jobName == jobs.CancelReceivable:
		res, err = GetCancelReceivableResult(ctx, referenceID, sourceEntityID)
	case jobName == jobs.CreatePayable:
		res, err = GetCreatePayableResult(ctx, referenceID, sourceEntityID)
	case jobName == jobs.CancelPayable:
		res, err = GetCancelPayableResult(ctx, referenceID, sourceEntityID)
	case jobName == jobs.Repayment:
		res, err = GetRepaymentResult(ctx, referenceID, sourceEntityID)
	case jobName == jobs.AgreementUnsigned:
		res, err = GetAgreementUnsignedResult(ctx, referenceID, sourceEntityID)
	case jobName == jobs.BankAnalysis:
		res, err = GetBankAnalysisResult(ctx, referenceID, sourceEntityID)
	case jobName == jobs.LisaNDCLetter:
		res, err = GetNDCLetterResult(ctx, referenceID, sourceEntityID)
	case jobName == jobs.PrincipalWorkflowForBulkActivity:
		res, err = GetPrincipleWorkflowActivityResult(ctx, referenceID, sourceEntityID)
	default:
		err = fmt.Errorf("unknown job name used in GetJobResult: %s", jobName)
	}
	return res, err
}

// GetKYCResult returns the API result for APIStack KycDetails API
func GetKYCResult(ctx context.Context, referenceID, sourceEntityID string) (res APIResult, err error) {
	// Check if the item with referenceID exists

	operation, err := getUserOperationInfo(referenceID, sourceEntityID, []string{jobs.OKYC, jobs.IdentityCheck}, QueryWithIDAndNamesInArr)
	if err != nil {
		logger.WithSourceEntity(sourceEntityID).Errorln(err)
		if err == sql.ErrNoRows {
			return res, &structs.CustomError{Err: errors.New(constants.ErrStringInvalidReferenceID), ErrCode: "", HTTPCode: http.StatusNotFound}
		}
		return res, err
	}
	userID := operation.UserID
	jobStatus := operation.Status

	// fill customerID
	customerID, err := users.GetUniqueIDFromUserID(userID)
	if err != nil {
		logger.WithUser(userID).Errorln(err, "err getting customerID for user")
		return res, err
	}

	resData := map[string]any{
		"referenceID": referenceID,
		"status":      "PROCESSING",
		"results":     nil,
		"action":      "WAIT",
		"customerID":  customerID,
	}

	statusCode := constants.APIGetKYCDetailsProcessing
	switch jobStatus {
	case jobs.StatusFailed:
		{
			prepareFailureResData := func(resData map[string]any) error {
				// If API stack KYC application is not rejected then it's still soft reject
				app, err := userkycapplications.GetLatestByUser(userID, sourceEntityID)
				if err != nil {
					logger.WithUser(userID).Error(err)
					return err
				}

				var status string
				var action string
				if app.Status == userkycapplications.StatusRejected {
					status = "REJECTED"
					action = ""
				} else if operation.Message == constants.ErrEnumFaceMismatch || operation.Message == constants.ErrEnumSelfieLivenessMismatch {
					status = "SOFT_REJECTED"
					action = "TRIGGER_IDENTITY_CHECKS"
				} else if operation.Message == constants.ErrEnumOKYCFailed {
					status = "FAILED"
					action = "TRIGGER_OKYC"
				} else {
					status = "FAILED"
				}
				resData["message"] = operation.Message
				resData["action"] = action
				resData["status"] = status
				// Fill the data
				kycDetails, err := kycresults.GetDetails(ctx, userID)
				if err != nil {
					logger.WithUser(userID).Error(err)
					return err
				}
				PrepareKYCCategoricalDetails(kycDetails, resData)
				return nil
			}
			err = prepareFailureResData(resData)
			if err != nil {
				resData["status"] = "FAILED"
				resData["action"] = ""
				resData["message"] = "THIRD_PARTY_API_FAILURE"
			}
			statusCode = constants.APIGetKYCDetailsFailed
		}
	case jobs.StatusCompleted:
		{
			resData["status"] = "SUCCESS"

			// Getting KYC application attempt from DB
			app, err := userkycapplications.GetLatestByUser(userID, sourceEntityID)
			if err != nil {
				logger.WithUser(userID).Errorln(err)
				return res, err
			}

			resData["outputVariables"] = map[string]any{"manualReviewRequired": false}
			// set status to manualReviewRequired if conditional success
			if app.Status == userkycapplications.ConditonalSuccess {
				resData["status"] = "CONDITIONAL_SUCCESS"
				resData["action"] = "TRIGGER_IDENTITY_CHECKS"
				resData["outputVariables"] = map[string]any{"manualReviewRequired": true}
			}

			// Determine Udyam status and update it in the outputVariables map
			udyamStatus := UdyamStatusN
			_, err = userbusinessuan.GetUANDataFromUserID(userID)
			if err == nil {
				udyamStatus = UdyamStatusY
			}

			if outputVars, ok := resData["outputVariables"].(map[string]any); ok {
				outputVars["udyamStatus"] = udyamStatus
			}

			// KYC service get modules to know which module is active at KYC Service
			modulesResp, err := kycservice.GetModules(app.UserID, app.ApplicationID)
			if err != nil {
				logger.WithUser(userID).Errorln(err)
				return res, err
			}

			action := ""
			// KYC Module, SubModule and State
			moduleName := modulesResp.CurrentModule.ModuleName
			subModuleName := modulesResp.CurrentModule.SubModuleName
			state := modulesResp.CurrentModule.State

			// Deducing Action based on the status in userkycapplications table and
			// current module active at KYC Service
			switch {
			case app.Status == userkycapplications.StatusFailed, app.Status == userkycapplications.StatusRejected:
				action = "TRIGGER_OKYC"
			case app.InTerminalState():
				action = ""
			case moduleName == "MANUAL_KYC" && state == "START" && subModuleName == "DETAILS":
				action = "TRIGGER_OKYC"
			case moduleName == "MANUAL_KYC" && subModuleName == "UPLOAD_DOCUMENT":
				action = "WAIT"
			case moduleName == "SELFIE" && (state == "START" || state == "FAILED"):
				action = "TRIGGER_IDENTITY_CHECKS"
			}

			// get the KYC details from DB
			kycDetails, err := kycresults.GetDetails(ctx, app.UserID)
			if err != nil {
				logger.WithUser(userID).Errorln(err)
				return res, err
			} else {
				PrepareKYCCategoricalDetails(kycDetails, resData)
			}
			// Return KYC service specific status for PhonePe
			// TODO: remove after consulting with PhonePe
			if journey.IsMuthootEDIPartner(sourceEntityID) || journey.IsMFLEMIPartner(sourceEntityID) {
				status := userkycapplications.GetReadableStatusMap(sourceEntityID)[app.Status]
				logger.DebugWithUser(app.UserID, "status: ", status)
				resData["status"] = status
			}
			resData["action"] = action

			switch {
			// return success statusCode for API if last identity check done
			// or if KYC was rejected
			case app.Status == userkycapplications.StatusRejected,
				operation.OperationName == jobs.IdentityCheck:
				{
					statusCode = constants.APIGetKYCDetailsSuccess
				}
			}
		}
	case jobs.StatusRunning:
		{
			// Do nothing
		}
	default:
		{
			errorHandler.ReportToSentryWithoutRequest(fmt.Errorf("job status unhandled in GetKYCDetails: %d, userID: %s", jobStatus, userID))
			logger.WithUser(userID).Info(general.AnyToJSONString(operation))
		}
	}

	return APIResult{Data: resData, StatusCode: statusCode, ReferenceID: referenceID, CustomerID: customerID}, nil
}

// GetBREResult returns the API result for APIStack OfferDetails API
func GetBREResult(ctx context.Context, referenceID, sourceEntityID string) (res APIResult, err error) {
	// Check if the item with referenceID exists
	operation, err := getUserOperationInfo(referenceID, sourceEntityID, []string{jobs.BRE}, QueryWithIDAndNameLike)
	if err != nil {
		logger.WithSourceEntity(sourceEntityID).Error(err)
		if err == sql.ErrNoRows {
			return res, &structs.CustomError{Err: errors.New(constants.ErrStringInvalidReferenceID), ErrCode: "", HTTPCode: http.StatusNotFound}
		}
		return res, err
	}
	userID := operation.UserID

	// fill customerID
	customerID, err := users.GetUniqueIDFromUserID(userID)
	if err != nil {
		logger.WithUser(userID).Errorln(err, "err getting customerID for user")
		return res, err
	}

	// Default response
	resData := map[string]interface{}{
		"referenceID":     referenceID,
		"offers":          nil,
		"rejectionReason": nil,
		"outputVariables": nil,
		"status":          "PROCESSING",
		"customerID":      customerID,
	}
	statusCode := constants.APIGetOffersStatusProcessing

	// Find the User and handle disqualified response
	user, err := users.Get(userID)
	if err != nil {
		logger.WithUser(userID).Error(err)
		return res, err
	}
	if *user.Status == constants.UserStatusDisqualified {
		// fetch rejection reason for user
		var rejectionReasonMessage string
		rejectionReasonEnum := "LENDER_BRE_REJECT"
		// Try to find rejection from sentinel first
		rejectionReasonMessage, err = decisionrules.GetUserFailedBRERulesString(ctx, userID, user.SourceEntityID, " , ")
		if err != nil || rejectionReasonMessage == "" {
			if err != sql.ErrNoRows && rejectionReasonMessage != "" {
				logger.WithUser(userID).Error(err, "error getting rejection from BRE Decision result")
				return res, err
			}
			logger.DebugWithUser(userID, "rejection not found in BRE Decision result, Trying rejection logs")
			// if decision not found in rejection logs (PRE BRE rejection source)
			// try finding in decision engine response
			rejection, err := rejection.GetLatestLog(context.Background(), userID)
			if err != nil {
				logger.WithUser(userID).Errorln(err)
				return res, err
			}
			// Set Rejection reason from rejection logs
			rejectionReasonMessage = rejection.Reason
			if rejectionReasonMessage == constants.NoOffer {
				rejectionReasonEnum = "LENDER_BRE_REJECT"
			} else {
				rejectionReasonEnum = "PRE_BRE_REJECT"
			}
		}
		// Set the rejections in response and return
		resData["rejectionReason"] = rejectionReasonEnum
		resData["outputVariables"] = map[string]any{"rejectionReason": rejectionReasonMessage}
		resData["status"] = "REJECTED"
		statusCode = constants.APIGetOffersStatusRejected
		return APIResult{Data: resData, StatusCode: statusCode, ReferenceID: referenceID, CustomerID: customerID}, nil
	}

	// prepare response based on Job Status
	switch operation.Status {
	case jobs.StatusFailed:
		{
			resData["status"] = "FAILED"
			statusCode = constants.APIGetOffersStatusFailed
		}
	case jobs.StatusCompleted:
		switch {
		case journey.IsIIFLBLSourcing(sourceEntityID):
			dataRequired := map[string]string{}

			offer, err := businessloanoffer.GetLatestForUser(user.ID)
			if err != nil {
				logger.WithUser(user.ID).Error(err.Error())
				panic(err)
			}

			decisionData, err := decisionengine.GetDecisionAndOutputVariablesV2(decisionengine.DecisionAndOutputVariablesInput{UserID: user.ID, RuleType: decisionengine.RuleTypeWorkflow})
			if err != nil {
				logger.WithUser(user.ID).Error(err.Error())
				panic(err)
			}

			switch decisionData.Decision {
			case constants.UnderwritingDecisionBoost:
				dataRequired["banking"] = "optional"
			case constants.UnderwritingDecisionBankConnect:
				dataRequired["banking"] = "mandatory"
			default:
				if offer.OfferType == constants.OfferTypeTentative {
					// set mandatory by default as BC is required to run after bureau
					dataRequired["banking"] = "mandatory"
				}
			}

			lenderID, _ := underwriting.GetLenderID(sourceEntityID, user.ID, "")
			lenderObj, err := lender.Get(lenderID)
			if err != nil {
				logger.WithUser(user.ID).Errorln(err)
				panic(err)
			}
			var offerMeta businessloanoffer.OfferMetadata
			err = json.Unmarshal([]byte(offer.OfferMetadata), &offerMeta)
			if err != nil {
				logger.WithUser(user.ID).Error(err)
				panic(err)
			}

			// Calulate maximum allowed emi to show in offer
			tempMaxEMI, _, _ := calc.GetEMI(offer.Method, offer.EligibleAmount, offer.Tenure, offer.Interest, time.Now(), sourceEntityID, lenderID, userID)
			if tempMaxEMI > 0 && tempMaxEMI > offer.MaxEMI {
				offer.MaxEMI = tempMaxEMI
			}

			if len(offerMeta.Offers) > 0 {
				offerMeta.Offers[0].MaxEMI = offer.MaxEMI
			}

			resData["status"] = "SUCCESS"
			resData["dataRequired"] = dataRequired
			resData["offers"] = []map[string]interface{}{
				{
					"offerID":       offer.LoanOfferID,
					"createdAt":     offer.CreatedAt,
					"expiresAt":     "",
					"lenderID":      lenderID,
					"lenderName":    lenderObj.LenderName,
					"lenderLogoURL": lenderObj.LenderLogoURL,
					"status":        "ACTIVE",
					"offerType":     offer.OfferType,
					"slabs":         offerMeta.Offers,
				},
			}
		case journey.IsABFLBLSourcing(sourceEntityID):
			dataRequired := map[string]string{}

			skipBooster, bankBooster, gstBooster := underwriting.APIStackGetBoosterOptions(user.ID, sourceEntityID)
			if bankBooster && !skipBooster {
				dataRequired["banking"] = "mandatory"
			} else if bankBooster {
				dataRequired["banking"] = "optional"
			}

			if gstBooster && !skipBooster {
				dataRequired["gst"] = "mandatory"
			} else if gstBooster {
				dataRequired["gst"] = "optional"
			}

			// offers, err := offergetter.GetOffers(user.ID, sourceEntityID, "")
			// if err != nil {
			// 	logger.WithUser(user.ID).Error(err.Error())
			// 	panic(err)
			// }

			offer, err := businessloanoffer.GetLatestForUser(user.ID)
			if err != nil {
				logger.WithUser(user.ID).Error(err.Error())
				panic(err)
			}

			// Select offer expiry
			expiryAt, err := expiry.GetUserOfferExpiry(user.ID)
			if err != nil {
				logger.WithUser(user.ID).Error(err)
				panic(err)
			}

			offerExpiryTime, err := time.Parse("02-Jan-2006", expiryAt)
			if err != nil {
				logger.WithUser(user.ID).Error(err)
				panic(err)
			}

			lenderID, _ := underwriting.GetLenderID(sourceEntityID, user.ID, "")
			lenderObj, err := lender.Get(lenderID)
			if err != nil {
				logger.WithUser(user.ID).Errorln(err)
				panic(err)
			}
			var offerMeta businessloanoffer.OfferMetadata
			err = json.Unmarshal([]byte(offer.OfferMetadata), &offerMeta)
			if err != nil {
				logger.WithUser(user.ID).Error(err)
				panic(err)
			}

			resData["status"] = "SUCCESS"
			resData["dataRequired"] = dataRequired
			resData["offers"] = []map[string]interface{}{
				{
					"offerID":       offer.LoanOfferID,
					"createdAt":     offer.CreatedAt,
					"expiresAt":     offerExpiryTime.Format(constants.APIStackDateTimeFormat),
					"lenderID":      lenderID,
					"lenderName":    lenderObj.LenderName,
					"lenderLogoURL": lenderObj.LenderLogoURL,
					"status":        "ACTIVE",
					"offerType":     offer.OfferType,
					"slabs":         offerMeta.Offers,
				},
			}
		case journey.IsABFLPLSourcing(sourceEntityID):
			// implement some interface with well-defined structure for all types
			offer, err := personalloanoffer.GetLatestForUser(user.ID)
			if err != nil {
				logger.WithUser(user.ID).Error(err)
				return res, err
			}

			lenderID, _ := underwriting.GetLenderID(sourceEntityID, user.ID, "")
			lenderObj, err := lender.Get(lenderID)
			if err != nil {
				logger.WithUser(user.ID).Errorln(err)
				return res, err
			}
			var offerMeta personalloanoffer.OfferMetadata
			err = json.Unmarshal([]byte(offer.OfferMetadata), &offerMeta)
			if err != nil {
				logger.WithUser(user.ID).Error(err)
				return res, err
			}

			// fetch and prepare output variables to be sent in response
			// get raw output variables and parse them
			outputVarsRes, err := decisionengine.GetDecisionAndOutputVariables(user.ID, lenderID)
			if err != nil {
				logger.WithUser(user.ID).Error(err)
				return res, err
			}
			outputVarsMap := map[string]any{}
			err = json.Unmarshal([]byte(outputVarsRes.OutputVariables), &outputVarsMap)
			if err != nil {
				logger.WithUser(user.ID).Error(err)
				return res, err
			}
			// pick the exposed output variables in set in final output variables map
			exposedOutputVars := []string{"thick_thin_multiplier"}
			exposedOutputVariables := map[string]any{}
			for _, v := range exposedOutputVars {
				if _, ok := outputVarsMap[v]; ok {
					exposedOutputVariables[v] = outputVarsMap[v]
				}
			}
			slabs := []map[string]interface{}{}
			for _, offerStruct := range offerMeta.Offers {
				slab := map[string]interface{}{
					"maxAmount":         offerStruct.MaxAmount,
					"minAmount":         offerStruct.MinAmount,
					"tenure":            offerStruct.Tenure,
					"interest":          offerStruct.Interest,
					"method":            offerStruct.Method,
					"processingFee":     offerStruct.ProcessingFee,
					"processingFeeType": offerStruct.ProcessingFeeType,
					"gst":               offerStruct.GST,
				}
				slabs = append(slabs, slab)
			}
			resData["status"] = "SUCCESS"
			resData["outputVariables"] = exposedOutputVariables
			resData["offers"] = []map[string]interface{}{
				{
					"offerID":       offer.LoanOfferID,
					"createdAt":     offer.CreatedAt,
					"expiresAt":     "",
					"lenderID":      lenderID,
					"lenderName":    lenderObj.LenderName,
					"lenderLogoURL": lenderObj.LenderLogoURL,
					"status":        "ACTIVE",
					"offerType":     offer.OfferType,
					"slabs":         slabs,
				},
			}
		case journey.IsIncredSourcing(sourceEntityID):
			// Fetch the latest personal loan offer for the user
			offer, err := personalloanoffer.GetLatestForUser(user.ID)
			if err != nil {
				logger.WithUser(user.ID).Error(err)
				return res, err
			}

			// Fetch lender details
			lenderID, _ := underwriting.GetLenderID(sourceEntityID, user.ID, "")
			lenderObj, err := lender.Get(lenderID)
			if err != nil {
				logger.WithUser(user.ID).Errorln(err)
				return res, err
			}

			var offerMeta personalloanoffer.OfferMetadata
			err = json.Unmarshal([]byte(offer.OfferMetadata), &offerMeta)
			if err != nil {
				logger.WithUser(user.ID).Error(err)
				return res, err
			}

			var maxEMI float64
			slabs := []map[string]interface{}{}

			// Iterate through offers and extract relevant details
			for _, offerStruct := range offerMeta.Offers {
				maxEMI = offerStruct.MaxEMI
				slab := map[string]interface{}{
					"maxAmount":         offerStruct.MaxAmount,
					"minAmount":         offerStruct.MinAmount,
					"tenure":            offerStruct.Tenure,
					"processingFee":     offerStruct.ProcessingFee,
					"processingFeeType": offerStruct.ProcessingFeeType,
					"interest":          offerStruct.Interest,
				}
				slabs = append(slabs, slab)
			}

			// for add bankingRequirementLevel
			bankingRequirementLevel, err := users.GetDynamicUserInfoField(user.ID, "bankingRequirementLevel")
			if err != nil {
				logger.WithUser(user.ID).Error(err)
				return res, err
			}

			// Prepare response data with banking requirement level
			resData["dataRequired"] = map[string]any{
				"banking": bankingRequirementLevel,
			}

			// Construct response with offer details
			resData["status"] = "SUCCESS"
			resData["offers"] = []map[string]interface{}{
				{
					"offerID":       offer.LoanOfferID,
					"createdAt":     offer.CreatedAt,
					"expiresAt":     "",
					"lenderID":      lenderID,
					"lenderName":    lenderObj.LenderName,
					"lenderLogoURL": lenderObj.LenderLogoURL,
					"status":        "ACTIVE",
					"offerType":     offer.OfferType,
					"slabs":         slabs,
					"maxEMI":        maxEMI,
				},
			}
		case sourceEntityID == constants.SuperMoneyID:
			offer, err := personalloanoffer.GetLatestForUser(user.ID)
			if err != nil {
				logger.WithUser(user.ID).Error(err)
				return res, err
			}

			// Select offer expiry
			expiryAt, err := expiry.GetUserOfferExpiry(user.ID)
			if err != nil {
				logger.WithUser(user.ID).Error(err)
				return res, err
			}

			offerExpiryTime, err := time.Parse("02-Jan-2006", expiryAt)
			if err != nil {
				logger.WithUser(user.ID).Error(err)
				return res, err
			}

			lenderID, _ := underwriting.GetLenderID(sourceEntityID, user.ID, "")
			lenderObj, err := lender.Get(lenderID)
			if err != nil {
				logger.WithUser(user.ID).Errorln(err)
				return res, err
			}

			var offerMeta personalloanoffer.OfferMetadata
			err = json.Unmarshal([]byte(offer.OfferMetadata), &offerMeta)
			if err != nil {
				logger.WithUser(user.ID).Error(err)
				panic(err)
			}
			dataRequired := map[string]string{}
			if offer.OfferType == "initial" {
				if offerMeta.Offers[0].Segments == "blue" {
					dataRequired["banking"] = "optional"
				} else {
					dataRequired["banking"] = "mandatory"
				}
			}
			resData["status"] = "SUCCESS"
			resData["dataRequired"] = dataRequired
			resData["offers"] = []map[string]interface{}{
				{
					"offerID":       offer.LoanOfferID,
					"createdAt":     offer.CreatedAt,
					"expiresAt":     offerExpiryTime.Format(constants.APIStackDateTimeFormat),
					"lenderID":      lenderID,
					"lenderName":    lenderObj.LenderName,
					"lenderLogoURL": lenderObj.LenderLogoURL,
					"status":        "ACTIVE",
					"offerType":     offer.OfferType,
					"slabs":         offerMeta.Offers,
				},
			}
		default:
			// TODO: Implement `offergetter.GetOffers` to make code consistent
			// implement some interface with well defined structure for all types
			offer, err := personalloanoffer.GetLatestForUser(user.ID)
			if err != nil {
				logger.WithUser(user.ID).Error(err)
				return res, err
			}
			// Select offer expiry
			expiryAt, err := expiry.GetUserOfferExpiry(user.ID)
			if err != nil {
				logger.WithUser(user.ID).Error(err)
				return res, err
			}

			offerExpiryTime, err := time.Parse("02-Jan-2006", expiryAt)
			if err != nil {
				logger.WithUser(user.ID).Error(err)
				return res, err
			}
			lenderID, _ := underwriting.GetLenderID(sourceEntityID, user.ID, "")
			lenderObj, err := lender.Get(lenderID)
			if err != nil {
				logger.WithUser(user.ID).Errorln(err)
				return res, err
			}
			var offerMeta structs.APIStackOfferMetaData
			err = json.Unmarshal([]byte(offer.OfferMetadata), &offerMeta)
			if err != nil {
				logger.WithUser(user.ID).Error(err)
				return res, err
			}

			// fetch and prepare output variables to be sent in response
			// get raw output variables and parse them
			outputVarsRes, err := decisionengine.GetDecisionAndOutputVariables(user.ID, lenderID)
			if err != nil {
				logger.WithUser(user.ID).Error(err)
				return res, err
			}
			outputVarsMap := map[string]any{}
			err = json.Unmarshal([]byte(outputVarsRes.OutputVariables), &outputVarsMap)
			if err != nil {
				logger.WithUser(user.ID).Error(err)
				return res, err
			}
			// pick the exposed output variables in set in final output variables map
			exposedOutputVars := []string{"thick_thin_multiplier"}
			exposedOutputVariables := map[string]any{}
			for _, v := range exposedOutputVars {
				if _, ok := outputVarsMap[v]; ok {
					exposedOutputVariables[v] = outputVarsMap[v]
				}
			}
			resData["status"] = "SUCCESS"
			resData["outputVariables"] = exposedOutputVariables
			resData["offers"] = []map[string]interface{}{
				{
					"offerID":       offer.LoanOfferID,
					"createdAt":     offer.CreatedAt,
					"expiresAt":     offerExpiryTime.Format(constants.APIStackDateTimeFormat),
					"lenderID":      lenderID,
					"lenderName":    lenderObj.LenderName,
					"lenderLogoURL": lenderObj.LenderLogoURL,
					"status":        "ACTIVE",
					"offerType":     offer.OfferType,
					"slabs":         offerMeta.Slabs,
				},
			}
		}
		statusCode = constants.APIGetOffersStatusSuccess
	case jobs.StatusRunning:
		{
			// Do nothing
		}
	default:
		{
			errorHandler.ReportToSentryWithoutRequest(fmt.Errorf("job status unhandled in GetBREResult: %d, userID: %s", operation.Status, userID))
			logger.WithUser(userID).Info(general.AnyToJSONString(operation))
		}
	}

	return APIResult{Data: resData, StatusCode: statusCode, ReferenceID: referenceID, CustomerID: customerID}, nil
}

// GetAgreementSignResult returns the API result for APIStack AgreementDetails API
func GetAgreementSignResult(ctx context.Context, referenceID, sourceEntityID string) (res APIResult, err error) {

	operation, err := getUserOperationInfo(referenceID, sourceEntityID, []string{jobs.AgreementSignAsync}, QueryWithExactIDAndName)
	if err != nil {
		logger.WithSourceEntity(sourceEntityID).Error(err)
		if err == sql.ErrNoRows {
			return res, &structs.CustomError{Err: errors.New(constants.ErrStringInvalidReferenceID), ErrCode: "", HTTPCode: http.StatusNotFound}
		}
		return res, err
	}
	userID := operation.UserID
	jobStatus := operation.Status

	// fill customerID
	customerID, err := users.GetUniqueIDFromUserID(userID)
	if err != nil {
		logger.WithUser(userID).Errorln(err, "err getting customerID for user")
		return res, err
	}

	// Default response
	resData := map[string]any{
		"referenceID":  referenceID,
		"status":       "PROCESSING",
		"agreementURL": nil,
		"customerID":   customerID,
	}
	statusCode := constants.APIGetAgreementResultSuccess

	switch jobStatus {
	case jobs.StatusFailed:
		{
			resData["status"] = "FAILED"
		}
	case jobs.StatusCompleted:
		{
			resData["status"] = "SUCCESS"
			// Get agreement sign from loan application
			var err error
			var signedURLKey string
			err = retry.CustomRetry(10, 50*time.Millisecond, func() error {
				signedURLKey, err = loanapplication.GetSignURLForUser(userID)
				if err != nil {
					logger.WithUser(userID).Error(err)
					return err
				}
				if signedURLKey == "" {
					logger.WithUser(userID).Error("Not able to find sign url key for LA")
					return fmt.Errorf("not able to find sign url key for LA, userID: %s", userID)
				}
				return nil
			})
			if err != nil {
				logger.WithUser(userID).Error(err)
				return res, err
			}
			signedURL := s3.GetPresignedURLS3(signedURLKey, 60)
			logger.DebugWithUser(userID, signedURL, " is the sign url and is it ", signedURL, " userID: ", userID)
			resData["agreementURL"] = signedURL
			statusCode = constants.APIGetAgreementResultSuccess
		}
	case jobs.StatusRunning:
		{
			// DO NOTHING
		}
	default:
		{
			errorHandler.ReportToSentryWithoutRequest(fmt.Errorf("[GetAgreementSignResult] job status unhandled in GetAgreementSignResult: %d, userID: %s, referenceID: %s", jobStatus, userID, referenceID))
			logger.WithUser(userID).Info(jobStatus)
		}
	}

	return APIResult{Data: resData, StatusCode: statusCode, ReferenceID: referenceID, CustomerID: customerID}, nil
}

// GetDisbursalResult returns the API result for APIStack DisbursalDetails API
func GetDisbursalResult(ctx context.Context, referenceID, sourceEntityID string) (res APIResult, err error) {
	// Check if the item with referenceID exists
	operation, err := getUserOperationInfo(referenceID, sourceEntityID, []string{jobs.Disbursal}, QueryWithExactIDAndName)
	if err != nil {
		logger.WithSourceEntity(sourceEntityID).Error(err)
		if err == sql.ErrNoRows {
			return res, &structs.CustomError{Err: errors.New(constants.ErrStringInvalidReferenceID), ErrCode: "", HTTPCode: http.StatusNotFound}
		}
		return res, err
	}
	userID := operation.UserID
	jobStatus := operation.Status

	// fill customerID
	customerID, err := users.GetUniqueIDFromUserID(userID)
	if err != nil {
		logger.WithUser(userID).Errorln(err, "err getting customerID for user")
		return res, err
	}

	// Get latest loan application
	loanApp, err := loanapplication.GetLatestByUser(userID)
	if err != nil {
		logger.WithUser(userID).Error(err)
		if errors.Is(err, sql.ErrNoRows) {
			return res, &structs.CustomError{Err: errors.New(constants.ErrStringLoanNotFound), ErrCode: "", HTTPCode: http.StatusNotFound}
		}
		return res, err
	}

	// Default response
	resData := map[string]any{
		"referenceID":       referenceID,
		"status":            "PROCESSING",
		"message":           "",
		"insurance":         nil,
		"customerID":        customerID,
		"loanAccountNumber": loanApp.LoanApplicationNo,
	}
	statusCode := constants.APIGetDisbursalResultSuccess

	insurance, insuranceExists, err := getInsuranceResult(userID, loanApp.ID.String())
	if err != nil {
		logger.WithUser(userID).Error(err)
		return res, err
	}

	switch jobStatus {
	case jobs.StatusFailed:
		{
			resData["status"] = "FAILED"
			resData["message"] = operation.Message
			// If message is one of the rejection messages
			// the api result status should be REJECTED
			if general.InArr(operation.Message, DisbursalRejectedMessages) {
				resData["status"] = "REJECTED"
			}
		}
	case jobs.StatusCompleted:
		{
			disbursalResult, err := getDisbursalDetails(userID, loanApp)
			if err != nil {
				logger.WithUser(userID).Error(err)
				return res, err
			}

			// Setting the UTR response
			resData["utrNumber"] = loanApp.LenderTransactionNumber
			resData["disbursedAt"] = disbursalResult.DisbursedAt
			resData["disbursedAmount"] = disbursalResult.DisbursedAmount
			resData["loanAccountNumber"] = loanApp.LoanApplicationNo
			resData["message"] = "Success"
			resData["status"] = "SUCCESS"
			resData["insurance"] = insurance
		}

	case jobs.StatusRunning:
		{
			// Update message for operation (currently used for PayTm T+3 requirement)
			resData["message"] = operation.Message

			if insuranceExists {
				resData["insurance"] = insurance
			}
		}
	default:
		{
			err = fmt.Errorf("job status unhandled in GetAgreementSignResult: %d, userID: %s", jobStatus, userID)
			errorHandler.ReportToSentryWithoutRequest(err)
			logger.WithUser(userID).Error(err)
		}
	}

	return APIResult{Data: resData, StatusCode: statusCode, ReferenceID: referenceID, CustomerID: customerID}, nil
}

func GetPrequalificationResults(ctx context.Context, referenceID, sourceEntityID string) (res APIResult, err error) {
	operation, err := getUserOperationInfo(referenceID, sourceEntityID, []string{jobs.Prequalification}, QueryWithExactIDAndName)
	if err != nil {
		logger.WithSourceEntity(sourceEntityID).Error(err)
		if err == sql.ErrNoRows {
			return res, &structs.CustomError{Err: errors.New(constants.ErrStringInvalidReferenceID), ErrCode: "", HTTPCode: http.StatusNotFound}
		}
		return res, err
	}
	userID := operation.UserID
	jobStatus := operation.Status

	user, err := users.Get(userID)
	if err != nil {
		logger.WithUser(userID).Errorln(err)
		return res, err
	}

	// Default response
	resData := map[string]any{
		"referenceID": referenceID,
		"status":      "PROCESSING",
		"customerID":  user.UniqueID,
		"results":     []prequalResult{},
	}
	statusCode := constants.APIGetPreqaulStatusSuccess

	switch jobStatus {
	case jobs.StatusFailed:
		pqRes, err := PreparePrequalResponse(ctx, *user.Status, referenceID, true)
		if err != nil {
			logger.WithUser(user.ID).Errorln(err)
			return res, err
		}
		resData["status"] = "FAILED"
		resData["results"] = pqRes
	case jobs.StatusCompleted:
		pqRes, err := PreparePrequalResponse(ctx, *user.Status, referenceID, false)
		if err != nil {
			logger.WithUser(user.ID).Errorln(err)
			return res, err
		}
		resData["status"] = "SUCCESS"
		resData["results"] = pqRes

		if err := addPrequalificationResponseExtras(ctx, resData, userID, sourceEntityID); err != nil {
			logger.WithUser(userID).Errorln(err)
			return res, err
		}
	case jobs.StatusRunning:
		statusCode = constants.APIGetPrequalStatusProcessing
	default:
		{
			errorHandler.ReportToSentryWithoutRequest(fmt.Errorf("[GetPrequalificationResults] job status unhandled in GetPrequalificationResults: %d, userID: %s, referenceID: %s", jobStatus, userID, referenceID))
			logger.WithUser(userID).Infoln(operation)
		}
	}

	return APIResult{Data: resData, StatusCode: statusCode, ReferenceID: referenceID, CustomerID: user.UniqueID}, nil
}

func getUserOperationInfo(referenceID, sourceEntityID string, jobNames []string, queryType APIResultQueryType) (op *OperationInfo, err error) {
	// check if argument jobNames is not blank
	if len(jobNames) == 0 {
		return nil, fmt.Errorf("no job name passed")
	}
	// Attempt to fetch job information
	var job jobs.Job
	switch queryType {
	case QueryWithExactIDAndName:
		job, err = commonutils.GetJobForClientByIDName(referenceID, jobNames[0], sourceEntityID)
	case QueryWithIDAndNamesInArr:
		job, err = commonutils.GetJobForClientByIDNames(referenceID, sourceEntityID, jobNames)
	case QueryWithIDAndNameLike:
		job, err = commonutils.GetJobForClientByIDLikeName(referenceID, jobNames[0], sourceEntityID)
	default:
		err = fmt.Errorf("unknown type for argument queryType")
	}
	if err == nil {
		// If no error, return job details
		return &OperationInfo{
			ReferenceID:   job.JobID,
			OperationName: job.JobName,
			UserID:        job.UserID,
			Status:        job.Status,
			Message:       job.Message,
			Metadata:      "{}",
			Type:          OperationTypeJob,
		}, nil
	}

	// Log the error if not related to "no rows" scenario
	if err != sql.ErrNoRows {
		logger.WithSourceEntity(sourceEntityID).Error(err)
		return nil, err
	}

	// At this point, err is sql.ErrNoRows. Try to fetch workflow information
	workFlowData, err := userworkflows.GetByID(context.TODO(), referenceID)
	if err != nil {
		// Log the error and return if unable to fetch workflow data
		logger.WithSourceEntity(sourceEntityID).Error(err)
		return nil, err
	}

	// If the workflow status in DB is running check with temporal if the workflow
	// backing this operation is running or not
	if workFlowData.Status == constants.TemporalStatusRunning {
		// TODO: pass real context here
		isWorkflowFailedAbruptly, errorCode, _, err := temporalutility.RunSanityChecksOnWorkflowID(context.TODO(), workFlowData.UserID, workFlowData.WorkflowID, workFlowData.RunID)
		if err != nil {
			logger.WithUser(workFlowData.UserID).Errorf("[getUserOperationInfo] failed to get workflow health, err: %v, userID: %s, workflowID: %s, runID: %s", err, workFlowData.UserID, workFlowData.WorkflowID, workFlowData.RunID)
			return nil, err
		}
		if isWorkflowFailedAbruptly {
			// Update current workflow status as failed
			workFlowData.Status = constants.TemporalStatusFailed
			// Update the failure reason for API stack based on errorCode
			var pass bool
			workFlowData.FailureReason, pass = TemporalWorkflowErrorToMessage[errorCode]
			if !pass {
				sentry.CaptureMessage(fmt.Sprintf("failure reason not found for errorCode: %s in TemporalWorkflowErrorToMessage map", errorCode))
				workFlowData.FailureReason = constants.APIErrorInternalFailure
			}
			// Update the last workflow status as failed
			errWfUpdate := userworkflows.UpdateStatus(nil, workFlowData.UserID, workFlowData.Status, workFlowData.WorkflowID, workFlowData.RunID, errorCode, workFlowData.FailureReason)
			if errWfUpdate != nil {
				errorHandler.ReportToSentryWithoutRequest(errWfUpdate)
				return nil, errWfUpdate
			}
		}
	}

	// Map workflow status to job status and return
	var status int
	switch workFlowData.Status {
	case constants.TemporalStatusRunning:
		status = jobs.StatusRunning
	case constants.TemporalStatusFailed:
		status = jobs.StatusFailed
	case constants.TemporalStatusSuccess:
		status = jobs.StatusCompleted
	default:
		status = -1
	}

	// If the workflow status is archived, mark the job status as failed
	if strings.Contains(workFlowData.Status, constants.TemporalStatusArchivedSuffix) {
		status = jobs.StatusFailed
	}

	return &OperationInfo{
		ReferenceID:   workFlowData.ID,
		OperationName: workFlowData.ModuleName,
		UserID:        workFlowData.UserID,
		Status:        status,
		Message:       workFlowData.FailureReason,
		Metadata:      workFlowData.Metadata,
		Type:          OperationTypeUserWorkflow,
	}, nil
}

func GetCreateReceivableResult(ctx context.Context, referenceID, sourceEntityID string) (res APIResult, err error) {

	operation, err := getUserOperationInfo(referenceID, sourceEntityID, []string{jobs.CreateReceivable}, QueryWithExactIDAndName)
	if err != nil {
		logger.WithSourceEntity(sourceEntityID).Error(err)
		if err == sql.ErrNoRows {
			return res, &structs.CustomError{Err: errors.New(constants.ErrStringInvalidReferenceID), ErrCode: "", HTTPCode: http.StatusNotFound}
		}
		return res, err
	}

	userID := operation.UserID
	jobStatus := operation.Status

	// fill customerID
	customerID, err := users.GetUniqueIDFromUserID(userID)
	if err != nil {
		logger.WithUser(userID).Errorln(err, "err getting customerID for user")
		return res, err
	}

	var workflowMetaDataMap map[string]interface{}
	err = json.Unmarshal([]byte(operation.Metadata), &workflowMetaDataMap)
	if err != nil {
		err := fmt.Errorf("[GetCreateReceivableResult] Error in un-marshalling workflow metadata for referenceID: %s, userID: %s", referenceID, userID)
		logger.WithUser(userID).Error(err)
		return res, err
	}

	// Default response
	resData := map[string]any{
		"referenceID":             referenceID,
		"status":                  constants.GetResultStatusProcessing,
		"message":                 nil,
		"customerID":              customerID,
		"loanApplicationID":       nil,
		"lenderChargeReferenceID": nil,
	}
	statusCode := constants.APIGetCreateReceivableResultSuccess

	if jobStatus != jobs.StatusCompleted {
		switch jobStatus {
		case jobs.StatusRunning:
			return APIResult{Data: resData, StatusCode: statusCode, ReferenceID: referenceID, CustomerID: customerID}, nil
		case jobs.StatusFailed:
			resData["status"] = constants.GetResultStatusFailed
			return APIResult{Data: resData, StatusCode: statusCode, ReferenceID: referenceID, CustomerID: customerID}, nil
		default:
			err := fmt.Errorf("workflow status unhandled in GetCreateReceivableResult: %v, userID: %s", jobStatus, userID)
			errorHandler.ReportToSentryWithoutRequest(err)
			logger.WithUser(userID).Error(err)
			return res, err
		}
	}

	resData["status"] = constants.GetResultStatusSuccess
	resData["message"] = "SUCCESS"
	resData["loanApplicationID"] = workflowMetaDataMap["loanApplicationID"]
	resData["lenderChargeReferenceID"] = workflowMetaDataMap["lenderChargeReferenceID"]

	return APIResult{Data: resData, StatusCode: statusCode, ReferenceID: referenceID, CustomerID: customerID}, nil
}

func GetCancelReceivableResult(ctx context.Context, referenceID, sourceEntityID string) (res APIResult, err error) {

	operation, err := getUserOperationInfo(referenceID, sourceEntityID, []string{jobs.CreateReceivable}, QueryWithExactIDAndName)
	if err != nil {
		logger.WithSourceEntity(sourceEntityID).Error(err)
		if err == sql.ErrNoRows {
			return res, &structs.CustomError{Err: errors.New(constants.ErrStringInvalidReferenceID), ErrCode: "", HTTPCode: http.StatusNotFound}
		}
		return res, err
	}
	userID := operation.UserID
	jobStatus := operation.Status

	// fill customerID
	customerID, err := users.GetUniqueIDFromUserID(userID)
	if err != nil {
		logger.WithUser(userID).Errorln(err, "err getting customerID for user")
		return res, err
	}

	var workflowMetaDataMap map[string]interface{}
	err = json.Unmarshal([]byte(operation.Metadata), &workflowMetaDataMap)
	if err != nil {
		err := fmt.Errorf("[GetCancelReceivableResult] Error in un-marshalling workflow metadata for referenceID: %s, userID: %s", referenceID, userID)
		logger.WithUser(userID).Error(err)
		return res, err
	}

	// Default response
	resData := map[string]any{
		"referenceID":             referenceID,
		"status":                  constants.GetResultStatusProcessing,
		"message":                 nil,
		"customerID":              customerID,
		"loanApplicationID":       nil,
		"lenderChargeReferenceID": nil,
	}
	statusCode := constants.APIGetCancelReceivableResultSuccess

	if jobStatus != jobs.StatusCompleted {
		switch jobStatus {
		case jobs.StatusRunning:
			return APIResult{Data: resData, StatusCode: statusCode, ReferenceID: referenceID, CustomerID: customerID}, nil
		case jobs.StatusFailed:
			resData["status"] = constants.GetResultStatusFailed
			return APIResult{Data: resData, StatusCode: statusCode, ReferenceID: referenceID, CustomerID: customerID}, nil
		default:
			err := fmt.Errorf("workflow status unhandled in GetCancelReceivableResult: %v, userID: %s", jobStatus, userID)
			errorHandler.ReportToSentryWithoutRequest(err)
			logger.WithUser(userID).Error(err)
			return res, err
		}
	}

	resData["status"] = constants.GetResultStatusSuccess
	resData["message"] = "SUCCESS"
	resData["loanApplicationID"] = workflowMetaDataMap["loanApplicationID"]
	resData["lenderChargeReferenceID"] = workflowMetaDataMap["lenderChargeReferenceID"]

	return APIResult{Data: resData, StatusCode: statusCode, ReferenceID: referenceID, CustomerID: customerID}, nil
}

func GetCreatePayableResult(ctx context.Context, referenceID, sourceEntityID string) (res APIResult, err error) {
	operation, err := getUserOperationInfo(referenceID, sourceEntityID, []string{jobs.CreatePayable}, QueryWithExactIDAndName)
	if err != nil {
		logger.WithSourceEntity(sourceEntityID).Error(err)
		if err == sql.ErrNoRows {
			return res, &structs.CustomError{Err: errors.New(constants.ErrStringInvalidReferenceID), ErrCode: "", HTTPCode: http.StatusNotFound}
		}
		return res, err
	}
	userID := operation.UserID
	jobStatus := operation.Status

	// fill customerID
	customerID, err := users.GetUniqueIDFromUserID(userID)
	if err != nil {
		logger.WithUser(userID).Errorln(err, "err getting customerID for user")
		return res, err
	}

	var workflowMetaDataMap map[string]interface{}
	err = json.Unmarshal([]byte(operation.Metadata), &workflowMetaDataMap)
	if err != nil {
		err := fmt.Errorf("[GeCreatePayableResult] Error in un-marshalling workflow metadata for referenceID: %s, userID: %s", referenceID, userID)
		logger.WithUser(userID).Error(err)
		return res, err
	}

	// Default response
	resData := map[string]any{
		"referenceID":             referenceID,
		"status":                  constants.GetResultStatusProcessing,
		"message":                 nil,
		"customerID":              customerID,
		"loanApplicationID":       nil,
		"lenderChargeReferenceID": nil,
	}

	statusCode := constants.ApiGetCreatePayableResultSuccess

	if jobStatus != jobs.StatusCompleted {
		switch jobStatus {
		case jobs.StatusRunning:
			return APIResult{Data: resData, StatusCode: statusCode, ReferenceID: referenceID, CustomerID: customerID}, nil
		case jobs.StatusFailed:
			resData["status"] = constants.GetResultStatusFailed
			return APIResult{Data: resData, StatusCode: statusCode, ReferenceID: referenceID, CustomerID: customerID}, nil
		default:
			err := fmt.Errorf("workflow status unhandled in GetCreatePayableResult: %v, userID: %s", jobStatus, userID)
			errorHandler.ReportToSentryWithoutRequest(err)
			logger.WithUser(userID).Error(err)
			return res, err
		}
	}
	resData["status"] = constants.GetResultStatusSuccess
	resData["message"] = "SUCCESS"
	resData["loanApplicationID"] = workflowMetaDataMap["loanApplicationID"]
	resData["lenderChargeReferenceID"] = workflowMetaDataMap["lenderChargeReferenceID"]

	return APIResult{Data: resData, StatusCode: statusCode, ReferenceID: referenceID, CustomerID: customerID}, nil
}

func GetCancelPayableResult(ctx context.Context, referenceID, sourceEntityID string) (res APIResult, err error) {
	operation, err := getUserOperationInfo(referenceID, sourceEntityID, []string{jobs.CancelPayable}, QueryWithExactIDAndName)
	if err != nil {
		logger.WithSourceEntity(sourceEntityID).Error(err)
		if err == sql.ErrNoRows {
			return res, &structs.CustomError{Err: errors.New(constants.ErrStringInvalidReferenceID), ErrCode: "", HTTPCode: http.StatusNotFound}
		}
		return res, err
	}
	userID := operation.UserID
	jobStatus := operation.Status

	// fill customerID
	customerID, err := users.GetUniqueIDFromUserID(userID)
	if err != nil {
		logger.WithUser(userID).Errorln(err, "err getting customerID for user")
		return res, err
	}

	var workflowMetaDataMap map[string]interface{}
	err = json.Unmarshal([]byte(operation.Metadata), &workflowMetaDataMap)
	if err != nil {
		err := fmt.Errorf("[GetCancelPayableResult] Error in un-marshalling workflow metadata for referenceID: %s, userID: %s", referenceID, userID)
		logger.WithUser(userID).Error(err)
		return res, err
	}

	// Default response
	resData := map[string]any{
		"referenceID":             referenceID,
		"status":                  constants.GetResultStatusProcessing,
		"message":                 nil,
		"customerID":              customerID,
		"loanApplicationID":       nil,
		"lenderChargeReferenceID": nil,
	}

	statusCode := constants.APIGetCancelPayableResultSuccess

	if jobStatus != jobs.StatusCompleted {
		switch jobStatus {
		case jobs.StatusRunning:
			return APIResult{Data: resData, StatusCode: statusCode, ReferenceID: referenceID, CustomerID: customerID}, nil
		case jobs.StatusFailed:
			resData["status"] = constants.GetResultStatusFailed
			return APIResult{Data: resData, StatusCode: statusCode, ReferenceID: referenceID, CustomerID: customerID}, nil
		default:
			err := fmt.Errorf("workflow status unhandled in GetCancelPayableResult: %v, userID: %s", jobStatus, userID)
			errorHandler.ReportToSentryWithoutRequest(err)
			logger.WithUser(userID).Error(err)
			return res, err
		}
	}

	resData["status"] = constants.GetResultStatusSuccess
	resData["message"] = "SUCCESS"
	resData["loanApplicationID"] = workflowMetaDataMap["loanApplicationID"]
	resData["lenderChargeReferenceID"] = workflowMetaDataMap["lenderChargeReferenceID"]

	return APIResult{Data: resData, StatusCode: statusCode, ReferenceID: referenceID, CustomerID: customerID}, nil
}

func GetRepaymentResult(ctx context.Context, referenceID, sourceEntityID string) (res APIResult, err error) {
	operation, err := getUserOperationInfo(referenceID, sourceEntityID, []string{jobs.Repayment}, QueryWithExactIDAndName)
	if err != nil {
		logger.WithSourceEntity(sourceEntityID).Error(err)
		if err == sql.ErrNoRows {
			return res, &structs.CustomError{Err: errors.New(constants.ErrStringInvalidReferenceID), ErrCode: "", HTTPCode: http.StatusNotFound}
		}
		return res, err
	}

	customerID, err := users.GetUniqueIDFromUserID(operation.UserID)
	if err != nil {
		logger.WithUser(operation.UserID).Error(err)
		return APIResult{}, err
	}

	// Default response
	resData := map[string]any{
		"referenceID":       referenceID,
		"status":            "PROCESSING",
		"message":           "",
		"customerID":        customerID,
		"repaymentID":       "",
		"loanApplicationID": "",
		"lenderRepaymentID": "",
	}

	statusCode := constants.APIGetRepaymentResultSuccess

	switch operation.Status {
	case jobs.StatusCompleted:
		resData["status"] = "SUCCESS"
		resData["message"] = "SUCCESS"

		// get records in case of success
		var workFlowMetaDataMap map[string]interface{}
		err = json.Unmarshal([]byte(operation.Metadata), &workFlowMetaDataMap)
		if err != nil {
			err := fmt.Errorf("[GetRepaymentResult] failed to unmarshal metaData into map, err : %w", err)
			logger.WithUser(operation.UserID).Error(err)
			return APIResult{}, err
		}

		recordID, ok := workFlowMetaDataMap["record_id"].(string)
		if !ok {
			err := fmt.Errorf("[GetRepaymentResult] unable to get record_id in the meta data")
			logger.WithUser(operation.UserID).Error(err)
			return APIResult{}, err

		}

		// use that primary key to get the data
		vendorPaymentData, err := vendorpayments.GetByVendorPaymentID(nil, recordID)
		if err != nil {
			logger.WithUser(operation.UserID).Error(err)
			return APIResult{}, err
		}

		var vendorPaymentsMetaDataMap map[string]interface{}
		err = json.Unmarshal([]byte(vendorPaymentData.Metadata), &vendorPaymentsMetaDataMap)
		if err != nil {
			err := fmt.Errorf("[GetRepaymentResult] failed to unmarshal metaData into map, err : %w", err)
			logger.WithUser(operation.UserID).Error(err)
			return APIResult{}, err
		}

		resData["repaymentID"] = vendorPaymentData.VendorTxnID
		resData["loanApplicationID"] = vendorPaymentData.LoanApplicationID
		resData["lenderRepaymentID"] = vendorPaymentsMetaDataMap["receipt_id"]

	case jobs.StatusRunning:
		// let it be processing
	case jobs.StatusFailed:
		resData["status"] = "FAILED"
		resData["message"] = operation.Message

	default:
		err := fmt.Errorf("[GetRepaymentResult] workflow status unhandled in GetRepaymentResult: %v, userID: %s", operation.Status, operation.UserID)
		errorHandler.ReportToSentryWithoutRequest(err)
		logger.WithUser(operation.UserID).Error(err)
	}

	return APIResult{Data: resData, StatusCode: statusCode, ReferenceID: referenceID, CustomerID: customerID}, nil
}

// GetAgreementUnsignedResult returns the result of the unsigned agreement generation operation
func GetAgreementUnsignedResult(ctx context.Context, referenceID, sourceEntityID string) (res APIResult, err error) {
	operation, err := getUserOperationInfo(referenceID, sourceEntityID, []string{jobs.AgreementUnsigned}, QueryWithExactIDAndName)
	if err != nil {
		logger.WithSourceEntity(sourceEntityID).Error(err)
		if err == sql.ErrNoRows {
			return res, &structs.CustomError{Err: errors.New(constants.ErrStringInvalidReferenceID), ErrCode: "", HTTPCode: http.StatusNotFound}
		}
		return res, err
	}

	customerID, err := users.GetUniqueIDFromUserID(operation.UserID)
	if err != nil {
		logger.WithUser(operation.UserID).Error(err)
		return APIResult{}, err
	}

	// Default response
	resData := map[string]any{
		"referenceID":  referenceID,
		"status":       "PROCESSING",
		"agreementURL": nil,
		"kfsURL":       nil,
		"customerID":   customerID,
	}

	statusCode := constants.APIGetUnsignedAgreementSuccess

	res = APIResult{Data: resData, StatusCode: statusCode, ReferenceID: referenceID, CustomerID: customerID}

	switch operation.Status {
	case jobs.StatusCompleted:

		// get records in case of success
		var workflowMetadata map[string]interface{}
		err = json.Unmarshal([]byte(operation.Metadata), &workflowMetadata)
		if err != nil {
			err := fmt.Errorf("[GetAgreementUnsignedResult] failed to unmarshal metaData into map, err : %w", err)
			logger.WithUser(operation.UserID).Error(err)
			errorHandler.ReportToSentryWithFields(map[string]interface{}{
				"userID":      operation.UserID,
				"referenceID": referenceID,
			}, err)
			return res, err
		}

		var agreementPresignedURL string
		var kfsPresignedURL string

		// Retry thrice to get the presigned URL
		if err := retry.CustomRetry(3, 50*time.Millisecond, func() error {
			objectKey, err := media.GetObjectKeyWithUserID(constants.MediaTypeUnsignedLoanAgreement, operation.UserID)
			if err != nil {
				logger.WithUser(operation.UserID).Error(err)
				return err
			}
			agreementPresignedURL = s3.GetPresignedURLS3(objectKey, 60)
			if agreementPresignedURL == "" {
				logger.WithUser(operation.UserID).Error("unable to get presigned url")
				return err
			}
			return nil
		}); err != nil {
			return res, err
		}

		// Retry thrice to get the presigned URL
		if err := retry.CustomRetry(3, 50*time.Millisecond, func() error {
			objectKey, err := media.GetObjectKeyWithUserID(constants.MediaTypeKFSDocument, operation.UserID)
			if err != nil {
				logger.WithUser(operation.UserID).Error(err)
				return err
			}
			kfsPresignedURL = s3.GetPresignedURLS3(objectKey, 60)
			if kfsPresignedURL == "" {
				logger.WithUser(operation.UserID).Error("unable to get presigned url")
				return err
			}
			return nil
		}); err != nil {
			return res, err
		}

		resData["status"] = "SUCCESS"
		resData["agreementURL"] = agreementPresignedURL
		resData["kfsURL"] = kfsPresignedURL
	case jobs.StatusRunning:
		// let it be processing
	case jobs.StatusFailed:
		resData["status"] = "FAILED"
	default:
		err := fmt.Errorf("[GetAgreementUnsignedResult] workflow status unhandled in GetAgreementUnsignedResult: %v, userID: %s", operation.Status, operation.UserID)
		errorHandler.ReportToSentryWithoutRequest(err)
		logger.WithUser(operation.UserID).Error(err)
		return res, err
	}

	return res, nil
}

// GetNDCLetterResult returns the API result for NDC letter fetch
func GetNDCLetterResult(ctx context.Context, referenceID, sourceEntityID string) (res APIResult, err error) {
	operation, err := getUserOperationInfo(referenceID, sourceEntityID, []string{jobs.LisaNDCLetter}, QueryWithExactIDAndName)
	if err != nil {
		logger.WithSourceEntity(sourceEntityID).Error(err)
		if err == sql.ErrNoRows {
			return res, &structs.CustomError{Err: errors.New(constants.ErrStringInvalidReferenceID), ErrCode: "", HTTPCode: http.StatusNotFound}
		}
		return res, err
	}

	customerID, err := users.GetUniqueIDFromUserID(operation.UserID)
	if err != nil {
		logger.WithUser(operation.UserID).Errorln(err, "err getting customerID for user")
		return res, err
	}

	// Default response
	resData := map[string]any{
		"status": "PROCESSING",
	}
	statusCode := constants.APIGetNDCLetterSuccess

	switch operation.Status {
	case jobs.StatusFailed:
		resData["status"] = "FAILED"
		statusCode = constants.APIGetNDCLetterFailure
	case jobs.StatusCompleted:

		var ndcMetadata struct {
			MediaID string `json:"mediaID"`
			Type    string `json:"type"`
		}

		if err := json.Unmarshal([]byte(operation.Metadata), &ndcMetadata); err != nil {
			logger.WithUser(operation.UserID).Error(err)
			errorHandler.ReportToSentryWithFields(map[string]interface{}{
				"userID":      operation.UserID,
				"referenceID": referenceID,
			}, err)
			return res, err
		}

		mediaObj, err := media.Get(ctx, ndcMetadata.MediaID)
		if err != nil {
			logger.WithUser(operation.UserID).Error(err)
			errorHandler.ReportToSentryWithFields(map[string]interface{}{
				"userID":      operation.UserID,
				"referenceID": referenceID,
			}, err)
			return res, err
		}

		// Get the base64 from s3
		base64 := s3.GetBase64FromS3(mediaObj.Path)

		resData["type"] = ndcMetadata.Type
		resData["body"] = base64
		resData["status"] = "SUCCESS"

	case jobs.StatusRunning:
		// Do nothing, keep status as PROCESSING
	default:
		err := fmt.Errorf("workflow status unhandled in GetNDCLetterResult: %d, userID: %s", operation.Status, operation.UserID)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{
			"userID":      operation.UserID,
			"referenceID": referenceID,
		}, err)
		logger.WithUser(operation.UserID).Error(err)
		return res, err
	}

	return APIResult{Data: resData, StatusCode: statusCode, ReferenceID: referenceID, CustomerID: customerID}, nil
}

func GetBankAnalysisResult(ctx context.Context, referenceID, sourceEntityID string) (res APIResult, err error) {
	operation, err := getUserOperationInfo(referenceID, sourceEntityID, []string{jobs.BankAnalysis}, QueryWithExactIDAndName)
	if err != nil {
		logger.WithSourceEntity(sourceEntityID).Error(err)
		if err == sql.ErrNoRows {
			return res, &structs.CustomError{Err: errors.New(constants.ErrStringInvalidReferenceID), ErrCode: "", HTTPCode: http.StatusNotFound}
		}
		return res, err
	}

	customerID, err := users.GetUniqueIDFromUserID(operation.UserID)
	if err != nil {
		logger.WithUser(operation.UserID).Error(err)
		return APIResult{}, err
	}

	// Default response
	resData := map[string]any{
		"referenceID":  referenceID,
		"status":       "PROCESSING",
		"errorCode":    "",
		"errorMessage": "",
	}

	statusCode := constants.APIGetBankAnalysisResultSuccess

	switch operation.Status {
	case jobs.StatusCompleted:
		resData["status"] = "SUCCESS"
	case jobs.StatusFailed:
		resData["status"] = "FAILED"

		type statementAnalysis struct {
			BankAnalysisError struct {
				ErrorCode    string `json:"errorCode"`
				ErrorMessage string `json:"errorMessage"`
			} `json:"bankAnalysisError"`
		}
		var analysisRes statementAnalysis

		err := json.Unmarshal([]byte(operation.Metadata), &analysisRes)
		if err != nil {
			logger.WithUser(operation.UserID).Error(err)
			return APIResult{}, err
		}

		resData["errorCode"] = analysisRes.BankAnalysisError.ErrorCode
		resData["errorMessage"] = analysisRes.BankAnalysisError.ErrorMessage
	}

	return APIResult{Data: resData, StatusCode: statusCode, ReferenceID: referenceID, CustomerID: customerID}, nil
}

func GetReferenceIDByUserIDAndModuleName(ctx context.Context, userID, jobName, sourceEntityID string) (string, error) {
	job, err := commonutils.GetJobForClientByUserAndJobNameLike(userID, jobName, sourceEntityID)
	if err == nil {
		return job.JobID, nil
	}

	wf, err := userworkflows.GetByiLikeModuleName(userID, jobName)
	if err != nil {
		return "", err
	}

	return wf.ID, nil
}

// GetPrincipleWorkflowActivityResult returns the result of the bulk interest certificate generation operation
func GetPrincipleWorkflowActivityResult(ctx context.Context, referenceID, sourceEntityID string) (res APIResult, err error) {
	operation, err := getUserOperationInfo(referenceID, sourceEntityID, []string{jobs.PrincipalWorkflowForBulkActivity}, QueryWithExactIDAndName)
	if err != nil {
		logger.WithSourceEntity(sourceEntityID).Error(err)
		if err == sql.ErrNoRows {
			return res, &structs.CustomError{Err: errors.New(constants.ErrStringInvalidReferenceID), ErrCode: "", HTTPCode: http.StatusNotFound}
		}
		return res, err
	}

	customerID, err := users.GetUniqueIDFromUserID(operation.UserID)
	if err != nil {
		logger.WithUser(operation.UserID).Error(err)
		return APIResult{}, err
	}

	// Default response
	resData := map[string]any{
		"referenceID": referenceID,
		"status":      "PROCESSING",
	}

	statusCode := constants.APIGetBatchedWorkflowSuccess

	res = APIResult{Data: resData, StatusCode: statusCode, ReferenceID: referenceID, CustomerID: customerID}

	switch operation.Status {
	case jobs.StatusCompleted:

		resData["status"] = "SUCCESS"
	case jobs.StatusRunning:
		// let it be processing
	case jobs.StatusFailed:
		resData["status"] = "FAILED"
	default:
		err := fmt.Errorf("[GetPrincipleWorkflowActivityResult] workflow status unhandled in GetPrincipleWorkflowActivityResult: %v, userID: %s", operation.Status, operation.UserID)
		errorHandler.ReportToSentryWithoutRequest(err)
		logger.WithUser(operation.UserID).Error(err)
		return res, err
	}

	return res, nil
}
