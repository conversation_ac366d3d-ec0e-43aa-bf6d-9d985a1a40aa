package supermoney

import (
	"encoding/json"
	"finbox/go-api/models/loanapplication"
)

type AdditionalLoanData struct {
	Loan            *loanapplication.LoanApplication `json:"loan,omitempty"`
	LenderVariables *LenderVariablesData             `json:"lenderVariables,omitempty"`
	Insurance       *InsuranceData                   `json:"insurance,omitempty"`
	Offer           *OfferData                       `json:"offer,omitempty"`
}

type LenderVariablesData struct {
	ReferenceID      string                 `json:"referenceID"`
	LenderID         string                 `json:"lenderID"`
	DynamicVariables map[string]interface{} `json:"dynamicVariables"`
}

type InsuranceData struct {
	Status  int     `json:"status"`
	Premium float64 `json:"premium"`
	Tenure  int     `json:"tenure"`
}

type OfferData struct {
	OfferType string `json:"offerType"`
}

func (a *AdditionalLoanData) ToStringMap() (map[string]interface{}, error) {
	var m map[string]interface{}
	jsonData, err := json.Marshal(a)
	if err != nil {
		return nil, err
	}

	err = json.Unmarshal(jsonData, &m)
	if err != nil {
		return nil, err
	}

	return m, nil
}
