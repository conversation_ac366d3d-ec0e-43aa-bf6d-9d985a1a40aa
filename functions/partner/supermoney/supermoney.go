package supermoney

import (
	"context"
	"database/sql"
	"finbox/go-api/constants"
	"finbox/go-api/errorHandler"
	"finbox/go-api/functions/logger"
	"finbox/go-api/models/insurance"
	"finbox/go-api/models/lendervariables"
	"finbox/go-api/models/loanapplication"
	"finbox/go-api/models/personalloanoffer"
)

// GetAdditionalLoanData fetches additional loan-related data for a user
// Returns a struct containing loan, lender variables, insurance and offer details
func GetAdditionalLoanData(ctx context.Context, userID string) (*AdditionalLoanData, error) {
	additionalData := &AdditionalLoanData{}

	// Use GetLatestByUser to get loan application in a single DB call
	loan, err := loanapplication.GetLatestByUser(userID)
	if err != nil && err != sql.ErrNoRows {
		logger.WithUser(userID).Errorln(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{
			"user_id": userID,
		}, err)
		return nil, err
	}

	// Only attempt to get loan-related data if we have a loan
	if err != sql.ErrNoRows {
		// Convert loan to pointer to match the expected type
		additionalData.Loan = &loan

		lv, err := lendervariables.Get(userID, constants.PoonawallaFincorpID)
		if err != nil && err != sql.ErrNoRows {
			logger.WithUser(userID).Errorln(err)
			return nil, err
		}
		if lv.ReferenceID != "" { // Check for non-empty reference ID instead of struct comparison
			additionalData.LenderVariables = &LenderVariablesData{
				ReferenceID:      lv.ReferenceID,
				LenderID:         lv.LenderID,
				DynamicVariables: lv.DynamicVariablesMap,
			}
		}

		insuranceObj, err := insurance.GetLatestByLoanApplicationID(loan.ID.String())
		if err != nil && err != sql.ErrNoRows {
			logger.WithUser(userID).Errorln(err)
			errorHandler.ReportToSentryWithFields(map[string]interface{}{
				"user_id": userID,
			}, err)
			return nil, err
		}
		if insuranceObj != nil {
			additionalData.Insurance = &InsuranceData{
				Status:  insuranceObj.Status,
				Premium: insuranceObj.Premium,
				Tenure:  insuranceObj.Tenure,
			}
		}

		if loan.LoanOfferID != "" {
			personalLoanOffer := personalloanoffer.Get(loan.LoanOfferID)
			if personalLoanOffer.PersonalLoanOfferID != "" { // Check for non-empty PersonalLoanOfferID
				additionalData.Offer = &OfferData{
					OfferType: personalLoanOffer.OfferType,
				}
			}
		}
	}

	return additionalData, nil
}
