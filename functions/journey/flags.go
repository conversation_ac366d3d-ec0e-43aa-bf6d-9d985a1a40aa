package journey

const FlagDigilockerMainKYC = "digilocker_main_kyc"
const FlagUseKYCService = "kyc_service_integration"
const FlagUseKYCServiceWorkflow = "kyc_service_workflow"
const FlagUPIAutoPay = "upi_autopay"
const FlagUpdatedPANFlow = "updated_pan_flow"
const FlagJourneyBuilder = "journey_builder"
const FlagTemporal = "temporal_flow"
const FlagMultiLoanOffer = "multi_loan_offer"
const FlagMultiLoanOfferV2 = "multi_loan_offer_v2"
const FlagUpdatePersonalInfoFlow = "updated_personal_info"
const FlagSentinelEvalFlowBureau = "sentinel_eval_flow"
const FlagSentinelEvalFlowBank = "sentinel_eval_flow_bank"
const FlagOfferFlowV2 = "offer_flow_v2"
const FlagCurrentModule = "current_module"
const BureauConnectFlag = "bureau_connect" // this flag was used to send predictors' creation request to bureau connect on rollout basis, unused now as all requests are routed to bureau connect
const FlagSentinelEvalMultiBankFlow = "sentinel_eval_multi_bank_flow"
const FlagUseBankConnectFCU = "bank_connect_fcu" // flag used for lenders where perfios was being used as the FCU but cases are being migrated to Bank Connect on rollout basis
const FlagBankConnectSessionFlow = "bank_connect_session_flow"
const FlagTemporalBureau = "temporal_bureau"
const FlagTemporalBankConnectApiStack = "temporal_bank_connect_api_stack"
const FlagABFLAPIStack = "abfl_api_stack"
const FlagABFLMDPFlow = "abfl_mdp_flow"

const FlagBankConnectAPIStack = "bank_connect_api_stack"
const FlagGSTAPIStack = "gst_api_stack"

const FlagAcceptOfferExperimentation = "accept_offer_experimentation"
const FlagAcceptOfferValidations = "accept_offer_validations"

const FlagCoApplicantJourney = "co_applicant_journey"
const FlagCoApplicantJourneyOptional = "co_applicant_journey_optional"

const FlagKYCManualReview = "kyc_manual_review"
const FlagGotenbergPANDoc = "gotenberg_pan_doc"

const FlagBreFlowV3 = "bre_flow_v3"

const FlagBreTemporalFlow = "bre_temporal_flow"

const FlagPANPhoneToUdyam = "pan_phone_to_udyam"
const FlagPANPhoneToUdyamV2 = "pan_phone_to_udyam_v2"

const FlagDisableCKYCInOKYC = "disable_ckyc_in_okyc"

const FlagYOBMatchOnDOBMismatch = "yob_match_on_dob_mismatch" // This flag is used in OKYC to enable Year of Birth (YOB) matching when the complete Date of Birth (DOB) does not match.

const FlagMandatoryLoanAgreementConsentType = "loan_agreement_required_consent_type"
const FlagMandatoryHybridConsentType = "mandatory_hybrid_consent_type"

const FlagEndpointVersion2 = "bre_endpoints_v2" // This tells us that if the user uses new bre policies or old own. This is part of abfl simplification process.
const CompanyCategoryFromLenderVaribales = "company_category_from_lender_variables"

// Octopus APIs and rollout percentage

const FlagOctopusKarix = "octopus_sms_karix"
const FlagOctopusPANDetails = "octopus_pan_detailed"
const FlagOctopusPANGST = "octopus_pan_gst"
const FlagOctopusGSTDetailed = "octopus_gst_details"
const ABFLPLFlagOctopusPANGST = "abflpl_octopus_pan_gst"
const ABFLPLFlagOctopusGSTDetailed = "abflpl_octopus_gst_details"
const FlagKYCServicePANReuse = "kyc_service_pan_reuse"

const FlagS3Sync = "s3_sync"
const FlagAbflBLNonHigherLimitDSA = "abfl_bl_non_higher_limit_dsa"

const FlagLenderGrouping = "lender_grouping"

const FlagCustomWebhookPayload = "custom_webhook_payload" // This flag is used to enable custom modifier for webhook payload for a source entity.

const FlagPreventDuplicateAPIStackModuleCalls = "prevent_duplicate_api_stack_module_calls"

var OctopusServiceRollout = map[string]int{
	FlagOctopusKarix:             100,
	FlagOctopusGSTDetailed:       30,
	FlagOctopusPANGST:            30,
	ABFLPLFlagOctopusPANGST:      0,
	FlagOctopusPANDetails:        30,
	ABFLPLFlagOctopusGSTDetailed: 0,
}

const FlagUnsignedAgreementAPIStackWorkflow = "unsigned_agreement_apistack_workflow"
const FlagPreQualificationAPIStackWorkflow = "pre_qualification_apistack_workflow"
const FlagDisableLenderDedupeInBureauModule = "disable_lender_dedupe_in_bureau_module"

const FlagEnableBEFISCForMC = "enable_befisc_for_mc"
