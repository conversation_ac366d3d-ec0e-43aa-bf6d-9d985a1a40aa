package journey

import (
	"finbox/go-api/constants"
	"finbox/go-api/errorHandler"
	"finbox/go-api/functions/logger"
	"finbox/go-api/models/featureflag"
	"finbox/go-api/models/featureflagsettings"
	"finbox/go-api/utils/general"
	"fmt"
)

func BuilderRolloutPerc(sourceEntityID, leadSource string) (bool, int) {
	if IsIIFLPLSourcing(sourceEntityID) {
		return true, 5
	}
	if IsIIFLBLSourcing(sourceEntityID) {
		return true, 5
	}
	return false, 0
}

func UPIAutoPayRolloutPerc(sourceEntityID string) (bool, int) {
	return false, 0
}

// UpdatedPANRolloutPerc returns if updated PAN flow is enabled and percentage
func UpdatedPANRolloutPerc(sourceEntityID string) (bool, int) {
	// if IsIIFLSourcing(sourceEntityID) {
	// 	return true, 100
	// }
	return false, 0
}

func UpdatedPersonalInfoRolloutPerc(sourceEntityID string) (bool, int) {
	if IsIIFLPLSourcing(sourceEntityID) {
		return true, 100
	}
	return false, 0
}

func UpdatedIIFBLRolloutPerc(sourceEntityID string) (bool, int) {
	if IsIIFLBLSourcing(sourceEntityID) {
		return true, 100
	}
	return false, 0
}

// MultiOfferRolloutPerc returns whether multi offer flow is to be enabled and percentage
func MultiOfferRolloutPerc(sourceEntityID string) (bool, int) {
	if sourceEntityID == constants.TataNexarcID {
		return true, 100
	} else if sourceEntityID == constants.TataPLID {
		return true, 100
	}
	if sourceEntityID == constants.MoneyControlID || sourceEntityID == constants.HousingID {
		return true, 100
	}
	return false, 0
}

func OctopusRolloutPerc(sourceEntityID, serviceType string, flag bool) (bool, int) {

	//if flag = true : IIFLBL flow
	if flag {
		if general.InArr(sourceEntityID, []string{constants.IIFLBLID, constants.ABFLPLID, constants.MoneyControlID}) {

			percentage := OctopusServiceRollout[serviceType]
			return true, percentage
		}
	} else {
		if general.InArr(sourceEntityID, []string{constants.IIFLID}) {

			percentage := OctopusServiceRollout[serviceType]
			return true, percentage
		}
	}
	return false, 0
}

// BureauSentinelEvalRolloutPerc returns whether sentinel evaluation flow is to be enabled and percentage
func BureauSentinelEvalRolloutPerc(sourceEntityID string) (bool, int) {
	return false, 0
}

// BankingSentinelEvalRolloutPerc returns whether sentinel evaluation flow is to be enabled and percentage
func BankingSentinelEvalRolloutPerc(sourceEntityID string) (bool, int) {
	if sourceEntityID == constants.MoneyControlID {
		return true, 100
	}
	if IsMFLBLSourcing(sourceEntityID) {
		return true, 100
	}
	return false, 0
}

// OfferFlowV2 returns whether offer flow v2 is to be enabled and percentage
func OfferFlowV2(sourceEntityID string) (bool, int) {
	if sourceEntityID == constants.SuperMoneyID {
		return true, 100
	}

	if IsPFLSourcing(sourceEntityID) {
		return true, 100
	}

	if sourceEntityID == constants.SwiggyID {
		return true, 100
	}
	if sourceEntityID == constants.HousingID {
		return true, 100
	}
	if sourceEntityID == constants.MoneyControlID {
		return true, 100
	}
	if IsIIFLPLSourcing(sourceEntityID) {
		return true, 100
	}
	if IsIIFLBLSourcing(sourceEntityID) {
		return true, 100
	}
	if IsMFLBLSourcing(sourceEntityID) {
		return true, 100
	}
	if IsIncredSourcing(sourceEntityID) {
		return true, 100
	}
	return false, 0
}

// ReassignOfferFlowV2 returns whether offer flow v2 is to be enabled
func ReassignOfferFlowV2(sourceEntityID string) bool {
	if general.InArr(sourceEntityID, []string{constants.MoneyControlID}) {
		return true
	}

	if IsPFLSourcing(sourceEntityID) {
		return true
	}

	if IsIIFLSourcing(sourceEntityID) {
		return true
	}
	return false
}

// SentinelEvalMultiBankRolloutPerc returns whether sentinel eval multi bank flow is to be enabled and percentage
func SentinelEvalMultiBankRolloutPerc(sourceEntityID string) (bool, int) {
	return false, 0
}

// CurrentModuleFlow returns whether current module flow is to be enabled and percentage
func CurrentModuleFlow(sourceEntityID string) (bool, int) {
	if IsPFLSourcing(sourceEntityID) {
		return true, 100
	}
	if sourceEntityID == constants.SwiggyID {
		return true, 100
	}
	if IsMuthootCLPartner(sourceEntityID) {
		return true, 100
	}
	if IsIIFLBLSourcing(sourceEntityID) {
		return true, 100
	}

	if IsMFLBLSourcing(sourceEntityID) {
		return true, 100
	}
	if IsSuperMoneyJourney("", sourceEntityID) {
		return true, 100
	}
	if general.InArr(sourceEntityID, []string{constants.MoneyControlID, constants.HousingID}) {
		return true, 100
	}
	if IsIncredSourcing(sourceEntityID) {
		return true, 100
	}
	return false, 0
}

// ReassignCurrentModuleFlow returns whether current module flow is to be enabled
func ReassignCurrentModuleFlow(sourceEntityID string) bool {
	if general.InArr(sourceEntityID, []string{constants.SwiggyID, constants.MoneyControlID}) || IsMuthootCLPartner(sourceEntityID) {
		return true
	}

	if IsPFLSourcing(sourceEntityID) {
		return true
	}

	if IsIIFLBLSourcing(sourceEntityID) {
		return true
	}
	return false
}

// ReassignUseKYCService : NOTE - move all 100% rollouts of UseKYCService to this
func ReassignUseKYCService(sourceEntityID string) bool {
	iiflAggRes := IIFLAgg(sourceEntityID, false)
	if general.InArr(sourceEntityID, []string{constants.IIFLID, constants.IIFLBLID}) || iiflAggRes.IsAgg || sourceEntityID == constants.MoneyControlID {
		return true
	}
	if IsABFLPLSourcing(sourceEntityID) {
		return true
	}
	if IsABFLBLSourcing(sourceEntityID) {
		return true
	}
	if IsMintifiAsLender(sourceEntityID) {
		return true
	}
	return false
}

func ReassignUseKYCServiceWorkflow(userID, sourceEntityID string) bool {
	// at the moment no user will enter in following condition as on reinitiation
	// IsCoApplicantJourney will yield false as user_journey table's metadata is reset
	if IsABFLBLSourcing(sourceEntityID) && IsCoApplicantJourney(userID, sourceEntityID) {
		return true
	}
	return false
}

func ReassignFeatureFlagSettings(userID, sourceEntityID string) bool {
	return IsABFLBLSourcing(sourceEntityID)

}

func ReassignUseDigilockerAsMainKYC(sourceEntityID string) bool {
	if general.InArr(sourceEntityID, []string{constants.IIFLID, constants.IIFLBLID}) {
		return true
	}
	res := IIFLAgg(sourceEntityID, false)
	return res.IsAgg
}

// APIStackJourney for API stack journey
func APIStackJourney(leadSource, sourceEntityID string) (bool, int) {
	if (IsABFLBLSourcing(sourceEntityID) || IsIIFLBLSourcing(sourceEntityID)) && leadSource == constants.LeadSourceAPIStack {
		return true, 100
	}
	return false, 0
}

func IsMDPFlow(sourceEntityID string) (bool, int) {
	if IsABFLBLSourcing(sourceEntityID) {
		return true, 100
	}
	return false, 0
}

func CreateAllFeatureFlagsFromSourceEntity(userID, sourceEntityID string) ([]featureflag.SetStruct, error) {
	// get flags from feature rollouts
	apiStackFlags, err := featureflagsettings.GetAllFeatureFlagsBySourceEntity(sourceEntityID)
	if err != nil {
		logger.WithUser(userID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(fmt.Errorf("error getting feature flags for userID %s: %s", userID, err.Error()))
		return nil, err
	}

	// make an array of feature flags set struct
	var flags []featureflag.SetStruct

	for _, flag := range apiStackFlags {
		newFlag := []featureflag.SetStruct{
			{
				Key:   userID,
				Flag:  flag.FeatureFlag,
				Value: general.CalculateFlag(userID, flag.FeatureFlag, int(flag.RolloutPercentage)),
			},
		}
		flags = append(flags, newFlag...)
	}
	logger.WithUser(userID).Infof("Feature flags for user %s, soure entity: %s: %v", userID, sourceEntityID, flags)
	return flags, nil
}

func S3SyncRollout(sourceEntityID string) (bool, int) {
	if general.InArr(sourceEntityID, []string{constants.PhonePeID, constants.BharatPeID, constants.PayTMID}) {
		return true, 100
	}
	return false, 0
}
