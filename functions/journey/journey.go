// Package journey is used to store the config of all journeys
package journey

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"strconv"
	"strings"
	"time"

	"golang.org/x/text/language"
	"golang.org/x/text/message"

	"finbox/go-api/conf"
	"finbox/go-api/constants"
	"finbox/go-api/errorHandler"
	"finbox/go-api/functions/emaillib"
	"finbox/go-api/functions/logger"
	"finbox/go-api/functions/structs"
	"finbox/go-api/htmltemplates"
	"finbox/go-api/infra/db"
	"finbox/go-api/infra/redis"
	"finbox/go-api/models/bankconnectkeys"
	"finbox/go-api/models/featureflag"
	"finbox/go-api/models/loanapplication"
	"finbox/go-api/models/multiuserloanrelations"
	"finbox/go-api/models/personalloanoffer"
	"finbox/go-api/models/preselectedlender"
	"finbox/go-api/models/userbusiness"
	"finbox/go-api/models/userjourney"
	"finbox/go-api/models/usermodulemapping"

	"finbox/go-api/models/users"

	// "finbox/go-api/models/users"
	"finbox/go-api/utils/general"
)

var log = logger.Log
var database = db.GetDB()

const OfferExpiryTimeProd = 60 * 60 * 24 * 30             // 30 days
const OfferExpiryTimeDev = 60 * 60                        // 1 hour
const AllowGenerateOfferThresholdTime = 60 * 60 * 24 * 30 // 30 days
const EMandateFailureThresholdForUPIAutopay = 1           // TODO: Make this client configurable

type AggStruct struct {
	PartnerCode string `db:"agent_code"`
	LoanType    string `db:"product_type"`
	IsAgg       bool
}

const iiflAggSuffix = "_iifl_agg"
const muthootCLSuffix = "_muthoot_suffix"
const abflblSuffix = "_abfl_suffix"
const abflplSuffix = "_abfl_pl_suffix"
const mflblSuffix = "_mfl_bl_suffix"
const pflSuffix = "_pfl_suffix"
const pflplSuffix = "_pfl_pl_suffix"
const apiStackSuffix = "_api_stack_user"
const oneMuthootSuffix = "_one_muthoot_suffix"

// IIFLAgg returns struct indicating whether agg or not, and handy fields
func IIFLAgg(sourceEntityID string, update bool) AggStruct {
	key := sourceEntityID + iiflAggSuffix
	valueStr, _ := redis.Get(context.TODO(), key)
	if valueStr == "" {
		update = true
	}
	var obj AggStruct
	if update {
		query := `select agent_code, product_type from dsa where owner_id = $1 and source_entity_id = $2`
		err := database.Get(&obj, query, constants.IIFLID, sourceEntityID)
		obj.IsAgg = err == nil
		// save in redis (including negative values with isAgg = false)
		valueBytes, err := json.Marshal(obj)
		if err != nil {
			log.Println(err)
		}
		err = redis.Set(key, string(valueBytes), time.Hour*24*7)
		if err != nil {
			log.Println(err)
		}
		return obj
	}
	err := json.Unmarshal([]byte(valueStr), &obj)
	if err != nil {
		log.Println(err)
	}
	return obj
}

func IsOneMuthootPartner(sourceEntityID string) bool {
	if sourceEntityID == constants.OneMuthootID {
		return true
	}
	oneMuthootAgg := IsSourceAgg(sourceEntityID, constants.OneMuthootID, oneMuthootSuffix)
	return oneMuthootAgg.IsAgg
}

var ABFLNonDSAPartners = map[string]string{
	ABFLNonDSAPartnerABFLPL:       constants.ABFLPLID,
	MoneyControlABFLNonDSAPartner: constants.MoneyControlID,
}

const (
	ABFLNonDSAPartnerABFLPL = "abfl_pl"
)

// IIFLNonDSAPartners is a map of virtual partner codes to source entity IDs for non DSA partners of IIFL where lender is IIFL
var IIFLNonDSAPartners = map[string]string{
	constants.IIFLNonDSAPartnerTataMarketplace: constants.TataPLID,
	constants.IIFLNonDSAPartnerSwiggyBL:        constants.SwiggyID,
}

const (
	MoneyControlABFLNonDSAPartner = "moneycontrol_marketplace"
)

// IIFLNonDSAPartnersList is a map of virtual partner codes to source entity IDs for non DSA partners of IIFL where lender is IIFL
var IIFLNonDSAPartnersList = []string{constants.TataNexarcID, constants.HousingID, constants.TataPLID, constants.VyaparID}

// GetLoanAppName returns the loan app name and follow up sentence for a given sourcing entity
func GetLoanAppName(sourceEntityID string, sourceEntityName string, lenderName string) (string, string) {
	// TODO: add support for program ID here later
	loanType, _, _ := GetLoanType(sourceEntityID)
	if sourceEntityID == constants.GeniusID {
		return "Freedom Loans", ""
	} else if sourceEntityID == constants.ShopKiranaCLID {
		return "ShopKirana Credit", ""
	} else if loanType == constants.LoanTypeCreditLine || loanType == constants.LoanTypeOverDraft {
		return sourceEntityName + " Credit", ""
	}
	return lenderName + " Loans", " through " + sourceEntityName
}

// GetSupportEmail returns the support email for a given sourcing entity
func GetSupportEmail(sourceEntityID string, lenderID string) string {
	if general.InArr(sourceEntityID, []string{constants.IIFLID, constants.IIFLBLID}) {
		return "<EMAIL>"
	} else if sourceEntityID == constants.KhatabookID {
		return "<EMAIL>"
	} else if sourceEntityID == constants.TataNexarcID {
		return "<EMAIL>"
	} else if lenderID == constants.MFLBLID {
		return "<EMAIL>"
	} else if lenderID == constants.IIFLID && sourceEntityID == constants.TataPLID {
		return "<EMAIL>"
	} else if lenderID == constants.FibeID {
		return "<EMAIL>"
	} else if lenderID == constants.LandTID {
		return "<EMAIL>"
	} else if IsABFLPLSourcing(sourceEntityID) {
		return "<EMAIL>"
	} else if lenderID == constants.NiroID {
		return "<EMAIL>"
	} else if lenderID == constants.CasheMCID {
		return "<EMAIL>"
	} else if lenderID == constants.PrefrMCID {
		return "<EMAIL>"
	} else if lenderID == constants.RingMCID {
		return "<EMAIL>"
	}
	resp := IIFLAgg(sourceEntityID, false)
	if resp.IsAgg {
		return "<EMAIL>"
	}
	return "<EMAIL>"
}

// GetSupportWAPhone returns the whatsapp phone number of our business for a given sourcing entity
// Note: Not to send source entity and lender both. Send either one for this to work.
func GetSupportWAPhone(sourceEntityID, lenderID string) string {

	if general.InArr(sourceEntityID, []string{constants.IIFLID, constants.IIFLBLID, constants.TataNexarcID}) {
		return ""
	} else if sourceEntityID == constants.KhatabookID {
		return "9606800800"
		// For MoneyControl and Housing, we will send blank so that option is removed from frontend.
	} else if general.InArr(sourceEntityID, []string{constants.MoneyControlID, constants.HousingID}) {
		return ""
	} else if lenderID == constants.FibeID {
		return "***********"
	} else if lenderID == constants.LandTID {
		return "***********"
	} else if IsABFLPLSourcing(sourceEntityID) {
		return "***********"
	} else if lenderID == constants.NiroID {
		return "8095206500"
	} else if lenderID == constants.CasheMCID {
		return "022-69716161"
	} else if lenderID == constants.PrefrMCID {
		return "022-69716161"
	} else if lenderID == constants.RingMCID {
		return "08044745880"
	}
	resp := IIFLAgg(sourceEntityID, false)
	if resp.IsAgg {
		return ""
	}
	return "9108735071"
}

// GetMissCall returns the number to show when miss call phone is there for sourcing entity
func GetMissCall(sourceEntityID string) string {
	if general.InArr(sourceEntityID, []string{constants.IIFLID, constants.IIFLBLID}) {
		return "02262539302"
	} else if sourceEntityID == constants.TataNexarcID {
		return "9022112299"
	}
	resp := IIFLAgg(sourceEntityID, false)
	if resp.IsAgg {
		return "02262539302"
	}
	return ""
}

// GetLoanType returns the loan type, is consumer and text to appear for the product configured for the corresponding sourcing entity
// TODO: add support for program id / name here
func GetLoanType(sourceEntityID string) (string, bool, string) {
	if general.InArr(sourceEntityID, []string{constants.IIFLBLID, constants.IndiaMARTID, constants.GimBooksBLID, constants.DeazzleID, constants.VyaparID, constants.NovopayID, constants.ClearTaxID, constants.NiyoBLID, constants.IppoPayID, constants.TataNexarcID, constants.SwiggyID}) {
		return constants.LoanTypeBusinessLoan, false, "Loan"
	} else if general.InArr(sourceEntityID, []string{constants.CityMallID, constants.PagarBookID, constants.FyndID, constants.GojoID}) {
		return constants.LoanTypeCreditLine, true, "Credit Line"
	} else if IsMuthootCLPartner(sourceEntityID) || general.InArr(sourceEntityID, []string{constants.ArzoooID, constants.DukaanPeID, constants.MediboxID, constants.BeldaraID, constants.ExpressStoresID, constants.ShopKiranaCLID, constants.PlantixID, constants.CashifyID, constants.PayMateID, constants.SolvID, constants.BigBasketID, constants.InfraMarketID, constants.AquaConnectID, constants.FloBizID}) || IsMintifiAsLender(sourceEntityID) {
		return constants.LoanTypeCreditLine, false, "Credit Line"
	} else if general.InArr(sourceEntityID, []string{constants.ODTestID, constants.NinjaCartID, constants.MitronTVID, constants.AgrostarID}) {
		return constants.LoanTypeOverDraft, false, "Credit Line"
	} else if IsABFLPLSourcing(sourceEntityID) {
		return constants.LoanTypePersonalLoan, true, "Loan"
	} else if IsABFLBLSourcing(sourceEntityID) || sourceEntityID == constants.ABFLMarketplaceID {
		return constants.LoanTypeBusinessLoan, false, "Loan"
	} else if IsPFLEDUSourcing(sourceEntityID) {
		return constants.LoanTypeEducationLoan, true, "Loan"
	}

	resp := IIFLAgg(sourceEntityID, false)
	if resp.IsAgg {
		return resp.LoanType, resp.LoanType != constants.LoanTypeBusinessLoan, "Loan"
	}
	return constants.LoanTypePersonalLoan, true, "Loan"
}

// IsECommerceCL returns whether its a case of e-commerce with credit line
func IsECommerceCL(sourceEntityID string) bool {
	return IsMintifiAsLender(sourceEntityID) || IsMuthootCLPartner(sourceEntityID) || general.InArr(sourceEntityID, []string{constants.DukaanPeID, constants.MediboxID, constants.BeldaraID, constants.ExpressStoresID, constants.PlantixID, constants.CashifyID, constants.PayMateID, constants.CityMallID, constants.SolvID, constants.BigBasketID, constants.AgrostarID, constants.FyndID})
}

// ShowPennyDrop returns if penny drop is to be shown or not, and if it is for credit
func ShowPennyDrop(sourceEntityID string) (bool, bool) {
	if sourceEntityID == constants.NiyoID {
		return false, true
	} else if general.InArr(sourceEntityID, []string{constants.ExpressStoresID}) {
		return false, false
	} else {
		loanType, _, _ := GetLoanType(sourceEntityID)
		if general.InArr(loanType, []string{constants.LoanTypeCreditLine, constants.LoanTypeOverDraft}) {
			return true, false
		}
	}
	return true, true
}

// ShowAutoPay returns if e-nach is to be shown or not
func ShowAutoPay(sourceEntityID string) bool {
	return !general.InArr(sourceEntityID, []string{constants.GeniusID, constants.ExpressStoresID, constants.PagarBookID, constants.TestingAccount, constants.PorterID, constants.AgrostarID})
}

// IsIIFLExclusiveLender returns if IIFL is probable lender
func IsIIFLExclusiveLender(sourceEntityID string) bool {
	if general.InArr(sourceEntityID, []string{constants.IIFLBLID, constants.IIFLID, constants.ODTestID, constants.ArzoooID, constants.NiyoBLID, constants.IndiaMARTID, constants.SwiggyID}) {
		return true
	}
	resp := IIFLAgg(sourceEntityID, false)
	return resp.IsAgg
}

// IsIIFLSourcing returns if sourceEntity is either IIFL or DSA
func IsIIFLSourcing(sourceEntityID string) bool {
	if general.InArr(sourceEntityID, []string{constants.IIFLID, constants.IIFLBLID, constants.SwiggyID}) {
		return true
	}
	resp := IIFLAgg(sourceEntityID, false)
	return resp.IsAgg
}

func IsIIFLPLSourcing(sourceEntityID string) bool {
	if sourceEntityID == constants.IIFLID {
		return true
	}
	resp := IIFLAgg(sourceEntityID, false)
	if resp.LoanType == constants.LoanTypePersonalLoan {
		return resp.IsAgg
	}
	return false
}

func IsIIFLBLSourcing(sourceEntityID string) bool {
	if sourceEntityID == constants.IIFLBLID {
		return true
	}
	resp := IIFLAgg(sourceEntityID, false)
	if resp.LoanType == constants.LoanTypeBusinessLoan {
		return resp.IsAgg
	}
	return false
}

func IsIncredSourcing(sourceEntityID string) bool {
	return sourceEntityID == constants.DSAMoneyControlIncredID
}

// IsMultiBankApplicable checks if a user is eligible for the multi-bank feature
func IsMultiBankApplicable(userID, sourceEntityID string) bool {
	if enabled, _ := SentinelEvalMultiBankRolloutPerc(sourceEntityID); enabled {
		return featureflag.Get(userID, FlagSentinelEvalMultiBankFlow)
	}
	return false
}

// ShouldSendKYCEmail returns if KYC email should be sent for provided sourceEntityID or not
func ShouldSendKYCEmail(sourceEntityID string) bool {
	return IsIIFLSourcing(sourceEntityID)
}

// IsUpdatedPANFlow returns if updated PAN flow is enabled
// checks feature flag table
func IsUpdatedPANFlow(userID, sourceEntityID string) bool {
	if IsIIFLSourcing(sourceEntityID) {
		return true
	}
	if enabled, _ := UpdatedPANRolloutPerc(sourceEntityID); enabled {
		return featureflag.Get(userID, FlagUpdatedPANFlow)
	}
	return false
}

// IsTemporalFlow returns if temporal flow is enabled for a module
// checks feature flag table
func IsTemporalFlow(userID, sourceEntityID, moduleName string) bool {
	if moduleName == usermodulemapping.Rejected {
		return false
	}
	if sourceEntityID == constants.DSAKreditBeeMCID {
		return true
	}
	if IsPFLEducationLoanJourney(sourceEntityID) { // exception case. TODO: migrate to temporal & remove this condition
		return moduleName != usermodulemapping.KYC
	}
	if IsABFLPLSourcing(sourceEntityID) && general.InArr(moduleName, []string{usermodulemapping.ENACH, usermodulemapping.ESign, usermodulemapping.Disbursal, "DISBURSED"}) {
		return false
	}
	if (IsPFLSourcing(sourceEntityID) || IsMFLBLSourcing(sourceEntityID)) && general.InArr(moduleName, []string{usermodulemapping.PersonalInfo,
		usermodulemapping.Insurance, usermodulemapping.OfferSelection, usermodulemapping.PennyDrop, usermodulemapping.BankConnect,
		usermodulemapping.Bureau, usermodulemapping.PreDisbursal, usermodulemapping.PreLoan}) {
		return true
	} else if IsABFLPLSourcing(sourceEntityID) && general.InArr(moduleName, []string{usermodulemapping.PreDisbursal}) {
		return true
	} else if sourceEntityID == constants.MoneyControlID && general.InArr(moduleName, []string{usermodulemapping.EmploymentVerification, usermodulemapping.PreDisbursal}) {
		return true
	}
	if sourceEntityID == constants.TataPLID && moduleName == usermodulemapping.BankConnect {
		return true
	}
	return featureflag.Get(userID, TemporalKey(moduleName))
}

func IsBigBasketFlow(sourceEntityID string) bool {
	return sourceEntityID == constants.BigBasketID
}

// ReusePANExtentedResponseKYC returns if PAN extended response needs to be reused in KYC module
func ReusePANExtentedResponseKYC(sourceEntityID string) bool {
	return IsPFLSourcing(sourceEntityID)
}

// IsBureauSentinelEvalFlow returns if sentinel BRE evaluation flow is enabled
func IsBureauSentinelEvalFlow(userID, sourceEntityID string) bool {
	if general.InArr(sourceEntityID, []string{constants.HousingID, constants.MoneyControlID, constants.SwiggyID, constants.IndiaMARTID, constants.SupremeSolarID, constants.TataNexarcID, constants.ABFLMarketplaceID, constants.ABCDMarketPlaceID}) || IsMuthootEDIPartner(sourceEntityID) || IsMuthootCLPartner(sourceEntityID) || IsVyaparLendingKart(userID, sourceEntityID) || IsABFLBLSourcing(sourceEntityID) || IsABFLPLSourcing(sourceEntityID) || IsIIFLBLSourcing(sourceEntityID) || IsIIFLPLSourcing(sourceEntityID) || IsMFLEMIPartner(sourceEntityID) || IsMFLBLSourcing(sourceEntityID) || IsPFLSourcing(sourceEntityID) {
		return true
	}
	if sourceEntityID == constants.VyaparID && preselectedlender.Get(userID) == constants.IIFLID {
		return true
	}
	if enabled, _ := BureauSentinelEvalRolloutPerc(sourceEntityID); enabled {
		return featureflag.Get(userID, FlagSentinelEvalFlowBureau)
	}
	return false
}

// IsBankingSentinelEvalFlow returns if sentinel BRE evaluation flow is enabled
func IsBankingSentinelEvalFlow(userID, sourceEntityID string) bool {
	if general.InArr(sourceEntityID, []string{constants.SwiggyID, constants.IndiaMARTID, constants.ABFLMarketplaceID}) || IsMuthootCLPartner(sourceEntityID) || IsABFLBLSourcing(sourceEntityID) || IsIIFLBLSourcing(sourceEntityID) || IsIIFLPLSourcing(sourceEntityID) || IsABFLPLSourcing(sourceEntityID) || IsPFLSourcing(sourceEntityID) {
		return true
	}
	if sourceEntityID == constants.VyaparID && preselectedlender.Get(userID) == constants.IIFLID {
		return true
	}
	if IsMFLBLSourcing(sourceEntityID) {
		return true
	}
	// hack for old users
	if sourceEntityID == constants.MoneyControlID {
		return true
	}
	if enabled, _ := BankingSentinelEvalRolloutPerc(sourceEntityID); enabled {
		return featureflag.Get(userID, FlagSentinelEvalFlowBank)
	}
	return false
}

// ShowBankDetails is used whether we want to show the bank details or not at the last of the loan journey page - Loan Sanctioned
func ShowBankDetails(sourceEntityID string) bool {
	return !general.InArr(sourceEntityID, []string{constants.GPayIIFLPLID, constants.GPayIIFLBLID})
}

// DoSmartMatch returns if smart match is to be done
func DoSmartMatch(sourceEntityID string) bool {

	return false

	// if general.InArr(sourceEntityID, []string{constants.IIFLBLID, constants.IIFLID, constants.GimBooksBLID, constants.VyaparID, constants.NiyoBLID}) {
	// 	return true
	// }
	// resp := GetMapIIFLAGGVal(false)
	// _, found := resp[sourceEntityID]
	// return found
}

// GetStage2Bureau returns the stage 2 bureau
func GetStage2Bureau(userID string, sourceEntityID string) string {
	if sourceEntityID == constants.VyaparID && userID != "" && preselectedlender.Get(userID) == constants.IIFLID {
		return constants.BureauCIBIL
	}
	if general.InArr(sourceEntityID, []string{constants.BigBasketID, constants.IIFLBLID, constants.IIFLID, constants.GimBooksBLID, constants.NiyoBLID, constants.IndiaMARTID, constants.TataBNPLID, constants.TataPLID, constants.SupremeSolarID, constants.TataNexarcID, constants.SwiggyID, constants.MoneyControlID}) {
		return constants.BureauCIBIL
	}
	if IsABFLBLSourcing(sourceEntityID) || IsABFLPLSourcing(sourceEntityID) || IsMFLBLSourcing(sourceEntityID) || IsPFLSourcing(sourceEntityID) {
		return constants.BureauCIBIL
	}
	if IsMuthootCLPartner(sourceEntityID) || IsMuthootEDIPartner(sourceEntityID) || IsMFLEMIPartner(sourceEntityID) {
		return constants.BureauCIBIL
	}

	resp := IIFLAgg(sourceEntityID, false)
	if resp.IsAgg {
		return constants.BureauCIBIL
	}
	return constants.BureauExperian
}

// GetBCMonthThreshold returns bank connect threshold for number of months (min and max)
// day of the month after which only consider transactions from that month & threshold for number of days (if this is disabled then -1 is returned)
func GetBCMonthThreshold(sourceEntityID string) (int, int, int, int) {
	switch {
	case sourceEntityID == constants.TataNexarcID:
		return 12, 12, 20, -1
	case IsABFLBLSourcing(sourceEntityID) || sourceEntityID == constants.ABFLMarketplaceID:
		return 6, 12, 31, -1
	case IsPFLSourcing(sourceEntityID):
		todaysDate := time.Now().Day()
		return 6, 12, todaysDate - 1, 180
	case IsPFLEDUSourcing(sourceEntityID):
		todaysDate := time.Now().Day()
		return 6, 12, todaysDate - 15, 180
	case sourceEntityID == constants.TataPLID:
		todaysDate := time.Now().Day()
		return 7, 12, todaysDate - 1, -1
	case IsABFLPLSourcing(sourceEntityID):
		todaysDate := time.Now().Day()
		return 6, 12, todaysDate - 1, 120
		// todaysDate := time.Now().Day()
		// startDate := time.Now().AddDate(0, 0, -1*(180+(todaysDate-1)))
		// return 6, 12, todaysDate - 1, 180 + (todaysDate - 1) + (startDate.Day() - 2)
	default:
		return 6, 12, 20, -1
	}
}

func GetBCMonthThresholdV2(sourceEntityID, userID string) (int, int, int, int) {
	dynamicUserInfo, err := users.GetDynamicUserInfoMap(userID)
	if err != nil {
		logger.WithUser(userID).Errorln(err)
	}

	switch {
	case sourceEntityID == constants.TataNexarcID:
		return 12, 12, 20, -1
	case IsABFLBLSourcing(sourceEntityID) || sourceEntityID == constants.ABFLMarketplaceID:
		return 6, 12, 31, -1
	case IsPFLSourcing(sourceEntityID):
		todaysDate := time.Now().Day()
		return 6, 12, todaysDate - 1, 180
	case IsPFLEDUSourcing(sourceEntityID):
		todaysDate := time.Now().Day()
		return 6, 12, todaysDate - 15, 180
	case sourceEntityID == constants.MoneyControlID || IsABFLPLSourcing(sourceEntityID):
		employmentType, isPresent := dynamicUserInfo["employmentType"].(string)
		if isPresent && employmentType == "Salaried" {
			todaysDate := time.Now().Day()
			return 6, 12, todaysDate - 1, 120
		}

		todaysDate := time.Now().Day()
		return 6, 12, todaysDate - 1, 180
		// todaysDate := time.Now().Day()
		// startDate := time.Now().AddDate(0, 0, -1*(180+(todaysDate-1)))
		// return 6, 12, todaysDate - 1, 180 + (todaysDate - 1) + (startDate.Day() - 2)
	default:
		return 6, 12, 20, -1
	}
}

// GetBCRange returns from and to date string (YYYY-MM-DD) based on condition of returning based on min
// or max threshold
func GetBCRange(sourceEntityID, userID string, today time.Time, returnMax bool) (time.Time, time.Time) {

	var (
		minThreshold  int
		maxThreshold  int
		dayOfMonth    int
		daysThreshold int
	)

	if userID == "" {
		minThreshold, maxThreshold, dayOfMonth, daysThreshold = GetBCMonthThreshold(sourceEntityID)
	} else {
		minThreshold, maxThreshold, dayOfMonth, daysThreshold = GetBCMonthThresholdV2(sourceEntityID, userID)
	}

	monthThreshold := minThreshold
	if returnMax {
		monthThreshold = maxThreshold
	}
	var toDate time.Time
	var fromDate time.Time
	if today.Day() <= dayOfMonth {
		// to date is last day of previous month
		toDate = today.AddDate(0, 0, -today.Day())
	} else {
		toDate = today.AddDate(0, 0, -today.Day()+dayOfMonth) // set as day of threshold
	}
	currentCount := 1
	currentDate := toDate.AddDate(0, 0, -toDate.Day()+1) // set as first of to date
	if daysThreshold != -1 {
		fromDate = toDate.AddDate(0, 0, -1*daysThreshold)
	} else {
		for currentCount <= monthThreshold {
			fromDate = currentDate
			currentDate = currentDate.AddDate(0, -1, 0)
			currentCount++
		}
		fromDate = fromDate.AddDate(0, 0, -fromDate.Day()+1) // set as first of month
	}

	return fromDate, toDate
}

// FetchActiveGSTFromPAN tells whether to fetch active GST from PAN
func FetchActiveGSTFromPAN(sourceEntityID string) bool {
	if general.InArr(sourceEntityID, []string{constants.IIFLBLID, constants.GimBooksBLID, constants.SwiggyID}) {
		return true
	}
	resp := IIFLAgg(sourceEntityID, false)
	return resp.IsAgg && resp.LoanType == constants.LoanTypeBusinessLoan
}

// ShowGSTConnect whether to show GST Connect module or not
func ShowGSTConnect(sourceEntityID string) bool {
	return false
}

// ShowBankConnect tells whether to show BankConnect module or not
func ShowBankConnect(sourceEntityID string) bool {
	return !(general.InArr(sourceEntityID, []string{constants.DukaanPeID, constants.BeldaraID, constants.ClearTaxID, constants.BigBasketID, constants.AquaConnectID, constants.IppoPayID, constants.PorterID, constants.JMFinancialID, constants.SupremeSolarID, constants.HousingID}) || IsMintifiAsLender(sourceEntityID))
}

// CheckLocationPincode returns whether to check for location based pincode check
// in block list
func CheckLocationPincode(sourceEntityID string) bool {
	return sourceEntityID == constants.GimBooksID
}

// CheckPlatformPincode returns whether to check in platform block list
func CheckPlatformPincode(sourceEntityID string) bool {
	return sourceEntityID == constants.GimBooksID
}

// GetPredictorsVersion returns DC Predictors version to use and fetchFIS
func GetPredictorsVersion(sourceEntityID, sdkVersion string) (string, bool) {
	parentSourcingEntityID := sourceEntityID
	GetParentSourceEntityID("", &parentSourcingEntityID)

	switch parentSourcingEntityID {
	case constants.TataPLID, constants.TataBNPLID:
		return "6", false
	case constants.UrbanCompanyID:
		return "4", false
	case constants.KhatabookID:
		return "10", true
	case constants.PoonawallaFincorpID:
		if sdkVersion == constants.SDKVersionWeb {
			return constants.SDKVersionWeb, false
		}
		return "8", false
	default:
		return "5", true
	}
}

// GenerateLoanApplicationNo returns the loan application no
// TODO: move to loan application models package later
func GenerateLoanApplicationNo(sourceEntityID string) string {
	var initial string
	query := "SELECT initial from source_entity where source_entity_id = $1"
	_ = database.Get(&initial, query, sourceEntityID)
	if initial != "" {
		return "FB" + initial + (strconv.Itoa(int(time.Now().UnixNano() / 1000))[2:])
	}
	return "FB" + strconv.Itoa(int(time.Now().UnixNano()/1000))
}

// ShowHelp returns whether to show help icon or not
func ShowHelp(sourceEntityID string) bool {
	return !(general.InArr(sourceEntityID, []string{constants.ClearTaxID}) || IsPFLSourcing(sourceEntityID))
}

// WaitForPhysicalNachCompletion returns whether to wait for physical nach
func WaitForPhysicalNachCompletion(sourceEntityID string, loanAmount float64) bool {
	return true
}

// ShowPhysicalMandate shows physical mandate option for users
func ShowPhysicalMandate(sourceEntityID string) bool {
	if IsMintifiAsLender(sourceEntityID) || IsMuthootCLPartner(sourceEntityID) || general.InArr(sourceEntityID, []string{constants.ArzoooID, constants.PlantixID, constants.IIFLID, constants.IIFLBLID, constants.KhatabookID, constants.VyaparID, constants.NiyoBLID, constants.CityMallID, constants.AgrostarID, constants.TataNexarcID, constants.IndiaMARTID, constants.FloBizID, constants.JMFinancialID, constants.SwiggyID}) {
		return true
	}
	resp := IIFLAgg(sourceEntityID, false)
	return resp.IsAgg
}

// ShowUPIMandate shows UPI mandate option for users
func ShowUPIMandate(sourceEntityID string, emi float64, upiLimit float64, attemptCount int, userID string) bool {
	if emi > 0 && IsMFLBLSourcing(sourceEntityID) && IsEDIJourney(userID, sourceEntityID) {
		return true
	}

	if emi > 0 && emi <= upiLimit && attemptCount >= EMandateFailureThresholdForUPIAutopay {
		if IsIIFLBLSourcing(sourceEntityID) || IsIIFLPLSourcing(sourceEntityID) {
			return true
		}
		if clientEnabled, _ := UPIAutoPayRolloutPerc(sourceEntityID); clientEnabled {
			return featureflag.Get(userID, FlagUPIAutoPay)
		}
		return false
	}
	return false
}

// GetMinAttemptsForPhysicalMandate returns attempt count >= which we need to show
// physical mandate, if e-mandate is supported by bank
func GetMinAttemptsForPhysicalMandate(sourceEntityID string) int {
	if IsMintifiAsLender(sourceEntityID) || IsMuthootCLPartner(sourceEntityID) {
		return 3
	}
	return 2
}

// AllowOnlyENachAccounts returns if for specific sourcing entities e-nach only check needs to be added
func AllowOnlyENachAccounts(sourceEntityID string) bool {
	return !ShowPhysicalMandate(sourceEntityID) && ShowAutoPay(sourceEntityID)
}

// CheckForDailyApprovalLimiter adds a daily limiter for daily approvals
func CheckForDailyApprovalLimiter(sourceEntityID string) (bool, int) {
	// if sourceEntityID == constants.GimBooksID {
	// 	return true, 50
	// }
	return false, 100
}

// IsHCINFlow checks whether flow is for HCIN
func IsHCINFlow(sourceEntityID string) bool {
	return sourceEntityID == constants.TestingAccount
}
func AllowMobileEdit(sourceEntityID string) bool {
	return general.InArr(sourceEntityID, []string{constants.BigBasketID, constants.AgrostarID})
}

// SendCustomerMail checks whether to send customer an email
func SendCustomerMail(sourceEntityID string) bool {
	return !IsHCINFlow(sourceEntityID) && !IsSaraloanAsLender(sourceEntityID) && !(IsPFLSourcing(sourceEntityID)) && !(IsMuthootCLPartner(sourceEntityID)) && !general.InArr(sourceEntityID, []string{constants.TataBNPLID, constants.TataPLID})
}

func SendSMSPostAgreement(sourceEntityID string) bool {
	return IsMuthootCLPartner(sourceEntityID) || sourceEntityID == constants.KhatabookID
}

func SendSMSOnDisqualification(sourceEntityID string) bool {
	return IsMuthootCLPartner(sourceEntityID)
}

// ExtraCustomerAttachment returns extra attachment to be made with welcome email
// returns file name and URL
func ExtraCustomerAttachment(sourceEntityID string) []emaillib.EmailAttachment {
	if sourceEntityID == constants.TataPLID {
		return []emaillib.EmailAttachment{{
			Path:     "https://finbox-cdn.s3.ap-south-1.amazonaws.com/Tdl/TDL_Privacy_Policy.pdf",
			FileName: "Tata_Digital_Privacy_Policy.pdf",
		}}
	}
	return nil
}

// DisableAddressPhoto disables options in address proof, and enable only E-KYC
func DisableAddressPhoto(sourceEntityID string) bool {
	return IsHCINFlow(sourceEntityID)
}

// ShowBCSkip tells whether to show users option to skip bank connect
func ShowBCSkip(userID, sourceEntityID, offerType string) bool {
	if general.InArr(sourceEntityID, []string{constants.AgrostarID}) {
		return true
	}
	if IsMultiBankApplicable(userID, sourceEntityID) && offerType == constants.OfferTypeBooster {
		return true
	}
	return false
}

// ShowCalc tells whether to show calculator
func ShowCalc(sourceEntityID string) bool {
	if sourceEntityID == constants.GeniusID || IsHCINFlow(sourceEntityID) {
		return false
	}
	loanType, _, _ := GetLoanType(sourceEntityID)
	if loanType == constants.LoanTypeOverDraft || loanType == constants.LoanTypeCreditLine {
		return false
	}
	return true
}

// AllowDashboardBCSkip tells whether to allow bank connect skip option in anchor platform dashboard
func AllowDashboardBCSkip(sourceEntityID string) bool {
	if conf.ENV != conf.ENV_PROD && general.InArr(sourceEntityID, []string{constants.SolvID, constants.HousingID}) {
		return true
	}
	return general.InArr(sourceEntityID, []string{constants.IIFLID, constants.IIFLBLID, constants.ODTestID})
}

// GetExotelCallerID returns exotel caller ID
func GetExotelCallerID(sourceEntityID string) string {
	switch sourceEntityID {
	case constants.GimBooksID:
		return "***********"
	case constants.PlantixID:
		return "***********"
	case constants.ArzoooID:
		return "***********"
	}
	return "***********"
}

// CheckForExportEmail returns whether to allow personal emails in export option in anchor platform dashboard
func CheckForExportEmail(sourceEntityID string) bool {
	return sourceEntityID != constants.GimBooksID
}

// IsAadhaarMaskRequired returns whether to automatically mask and store Aadhaar or not. Important for lenders who want Aadhaar images unmasked
func IsAadhaarMaskRequired(sourceEntityID string) bool {
	switch sourceEntityID {
	case constants.LetsTransportID, constants.ExpressStoresID, constants.ShopKiranaID, constants.BeldaraID:
		return false
	case constants.ArzoooID, constants.CityMallID:
		return false
	case constants.CashifyID:
		return false
	default:
		return true
	}
}

// ShowStatementsPlatform indicates whether to show statements download in anchor platform or not
func ShowStatementsPlatform(sourceEntityID string) bool {
	if general.InArr(sourceEntityID, []string{constants.IIFLBLID, constants.IIFLID, constants.VyaparID, constants.TataNexarcID, constants.TataPLID, constants.TataBNPLID, constants.ABFLMarketplaceID}) {
		return true
	} else if IsOneMuthootPartner(sourceEntityID) || IsMuthootCLPartner(sourceEntityID) {
		return true
	} else if IsABFLBLSourcing(sourceEntityID) {
		return true
	}
	resp := IIFLAgg(sourceEntityID, false)
	return resp.IsAgg
}

// GetRejectionReasonCategory return category for rejection reason
func GetRejectionReasonCategory(rejectionReason string) string {
	rejectionReason = strings.ToLower(rejectionReason)
	var rejectionReasonCategory string
	switch {
	case strings.Contains(rejectionReason, "inward cheque / emi return"):
		rejectionReasonCategory = "Cheque and other Bounces"
	case strings.Contains(rejectionReason, "dpd"):
		rejectionReasonCategory = "DPD on Recent Loans"
	case strings.Contains(rejectionReason, "inquiries in last 90 days") || strings.Contains(rejectionReason, "total enquiry on unsecured loans") || strings.Contains(rejectionReason, "enquiries in last 90 days"):
		rejectionReasonCategory = "High Loan Enquiries"
	case strings.Contains(rejectionReason, "abb") || strings.Contains(rejectionReason, "calculated income") || strings.Contains(rejectionReason, "avg balance"):
		rejectionReasonCategory = "Insufficient Calculated Income"
	case strings.Contains(rejectionReason, "mca") || strings.Contains(rejectionReason, "calculated max credit amount"):
		rejectionReasonCategory = "Insufficient Loan Amount Calculated"
	case strings.Contains(rejectionReason, "no individual/joint written off/suit filed/ wilful default accounts") || strings.Contains(rejectionReason, "written-off/settled in bureau"):
		rejectionReasonCategory = "Loans Defaulted/Written Off"
	case strings.Contains(rejectionReason, "credit") || strings.Contains(rejectionReason, "debit"):
		rejectionReasonCategory = "Minimum Number of Banking Transactions not sufficient"
	case strings.Contains(rejectionReason, "cibil") || strings.Contains(rejectionReason, "experian") || strings.Contains(rejectionReason, "score_xsell") || strings.Contains(rejectionReason, "ntc") || strings.Contains(rejectionReason, "bureau"):
		rejectionReasonCategory = "Poor Bureau"
	case strings.Contains(rejectionReason, "risk bucket"):
		rejectionReasonCategory = "Risk Bucket is High"
	case strings.Contains(rejectionReason, "active accounts"):
		rejectionReasonCategory = "High Active loan accounts"
	case strings.Contains(rejectionReason, "atleast one loan"):
		rejectionReasonCategory = "No previous loan taken"
	case strings.Contains(rejectionReason, "new accounts opened"):
		rejectionReasonCategory = "High loan accounts opened recently"
	case strings.Contains(rejectionReason, "pincode"):
		rejectionReasonCategory = "Unserviceable Pincode"
	case strings.Contains(rejectionReason, "pan data"):
		rejectionReasonCategory = "Duplicate Application"
	case strings.Contains(rejectionReason, "iifl brm"):
		rejectionReasonCategory = "IIFL BRM rejected"
	case strings.Contains(rejectionReason, "gstin"):
		rejectionReasonCategory = "Atleast one GSTIN available which is active, tax payer type = regular, min 2 year reg. vintage"
	case strings.Contains(rejectionReason, "vintage"):
		rejectionReasonCategory = "Less business vintage"
	case strings.Contains(rejectionReason, "banking balance"):
		rejectionReasonCategory = "Insufficient banking balance"
	case strings.Contains(rejectionReason, "age"):
		rejectionReasonCategory = "Age criteria not met"
	case strings.Contains(rejectionReason, "device id fraud"):
		rejectionReasonCategory = "Device Fraud"
	case strings.Contains(rejectionReason, "fraudcheck failed"):
		rejectionReasonCategory = "PAN fraudcheck failed"
	case strings.Contains(rejectionReason, "fraud"):
		rejectionReasonCategory = "Bank Statement PDF Modified"
	case strings.Contains(rejectionReason, "constitution is not proprietorship"):
		rejectionReasonCategory = "Constitution is not Proprietorship"
	case strings.Contains(rejectionReason, "number of customers") || strings.Contains(rejectionReason, "number of suppliers"):
		rejectionReasonCategory = "GST Rule - Customer/Supplier Count Insufficient"
	case strings.Contains(rejectionReason, "3m turnover growth"):
		rejectionReasonCategory = "Latest 3 months turnover growth not sufficient"
	case strings.Contains(rejectionReason, "salary credits"):
		rejectionReasonCategory = "Salary Credits not visible"
	case strings.Contains(rejectionReason, "duplicate email"):
		rejectionReasonCategory = "Duplicate email match with an active application/loan"
	case strings.Contains(rejectionReason, "duplicate mobile"):
		rejectionReasonCategory = "Duplicate mobile match with an active application/loan"
	case strings.Contains(rejectionReason, "duplicate android"):
		rejectionReasonCategory = "Duplicate android id match with an active application/loan"
	case strings.Contains(rejectionReason, "trust checker"):
		rejectionReasonCategory = "Rejected by Trust Checker"
	case strings.Contains(rejectionReason, "hunter"):
		rejectionReasonCategory = "Hunter match found"
	case strings.Contains(rejectionReason, constants.PANMobileDedupeDesc):
		rejectionReasonCategory = constants.PANMobileDedupeDesc
	case strings.Contains(rejectionReason, constants.PANMobileDedupeActiveDesc):
		rejectionReasonCategory = constants.PANMobileDedupeActiveDesc
	case strings.Contains(rejectionReason, constants.PANMobileDedupeRejectedDesc):
		rejectionReasonCategory = constants.PANMobileDedupeRejectedDesc
	case strings.Contains(rejectionReason, "pan verification velocity check"):
		rejectionReasonCategory = "PAN Verification velocity checks failed"
	case strings.Contains(rejectionReason, " with ProspectID"):
		rejectionReasonCategory = constants.PANMobileDedupeDesc
	case strings.Contains(rejectionReason, "duplicate pan") || strings.Contains(rejectionReason, "pan already exists") || strings.Contains(rejectionReason, "same PAN"):
		rejectionReasonCategory = "Duplicate Application"
	case strings.Contains(rejectionReason, "lead exists"):
		rejectionReasonCategory = "Duplicate Application"
	case strings.Contains(rejectionReason, "lender rejected"):
		rejectionReasonCategory = "Rejected by Lender"
	case strings.Contains(rejectionReason, "wferr001"):
		rejectionReasonCategory = "Credit Workflow Rejection"
	case strings.Contains(rejectionReason, "banking sufficiency"):
		rejectionReasonCategory = "Offer not generated"
	case strings.Contains(rejectionReason, "number of enquiries"):
		rejectionReasonCategory = "Enquiry criteria not met"
	case strings.Contains(rejectionReason, "SOP Policy"):
		rejectionReasonCategory = "SOP Norms criteria not met"
	default:
		rejectionReasonCategory = "Other"
	}
	return rejectionReasonCategory
}

// ShowEDIValues enables EDI related flows and APIs
func ShowEDIValues(sourceEntityID string) bool {
	return general.InArr(sourceEntityID, []string{constants.KhatabookID})
}

func IIFLPLPolicy(sourceEntityID string) bool {
	if sourceEntityID == constants.IIFLID {
		return true
	}
	aggValue := IIFLAgg(sourceEntityID, false)
	return aggValue.IsAgg && aggValue.LoanType == constants.LoanTypePersonalLoan
}

func IIFLBLPolicy(userID, sourceEntityID string) bool {
	if general.InArr(sourceEntityID, []string{constants.IIFLBLID, constants.GimBooksBLID, constants.NiyoBLID, constants.IndiaMARTID, constants.SwiggyID}) {
		return true
	}
	if sourceEntityID == constants.VyaparID && preselectedlender.Get(userID) == constants.IIFLID {
		return true
	}
	aggValue := IIFLAgg(sourceEntityID, false)
	return aggValue.IsAgg && aggValue.LoanType == constants.LoanTypeBusinessLoan
}

// AllowDCAPI enables /deviceDetails API to fetch predictors via server-to-server call
// and send additional webhook whenever device connect data fetch is complete
func AllowDCAPI(sourceEntityID string) bool {
	return sourceEntityID == constants.KhatabookID
}

// HideGSTKYCforActiveGSTIN returns true if business registration proof is not to shown to users in kyc when active gstin is present.
// Always upload gst certificate for these cases using gst json
func HideGSTKYCforActiveGSTIN(sourceEntityID string) bool {
	return general.InArr(sourceEntityID, []string{constants.ArzoooID})
}

// HideBusinessProofBasedOnLoanAmount returns true with <= loan amount limit if business registration proof is not shown to users
func HideBusinessProofBasedOnLoanAmount(sourceEntityID string) (bool, float64) {
	if general.InArr(sourceEntityID, []string{constants.VyaparID}) {
		return true, 1_00_000
	}
	return false, 0
}

// GiveLenderPowers gives underwriting view and loan offer edit from dashboard functionality enabled for anchor platform
func GiveLenderPowers(sourceEntityID string) bool {
	return sourceEntityID == constants.KhatabookID
}

// ShowPANInKYC hides PAN upload in KYC manual flow (post digilocker)
func ShowPANInKYC(sourceEntityID string) bool {
	return !(general.InArr(sourceEntityID, []string{}))
}

// ShowCoApplicantInKYC decides to show co applicant details in KYC or not
func ShowCoApplicantInKYC(sourceEntityID string) bool {
	return sourceEntityID == constants.TataNexarcID
}

// CheckUnderReview checks for a condition and returns whether to send to manual bucket or not
func CheckUnderReview(sourceEntityID, reviewItem string) bool {
	return constants.UnderReviewItems[sourceEntityID][reviewItem]
}

// CheckUnderReviewRule checks for a rule item for a source entity and returns whether to send to under_review or not
func CheckUnderReviewRule(sourceEntityID, ruleName string) bool {
	return constants.UnderReviewRules[sourceEntityID][ruleName]
}

// ShowWebLink tells whether to allow web link generation from dashboard for a user
func ShowWebLink(sourceEntityID string, userID string) bool {
	//only allow link generation in city mall if device data fetched
	if sourceEntityID == constants.CityMallID {
		var count int
		query := "select count(*) from device_connect_details where user_id = $1 and status = $2"
		err := database.Get(&count, query, userID, constants.DeviceConnectStatusCompleted)
		if err != nil {
			log.Println(err)
		}
		if count == 0 {
			return false
		}
	}

	return true
}

// AllowSingleTxn allow only one transaction to be allowed at a time for platform for OD / CL products
// it returns available limit as 0
func AllowSingleTxn(sourceEntityID string) bool {
	return general.InArr(sourceEntityID, []string{constants.CashifyID, constants.PagarBookID})
}

// ShowResidenceInKYC tells whether to ask for residence type in frontend
func ShowResidenceInKYC(sourceEntityID, lenderID string) bool {
	return sourceEntityID != constants.KhatabookID && lenderID != constants.DMIID
}

// BlockNewTxn blocks all new transactions from happening. Sets available limit to 0, and disallows new transaction confirmations
func BlockNewTxn(sourceEntityID string) bool {
	return BlockJourneys(sourceEntityID)
}

// BlockJourneys blocks all new journeys (except repayments) to continue
func BlockJourneys(sourceEntityID string) bool {
	return general.InArr(sourceEntityID, []string{constants.ArzoooID, constants.GimBooksBLID, constants.GimBooksID,
		constants.NiyoBLID, constants.NiyoID, constants.UrbanCompanyID, constants.ShopKiranaCLID, constants.ShopKiranaID,
		constants.CityMallID, constants.PagarBookID})
}

// EnableVirtualAccount enables virtual account repayment feature for the platform
func EnableVirtualAccount(sourceEntityID string) bool {
	return general.InArr(sourceEntityID, []string{constants.AgrostarID})
}

// DoCIBILHardPull specifies whether to do hard pull for a customer
func DoCIBILHardPull(sourceEntityID string, userID string) bool {
	if sourceEntityID == constants.VyaparID {
		type scoreStruct struct {
			CibilScore    int
			ExperianScore int
		}
		var scoreStructObj scoreStruct
		query := `select coalesce(cibil_score, -1) as cibilscore , coalesce(experian_score, -1) as experianscore
				  from bureau_score where user_id = $1 order by experian_updated_at, cibil_updated_at desc limit 1`
		err := database.Get(&scoreStructObj, query, userID)

		if err != nil {
			scoreStructObj.ExperianScore = -1
			scoreStructObj.CibilScore = -1
			logger.WithUser(userID).Error(err)
		}
		if scoreStructObj.ExperianScore == -1 && scoreStructObj.CibilScore == -1 {
			return true
		} else if scoreStructObj.CibilScore >= 700 && scoreStructObj.ExperianScore >= 730 {
			return true
		} else if scoreStructObj.ExperianScore == -1 && scoreStructObj.CibilScore >= 700 {
			return true
		} else if scoreStructObj.CibilScore == -1 && scoreStructObj.ExperianScore >= 720 {
			return true
		}
	}
	return sourceEntityID != constants.VyaparID && (IIFLBLPolicy(userID, sourceEntityID) || IsNexarcIIFL(userID, sourceEntityID))
}

// AutoActivate tells to auto activate the CL / OD limit if e-kyc and gstin available
func AutoActivate(sourceEntityID string) bool {
	return false
}

// SettleAllEMIsFromNACH indicates we can auto-settle next EMI portions in case of NACH success, not moving them to excess funds
func SettleAllEMIsFromNACH(sourceEntityID string) bool {
	return sourceEntityID == constants.KhatabookID
}

// AllowSDKKYCUpload allow non-capture browse and upload feature in post digilocker manual mode of KYC
// it returns whether this config is valid and the config value
func AllowSDKKYCUpload(sourceEntityID, docType string) (bool, bool) {
	var valid, value bool
	if general.InArr(sourceEntityID, []string{constants.GPayIIFLBLID, constants.GPayIIFLPLID}) && docType == constants.DocTypeBusinessProof {
		valid = true
		value = false
	} else if general.InArr(sourceEntityID, []string{constants.ClearTaxID, constants.TataNexarcID}) {
		valid = true
		value = true
	}
	return valid, value
}

// ShowInsurance enables insurance for a platform
func ShowInsurance(userID, sourceEntityID, lenderID string) bool {
	// TODO: add ABFLID
	if general.InArr(sourceEntityID, []string{constants.IIFLID, constants.IIFLBLID, constants.TataNexarcID}) {
		return true
	}

	if IsPFLSourcing(sourceEntityID) {
		return true
	}

	if IsSuperMoneyJourney(userID, sourceEntityID) {
		return true
	}
	if general.InArr(lenderID, []string{constants.ABFLID, constants.ABFLPLID}) {
		return true
	}
	if sourceEntityID == constants.VyaparID && preselectedlender.Get(userID) == constants.IIFLID {
		return true
	}
	resp := IIFLAgg(sourceEntityID, false)
	return resp.IsAgg
}

// AllowThirdGender enables third gender to be visible in frontend in gender selection
func AllowThirdGender(sourceEntityID string) bool {
	// currently it is not allowed for hcin, iifl pl, bl and it's partners
	if IsHCINFlow(sourceEntityID) || general.InArr(sourceEntityID, []string{constants.IIFLID, constants.IIFLBLID}) {
		return false
	}
	resp := IIFLAgg(sourceEntityID, false)
	return !resp.IsAgg
}

func HideBureau(userID, sourceEntityID string) bool {
	if general.InArr(sourceEntityID, []string{constants.AgrostarID, constants.OneMuthootID, constants.HousingID, constants.MoneyControlID}) || IsMintifiAsLender(sourceEntityID) || IsPFLSourcing(sourceEntityID) {
		return true
	}
	// CAUTION: If new lenders are added where bureau flow is via FinBox below condition needs to updated
	if sourceEntityID == constants.TataPLID {
		activeLenders := GetActiveLenders(constants.TataPLID, "")
		if !general.InArr(constants.IIFLID, activeLenders) {
			return true
		}
	}
	return false
}

func IsSaraloanAsLender(sourceEntityID string) bool {
	return general.InArr(sourceEntityID, []string{constants.AgrostarID})
}

func IsTataCapitalAsLender(sourceEntityID string) bool {
	return general.InArr(sourceEntityID, []string{constants.TataPLID})
}

func IsMintifiAsLender(sourceEntityID string) bool {
	return general.InArr(sourceEntityID, []string{constants.CostboID, constants.Lab2LandID, constants.VyaparCLID, constants.PlantixID})
}

func HideCreditlineTxns(lenderID string) bool {
	return general.InArr(lenderID, []string{constants.MintifiID})
}

func PODCompulsory(sourceEntityID string) bool {
	return !general.InArr(sourceEntityID, []string{constants.AgrostarID})
}

func IsKotakAsLender(sourceEntityID string) bool {
	return general.InArr(sourceEntityID, []string{constants.BigBasketID})
}

func WithdrawAPIAccess(sourceEntityID string) bool {
	return IsMintifiAsLender(sourceEntityID) || IsMuthootCLPartner(sourceEntityID) || general.InArr(sourceEntityID, []string{constants.PagarBookID, constants.CityMallID, constants.TataPLID, constants.TataBNPLID, constants.AgrostarID})
}

func IsHousingLoanTap(userID string, sourceEntityID string) bool {
	if sourceEntityID != constants.HousingID {
		return false
	}
	lender := preselectedlender.Get(userID)
	return lender == constants.LoanTapID
}

// MoveToReviewIfPhoneMismatch checks if for a valid source entity a user
// with last experian report entry failed with phone mismatch and needs to be put under review.
func MoveToReviewIfPhoneMismatch(userID, sourceEntityID string) bool {
	return false
}

func SkipLoanFormForNewApp(sourceEntityID string) bool {
	return general.InArr(sourceEntityID, []string{constants.KhatabookID})
}

func SkipKYCForNewApp(sourceEntityID string) bool {
	return general.InArr(sourceEntityID, []string{constants.KhatabookID})
}

func SkipNachForNewApp(sourceEntityID string) bool {
	return general.InArr(sourceEntityID, []string{constants.KhatabookID})
}

func MatchBCPennydrop(sourceEntityID string) bool {
	return general.InArr(sourceEntityID, []string{constants.VyaparID, constants.AgrostarID, constants.TataNexarcID, constants.IndiaMARTID})
}

func CallArthanAPI(sourceEntityID string) bool {
	return general.InArr(sourceEntityID, []string{constants.KhatabookID})
}

func ShowSendAgreementToLender(sourceEntityID string) bool {
	return general.InArr(sourceEntityID, []string{constants.KhatabookID})
}

// DisqualifyRevertLimits returns the limits minAmount, maxAmount, minTenure, maxTenure for a source entity
func DisqualifyRevertLimits(sourceEntityID string) (int, int, int, int) {
	if sourceEntityID == constants.KhatabookID {
		return 5_000, 1_00_000, 3, 12
	}
	return -1, -1, -1, -1
}

// GetPartnerCode returns transformed partner code for use in rule engine / underwriting policy settings
// TODO: to be deprecated
// CAUTION: to be kept in sync with functions/underwriting/helpers.transformPartnerCode
func GetPartnerCode(userID string) (string, string) {
	type partnerStruct struct {
		PartnerCode             string
		SourceEntityName        string
		SourceEntityID          string
		SourceEntityPartnerCode string
	}
	partnerObj := partnerStruct{}
	query := `SELECT coalesce(u.partner_code, '') as partnercode, s.source_entity_name as sourceentityname,
				u.source_entity_id as sourceentityid, s.partner_code as sourceentitypartnercode
				FROM users u JOIN source_entity s ON s.source_entity_id = u.source_entity_id
				WHERE u.user_id = $1`
	err := database.Get(&partnerObj, query, userID)
	if err != nil {
		log.Println(err)
	}

	if IsProgramApplicable(partnerObj.SourceEntityID) {
		partnerObj.PartnerCode = constants.TDLBREPartnerCode
	} else if partnerObj.SourceEntityID == constants.TataNexarcID {
		partnerObj.PartnerCode = constants.NexarcBREPartnerCode
	} else if partnerObj.SourceEntityID == constants.VyaparID {
		partnerObj.PartnerCode = constants.VyaparBREPartnerCode
	} else if IsMuthootCLPartner(partnerObj.SourceEntityID) {
		partnerObj.PartnerCode = partnerObj.SourceEntityPartnerCode
	} else {
		switch partnerObj.SourceEntityID {
		case constants.IIFLID:
			partnerObj.PartnerCode = constants.IIFLPartnerCode
		case constants.IIFLBLID:
			partnerObj.PartnerCode = constants.IIFLBLPartnerCode
		default:
			resp := IIFLAgg(partnerObj.SourceEntityID, false)
			if resp.IsAgg {
				partnerObj.PartnerCode = resp.PartnerCode
			}
		}
	}
	return partnerObj.PartnerCode, partnerObj.SourceEntityName
}

func GetMultiEsignURLDescription(sourceEnityID string) (description string) {

	if IsABFLBLSourcing(sourceEnityID) {
		description = "To proceed, all selected applicants, needs to sign the agreement"

	}

	return description

}

func GetESignMode(lenderID string) string {
	// Append lenders here to enable leegality / link based flow
	if general.InArr(lenderID, []string{constants.MintifiID, constants.MCSLID, constants.PoonawallaFincorpID, constants.LoanTapID, constants.MuthootCLID, constants.ABFLID}) {
		return constants.ESignModeSignURL
	}

	return constants.ESignModeOTP
}

func PostOfferAPIAccess(sourceEntityID string) bool {
	return general.InArr(sourceEntityID, []string{constants.KhatabookID})
}

func RenewalFlow(sourceEntityID string) bool {
	return general.InArr(sourceEntityID, []string{constants.KhatabookID})
}

func CheckDuplicateMailForRejection(sourceEntityID string) bool {
	return !general.InArr(sourceEntityID, []string{constants.KhatabookID})
}

func AllowReverseDisbursedTxn(sourceEntityID string) bool {
	return IsSaraloanAsLender(sourceEntityID)
}

func GetLoanRejectionDescription(sourceEntityID string) string {
	if sourceEntityID == constants.TataNexarcID {
		return "Your loan application did not meet our approval criteria at the moment"
	} else if IsPFLSourcing(sourceEntityID) {
		return "We regret to inform you that we are unable to process your application as it does not meet our internal policy criteria. Please note this in no way is indicative of your creditworthiness."
	} else if IsABFLBLSourcing(sourceEntityID) {
		// TODO: confirm text
		return "We regret to inform you that we are unable to process your application as it does not meet our internal policy criteria. Please note this in no way is indicative of your creditworthiness."
	} else if general.InArr(sourceEntityID, []string{constants.HousingID, constants.MoneyControlID}) {
		return "We regret to inform you that we are unable to process your application as it does not meet our internal policy criteria. Please note the reason for the decline of your application is a business decision and is based on multiple parameters and certainly in no way, is indicative of your creditworthiness."
	} else if sourceEntityID == constants.ABFLMarketplaceID {
		return "We regret to inform you that we are unable to proceed with your application at this time. Please understand that this decision is not a reflection of your creditworthiness."
	} else if sourceEntityID == constants.SentinelTestID {
		return "Please start again"
	}

	return "We regret to inform you that we are unable to process your application as it does not meet our internal policy criteria. Please note the reason for the decline of your application is a business decision and is based on multiple parameters and certainly in no way, is indicative of your creditworthiness. Please try again after 60 days."
}

func LoanRejectionHelperText(sourceEntityID string) string {
	if IsPFLSourcing(sourceEntityID) {
		return "Please try again with a fresh application after %.0f %s."
	} else if sourceEntityID == constants.SentinelTestID {
		return ""
	}
	return "You can still apply another loan with us in %.0f %s"
}

func GetIIFLSchemeCode(sourceEntityID string) string {
	resp := IIFLAgg(sourceEntityID, false)
	if resp.IsAgg {
		switch resp.LoanType {
		case constants.LoanTypePersonalLoan:
			sourceEntityID = constants.IIFLID
		case constants.LoanTypeBusinessLoan:
			sourceEntityID = constants.IIFLBLID
		}
	}

	if conf.ENV != conf.ENV_PROD {
		switch sourceEntityID {
		case constants.ArzoooID:
			return "85162"
		case constants.ODTestID:
			return "85160"
		case constants.IIFLBLID, constants.NiyoBLID:
			return "84040"
		case constants.VyaparID:
			return "85167"
		case constants.CashifyID:
			return "85176"
		case constants.KhatabookID:
			return "85194"
		case constants.TataNexarcID:
			return "84048"
		case constants.IndiaMARTID:
			return "84045"
		case constants.HousingID:
			return "89071"
		case constants.TataPLID:
			return "89093"
		default:
			return "89071"
		}
	}
	switch sourceEntityID {
	case constants.ShopKiranaID:
		return "85101"
	case constants.GimBooksID, constants.GimBooksBLID:
		return "85102"
	case constants.IIFLBLID:
		return "84040"
	case constants.ArzoooID:
		return "85100"
	case constants.VyaparID:
		return "84040" // TODO: change to vyapar back when we have scheme code
	case constants.NiyoBLID:
		return "84040" // TODO: change to niyo bl back when we have scheme code
	case constants.CashifyID:
		return "85110"
	case constants.KhatabookID:
		return "85120"
	case constants.TataNexarcID:
		return "84048"
	case constants.IndiaMARTID:
		return "84045"
	case constants.HousingID:
		return "89059"
	}
	return "89059"
}

func GetMinTenure(sourceEntityID, lenderID, loanType, subProductType string) int {
	minTenure := 3
	if loanType == constants.LoanTypeBusinessLoan {
		minTenure = 6
	}
	if minEligibleTenure, ok := constants.MinEligibleTenures[lenderID]; ok {
		minTenure = minEligibleTenure
	}
	if IsPFLSourcing(sourceEntityID) && subProductType == constants.PFLOfferTypePLIPL {
		minTenure = 3
	}
	return minTenure
}

func GetMinAmount(sourceEntityID, lenderID, loanType, subProductType string, eligibleAmount float64) float64 {
	var minAmount float64 = 5_000
	if loanType == constants.LoanTypeBusinessLoan {
		minAmount = 10_000
		if eligibleAmount != 0.0 && eligibleAmount > 5_00_000 {
			minAmount = 15_000
		}
	}
	if minEligibleAmount, ok := constants.MinEligibleAmounts[sourceEntityID]; ok {
		minAmount = minEligibleAmount
	}
	if IsPFLSourcing(sourceEntityID) && subProductType == constants.PFLOfferTypePLIPL {
		minAmount = 7_000
	}
	if eligibleAmount != 0.0 && minAmount > eligibleAmount {
		minAmount = eligibleAmount
	}
	return minAmount
}

func CheckWaitStateAfterNACH(sourceEntityID, lenderID string) bool {
	//return lenderID == constants.ArthanLenderID && sourceEntityID == constants.KhatabookID
	return true
}

func CallIIFLDecisionOnESign(sourceEntityID, lenderID string) bool {
	return lenderID == constants.IIFLID && sourceEntityID != constants.KhatabookID
}

func PushToLenderOnNACH(sourceEntityID, lenderID string) bool {
	switch lenderID {
	case constants.ArthanLenderID:
		return general.InArr(sourceEntityID, []string{constants.KhatabookID})
	case constants.IIFLID:
		return general.InArr(sourceEntityID, []string{constants.KhatabookID})
	}
	return false
}

func PushToLenderOnPenny(sourceEntityID, lenderID string) bool {

	switch lenderID {
	case constants.ArthanLenderID:
		return !general.InArr(sourceEntityID, []string{constants.KhatabookID}) && CallArthanAPI(sourceEntityID)
	case constants.IIFLID:
		return !general.InArr(sourceEntityID, []string{constants.KhatabookID})
	}
	return true
}

func AddPreEMIPostDisbursal(sourceEntityID, lenderID string) bool {
	return lenderID == constants.IIFLID && sourceEntityID == constants.KhatabookID
}

func GetStampPaperRequiredLeegality(userID, loanApplicationID string) (bool, error) {

	// type dbStruct struct {
	// 	SourceEntityID string
	// 	LenderID       string
	// }
	// var dbObj dbStruct
	// query := `SELECT source_entity_id as sourceentityid, lender_id as lenderid from loan_application
	// where user_id=$1 and loan_application_id=$2`
	// err := database.Get(&dbObj, query, userID, loanApplicationID)
	// if err != nil {
	// 	log.Println(err)
	// 	return false, err
	// }
	//append condition to return stamp paper flow
	// if GetESignMode(dbObj.LenderID) == constants.ESignModeSignURL {
	// 	return true, nil
	// }
	return false, nil
}

func HasMultiLender(sourceEntityID string) bool {
	return general.InArr(sourceEntityID, []string{constants.KhatabookID, constants.TataNexarcID, constants.TataPLID, constants.VyaparID, constants.HousingID, constants.MoneyControlID})
}

// useBCLenderCreds returns whether we should use lender creds to call bank connect APIs
// and default lender creds to be used (only to be used for DSA)
// TODO: remove this, and migrate all source entities to use lender creds for bank connect
func useBCLenderCreds(userID string, sourceEntityID string) (bool, string) {
	if general.InArr(sourceEntityID, []string{constants.OneMuthootID, constants.HousingID}) {
		return true, ""
	}
	if sourceEntityID == constants.VyaparID && preselectedlender.Get(userID) == constants.LendingKartID {
		return true, constants.LendingKartID
	}
	if IsABFLBLSourcing(sourceEntityID) {
		return true, constants.ABFLID
	}
	if IsMFLBLSourcing(sourceEntityID) {
		return true, constants.MFLBLID
	}
	return false, ""
}

// UseCustomBCKeys returns whether custom bank connect key is to be used & the key id
func UseCustomBCKeys(userID string, sourceEntityID string) (bool, string) {
	lenderID := ""
	if useLenderCreds, defaultLenderID := useBCLenderCreds(userID, sourceEntityID); useLenderCreds {
		if defaultLenderID != "" {
			lenderID = defaultLenderID
		} else {
			lenderID = preselectedlender.Get(userID)
			if lenderID == "" {
				err := fmt.Errorf("lender not found for user %s", userID)
				logger.WithUser(userID).Errorln("lender not found")
				errorHandler.ReportToSentryWithoutRequest(err)
				return false, ""
			}
		}
		return true, lenderID // return lenderID as the default keyID as keyID == lenderID in bank_connect_keys (ensured through migration)
	}
	if IsBankConnectSessionFlow(userID, sourceEntityID, false) {
		var keyID string
		var err error
		if category, defaultLenderID := GetBankConnectKeyCategory(userID, sourceEntityID); category != "" && defaultLenderID != "" {
			keyID, _, _, err = bankconnectkeys.GetByCategoryAndLender(defaultLenderID, category)
		} else {
			keyID, _, _, err = bankconnectkeys.Get(sourceEntityID, "", bankconnectkeys.FCUBankConnect)
		}
		if err != nil {
			err := fmt.Errorf("custom bank connect key not configured for userID: %s, sourceEntityID: %s, FCU: %s", userID, sourceEntityID, bankconnectkeys.FCUBankConnect)
			logger.WithUser(userID).Errorln(err)
			errorHandler.ReportToSentryWithoutRequest(err)
			return false, ""
		}
		return true, keyID
	}
	return false, ""
}

func GetBankConnectKeyCategory(userID string, sourceEntityID string) (string, string) {
	if IsMFLBLSourcing(sourceEntityID) {
		return constants.BankConnectKeyCategoryOrganic, constants.MFLBLID
	}
	//add condition for dsas.
	if IsPFLSourcing(sourceEntityID) || IsPFLEDUSourcing(sourceEntityID) {
		return constants.BankConnectKeyCategoryOrganic, constants.PoonawallaFincorpID
	}
	if general.InArr(sourceEntityID, []string{constants.IIFLID, constants.IIFLBLID}) {
		return constants.BankConnectKeyCategoryOrganic, constants.IIFLID
	}
	if IIFLAgg(sourceEntityID, false).IsAgg {
		return constants.BankConnectKeyCategoryDSA, constants.IIFLID
	}
	if preselectedlender.Get(userID) == constants.IIFLID {
		return constants.BankConnectKeyCategoryEFPartners, constants.IIFLID
	}
	if sourceEntityID == constants.MuthootCLID {
		return constants.BankConnectKeyCategoryOrganic, constants.MuthootCLID
	}
	if IsMuthootCLPartner(sourceEntityID) {
		return constants.BankConnectKeyCategoryDSA, constants.MuthootCLID
	}
	if IsMuthootEDIPartner(sourceEntityID) || IsMFLEMIPartner(sourceEntityID) {
		return constants.BankConnectKeyCategoryAPIPartners, constants.MuthootCLID
	}
	if sourceEntityID == constants.ABFLMarketplaceID {
		return constants.BankConnectKeyCategoryMarketplacePartners, constants.ABFLID
	}
	if sourceEntityID != constants.ABFLPLID && IsABFLPLSourcing(sourceEntityID) {
		return constants.BankConnectKeyCategoryDSA, constants.ABFLPLID
	}
	return "", ""
}

func AllowDisqualifyRevert(sourceEntityID, lenderID string) bool {
	switch lenderID {
	case constants.ArthanLenderID:
		return general.InArr(sourceEntityID, []string{constants.KhatabookID})
	case constants.IIFLID:
		return false
	default:
		return true
	}
}

func AllowLoanApproval(sourceEntityID, lenderID string) bool {
	switch lenderID {
	case constants.ArthanLenderID:
		return general.InArr(sourceEntityID, []string{constants.KhatabookID})
	case constants.IIFLID:
		return false
	default:
		return false
	}
}

func RetriggerIIFLAccess(sourceEntityID string) bool {
	return IsIIFLExclusiveLender(sourceEntityID) || general.InArr(sourceEntityID, []string{constants.KhatabookID})
}

func IsProgramApplicable(sourceEntityID string) bool {
	if general.InArr(sourceEntityID, []string{constants.TataBNPLID, constants.TataPLID}) {
		return true
	}
	if IsABFLBLSourcing(sourceEntityID) {
		return true
	}
	return false
}

func IsAgreementRBChangeReq(method string, lenderID string, sourceEntityID string) bool {
	if sourceEntityID != constants.KhatabookID {
		return false
	}
	return method == constants.MethodSimpleInterest && (lenderID == constants.IIFLID || lenderID == constants.ArthanLenderID)
}

func AllowSegment(segment, lenderID string, sourceEntityID string) bool {
	if sourceEntityID != constants.KhatabookID {
		return true
	}
	switch lenderID {
	case constants.WesternCapitalID:
		return general.InArr(segment, []string{""})
	case constants.IIFLID:
		return general.InArr(segment, []string{"", constants.KBUserSegmentLedger, constants.KBUserSegmentSkip})
	case constants.ArthanLenderID:
		return general.InArr(segment, []string{"", constants.KBUserSegmentRenewal})
	}
	return true
}

// GetActiveLenders returns the list of active lenders in order of priority
func GetActiveLenders(sourceEntityID string, segment string) []string {
	params := map[string]interface{}{
		"source_entity_id": sourceEntityID,
	}
	var lenders []string

	query := "select lender_id from platform_active_lenders where source_entity_id =:source_entity_id and active = true order by priority"
	namedQuery, err := database.PrepareNamed(query)
	if err != nil {
		log.Errorln(err)
	}

	err = namedQuery.Select(&lenders, params)
	if err != nil {
		log.Errorln(err)
	}

	res := []string{}
	for _, lender := range lenders {
		if AllowSegment(segment, lender, sourceEntityID) {
			res = append(res, lender)
		}
	}
	return res
}

// HidePoweredBy is used to control 'powered by FinBox' on the credit link page
func HidePoweredBy(sourceEntityID string) bool {
	return general.InArr(sourceEntityID, []string{constants.MoneyControlID, constants.PFLEducationLoanSEID}) ||
		IsIIFLSourcing(sourceEntityID) ||
		IsABFLBLSourcing(sourceEntityID) ||
		IsABFLPLSourcing(sourceEntityID) ||
		IsMFLBLSourcing(sourceEntityID) ||
		IsSuperMoneyJourney("", sourceEntityID) ||
		IsPFLSourcing(sourceEntityID) ||
		IsIncredSourcing(sourceEntityID) ||
		sourceEntityID == constants.SentinelTestID
}

func AllowIfBureauFailed(sourceEntityID string, userID string) bool {

	if general.InArr(sourceEntityID, []string{constants.TataNexarcID}) {
		type dbStruct struct {
			CibilStatus    string
			ExperianStatus string
		}
		query := `select coalesce(cibil_status, '') as cibilstatus, coalesce(experian_status, '') as experianstatus from bureau_score where user_id = $1
		          order by cibil_updated_at, experian_updated_at desc limit 1`
		var dbObj dbStruct
		err := database.Get(&dbObj, query, userID)
		if err != nil {
			logger.WithUser(userID).Error(err)
			return false
		}
		if general.InArr(dbObj.CibilStatus, []string{constants.BureauStatusFailed, constants.BureauStatusFailedBypassed}) && general.InArr(dbObj.ExperianStatus, []string{constants.BureauStatusFailed, ""}) {
			return true
		} else if general.InArr(dbObj.CibilStatus, []string{constants.BureauStatusFailed, constants.BureauStatusFailedBypassed, ""}) && dbObj.ExperianStatus == constants.BureauStatusFailed {
			return true
		}
	}
	return false
}

func IsHideSignature(sourceEntityID string) bool {
	return !IsMuthootCLPartner(sourceEntityID)
}

func AllowOfferUpdateAfterSign(sourceEntityID, lenderID string) bool {
	return !(general.InArr(sourceEntityID, []string{constants.KhatabookID}) || lenderID == constants.WesternCapitalID)
}

func ShowDownloadAuthLetter(sourceEntityID string) bool {
	return general.InArr(sourceEntityID, []string{constants.GeniusID, constants.LetsTransportID})
}

func AllowTagsOnLender(lenderID string, sourceEntityID string, userID string) bool {
	if lenderID != "" {
		return general.InArr(lenderID, []string{constants.IIFLID})
	}
	return IIFLPLPolicy(sourceEntityID) || IIFLBLPolicy(userID, sourceEntityID) || IsNexarcIIFL(userID, sourceEntityID)
}

func AddDualDOBToAgreement(lenderID string) bool {
	return !general.InArr(lenderID, []string{constants.WesternCapitalID})
}

func AddDualNameToAgreement(lenderID string) bool {
	return !general.InArr(lenderID, []string{constants.WesternCapitalID})
}

func BlockPaymentLink(sourceEntityID, lenderID string) bool {
	if sourceEntityID == constants.KhatabookID {
		return true
	}
	return !general.InArr(lenderID, []string{constants.IIFLID, constants.MuthootCLID})
}

func DoAutofillFromGSTViaPAN(userID, sourceEntityID string) bool {
	return IsABFLBLSourcing(sourceEntityID) || (IsMuthootCLPartner(sourceEntityID) && IsCurrentModuleEnabled(userID, sourceEntityID))
}

// ForceNewApplication returns whether minimum threshold days check for new application is to be ignored
func ForceNewApplication(sourceEntityID string) bool {
	return general.InArr(sourceEntityID, []string{constants.KhatabookID}) || IsMuthootEDIPartner(sourceEntityID) || (sourceEntityID == constants.SentinelTestID)
}

// OnlyAadhaarManual enforces use of only aadhaar when manual mode is being used
func OnlyAadhaarManual(sourceEntityID string) bool {
	if general.InArr(sourceEntityID, []string{constants.KhatabookID}) {
		return true
	}
	// generally when masking is not required its case of Arthan and only aadhaar manual will be required
	return !IsAadhaarMaskRequired(sourceEntityID)
}

// ShowEligibilityInUserExport returns whether to include user eligibility information in Users data dump
func ShowEligibilityInUserExport(sourceEntityID string) bool {
	return general.InArr(sourceEntityID, []string{constants.TataNexarcID, constants.VyaparID})
}

func IsNexarcIIFL(userID, sourceEntityID string) bool {
	return sourceEntityID == constants.TataNexarcID && GetNexarcLender(userID) == constants.IIFLID
}

func IsNexarcKotak(userID, sourceEntityID string) bool {
	return sourceEntityID == constants.TataNexarcID && GetNexarcLender(userID) == constants.KotakID
}

func IsNexarcTataCapital(userID, sourceEntityID string) bool {
	return sourceEntityID == constants.TataNexarcID && GetNexarcLender(userID) == constants.TataCapitalID
}

// GetNexarcLender return Nexarc's lender from preselected lender table
func GetNexarcLender(userID string) string {
	lenderID := ""
	if HasMultiLender(constants.TataNexarcID) {
		lenderID = preselectedlender.Get(userID)
	}
	return lenderID
}

func TakeDecisionFromSentinel(ruleVersion string) (bool, string) {
	_, runUnified := constants.UnifiedPolicies[ruleVersion]
	if runUnified {
		return false, constants.PolicyTypeDefault
	}
	_, runSentinelWorkflow := constants.SentinelWorkflowPolicies[ruleVersion]
	if runSentinelWorkflow {
		return true, constants.PolicyTypeWorkflow
	}
	return true, constants.PolicyTypeDefault
}

// CheckForPaymentsBeforeClosure check whether to check for payments before closure or not for a source entity
func CheckForPaymentsBeforeClosure(sourceEntityID string) bool {
	return general.InArr(sourceEntityID, []string{constants.KhatabookID})
}

func BypassBureauConsent(sourceEntityID string) bool {
	if general.InArr(sourceEntityID, []string{constants.TataPLID, constants.TataBNPLID, constants.HousingID, constants.MoneyControlID}) {
		return true
	}
	if IsPFLSourcing(sourceEntityID) {
		return true
	}
	if IsABFLBLSourcing(sourceEntityID) {
		return true
	}
	return false
}

// MockToSingleLender selects a lender if source entity supports dual offers
// TODO: Add real working logic
func MockToSingleLender(customerID, sourceEntityID, mobile string) (string, error) {
	if !general.InArr(sourceEntityID, []string{constants.TataBNPLID, constants.TataPLID}) {
		return "", errors.New("source entity does not support dual offer")
	}

	if conf.ENV != conf.ENV_PROD {
		axisMobileNumbers := []string{
			"**********",
			"**********",
			"************",
			"************",
			"************",
			"************",
			"************",
			"************",
			"************",
			"************",
			"************",
			"************",
			"**********",
			"**********",
			"**********",
			"**********",
		}

		// Hack for testing
		if strings.HasSuffix(customerID, "axis") || general.InArr(mobile, axisMobileNumbers) {
			return constants.AxisBankID, nil
		}
	}

	return constants.CapitalFloatID, nil
}

// GetEventsTimeLapse returns in seconds the difference in time between user eventB and user eventA.
// Assumption: Both events exist for the user
func GetEventsTimeLapse(userID, eventA, eventB string) (float64, error) {
	var eventObj []struct {
		EventType string
		LoggedAt  float64
	}
	query := `SELECT DISTINCT on(event_type) event_type as eventtype, extract(epoch from now() - logged_at) as loggedat
			FROM activity_log WHERE event_type IN ($1, $2)
		and user_id = $3 order by event_type, logged_at desc`

	err := database.Select(&eventObj, query, eventA, eventB, userID)
	if err != nil {
		log.Errorln(err)
		return 0, err
	}

	if len(eventObj) == 2 {
		return math.Abs(eventObj[0].LoggedAt - eventObj[1].LoggedAt), nil
	}
	return 0, errors.New("cannot find")
}

// SkipDaysForEDI returns if there are days needed to be skipped in edi collection
func SkipDaysForEDI(sourceEntityID, lenderID string, day time.Weekday) bool {
	switch sourceEntityID {
	case constants.KhatabookID:
		switch lenderID {
		case constants.ArthanLenderID:
			return false
		default:
			return general.InArr(day.String(), []string{time.Sunday.String()})
		}

	default:
		return false
	}
}

// TODO: SkipRepayment >> Caution >> Needs to be refactored
// SkipRepayment returns if a specific day needs to skipped for collection for that lender
func SkipRepayment(t time.Weekday, lenderID string) bool {
	switch t {
	case time.Saturday:
		return !general.InArr(lenderID, []string{constants.IIFLID, constants.ArthanLenderID, constants.PoonawallaFincorpID, constants.ABFLID, constants.ABFLPLID, constants.MFLBLID})
	case time.Sunday:
		return !general.InArr(lenderID, []string{constants.IIFLID, constants.ArthanLenderID, constants.PoonawallaFincorpID, constants.ABFLID, constants.ABFLPLID, constants.MFLBLID})
	default:
		return false
	}
}

// GetRuleChain returns the list of policies to be executed in order
// and a flag which tells whether there are any applicable policies
// NOTE: currently in use only for KB and multi offer flow
func GetRuleChain(sourceEntityID, lenderID, segment string) ([]string, bool) {
	var ruleChain []string
	isValid := true
	if !AllowSegment(segment, lenderID, sourceEntityID) {
		return ruleChain, isValid
	}
	switch lenderID {
	case constants.WesternCapitalID:
		ruleChain = []string{"westerncap_v1"}
	case constants.IIFLID:
		switch sourceEntityID {
		case constants.KhatabookID:
			ruleChain = []string{"khatabook_v1.9_a", "khatabook_v1.9_b"}
			switch segment {
			case constants.KBUserSegmentSkip:
				ruleChain = []string{"khatabook_qr_v2"}
			case constants.KBUserSegmentLedger:
				ruleChain = []string{"khatabook_ledger_v1"}
			}
		case constants.TataNexarcID:
			ruleChain = []string{constants.NexarcGatingPolicy}
		case constants.TataPLID:
			ruleChain = []string{"tdl_iifl_pl_compound_policy_v2"}
		}
	case constants.ArthanLenderID:
		ruleChain = []string{
			"khatabook_v1.8_0",
			"khatabook_v1.8_1",
		}
		if segment == constants.KBUserSegmentRenewal {
			ruleChain = []string{"khatabook_v1.8_0_r_v2",
				"khatabook_v1.8_1_r_v2",
			}
		}
	case constants.KotakID:
		if sourceEntityID == constants.TataNexarcID {
			return []string{"kotak_nexarc_bank_knock_off_v3"}, isValid
		}
	case constants.LendingKartID:
		if sourceEntityID == constants.TataNexarcID {
			return []string{"lending_kart_bank_knock_off_v1.3"}, isValid
		}
	case constants.TataCapitalID:
		if sourceEntityID == constants.TataNexarcID {
			return []string{"tcap_knock_off_v1"}, isValid
		}
		if sourceEntityID == constants.TataPLID {
			return []string{}, false
		}
	case constants.DMIID:
		if sourceEntityID == constants.TataPLID {
			isValid = false
		}
	case constants.ABFLID:
		if sourceEntityID == constants.TataNexarcID {
			return []string{"nexarc_abfl_knockoff_v1"}, isValid
		} else if sourceEntityID == constants.TataPLID {
			isValid = false
		}
	case constants.CasheID:
		if sourceEntityID == constants.TataPLID {
			isValid = false
		}
	case constants.MoneyViewID:
		if sourceEntityID == constants.TataPLID {
			isValid = false
		}
	case constants.TDLKreditBeeID:
		if sourceEntityID == constants.TataPLID {
			isValid = false
		}
	case constants.AxisBankID:
		if sourceEntityID == constants.TataPLID {
			isValid = false
		}
	case constants.PrefrID:
		if sourceEntityID == constants.TataPLID {
			isValid = false
		}
	case constants.KisshtID:
		if sourceEntityID == constants.TataPLID {
			isValid = false
		}
	case constants.HDFCLenderID:
		if sourceEntityID == constants.TataPLID {
			isValid = false
		}
	case constants.OndcFibeID:
		if sourceEntityID == constants.TataPLID {
			isValid = false
		}
	case constants.OndcBajajID:
		if sourceEntityID == constants.TataPLID {
			isValid = false
		}
	}
	return ruleChain, isValid
}

// GetMultiOfferBoostRuleChain gives rule version chain for boost in case of multi offer
func GetMultiOfferBoostRuleChain(sourceEntityID string, lenderID string, ignoreCustom bool, userID string) []string {
	switch lenderID {
	case constants.IIFLID:
		switch sourceEntityID {
		case constants.TataNexarcID:
			return constants.NexarcIIFLBankPolicies
		case constants.TataPLID:
			personalOffer := personalloanoffer.GetSelectedOffer(userID)
			switch personalOffer.OfferType {
			case constants.OfferTypeBooster:
				return []string{"iifl_tdl_salaried_bank_boost_v1.3", "iifl_tdl_self_employed_boost_v2.2", "iifl_tdl_self_employed_boost_v2.2_a", "iifl_tdl_self_employed_boost_v2.2_b", "iifl_bank_pl_creditcard_surrogate_v1.2", "iifl_pl_bank_hl_lap_v1"}
			default:
				return []string{"iifl_salaried_bank_v1.4.3", "iifl_self_employed_v2.4.4", "iifl_self_employed_v2.4.4_a", "iifl_self_employed_v2.4.4_b", "iifl_bank_pl_creditcard_surrogate_v1.2", "iifl_pl_bank_hl_lap_v1"}
			}
		}
	case constants.KotakID:
		if sourceEntityID == constants.TataNexarcID {
			return constants.NexarcKotakBankPolicies
		}
	case constants.TataCapitalID:
		if sourceEntityID == constants.TataNexarcID {
			return constants.NexarcTCapBankPolicies
		}
	case constants.LendingKartID:
		if sourceEntityID == constants.TataNexarcID {
			return []string{constants.LendingKartPolicy}
		}
	case constants.ABFLID:
		if sourceEntityID == constants.TataNexarcID {
			return []string{"nexarc_abfl_booster_v1"}
		}
	}
	return []string{}
}

// EnablePvtLtdJourney returns whether to enable pvt ltd journey for any client
// deprecated
func EnablePvtLtdJourney(sourceEntityID string) bool {
	return general.InArr(sourceEntityID, []string{constants.TataNexarcID})
}

func IsVyaparLendingKart(userID, sourceEntityID string) bool {
	return sourceEntityID == constants.VyaparID && preselectedlender.Get(userID) == constants.LendingKartID
}

func IsNexarcLendingKart(userID, sourceEntityID string) bool {
	return sourceEntityID == constants.TataNexarcID && GetNexarcLender(userID) == constants.LendingKartID
}

// IsPerfiosCase tells whether its a probable perfios case
func IsPerfiosCase(userID, sourceEntityID string) bool {
	return sourceEntityID == constants.TataNexarcID || IIFLPLPolicy(sourceEntityID) || IIFLBLPolicy(userID, sourceEntityID)
}

// DoAutoFillPincodeFromPAN tells whether to disable pincode autofill from PAN
func DoAutoFillPincodeFromPAN(sourceEntityID string) bool {
	return false // currently disabled for all as often pincode in PAN are very old, and pincode based rejection happen because of it
}

type KFSOtherDisclosurs struct {
	LockInPeriod     string
	CoolOffPeriod    string
	LSPAgent         string
	LSPLink          string
	RELink           string
	NodalOfficer     string
	LSPAgentCustomer string
}

func GetKFSOtherDisclosures(lenderID, sourceEntityID string, numEMIs int, loanType string) KFSOtherDisclosurs {
	var kfsOtherDisclosurs KFSOtherDisclosurs
	var extraNodalOfficerDetails string
	switch lenderID {
	case constants.ArthanLenderID:
		kfsOtherDisclosurs.CoolOffPeriod = "3 days"
		if numEMIs < 6 {
			kfsOtherDisclosurs.CoolOffPeriod = "1 days"
		}
		switch loanType {
		case constants.LoanTypeCreditLine, constants.LoanTypeOverDraft:
			kfsOtherDisclosurs.CoolOffPeriod = "three days for loans having tenor of seven days or more and one day for loans having tenor of less than seven days"
			kfsOtherDisclosurs.LSPAgent = "FinBox"
		}
		if sourceEntityID == constants.KhatabookID {
			kfsOtherDisclosurs.LSPAgent = "ADJ Utilities Private Limited (Khatabook)"
			kfsOtherDisclosurs.NodalOfficer += `
			Name - Mr. Yogesh Pawde<br/>
			Email ID - <EMAIL><br/>
			Nodal Grievance Redressal Officer, Khatabook<br/>`
		}
		kfsOtherDisclosurs.NodalOfficer += `
		Name: Mr. Rupesh Kalokhe<br/>
		Designation: Chief Operating Officer<br/>
		Email ID: <EMAIL><br/>
		Contact No: +91 80073 39339<br/>
		Address: 3rd Floor, Building No 2 Office No 302, Star Hub, Sahar Rd, Andheri East, Mumbai, Maharashtra
		400059<br/>`
	case constants.WesternCapitalID:
		kfsOtherDisclosurs.CoolOffPeriod = "7 days"
		kfsOtherDisclosurs.LSPAgent = "ADJ Utilities Private Limited (Khatabook)"
		kfsOtherDisclosurs.NodalOfficer = `For ADJ Utilities Private Limited (Khatabook)<br/>
		Name : Yogesh Pawde<br/>
		Designation : Grievance Nodal Officer<br/>
		Address : 1203, 22nd Cross Rd, <br/>
		Sector 3, HSR Layout, <br/>
		Bengaluru, Karnataka 560102<br/>
		Email ID : <EMAIL><br/>

		For Western Cap Advisors Private Limited<br/>
		Name : Jay Popawala<br/>
		Designation : Head-Operations<br/>
		Address : C-402, Business Square, Andheri
		Kurla Road, Chakala, Andheri (E), Mumbai -
		400093<br/>
		Contact No : 022-28256772<br/>
		Email ID : <EMAIL><br/>
		`
		kfsOtherDisclosurs.LSPLink = "https://khatabook.com/privacy"
		kfsOtherDisclosurs.RELink = "https://westerncap.in/"
	case constants.MuthootCLID:
		switch sourceEntityID {
		case constants.SolvMuthootCLDSAID, constants.ValueMediMuthootCLDSAID:
			// not using these in latest agreement template
			kfsOtherDisclosurs.LSPAgent = "Spocto Solution"
			kfsOtherDisclosurs.NodalOfficer = `
			Grievance/redressal details:<br/>
			Address: Unit A, 6th Floor,Techniplex I, Techniplex Complex opp Veer Savarkar Flyover, Goregaon Mumbai, Mumbai City Maharashtra 400062, India.<br/>
			Email: <EMAIL> (Ezhilarasi Govindarajan)<br/>`
			kfsOtherDisclosurs.LSPLink = "https://www.spocto.com/"
		case constants.FloBizMuthootCLDSAID:
			kfsOtherDisclosurs.LSPAgent = "Valorem Stack Private Limited (Flobiz)"
			kfsOtherDisclosurs.NodalOfficer = `
			Grievance/redressal details:<br/>
			Address: GNGi Elite, 11/3, Bomanahalli, Hosur Road, Bangalore.<br/>
			Email: <EMAIL> (Mr. Tushar Mohan Deshpande)<br/>`
			kfsOtherDisclosurs.LSPLink = "https://flobiz.in/"
		case constants.VyaparMuthootCLDSAID:
			kfsOtherDisclosurs.LSPAgent = "Simply Vyapar Apps Private Limited (Vyapar)"
			kfsOtherDisclosurs.NodalOfficer = `
			Grievance/redressal details:<br/>
			Address: 24th, 1,2 & 3 floor, 150/2 Enzyme Diamond, 7th Cross Rd, 1st Sector, HSR Layout, Bengaluru, Karnataka 560102<br/>
			Email: <EMAIL> (Mr. Aman Srivastava)<br/>`
			kfsOtherDisclosurs.LSPLink = "https://vyaparapp.in/"
		}
	case constants.IIFLID:
		switch sourceEntityID {
		case constants.KhatabookID:
			kfsOtherDisclosurs.LSPAgent = "ADJ Utilities Private Limited (Khatabook)"
			kfsOtherDisclosurs.NodalOfficer += `
			Name - Mr. Yogesh Pawde<br/>
			Email ID - <EMAIL><br/>
			Nodal Grievance Redressal Officer, Khatabook<br/>`
		case constants.TataPLID:
			extraNodalOfficerDetails = `Designation - Nodal Officer for LSP<br/>
			Address: 1st Floor, Army & Navy Building, 148, M G Road, Opposite Kala Ghoda, Fort, Mumbai, 400001, India <br/>
			Email ID: <EMAIL> <br/>`
			kfsOtherDisclosurs.LSPAgent = "IIFL Finance Limited"
			kfsOtherDisclosurs.LSPAgentCustomer = "Tata Digital Private Limited"
		default:
			kfsOtherDisclosurs.LSPAgent = "IIFL Finance Limited"
		}
		kfsOtherDisclosurs.CoolOffPeriod = "7 days"
		kfsOtherDisclosurs.LockInPeriod = "Nil"
		kfsOtherDisclosurs.NodalOfficer += `
		Contact No: +91 22 4520 5810 / +91 22 6817 8410<br/>
		Email ID: <EMAIL><br/>
		Grievance redressal-https://www.iifl.com/finance/grievance-redressal-procedure<br/>
		`
		kfsOtherDisclosurs.NodalOfficer += extraNodalOfficerDetails
	}
	return kfsOtherDisclosurs
}

func DoNotAllowInitiateNewApplication(sourceEntityID, entityType string) bool {
	if entityType == constants.EntityTypeSourcingEntity {
		return false
	} else if entityType == constants.EntityTypeCustomer {
		return general.InArr(sourceEntityID, []string{constants.KhatabookID, constants.ABFLMarketplaceID})
	}
	return false
}

// GetTitleForDisbursalModule returns title used in the disbursal page of the journey
func GetTitleForDisbursalModule(loanType, sourceEntityName, sourceEntityID string) string {
	if general.InArr(loanType, []string{constants.LoanTypeCreditLine, constants.LoanTypeOverDraft}) {
		return fmt.Sprintf("%s credit", sourceEntityName)
	}

	if sourceEntityID == constants.MFLBLID {
		return "Loan sanctioned"
	}

	if IsPFLSourcing(sourceEntityID) {
		return "Loan Details"
	}
	return "Loan sanctioned"
}

// GetDescriptionForDisbursalModule returns description used in the disbursal page of the journey
func GetDescriptionForDisbursalModule(loanType, sourceEntityID string) string {
	if general.InArr(loanType, []string{constants.LoanTypeCreditLine, constants.LoanTypeOverDraft}) {
		return "Your application has been acknowledged, the line will be activated within 1 working day, subject to final approval."
	}
	parentSourceEntityID := sourceEntityID
	if IsPFLSourcing(parentSourceEntityID) {
		GetParentSourceEntityID("", &parentSourceEntityID)
	}

	switch parentSourceEntityID {
	case constants.MFLBLID:
		return "Your loan application has been acknowledged, the amount will be disbursed within 2 working days subject to final approval."
	case constants.GPayIIFLBLID, constants.GPayIIFLPLID:
		return "Your loan application has been completed successfully. Our representative will get in touch with you in the next 24 hours for verification and disbursement process"
	case constants.KhatabookID:
		return "Your loan application has been acknowledged, the amount will be disbursed within 3 to 4 business days subject to final approval. The total payable interest amount will vary depending on the actual disbursal date."
	case constants.PoonawallaFincorpID:
		return "We have received your loan application. The loan amount will be disbursed to your bank account within the next two hours, subject to loan approval."
	}
	return "Your loan application has been acknowledged, the amount will be disbursed within 2 working days subject to final approval."
}

// IsCurrentAddressKYCFlow tells whether the flow includes current address in kyc flow or not
func IsCurrentAddressKYCFlow(flowID int) bool {
	return general.InArr(flowID, []int{constants.KYCFlowDigilockerCurrentAddressPL, constants.KYCFlowKYCService})
}

// DeviceConnectThreshold returns the threshold in hours before refetching dc data
func DeviceConnectThreshold(sourceEntityID string) int {
	if sourceEntityID == constants.KhatabookID {
		return 30 * 24
	}
	return 48
}

// GetEMIMethodForAgreement - returns EMI method for some lender+loanType combination
// as the stored method is different from the one that should be used in calculation
func GetEMIMethodForAgreement(loanType string, lenderID string, currentMethod string) string {
	if lenderID == constants.ArthanLenderID && general.InArr(loanType, []string{constants.LoanTypeCreditLine, constants.LoanTypeOverDraft}) {
		return constants.MethodSimpleInterest
	}
	return currentMethod
}

// IsMultiOfferAllowed returns whether multi offer is allowed or not for a user and platform combination
func IsMultiOfferAllowed(sourceEntityID, userID string) bool {
	if sourceEntityID == constants.TataPLID {
		return true
	}
	if sourceEntityID == constants.ABFLMarketplaceID || sourceEntityID == constants.ABCDMarketPlaceID {
		return true
	}
	if enable, _ := MultiOfferRolloutPerc(sourceEntityID); enable {
		return featureflag.Get(userID, FlagMultiLoanOffer)
	}
	return false
}

// IsMultiOfferV2Allowed checks if v2 is allowed for a source and user
func IsMultiOfferV2Allowed(sourceEntityID, userID string) bool {
	return featureflag.Get(userID, FlagMultiLoanOfferV2)
}

// UseKYCService returns whether KYC service will be used for KYC
// and for how much % of new users
func UseKYCService(sourceEntityID string) (bool, int) {
	res := IIFLAgg(sourceEntityID, false)
	// assign the KYC service at the correct place
	if IsPFLEducationLoanJourney(sourceEntityID) {
		return true, 100
	}
	if IsMFLBLSourcing(sourceEntityID) {
		return true, 100
	}
	if sourceEntityID == constants.IIFLID || sourceEntityID == constants.IIFLBLID || res.IsAgg || sourceEntityID == constants.MoneyControlID {
		return true, 100
	}
	if IsABFLPLSourcing(sourceEntityID) {
		return true, 100
	}
	if IsABFLBLSourcing(sourceEntityID) {
		return true, constants.KYCServiceABFLUsagePercentage
	}
	if IsMintifiAsLender(sourceEntityID) {
		return true, constants.KYCServiceMintifiUsagePercentage
	}
	return false, 0
}

type KYCServiceWfConfig struct {
	MaxKYCAttempts              int
	ManualReviewEnableAttempts  int
	ManualKYCEnableAttempts     int
	MinKYCCompletionsForSuccess int
}

func GetKYCServiceWorkflowSessionConfig(userID, sourceEntityID string, numberOfApplications int) (config KYCServiceWfConfig) {
	maxKYCAttempts := 1
	if conf.ENV == conf.ENV_PROD {
		maxKYCAttempts = 2
	}
	if IsABFLBLSourcing(sourceEntityID) {
		if IsCoApplicantJourney(userID, sourceEntityID) {
			maxKYCAttempts = 20
		} else if IsMultiUserConstitutionJourney(userID, sourceEntityID) {
			maxKYCAttempts = 5
		}
	}
	if IsPFLEducationLoanJourney(sourceEntityID) {
		maxKYCAttempts = 5
	}
	return KYCServiceWfConfig{
		MaxKYCAttempts:              maxKYCAttempts,
		ManualReviewEnableAttempts:  1,
		ManualKYCEnableAttempts:     0,
		MinKYCCompletionsForSuccess: numberOfApplications,
	}
}

type OverrideKYCServiceProgramNamesStruct struct {
	ProgramName string
	KYCType     string
	CTA         string
}

// OverrideKYCServiceProgramNames is used to pass program name while creating a kyc service session. This flag is only to be used in specific cases
// where there could be multiple programs for a single sourceEntity and lenderID combination for a single loanVariant
func OverrideKYCServiceProgramNames(sourceEntityID, lenderID, userID string, filterOnKYCType bool, kycType string) (overrideApplicable bool, programs []OverrideKYCServiceProgramNamesStruct) {
	programs = make([]OverrideKYCServiceProgramNamesStruct, 0)
	if lenderID == constants.MFLBLID {
		dynamicUserInfoMap, err := users.GetDynamicUserInfoMap(userID)
		if err != nil {
			logger.WithUser(userID).Error(err)
		}

		if dynamicUserInfoMap["occupation"] == constants.MFLOccupationTypeSalaried {
			programs = append(programs, OverrideKYCServiceProgramNamesStruct{
				ProgramName: constants.KYCServiceProgNameMFLBLDigilockerSalaried,
				KYCType:     constants.KYCServiceProgramTypeDigilocker,
			})
		} else {
			programs = append(programs, OverrideKYCServiceProgramNamesStruct{
				ProgramName: constants.KYCServiceProgNameMFLBLDigilocker,
				KYCType:     constants.KYCServiceProgramTypeDigilocker,
			})
		}
		return true, programs
	} else if IsPFLEducationLoanJourney(sourceEntityID) {
		programs = append(programs, OverrideKYCServiceProgramNamesStruct{
			ProgramName: constants.KYCServiceProgNamePFLEducationLoanDigilocker,
			KYCType:     constants.KYCServiceProgramTypeDigilocker,
			CTA:         "",
		}, OverrideKYCServiceProgramNamesStruct{
			ProgramName: constants.KYCServiceProgNamePFLEducationLoanManual,
			KYCType:     constants.KYCServiceProgramTypeManual,
			CTA:         "",
		})
		return true, programs
	} else if sourceEntityID == constants.MoneyControlID && lenderID == constants.ABFLPLID {
		programs = append(programs, OverrideKYCServiceProgramNamesStruct{
			ProgramName: constants.KYCServiceProgNameMoneyControlABFLDigilocker,
			KYCType:     constants.KYCServiceProgramTypeDigilocker,
			CTA:         "",
		}, OverrideKYCServiceProgramNamesStruct{
			ProgramName: constants.KYCServiceProgNameMoneyControlABFLManual,
			KYCType:     constants.KYCServiceProgramTypeManual,
			CTA:         "",
		})
		return true, programs
	} else if IsABFLPLSourcing(sourceEntityID) {
		programs = append(programs, OverrideKYCServiceProgramNamesStruct{
			ProgramName: constants.KYCServiceProgNameABFLPLDigilockerKYC,
			KYCType:     constants.KYCServiceProgramTypeDigilocker,
		}, OverrideKYCServiceProgramNamesStruct{
			ProgramName: constants.KYCServiceProgNameABFLPLManualKYC,
			KYCType:     constants.KYCServiceProgramTypeManual,
		})
		return true, programs
	} else if IsABFLBLSourcing(sourceEntityID) {
		if IsCoApplicantJourney(userID, sourceEntityID) && IsKYCServiceWorkflowEnabled(userID) {
			// for co applicant cases of DSA Pre approved journey
			if general.InArr(sourceEntityID, []string{constants.DSAPreApprovedABFLPartnerABCID, constants.DSAPreApprovedABFLPartnerABCIDV2, constants.DSAPreApprovedABFLPartnerDSTID, constants.DSAPreApprovedABFLABG}) {
				programs = append(programs, OverrideKYCServiceProgramNamesStruct{
					ProgramName: constants.KYCServiceProgNameABFLDSAABCDDigilocker,
					KYCType:     constants.KYCServiceProgramTypeDigilocker,
					CTA:         "",
				})
				return true, programs
			} else {
				programs = append(programs, OverrideKYCServiceProgramNamesStruct{
					ProgramName: constants.KYCServiceProgNameABFLDigilockerCoApplicantWorkflowJourney,
					KYCType:     constants.KYCServiceProgramTypeDigilocker,
					CTA:         "",
				})
				return true, programs
			}
		} else if general.InArr(sourceEntityID, []string{constants.DSAPreApprovedABFLPartnerABCID, constants.DSAPreApprovedABFLPartnerABCIDV2, constants.DSAPreApprovedABFLPartnerDSTID, constants.DSAPreApprovedABFLDigiPartner, constants.DSAPreApprovedABFLDistribution, constants.DSAPreApprovedABFLABG}) {
			programs = append(programs, OverrideKYCServiceProgramNamesStruct{
				ProgramName: constants.KYCServiceProgNameABFLDSAABCDDigilocker,
				KYCType:     constants.KYCServiceProgramTypeDigilocker,
				CTA:         "",
			})
			overrideApplicable = true
		} else if IsMultiUserConstitutionJourney(userID, sourceEntityID) {
			programs = append(programs, OverrideKYCServiceProgramNamesStruct{
				ProgramName: constants.KYCServiceProgNameABFLDigilockerPrivateLimited,
				KYCType:     constants.KYCServiceProgramTypeDigilocker,
				CTA:         "",
			})
			overrideApplicable = true
		}
	}

	if filterOnKYCType {
		filteredPrograms := make([]OverrideKYCServiceProgramNamesStruct, 0)
		for i := 0; i < len(programs); i++ {
			if programs[i].KYCType == kycType {
				filteredPrograms = append(filteredPrograms, OverrideKYCServiceProgramNamesStruct{
					ProgramName: programs[i].ProgramName,
					KYCType:     kycType,
					CTA:         "",
				})
			}
		}
		if len(filteredPrograms) == 0 {
			return false, filteredPrograms
		}
		return overrideApplicable, filteredPrograms
	}

	return overrideApplicable, programs
}

// IsPrimaryUserKYCToBeSkipped is a flag used to tell if for a user journey, the primary user's kyc is to be done or skipped.
// cases where primary user is actually a business, KYC is not needed.
// return true for a journey where kyc of primary used is to be skipped.
func IsPrimaryUserKYCToBeSkipped(sourceEntityID, lenderID, primaryUserID string) (skipPrimaryUserKYC bool) {
	return IsABFLBLSourcing(sourceEntityID) && IsMultiUserConstitutionJourney(primaryUserID, sourceEntityID)
}

// UseKYCServiceWorkflow returns whether KYC service workflow will be used for KYC
// and how much % of new users will be assigned this flag
func UseKYCServiceWorkflow(userID, sourceEntityID string) (bool, int) {
	if IsABFLBLSourcing(sourceEntityID) && IsCoApplicantJourney(userID, sourceEntityID) {
		return true, 100
	}
	return false, 0
}

// UseKYCServiceWorkflowFromStart returns whether KYC service workflow will be used for KYC
// and how much % of new users will be assigned this flag
func UseKYCServiceWorkflowFromStart(sourceEntityID string) bool {
	return IsPFLEducationLoanJourney(sourceEntityID)
}

func UseDigilockerAsMainKYC(sourceEntityID string) (bool, int) {
	res := IIFLAgg(sourceEntityID, false)
	if sourceEntityID == constants.IIFLID || sourceEntityID == constants.IIFLBLID || res.IsAgg {
		switch {
		case sourceEntityID == constants.IIFLID || (res.IsAgg && res.LoanType == constants.LoanTypePersonalLoan):
			{
				return true, constants.IIFLDigilockerMainKYCIIFLPLUsagePercentage
			}
		case sourceEntityID == constants.IIFLBLID || (res.IsAgg && res.LoanType == constants.LoanTypeBusinessLoan):
			{
				return true, constants.IIFLDigilockerMainKYCIIFLBLUsagePercentage
			}
		}
	}
	return false, 0
}

// IsFallBackExperian return whether fallback bureau module to CIBIL is experian or not.
func IsFallBackExperian(sourceEntityID, lenderID string) bool {
	if sourceEntityID == constants.HousingID {
		return false
	}
	if general.InArr(sourceEntityID, []string{constants.SupremeSolarID, constants.TataNexarcID}) {
		return false
	}
	if IsABFLBLSourcing(sourceEntityID) || sourceEntityID == constants.ABFLMarketplaceID {
		return false
	}
	return !general.InArr(sourceEntityID, []string{constants.TataBNPLID, constants.TataPLID}) && !IsMuthootCLPartner(sourceEntityID) && !IsMuthootEDIPartner(sourceEntityID)
}

// HidePIIsdkAPI is used to hide personal identification information of users in different parts of the journey
func HidePIIsdkAPI(sourceEntityID, programName string) bool {
	return general.InArr(sourceEntityID, []string{constants.TataPLID, constants.TataBNPLID}) // true for bnpl and pl both
}

// IsBankConnectReportDownloadAllowed returns whether bank connect report download is enabled for a platform
func IsBankConnectReportDownloadAllowed(sourceEntityID string) bool {
	return general.InArr(sourceEntityID, []string{constants.TataNexarcID}) || IsOneMuthootPartner(sourceEntityID) || IsMuthootCLPartner(sourceEntityID) ||
		IsABFLBLSourcing(sourceEntityID) || IsIIFLSourcing(sourceEntityID)
}

// IsPlatformDSA returns whether DSA-like features are enabled for input anchor
func IsPlatformDSA(sourceEntityID string) bool {
	return general.InArr(sourceEntityID, []string{constants.TataNexarcID})
}

// HasCustomTheme is used to exclude child source entities from selecting the same theme as parent
func HasCustomTheme(sourceEntityID string) bool {
	return general.InArr(sourceEntityID, []string{constants.GPayIIFLPLID, constants.GPayIIFLBLID})
}

// HasCustomKYCFlow is used to exclude child source entities from selecting the same KYC flow as parent
func HasCustomKYCFlow(sourceEntityID string) bool {
	return general.InArr(sourceEntityID, []string{constants.GPayIIFLPLID, constants.GPayIIFLBLID, constants.DSAAndromedaPL})
}

// ShowCreditScore is used whether to show the credit score page or not after the bureau is completed
func ShowCreditScore(sourceEntityID string) bool {
	return general.InArr(sourceEntityID, []string{constants.GPayIIFLPLID, constants.GPayIIFLBLID, constants.HousingID})
}

// IsBCRedirectFlow gives whether to use the redirect flow for bank connect
func IsBCRedirectFlow(sourceEntityID string) bool {
	return general.InArr(sourceEntityID, []string{constants.GPayIIFLPLID, constants.GPayIIFLBLID, constants.DSAGpayABFLBL})
}

func HasDynamicUserInfoEnabled(sourceEntityID string) bool {
	return general.InArr(sourceEntityID, []string{constants.SupremeSolarID}) || IsOneMuthootPartner(sourceEntityID) || IsMuthootCLPartner(sourceEntityID) || IsPFLSourcing(sourceEntityID)
}

// NewRejectionScreen gives whether to show new rejection screen
func NewRejectionScreen(sourceEntityID string) bool {
	return sourceEntityID == constants.TataNexarcID
}

// ShowBusinessAddressOwnership gives whether to show ownership type in business details page
func ShowBusinessAddressOwnership(sourceEntityID string) bool {
	return general.InArr(sourceEntityID, []string{constants.TataNexarcID})
}

// WaitForDC return wether to wait for device connect fetch risk or not
func WaitForDC(userID, sourceEntityID, sdkVersion string) bool {
	if general.InArr(sourceEntityID, []string{constants.TataPLID, constants.TataBNPLID}) {
		return true
	}

	if IsPFLSourcing(sourceEntityID) {
		return true
	}

	if sdkVersion == constants.SDKVersionWeb {
		return false
	}
	if general.InArr(sourceEntityID, []string{constants.KhatabookID, constants.VyaparID}) {
		return true
	}
	if IIFLBLPolicy("", sourceEntityID) || IIFLPLPolicy(sourceEntityID) || IsMuthootEDIPartner(sourceEntityID) || IsMFLEMIPartner(sourceEntityID) {
		return false
	}
	if IsABFLBLSourcing(sourceEntityID) {
		return false
	}
	if IsABFLPLSourcing(sourceEntityID) {
		return false
	}
	return true
}

// ShowManualMode returns whether manual mode is ever to be shown in Digilocker page
// as "upload manually option in UI"
// can't iterate over KYC flow array and check for manual
// because for kyc flow 7, we have manual in list for collecting selfie and business proof
// but we still don't want manual mode to be available directly
// takes in source entity as well to decide
// TODO: in future if required, this can also take attempt count as input
func ShowManualMode(kycFlowID int, sourceEntityID string) bool {
	if IsMuthootCLPartner(sourceEntityID) {
		return false
	}
	return !IsPFLSourcing(sourceEntityID) && !general.InArr(kycFlowID, []int{constants.KYCFlowLenderWeb, constants.KYCFlowLender, constants.KYCFlowDigilockerBLWithoutManual, constants.KYCFlowDigilockerSelfie, constants.KYCFlowLiveness})
}

// ShowKFS returns bool based on lender ID
// It controls whether to show the KFS document before the user agrees to the loan offer
func ShowKFS(lenderID string) bool {
	return lenderID == constants.PoonawallaFincorpID || lenderID == constants.MFLBLID || lenderID == constants.ABFLPLID
}

// AllowDontHaveGST returns whether user should be allowed to proceed with No GST option in journey or not
func AllowDontHaveGST(sourceEntityID string) bool {
	return !general.InArr(sourceEntityID, []string{constants.TataNexarcID})
}

// UseAadhaarNameInPennyChecks returns which name to use for name check in penny drop
func UseAadhaarNameInPennyChecks(sourceEntityID string) bool {
	return IsPFLSourcing(sourceEntityID)
}

// ShowDebitCardEnach return whether debit card option is to be enabled for enach
func ShowDebitCardEnach(lenderID string) bool {
	return !general.InArr(lenderID, []string{constants.MCSLID})
}

// EnachBankChangeAllowed return whether enach can be done on a bank account different from bank connect
func EnachBankChangeAllowed(sourceEntityID string) bool {
	if IsABFLBLSourcing(sourceEntityID) {
		return false
	}
	return !IsOneMuthootPartner(sourceEntityID)
}

// ShowRejectReason tells whether to show reject reason in activity log for certain partners or not
func ShowRejectReason(sourceEntityID string) bool {
	return general.InArr(sourceEntityID, []string{constants.VyaparID, constants.KhatabookID, constants.BigBasketID, constants.TataNexarcID, constants.TataBNPLID, constants.TataPLID, constants.OneMuthootID, constants.HousingID, constants.ABFLMarketplaceID, constants.MoneyControlID, constants.MFLBLID, constants.ABCDMarketPlaceID, constants.KreditBeePlatformID, constants.DSAKreditBeeMCID}) || IIFLPLPolicy(sourceEntityID) || IIFLBLPolicy("", sourceEntityID) || IsMintifiAsLender(sourceEntityID) || IsABFLPLSourcing(sourceEntityID) || IsABFLBLSourcing(sourceEntityID) || IsPFLEDUSourcing(sourceEntityID) || IsPFLSourcing(sourceEntityID) || IsMFLBLSourcing(sourceEntityID) || IsIncredSourcing(sourceEntityID)
}

// IsPANFraudCheckApplicable tells whether we want to restrict PAN fetches for a user
func IsPANFraudCheckApplicable(sourceEntityID string) bool {
	if IsABFLBLSourcing(sourceEntityID) {
		return true
	}
	return IsPFLSourcing(sourceEntityID)
}

// ShowPincodePopup controls whether to show the pincode popup on the PAN Info screen
func ShowPincodePopup(sourceEntityID string) bool {
	if IsABFLBLSourcing(sourceEntityID) {
		return false
	}
	return !IsPFLSourcing(sourceEntityID)
}

// PullDeviceData tells whether to pull the device data for web journey
func PullDeviceData(sourceEntityID string, sdkVersion string) bool {
	return IsPFLSourcing(sourceEntityID) && sdkVersion == constants.SDKVersionWeb
}

// CalculateMaxEMI tells whether to calculate max EMI
func CalculateMaxEMI(lenderID string) bool {
	return lenderID == constants.PoonawallaFincorpID
}

// HideLenderLogo tells whether to show lender logo on the calculator and accept offer page
func HideLenderLogo(sourceEntityID string) bool {
	return IsOneMuthootPartner(sourceEntityID) || IsABFLBLSourcing(sourceEntityID) || IsABFLPLSourcing(sourceEntityID) || IsPFLSourcing(sourceEntityID)
}

// HideLendingPartnerText tells whether to show 'Lending Partner' text on calculator page
func HideLendingPartnerText(sourceEntityID string) bool {
	return IsOneMuthootPartner(sourceEntityID) || IsABFLBLSourcing(sourceEntityID) || IsPFLSourcing(sourceEntityID)
}

// HideLenderName tells whether to show lender name on the accept offer page
func HideLenderName(sourceEntityID string) bool {
	return IsOneMuthootPartner(sourceEntityID) || IsABFLPLSourcing(sourceEntityID) || IsPFLSourcing(sourceEntityID)
}

// IsRoundOffRequired tells whether to use rounded off values of amounts
func IsRoundOffRequired(sourceEntityID, lenderID string) bool {
	if IsIIFLBLSourcing(sourceEntityID) {
		return true
	}
	return general.InArr(lenderID, []string{constants.PoonawallaFincorpID, constants.ABFLID, constants.ABFLPLID})
}

// OfferTnCText returns the Tnc text shown on the calculator page
func OfferTnCText(lenderID string) string {
	if lenderID == constants.PoonawallaFincorpID {
		return "*List of all applicable charges will be provided in the Key Fact Statement."
	}
	return ""
}

// OfferAmountMultiplier returns the multiple in which the offer amount can be changed on the calculator page
func OfferAmountMultiplier(lenderID string) int {
	if lenderID == constants.PoonawallaFincorpID {
		return 1000
	} else {
		// In case of -1 default config in the frontend gets used
		return -1
	}
}

// IsConsumerLoan returns whether loan is a consumer loan - loan offered to customers to buy goods/appliances/devices
func IsConsumerLoan(sourceEntityID string) bool {
	return sourceEntityID == constants.SupremeSolarID
}

// AmountToString returns the amount in formatted string (shown in KFS and agreement) as per the lender
func AmountToString(lenderID string, num float64) string {
	if lenderID == constants.MFLBLID {
		p := message.NewPrinter(language.Hindi)
		num = math.Ceil(num)
		return p.Sprintf("%.0f", num)
	} else if lenderID == constants.PoonawallaFincorpID {
		p := message.NewPrinter(language.Hindi)
		return p.Sprintf("%.0f", num)
	}
	return fmt.Sprintf("%.2f", num)
}

// HideFAQ tells whether to show the FAQ option in the journey
func HideFAQ(sourceEntityID string) bool {
	return IsOneMuthootPartner(sourceEntityID) ||
		IsMFLBLSourcing(sourceEntityID) ||
		IsSuperMoneyJourney("", sourceEntityID) ||
		IsPFLSourcing(sourceEntityID)
}

// BureauWaitText return expected text to show on frontend for bureau fetching
func BureauWaitText(sourceEntityID string) string {
	if IsPFLSourcing(sourceEntityID) {
		return "Please wait, your credit information is on the way"
	}
	return "Expected Completion Time"
}

// ExpectedBureauCompletionTime return expected time to show on frontend for bureau completion
func ExpectedBureauCompletionTime(sourceEntityID string) string {
	if IsPFLSourcing(sourceEntityID) {
		return ""
	} else if sourceEntityID == constants.MoneyControlID {
		return fmt.Sprintf("%s %s", constants.MoneyControlBureauTimeCompletion, constants.BureauTimeCompletionFormat)
	}
	return fmt.Sprintf("%s %s", constants.DefaultBureauTimeCompletion, constants.BureauTimeCompletionFormat)
}

// Hiding bureau logo
func HideBureauIcon(sourceEntityID string) bool {
	return sourceEntityID == constants.MoneyControlID
}

func ShowScamAlertMsg(sourceEntityID string) bool {
	return IsIIFLBLSourcing(sourceEntityID)
}

// BureauCountDown returns expected completion time(secs) counter. if value is 0 counter will not be applicable
func BureauCountDown(sourceEntityID string) (completionTime int) {
	if sourceEntityID == constants.MoneyControlID {
		completionTime, _ = strconv.Atoi(constants.MoneyControlBureauTimeCompletion)
	}
	return
}

// FormatAdvanceEMI returns rounded/ceil advance EMI value as per the lender
func FormatAdvanceEMI(lenderID string, advanceEMI float64) float64 {
	if lenderID == constants.PoonawallaFincorpID || lenderID == constants.MFLBLID {
		return math.Ceil(advanceEMI)
	}
	return math.Round(advanceEMI)
}

// AdvanceEMIKeyText returns the key text for advance EMI that is shown on frontend
func AdvanceEMIKeyText(lenderID string) string {
	if lenderID == constants.PoonawallaFincorpID || lenderID == constants.MFLBLID {
		return "Broken period interest"
	}
	return "Pre EMI"
}

// InterestValueText returns text shown along with the interest amount
func InterestValueText(lenderID string) string {
	if lenderID == constants.PoonawallaFincorpID {
		return "per annum"
	}
	return "p.a"
}

// GSTText returns the text shown on the frontend for GST key
func GSTText(lenderID string, gst float64) string {
	if lenderID == constants.PoonawallaFincorpID {
		return "GST (as applicable)"
	}
	return fmt.Sprintf("GST @ %.0f%s", gst, "%")
}

// HidePFInclusiveGST returns whether to show processing fee including GST on frontend
func HidePFInclusiveGST(lenderID string) bool {
	return lenderID == constants.PoonawallaFincorpID
}

// TakeResidenceType tells whether to ask for residence type after digilocker is successful
func TakeResidenceType(sourceEntityID string) bool {
	return IsPFLSourcing(sourceEntityID)
}

// AllowManualReviewOverride tells whether to show manual review option for a source entity
// TODO: this is can removed by updating the kyc flow
func AllowManualReviewOverride(sourceEntityID, lenderID, userID string, kycAttempts int) (bool, bool) {
	if IsMFLBLSourcing(sourceEntityID) {
		return true, false
	}
	if IsABFLPLSourcing(sourceEntityID) || lenderID == constants.ABFLPLID {
		return true, false
	}
	if IsPFLSourcing(sourceEntityID) {
		return true, false
	}
	if IsABFLBLSourcing(sourceEntityID) && IsMultiUserConstitutionJourney(userID, sourceEntityID) {
		if kycAttempts >= 1 {
			return true, true
		} else {
			return true, false
		}
	}
	if IsABFLBLSourcing(sourceEntityID) && (IsProprietorshipConstitutionJourney(userID, sourceEntityID) || IsCoApplicantJourney(userID, sourceEntityID)) {
		return true, false
	}
	return false, true
}

func AllowManualUploadKYCOverride(sourceEntityID, lenderID, userID string, attempts int) (bool, bool) {
	if IsMFLBLSourcing(sourceEntityID) {
		return true, false
	}
	if attempts < 1 && IsABFLBLSourcing(sourceEntityID) {
		return true, false
	}
	if IsABFLPLSourcing(sourceEntityID) || lenderID == constants.ABFLPLID {
		return true, false
	}
	if IsKYCServiceWorkflowEnabled(userID) {
		return true, false
	}
	if IsABFLBLSourcing(sourceEntityID) && (IsProprietorshipConstitutionJourney(userID, sourceEntityID) || IsCoApplicantJourney(userID, sourceEntityID)) {
		return true, false
	}
	return false, false
}

// IsFuzzyAddressMatchApplicableAtKYC this flag controls whether address match is to be performed when we receive callback from KYC Service
func IsFuzzyAddressMatchApplicableAtKYC(sourceEntityID string) bool {
	return IsMFLBLSourcing(sourceEntityID)
}

// IsDynamicAddressToBeLoanAttached this flag is used to set address from dynamic_user_info into user_loan_details -> current_address
func IsDynamicAddressToBeLoanAttached(sourceEntityID string) bool {
	return !general.InArr(sourceEntityID, []string{constants.DSAPreApprovedABFLPartnerABCID, constants.DSAPreApprovedABFLPartnerABCIDV2, constants.DSAPreApprovedABFLPartnerDSTID, constants.DSAPreApprovedABFLDigiPartner, constants.DSAPreApprovedABFLDistribution, constants.DSAPreApprovedABFLABG}) && IsABFLBLSourcing(sourceEntityID)
}

// GetInitiateNewAfterRejectDays gives no of cool down days after which user can re-apply based on sourcing entity
func GetInitiateNewAfterRejectDays(sourceEntityID string) int {
	if general.InArr(sourceEntityID, []string{constants.TataPLID, constants.MoneyControlID}) {
		return 30
	}

	if IsPFLSourcing(sourceEntityID) {
		return 30
	}

	if IsABFLBLSourcing(sourceEntityID) || general.InArr(sourceEntityID, []string{constants.HousingID}) {
		return 90
	}
	if sourceEntityID == constants.SentinelTestID {
		return 0
	}
	return 60
}

func GetKYCConsentText(sourceEntityID, lenderID string) (toCapture bool, text string) {
	if lenderID == constants.ABFLPLID {
		toCapture = true
		text = "I hereby confirm my mobile number is linked with my Aadhaar number and authorize Aditya Birla Capital Limited to collect, store, verify my KYC details from Digilocker."
		return toCapture, text
	} else if lenderID == constants.ABFLID {
		toCapture = true
		text = "I hereby give my consent to Aditya Birla Capital Limited to collect, store & verify my KYC data issued by UIDAI and establish my identity/address for verification & regulatory purposes only & access my Location Co-ordinates"
		return toCapture, text
	}
	return false, ""
}

// GetContactText gives sourcing entity contact info shown on rejection page
func GetContactText(sourceEntityID string) string {
	return ""
}

// ShowDisbursedModule gives whether to show Disbursed module based on sourcing entity
func ShowDisbursedModule(sourceEntityID string) bool {
	return IsHCINFlow(sourceEntityID) || IsABFLBLSourcing(sourceEntityID) || sourceEntityID == constants.ABFLMarketplaceID || IsPFLSourcing(sourceEntityID)
}

// HideClose gives whether to show the close button in journey
func HideClose(sourceEntityID string) bool {
	return general.InArr(sourceEntityID, []string{constants.MoneyControlID, constants.ABFLMarketplaceID, constants.DSAMoneyControlIncredID}) || IsPFLSourcing(sourceEntityID)
}

// ShowTopPlatformLogo gives whether to show source entity logo always on top
func ShowTopPlatformLogo(sourceEntityID string) bool {
	return general.InArr(sourceEntityID, []string{constants.ABFLMarketplaceID}) || IsABFLBLSourcing(sourceEntityID) || IsABFLPLSourcing(sourceEntityID) || IsPFLSourcing(sourceEntityID)
}

func ShowBottomPlatformLogo(sourceEntityID string) bool {

	return sourceEntityID == constants.MoneyControlID ||
		IsMFLBLSourcing(sourceEntityID) ||
		IsSuperMoneyJourney("", sourceEntityID)
}

// WebPageTitle gives the webpage title shown in the web sdk
func WebPageTitle(sourceEntityID string) string {
	parentSourceEntityID := sourceEntityID
	GetParentSourceEntityID("", &parentSourceEntityID)
	switch parentSourceEntityID {
	case constants.PoonawallaFincorpID:
		return "Poonawalla Fincorp - Apply Personal Loan"
	default:
		return "FinBox - Lending SDK"
	}
}

// AccountDetailsDescription gives the text shown to the user while entering bank account details
func AccountDetailsDescription(sourceEntityID, panAPIName string) string {
	if IsPFLSourcing(sourceEntityID) {
		return "Please input bank details that is in your name (same as Aadhaar)"
	}
	return fmt.Sprintf("The account details you provide here, must be yours and the account holder's name has to be same as your PAN Card name, &lt;b>i.e %s &lt;/b>", panAPIName)
}

// AccountDetailsBusinessDescription gives the text shown to the business loan user while entering bank account details
func AccountDetailsBusinessDescription(sourceEntityID, panAPIName, businessName string) string {
	if IsPFLSourcing(sourceEntityID) {
		return fmt.Sprintf("The account holder's name should match with your PAN Card, &lt;b>i.e %s &lt;/b> or your business name &lt;b>i.e %s &lt;/b>", panAPIName, businessName)
	}
	return fmt.Sprintf("The account holder's name should match with your PAN Card name, &lt;b>i.e %s &lt;/b> or your business name &lt;b>i.e %s &lt;/b>", panAPIName, businessName)
}

// SkipAutoRedirect skips the auto-redirection on the Disbursal module page
func SkipAutoRedirect(sourceEntityID string) bool {
	return IsMuthootCLPartner(sourceEntityID) ||
		IsABFLPLSourcing(sourceEntityID) ||
		IsSuperMoneyJourney("", sourceEntityID) ||
		IsPFLSourcing(sourceEntityID)
}

// GenerateNewAgreementIfDaysPassed tells whether to send a fresh generated agreement if x days are passed since generating old agreement.
// x is sourceEntity dependent, we return x from this function.
// isApplicable : Is this check applicable for a given sourceEntity.
// days: x
func GenerateNewAgreementIfDaysPassed(sourceEntityID string) (isApplicable bool, days int) {
	if IsPFLSourcing(sourceEntityID) {
		return true, 1
	}
	return false, 0
}

func SendExitSurveyToClient(sourceEntityID string) bool {
	return general.InArr(sourceEntityID, []string{constants.TataPLID, constants.TataBNPLID})
}

func GetRelativeURLToSubmitExitSurvey(sourceEntityID string) (url string) {

	switch sourceEntityID {
	case constants.TataBNPLID:
		url = "/api/FinBoxPartner/Controller/bnpl/customer-exitSurvey"

	case constants.TataPLID:
		url = "/api/v1/marketplacepartnercontroller/exit-survey"
	}

	return url
}

// AskForOccupationType returns whether occupation type should be asked from users in Personal Info screen
// TODO: Remove this config and use dynamic user info/ builder instead for this
func AskForOccupationType(sourceEntityID string) bool {
	return false
}

// GetSentinelTriggerEvalSource returns source which needs to be sent in Sentinel Trigger
// evaluation API
func GetSentinelTriggerEvalSource(sourceEntityID, lenderID string) string {
	switch sourceEntityID {
	case constants.PhonePeID:
		return constants.EvalSourceMuthootPhonePe
	case constants.PayTMID:
		return constants.EvalSourceMuthootPayTM
	case constants.BharatPeID:
		return constants.EvalSourceMuthootBharatPe
	case constants.MoneyControlID:
		if lenderID == constants.LandTID {
			return constants.LandTSentinelPolicySource
		} else if lenderID == constants.ABFLPLID {
			return constants.EvalSourceABFLMoneyControl
		} else {
			return constants.SourceTypeBureau
		}
	case constants.MoneyViewMFLID:
		return constants.EvalSourceMFLMoneyView
	}
	return ""
}

// GetCIBILSource returns for a sourceEntity lender combination whether TransUnion Specified CIBIL is to be used or IIFL CIBIL APIs
func GetCIBILSource(sourceEntityID, lenderID string) string {

	if general.InArr(sourceEntityID, []string{constants.TataNexarcID, constants.SupremeSolarID}) {
		return constants.CIBILReportSourceTransUnionV2
	}

	if sourceEntityID == constants.HousingID {
		return constants.CIBILReportSourceTransUnionV2
	}

	if IsABFLBLSourcing(sourceEntityID) || IsABFLPLSourcing(sourceEntityID) {
		return constants.CIBILReportSourceABFLHardPull
	}

	if IsMuthootCLPartner(sourceEntityID) || IsMuthootEDIPartner(sourceEntityID) || IsMFLEMIPartner(sourceEntityID) {
		return constants.CIBILReportSourceMuthootV2
	}

	if sourceEntityID == constants.MoneyControlID && lenderID == constants.ABFLPLID {
		return constants.CIBILReportSourceABFLHardPull
	}

	return constants.CIBILReportSourceIIFLSoftPull
}

// ShowCRMID tells whether to show the CRM ID on dashboard
func ShowCRMID(sourceEntityID string) bool {
	return IsPFLSourcing(sourceEntityID)
}

// SearchWorkflowConfigBasedOnSourceEntityOnly is used to bypass lenderID from searching for workflow config
func SearchWorkflowConfigBasedOnSourceEntityOnly(sourceEntityID, moduleName string) bool {
	return sourceEntityID == constants.TataPLID && moduleName == "BUREAU"
}

// IsSourceAgg checks and returns if the source entity is an aggregator for the given ownerID
func IsSourceAgg(sourceEntityID, ownerID, suffix string) AggStruct {
	var update bool
	key := sourceEntityID + suffix
	valueStr, _ := redis.Get(context.TODO(), key)
	if valueStr == "" {
		update = true
	}
	var obj AggStruct
	if update {
		query := `select agent_code, product_type from dsa where owner_id = $1 and source_entity_id = $2`
		err := database.Get(&obj, query, ownerID, sourceEntityID)
		obj.IsAgg = err == nil
		// save in redis (including negative values with isAgg = false)
		valueBytes, err := json.Marshal(obj)
		if err != nil {
			log.Errorln(err)
			return obj
		}
		err = redis.Set(key, string(valueBytes), time.Hour*24*7)
		if err != nil {
			log.Errorln(err)
			return obj
		}
		return obj
	}
	err := json.Unmarshal([]byte(valueStr), &obj)
	if err != nil {
		log.Errorln(err)
		return obj
	}
	return obj
}

// ToLoadUnderwritingBankData checks if bank data is supposed to be loaded
// in underwriting for given sourceEntityID
func ToLoadUnderwritingBankData(sourceEntityID string) bool {
	return !(IsMuthootEDIPartner(sourceEntityID) || IsMFLEMIPartner(sourceEntityID))
}

// IsMuthootEDIPartner returns if the given source entity is Muthooth EDI partner
func IsMuthootEDIPartner(sourceEntityID string) bool {
	return sourceEntityID == constants.PhonePeID || sourceEntityID == constants.BharatPeID || sourceEntityID == constants.PayTMID
}

func IsMFLEMIPartner(sourceEntityID string) bool {
	return sourceEntityID == constants.MoneyViewMFLID
}

// IsLoadingDeviceConnectDataRequired if returns true, deviceConnect Data will be loaded at Underwriting
func IsLoadingDeviceConnectDataRequired(userID, sourceEntityID string) bool {
	if IsMuthootEDIPartner(sourceEntityID) {
		return false
	}
	if IsABFLPLSourcing(sourceEntityID) {
		return false
	}
	// for ABFL CoApplicant & API Stack Journey, deviceConnectData is not needed
	if IsABFLBLSourcing(sourceEntityID) && (IsUserCoApplicant(userID, sourceEntityID) || IsAPIStackFlow(userID, sourceEntityID)) {
		return false
	}
	return true
}

// IsMuthootCLPartner returns whether the MuthootBNPL lender is valid for the journey given sourceEntityID for the DSA
// returns true for sourceEntityID of MuthootBNPL by default. returns true for DSA of OneMuthootBNPL and loanType == "credit_line"
func IsMuthootCLPartner(sourceEntityID string) bool {
	if sourceEntityID == constants.MuthootCLID {
		return true
	}
	mBNPLAgg := IsSourceAgg(sourceEntityID, constants.MuthootCLID, muthootCLSuffix)
	return mBNPLAgg.IsAgg && mBNPLAgg.LoanType == constants.LoanTypeCreditLine
}

// HideConsentTextonLoanOffer is used to hide consent text on loan offer screen
func HideConsentTextonLoanOffer(sourceEntityID string) bool {
	return IsABFLBLSourcing(sourceEntityID) || IsMFLBLSourcing(sourceEntityID)
}

// IsABFLBLSourcing returns true for sourceEntityID of ABFL by default. returns true for DSA of OneMuthootBNPL and loanType == "credit_line"
func IsABFLBLSourcing(sourceEntityID string) bool {
	if sourceEntityID == constants.ABFLID {
		return true
	}
	abflAgg := IsSourceAgg(sourceEntityID, constants.ABFLID, abflblSuffix)
	return abflAgg.IsAgg && abflAgg.LoanType == constants.LoanTypeBusinessLoan
}

// IsPFLEDUSourcing returns true for sourceEntityID of ABFL by default. returns true for DSA of OneMuthootBNPL and loanType == "credit_line"
func IsPFLEDUSourcing(sourceEntityID string) bool {
	if sourceEntityID == constants.PFLEducationLoanSEID {
		return true
	}
	pflAgg := IsSourceAgg(sourceEntityID, constants.PoonawallaFincorpID, pflSuffix)
	return pflAgg.IsAgg && pflAgg.LoanType == constants.LoanTypeEducationLoan
}

// IsABFLPLSourcing returns true if the source entity ID is ABFL PL or its DSA with a personal loan type
func IsABFLPLSourcing(sourceEntityID string) bool {
	if sourceEntityID == constants.ABFLPLID {
		return true
	}
	abflAgg := IsSourceAgg(sourceEntityID, constants.ABFLPLID, abflplSuffix)
	return abflAgg.IsAgg && abflAgg.LoanType == constants.LoanTypePersonalLoan
}

// IsMFLBLSourcing returns true if the source entity ID is MFL BL or its DSAs with valid loan types
func IsMFLBLSourcing(sourceEntityID string) bool {
	if sourceEntityID == constants.MFLBLID {
		return true
	}
	mflBLAgg := IsSourceAgg(sourceEntityID, constants.MFLBLID, mflblSuffix)
	// add handling for loantypes BL, BL_EDI and BL_EMI
	return mflBLAgg.IsAgg && general.InArr(mflBLAgg.LoanType, []string{constants.LoanTypeBusinessLoan, constants.LoanTypeBusinessLoanEdi, constants.LoanTypeBusinessLoanEmi})
}

// IsMCPLSourcing returns true if the source entity ID is MC and loan type is PL
func IsMCPLSourcing(sourceEntityID string, loanType string) bool {
	return sourceEntityID == constants.MoneyControlID && loanType == constants.LoanTypePersonalLoan
}

func IsPFLSourcing(sourceEntityID string) bool {
	if sourceEntityID == constants.PoonawallaFincorpID {
		return true
	}
	pflAgg := IsSourceAgg(sourceEntityID, constants.PoonawallaFincorpID, pflplSuffix)
	return pflAgg.IsAgg && pflAgg.LoanType == constants.LoanTypePersonalLoan
}

// IsABFLSDKFlow returns true if sourceEntityID is ABFLSourcing and not an api stack flow
func IsABFLSDKFlow(userID, sourceEntityID string) bool {
	return IsABFLBLSourcing(sourceEntityID) && !IsAPIStackFlow(userID, sourceEntityID)
}

// ByPassBureauOnFailure this flag will return whether on failure of bureau fetch api, should the user move forward ( returning true in this case ) or
// we should stop him/her right there and show option to retry bureau on the screen thus pausing the user journey there itself (returning false in this case )
func ByPassBureauOnFailure(sourceEntityID string) bool {
	return !IsMuthootCLPartner(sourceEntityID) // modify this for required sourceEntity;s custom handling
}

// ToShowBankConnectPostGSTDisqualification basicaully gives the boolean value to move to bankconnect if a user is disqualified after GST BRE
func ToShowBankConnectPostGSTDisqualification(sourceEntityID string, sendToBankConnect bool, sourceModule string) bool {
	var toShow = sendToBankConnect && sourceModule == constants.GST
	iiflAgg := IIFLAgg(sourceEntityID, false)
	if general.InArr(sourceEntityID, []string{constants.IIFLBLID}) || (iiflAgg.IsAgg && iiflAgg.LoanType == constants.LoanTypeBusinessLoan) {
		return toShow
	}
	return false
}

// AutoInitiateApplication tells whether to automatically start the journey for a rejected/expired user who is eligible to apply again
func AutoInitiateApplication(sourceEntityID string) bool {
	return IsPFLSourcing(sourceEntityID)
}

// IsFallbackZoopPANLite tells whether to use Zoop PAN Lite API if hyperverge PAN detailed API fails
func IsFallbackZoopPANLite(sourceEntityID string) bool {
	return !general.InArr(sourceEntityID, []string{constants.ABFLID}) && !IsMuthootEDIPartner(sourceEntityID) && !IsMFLEMIPartner(sourceEntityID) && !IsPFLSourcing(sourceEntityID)
}

// MaskPANDoc tells whether to mask PAN in the KYC document
func MaskPANDoc(sourceEntityID string) bool {
	return IsPFLSourcing(sourceEntityID)
}

// ShowDropOffModule tells whether the under review user should be shown the drop off screen
func ShowDropOffModule(sourceEntityID string) bool {
	return IsPFLSourcing(sourceEntityID)
}

// EnableSliderCalc tells whether to enable slider on Loan Calculations Page for an anchor
func EnableSliderCalc(sourceEntityID string) bool {
	return sourceEntityID != constants.SupremeSolarID
}

// AllowLoanAmountEditCalc tells whether loan amount should be editable on Loan Calculations Page for an anchor / lender
func AllowLoanAmountEditCalc(sourceEntityID, lenderID string) bool {
	if lenderID == constants.KotakID {
		return false
	} else if general.InArr(sourceEntityID, []string{constants.TataNexarcID, constants.SupremeSolarID}) {
		return false
	}
	return true
}

// UserExpiryActionText returns the action text for expiry page
func UserExpiryActionText(sourceEntityID string) string {
	if IsPFLSourcing(sourceEntityID) {
		return "Initiate New Application"
	}
	return "Apply for New Loan"
}

// UserExpiryHelperText returns the helper text shown on the expiry page
func UserExpiryHelperText(sourceEntityID string) string {
	if IsPFLSourcing(sourceEntityID) {
		return "For any assistance, you can also reach out to us at our tollfree number: 1800-266-3201"
	}
	return "You can initiate a fresh application right away"
}

// UserExpiryDescription returns the description shown on the expiry page
func UserExpiryDescription(sourceEntityID, expiryAt string) string {
	if IsPFLSourcing(sourceEntityID) {
		return fmt.Sprintf("We regret to inform you that we are unable to process your application as the offer available earlier has expired on %s. Please try again for fresh application by clicking “Initiate New Application” button", expiryAt)
	}
	return "Your previous journey has expired"
}

// CollectAdditionalAddressDetails dynamic address allocation related source entity IDs
// TODO: return dynamic fields to be collected as well
func CollectAdditionalAddressDetails(sourceEntityID string) bool {
	return IsIIFLSourcing(sourceEntityID)
}

// IsOfferFlowV2 returns if offer flow v2 is enabled
func IsOfferFlowV2(userID, sourceEntityID string) bool {
	if IsPFLEducationLoanJourney(sourceEntityID) {
		return true
	}
	if sourceEntityID == constants.ABCDMarketPlaceID {
		return true
	}
	if IsABFLBLSourcing(sourceEntityID) || sourceEntityID == constants.ABFLMarketplaceID || IsABFLPLSourcing(sourceEntityID) || IsMFLBLSourcing(sourceEntityID) || sourceEntityID == constants.SentinelTestID {
		return true
	}

	// if group is assigned; then user is on configs built via journey builder - meaning offer flow is enabled
	groupID := userjourney.GetGroupID(userID)
	if groupID != "" {
		return true
	}

	if enabled, _ := OfferFlowV2(sourceEntityID); enabled {
		return featureflag.Get(userID, FlagOfferFlowV2)
	}
	return false
}

// IsCoApplicantJourney basically tells whether a user has ability to add child co-applicants.
// This is a check on the parentUserID
func IsCoApplicantJourney(userID, sourceEntityID string) bool {
	if IsABFLBLSourcing(sourceEntityID) {
		metadata, err := userjourney.GetUserJourneyMetadata(userID)
		if err != nil {
			errorHandler.ReportToSentryWithFields(map[string]interface{}{
				"user_id": userID,
			}, err)
			logger.WithUser(userID).Error("Error in Fetching Metadata column", err)
			return false
		}
		return metadata.LoanVariant == constants.LoanVariantCoApplication
	}
	return false

}

func IsMultiUserJourney(userID, sourceEntityID string) bool {
	metadata, err := userjourney.GetUserJourneyMetadata(userID)
	if err != nil {
		errorHandler.ReportToSentryWithFields(errorHandler.ErrorFields{"userID": userID}, err)
		logger.WithUser(userID).Error("Error in Fetching Metadata column", err)
		return false
	}
	return metadata.LoanVariant == constants.LoanVariantCoApplication || general.InArr(metadata.Constitution, []string{constants.PublicLimited, constants.PrivateLimited, constants.Partnership, constants.LimitedLiability, constants.Companies})
}

func IsCompanyConstitutionJourney(userID, sourceEntityID string) bool {
	metadata, err := userjourney.GetUserJourneyMetadata(userID)
	if err != nil {
		errorHandler.ReportToSentryWithFields(map[string]interface{}{
			"user_id": userID,
		}, err)
		logger.WithUser(userID).Error("Error in Fetching Metadata column", err)
		return false

	}
	return general.InArr(metadata.Constitution, []string{constants.PrivateLimited, constants.PublicLimited, constants.Companies})

}

func IsPartnershipConstitutionJourney(userID, sourceEntityID string) bool {
	metadata, err := userjourney.GetUserJourneyMetadata(userID)
	if err != nil {
		errorHandler.ReportToSentryWithFields(map[string]interface{}{
			"user_id": userID,
		}, err)
		logger.WithUser(userID).Error("Error in Fetching Metadata column", err)
		return false

	}
	return general.InArr(metadata.Constitution, []string{constants.Partnership, constants.LimitedLiability})

}

func IsProprietorshipConstitutionJourney(userID, sourceEntityID string) bool {
	metadata, err := userjourney.GetUserJourneyMetadata(userID)
	if err != nil {
		errorHandler.ReportToSentryWithFields(map[string]interface{}{
			"user_id": userID,
		}, err)
		logger.WithUser(userID).Error("Error in Fetching Metadata column", err)
		return false
	}
	// In old case, we used to not update constiution in userjourney table. So Backfill it using userbusiness table.
	if metadata.Constitution == "" {
		userbusinessResult, err := userbusiness.Get(context.Background(), userID)
		if err != nil {
			logger.WithUser(userID).Error("Error in Fetching user business data", err)
			return false
		}
		err = userjourney.UpdateMetadata(userID, []general.JSONData{
			{
				Key:   userjourney.MetadataConstitution,
				Value: userbusinessResult.Constitution,
			},
		})
		if err != nil {
			errorHandler.ReportToSentryWithFields(map[string]interface{}{
				"user_id": userID,
			}, err)
			logger.WithUser(userID).Error("Error in updating user journey data", err)
			return false

		}
		metadata.Constitution = userbusinessResult.Constitution

	}
	return general.InArr(metadata.Constitution, []string{constants.Proprietorship})

}

// IsUserEsignActivityRequired - Used to check if the user level activity log required for esigning agreement.
// Generally it is only at loan application level - loan_esigned.
func IsUserEsignActivityRequired(userID, sourceEntityID string) bool {
	if IsABFLBLSourcing(sourceEntityID) && IsCoApplicantJourney(userID, sourceEntityID) {
		return true
	}

	return false
}

// IsUserCoApplicant : This tells whether a user is a coApplicant or not.
// Logic: Checks if the user is in multi_user_loan_relations table as a user and should be active.
func IsUserCoApplicant(userID, sourceEntityID string) bool {
	if IsABFLBLSourcing(sourceEntityID) {
		valid, err := multiuserloanrelations.IsUserCoApplicant(userID)
		if err != nil {
			errorHandler.ReportToSentryWithFields(map[string]interface{}{
				"userID": userID,
			}, err)
			logger.WithUser(userID).Error("Error in Fetching IsUserCoApplicant", err)
		}
		return valid
	}
	return false
}

// IsBankConnectSessionFlow return if new bank connect session flow is enabled
func IsBankConnectSessionFlow(userID, sourceEntityID string, forceDisable bool) bool {
	if forceDisable {
		return false
	}
	if IsPFLSourcing(sourceEntityID) || IsPFLEDUSourcing(sourceEntityID) || sourceEntityID == constants.TataPLID {
		return true
	}

	if sourceEntityID == constants.VyaparID {
		return true
	}
	if IsABFLBLSourcing(sourceEntityID) || sourceEntityID == constants.ABFLMarketplaceID {
		return true
	}
	if sourceEntityID == constants.MoneyControlID || IsABFLPLSourcing(sourceEntityID) {
		return true
	}
	if IsMFLBLSourcing(sourceEntityID) {
		return true
	}
	// Platforms migrated from Perfios will have session flow enabled
	if MigrateToBankConnectFromPerfios(userID, sourceEntityID) {
		return true
	}
	if IsMuthootCLPartner(sourceEntityID) || IsMuthootEDIPartner(sourceEntityID) || IsMFLEMIPartner(sourceEntityID) {
		return true
	}
	return false
}

// OfferNextCall tells whether to select or accept offer on the offer slider page
func OfferNextCall(userID, sourceEntityID, moduleName string) string {
	if sourceEntityID == constants.TataNexarcID {
		return "acceptOffer"
	} else if IsABFLPLSourcing(sourceEntityID) {
		return "acceptOffer"
	} else if enabled, _ := OfferFlowV2(sourceEntityID); enabled && featureflag.Get(userID, FlagOfferFlowV2) {
		return "acceptOffer"
	} else if IsABFLBLSourcing(sourceEntityID) {
		switch moduleName {
		case usermodulemapping.OfferSelection:
			return "selectOffer"
		case usermodulemapping.Fresh:
			return "acceptOffer"
		}
	} else if sourceEntityID == constants.MoneyControlID {
		return "acceptOffer"
	}

	return "selectOffer"
}

// SkipFreshLoan returns true if fresh loan application status is skipped
func SkipFreshLoan(sourceEntityID string) bool {
	return general.InArr(sourceEntityID, []string{constants.TataBNPLID, constants.TataPLID})
}

func EndJourneyOnOfferAcceptance(sourceEntityID, lenderID, constitution string) bool {
	return (EnablePvtLtdJourney(sourceEntityID) && constitution != constants.Proprietorship) || (sourceEntityID == constants.TataNexarcID && general.InArr(lenderID, []string{constants.ABFLID, constants.TataCapitalID}))
}

func ShowAccountTypeInBusinessDetails(userID, sourceEntityID string) bool {
	loanType, _, _ := GetLoanType(sourceEntityID)

	return IsTemporalFlow(userID, sourceEntityID, usermodulemapping.PersonalInfo) && loanType == constants.LoanTypeBusinessLoan && IsIIFLBLSourcing(sourceEntityID)
}

func SkipPreLoanForFlow(userID, sourceEntityID string) bool {
	loanType, _, _ := GetLoanType(sourceEntityID)

	return (IsTemporalFlow(userID, sourceEntityID, usermodulemapping.PersonalInfo)) &&
		loanType == constants.LoanTypePersonalLoan && IsIIFLPLSourcing(sourceEntityID)
}

// SendDisbursalEmailToCustomer after disbursal has completed, other than dmi send email to customer
func SendDisbursalEmailToCustomer(lenderID string) bool {
	return !general.InArr(lenderID, []string{constants.DMIID, constants.ABFLID, constants.ABFLPLID})
}

// CoolOffDaysForLoanClosure starting from disbursal date, user can able to close the loan within the specified cooloof days, after that
// user cant able to request the loan closure
func CoolOffDaysForLoanClosure(sourceEntityID, lenderID string) int {
	if sourceEntityID == constants.TataPLID {
		switch lenderID {
		case constants.IIFLID:
			return 7
		case constants.AxisBankID:
			return 3
		case constants.DMIID:
			return 5
		case constants.TataCapitalID:
			return 3
		}
	}
	return 0
}

// DisableLocationPermissionAtReviewScreen at review screen, while signing agreement, dot ask for user location for axis
func DisableLocationPermissionAtReviewScreen(lenderID string) bool {
	return lenderID == constants.AxisBankID
}

// IsFreshModuleApplicable flag tells at the time of offer acceptance if FRESH module needs to be marked as completed
func IsFreshModuleApplicable(userID, sourceEntityID string) bool {
	return IsABFLBLSourcing(sourceEntityID) || ((IsIIFLBLSourcing(sourceEntityID) || sourceEntityID == constants.SwiggyID) && IsCurrentModuleEnabled(userID, sourceEntityID))
}

func ShowBureauConsent(sourceEntityID string) bool {
	return IsPFLSourcing(sourceEntityID)
}

func ShowSalariedAccountMessage(sourceEntityID string) bool {
	return general.InArr(sourceEntityID, []string{constants.HousingID})
}

func GetBureauConsentText(sourceEntityID string) string {
	if IsPFLSourcing(sourceEntityID) {
		return htmltemplates.PoonawallaConsentText
	}
	return ""
}

func GetCustomDSALogoURL(sourceEntityID string) string {
	if sourceEntityID == constants.FloBizMuthootCLDSAID {
		return "https://finbox-cdn.s3.ap-south-1.amazonaws.com/assets/FLOBIZ-LOGO.png"
	}
	return ""
}

// IsOrganizationApplicable tells whether to use current modules or not
func IsOrganizationApplicable(userID, sourceEntityID, organizationID string) bool {
	return general.InArr(organizationID, []string{constants.TataOrganizationID, constants.ABFLOrganizationID})
}

// IsCurrentModuleEnabled checks if a user is eligible for the current module flow
func IsCurrentModuleEnabled(userID, sourceEntityID string) bool {
	if IsPFLEducationLoanJourney(sourceEntityID) || sourceEntityID == constants.ABFLMarketplaceID || sourceEntityID == constants.ABCDMarketPlaceID || IsABFLPLSourcing(sourceEntityID) {
		return true
	}
	// if group is assigned; then user is on configs built via journey builder - meaning tsm (current modules) is enabled
	groupID := userjourney.GetGroupID(userID)
	if groupID != "" {
		return true
	}
	if enabled, _ := CurrentModuleFlow(sourceEntityID); enabled {
		return featureflag.Get(userID, FlagCurrentModule)
	}
	return false
}

func UseLenderNameInNarration(lenderID string) (bool, string) {
	switch lenderID {
	case constants.IIFLID:
		return true, "IIFL Finance"
	case constants.ArthanLenderID:
		return true, "Arthan Finance"
	default:
		return false, ""
	}
}

func SendSMSSignAgreement(lenderID string) bool {
	return general.InArr(lenderID, []string{constants.PoonawallaFincorpID, constants.ABFLID, constants.ABFLPLID})
}

func GetParentSourceEntityID(userID string, sourceEntityID *string) {
	if IsIIFLPLSourcing(*sourceEntityID) {
		*sourceEntityID = constants.IIFLID
	} else if IsIIFLBLSourcing(*sourceEntityID) {
		*sourceEntityID = constants.IIFLBLID
	} else if IsOneMuthootPartner(*sourceEntityID) {
		*sourceEntityID = constants.OneMuthootID
	} else if IsMuthootCLPartner(*sourceEntityID) {
		*sourceEntityID = constants.MuthootCLID
	} else if IsABFLPLSourcing(*sourceEntityID) {
		*sourceEntityID = constants.ABFLPLID
	} else if IsABFLBLSourcing(*sourceEntityID) {
		*sourceEntityID = constants.ABFLID
	} else if IsMFLBLSourcing(*sourceEntityID) {
		*sourceEntityID = constants.MFLBLID
	} else if IsPFLEDUSourcing(*sourceEntityID) {
		*sourceEntityID = constants.PFLEducationLoanSEID
	} else if IsPFLSourcing(*sourceEntityID) {
		*sourceEntityID = constants.PoonawallaFincorpID
	}
}

// IsKYCServiceUsedExplicitly flag is used to override isLastKYCModule check. This flag should be true if one
// uses kyc service apis outside of KYCModule struct Based approach of lending journies. For instance for KreditBee,
// it is dependent on their getModules API, hence it is true
func IsKYCServiceUsedExplicitly(sourceEntityID string, lenderID string) bool {
	if sourceEntityID == constants.TataPLID && lenderID == constants.TDLKreditBeeID {
		return true
	}
	return false
}

func HideCloseOnWait(sourceEntityID string) bool {
	return IsABFLBLSourcing(sourceEntityID) || IsABFLPLSourcing(sourceEntityID)
}

func IsTdlTcapPLFlow(userID string, sourceEntityID, lenderID, loanApplicationID string) bool {
	if sourceEntityID != constants.TataPLID {
		return false
	}
	if lenderID == "" {
		if loanApplicationID == "" {
			lenderID = preselectedlender.Get(userID)
		} else {
			loanApp, _ := loanapplication.Get(context.TODO(), loanApplicationID)
			lenderID = loanApp.LenderID
		}
	}
	if lenderID != constants.TataCapitalID {
		return false
	}
	value := GetTDLLoanType(userID, constants.TataPLID)
	return value == "FRESH"
}

func CheckOnlyLiveness(userID string, sourceEntityID, lenderID, loanApplicationID string) bool {
	return IsTdlTcapPLFlow(userID, sourceEntityID, lenderID, loanApplicationID)
}

func GetTDLLoanType(userID, sourceEntityID string) string {
	if sourceEntityID != constants.TataPLID {
		return ""
	}
	var value string
	query := `SELECT coalesce(partner_data::jsonb->>'loanType'::TEXT, 'FRESH') as value 
	from users where user_id = $1 and source_entity_id =$2`
	err := database.Get(&value, query, userID, sourceEntityID)
	if err != nil {
		logger.WithUser(userID).Errorln(err)
	}
	return value
}

func GetCustomDescription(userID, loanApplicationID, sourceEntityID, lenderID, loanType string) string {
	if sourceEntityID != constants.TataPLID {
		return ""
	}
	if lenderID == "" {
		var err error
		if loanApplicationID == "" {
			lenderID = preselectedlender.Get(userID)
		} else {
			lenderID, err = loanapplication.GetLender(loanApplicationID)
			if err != nil {
				logger.WithUser(userID).Error(err)
			}
		}
		if lenderID == "" {
			return ""
		}
	}
	lenderName := constants.LenderNamesMap[lenderID]
	if loanType == "" && sourceEntityID == constants.TataPLID {
		loanType = GetTDLLoanType(userID, sourceEntityID)
	}
	return fmt.Sprintf(`{"lender": "%s", "loanType": "%s"}`, lenderName, loanType)
}
func SkipPreEMIAddition(sourceEntityID string) bool {
	return IsABFLBLSourcing(sourceEntityID) ||
		IsSuperMoneyJourney("", sourceEntityID) ||
		IsPFLSourcing(sourceEntityID)
}

func BypassLoanStatusChecks(lenderID string) bool {
	return lenderID == constants.TDLKreditBeeID
}

// UseKYCServiceFlowForSourceEntity checks if sourceentity is enabled for kyc-service.
func UseKYCServiceFlowForSourceEntity(sourceEntityID string) bool {
	return IsMintifiAsLender(sourceEntityID)
}

// IsKYCServiceFlowEnabled checks if KYC service flow enabled for a source-entity or
// a feature flag is set
func IsKYCServiceFlowEnabled(sourceEntityID, userID string) bool {
	if UseKYCServiceFlowForSourceEntity(sourceEntityID) {
		return true
	}
	return featureflag.Get(userID, FlagUseKYCService)
}

// IsKYCServiceWorkflowEnabled checks if kyc service workflow is enabled for
// multi user kyc
func IsKYCServiceWorkflowEnabled(userID string) bool {
	return featureflag.Get(userID, FlagUseKYCServiceWorkflow)
}

// GetGSTVendor returns the vendor that should be used when fetching GST-related information for given sourceEntityID
func GetGSTVendor(userID, sourceEntityID string) string {
	switch {
	case IsMuthootCLPartner(sourceEntityID):
		return constants.GSTVendorOnGrid
	case IsABFLBLSourcing(sourceEntityID):
		return constants.GSTVendorKarza
	case IIFLBLPolicy(userID, sourceEntityID):
		return constants.GSTVendorOnGrid
	}
	return ""
}

type AdditionalDocumentName struct {
	Count              int
	CaptureTitle       string
	CaptureDescription string
	Permission         structs.Permission
}

func GetAdditionalDocNames(sourceEntityID string) (additionalDocsMap map[string]AdditionalDocumentName) {

	if IsMintifiAsLender(sourceEntityID) {
		additionalDocsMap = map[string]AdditionalDocumentName{
			"CANCELLED_CHEQUE": {
				Count: 4,
			},
		}
	} else if IsMuthootCLPartner(sourceEntityID) {
		additionalDocsMap = map[string]AdditionalDocumentName{
			"STORE_PHOTO": {
				Count:              1,
				CaptureTitle:       "Shop Photo",
				CaptureDescription: "Upload the photo of shop with name board clearly visible",
				Permission: structs.Permission{
					Mandatory: []string{"business_location"},
				},
			},
			"STOCK_PHOTO": {
				Count:              1,
				CaptureTitle:       "Stock photo",
				CaptureDescription: "Upload the photo of your stock in shop",
			},
			"SELFIE_WITH_STORE": {
				Count:              1,
				CaptureTitle:       "Selfie with shop",
				CaptureDescription: "Upload your selfie with your shop in background",
			},
		}
	}

	return
}

// Variable if user can upload any one document or all document

func IsMulitpleAdditionalDocsRequired(lenderID string) bool {
	return general.InArr(lenderID, []string{constants.MintifiID, constants.MuthootCLID, constants.TataCapitalID, constants.MFLBLID})
}

// SendEmailOnLoanClosureRequest returns whether email has to be sent to corresponding sourceEntityID/Lenders etc. upon a user request for loanClosure
func SendEmailOnLoanClosureRequest(sourceEntityID string, lenderID string) (toSend bool, namesList []string, emailList []string, subjectHTML string, bodyHTML string) {
	// if general.InArr(lenderID, []string{constants.DMIID, constants.KreditBeeID, constants.PrefrID}) && sourceEntityID == constants.TataPLID {
	// 	return true, []string{"tata digital"}, []string{"<EMAIL>"}, emaillib.TataPLLoanClosureRequestSubject, emaillib.TataPLLoanClosureRequestHTML
	// }
	return false, []string{}, []string{}, "", ""
}

type AutoPayModesDescription struct {
	Download string `json:"download"`
	Print    string `json:"print"`
	Upload   string `json:"upload"`
}

// GetMandateFormUploadDescription returns the description to be displayed on the autp page page
func GetMandateFormUploadDescription(sourceEntityID string) AutoPayModesDescription {

	autoPaymodesDesc := AutoPayModesDescription{
		Download: "You can get it via email or you can directly download it.",
		Print:    "Form will look like this, with your signature as shown.",
		Upload:   "After signing the document, you have to upload it by taking a clear image of the document.",
	}

	if IsMintifiAsLender(sourceEntityID) {
		autoPaymodesDesc.Print = "Form will look like this, with your signature and stamp as shown."
		autoPaymodesDesc.Upload = "After signing the document, you have to upload the entire document in pdf format."

	}

	return autoPaymodesDesc

}

type AutoPayDocumentUploadModes struct {
	Name              string   `json:"name"`
	AllowedExtensions []string `json:"allowedExtensions"`
}

// GetMandateFormUploadUploadModes return the modes in which documents can be uploaded.
func GetMandateFormUploadUploadModes(sourceEntityID string) []AutoPayDocumentUploadModes {

	GalleryAllowedExtensions := GetMandateFormUploadExtensions(sourceEntityID)
	autoPayModes := []AutoPayDocumentUploadModes{
		{
			Name:              "gallery",
			AllowedExtensions: GalleryAllowedExtensions,
		},
		{
			Name: "camera",
		}}

	if IsMintifiAsLender(sourceEntityID) { // minitfy requires only gallery, no camera
		autoPayModes = []AutoPayDocumentUploadModes{{
			Name:              "gallery",
			AllowedExtensions: GalleryAllowedExtensions,
		},
		}
	}

	return autoPayModes

}

// GetMandateFormUploadExtensions returns Allowed file extensions to upload the document in auto pay mode.
func GetMandateFormUploadExtensions(sourceEntityID string) []string {

	if IsMintifiAsLender(sourceEntityID) {
		return []string{".pdf"}
	}

	return []string{".png", " .jpg", ".jpeg", ".pdf"}
}
func SkipSetupAutopayBanner(sourceEntityID string) bool {
	return IsSaraloanAsLender(sourceEntityID) || IsMintifiAsLender(sourceEntityID)
}

// GetBureauThreshold returns the bureau threshold for a given sourceEntityID
func GetBureauThreshold(sourceEntityID string) int {
	if sourceEntityID == constants.MoneyControlID {
		return 0
	}
	if IsABFLBLSourcing(sourceEntityID) || IsABFLPLSourcing(sourceEntityID) {
		return 0
	}
	if IsMuthootEDIPartner(sourceEntityID) || IsMFLEMIPartner(sourceEntityID) || IsMFLBLSourcing(sourceEntityID) {
		return 0
	}

	if IsMuthootCLPartner(sourceEntityID) {
		return math.MinInt
	}
	return 300
}

func AllowConfirmTxn(sourceEntityID string) bool {
	return IsSaraloanAsLender(sourceEntityID) || IsMintifiAsLender(sourceEntityID)
}

// MigrateToBankConnectFromPerfios returns whether to call BankConnect for statement extraction, analysis and fraud analysis (migrating from Perfios flow)
func MigrateToBankConnectFromPerfios(userID string, sourceEntityID string) bool {
	if sourceEntityID == constants.VyaparID && preselectedlender.Get(userID) == constants.IIFLID {
		return true
	}
	// Below check was in use when a handful of DSAs were to be migrated to Bank Connect Session flow from Perfios flow.
	// if unblockpolicy.IsUnblocked(sourceEntityID, constants.UnblockBankConnect) {
	// 	return true
	// }
	if IsIIFLSourcing(sourceEntityID) {
		return true
	}
	return false
}

func RunPerfiosFCU(userID string, sourceEntityID string) bool {
	if MigrateToBankConnectFromPerfios(userID, sourceEntityID) {
		var mode string
		query := `SELECT coalesce(mode, '') from bank_connect_details where user_id = $1 and status = $2 and (perfios_transaction_id is null or length(perfios_transaction_id) = 0) order by created_at desc limit 1`
		_ = database.Get(&mode, query, userID, constants.BankConnectStatusCompleted)
		return mode == constants.BankConnectModePDF
	}
	return false
}

func IsKarzaOTPFlow(sourceEntityID string) bool {
	return IsABFLBLSourcing(sourceEntityID)
}

func IsAPIStackFlow(userID, sourceEntityID string) bool {
	key := userID + apiStackSuffix
	v, _ := redis.Get(context.Background(), key)
	if v != "" {
		return true
	}

	flagSet := featureflag.Get(userID, FlagABFLAPIStack)
	if flagSet {
		if err := redis.Set(key, "1", redis.LongRedisTTL); err != nil {
			logger.WithUser(userID).Errorln(err)
		}
	}

	return flagSet
}

// IsActiveJourneyBlocked returns whether fresh sourcing/active journey (non-disbursed case) is blocked for a source entity
func IsActiveJourneyBlocked(sourceEntityID string) bool {
	var status int
	query := `SELECT status from source_entity where source_entity_id = $1`
	err := database.Get(&status, query, sourceEntityID)
	if err != nil {
		log.Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(fmt.Errorf("error: %v, source_entity_id: %s", err, sourceEntityID))
	}
	return status == constants.SourceEntityStatusActiveJourneyBlocked
}

// IsPreApprovedJourney returns whether the journey is pre-approved for a source entity in first bool and wether to call bre in second bool
func IsPreApprovedJourney(sourceEntityID string) (bool, bool) {
	if sourceEntityID == constants.DSAPreApprovedABFLID {
		return true, false
	}
	if general.InArr(sourceEntityID, []string{constants.DSAPreApprovedABFLPartnerABCID, constants.DSAPreApprovedABFLPartnerABCIDV2, constants.DSAPreApprovedABFLPartnerDSTID, constants.DSAPreApprovedABFLDigiPartner, constants.DSAPreApprovedABFLDistribution, constants.DSAPreApprovedABFLABG}) {
		return true, true
	}
	return false, false
}

// IsSetOfferFromPreApproved returns whether the offer should be populated from pre_approved table
func IsSetOfferFromPreApproved(sourceEntityID string) bool {
	return general.InArr(sourceEntityID, []string{constants.DSAPreApprovedABFLID})
}

func DisableChangePAN(sourceEntityID string) bool {
	return general.InArr(sourceEntityID, []string{constants.IIFLBLTopUpID, constants.DSAPreApprovedABFLID, constants.DSAPreApprovedABFLPartnerABCID, constants.DSAPreApprovedABFLPartnerABCIDV2, constants.DSAPreApprovedABFLPartnerDSTID, constants.DSAPreApprovedABFLABG})
}

// AllowSkipGST returns true if partner allows to skip adding GST/UAN, false if GST/UAN is mandatory.
func AllowSkipGST(userID, sourceEntityID string) bool {
	if IsAPIStackFlow(userID, sourceEntityID) {
		return false
	}

	return sourceEntityID != constants.KredMintABFLDSAID
}

func ShowFinalOfferText(lenderID string, boosterEligible bool) bool {
	// Should show final offerText for
	return lenderID != constants.FibeID
}

// IsLoanBookedOnLender checks if the LoanApplication has both new and old loan application no
// signifying if the loan is registered at lender's end
func IsLoanBookedOnLender(sourceEntityID string, l loanapplication.LoanApplication) bool {
	if IsMuthootEDIPartner(sourceEntityID) || IsMFLEMIPartner(sourceEntityID) {
		return l.LoanApplicationNo != "" && l.OldApplicationNo != ""
	}
	return l.Status == constants.LoanStatusDisbursed
}

// KYCServiceInactivationDocTypes : In kycservice integration, everytime a callback is received from kycservice,
// previous kyc attempt documents are marked inactive and new ones are inserted. This journey flag helps to mark
// only a certain type of docs as inactive, preserving the status of other documents that maybe collected outside KYC.
func KYCServiceInactivationDocTypes(sourceEntityID, lenderID string) (applicable bool, inactivateDocTypes []string) {
	if sourceEntityID == constants.TataPLID && lenderID == constants.TDLKreditBeeID {
		return true, []string{constants.DocTypePhoto}
	}
	return false, []string{}
}

// GetBureauRefetchThreshold returns the number of days before a bureau can be re-fetched for a given sourceEntityID
func GetBureauRefetchThreshold(sourceEntityID string) float64 {
	// default value
	var bureauRefetchThreshold float64
	switch {
	case IsABFLBLSourcing(sourceEntityID):
		bureauRefetchThreshold = constants.ABFLBureauRefetchThreshold
	default:
		bureauRefetchThreshold = constants.DefaultBureauRefetchThreshold
	}

	return bureauRefetchThreshold
}

func IsPFLEducationLoanJourney(sourceEntityID string) bool {
	if IsPFLEDUSourcing(sourceEntityID) {
		return true
	}
	return sourceEntityID == constants.PFLEducationLoanSEID
}

// IsTopUp returns whether it's a top-up journey, default lender, default source entity & loan type
func IsTopUp(sourceEntityID string) (bool, string, string, string) {
	if sourceEntityID == constants.IIFLBLTopUpID {
		return true, constants.IIFLID, constants.IIFLBLID, constants.LoanTypeBusinessLoan
	}
	return false, "", "", ""
}

func SkipAllDedupe(sourceEntityID string) bool {
	isTopUpFlow, _, _, _ := IsTopUp(sourceEntityID)
	return isTopUpFlow
}

func GetLenderPaymentPartner(lenderID string) string {
	if lenderID == constants.IIFLID {
		return constants.PaymentPartnerQuickPay
	}
	return constants.PaymentPartnerCashfree
}

func IsTDLRepeatLoanLenders(lenderID, sourceEntityID string) bool {
	return sourceEntityID == constants.TataPLID && general.InArr(lenderID, []string{constants.TDLKreditBeeID})
}

func UseOfferMetadata(sourceEntityID string) bool {
	if sourceEntityID == constants.TataPLID {
		return true
	}
	if IsABFLBLSourcing(sourceEntityID) || IsABFLPLSourcing(sourceEntityID) {
		return true
	}
	return false
}

func SkipMaskedAadhaarMatchingKYCCheck(lenderID string) bool {
	return lenderID == constants.PoonawallaFincorpID
}

// IsEsignAttemptVersion2 : This Flag will tell us whether we need to user version 2 for EsignAttempt table or not.
// In Version 2, users status will be maintained in user_esign_attempt table, while in version 1 everything is maintained in esign_attempt table
func IsEsignAttemptVersion2(userID string, sourceEntityID string) bool {
	if IsABFLBLSourcing(sourceEntityID) {
		if IsMultiUserJourney(userID, sourceEntityID) {
			return true
		}
	}
	return false
}

// CheckTradeNameFromGST returns whether to do bank account holder name match with GST trade name & fallback on legal name if name match with trade name fails
func CheckTradeNameFromGST(sourceEntityID string) (bool, bool) {
	if IsABFLBLSourcing(sourceEntityID) {
		return true, false
	}
	if IsMuthootCLPartner(sourceEntityID) {
		return true, true
	}
	return false, false
}
func GetClientQueue(sourceEntityID string) string {

	parentSourceEntityID := sourceEntityID
	GetParentSourceEntityID("", &parentSourceEntityID)

	switch parentSourceEntityID {
	case constants.PoonawallaFincorpID:
		return constants.PFLTaskQueue
	case constants.TataPLID, constants.TataBNPLID:
		return constants.TDLTaskQueue
	default:
		if IsIIFLSourcing(sourceEntityID) {
			return constants.IIFLTaskQueue
		}
		if IsABFLBLSourcing(sourceEntityID) || IsABFLPLSourcing(sourceEntityID) {
			return constants.ABFLTaskQueue
		}
		return constants.LendingGenericTaskQueue
	}
}

// IsPincodeOptionalPANInfo return whether pincode check is optional during PAN confirmation
func IsPincodeOptionalPANInfo(sourceEntityID string) bool {
	return general.InArr(sourceEntityID, []string{constants.DSAPreApprovedABFLPartnerABCID, constants.DSAPreApprovedABFLPartnerABCIDV2, constants.DSAPreApprovedABFLPartnerDSTID, constants.DSAPreApprovedABFLABG})
}

func IsMultiUserConstitutionJourney(userID, sourceEntityID string) bool {
	metadata, err := userjourney.GetUserJourneyMetadata(userID)
	if err != nil {
		errorHandler.ReportToSentryWithoutRequest(err)
		logger.WithUser(userID).Error("Error in Fetching Metadata column", err)
		return false
	}
	return general.InArr(metadata.Constitution, []string{constants.PublicLimited, constants.PrivateLimited, constants.Partnership, constants.LimitedLiability, constants.Companies})
}

// EnableMarketplaceOffers returns whether to enable marketplace offers, source entity id of the marketplace journey and lender name
func EnableMarketplaceOffers(sourceEntityID string) (bool, string, string) {
	if general.InArr(sourceEntityID, []string{constants.ABFLUdyogPlusWebsiteBLPROD, constants.ABFLUdyogPlusWebsiteBLNonPROD}) {
		return true, constants.ABFLMarketplaceID, "ABFL"
	}
	return false, "", ""
}

// ThresholdDaysToBringInactiveUsersToMLO threshold days to fetch in active users
func ThresholdDaysToBringInactiveUsersToMLO(sourceEntityID string) int {
	if sourceEntityID == constants.TataPLID {
		return 8
	}
	return -1
}

// ToRoundInterest returns whether to round interest to nearest integer value for a platform when showing offer details to borrower
func ToRoundInterest(sourceEntityID string) bool {
	return sourceEntityID == constants.ABFLMarketplaceID
}

// ShowLenderPartnerDisbursal return whether to show lending partner info & logo in Disbursal screen
func ShowLenderPartnerDisbursal(sourceEntityID string) bool {
	return sourceEntityID == constants.ABFLMarketplaceID
}

// ExpireUsersOnDisqualification returns whether to expire user journey on disqualification, expiry type & expiry offset
func ExpireUsersOnDisqualification(sourceEntityID string, eventType string, eventDescription string) (bool, string, int) {
	if sourceEntityID == constants.TataPLID && eventType == constants.ActivityUserDisqualified && strings.Contains(eventDescription, "all active policies") {
		return true, constants.ExpiryTypeArchival, 30
	}
	return false, "", 0
}

// GetMarketplaceExplorationWindow returns the window duration (in days) before which a marketplace journey can be initiated
func GetMarketplaceExplorationWindow(sourceEntityID string) int {
	if sourceEntityID == constants.ABFLMarketplaceID {
		return 45
	}
	return 45
}

// ShouldSkipBoostQualification returns if boost qualification should be called after BC and GST is completed.
// For all not API stack cases it will return false. For API stack cases which do not belong to ABFLWhatsApp it will
// return true.
func ShouldSkipBoostQualification(userID, sourceEntityID string, moduleName string) bool {
	if moduleName == usermodulemapping.BankConnect && featureflag.Get(userID, FlagBankConnectAPIStack) {
		return true
	}
	if moduleName == usermodulemapping.GST && featureflag.Get(userID, FlagGSTAPIStack) {
		return true
	}
	return false
}

// ShouldRedirectOnComplete returns value of `redirectOnComplete` key for post BC and GST module.
// In API stack if the boost qauli call was skipped either after BC or GST, it will be done via API call.
// This is required on FE to redirect or continue for API stack cases.
func ShouldRedirectOnComplete(userID, sourceEntityID string, moduleName string) bool {
	return ShouldSkipBoostQualification(userID, sourceEntityID, moduleName)
}

func ShowGRODetailsInFAQ(sourceEntityID, lenderID string) (bool, structs.GRODetails) {
	if IsABFLPLSourcing(sourceEntityID) || lenderID == constants.ABFLPLID {
		return true, structs.GRODetails{
			Name:       "Mr. Arijit Sen",
			Email:      "<EMAIL>",
			Number:     "080 – ********",
			Address:    "Aditya Birla Capital Limited,10th Floor, R-Tech Park, Nirlon Complex, Goregaon, Mumbai – 400063",
			ClientName: "ABFL",
		}
	}
	return false, structs.GRODetails{}
}

// IsTDLLender checking lender belongs to tdl pl platform
func IsTDLLender(sourceEntityID, lenderID string) bool {
	if sourceEntityID == constants.TataPLID {
		return general.InArr(lenderID, []string{constants.DMIID, constants.TDLKreditBeeID, constants.ABFLID, constants.MoneyViewID, constants.CasheID, constants.KisshtID, constants.PrefrID, constants.HDFCLenderID, constants.AxisBankID})
	}
	return false
}

// IsLenderGroupingFlow checks the flag indicating tdl lender grouping flow
func IsLenderGroupingFlow(userID, sourceEntityID string) bool {
	if sourceEntityID == constants.TataPLID {
		return featureflag.Get(userID, FlagLenderGrouping)
	}
	return false
}

// EnableRepeatLoan returns whether to enable repear loan for a source entity, max count of allowed disbursed loans & days before which a repeat loan application can be initiated
func EnableRepeatLoan(sourceEntityID string) (bool, int, int) {
	if sourceEntityID == constants.TataPLID {
		return true, 2, 45
	} else if sourceEntityID == constants.MoneyControlID {
		return true, -1, 30
	}
	return false, -1, -1
}

func GetYearRange(lenderID, sourceEntityID string) string {
	currentYear := time.Now().Year()
	nextYear := currentYear + 1
	switch lenderID {
	case constants.PoonawallaFincorpID:
		return fmt.Sprintf("%d-%d", currentYear, nextYear%100)
	default:
		return fmt.Sprintf("%d-%d", currentYear, nextYear)
	}
}

func AssignParentSourceFeatureFlags(sourceEntityID string) bool {
	return IsABFLPLSourcing(sourceEntityID) || IsABFLBLSourcing(sourceEntityID)
}

func IsEDIJourney(userID string, sourceEntityID string) bool {

	metadata, err := userjourney.GetUserJourneyMetadata(userID)
	if err != nil {
		errorHandler.ReportToSentryWithFields(map[string]interface{}{
			"user_id": userID,
		}, err)
		logger.WithUser(userID).Error("Error in Fetching Metadata column", err)
		return false
	}
	return metadata.InstallmentProgramme == constants.InstallmentProgrammeDaily
}

func IsEMIJourney(userID string, sourceEntityID string) bool {

	metadata, err := userjourney.GetUserJourneyMetadata(userID)
	if err != nil {
		errorHandler.ReportToSentryWithFields(map[string]interface{}{
			"user_id": userID,
		}, err)
		logger.WithUser(userID).Error("Error in Fetching Metadata column", err)
		return false
	}
	return metadata.InstallmentProgramme == constants.InstallmentProgrammeMonthly || metadata.InstallmentProgramme == ""
}

func GetInstallmentFrequency(userID, sourceEntityID string) (string, error) {
	metadata, err := userjourney.GetUserJourneyMetadata(userID)
	if err != nil {
		errorHandler.ReportToSentryWithFields(map[string]interface{}{
			"user_id": userID,
		}, err)
		logger.WithUser(userID).Error("Error in Fetching Metadata column", err)
		return "", err
	}
	if metadata.InstallmentProgramme == "" {
		metadata.InstallmentProgramme = constants.InstallmentProgrammeMonthly
	}
	return metadata.InstallmentProgramme, nil
}

// IsMultiOfferBoostQualification ... multi lender boost qualificcation
func IsMultiOfferBoostQualification(sourceEntityID string) bool {
	return sourceEntityID == constants.ABFLMarketplaceID
}

func CallLisaRedirectOnDisbursalCTA(lenderID string) bool {
	return lenderID == constants.FlexiLoansID
}

func HideLoanAmountOnDisbursalPage(sourceEntityID string) bool {
	return sourceEntityID == constants.ABFLMarketplaceID
}

func GetScheme(lenderID, installmentProgramme string) string {
	schemeMapping, exists := constants.LenderToInstallmentTypeToScheme[lenderID]
	if !exists {
		return ""
	}
	if scheme, ok := schemeMapping[installmentProgramme]; ok {
		return scheme
	}
	return ""
}

// IsSuperMoneyJourney ... super money journey, keeping UserId for future use just in case.
// It might help adding DSAs in future
func IsSuperMoneyJourney(userID, sourceEntityID string) bool {
	return sourceEntityID == constants.SuperMoneyID
}

// GetControlConfigs is used to fetch actions per source
func GetControlConfigs(sourceEntityID string) map[string][]string {
	switch sourceEntityID {
	case constants.DSAGpayABFLBL:
		return map[string][]string{
			"gpay": {"trigger_script"},
		}
	}
	return map[string][]string{}
}

func IsTopUpJourney(userID, sourceEntityID string) bool {

	if IsPFLSourcing(sourceEntityID) {
		userjourney, err := userjourney.GetUserJourneyMetadata(userID)
		if err != nil {
			logger.WithUser(userID).Error(err)

			return false
		}

		if userjourney.JourneyType == constants.PFLJourneyTopUP {
			return true
		}
	}

	return false
}

// GetEvalCodeFromConfigManagement : This journey flag is used for getting evaluation code from config management tables.
// This is done because there were multiple codes for single source. Currently only ABFL uses it. Created a journey flag to drive it.
func GetEvalCodeFromConfigManagement(sourceEntityID string) bool {
	return IsABFLBLSourcing(sourceEntityID)
}

// ShowHelplineNo returns whether to show helpline number or not
// is no. is present then front end will automatically
func ShowHelplineNo(sourceEntityID, moduleName string) string {
	if general.InArr(sourceEntityID, []string{constants.MoneyControlID, constants.DSAMoneyControlIncredID}) && !general.InArr(moduleName, []string{"REJECTED", "EXPIRED"}) {
		return "022-69716161"
	}
	return ""
}

func IsABFLFinarkeinEntity(sourceEntityID string) bool {
	return general.InArr(sourceEntityID, []string{constants.ABFLDSAGetVantage})
}

func CustomCreditTheme(sourceEntityID string) string {
	if IsMFLBLSourcing(sourceEntityID) {
		return "MFLBL"
	}
	return ""
}

func IsSuperMoneySourcing(sourceEntityID string) bool {
	return general.InArr(sourceEntityID, []string{constants.SuperMoneyID, constants.PrefrSuperMoneyID})
}
