// Package lenderservice implements the lender integration service
// for implemnetation any lender associated operations.
package lenderservice

import (
	"bytes"
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"finbox/go-api/conf"
	"finbox/go-api/constants"
	"finbox/go-api/functions/journey"
	"finbox/go-api/functions/logger"
	"finbox/go-api/functions/requestutils"
	"finbox/go-api/functions/retry"
	"finbox/go-api/functions/serviceslib"
	"finbox/go-api/functions/tracer"
	"finbox/go-api/infra/db"
	"finbox/go-api/internal/fbxerrors"
	"finbox/go-api/models/users"
	"finbox/go-api/utils/general"
	"finbox/go-api/utils/mapdecoder"
	"fmt"
	"io"
	"log"
	"net/http"
	"strconv"
	"strings"
	"time"
)

var (
	creds    = conf.LenderServiceCreds
	database = db.Mgr.DBConn
)

func DedupeCheck(ctx context.Context, reqBody *DedupeCheckReq) (*DedupeCheckResp, error) {
	if toMockLISA() {
		return mockDedupeCheck()
	}
	resourceName := DedupeCheckResource
	url := creds["baseURL"] + "dedupe"
	method := "GET"
	userID := reqBody.UserID

	payload, err := json.Marshal(reqBody)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, "", "", serviceslib.ErrorStatusCode, reqBody.UserID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	client := tracer.GetTraceableHTTPClient(nil, resourceName)
	req, err := requestutils.GetMockableHTTPRequest(userID, resourceName, method, url, bytes.NewBuffer(payload))
	resourceName = resourceName + ":" + reqBody.ApplicationReq.Intent
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Authorization", creds["api-key"])
	req.Header.Add("lending-url", conf.BaseURL)
	res, err := client.Do(req)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	if res.StatusCode != http.StatusOK {
		err := fmt.Errorf("unable to check dedupe at service")
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	var response LenderServiceResp
	if err := json.Unmarshal(body, &response); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	var appResp DedupeCheckResp
	if err := mapdecoder.JSONDecoder(response.Data, &appResp); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.SuccessStatusCode, userID, url, "", general.GetUUID())
	return &appResp, nil
}

func HunterCheck(ctx context.Context, reqBody *ApplicationReq) (*HunterCheckResp, error) {
	resourceName := HunterCheckResource
	url := creds["baseURL"] + "hunter"
	method := "POST"
	userID := reqBody.UserID

	payload, err := json.Marshal(reqBody)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, "", "", serviceslib.ErrorStatusCode, reqBody.UserID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	client := tracer.GetTraceableHTTPClient(nil, resourceName)
	req, err := requestutils.GetMockableHTTPRequest(userID, resourceName, method, url, bytes.NewBuffer(payload))

	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Authorization", creds["api-key"])
	req.Header.Add("lending-url", conf.BaseURL)
	res, err := client.Do(req)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	if res.StatusCode != http.StatusOK {
		err := fmt.Errorf("unable to check hunter at service")
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	var response LenderServiceResp
	if err := json.Unmarshal(body, &response); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	var appResp HunterCheckResp
	if err := mapdecoder.JSONDecoder(response.Data, &appResp); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.SuccessStatusCode, userID, url, "", general.GetUUID())
	return &appResp, nil
}

// GetBusinessDate retrieves current business date for a given lender
// This API is used to decide weather to call the lender API or not, If the business date is not the current date in
// lender system, then the subsequent lender APIs are not supposed to be called.
//
// Parameters:
//   ctx context.Context: Contextual information for the request.
//   reqBody *ApplicationReq: Pointer to a struct containing the request body, including the UserID and LenderID.
//
// Returns:
//   *BusinessDateInfo: containing the retrieved business date information.
//   error: Error encountered during the function execution, if any.

func GetBusinessDate(ctx context.Context, reqBody *ApplicationReq) (data *BusinessDateInfo, err error) {
	if (conf.ENV != conf.ENV_PROD && journey.IsMuthootEDIPartner(reqBody.SourceEntityID)) ||
		toMockLISA() {
		return mockBusinessDateAPI()
	}

	resourceName := BusinessDate
	url := creds["baseURL"] + "lms/details"
	method := "GET"
	userID := reqBody.UserID

	gobj := map[string]string{
		"url":    url,
		"strReq": "",
		"strRes": "",
		"userID": userID,
		"id":     general.GetUUID(),
	}

	// log the API call at the end
	defer func() {
		status := serviceslib.SuccessStatusCode
		errStr := ""
		if err != nil {
			status = serviceslib.ErrorStatusCode
			errStr = err.Error()
		}
		serviceslib.WriteToDB(resourceName, gobj["strReq"], gobj["strRes"], status, gobj["userID"], gobj["url"], errStr, gobj["id"])
	}()

	// generate payload from request body
	payload, err := json.Marshal(reqBody)
	if err != nil {
		logger.WithUser(userID).Error(err)
		return nil, err
	}
	gobj["strReq"] = string(payload)

	client := tracer.GetTraceableHTTPClient(nil, resourceName)
	req, err := http.NewRequest(method, url, bytes.NewBuffer(payload))
	if err != nil {
		logger.WithUser(userID).Error(err)
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Authorization", creds["api-key"])
	req.Header.Add("lending-url", conf.BaseURL)
	res, err := client.Do(req)
	if err != nil {
		logger.WithUser(userID).Error(err)
		return nil, err
	}
	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		logger.WithUser(userID).Error(err)
		return nil, err
	}
	gobj["strRes"] = string(body)

	if res.StatusCode != http.StatusOK {
		err := fmt.Errorf("unable to get business date from service")
		logger.WithUser(userID).Error(err)
		return nil, err
	}

	var response BusinessDateResp
	if err := json.Unmarshal(body, &response); err != nil {
		logger.WithUser(userID).Error(err)
		return nil, err
	}

	if errMsg := response.Error1; errMsg != "" {
		logger.WithUser(userID).Error("error from lender", response.Error1)
		return nil, fmt.Errorf(response.Error1)
	}

	if general.RemoveAllSpaces(response.Data.BusinessDate) == "" {
		err = fmt.Errorf(constants.ErrStrEmptyBusinessDate)
		logger.WithUser(userID).Error(err)
		return nil, err
	}

	return &response.Data, nil
}

// CheckBusinessDate is used to check if the lender system's business date is
// current date and not backdated or future dated date
// returns an error if current date is not business date
func CheckBusinessDate(ctx context.Context, userID, loanApplicationID string) (string, error) {
	// Prepare the LISA request for Create Applicant
	applicationReq, err := GetApplicationReqByUserAndLoan(ctx, userID, loanApplicationID, nil)
	if err != nil {
		logger.WithUser(userID).Error(err)
		return "", err
	}

	res, err := GetBusinessDate(ctx, &applicationReq)
	if err != nil {
		logger.WithUser(userID).Error(err)
		return "", err
	}

	// Get current date and compare with lender system business date
	loc, err := time.LoadLocation(constants.DefaultTimeZone)
	if err != nil {
		logger.WithUser(userID).Error(err)
		return res.BusinessDate, err
	}
	currentDate := time.Now().In(loc).Format(BusinessDateFormat)
	logger.DebugWithUser(userID, fmt.Sprintf("CurrentDate: %s, business Date: %s, date format used for current date: %s, is Date Same: %v", currentDate, res.BusinessDate, BusinessDateFormat, currentDate == res.BusinessDate))
	// If current date and business date did not match raise error
	if currentDate != res.BusinessDate {
		err = fmt.Errorf(constants.ErrStrCurrentAndBusinessDateMismatch)
		logger.WithUser(userID).Error(err)
		return res.BusinessDate, err
	}

	return res.BusinessDate, nil
}

func CreateApplicant(ctx context.Context, reqBody *ApplicationReq) (*ApplicationResp, error) {
	if general.InArr(conf.ENV, []string{conf.ENV_UAT11}) {

		if reqBody.LenderID == constants.LandTID {
			userObj, err := users.Get(reqBody.UserID)
			if err != nil {
				err = fmt.Errorf("no userDetails found err: %s", err.Error())
				logger.WithUser(reqBody.UserID).Error(err)
			}

			if general.InArr(userObj.PAN, constants.MockLANDTPANList) {
				return mockCreateApplicant()
			}
		}

		// return mockCreateApplicant()
	}
	if conf.ENV == conf.ENV_LOCAL {
		return mockCreateApplicant()
	}

	resourceName := CreateApplicantResource
	url := creds["baseURL"] + "applicant"
	method := "POST"
	userID := reqBody.UserID

	payload, err := json.Marshal(reqBody)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, "", "", serviceslib.ErrorStatusCode, reqBody.UserID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	client := tracer.GetTraceableHTTPClient(nil, resourceName)
	req, err := requestutils.GetMockableHTTPRequest(userID, resourceName, method, url, bytes.NewBuffer(payload))
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Authorization", creds["api-key"])
	req.Header.Add("lending-url", conf.BaseURL)
	res, err := client.Do(req)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	if res.StatusCode >= http.StatusInternalServerError {
		err := fmt.Errorf("lisa_create_applicant_fatal:http_status:%d", res.StatusCode)
		logger.WithUser(reqBody.UserID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	var response LenderServiceRespV2
	if err := json.Unmarshal(body, &response); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	// check if there is any error message from lisa.
	if errMsg := response.Error1; errMsg != "" {
		logger.WithUser(reqBody.UserID).Error(errMsg)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, errMsg, general.GetUUID())
		return nil, errors.New(errMsg)
	}
	var appResp ApplicationResp
	if err := mapdecoder.JSONDecoder(response.Data, &appResp); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.SuccessStatusCode, userID, url, "", general.GetUUID())
	return &appResp, nil
}

func UpdateApplicant(ctx context.Context, reqBody *ApplicationReq) (*ApplicationResp, error) {
	resourceName := UpdateApplicantResource
	url := creds["baseURL"] + "applicant"
	method := "PUT"
	userID := reqBody.UserID

	payload, err := json.Marshal(reqBody)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, "", "", serviceslib.ErrorStatusCode, reqBody.UserID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	client := tracer.GetTraceableHTTPClient(nil, resourceName)
	req, err := requestutils.GetMockableHTTPRequest(userID, resourceName, method, url, bytes.NewBuffer(payload))
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Authorization", creds["api-key"])
	req.Header.Add("lending-url", conf.BaseURL)
	res, err := client.Do(req)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	if res.StatusCode >= http.StatusInternalServerError {
		err := fmt.Errorf("lisa_update_applicant_fatal:http_status:%d", res.StatusCode)
		logger.WithUser(reqBody.UserID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	var response LenderServiceRespV2
	if err := json.Unmarshal(body, &response); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	// check if there is any error message from lisa.
	if errMsg := response.Error1; errMsg != "" {
		logger.WithUser(reqBody.UserID).Error(errMsg)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, errMsg, general.GetUUID())
		return nil, errors.New(errMsg)
	}

	var appResp ApplicationResp
	if err := mapdecoder.JSONDecoder(response.Data, &appResp); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.SuccessStatusCode, userID, url, "", general.GetUUID())
	return &appResp, nil
}

func CreateCoApplicant(ctx context.Context, reqBody *CoApplicantReq) (*CoApplicantResp, error) {
	if toMockLISA() {
		return mockCreateCoApplicant()
	}
	resourceName := CreateCoApplicantResource
	url := creds["baseURL"] + "coapplicant"
	method := "POST"
	userID := reqBody.UserID

	payload, err := json.Marshal(reqBody)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, "", "", serviceslib.ErrorStatusCode, reqBody.UserID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	client := tracer.GetTraceableHTTPClient(nil, resourceName)
	req, err := requestutils.GetMockableHTTPRequest(userID, resourceName, method, url, bytes.NewBuffer(payload))
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Authorization", creds["api-key"])
	req.Header.Add("lending-url", conf.BaseURL)
	res, err := client.Do(req)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	if res.StatusCode >= http.StatusInternalServerError {
		err := fmt.Errorf("lisa_create_coapplicant_fatal:http_status:%d", res.StatusCode)
		logger.WithUser(reqBody.UserID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	var response LenderServiceRespV2
	if err := json.Unmarshal(body, &response); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	// check if there is any error message from lisa.
	if errMsg := response.Error1; errMsg != "" {
		logger.WithUser(reqBody.UserID).Error(errMsg)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, errMsg, general.GetUUID())
		return nil, errors.New(errMsg)
	}

	var appResp CoApplicantResp
	if err := mapdecoder.JSONDecoder(response.Data, &appResp); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.SuccessStatusCode, userID, url, "", general.GetUUID())
	return &appResp, nil
}

func CreateApplication(ctx context.Context, reqBody *ApplicationReq) (*ApplicationResp, error) {
	if toMockLISA() {
		return mockCreateApplication()
	}
	resourceName := CreateApplicationResource
	url := creds["baseURL"] + "application"
	method := "POST"
	userID := reqBody.UserID

	if reqBody != nil {
		if journey.IsPFLEducationLoanJourney(reqBody.SourceEntityID) {
			reqBody.SourceEntityID = constants.PFLEducationLoanSEID
		}
	}

	payload, err := json.Marshal(reqBody)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, "", "", serviceslib.ErrorStatusCode, reqBody.UserID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	client := tracer.GetTraceableHTTPClient(nil, resourceName)
	req, err := requestutils.GetMockableHTTPRequest(userID, resourceName, method, url, bytes.NewBuffer(payload))
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Authorization", creds["api-key"])
	req.Header.Add("lending-url", conf.BaseURL)
	res, err := client.Do(req)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	var response LenderServiceRespV2
	if err := json.Unmarshal(body, &response); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	// check if there is any error message from lisa.
	if errMsg := response.Error1; errMsg != "" {
		logger.WithUser(reqBody.UserID).Error(errMsg)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, errMsg, general.GetUUID())
		return nil, errors.New(errMsg)
	}

	if res.StatusCode >= http.StatusInternalServerError {
		err := fmt.Errorf("lisa_create_application_fatal:http_status:%d", res.StatusCode)
		logger.WithUser(reqBody.UserID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	var appResp ApplicationResp
	if err := mapdecoder.JSONDecoder(response.Data, &appResp); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.SuccessStatusCode, userID, url, "", general.GetUUID())
	return &appResp, nil
}

func UpdateApplication(ctx context.Context, reqBody *UpdateApplicationReq) (*ApplicationResp, error) {
	resourceName := UpdateApplicationResource + ":" + reqBody.Intent
	url := creds["baseURL"] + "application"
	method := "PUT"
	userID := reqBody.UserID

	payload, err := json.Marshal(reqBody)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, "", "", serviceslib.ErrorStatusCode, reqBody.UserID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	client := tracer.GetTraceableHTTPClient(nil, resourceName)
	req, err := requestutils.GetMockableHTTPRequest(userID, resourceName, method, url, bytes.NewBuffer(payload))
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Authorization", creds["api-key"])
	req.Header.Add("lending-url", conf.BaseURL)
	res, err := client.Do(req)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	if res.StatusCode != http.StatusOK {
		err := fmt.Errorf("unable to update application at service")
		logger.WithUser(userID).Error(err)

		var response LenderServiceResp
		_ = json.Unmarshal(body, &response)
		// TODO: Move this inside application status activity
		switch {
		case response.Error != nil && strings.Contains(strings.ToLower(response.Error.Error()), strings.ToLower("Email already Exists")):
			serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
			return nil, errors.New(constants.ErrEmailAlreadyExists)
		}

		// check if there is any error message from lisa.
		if errMsg := response.Error1; errMsg != "" {
			logger.WithUser(reqBody.UserID).Error(errMsg)
			serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, errMsg, general.GetUUID())
			return nil, errors.New(errMsg)
		}

		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	var response LenderServiceResp
	if err := json.Unmarshal(body, &response); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	var appResp ApplicationResp
	if err := mapdecoder.JSONDecoder(response.Data, &appResp); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.SuccessStatusCode, userID, url, "", general.GetUUID())
	return &appResp, nil
}

// GetReportDetails fetches the lender reports based on the specified intent and data
func GetReportDetails(ctx context.Context, reqBody *ReportReq) (*ReportResp, error) {
	resourceName := GetReportResource + ":" + reqBody.Intent
	url := creds["baseURL"] + "report"
	method := "GET"
	userID := reqBody.UserID

	payload, err := json.Marshal(reqBody)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, "", "", serviceslib.ErrorStatusCode, reqBody.UserID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	client := tracer.GetTraceableHTTPClient(nil, resourceName)
	req, err := requestutils.GetMockableHTTPRequest(userID, resourceName, method, url, bytes.NewBuffer(payload))
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Authorization", creds["api-key"])
	req.Header.Add("lending-url", conf.BaseURL)
	res, err := client.Do(req)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	if res.StatusCode >= http.StatusInternalServerError {
		err := errors.New(fmt.Sprintf("lisa_getreport_fatal:http_status:%d", res.StatusCode))
		logger.WithUser(reqBody.UserID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	var response LenderServiceRespV2
	if err := json.Unmarshal(body, &response); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	// check if there is any error message from lisa.
	if errMsg := response.Error1; errMsg != "" {
		logger.WithUser(reqBody.UserID).Error(errMsg)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, errMsg, general.GetUUID())
		return nil, errors.New(errMsg)
	}

	var appResp ReportResp
	if err := mapdecoder.JSONDecoder(response.Data, &appResp); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.SuccessStatusCode, userID, url, "", general.GetUUID())
	return &appResp, nil
}

func UtrDetail(ctx context.Context, reqBody *UtrReq) (*UtrResp, error) {
	if (conf.ENV != conf.ENV_PROD && journey.IsMuthootEDIPartner(reqBody.SourceEntityID)) ||
		toMockLISA() {
		return mockUTRDetail()
	}
	resourceName := UtrDetailResource
	url := creds["baseURL"] + "utr"
	method := "POST"
	userID := reqBody.UserID

	payload, err := json.Marshal(reqBody)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, "", "", serviceslib.ErrorStatusCode, reqBody.UserID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	client := tracer.GetTraceableHTTPClient(nil, resourceName)
	req, err := requestutils.GetMockableHTTPRequest(userID, resourceName, method, url, bytes.NewBuffer(payload))
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Authorization", creds["api-key"])
	req.Header.Add("lending-url", conf.BaseURL)
	res, err := client.Do(req)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	if res.StatusCode >= http.StatusInternalServerError {
		err := errors.New(fmt.Sprintf("lisa_utr_fatal:http_status:%d", res.StatusCode))
		logger.WithUser(reqBody.UserID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	var response LenderServiceRespV2
	if err := json.Unmarshal(body, &response); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	// check if there is any error message from lisa.
	if errMsg := response.Error1; errMsg != "" {
		logger.WithUser(reqBody.UserID).Error(errMsg)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, errMsg, general.GetUUID())
		return nil, errors.New(errMsg)
	}

	var appResp UtrResp
	if err := mapdecoder.JSONDecoder(response.Data, &appResp); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.SuccessStatusCode, userID, url, "", general.GetUUID())
	return &appResp, nil
}

func GetOffers(ctx context.Context, reqBody *GetOfferReq) (*GetOfferResp, error) {
	if toMockLISA() {
		return mockGetOffers()
	}
	resourceName := GetOffersResource
	url := creds["baseURL"] + "offer"
	method := "GET"

	userID := reqBody.UserID

	payload, err := json.Marshal(reqBody)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, "", "", serviceslib.ErrorStatusCode, reqBody.UserID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	client := tracer.GetTraceableHTTPClient(nil, resourceName)
	req, err := requestutils.GetMockableHTTPRequest(userID, resourceName, method, url, bytes.NewBuffer(payload))
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Authorization", creds["api-key"])
	req.Header.Add("lending-url", conf.BaseURL)
	res, err := client.Do(req)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	if res.StatusCode >= http.StatusInternalServerError {
		err := errors.New(fmt.Sprintf("lisa_getoffer_fatal:http_status:%d", res.StatusCode))
		logger.WithUser(reqBody.UserID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	var response LenderServiceRespV2
	if err := json.Unmarshal(body, &response); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	// check if there is any error message from lisa.
	if errMsg := response.Error1; errMsg != "" {
		logger.WithUser(reqBody.UserID).Error(errMsg)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, errMsg, general.GetUUID())
		return nil, errors.New(errMsg)
	}

	var appResp GetOfferResp
	if err := mapdecoder.JSONDecoder(response.Data, &appResp); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.SuccessStatusCode, userID, url, "", general.GetUUID())
	return &appResp, nil
}

func GetMetaData(ctx context.Context, reqBody *GetMetadataReq) (*GetMetadataResp, error) {
	resourceName := GetMetadataResource
	url := creds["baseURL"] + "metadata"
	method := "GET"

	userID := reqBody.UserID

	payload, err := json.Marshal(reqBody)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, "", "", serviceslib.ErrorStatusCode, reqBody.UserID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	client := tracer.GetTraceableHTTPClient(nil, resourceName)
	req, err := requestutils.GetMockableHTTPRequest(userID, resourceName, method, url, bytes.NewBuffer(payload))
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Authorization", creds["api-key"])
	req.Header.Add("lending-url", conf.BaseURL)
	res, err := client.Do(req)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	if res.StatusCode >= http.StatusInternalServerError {
		err := errors.New(fmt.Sprintf("lisa_getmetadata_fatal:http_status:%d", res.StatusCode))
		logger.WithUser(reqBody.UserID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	var response LenderServiceRespV2
	if err := json.Unmarshal(body, &response); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	// check if there is any error message from lisa.
	if errMsg := response.Error1; errMsg != "" {
		logger.WithUser(reqBody.UserID).Error(errMsg)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, errMsg, general.GetUUID())
		return nil, errors.New(errMsg)
	}

	var appResp GetMetadataResp
	if err := mapdecoder.JSONDecoder(response.Data, &appResp); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.SuccessStatusCode, userID, url, "", general.GetUUID())
	return &appResp, nil
}


func GetOfferStatus(ctx context.Context, reqBody *GetOfferStatusReq) (*GetOfferStatusResp, error) {
	resourceName := GetOfferStatusResource
	url := creds["baseURL"] + "offer/status"
	method := "GET"

	userID := reqBody.UserID

	payload, err := json.Marshal(reqBody)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, "", "", serviceslib.ErrorStatusCode, reqBody.UserID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	client := tracer.GetTraceableHTTPClient(nil, resourceName)
	req, err := requestutils.GetMockableHTTPRequest(userID, resourceName, method, url, bytes.NewBuffer(payload))
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Authorization", creds["api-key"])
	req.Header.Add("lending-url", conf.BaseURL)
	res, err := client.Do(req)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	if res.StatusCode >= http.StatusInternalServerError {
		err := errors.New(fmt.Sprintf("lisa_getofferstatus_fatal:http_status:%d", res.StatusCode))
		logger.WithUser(reqBody.UserID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	var response LenderServiceRespV2
	if err := json.Unmarshal(body, &response); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	// check if there is any error message from lisa.
	if errMsg := response.Error1; errMsg != "" {
		logger.WithUser(reqBody.UserID).Error(errMsg)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, errMsg, general.GetUUID())
		return nil, errors.New(errMsg)
	}

	var appResp GetOfferStatusResp
	if err := mapdecoder.JSONDecoder(response.Data, &appResp); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.SuccessStatusCode, userID, url, "", general.GetUUID())
	return &appResp, nil
}

func AcceptOffer(ctx context.Context, reqBody *AcceptOfferReq) (*AcceptOfferResp, error) {
	if toMockLISA() {
		return mockAcceptOffer()
	}
	resourceName := AcceptOfferResource
	url := creds["baseURL"] + "offer/accept"

	method := "POST"
	userID := reqBody.UserID

	payload, err := json.Marshal(reqBody)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, "", "", serviceslib.ErrorStatusCode, reqBody.UserID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	client := tracer.GetTraceableHTTPClient(nil, resourceName)
	req, err := requestutils.GetMockableHTTPRequest(userID, resourceName, method, url, bytes.NewBuffer(payload))
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Authorization", creds["api-key"])
	req.Header.Add("lending-url", conf.BaseURL)
	res, err := client.Do(req)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	if res.StatusCode != http.StatusOK {
		err := fmt.Errorf("unable to accept offer at service")
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	var response LenderServiceResp
	if err := json.Unmarshal(body, &response); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	var appResp AcceptOfferResp
	if err := mapdecoder.JSONDecoder(response.Data, &appResp); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.SuccessStatusCode, userID, url, "", general.GetUUID())
	return &appResp, nil
}

func CreateBusiness(ctx context.Context, reqBody *BusinessReq) error {
	if toMockLISA() {
		return nil
	}
	resourceName := CreateBusinessResource
	url := creds["baseURL"] + "business"
	method := "POST"
	userID := reqBody.ApplicationReq.UserID

	payload, err := json.Marshal(reqBody)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, "", "", serviceslib.ErrorStatusCode, reqBody.ApplicationReq.UserID, url, err.Error(), general.GetUUID())
		return err
	}
	client := tracer.GetTraceableHTTPClient(nil, resourceName)
	req, err := requestutils.GetMockableHTTPRequest(userID, resourceName, method, url, bytes.NewBuffer(payload))
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return err
	}
	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Authorization", creds["api-key"])
	req.Header.Add("lending-url", conf.BaseURL)
	res, err := client.Do(req)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return err
	}
	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return err
	}
	if res.StatusCode != http.StatusOK {
		err := fmt.Errorf("unable to create business at service")
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return err
	}
	var response LenderServiceResp
	if err := json.Unmarshal(body, &response); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return err
	}
	serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.SuccessStatusCode, userID, url, "", general.GetUUID())
	return nil
}

func CreateBankAccount(ctx context.Context, reqBody *BankAccountReq) (*BankAccountResp, error) {
	resourceName := CreateBankAccountResource
	url := creds["baseURL"] + "bankaccount"
	method := "POST"
	userID := reqBody.UserID

	payload, err := json.Marshal(reqBody)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, "", "", serviceslib.ErrorStatusCode, reqBody.UserID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	client := tracer.GetTraceableHTTPClient(nil, resourceName)
	req, err := requestutils.GetMockableHTTPRequest(userID, resourceName, method, url, bytes.NewBuffer(payload))
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Authorization", creds["api-key"])
	req.Header.Add("lending-url", conf.BaseURL)
	res, err := client.Do(req)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	if res.StatusCode != http.StatusOK {
		err := fmt.Errorf("unable to create bank account at service")
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	var response LenderServiceResp
	if err := json.Unmarshal(body, &response); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	var appResp BankAccountResp
	if err := mapdecoder.JSONDecoder(response.Data, &appResp); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.SuccessStatusCode, userID, url, "", general.GetUUID())
	return &appResp, nil
}

func GetBankAccounts(ctx context.Context, reqBody *BankAccountReq) (*GetBankAccountResp, error) {
	resourceName := GetBankAccountResource
	url := creds["baseURL"] + "bankaccount"
	method := "GET"
	userID := reqBody.UserID
	payload, err := json.Marshal(reqBody)

	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, "", "", serviceslib.ErrorStatusCode, reqBody.UserID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	client := tracer.GetTraceableHTTPClient(nil, resourceName)
	req, err := requestutils.GetMockableHTTPRequest(userID, resourceName, method, url, bytes.NewBuffer(payload))

	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Authorization", creds["api-key"])
	req.Header.Add("lending-url", conf.BaseURL)

	res, err := client.Do(req)

	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)

	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	if res.StatusCode != http.StatusOK {
		err := fmt.Errorf("unable to create bank account at service")
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	var response LenderServiceResp
	if err := json.Unmarshal(body, &response); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	var appResp GetBankAccountResp
	if err := mapdecoder.JSONDecoder(response.Data, &appResp); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.SuccessStatusCode, userID, url, "", general.GetUUID())
	return &appResp, nil
}

func UploadDocument(ctx context.Context, reqBody *UploadDocumentReq) error {
	resourceName := UploadDocumentResource
	url := creds["baseURL"] + "upload/document"
	method := "POST"
	userID := reqBody.UserID

	if reqBody != nil {
		if journey.IsPFLEducationLoanJourney(reqBody.SourceEntityID) {
			reqBody.SourceEntityID = constants.PFLEducationLoanSEID
		}
	}

	payload, err := json.Marshal(reqBody)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, "", "", serviceslib.ErrorStatusCode, reqBody.UserID, url, err.Error(), general.GetUUID())
		return err
	}
	client := tracer.GetTraceableHTTPClient(nil, resourceName)
	req, err := requestutils.GetMockableHTTPRequest(userID, resourceName, method, url, bytes.NewBuffer(payload))
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return err
	}
	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Authorization", creds["api-key"])
	req.Header.Add("lending-url", conf.BaseURL)
	res, err := client.Do(req)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return err
	}
	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return err
	}

	if res.StatusCode != http.StatusOK {
		err := fmt.Errorf("unable to upload document at service")
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return err
	}
	var response LenderServiceResp
	if err := json.Unmarshal(body, &response); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return err
	}

	serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.SuccessStatusCode, userID, url, "", general.GetUUID())
	return nil
}

func UploadKYC(ctx context.Context, reqBody *UploadDocumentsReq) (*UploadKYCResp, error) {
	if (conf.ENV != conf.ENV_PROD && journey.IsMuthootEDIPartner(reqBody.SourceEntityID)) ||
		toMockLISA() {
		return mockUploadKYC()
	}
	resourceName := UploadKYCResource
	url := creds["baseURL"] + "kyc/upload"
	method := "POST"
	userID := reqBody.UserID

	payload, err := json.Marshal(reqBody)
	if err != nil {
		logger.WithUser(reqBody.UserID).Error(err)
		serviceslib.WriteToDB(resourceName, "", "", serviceslib.ErrorStatusCode, reqBody.UserID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	client := tracer.GetTraceableHTTPClient(nil, resourceName)
	req, err := requestutils.GetMockableHTTPRequest(userID, resourceName, method, url, bytes.NewBuffer(payload))
	if err != nil {
		logger.WithUser(reqBody.UserID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Authorization", creds["api-key"])
	req.Header.Add("lending-url", conf.BaseURL)
	res, err := client.Do(req)
	if err != nil {
		logger.WithUser(reqBody.UserID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		logger.WithUser(reqBody.UserID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	if res.StatusCode >= http.StatusInternalServerError {
		err := errors.New(fmt.Sprintf("lisa_upload_kyc_fatal:http_status:%d", res.StatusCode))
		logger.WithUser(reqBody.UserID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	var response LenderServiceRespV2
	if err := json.Unmarshal(body, &response); err != nil {
		logger.WithUser(reqBody.UserID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	// check if there is any error message from lisa.
	if errMsg := response.Error1; errMsg != "" {
		logger.WithUser(reqBody.UserID).Error(errMsg)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, errMsg, general.GetUUID())
		return nil, errors.New(errMsg)
	}

	var appResp UploadKYCResp
	if err := mapdecoder.JSONDecoder(response.Data, &appResp); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.SuccessStatusCode, userID, url, "", general.GetUUID())
	return &appResp, nil
}

func KYCStatus(ctx context.Context, reqBody *ApplicationReq) (*KYCStatusResp, error) {
	resourceName := GetKYCResource
	url := creds["baseURL"] + "kyc/status"
	method := "GET"
	userID := reqBody.UserID

	payload, err := json.Marshal(reqBody)
	if err != nil {
		logger.WithUser(reqBody.UserID).Error(err)
		serviceslib.WriteToDB(resourceName, "", "", serviceslib.ErrorStatusCode, reqBody.UserID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	client := tracer.GetTraceableHTTPClient(nil, resourceName)
	req, err := requestutils.GetMockableHTTPRequest(userID, resourceName, method, url, bytes.NewBuffer(payload))
	if err != nil {
		logger.WithUser(reqBody.UserID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Authorization", creds["api-key"])
	req.Header.Add("lending-url", conf.BaseURL)

	res, err := client.Do(req)
	if err != nil {
		logger.WithUser(reqBody.UserID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		logger.WithUser(reqBody.UserID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	if res.StatusCode != http.StatusOK {
		err := fmt.Errorf("unable to get kyc status at service")
		logger.WithUser(reqBody.UserID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	var response LenderServiceResp
	if err := json.Unmarshal(body, &response); err != nil {
		logger.WithUser(reqBody.UserID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	var kycResp KYCStatusResp
	if err := mapdecoder.JSONDecoder(response.Data, &kycResp); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.SuccessStatusCode, userID, url, "", general.GetUUID())
	return &kycResp, nil
}

func InitiateNach(ctx context.Context, reqBody *NachInitReq) (*InititiateNachResp, error) {
	resourceName := InitiateNachResource
	url := creds["baseURL"] + "nach/init"
	method := "POST"
	userID := reqBody.UserID
	payload, err := json.Marshal(reqBody)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, "", "", serviceslib.ErrorStatusCode, reqBody.UserID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	client := tracer.GetTraceableHTTPClient(nil, resourceName)
	req, err := requestutils.GetMockableHTTPRequest(userID, resourceName, method, url, bytes.NewBuffer(payload))
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Authorization", creds["api-key"])
	req.Header.Add("lending-url", conf.BaseURL)
	res, err := client.Do(req)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	if res.StatusCode != http.StatusOK {
		err := fmt.Errorf("unable to initiate nach at service")
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	var response LenderServiceResp
	if err := json.Unmarshal(body, &response); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	var appResp InititiateNachResp
	if err := mapdecoder.JSONDecoder(response.Data, &appResp); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.SuccessStatusCode, userID, url, "", general.GetUUID())
	return &appResp, nil
}

func NachStatus(ctx context.Context, reqBody *NachInitReq) (*NachStatusResp, error) {
	var nachStatus NachStatusResp
	if reqBody == nil {
		return &NachStatusResp{}, errors.New("empty application request body")
	}
	err := makeRequest(ctx,
		NachStatusResource,
		getUrl("nach/status"),
		http.MethodGet,
		reqBody.UserID,
		reqBody,
		&nachStatus)
	if err != nil {
		logger.Log.Errorln(err)
		return nil, err
	}
	return &nachStatus, nil
}

func RePaymentSchedule(ctx context.Context, reqBody *ApplicationReq) (*RPSResp, error) {
	var rpsResp RPSResp
	err := makeRequest(ctx,
		RePaymentResource,
		getUrl("rps"),
		http.MethodPost,
		reqBody.UserID,
		reqBody,
		&rpsResp)
	if err != nil {
		return nil, err
	}
	return &rpsResp, nil
}

func BookLoan(ctx context.Context, reqBody *ApplicationReq) (*ApplicationResp, error) {
	var response ApplicationResp
	err := makeRequest(ctx,
		BookLoanResource,
		getUrl("application/approve"),
		http.MethodPost,
		reqBody.UserID,
		reqBody,
		&response)
	if err != nil {
		return nil, err
	}
	return &response, nil
}

func RadiusCheck(ctx context.Context, reqBody *ApplicationReq) (*RadiusCheckResp, error) {
	var response RadiusCheckResp
	err := makeRequest(ctx,
		RadiusCheckResource,
		getUrl("radiusCheck"),
		http.MethodPost,
		reqBody.UserID,
		reqBody,
		&response)
	if err != nil {
		logger.Log.Errorln(err)
		return nil, err
	}
	return &response, nil
}

func RadiusUpdate(ctx context.Context, reqBody *UpdateApplicationReq) (map[string]interface{}, error) {
	// var response LenderServiceResp
	var response map[string]interface{}
	err := makeRequest(ctx,
		RadiusUpdateResource,
		getUrl("application"),
		http.MethodPut,
		reqBody.UserID,
		reqBody,
		&response)
	if err != nil {
		logger.Log.Errorln(err)
		return nil, err
	}
	return response, nil
}

func FetchNachID(ctx context.Context, reqBody *NachInitReq) (*FetchNachResp, error) {
	var FetchNach FetchNachResp
	if reqBody == nil {
		return &FetchNachResp{}, errors.New("empty application request body")
	}
	err := makeRequest(ctx,
		NachIDFetchResource,
		getUrl("nach/fetch/NachID"),
		http.MethodPost,
		reqBody.UserID,
		reqBody,
		&FetchNach)
	if err != nil {
		logger.Log.Errorln(err)
		return nil, err
	}
	return &FetchNach, nil
}

func GetBankingStatus(ctx context.Context, reqBody *BankingStatusReq) (*BankingStatusResp, error) {
	var BankStatusResp BankingStatusResp
	if reqBody == nil {
		return &BankingStatusResp{}, errors.New("empty application request body")
	}
	err := makeRequest(ctx,
		BankingFetchResource,
		getUrl("banking"),
		http.MethodGet,
		reqBody.UserID,
		reqBody,
		&BankStatusResp)
	if err != nil {
		logger.Log.Errorln(err)
		return nil, err
	}
	return &BankStatusResp, nil

}

func Esign(ctx context.Context, reqBody *EsignReq) (*EsignResp, error) {
	resourceName := EsignResource
	url := creds["baseURL"] + "esign"
	method := "POST"
	userID := reqBody.UserID

	payload, err := json.Marshal(reqBody)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, "", "", serviceslib.ErrorStatusCode, reqBody.UserID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	client := tracer.GetTraceableHTTPClient(nil, resourceName)
	req, err := requestutils.GetMockableHTTPRequest(userID, resourceName, method, url, bytes.NewBuffer(payload))
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Authorization", creds["api-key"])
	req.Header.Add("lending-url", conf.BaseURL)
	res, err := client.Do(req)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	if res.StatusCode != http.StatusOK {
		err := fmt.Errorf("unable to create esign at service")
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())

		var response LenderServiceResp
		_ = json.Unmarshal(body, &response)
		switch {
		case response.Error != nil && strings.Contains(response.Error.Error(), "reupload_selfie"):
			return nil, errors.New(constants.ErrSelfieReupload)
		}
		return nil, err
	}

	var response LenderServiceResp
	if err := json.Unmarshal(body, &response); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	var resp EsignResp
	if err := mapdecoder.JSONDecoder(response.Data, &resp); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.SuccessStatusCode, userID, url, "", general.GetUUID())
	return &resp, err
}

func GetEsign(ctx context.Context, reqBody *EsignReq) (*GetEsignResp, error) {
	resourceName := EsignResource
	url := creds["baseURL"] + "esign"
	method := "GET"
	userID := reqBody.UserID

	payload, err := json.Marshal(reqBody)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, "", "", serviceslib.ErrorStatusCode, reqBody.UserID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	client := tracer.GetTraceableHTTPClient(nil, resourceName)
	req, err := requestutils.GetMockableHTTPRequest(userID, resourceName, method, url, bytes.NewBuffer(payload))
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Authorization", creds["api-key"])
	req.Header.Add("lending-url", conf.BaseURL)

	res, err := client.Do(req)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	if res.StatusCode != http.StatusOK {
		err := fmt.Errorf("unable to create esign at service")
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	var response LenderServiceResp
	if err := json.Unmarshal(body, &response); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	var resp GetEsignResp
	if err := mapdecoder.JSONDecoder(response.Data, &resp); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.SuccessStatusCode, userID, url, "", general.GetUUID())
	return &resp, err
}

func GetEsignRequest(ctx context.Context, reqBody *EsignReq) (*EsignResp, error) {
	resourceName := GetEsignResource
	url := creds["baseURL"] + "esign"
	method := "GET"
	userID := reqBody.UserID

	payload, err := json.Marshal(reqBody)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, "", "", serviceslib.ErrorStatusCode, reqBody.UserID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	client := tracer.GetTraceableHTTPClient(nil, resourceName)
	req, err := requestutils.GetMockableHTTPRequest(userID, resourceName, method, url, bytes.NewBuffer(payload))
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Authorization", creds["api-key"])
	req.Header.Add("lending-url", conf.BaseURL)

	res, err := client.Do(req)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)

	log.Println("=========>>>>>> Inside Get Agreement Lisa call <<<<<<<<===========")

	log.Println(res.Body)
	log.Println(string(body))

	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	if res.StatusCode != http.StatusOK {
		err := fmt.Errorf("unable to create esign at service")
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	var response LenderServiceResp
	if err := json.Unmarshal(body, &response); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	if response.Error != nil {
		err := response.Error
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	log.Println(response)
	var resp EsignResp
	if err := general.DecodeToStruct(response.Data, &resp); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	log.Println(resp)
	serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.SuccessStatusCode, userID, url, "", general.GetUUID())

	resp.EsignURL = strings.ReplaceAll(resp.EsignURL, "\u0026", "&")
	return &resp, err
}

func ApproveApplication(ctx context.Context, reqBody *ApplicationReq) (*ApplicationResp, error) {
	resourceName := ApproveApplicationResource
	url := creds["baseURL"] + "application/approve"
	method := "POST"
	userID := reqBody.UserID

	payload, err := json.Marshal(reqBody)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, "", "", serviceslib.ErrorStatusCode, reqBody.UserID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	client := tracer.GetTraceableHTTPClient(nil, resourceName)
	req, err := requestutils.GetMockableHTTPRequest(userID, resourceName, method, url, bytes.NewBuffer(payload))
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Authorization", creds["api-key"])
	req.Header.Add("lending-url", conf.BaseURL)
	res, err := client.Do(req)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	if res.StatusCode != http.StatusOK {
		err := fmt.Errorf("unable to approve application at service")
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())

		var response LenderServiceResp
		_ = json.Unmarshal(body, &response)
		switch {
		case response.Error != nil && strings.Contains(response.Error.Error(), "User is Fraud"):
			return nil, errors.New(constants.ErrFraudUser)
		case response.Error != nil && strings.Contains(response.Error.Error(), "Active Loan exists"):
			return nil, errors.New(constants.ErrActiveLoanAlreadyExists)
		}
		return nil, err
	}

	var response LenderServiceResp
	if err := json.Unmarshal(body, &response); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	var appResp ApplicationResp
	if err := mapdecoder.JSONDecoder(response.Data, &appResp); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.SuccessStatusCode, userID, url, "", general.GetUUID())
	return &appResp, nil
}

func DisburseLoan(ctx context.Context, reqBody *ApplicationReq) (*DisburseLoanResp, error) {
	resourceName := DisburseLoanResource
	url := creds["baseURL"] + "application/disburse"
	method := "POST"
	userID := reqBody.UserID

	payload, err := json.Marshal(reqBody)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, "", "", serviceslib.ErrorStatusCode, reqBody.UserID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	client := tracer.GetTraceableHTTPClient(nil, resourceName)
	req, err := requestutils.GetMockableHTTPRequest(userID, resourceName, method, url, bytes.NewBuffer(payload))
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Authorization", creds["api-key"])
	req.Header.Add("lending-url", conf.BaseURL)
	res, err := client.Do(req)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	if res.StatusCode != http.StatusOK {
		err := fmt.Errorf("unable to disburse loan at service")
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	var response LenderServiceResp
	if err := json.Unmarshal(body, &response); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	var appResp DisburseLoanResp
	if err := mapdecoder.JSONDecoder(response.Data, &appResp); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.SuccessStatusCode, userID, url, "", general.GetUUID())
	return &appResp, nil
}

func Repayment(ctx context.Context, reqBody *RepaymentReq) (*RepaymentResp, error) {
	if toMockLISA() {
		return mockRepayment()
	}
	resourceName := RepaymentResource
	url := creds["baseURL"] + "repayment"
	method := "POST"
	userID := reqBody.UserID
	var appResp RepaymentResp
	var body []byte
	payload, err := json.Marshal(reqBody)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, "", "", serviceslib.ErrorStatusCode, reqBody.UserID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	err = retry.CustomRetry(2, 1*time.Second, func() error {
		timeout := 90 * time.Second
		client := tracer.GetTraceableHTTPClient(&timeout, resourceName)
		req, err := requestutils.GetMockableHTTPRequest(userID, resourceName, method, url, bytes.NewBuffer(payload))
		if err != nil {
			logger.WithUser(userID).Error(err)
			serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
			return err
		}
		req.Header.Add("Content-Type", "application/json")
		req.Header.Add("Authorization", creds["api-key"])
		req.Header.Add("lending-url", conf.BaseURL)
		res, err := client.Do(req)
		if err != nil {
			logger.WithUser(userID).Error(err)
			serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
			return err
		}
		defer res.Body.Close()
		body, err = io.ReadAll(res.Body)
		if err != nil {
			logger.WithUser(userID).Error(err)
			serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
			return err
		}

		if res.StatusCode != http.StatusOK {
			err := fmt.Errorf("unable to mark repayment at service")
			logger.WithUser(userID).Error(err)
			serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
			return err
		}

		var response LenderServiceResp
		if err := json.Unmarshal(body, &response); err != nil {
			logger.WithUser(userID).Error(err)
			serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
			return err
		}
		if err := muthootRetryError(response); err != nil {
			logger.WithUser(userID).Error(err)
			serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
			return err
		}
		if err := mapdecoder.JSONDecoder(response.Data, &appResp); err != nil {
			logger.WithUser(userID).Error(err)
			serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
			return err
		}
		return nil
	})
	if err == nil {
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.SuccessStatusCode, userID, url, "", general.GetUUID())

	}
	return &appResp, err
}

func muthootRetryError(resp LenderServiceResp) error {
	if !resp.Status {
		errorMessage := strings.ToLower(resp.Error.Error())
		switch {
		case strings.Contains(errorMessage, "net payment amount less than zero from foreclosure api"):
			return errors.New(resp.Error.Error())
		case strings.Contains(errorMessage, "transaction processed successfully with receipt"):
			return errors.New(resp.Error.Error())
		case strings.Contains(errorMessage, "foreclosure:http_502"):
			return errors.New(resp.Error.Error())
		case strings.Contains(errorMessage, "foreclosure:http_403"):
			return errors.New(resp.Error.Error())
		case strings.Contains(errorMessage, "access_token_bnpl:error_empty_token"):
			return errors.New(resp.Error.Error())
		case strings.Contains(errorMessage, "access_token_bnpl:unexpected_response"):
			return errors.New(resp.Error.Error())
		case strings.Contains(errorMessage, "invalid character"):
			return errors.New(resp.Error.Error())
		case strings.Contains(errorMessage, "unknown application error"):
			return errors.New(resp.Error.Error())
		case strings.Contains(errorMessage, "foreclosure:http_504"):
			return errors.New(resp.Error.Error())
		default:
			return nil
		}
	}
	if resp.Status {
		dataBytes, err := json.Marshal(resp.Data)
		if err != nil {
			return err
		}
		// Check for specific true cases
		if strings.Contains(string(dataBytes), `"receiptId":0`) && strings.Contains(string(dataBytes), `"postPaymentStatus":true`) && strings.Contains(string(dataBytes), `"redirectionURL":"",`) {
			return errors.New("data is empty")
		} else if strings.Contains(string(dataBytes), `"receiptId":16825772`) && strings.Contains(string(dataBytes), `"postPaymentStatus":false`) && strings.Contains(string(dataBytes), `"redirectionURL":"",`) {
			return errors.New("data is empty")
		}
		return nil
	}
	return nil
}

// GetRepaymentDetails returns the repayment details for given loan application and user
func GetRepaymentDetails(ctx context.Context, reqBody *RepaymentReq) (*RepaymentResp, error) {
	resourceName := RepaymentResource
	url := creds["baseURL"] + "repayment"
	method := "GET"
	userID := reqBody.UserID
	payload, err := json.Marshal(reqBody)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, "", "", serviceslib.ErrorStatusCode, reqBody.UserID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	client := tracer.GetTraceableHTTPClient(nil, resourceName)
	req, err := requestutils.GetMockableHTTPRequest(userID, resourceName, method, url, bytes.NewBuffer(payload))
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Authorization", creds["api-key"])
	req.Header.Add("lending-url", conf.BaseURL)
	res, err := client.Do(req)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	if res.StatusCode != http.StatusOK {
		err := fmt.Errorf("unable to get repayment at service")
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	var response LenderServiceResp
	if err := json.Unmarshal(body, &response); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	var appResp RepaymentResp
	if err := mapdecoder.JSONDecoder(response.Data, &appResp); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.SuccessStatusCode, userID, url, "", general.GetUUID())
	return &appResp, nil
}

func GetApplicationReq(ctx context.Context, userID string) (ApplicationReq, error) {
	var appReq ApplicationReq
	query := `select coalesce(crm_id,'') as crm_id, user_id, source_entity_id from users where user_id=$1`
	if err := database.GetContext(ctx, &appReq, query, userID); err != nil {
		return appReq, err
	}
	query = `select loan_application_id,
		coalesce(loan_application_no,'') as loan_application_no, lender_id as lender_id
		from loan_application where user_id=$1
		order by created_at desc limit 1`
	if err := database.GetContext(ctx, &appReq, query, userID); err != nil {
		err = fmt.Errorf("no loan application details found. err: %s", err.Error())
		logger.WithUser(userID).Error(err)
	}
	return appReq, nil
}

func GetApplicationReqByUserAndLoan(ctx context.Context, userID, loanApplicationID string, opts *GetApplicationReqOptions) (ApplicationReq, error) {
	var appReq ApplicationReq
	query := `select coalesce(crm_id,'') as crm_id, coalesce(prospect_no,'') as prospect_no, user_id, source_entity_id from users where user_id=$1`
	if err := database.GetContext(ctx, &appReq, query, userID); err != nil {
		return appReq, err
	}

	query = `select loan_application_id,
			coalesce(loan_application_no,'') as loan_application_no, lender_id as lender_id
			from loan_application where loan_application_id=$1`
	if err := database.GetContext(ctx, &appReq, query, loanApplicationID); err != nil {
		if (err == sql.ErrNoRows || loanApplicationID == "") && opts != nil && opts.SkipLoanCheck {
			// If SkipLoanCheck is true, dont error.
			// If a lenderID is provided, use it
			if opts.LenderID != "" {
				appReq.LenderID = opts.LenderID
			}
		} else {
			err = fmt.Errorf("no loan application details found. err: %s", err.Error())
			logger.WithUser(userID).Error(err)
			return appReq, err
		}
	}

	return appReq, nil
}

func BureauPull(ctx context.Context, reqBody *ApplicationReq) (*CibilStatusScore, error) {
	resourceName := BureauPullResource
	url := creds["baseURL"] + "bureaupull"
	method := "POST"
	userID := reqBody.UserID

	payload, err := json.Marshal(reqBody)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, "", "", serviceslib.ErrorStatusCode, reqBody.UserID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	client := tracer.GetTraceableHTTPClient(nil, resourceName)
	req, err := requestutils.GetMockableHTTPRequest(userID, resourceName, method, url, bytes.NewBuffer(payload))

	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Authorization", creds["api-key"])
	req.Header.Add("lending-url", conf.BaseURL)
	res, err := client.Do(req)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	if res.StatusCode != http.StatusOK {
		err := fmt.Errorf("unable to cibil check at service")
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	var response LenderServiceResp
	if err := json.Unmarshal(body, &response); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	var appResp CibilStatusScore
	if err := mapdecoder.JSONDecoder(response.Data, &appResp); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.SuccessStatusCode, userID, url, "", general.GetUUID())
	return &appResp, nil
}

func GetLoanDetails(ctx context.Context, reqBody *LoanDetailsReq) (*GetLoanDetailsResp, error) {
	resourceName := GetLoanDetailsResource
	url := creds["baseURL"] + "getLoanDetails"
	method := "POST"
	userID := reqBody.UserID

	payload, err := json.Marshal(reqBody)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, "", "", serviceslib.ErrorStatusCode, reqBody.UserID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	client := tracer.GetTraceableHTTPClient(nil, resourceName)
	req, err := requestutils.GetMockableHTTPRequest(userID, resourceName, method, url, bytes.NewBuffer(payload))
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Authorization", creds["api-key"])
	req.Header.Add("lending-url", conf.BaseURL)

	res, err := client.Do(req)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	if res.StatusCode >= http.StatusInternalServerError {
		err := errors.New(fmt.Sprintf("lisa_get_loan_details_fatal:http_status:%d", res.StatusCode))
		logger.WithUser(reqBody.UserID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	var response LenderServiceRespV2
	if err := json.Unmarshal(body, &response); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	// check if there is any error message from lisa.
	if errMsg := response.Error1; errMsg != "" {
		logger.WithUser(reqBody.UserID).Error(errMsg)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, errMsg, general.GetUUID())
		return nil, errors.New(errMsg)
	}

	var appResp GetLoanDetailsResp
	if err := mapdecoder.JSONDecoder(response.Data, &appResp); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.SuccessStatusCode, userID, url, "", general.GetUUID())
	return &appResp, nil
}

func ApplicationDetails(ctx context.Context, reqBody *ApplicationReq) (*GetLoanDetailsResp, error) {
	resourceName := GetLoanDetailsResource
	url := creds["baseURL"] + "application/details"
	method := "POST"
	userID := reqBody.UserID

	payload, err := json.Marshal(reqBody)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, "", "", serviceslib.ErrorStatusCode, reqBody.UserID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	client := tracer.GetTraceableHTTPClient(nil, resourceName)
	req, err := requestutils.GetMockableHTTPRequest(userID, resourceName, method, url, bytes.NewBuffer(payload))
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Authorization", creds["api-key"])
	req.Header.Add("lending-url", conf.BaseURL)
	res, err := client.Do(req)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	if res.StatusCode != http.StatusOK {
		err := fmt.Errorf("unable to get loan details at service")
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	var response LenderServiceResp
	if err := json.Unmarshal(body, &response); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	var appResp GetLoanDetailsResp
	if err := mapdecoder.JSONDecoder(response.Data, &appResp); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.SuccessStatusCode, userID, url, "", general.GetUUID())
	return &appResp, nil
}

func ApplicationStatus(ctx context.Context, reqBody *ApplicationStatusReq, applicationResourceName string) (*ApplicationStatusRes, error) {
	resourceName := applicationResourceName
	url := creds["baseURL"] + "application/status"
	method := "GET"
	userID := reqBody.UserID

	payload, err := json.Marshal(reqBody)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, "", "", serviceslib.ErrorStatusCode, reqBody.UserID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	client := tracer.GetTraceableHTTPClientV3(nil, resourceName, "")
	req, err := requestutils.GetMockableHTTPRequestWithContext(ctx, userID, resourceName, method, url, bytes.NewBuffer(payload))

	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Authorization", creds["api-key"])
	req.Header.Add("lending-url", conf.BaseURL)
	res, err := client.Do(req)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	if res.StatusCode != http.StatusOK {
		err := fmt.Errorf("unable to get application status at service")
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	var response LenderServiceResp
	if err := json.Unmarshal(body, &response); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	var appResp ApplicationStatusRes
	if err := mapdecoder.JSONDecoder(response.Data, &appResp); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.SuccessStatusCode, userID, url, "", general.GetUUID())
	return &appResp, nil
}

func PanInfo(ctx context.Context, reqBody *ApplicationReq) (*PanInfoResponse, error) {
	resourceName := ApplicationStatusResource
	url := creds["baseURL"] + "panInfo"
	method := "POST"
	userID := reqBody.UserID

	payload, err := json.Marshal(reqBody)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, "", "", serviceslib.ErrorStatusCode, reqBody.UserID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	client := tracer.GetTraceableHTTPClient(nil, resourceName)
	req, err := requestutils.GetMockableHTTPRequest(userID, resourceName, method, url, bytes.NewBuffer(payload))

	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Authorization", creds["api-key"])
	req.Header.Add("lending-url", conf.BaseURL)
	res, err := client.Do(req)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	if res.StatusCode != http.StatusOK {
		err := fmt.Errorf("unable to get pan info at service")
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	var response LenderServiceResp
	if err := json.Unmarshal(body, &response); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	var appResp PanInfoResponse
	if err := mapdecoder.JSONDecoder(response.Data, &appResp); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.SuccessStatusCode, userID, url, "", general.GetUUID())
	return &appResp, nil
}

func GenerateOtp(ctx context.Context, reqBody *GenerateOtpReq) (*GenerateOtpRespStruct, error) {
	resourceName := GenerateOtpResource
	url := creds["baseURL"] + "otp/generate"
	method := "POST"
	userID := reqBody.UserID
	payload, err := json.Marshal(reqBody)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, "", "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	client := tracer.GetTraceableHTTPClient(nil, resourceName)
	req, err := requestutils.GetMockableHTTPRequest(userID, resourceName, method, url, bytes.NewBuffer(payload))
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, "", "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Authorization", creds["api-key"])
	req.Header.Add("lending-url", conf.BaseURL)
	res, err := client.Do(req)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	var response LenderServiceResp
	if res.StatusCode != http.StatusOK {
		err := fmt.Errorf("unable to generate otp at service")
		logger.WithUser(reqBody.UserID).Errorln(err, " statuscode:", res.StatusCode, " response:", body)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, "", general.GetUUID())
		_ = json.Unmarshal(body, &response)
		var appResp GenerateOtpRespStruct
		switch {
		case response.Error != nil && strings.Contains(response.Error.Error(), "Maximum resend exceeded, please try after 30 minutes"):
			log.Printf("===> Error can be Shown on Frontend: %s <===", response.Error.Error())
			appResp.ErrorMessage = "Maximum resend exceeded, please try after 30 minutes"
			return &appResp, nil
		}
		return nil, err
	}

	if err := json.Unmarshal(body, &response); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	var appResp GenerateOtpRespStruct
	if err := mapdecoder.JSONDecoder(response.Data, &appResp); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.SuccessStatusCode, userID, url, "", general.GetUUID())
	return &appResp, nil
}

func KFS(ctx context.Context, reqBody *ApplicationReq) (*KFSResp, error) {
	resourceName := FetchKfsResource
	url := creds["baseURL"] + "kfs"
	method := "POST"
	userID := reqBody.UserID

	payload, err := json.Marshal(reqBody)
	if err != nil {
		logger.WithUser(reqBody.UserID).Error(err)
		serviceslib.WriteToDB(resourceName, "", "", serviceslib.ErrorStatusCode, reqBody.UserID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	client := tracer.GetTraceableHTTPClient(nil, resourceName)
	req, err := requestutils.GetMockableHTTPRequest(userID, resourceName, method, url, bytes.NewBuffer(payload))
	if err != nil {
		logger.WithUser(reqBody.UserID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Authorization", creds["api-key"])
	req.Header.Add("lending-url", conf.BaseURL)
	res, err := client.Do(req)
	if err != nil {
		logger.WithUser(reqBody.UserID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		logger.WithUser(reqBody.UserID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	if res.StatusCode != http.StatusOK {
		err := fmt.Errorf("unable to fetch kfs at service")
		logger.WithUser(reqBody.UserID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, "", general.GetUUID())
		return nil, err
	}
	var response LenderServiceResp
	if err := json.Unmarshal(body, &response); err != nil {
		logger.WithUser(reqBody.UserID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	var appResp KFSResp
	if err := mapdecoder.JSONDecoder(response.Data, &appResp); err != nil {
		logger.WithUser(reqBody.UserID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.SuccessStatusCode, userID, url, "", general.GetUUID())
	return &appResp, nil
}

func AccountAggregate(ctx context.Context, reqBody *AccountAggregateReq) (*AccountAggregateResp, error) {
	resourceName := AccountAggregateResource
	url := creds["baseURL"] + "accountaggregate"
	method := "POST"
	userID := reqBody.UserID

	payload, err := json.Marshal(reqBody)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, "", "", serviceslib.ErrorStatusCode, reqBody.UserID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	client := tracer.GetTraceableHTTPClient(nil, resourceName)
	req, err := requestutils.GetMockableHTTPRequest(userID, resourceName, method, url, bytes.NewBuffer(payload))

	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Authorization", creds["api-key"])
	req.Header.Add("lending-url", conf.BaseURL)
	res, err := client.Do(req)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	if res.StatusCode != http.StatusOK {
		err := fmt.Errorf("unable to generate url for account aggregate at service")
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	var response LenderServiceResp
	if err := json.Unmarshal(body, &response); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	var appResp AccountAggregateResp
	if err := mapdecoder.JSONDecoder(response.Data, &appResp); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.SuccessStatusCode, userID, url, "", general.GetUUID())
	return &appResp, nil
}

func AuthenticateOtp(ctx context.Context, reqBody *AuthenticateOTPReq) (*AuthenticOtpResponseStruct, error) {
	resourceName := VerifyOtpResource
	url := creds["baseURL"] + "otp/authenticate"
	method := "POST"
	userID := reqBody.UserID

	payload, err := json.Marshal(reqBody)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, "", "", serviceslib.ErrorStatusCode, reqBody.UserID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	client := tracer.GetTraceableHTTPClient(nil, resourceName)
	req, err := requestutils.GetMockableHTTPRequest(userID, resourceName, method, url, bytes.NewBuffer(payload))

	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Authorization", creds["api-key"])
	req.Header.Add("lending-url", conf.BaseURL)
	res, err := client.Do(req)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	if res.StatusCode != http.StatusOK {
		err := fmt.Errorf("unable to authentic otp at service")
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	var response LenderServiceResp
	if err := json.Unmarshal(body, &response); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	var otpResp AuthenticOtpResponseStruct
	if err := mapdecoder.JSONDecoder(response.Data, &otpResp); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.SuccessStatusCode, userID, url, "", general.GetUUID())
	return &otpResp, nil
}

func FetchBanks(ctx context.Context, reqBody *ApplicationReq) ([]BankDetailsResp, error) {
	resourceName := FetchBanksResource
	url := creds["baseURL"] + "banks"
	method := "GET"
	userID := reqBody.UserID
	payload, err := json.Marshal(reqBody)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, "", "", serviceslib.ErrorStatusCode, reqBody.UserID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	client := tracer.GetTraceableHTTPClient(nil, resourceName)
	req, err := requestutils.GetMockableHTTPRequest(userID, resourceName, method, url, bytes.NewBuffer(payload))
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Authorization", creds["api-key"])
	req.Header.Add("lending-url", conf.BaseURL)
	res, err := client.Do(req)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	if res.StatusCode != http.StatusOK {
		err := fmt.Errorf("unable to fetch banks at service")
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	var response LenderServiceResp
	if err := json.Unmarshal(body, &response); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	var resp []BankDetailsResp
	if err := mapdecoder.JSONDecoder(response.Data, &resp); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.SuccessStatusCode, userID, url, "", general.GetUUID())
	return resp, err
}

func RedirectUser(ctx context.Context, reqBody *ApplicationReq) (*RedirectUserURLResp, error) {
	resourceName := RedirectUserResource
	url := creds["baseURL"] + "redirect/user"
	method := "GET"
	userID := reqBody.UserID

	payload, err := json.Marshal(reqBody)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, "", "", serviceslib.ErrorStatusCode, reqBody.UserID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	client := tracer.GetTraceableHTTPClient(nil, resourceName)
	req, err := requestutils.GetMockableHTTPRequest(userID, resourceName, method, url, bytes.NewBuffer(payload))
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Authorization", creds["api-key"])
	req.Header.Add("lending-url", conf.BaseURL)

	res, err := client.Do(req)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	if res.StatusCode >= http.StatusInternalServerError {
		err := fmt.Errorf("lisa_redirection_link_fatal:http_status:%d", res.StatusCode)
		logger.WithUser(reqBody.UserID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	var response LenderServiceRespV2
	if err := json.Unmarshal(body, &response); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	// check if there is any error message from lisa.
	if errMsg := response.Error1; errMsg != "" {
		logger.WithUser(reqBody.UserID).Error(errMsg)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, errMsg, general.GetUUID())
		return nil, errors.New(errMsg)
	}

	var appResp RedirectUserURLResp
	if err := mapdecoder.JSONDecoder(response.Data, &appResp); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.SuccessStatusCode, userID, url, "", general.GetUUID())
	return &appResp, nil
}

func BoostOffer(ctx context.Context, reqBody *BoostOfferReq) (*BoostOfferResponse, error) {
	resourceName := BoostOfferResource
	url := creds["baseURL"] + "offer/boost"
	method := "GET"
	userID := reqBody.UserID

	payload, err := json.Marshal(reqBody)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, "", "", serviceslib.ErrorStatusCode, reqBody.UserID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	client := tracer.GetTraceableHTTPClient(nil, resourceName)
	req, err := requestutils.GetMockableHTTPRequest(userID, resourceName, method, url, bytes.NewBuffer(payload))
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Authorization", creds["api-key"])
	req.Header.Add("lending-url", conf.BaseURL)

	res, err := client.Do(req)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	if res.StatusCode != http.StatusOK {

		var response LenderServiceResp
		_ = json.Unmarshal(body, &response)

		// check if there is any error message from lisa.
		if errMsg := response.Error1; errMsg != "" {
			logger.WithUser(reqBody.UserID).Error(errMsg)
			serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, errMsg, general.GetUUID())
			return nil, errors.New(errMsg)
		}

		err := fmt.Errorf("unable to boost offer at service")
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	var response LenderServiceResp
	if err := json.Unmarshal(body, &response); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	var appResp BoostOfferResponse
	if err := mapdecoder.JSONDecoder(response.Data, &appResp); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.SuccessStatusCode, userID, url, "", general.GetUUID())
	return &appResp, nil
}

func BoostOfferStatus(ctx context.Context, reqBody *BoostOfferStatusReq) (*BoostOfferStatusResponse, error) {
	resourceName := BoostOfferStatusResource
	url := creds["baseURL"] + "offer/boost/status"
	method := "GET"
	userID := reqBody.UserID

	payload, err := json.Marshal(reqBody)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, "", "", serviceslib.ErrorStatusCode, reqBody.UserID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	client := tracer.GetTraceableHTTPClient(nil, resourceName)
	req, err := requestutils.GetMockableHTTPRequest(userID, resourceName, method, url, bytes.NewBuffer(payload))
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Authorization", creds["api-key"])
	req.Header.Add("lending-url", conf.BaseURL)

	res, err := client.Do(req)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	if res.StatusCode != http.StatusOK {
		err := fmt.Errorf("unable to get boost offer status at service")
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	var response LenderServiceResp
	if err := json.Unmarshal(body, &response); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	var appResp BoostOfferStatusResponse
	if err := mapdecoder.JSONDecoder(response.Data, &appResp); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.SuccessStatusCode, userID, url, "", general.GetUUID())
	return &appResp, nil
}

func SendConsent(ctx context.Context, reqBody *ConsentRequest) (*LenderServiceResp, error) {
	resourceName := SendConsentResource
	url := creds["baseURL"] + "consent/send"
	method := "POST"
	userID := reqBody.UserID

	payload, err := json.Marshal(reqBody)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, "", "", serviceslib.ErrorStatusCode, reqBody.UserID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	client := tracer.GetTraceableHTTPClient(nil, resourceName)
	req, err := requestutils.GetMockableHTTPRequest(userID, resourceName, method, url, bytes.NewBuffer(payload))
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Authorization", creds["api-key"])
	req.Header.Add("lending-url", conf.BaseURL)

	res, err := client.Do(req)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	if res.StatusCode != http.StatusOK {
		err := fmt.Errorf("unable to get boost offer status at service")
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	var response LenderServiceResp
	if err := json.Unmarshal(body, &response); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.SuccessStatusCode, userID, url, "", general.GetUUID())
	return &response, nil
}

func CibilInitate(ctx context.Context, reqBody *ApplicationReq) (*CibilInitiateResp, error) {
	resourceName := CibilInitiateResource
	url := creds["baseURL"] + "cibil/initiate"
	method := "POST"
	userID := reqBody.UserID

	payload, err := json.Marshal(reqBody)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, "", "", serviceslib.ErrorStatusCode, reqBody.UserID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	client := tracer.GetTraceableHTTPClient(nil, resourceName)
	req, err := requestutils.GetMockableHTTPRequest(userID, resourceName, method, url, bytes.NewBuffer(payload))
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Authorization", creds["api-key"])
	req.Header.Add("lending-url", conf.BaseURL)
	res, err := client.Do(req)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	if res.StatusCode != http.StatusOK {
		err := fmt.Errorf("unable to cibil initiate")
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	var response LenderServiceResp
	if err := json.Unmarshal(body, &response); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	var appResp CibilInitiateResp
	if err := mapdecoder.JSONDecoder(response.Data, &appResp); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.SuccessStatusCode, userID, url, "", general.GetUUID())
	return &appResp, nil
}

func CibilAuthQuestions(ctx context.Context, reqBody *ApplicationReq) (*CibilAuthQuestionResp, error) {
	resourceName := CibilQuestionsResource
	url := creds["baseURL"] + "cibil/questions"
	method := "POST"
	userID := reqBody.UserID

	payload, err := json.Marshal(reqBody)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, "", "", serviceslib.ErrorStatusCode, reqBody.UserID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	client := tracer.GetTraceableHTTPClient(nil, resourceName)
	req, err := requestutils.GetMockableHTTPRequest(userID, resourceName, method, url, bytes.NewBuffer(payload))
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Authorization", creds["api-key"])
	req.Header.Add("lending-url", conf.BaseURL)
	res, err := client.Do(req)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	if res.StatusCode != http.StatusOK {
		err := fmt.Errorf("unable to get auth questions")
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	var response LenderServiceResp
	if err := json.Unmarshal(body, &response); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	var appResp CibilAuthQuestionResp
	if err := mapdecoder.JSONDecoder(response.Data, &appResp); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.SuccessStatusCode, userID, url, "", general.GetUUID())
	return &appResp, nil
}

func CibilAnswers(ctx context.Context, reqBody *ApplicationReq) (*CibilVerifyAnswers, error) {
	resourceName := CibilVerifyAnswersResource
	url := creds["baseURL"] + "cibil/submit"
	method := "POST"
	userID := reqBody.UserID

	payload, err := json.Marshal(reqBody)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, "", "", serviceslib.ErrorStatusCode, reqBody.UserID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	client := tracer.GetTraceableHTTPClient(nil, resourceName)
	req, err := requestutils.GetMockableHTTPRequest(userID, resourceName, method, url, bytes.NewBuffer(payload))
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Authorization", creds["api-key"])
	req.Header.Add("lending-url", conf.BaseURL)
	res, err := client.Do(req)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	if res.StatusCode != http.StatusOK {
		err := fmt.Errorf("unable to verify answers")
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	var response LenderServiceResp
	if err := json.Unmarshal(body, &response); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	var appResp CibilVerifyAnswers
	if err := mapdecoder.JSONDecoder(response.Data, &appResp); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.SuccessStatusCode, userID, url, "", general.GetUUID())
	return &appResp, nil
}

func CibilCreditDeatils(ctx context.Context, reqBody *ApplicationReq) (*CibilCreditDetailsResp, error) {
	resourceName := CibilCreditDeatilsResource
	url := creds["baseURL"] + "cibil/details"
	method := "POST"
	userID := reqBody.UserID

	payload, err := json.Marshal(reqBody)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, "", "", serviceslib.ErrorStatusCode, reqBody.UserID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	client := tracer.GetTraceableHTTPClient(nil, resourceName)
	req, err := requestutils.GetMockableHTTPRequest(userID, resourceName, method, url, bytes.NewBuffer(payload))
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Authorization", creds["api-key"])
	req.Header.Add("lending-url", conf.BaseURL)
	res, err := client.Do(req)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	if res.StatusCode != http.StatusOK {
		err := fmt.Errorf("unable to get credit details")
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	var response LenderServiceResp
	if err := json.Unmarshal(body, &response); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	var appResp CibilCreditDetailsResp
	if err := mapdecoder.JSONDecoder(response.Data, &appResp); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.SuccessStatusCode, userID, url, "", general.GetUUID())
	return &appResp, nil
}

// func EsignGet(ctx context.Context, reqBody *EsignReq) (*EsignGetResp, error) {
// 	resourceName := EsignResource
// 	url := creds["baseURL"] + "esign/get"
// 	method := "POST"
// 	userID := reqBody.UserID
// 	payload, err := json.Marshal(reqBody)
// 	if err != nil {
// 		logger.WithUser(userID).Error(err)
// 		serviceslib.WriteToDB(resourceName, "", "", serviceslib.ErrorStatusCode, reqBody.UserID, url, err.Error(), general.GetUUID())
// 		return nil, err
// 	}
// 	client := tracer.GetTraceableHTTPClient(nil, resourceName)
// 	req, err := requestutils.GetMockableHTTPRequest(userID, resourceName, method, url, bytes.NewBuffer(payload))
// 	if err != nil {
// 		logger.WithUser(userID).Error(err)
// 		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
// 		return nil, err
// 	}
// 	req.Header.Add("Content-Type", "application/json")
// 	req.Header.Add("Authorization", creds["api-key"])
// 	req.Header.Add("lending-url", conf.BaseURL)
// 	res, err := client.Do(req)
// 	if err != nil {
// 		logger.WithUser(userID).Error(err)
// 		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
// 		return nil, err
// 	}
// 	defer res.Body.Close()
// 	body, err := io.ReadAll(res.Body)
// 	if err != nil {
// 		logger.WithUser(userID).Error(err)
// 		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
// 		return nil, err
// 	}
// 	if res.StatusCode != http.StatusOK {
// 		err := fmt.Errorf("unable to get esign at service")
// 		logger.WithUser(userID).Error(err)
// 		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
// 		return nil, err
// 	}
// 	var response LenderServiceResp
// 	if err := json.Unmarshal(body, &response); err != nil {
// 		logger.WithUser(userID).Error(err)
// 		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
// 		return nil, err
// 	}
// 	var resp EsignGetResp
// 	if err := mapdecoder.JSONDecoder(response.Data, &resp); err != nil {
// 		logger.WithUser(userID).Error(err)
// 		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
// 		return nil, err
// 	}
// 	serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.SuccessStatusCode, userID, url, "", general.GetUUID())
// 	return &resp, err
// }

// func EsignSend(ctx context.Context, reqBody *EsignReq) (*EsignSendResp, error) {
// 	resourceName := EsignResource
// 	url := creds["baseURL"] + "esign/send"
// 	method := "POST"
// 	userID := reqBody.UserID
// 	payload, err := json.Marshal(reqBody)
// 	if err != nil {
// 		logger.WithUser(userID).Error(err)
// 		serviceslib.WriteToDB(resourceName, "", "", serviceslib.ErrorStatusCode, reqBody.UserID, url, err.Error(), general.GetUUID())
// 		return nil, err
// 	}
// 	client := tracer.GetTraceableHTTPClient(nil, resourceName)
// 	req, err := requestutils.GetMockableHTTPRequest(userID, resourceName, method, url, bytes.NewBuffer(payload))
// 	if err != nil {
// 		logger.WithUser(userID).Error(err)
// 		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
// 		return nil, err
// 	}
// 	req.Header.Add("Content-Type", "application/json")
// 	req.Header.Add("Authorization", creds["api-key"])
// 	req.Header.Add("lending-url", conf.BaseURL)
// 	res, err := client.Do(req)
// 	if err != nil {
// 		logger.WithUser(userID).Error(err)
// 		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
// 		return nil, err
// 	}
// 	defer res.Body.Close()
// 	body, err := io.ReadAll(res.Body)
// 	if err != nil {
// 		logger.WithUser(userID).Error(err)
// 		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
// 		return nil, err
// 	}
// 	if res.StatusCode != http.StatusOK {
// 		err := fmt.Errorf("unable to send esign at service")
// 		logger.WithUser(userID).Error(err)
// 		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
// 		return nil, err
// 	}
// 	var response LenderServiceResp
// 	if err := json.Unmarshal(body, &response); err != nil {
// 		logger.WithUser(userID).Error(err)
// 		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
// 		return nil, err
// 	}
// 	var resp EsignSendResp
// 	if err := mapdecoder.JSONDecoder(response.Data, &resp); err != nil {
// 		logger.WithUser(userID).Error(err)
// 		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
// 		return nil, err
// 	}
// 	serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.SuccessStatusCode, userID, url, "", general.GetUUID())
// 	return &resp, err
// }

func KycInit(ctx context.Context, reqBody *KycInitReq) (*KycInitResponseStruct, error) {
	resourceName := service + "kyc/initiate"
	url := creds["baseURL"] + "kyc/initiate"
	method := "POST"
	userID := reqBody.UserID

	payload, err := json.Marshal(reqBody)
	if err != nil {
		logger.WithUser(reqBody.UserID).Error(err)
		serviceslib.WriteToDB(resourceName, "", "", serviceslib.ErrorStatusCode, reqBody.UserID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	client := tracer.GetTraceableHTTPClient(nil, resourceName)
	req, err := requestutils.GetMockableHTTPRequest(userID, resourceName, method, url, bytes.NewBuffer(payload))
	if err != nil {
		logger.WithUser(reqBody.UserID).Error(err)
		serviceslib.WriteToDB(resourceName, "", "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Authorization", creds["api-key"])
	req.Header.Add("lending-url", conf.BaseURL)

	res, err := client.Do(req)
	if err != nil {
		logger.WithUser(reqBody.UserID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		logger.WithUser(reqBody.UserID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	if res.StatusCode != http.StatusOK {
		err := fmt.Errorf("unable to kyc init at service")
		logger.WithUser(reqBody.UserID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, "", general.GetUUID())
		return nil, err
	}
	var response LenderServiceResp
	if err := json.Unmarshal(body, &response); err != nil {
		logger.WithUser(reqBody.UserID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	var appResp KycInitResponseStruct
	if err := mapdecoder.JSONDecoder(response.Data, &appResp); err != nil {
		logger.WithUser(reqBody.UserID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.SuccessStatusCode, userID, url, "", general.GetUUID())
	return &appResp, nil
}

func TokenGenerate(ctx context.Context, reqBody *ApplicationReq) (*TokenGenerationResp, error) {
	resourceName := EsignResource
	url := creds["baseURL"] + "token/generate"
	method := "POST"
	userID := reqBody.UserID
	payload, err := json.Marshal(reqBody)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, "", "", serviceslib.ErrorStatusCode, reqBody.UserID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	client := tracer.GetTraceableHTTPClient(nil, resourceName)
	req, err := requestutils.GetMockableHTTPRequest(userID, resourceName, method, url, bytes.NewBuffer(payload))
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Authorization", creds["api-key"])
	req.Header.Add("lending-url", conf.BaseURL)
	res, err := client.Do(req)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	if res.StatusCode != http.StatusOK {
		err := fmt.Errorf("unable to generate token at service")
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	var response LenderServiceResp
	if err := json.Unmarshal(body, &response); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	var resp TokenGenerationResp
	if err := mapdecoder.JSONDecoder(response.Data, &resp); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.SuccessStatusCode, userID, url, "", general.GetUUID())
	return &resp, err
}

func GetCreditLineStatement(ctx context.Context, reqBody *ApplicationReq) (*CreditLineStatementStruct, error) {
	resourceName := GetCreditLineStatementResource
	url := creds["baseURL"] + "creditStatement"
	method := "GET"
	userID := reqBody.UserID

	payload, err := json.Marshal(reqBody)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, "", "", serviceslib.ErrorStatusCode, reqBody.UserID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	client := tracer.GetTraceableHTTPClient(nil, resourceName)
	req, err := requestutils.GetMockableHTTPRequest(userID, resourceName, method, url, bytes.NewBuffer(payload))
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Authorization", creds["api-key"])
	req.Header.Add("lending-url", conf.BaseURL)
	res, err := client.Do(req)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	if res.StatusCode != http.StatusOK {
		err := fmt.Errorf("unable to get credit line statement at service")
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	var response LenderServiceResp
	if err := json.Unmarshal(body, &response); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	var appResp CreditLineStatementStruct
	if err := mapdecoder.JSONDecoder(response.Data, &appResp); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.SuccessStatusCode, userID, url, "", general.GetUUID())
	return &appResp, nil
}

func TransactionDetails(ctx context.Context, reqBody *ApplicationReq) (*TransactionDetailsResp, error) {
	resourceName := TransactionDetailsResource
	url := creds["baseURL"] + "transaction/details"
	method := "GET"
	userID := reqBody.UserID

	payload, err := json.Marshal(reqBody)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, "", "", serviceslib.ErrorStatusCode, reqBody.UserID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	client := tracer.GetTraceableHTTPClient(nil, resourceName)
	req, err := requestutils.GetMockableHTTPRequest(userID, resourceName, method, url, bytes.NewBuffer(payload))
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Authorization", creds["api-key"])
	req.Header.Add("lending-url", conf.BaseURL)
	res, err := client.Do(req)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	if res.StatusCode != http.StatusOK {
		err := fmt.Errorf("unable to fetch transaction details at service")
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	var response LenderServiceResp
	if err := json.Unmarshal(body, &response); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	var appResp TransactionDetailsResp
	if err := mapdecoder.JSONDecoder(response.Data, &appResp); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.SuccessStatusCode, userID, url, "", general.GetUUID())
	return &appResp, nil
}

func GetUserLogs(ctx context.Context, reqParams *UserLogReq) (*UserLogResp, error) {
	resourceName := GetUserLogsResource
	url := creds["baseURL"] + "getUserLogs"
	method := "GET"
	userID := reqParams.UserID

	client := tracer.GetTraceableHTTPClient(nil, resourceName)
	req, err := requestutils.GetMockableHTTPRequest(userID, resourceName, method, url, nil)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, "", "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	q := req.URL.Query()
	q.Add("user_id", userID)
	q.Add("collection_name", reqParams.CollectionName)
	q.Add("offset", strconv.Itoa(reqParams.Offset))
	q.Add("limit", strconv.Itoa(reqParams.Limit))
	q.Add("lender_id", reqParams.LenderID)
	q.Add("start_date", reqParams.StartDate.Format("2006-01-02"))
	q.Add("end_date", reqParams.EndDate.Format("2006-01-02"))
	req.URL.RawQuery = q.Encode()

	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Authorization", creds["api-key"])
	req.Header.Add("lending-url", conf.BaseURL)
	res, err := client.Do(req)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, "", "", serviceslib.ErrorStatusCode, userID, req.URL.String(), err.Error(), general.GetUUID())
		return nil, err
	}
	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, "", "", serviceslib.ErrorStatusCode, userID, req.URL.String(), err.Error(), general.GetUUID())
		return nil, err
	}

	if res.StatusCode != http.StatusOK {
		err := fmt.Errorf("unable to fetch transaction details at service")
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, "", string(body), serviceslib.ErrorStatusCode, userID, req.URL.String(), err.Error(), general.GetUUID())
		return nil, err
	}

	var response LenderServiceResp
	if err := json.Unmarshal(body, &response); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, "", string(body), serviceslib.ErrorStatusCode, userID, req.URL.String(), err.Error(), general.GetUUID())
		return nil, err
	}

	var logResp UserLogResp
	if err := mapdecoder.JSONDecoder(response.Data, &logResp); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, "", string(body), serviceslib.ErrorStatusCode, userID, req.URL.String(), err.Error(), general.GetUUID())
		return nil, err
	}

	serviceslib.WriteToDB(resourceName, "", string(body), serviceslib.SuccessStatusCode, userID, req.URL.String(), "", general.GetUUID())
	return &logResp, nil
}

func CreateReceivable(ctx context.Context, reqBody *CreateReceivableReq) (crr *CreateReceivableResp, err error) {
	if toMockLISA() {
		return mockCreateReceivable()
	}
	resourceName := CreateCharges
	url := creds["baseURL"] + "charge/add"
	method := "POST"
	userID := reqBody.UserID

	gobj := map[string]string{
		"url":    url,
		"strReq": "",
		"strRes": "",
		"userID": userID,
		"id":     general.GetUUID(),
	}

	defer func() {
		status := serviceslib.SuccessStatusCode
		errStr := ""
		if err != nil {
			status = serviceslib.ErrorStatusCode
			errStr = err.Error()
		}
		serviceslib.WriteToDB(resourceName, gobj["strReq"], gobj["strRes"], status, gobj["userID"], gobj["url"], errStr, gobj["id"])
	}()

	payload, err := json.Marshal(reqBody)
	if err != nil {
		logger.WithUser(userID).Error(err)
		return nil, err
	}
	gobj["strReq"] = string(payload)

	client := tracer.GetTraceableHTTPClient(nil, resourceName)
	req, err := http.NewRequest(method, url, bytes.NewBuffer(payload))
	if err != nil {
		logger.WithUser(userID).Error(err)
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Authorization", creds["api-key"])
	req.Header.Add("lending-url", conf.BaseURL)

	res, err := client.Do(req)
	if err != nil {
		logger.WithUser(userID).Error(err)
		return nil, err
	}
	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		logger.WithUser(userID).Error(err)
		return nil, err
	}
	gobj["strRes"] = string(body)

	if res.StatusCode != http.StatusOK {
		err = fbxerrors.ErrUnableToAddChargeAtService
		logger.WithUser(userID).Error(err)
		return nil, err
	}

	var response LenderServiceResp
	if err = json.Unmarshal(body, &response); err != nil {
		logger.WithUser(userID).Error(err)
		return nil, err
	}

	var createReceivableResp CreateReceivableResp

	if err = mapdecoder.JSONDecoder(response.Data, &createReceivableResp); err != nil {
		logger.WithUser(userID).Error(err)
		return nil, err
	}

	return &createReceivableResp, nil
}

func CancelReceivable(ctx context.Context, reqBody *CancelReceivableReq) (crr *CancelReceivableResp, err error) {
	if toMockLISA() {
		return mockCancelReceivable()
	}
	resourceName := CancelCharges
	url := creds["baseURL"] + "charge/cancel"
	method := "POST"
	userID := reqBody.UserID

	gobj := map[string]string{
		"url":    url,
		"strReq": "",
		"strRes": "",
		"userID": userID,
		"id":     general.GetUUID(),
	}

	defer func() {
		status := serviceslib.SuccessStatusCode
		errStr := ""
		if err != nil {
			status = serviceslib.ErrorStatusCode
			errStr = err.Error()
		}
		serviceslib.WriteToDB(resourceName, gobj["strReq"], gobj["strRes"], status, gobj["userID"], gobj["url"], errStr, gobj["id"])
	}()

	payload, err := json.Marshal(reqBody)
	if err != nil {
		logger.WithUser(userID).Error(err)
		return nil, err
	}

	gobj["strReq"] = string(payload)

	client := tracer.GetTraceableHTTPClient(nil, resourceName)
	req, err := http.NewRequest(method, url, bytes.NewBuffer(payload))
	if err != nil {
		logger.WithUser(userID).Error(err)
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Authorization", creds["api-key"])
	req.Header.Add("lending-url", conf.BaseURL)

	res, err := client.Do(req)
	if err != nil {
		logger.WithUser(userID).Error(err)
		return nil, err
	}

	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	gobj["strRes"] = string(body)

	if res.StatusCode != http.StatusOK {
		err = fmt.Errorf("unable to cancel charge at service")
		logger.WithUser(userID).Error(err)
		return nil, err
	}

	var response LenderServiceResp
	if err = json.Unmarshal(body, &response); err != nil {
		logger.WithUser(userID).Error(err)
		return nil, err
	}

	var cancelReceivableResp CancelReceivableResp

	if err = mapdecoder.JSONDecoder(response.Data, &cancelReceivableResp); err != nil {
		logger.WithUser(userID).Error(err)
		return nil, err
	}

	return &cancelReceivableResp, nil
}

func CreatePayable(ctx context.Context, reqBody *CreatePayableReq) (cpr *CreatePayableResp, err error) {
	if toMockLISA() {
		return mockCreatePayable()
	}

	resourceName := RefundCharges
	url := creds["baseURL"] + "charge/refund/initiate"
	method := "POST"
	userID := reqBody.UserID

	gobj := map[string]string{
		"url":    url,
		"strReq": "",
		"strRes": "",
		"userID": userID,
		"id":     general.GetUUID(),
	}

	defer func() {
		status := serviceslib.SuccessStatusCode
		errStr := ""
		if err != nil {
			status = serviceslib.ErrorStatusCode
			errStr = err.Error()
		}
		serviceslib.WriteToDB(resourceName, gobj["strReq"], gobj["strRes"], status, gobj["userID"], gobj["url"], errStr, gobj["id"])
	}()

	payload, err := json.Marshal(reqBody)
	if err != nil {
		logger.WithUser(userID).Error(err)
		return nil, err
	}

	gobj["strReq"] = string(payload)

	client := tracer.GetTraceableHTTPClient(nil, resourceName)
	req, err := http.NewRequest(method, url, bytes.NewBuffer(payload))
	if err != nil {
		logger.WithUser(userID).Error(err)
		return nil, err
	}

	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Authorization", creds["api-key"])
	req.Header.Add("lending-url", conf.BaseURL)

	res, err := client.Do(req)
	if err != nil {
		logger.WithUser(userID).Error(err)
		return nil, err
	}

	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		logger.WithUser(userID).Error(err)
		return nil, err
	}

	gobj["strRes"] = string(body)

	if res.StatusCode != http.StatusOK {
		err = fmt.Errorf("unable to create refund charge at service")
		logger.WithUser(userID).Error(err)
		return nil, err
	}

	var response LenderServiceResp
	if err = json.Unmarshal(body, &response); err != nil {
		logger.WithUser(userID).Error(err)
		return nil, err
	}

	var createPayableResp CreatePayableResp

	if err = mapdecoder.JSONDecoder(response.Data, &createPayableResp); err != nil {
		logger.WithUser(userID).Error(err)
		return nil, err
	}

	return &createPayableResp, nil
}

func CancelPayable(ctx context.Context, reqBody *CancelPayableReq) (cpr *CancelPayableResp, err error) {
	if toMockLISA() {
		return mockCancelPayable()
	}

	resourceName := CancelRefundCharges
	url := creds["baseURL"] + "charge/refund/cancel"
	method := "POST"
	userID := reqBody.UserID

	gobj := map[string]string{
		"url":    url,
		"strReq": "",
		"strRes": "",
		"userID": userID,
		"id":     general.GetUUID(),
	}

	defer func() {
		status := serviceslib.SuccessStatusCode
		errStr := ""
		if err != nil {
			status = serviceslib.ErrorStatusCode
			errStr = err.Error()
		}
		serviceslib.WriteToDB(resourceName, gobj["strReq"], gobj["strRes"], status, gobj["userID"], gobj["url"], errStr, gobj["id"])
	}()

	payload, err := json.Marshal(reqBody)
	if err != nil {
		logger.WithUser(userID).Error(err)
		return nil, err
	}

	gobj["strReq"] = string(payload)

	client := tracer.GetTraceableHTTPClient(nil, resourceName)
	req, err := http.NewRequest(method, url, bytes.NewBuffer(payload))
	if err != nil {
		logger.WithUser(userID).Error(err)
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Authorization", creds["api-key"])
	req.Header.Add("lending-url", conf.BaseURL)

	res, err := client.Do(req)
	if err != nil {
		logger.WithUser(userID).Error(err)
		return nil, err
	}

	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		logger.WithUser(userID).Error(err)
		return nil, err
	}

	gobj["strRes"] = string(body)

	if res.StatusCode != http.StatusOK {
		err = fmt.Errorf("unable to cancel refund charge at service")
		logger.WithUser(userID).Error(err)
		return nil, err
	}

	var response LenderServiceResp
	if err = json.Unmarshal(body, &response); err != nil {
		logger.WithUser(userID).Error(err)
		return nil, err
	}

	var cancelPayableResp CancelPayableResp

	if err = mapdecoder.JSONDecoder(response.Data, &cancelPayableResp); err != nil {
		logger.WithUser(userID).Error(err)
		return nil, err
	}

	return &cancelPayableResp, nil
}

func CreateCRMLead(ctx context.Context, reqBody *ApplicationReq) (*ApplicationResp, error) {
	resourceName := CreateCRMLeadResource
	url := creds["baseURL"] + "applicant/crm"
	method := "POST"
	userID := reqBody.UserID

	payload, err := json.Marshal(reqBody)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, "", "", serviceslib.ErrorStatusCode, reqBody.UserID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	client := tracer.GetTraceableHTTPClient(nil, resourceName)
	req, err := requestutils.GetMockableHTTPRequest(userID, resourceName, method, url, bytes.NewBuffer(payload))
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Authorization", creds["api-key"])
	req.Header.Add("lending-url", conf.BaseURL)
	res, err := client.Do(req)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)
	if err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), "", serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	if res.StatusCode != http.StatusOK {
		err := fmt.Errorf("unable to create applicant at service")
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	var response LenderServiceResp
	if err := json.Unmarshal(body, &response); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}

	var appResp ApplicationResp
	if err := mapdecoder.JSONDecoder(response.Data, &appResp); err != nil {
		logger.WithUser(userID).Error(err)
		serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.ErrorStatusCode, userID, url, err.Error(), general.GetUUID())
		return nil, err
	}
	serviceslib.WriteToDB(resourceName, string(payload), string(body), serviceslib.SuccessStatusCode, userID, url, "", general.GetUUID())
	return &appResp, nil
}

func PostPaymentDetails(ctx context.Context, reqBody *RepaymentReq) (ppr *PostPaymentResp, err error) {
	if toMockLISA() {
		return mockPostPaymentDetails()
	}

	resourceName := PostPaymentDetailsResource
	url := creds["baseURL"] + "postpayment/details"
	method := "GET"
	userID := reqBody.UserID

	gobj := map[string]string{
		"url":    url,
		"strReq": "",
		"strRes": "",
		"userID": userID,
		"id":     general.GetUUID(),
	}

	defer func() {
		status := serviceslib.SuccessStatusCode
		errStr := ""
		if err != nil {
			status = serviceslib.ErrorStatusCode
			errStr = err.Error()
		}
		serviceslib.WriteToDB(resourceName, gobj["strReq"], gobj["strRes"], status, gobj["userID"], gobj["url"], errStr, gobj["id"])
	}()

	payload, err := json.Marshal(reqBody)
	if err != nil {
		logger.WithUser(userID).Error(err)
		return nil, err
	}

	gobj["strReq"] = string(payload)
	client := tracer.GetTraceableHTTPClient(nil, resourceName)
	req, err := http.NewRequest(method, url, bytes.NewBuffer(payload))

	if err != nil {
		logger.WithUser(userID).Error(err)
		return nil, err
	}

	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Authorization", creds["api-key"])
	req.Header.Add("lending-url", conf.BaseURL)
	res, err := client.Do(req)

	if err != nil {
		logger.WithUser(userID).Error(err)
		return nil, err
	}

	defer res.Body.Close()
	body, err := io.ReadAll(res.Body)

	if err != nil {
		logger.WithUser(userID).Error(err)
		return nil, err
	}

	gobj["strRes"] = string(body)

	if res.StatusCode != http.StatusOK {
		err = fmt.Errorf("unable to get  PostPaymentDetail from service")
		logger.WithUser(userID).Error(err)
		return nil, err
	}

	var response LenderServiceResp
	if err = json.Unmarshal(body, &response); err != nil {
		logger.WithUser(userID).Error(err)
		return nil, err
	}

	var postPaymentResp PostPaymentResp

	if err = mapdecoder.JSONDecoder(response.Data, &postPaymentResp); err != nil {
		logger.WithUser(userID).Error(err)
		return nil, err
	}

	return &postPaymentResp, nil

}

func PennyDrop(ctx context.Context, reqBody *ApplicationReq) (*PennyDropResp, error) {
	var pennyDropResp PennyDropResp
	err := makeRequest(ctx,
		PennyDropInitResource,
		getUrl("pennyDrop"),
		http.MethodPost,
		reqBody.UserID,
		reqBody,
		&pennyDropResp)
	if err != nil {
		return nil, err
	}
	return &pennyDropResp, nil
}

func ApplicationStatusV2(ctx context.Context, reqBody *ApplicationStatusReq, applicationResourceName string) (*ApplicationStatusRes, error) {
	var applicationStatusRes ApplicationStatusRes
	err := makeRequest(ctx,
		applicationResourceName,
		getUrl("application/status"),
		http.MethodGet,
		reqBody.UserID,
		reqBody,
		&applicationStatusRes)
	if err != nil {
		return nil, err
	}
	return &applicationStatusRes, nil
}
