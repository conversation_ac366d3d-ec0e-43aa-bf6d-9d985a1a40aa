package underwriting

import (
	"context"
	"finbox/go-api/common/usersutil"
	"finbox/go-api/constants"
	"finbox/go-api/errorHandler"
	"finbox/go-api/functions/journey"
	"finbox/go-api/functions/lenders/lendingkart"
	"finbox/go-api/functions/logger"
	"finbox/go-api/functions/services/tdl"
	"finbox/go-api/infra/db"
	"finbox/go-api/models/evalcodemapping"
	"finbox/go-api/models/preapproved"
	"finbox/go-api/models/preselectedlender"
	"finbox/go-api/models/users"
	"finbox/go-api/utils/general"
	"fmt"
	"strings"
	"time"

	"github.com/getsentry/sentry-go"
)

var database = db.GetDB()

// AssignLenderForTata assigns lender for TATA and returns lenderID and selectionType
// It also set the lender in `pre_selected_lender`
func AssignLenderForTata(userID, sourceEntityID string) (string, string) {
	var lenderID string
	selectionType := constants.LenderSelectionTypeDefault
	activeLenders := journey.GetActiveLenders(constants.TataPLID, "")
	lenderList := []string{}

	type dbStruct struct {
		PAN        string `db:"pan"`
		Mobile     string `db:"mobile"`
		Pincode    string `db:"pincode"`
		DOB        string `db:"dob"`
		Occupation string `db:"occupation"`
	}

	var userObj dbStruct
	query := `SELECT u.mobile,
				u.pincode,
				u.pan,
				to_char(u.dob, 'YYYY-MM-DD') AS dob,
				coalesce(dynamic_user_info::jsonb->>'occupation', '') AS occupation
			  FROM users u
			  WHERE u.user_id = $1
			  AND u.source_entity_id = $2`
	err := database.Get(&userObj, query, userID, sourceEntityID)
	if err != nil {
		logger.WithUser(userID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(fmt.Errorf("user_id: %s, error: %v", userID, err))
		return "NA", selectionType
	}
	dobObj, err := time.Parse("2006-01-02", userObj.DOB)
	if err != nil {
		logger.WithUser(userID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(fmt.Errorf("user_id: %s, error: %v", userID, err))
		return "NA", selectionType
	}

	age := general.AgeAt(dobObj, time.Now())
	for _, lender := range activeLenders {
		if age < 21 && lender == constants.IIFLID {
			lenderList = []string{lender}
			selectionType = constants.LenderSelectionTypeAge
			break
		}
		selectionType = constants.LenderSelectionTypePincode
		if !CheckPincodeBlocked(constants.TataPLID, lender, userObj.Pincode) {
			lenderList = append(lenderList, lender)
		} else {
			continue
		}

		if preapproved.IsActiveForLender(userObj.PAN, userObj.Mobile, constants.TataPLID, lender) {
			lenderList = []string{lender}
			selectionType = constants.LenderSelectionTypePreApproved
			break
		}
	}

	if len(lenderList) == 0 {
		return "", selectionType
	}
	if len(lenderList) == 1 {
		err = preselectedlender.Set(userID, lenderList[0])
		if err != nil {
			logger.WithUser(userID).Error(err)
			errorHandler.ReportToSentryWithoutRequest(fmt.Errorf("preselectedlender.Set failed for user - %s with err - %+v", userID, err))
		}
		return lenderList[0], selectionType
	}
	selectionType = constants.LenderSelectionTypeProbability
	if general.CalculateFlag(userID, "tdl_lender_assignment", 100) {
		lenderID = constants.TataCapitalID
	} else {
		lenderID = constants.IIFLID
	}
	err = preselectedlender.Set(userID, lenderID)
	if err != nil {
		logger.WithUser(userID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(fmt.Errorf("preselectedlender.Set failed for user - %s with err - %+v", userID, err))
	}

	// get additional data
	// var userData struct {
	// 	DOB        string
	// 	Occupation string
	// }
	// query = `select coalesce(to_char(up.dob, 'YYYY-MM-DD'), to_char(u.dob, 'YYYY-MM-DD')) AS dob,
	// 		up.occupation
	// 		from users up
	// 		join users u on up.user_id = u.user_id
	// 		where up.user_id = $1 and program_id = $2;`
	// err = database.Get(&userData, query, userID, programID)
	// if err != nil {
	// 	logger.WithUser(userID).Error(err)
	// 	errorHandler.ReportToSentryWithoutRequest(fmt.Errorf("user_id: %s, error: %v", userID, err))
	// }

	// additionalData := map[string]interface{}{
	// 	// "bureauScore": userData.BureauScore,
	// 	"age":        age,
	// 	"occupation": strings.ToLower(userData.Occupation),
	// 	"x":          1, // this variable signifies for salaried users percentage of cases to be sent to TCAP
	// 	"y":          1, // this variable signifies for self-employed users percentage of cases to be sent to TCAP
	// }

	// data := CallSentinel(userID, "tdl_lender_assignment_v1.1", userID, constants.TataID, "", additionalData)
	// if age < 21 {
	// 	selectionType = constants.LenderSelectionTypeAge
	// } else {
	// 	selectionType = constants.LenderSelectionTypeProbability
	// }
	// lenderName, _ := data.DecisionRunStruct.Data.OutputVariables["lender"].(string)
	// lenderName = strings.ToLower(lenderName)

	// switch {
	// case strings.Contains(lenderName, "iifl"):
	// 	lenderID = constants.IIFLID
	// 	err = preselectedlender.Set(userID, lenderID)
	// case strings.Contains(lenderName, "tcap"):
	// 	lenderID = constants.TataCapitalID
	// 	err = preselectedlender.Set(userID, lenderID)
	// default:
	// 	lenderID = "NA"
	// }

	// if err != nil {
	// 	logger.WithUser(userID).Error(err)
	// 	errorHandler.ReportToSentryWithoutRequest(fmt.Errorf("preselectedlender.Set failed for user - %s with err - %+v", userID, err))
	// }
	return lenderID, selectionType
}

func AssignLenderForHousing(userID string) (string, string) {
	var lenderID string
	selectionType := constants.LenderSelectionTypeDefault
	activeLenders := journey.GetActiveLenders(constants.HousingID, "")
	lenderList := []string{}
	userObj, _ := users.Get(userID)
	var err error
	occupationType, err := users.GetDynamicUserInfoOccupation(userID)
	if err != nil {
		logger.WithUser(userID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
	}
	for _, lender := range activeLenders {
		selectionType = constants.LenderSelectionTypePincode
		if !CheckPincodeBlocked(constants.HousingID, lender, userObj.Pincode) {
			lenderList = append(lenderList, lender)
		} else {
			continue
		}
	}
	if len(lenderList) == 0 {
		return "", selectionType
	}
	if len(lenderList) == 1 {
		lenderID = lenderList[0]
		if lenderID == constants.LoanTapID && occupationType == constants.OccupationTypeSelfEmployed { // do not route cases to LoanTap if self employed
			return "", selectionType
		}
		err = preselectedlender.Set(userID, lenderList[0])
		if err != nil {
			logger.WithUser(userID).Error(err)
			errorHandler.ReportToSentryWithoutRequest(fmt.Errorf("preselectedlender.Set failed for user - %s with err - %+v", userID, err))
		}
		return lenderID, selectionType
	}
	switch occupationType {
	case constants.OccupationTypeSelfEmployed:
		selectionType = constants.LenderSelectionTypeSelfEmployed
		lenderID = constants.IIFLID
		err = preselectedlender.Set(userID, lenderID)
		if err != nil {
			logger.WithUser(userID).Error(err)
			errorHandler.ReportToSentryWithoutRequest(fmt.Errorf("preselectedlender.Set failed for user - %s with err - %+v", userID, err))
		}
	default:
		selectionType = constants.LenderSelectionTypeProbability
		if general.CalculateFlag(userID, "housing_routing", 50) {
			lenderID = constants.IIFLID
			err = preselectedlender.Set(userID, lenderID)
			if err != nil {
				logger.WithUser(userID).Error(err)
				errorHandler.ReportToSentryWithoutRequest(fmt.Errorf("preselectedlender.Set failed for user - %s with err - %+v", userID, err))
			}
		} else {
			lenderID = constants.LoanTapID
			err = preselectedlender.Set(userID, lenderID)
			if err != nil {
				logger.WithUser(userID).Error(err)
				errorHandler.ReportToSentryWithoutRequest(fmt.Errorf("preselectedlender.Set failed for user - %s with err - %+v", userID, err))
			}
		}
	}
	return lenderID, selectionType
}

// AssignLenderForTataV2 routing logic based on new scenario
func AssignLenderForTataV2(userID, sourceEntityID string) (lenderName string, selectionType string) {
	var lenderList []string
	var lenderID string
	var err error
	selectionType = constants.LenderSelectionTypeDefault
	activeLenders := journey.GetActiveLenders(constants.TataPLID, "")

	userObj, _ := users.Get(userID)
	if general.InArr(constants.TataCapitalID, activeLenders) {
		lenderID = CheckAndAssignTcapFromPartnerData(userID, sourceEntityID, userObj.Pincode)
		if lenderID != "" {
			return lenderID, constants.LenderSelectionTypePartnerPush
		}
	}

	for _, lender := range activeLenders {
		selectionType = constants.LenderSelectionTypePincode
		if !CheckPincodeBlocked(constants.TataPLID, lender, userObj.Pincode) {
			lenderList = append(lenderList, lender)
		} else {
			continue
		}
	}

	if len(lenderList) == 0 {
		return "", selectionType
	}

	// if tcap is not present in the list, assign to iifl as the flow goes to multi offer
	if !general.InArr(constants.TataCapitalID, lenderList) {
		return constants.IIFLID, constants.LenderSelectionTypePincode
	}

	if len(lenderList) == 1 {
		return lenderList[0], selectionType
	}

	age, occupation := getUserAgeAndOccupation(userID)
	/*
		additionalData := map[string]interface{}{
			"emp_type": strings.ToLower(occupation),
			"age":      age,
			"etb":      false,
			"x":        0,
			"y":        0,
		}
	*/
	underwritingData := UnderwritingDataStruct{
		UserID:         userID,
		UniqueID:       userObj.UniqueID,
		SourceEntityID: sourceEntityID,
		Age:            age,
		DynamicUserInfo: map[string]any{
			"occupation": occupation,
		},
	}

	preApprovedOffer, preApprovedOfferExists := usersutil.GetPreApprovedOffer(userObj.PAN, userObj.Mobile, "", userObj.SourceEntityID, "")
	if preApprovedOfferExists && general.InArr(preApprovedOffer.LenderID, lenderList) {
		// additionalData["pre_approved"] = constants.LenderNamesMap[preApprovedOffer.LenderID]
		underwritingData.PreApprovedOffer.LenderName = constants.LenderNamesMap[preApprovedOffer.LenderID]
	}
	underwritingData.PreApprovedOffer.IsPreApproved = preApprovedOfferExists

	evaluationCode, _, sentinelAdditionalConfig, sentinelDumpConfig, err := evalcodemapping.GetEvaluationConfig(userID, userObj.SourceEntityID, "", "lender_routing")
	if err != nil {
		logger.WithUser(userID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{
			"userID": userID,
		}, fmt.Errorf("error in getting evaluationConfig %s", err.Error()))
		return "NA", selectionType
	}

	// data := CallSentinel(userID, "lender_routing_v2.1", userID, constants.TataPLID, "", additionalData) //this will return
	_, done, _, data := CallRuleEngineV2(userID, userObj.SourceEntityID, "", "", evaluationCode, "lender_routing", &underwritingData, make(map[string]any), sentinelAdditionalConfig, sentinelDumpConfig)
	if !done {
		err = fmt.Errorf("decision failed for userID: %s", userID)
		logger.WithUser(userID).Error(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{
			"userID": userID,
		}, err)
		return "NA", selectionType
	}

	log.Debugln("Response from sentinel:", data)

	// lenderName, _ = data.DecisionRunStruct.Data.OutputVariables["final_lender"].(string)
	lenderName, _ = data.Data.OutputVariables["final_lender"].(string)
	lenderName = strings.ToLower(lenderName)

	switch {
	case strings.Contains(lenderName, "age"):
		selectionType = constants.LenderSelectionTypeAge
	case strings.Contains(lenderName, "pre_approved"):
		selectionType = constants.LenderSelectionTypePreApproved
	case strings.Contains(lenderName, "axis_etb"):
		selectionType = constants.LenderSelectionTypeAxisETB
	default:
		selectionType = constants.LenderSelectionTypeProbability
	}

	switch {
	case strings.Contains(lenderName, "iifl"), strings.Contains(lenderName, "axis"), strings.Contains(lenderName, "dmi"), strings.Contains(lenderName, "tcap"):
		//Here we are checking the case when sentinel returns AXIS/IIFL
		lenderID = constants.IIFLID
		err = preselectedlender.Set(userID, lenderID)
	default:
		errorHandler.ReportToSentryWithoutRequest(fmt.Errorf("lender name not found in tata's policy assignment for user: %s", userID))
		return "NA", selectionType
	}

	if err != nil {
		logger.WithUser(userID).Error(err)
		sentry.CaptureException(fmt.Errorf("preselectedlender.Set failed for user - %s with err - %+v", userID, err))
	}
	return lenderID, selectionType
}

type AssignLenderTataOptions struct {
	SkipTcapRouting               bool
	SkipTcapRoutingOnUserSalaried bool
}

type AssignLenderTataResp struct {
	LenderID      string
	SelectionType string
	ToRejectUser  bool
	RejectReason  string
	RoutingAPI    struct {
		CallExists            bool     // was routing api called for the user, this key was mainly made for backward compatibility for users for whom the routing api was not called because of previous logic
		CallFailed            bool     // if routing api was called, then did it fail ( throw some error because of which approved lenders were not received to middleware)
		GatingApprovedLenders []string // if routing api call was successful, show approved lenders
	}
}

// AssignLenderForTataV3 routing logic based on new scenario
// Note precedence order amongst options passed: SkipTcapRouting -> if true, SkipTcapRoutingOnUserSalaried -> if true
func AssignLenderForTataV3(userID, sourceEntityID string, options AssignLenderTataOptions) (resp AssignLenderTataResp) {
	var lenderList []string
	var err error
	resp.SelectionType = constants.LenderSelectionTypeDefault
	activeLenders := journey.GetActiveLenders(constants.TataPLID, "")

	userObj, _ := users.Get(userID)

	isRepeatLoan, err := users.GetDynamicUserInfoField(userID, "isRepeatLoan")
	if err != nil {
		logger.WithUser(userID).Error(err)
	}

	var tcapRoutingResp tdl.TcapRoutingResponse
	if isRepeatLoan == "Y" {
		resp.RoutingAPI.CallExists = false
		resp.LenderID = constants.IIFLID
	} else {
		var err error
		// call routing api at the top. response to be used later in the function
		tcapRoutingResp, err = tdl.TcapRouting(userObj)
		if err != nil {
			err = fmt.Errorf("for userID: %s, tdl tcap routing api failed with error: %s", userID, err.Error())
			logger.WithUser(userID).Error(err)
		}

		resp.RoutingAPI.CallExists = true
		resp.RoutingAPI.CallFailed = tcapRoutingResp.IsRoutingAPICallFailed
		resp.RoutingAPI.GatingApprovedLenders = tcapRoutingResp.GatingApprovedLenders
	}

	// if general.InArr(constants.TataCapitalID, activeLenders) {
	// 	lenderID := CheckAndAssignTcapFromPartnerData(userID, sourceEntityID, userObj.Pincode)
	// 	if lenderID != "" {
	// 		resp.LenderID = lenderID
	// 		resp.SelectionType = constants.LenderSelectionTypePartnerPush
	// 		return resp
	// 	}
	// }

	for _, lender := range activeLenders {
		resp.SelectionType = constants.LenderSelectionTypePincode
		if !CheckPincodeBlocked(constants.TataPLID, lender, userObj.Pincode) {
			lenderList = append(lenderList, lender)
		} else {
			continue
		}
	}

	if len(lenderList) == 0 {
		return resp
	}

	// if tcap is not present in the list, assign to iifl as the flow goes to multi offer
	if !general.InArr(constants.TataCapitalID, lenderList) {
		resp.LenderID = constants.IIFLID
		resp.SelectionType = constants.LenderSelectionTypePincode
		return resp
	}

	if len(lenderList) == 1 {
		resp.LenderID = lenderList[0]
		return resp
	}

	age, _ := getUserAgeAndOccupation(userID)
	etb := false
	// fetching all the pre approved offers available
	// preApprovedOffers, _ := usersutil.GetMultiPreApprovedOffers(userObj.UniqueID, userObj.SourceEntityID, "")
	// for _, preApprovedOffer := range preApprovedOffers {
	// 	if preApprovedOffer.ExpiryAt != "" {
	// 		isExpired, err := general.CheckExpiryOnDate(preApprovedOffer.ExpiryAt, "2006-01-02")
	// 		if err != nil {
	// 			logger.WithUser(userID).Errorln(err)
	// 			continue
	// 		}
	// 		if isExpired {
	// 			continue
	// 		}
	// 	}
	// 	// if tcap pre approved is present, flw type is tcap
	// 	if general.InArr(preApprovedOffer.LenderID, lenderList) {
	// 		if preApprovedOffer.LenderID == constants.TataCapitalID {
	// 			resp.SelectionType = constants.LenderSelectionTypePreApproved
	// 			resp.LenderID = constants.TataCapitalID
	// 			break
	// 		} else {
	// 			// checking pre approved for all the lnders
	// 			resp.SelectionType = constants.LenderSelectionTypePreApproved
	// 			resp.LenderID = constants.IIFLID
	// 		}
	// 	}
	// }
	if resp.LenderID == "" {
		if age <= 21 && age >= 19 {
			resp.LenderID = constants.IIFLID
			resp.SelectionType = constants.LenderSelectionTypeAge
		} else if etb {
			resp.LenderID = constants.IIFLID
			resp.SelectionType = constants.LenderSelectionTypeAxisETB
		} else {
			if options.SkipTcapRouting {
				resp.SelectionType = constants.LenderSelectionTypeMarketplaceOnFailure
				resp.LenderID = constants.IIFLID
			} else if options.SkipTcapRoutingOnUserSalaried {
				resp.SelectionType = constants.LenderSelectionTypeUserSalariedProbability
				resp.LenderID = constants.IIFLID
			} else {
				resp.LenderID = tcapRoutingResp.LenderID
				resp.ToRejectUser = tcapRoutingResp.ToRejectUser
				resp.RejectReason = tcapRoutingResp.RejectReason
				resp.SelectionType = constants.LenderSelectionTypeRoutingAPI

				if resp.ToRejectUser {
					return resp
				}
			}
		}
	}

	if resp.LenderID == "" {
		logger.WithUser(userID).Warnf("lender name not found in tata's policy assignment for user: %s", userID)
		resp.LenderID = "NA"
		return resp
	}

	err = preselectedlender.Set(userID, resp.LenderID)
	if err != nil {
		logger.WithUser(userID).Error(err)
		sentry.CaptureException(fmt.Errorf("preselectedlender.Set failed for user - %s with err - %+v", userID, err))
	}

	return resp
}

func AssignLenderForVyapar(userID string) (string, string) {
	var lenderID string
	selectionType := constants.LenderSelectionTypeDefault
	activeLenders := journey.GetActiveLenders(constants.VyaparID, "")
	lenderList := []string{}
	var err error
	for _, lender := range activeLenders {
		selectionType = constants.LenderSelectionTypeCascading
		if lender == constants.LendingKartID {
			isDuplicate := lendingkart.DedupeCheck(context.Background(), userID, constants.VyaparID)
			if isDuplicate {
				continue
			} else {
				lenderList = []string{lender}
				break
			}
		} else {
			lenderList = []string{lender}
			break
		}
	}
	if len(lenderList) == 0 {
		return "", selectionType
	}
	lenderID = lenderList[0]
	err = preselectedlender.Set(userID, lenderID)
	if err != nil {
		logger.WithUser(userID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(fmt.Errorf("preselectedlender.Set failed for user - %s with err - %+v", userID, err))
	}
	return lenderID, selectionType
}
