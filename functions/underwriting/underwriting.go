// Package underwriting contains the underwriting functions
package underwriting

import (
	"bytes"
	"context"
	"database/sql"
	"encoding/base64"
	"encoding/json"
	"finbox/go-api/functions/lenders/poonawalla"
	"finbox/go-api/functions/requestutils"
	"finbox/go-api/functions/structs"
	"finbox/go-api/models/businessloanoffer"
	"finbox/go-api/models/emailconfig"
	"finbox/go-api/models/hsnsaccodes"
	"finbox/go-api/models/loankycdetails"
	"finbox/go-api/models/media"
	"finbox/go-api/models/preapproved"
	"finbox/go-api/models/sourceentity"
	"finbox/go-api/models/userbusiness"
	"finbox/go-api/models/userbusinessgst"
	"finbox/go-api/models/userjourney"
	"finbox/go-api/models/usermodulemapping"
	"finbox/go-api/thirdparty/sentinel"
	"finbox/go-api/utils/fraudcheckutils"
	"finbox/go-api/utils/iiflutils"
	"finbox/go-api/utils/journeyutils"
	"finbox/go-api/utils/offergetter"
	"fmt"
	"io"
	"math"
	"strconv"
	"strings"
	"time"

	"github.com/pkg/errors"

	"github.com/getsentry/sentry-go"
	"github.com/google/uuid"
	"github.com/jmoiron/sqlx"

	"finbox/go-api/common/usersutil"
	"finbox/go-api/conf"
	"finbox/go-api/constants"
	"finbox/go-api/errorHandler"
	"finbox/go-api/functions/activity"
	"finbox/go-api/functions/insuranceutils"
	"finbox/go-api/functions/journey"
	"finbox/go-api/functions/lenders/hcin"
	"finbox/go-api/functions/lenders/kotak"
	"finbox/go-api/functions/lenders/lendingkart"
	"finbox/go-api/functions/lenders/mintifi"
	"finbox/go-api/functions/lenders/saraloan"
	"finbox/go-api/functions/lenderservice"
	"finbox/go-api/functions/loanprogram"
	"finbox/go-api/functions/logger"
	"finbox/go-api/functions/mailalert"
	"finbox/go-api/functions/partner"
	"finbox/go-api/functions/retry"
	"finbox/go-api/functions/services/bankconnect"
	"finbox/go-api/functions/services/deviceConnect"
	"finbox/go-api/functions/services/gst"
	"finbox/go-api/functions/services/pincodeapi"
	"finbox/go-api/functions/services/reversepincode"
	"finbox/go-api/functions/services/sms"
	"finbox/go-api/functions/serviceslib"
	"finbox/go-api/functions/tracer"
	"finbox/go-api/infra/redis"
	"finbox/go-api/infra/s3"
	"finbox/go-api/models/bankconnectdetails"
	"finbox/go-api/models/bureauscore"
	"finbox/go-api/models/coapplicant"
	"finbox/go-api/models/companydetails"
	"finbox/go-api/models/deviations"
	"finbox/go-api/models/lendervariables"
	"finbox/go-api/models/loanapplication"
	"finbox/go-api/models/nonserviceablepincode"
	"finbox/go-api/models/personalloanoffer"
	"finbox/go-api/models/preselectedlender"
	"finbox/go-api/models/serviceablepincode"
	"finbox/go-api/models/unblockpolicy"
	"finbox/go-api/models/usereligibility"
	"finbox/go-api/models/users"
	"finbox/go-api/utils/bankconnectutils"
	"finbox/go-api/utils/calc"
	"finbox/go-api/utils/general"
	"finbox/go-api/utils/moduleutils"
	"finbox/go-api/utils/workflowutils"
)

var log = logger.Log

const (
	FailDecision         = "FAIL"
	FailSentinelDecision = "reject"
)
const (
	PassDecision         = "PASS"
	PassSentinelDecision = "pass"
)

const (
	Progressing     = 0
	DefaultProgress = -1
)

const (
	CantDecideDecision         = "CAN'T DECIDE"
	CantDecideSentinelDecision = "cant_decide"
)

const sentinelServiceName = "decision_run_sentinel"
const sentinelWorkflowServiceName = "decision_run_sentinel_workflow"

const maxTry = 7

func GetDecision(value bool) string {
	if value {
		return PassDecision
	}
	return FailDecision
}

/*
GetUnderWritingRules simply returns a list of underwriting rules
in language of LENDER
*/
func GetUnderWritingRules() []string {
	return []string{
		"Match application name with PAN card name",
		"Details verified from partner",
		"Reported DOB verified from partner",
		"Check loan purpose (Not gold, jewelry, stocks, repayment)",
	}
}

// parseOutputVariables parses the JSON string of output variables
func parseOutputVariables(outputVariablesJSON string, userID string) map[string]interface{} {
	var outputVariables map[string]interface{}
	err := json.Unmarshal([]byte(outputVariablesJSON), &outputVariables)
	if err != nil {
		logger.WithUser(userID).Warnf("[GetUnderWritingMaps] Failed to parse outputVariables for user %s. error: %v", userID, err)
		return nil
	}
	return outputVariables
}

/*
GetUnderWritingMaps returns an array of array of maps after running underwriting rules for given loan application ID
each rule map has 4 keys: rule, details, value and decision
Also returns rule version and decision date for each row
*/
func GetUnderWritingMaps(ctx context.Context, userID string, lenderID string) []map[string]interface{} {

	response := []map[string]interface{}{}

	endQuery := ""
	if lenderID != "" {
		endQuery = fmt.Sprintf(" and d.lender_id = '%s' ", lenderID)
	}

	type dbStruct struct {
		ReferenceID     string
		EvaluationID    string
		RuleVersion     string
		DecisionDate    string
		RuleType        string
		OutputVariables string
		IsSimulated     bool `db:"is_simulated"`
	}
	var ruleObjs []dbStruct
	query := `SELECT 
				reference_id as referenceid, 
				evaluation_id as evaluationid , 
				coalesce(rule_version, '') as ruleversion,
				coalesce(rule_type, '') as ruletype,
				d.created_at as decisiondate,
				coalesce(d.output_variables, '{}') as outputvariables,
            	EXISTS(select 1 from simulated_decision_engine_response where reference_id=d.reference_id) as is_simulated
			FROM 
				decision_engine_response d
			JOIN 
				users u ON d.user_id = u.user_id
			WHERE
				 u.user_id = $1 ` + endQuery + ` order by d.created_at desc`

	err := database.Select(&ruleObjs, query, userID)
	if err != nil {
		logger.WithContext(ctx).Warnf("[GetUnderWritingMaps] failed to get policies. err: %v, userID: %+v", err, userID)
	}

	ruleListMap := make(map[string][]map[string]string)
	acceptedPolicies := make(map[string]string)

	for _, ruleObj := range ruleObjs {
		if ruleObj.ReferenceID != "" {
			rules := []map[string]string{}
			// get rules from DB
			query = `SELECT rule, details, value, decision from decision_engine_rules where reference_id = $1 order by rule_no`
			rows, err := database.Queryx(query, ruleObj.ReferenceID)
			if err != nil {
				logger.WithContext(ctx).Warnf("[GetUnderWritingMaps] failed to get rules. err: %v, policiy: %+v", err, ruleObj)
			} else {
				defer rows.Close()
				for rows.Next() {
					ruleMapTemp := make(map[string]interface{})
					_ = rows.MapScan(ruleMapTemp)
					ruleMap := make(map[string]string)
					for key, value := range ruleMapTemp {
						ruleMap[key] = value.(string)
					}
					rules = append(rules, ruleMap)
				}
				err = rows.Err()
				if err != nil {
					logger.WithContext(ctx).Warnf("[GetUnderWritingMaps] failed to parse db rows. err: %v, policy: %+v", err, ruleObj)
				}
			}

			val, ok := ruleListMap[ruleObj.RuleVersion]
			if ok && len(val) != 0 {
				if len(rules) != 0 {

					outputVariables := parseOutputVariables(ruleObj.OutputVariables, userID)

					response = append(response, map[string]interface{}{
						"evaluationID":    ruleObj.EvaluationID,
						"ruleVersion":     ruleObj.RuleVersion,
						"ruleType":        ruleObj.RuleType,
						"rules":           rules,
						"decisionDate":    ruleObj.DecisionDate,
						"outputVariables": outputVariables,
						"isSimulated":     ruleObj.IsSimulated,
					})
				} else {
					continue
				}

			} else {
				ruleListMap[ruleObj.RuleVersion] = rules
				acceptedPolicies[ruleObj.RuleVersion] = ruleObj.ReferenceID
			}
		}
	}

	for _, ruleObj := range ruleObjs {
		if ruleObj.ReferenceID != "" {

			rules, ok := ruleListMap[ruleObj.RuleVersion]
			referenceID, ok2 := acceptedPolicies[ruleObj.RuleVersion]
			if ok && ok2 && ruleObj.ReferenceID == referenceID {

				outputVariables := parseOutputVariables(ruleObj.OutputVariables, userID)

				response = append(response, map[string]interface{}{
					"evaluationID":    ruleObj.EvaluationID,
					"ruleVersion":     ruleObj.RuleVersion,
					"ruleType":        ruleObj.RuleType,
					"rules":           rules,
					"decisionDate":    ruleObj.DecisionDate,
					"outputVariables": outputVariables,
					"isSimulated":     ruleObj.IsSimulated,
				})
			}
		}
	}

	if len(ruleObjs) > 0 {
		return response
	}

	type userDBResp struct {
		Name           string
		UniqueID       string
		Mobile         string
		Dob            string
		SourceEntityID string
	}
	userObj := userDBResp{}
	query = `select name, unique_id as uniqueid, mobile,
				source_entity_id as sourceentityid,
				to_char(u.dob, 'dd-Mon-yy') as dob
				from users u
				where u.user_id = $1`
	err = database.Get(&userObj, query, userID)
	if err != nil {
		log.Println("Underwriting error :", err)
		return nil
	}

	// run below code only for genius
	if userObj.SourceEntityID != constants.GeniusID {
		return nil
	}

	var loanApplicationID string
	query = `SELECT loan_application_id from loan_application where user_id = $1 order by created_at desc limit 1`
	err = database.Get(&loanApplicationID, query, userID)
	if err != nil {
		log.Println(err)
		return nil
	}

	// fetch data from loan details
	type loanDBRes struct {
		Salary      float64
		Income      float64
		LoanPurpose string
	}
	loanDetailsObj := loanDBRes{}
	query = "select coalesce(salary, 0) as salary, coalesce(income, 0) as income, coalesce(loan_purpose, '') as loanpurpose from user_loan_details where loan_application_id = $1"
	err = database.Get(&loanDetailsObj, query, loanApplicationID)
	if err != nil {
		log.Println("Underwriting error :", err)
		return nil
	}
	if loanDetailsObj.Salary == 0 {
		// set salary as income for new applications where salary becomes 0
		loanDetailsObj.Salary = loanDetailsObj.Income
	}

	rules := []map[string]string{}

	if len(userObj.UniqueID) >= 5 && len(userObj.Mobile) >= 5 {
		// check for leaves
		last5EmployeeID := userObj.UniqueID[len(userObj.UniqueID)-5:]
		last5MobileNo := userObj.Mobile[len(userObj.Mobile)-5:]
		type geniusDBRes struct {
			Netsalary   float64
			Leave30days int
			Leave60days int
			Leave90days int
			Dob         string
		}
		geniusObj := geniusDBRes{}
		query = "select netsalary, leave30days, leave60days, leave90days, dob from genius_employee_data where last5employeeid = $1 and last5mobileno = $2 and activestatus='Active' order by created_at desc limit 1"
		err = database.Get(&geniusObj, query, last5EmployeeID, last5MobileNo)
		if err != nil {
			log.Println("Underwriting error :", err)
			return nil
		}
		rules = append(rules, map[string]string{
			"rule":     "Leaves in last 30 and 60 days must not be more than 3, and in last 90 days must not be more than 6",
			"details":  fmt.Sprintf("Leaves in last 30 days : %d, 60 days : %d and 90 days : %d", geniusObj.Leave30days, geniusObj.Leave60days, geniusObj.Leave90days),
			"value":    "",
			"decision": GetDecision(geniusObj.Leave30days <= 3 && geniusObj.Leave60days <= 3 && geniusObj.Leave90days <= 6),
		})

		// check reported DOB
		rules = append(rules, map[string]string{
			"rule":     "Reported DOB should be same as Genius DOB",
			"details":  fmt.Sprintf("Reported DOB : %s, Genius DOB : %s", userObj.Dob, geniusObj.Dob),
			"value":    "",
			"decision": GetDecision(userObj.Dob == geniusObj.Dob),
		})

		// check reported salary
		reportedTimes := loanDetailsObj.Salary / geniusObj.Netsalary
		rules = append(rules, map[string]string{
			"rule":     "Reported Salary must not be more than 1.1X Genius Salary",
			"details":  fmt.Sprintf("Reported Salary : %.2f, Genius Salary : %.2f", loanDetailsObj.Salary, geniusObj.Netsalary),
			"value":    fmt.Sprintf("%.2fX", reportedTimes),
			"decision": GetDecision(reportedTimes <= 1.1),
		})
	}

	// check Manual KYC Rejects
	kycRejects := 0
	query = "select count(*) from loan_kyc_details where loan_id = $1 and status = $2"
	err = database.Get(&kycRejects, query, loanApplicationID, constants.KYCDocStatusRejected)
	if err != nil {
		log.Println("Underwriting error :", err)
		return nil
	}
	rules = append(rules, map[string]string{
		"rule":     "Manual KYC Rejects must not be more than 3",
		"details":  "",
		"value":    fmt.Sprintf("%d", kycRejects),
		"decision": GetDecision(kycRejects <= 3),
	})

	// check loan purpose
	var loanPurposeDecision string
	switch loanDetailsObj.LoanPurpose {
	case "Repay an existing loan":
		loanPurposeDecision = FailDecision
	case "Invest in stocks":
		loanPurposeDecision = FailDecision
	case "Buy Gold":
		loanPurposeDecision = FailDecision
	case "Buy Jewelry":
		loanPurposeDecision = FailDecision
	default:
		if strings.HasPrefix(loanPurposeDecision, "Other") {
			loanPurposeDecision = CantDecideDecision
		} else {
			loanPurposeDecision = PassDecision
		}
	}
	rules = append(rules, map[string]string{
		"rule":     "Check loan purpose (Not gold, jewelry, stocks, repayment)",
		"details":  "",
		"value":    loanDetailsObj.LoanPurpose,
		"decision": loanPurposeDecision,
	})

	// check FIS Score
	var deviceConnectData string
	var fisDecision string
	var fisScore float64 = -1
	var fisScoreString string
	fisAvailable := true
	query = `select coalesce(device_connect_data::TEXT, '') as deviceconnectdata
				from device_connect_details d
			where d.user_id = $1 and d.status = $2
			order by d.created_at desc limit 1
			`
	err = database.Get(&deviceConnectData, query, userID, constants.DeviceConnectStatusCompleted)
	if err != nil {
		if err == sql.ErrNoRows {
			fisDecision = CantDecideDecision
			fisAvailable = false
		} else {
			log.Println("Underwriting error :", err)
			return nil
		}
	}
	if fisAvailable && deviceConnectData != "" {
		deviceConnectObj := deviceConnect.DeviceConnectRespStruct{}
		var predictorData []deviceConnect.DeviceDataStruct
		err = json.Unmarshal([]byte(deviceConnectData), &deviceConnectObj)
		if err != nil {
			// try directly with array
			err = json.Unmarshal([]byte(deviceConnectData), &predictorData)
			if err != nil {
				panic(err)
			}
		} else {
			predictorData = deviceConnectObj.Data
		}
		for _, predictor := range predictorData {
			if predictor.Name == "score_xsell" {
				var notNull bool
				fisScore, notNull = predictor.Value.(float64)
				if !notNull {
					fisScore = -1
				}
				break
			}
		}
		if fisScore == -1 {
			fisDecision = CantDecideDecision
			fisScoreString = "Not Available"
		} else if fisScore >= 611 {
			fisDecision = PassDecision
			fisScoreString = fmt.Sprintf("%.0f", fisScore)
		} else {
			fisDecision = FailDecision
			fisScoreString = fmt.Sprintf("%.0f", fisScore)
		}
	}
	rules = append(rules, map[string]string{
		"rule":     "FIS Score should be >= 611",
		"details":  "",
		"value":    fisScoreString,
		"decision": fisDecision,
	})
	response = append(response, map[string]interface{}{
		"ruleVersion":  "genius_live_policy",
		"rules":        rules,
		"decisionDate": "",
	})
	return response
}

// ApproveLoan approves the Loan for a given lender ID and generates offers
// returns an error string and error object if any
func ApproveLoan(loanApplicationID, lenderID string, customAmount float64, entityRef, entityType, eventDescription string) (string, error) {

	// get unique id for user
	type dbRespStruct struct {
		UserID         string
		SourceEntityID string
		LoanStatus     int
		KYCStatus      int
		LoanType       string
		KYCSubStatus   sql.NullInt64
	}
	dbResp := dbRespStruct{}
	query := `select user_id as userid, source_entity_id as sourceentityid, status as loanstatus,
				coalesce(kyc_status, 0) as kycstatus, loan_type as loantype, kyc_sub_status as kycsubstatus
				from loan_application
			where loan_application_id = $1 and lender_id = $2`
	err := database.Get(&dbResp, query, loanApplicationID, lenderID)
	if err != nil {
		logger.WithLoanApplication(loanApplicationID).Errorln(err)
		if err == sql.ErrNoRows {
			return "loan not found", nil
		} else {
			return "", err
		}
	}

	if journey.IsABFLBLSourcing(dbResp.SourceEntityID) || journey.IsABFLPLSourcing(dbResp.SourceEntityID) || (lenderID == constants.ABFLPLID && dbResp.SourceEntityID == constants.MoneyControlID) {
		if dbResp.SourceEntityID == constants.DSAPreApprovedABFLID {
			// this conditino is to bypass the check for kyc sub status for preapproved loans where ApproveLoan can be
			// called from multiple places and not necessarily after KYC.
		} else if !general.InArr(constants.GetLoanStatusText(dbResp.LoanStatus, dbResp.KYCStatus), []string{"UNDER_REVIEW", "KYC_SUCCESS"}) {
			return "loan is not in valid state", nil
		}
	} else if constants.GetLoanStatusText(dbResp.LoanStatus, dbResp.KYCStatus) != "KYC_SUCCESS" {
		return "loan decision already taken or kyc not approved", nil
	}

	// check for available offers
	type offerTemplateStruct struct {
		Method            string
		Interest          float64
		Tenure            int
		ProcessingFee     float64
		ProcessingFeeType string
		GST               float64
		SignedAgreement   string
		UnsignedAgreement string
	}
	offerTemplates := []offerTemplateStruct{}
	query = `SELECT method, interest, tenure, processing_fee as processingfee,
				processing_fee_type as processingfeetype, gst,
				signed_agreement as signedagreement, unsigned_agreement as unsignedagreement
				FROM loan_offer_template where source_entity_id = $1 and lender_id = $2`
	err = database.Select(&offerTemplates, query, dbResp.SourceEntityID, lenderID)
	if err != nil {
		logger.WithLoanApplication(loanApplicationID).Errorln(err)
		return "", err
	}

	if len(offerTemplates) == 0 {
		logger.WithLoanApplication(loanApplicationID).Warnln("No offers available for the loan application")
		return "No offers available for the loan application", nil
	}

	tx, _ := database.Begin()

	for _, offerTemplate := range offerTemplates {

		var eligibleAmount float64
		eligibleObj := loanprogram.GetUpdatedLoanOfferTemplate(dbResp.UserID, dbResp.SourceEntityID, offerTemplate.Tenure)
		// override amount if customAmount present
		eligibleAmount = eligibleObj.EligibleAmount
		if eligibleObj.Interest != -1 {
			offerTemplate.Interest = eligibleObj.Interest
		}
		if eligibleObj.ProcessingFee != -1 {
			offerTemplate.ProcessingFee = eligibleObj.ProcessingFee
		}
		if eligibleObj.ProcessingFeeType != "" {
			offerTemplate.ProcessingFeeType = eligibleObj.ProcessingFeeType
		}
		if eligibleObj.Tenure != 0 {
			offerTemplate.Tenure = eligibleObj.Tenure
		}
		if customAmount > 0 {
			eligibleAmount = customAmount
		}
		processingFee := calc.CalculateProcessingFee(eligibleAmount, offerTemplate.ProcessingFee, offerTemplate.ProcessingFeeType)
		if journey.IsRoundOffRequired(dbResp.SourceEntityID, lenderID) {
			processingFee = math.Round(processingFee)
		}
		if !journey.IsProgramApplicable(dbResp.SourceEntityID) && !journey.IsMultiOfferAllowed(dbResp.SourceEntityID, dbResp.UserID) && !journey.IsOfferFlowV2(dbResp.UserID, dbResp.SourceEntityID) {
			offerQuery := `insert into loan_offer (loan_offer_id, loan_id, amount,
				interest, tenure, processing_fee, gst, created_by,
				created_at, unsigned_agreement, signed_agreement, method, lender_id, reference_id) values (
					uuid_generate_v4(), $1, $2, $3, $4, $5, $6, $7, NOW(), $8, $9, $10, $11, $12
				)`
			_, err = tx.Exec(offerQuery, loanApplicationID, eligibleAmount, offerTemplate.Interest,
				offerTemplate.Tenure, processingFee, offerTemplate.GST,
				lenderID, offerTemplate.UnsignedAgreement, offerTemplate.SignedAgreement,
				offerTemplate.Method, lenderID, eligibleObj.BREReferenceID)
			if err != nil {
				logger.WithLoanApplication(loanApplicationID).Warnln(err)
				tx.Rollback()
				return "", err
			}
		}
	}

	dateTimeNowString := general.GetTimeStampString()

	if journey.IsABFLBLSourcing(dbResp.SourceEntityID) || journey.IsABFLPLSourcing(dbResp.SourceEntityID) || (lenderID == constants.ABFLPLID && dbResp.SourceEntityID == constants.MoneyControlID) {
		query = `update loan_application set
				approval_date = $1,
				updated_at = current_timestamp, status = $2, kyc_status = $3
				where loan_application_id = $4`

		_, err = tx.Exec(query, dateTimeNowString, constants.LoanStatusLoanApproved, constants.LoanKYCStatusDocApproved, loanApplicationID)
		if err != nil {
			logger.WithLoanApplication(loanApplicationID).Warnln(err)
			tx.Rollback()
			return "", err
		}
	} else {
		query = `update loan_application set
				approval_date = $1,
				updated_at = current_timestamp, status = $2
				where loan_application_id = $3`

		_, err = tx.Exec(query, dateTimeNowString, constants.LoanStatusLoanApproved, loanApplicationID)
		if err != nil {
			logger.WithLoanApplication(loanApplicationID).Warnln(err)
			tx.Rollback()
			return "", err
		}
	}

	tx.Commit()

	if dbResp.LoanType == constants.LoanTypeCreditLine || dbResp.LoanType == constants.LoanTypeOverDraft {
		// also attach offer if credit line

		type loanOfferStruct struct {
			LoanOfferID       string
			Tenure            int
			Amount            float64
			Interest          float64
			ProcessingFee     float64
			GST               float64
			UnsignedAgreement string
			SignedAgreement   string
			Method            string
		}
		loanOfferObj := loanOfferStruct{}
		query = `select tenure, amount, interest, processing_fee as processingfee,
					gst, unsigned_agreement as unsignedagreement,
					signed_agreement as signedagreement,
					method, loan_offer_id as loanofferid
				from loan_offer where loan_id = $1 limit 1`
		err = database.Get(&loanOfferObj, query, loanApplicationID)
		if err != nil {
			logger.WithLoanApplication(loanApplicationID).Warnln(err)
			return "", err
		}
		//generate agreement for mismatch dob

		acceptDate := time.Now()
		emi, advanceEMI, _ := calc.GetEMI(loanOfferObj.Method, loanOfferObj.Amount, loanOfferObj.Tenure, loanOfferObj.Interest, acceptDate, dbResp.SourceEntityID, lenderID, dbResp.UserID)

		loanObjToUpdate := loanapplication.StructForSet{
			ID:                        loanApplicationID,
			Tenure:                    loanOfferObj.Tenure,
			Amount:                    loanOfferObj.Amount,
			Interest:                  &loanOfferObj.Interest,
			ProcessingFee:             &loanOfferObj.ProcessingFee,
			EMI:                       emi,
			GST:                       loanOfferObj.GST,
			UnsignedAgreementTemplate: loanOfferObj.UnsignedAgreement,
			SignedAgreementTemplate:   loanOfferObj.SignedAgreement,
			LoanOfferID:               loanOfferObj.LoanOfferID,
			AdvanceEMI:                &advanceEMI,
		}

		err = loanapplication.Update(nil, loanObjToUpdate)
		if err != nil {
			logger.WithLoanApplication(loanApplicationID).Warnln(err)
			return "", err
		}
	}

	if general.InArr(lenderID, []string{constants.ABFLID, constants.ABFLPLID, constants.IIFLID}) && general.InArr(dbResp.LoanType, []string{constants.LoanTypePersonalLoan, constants.LoanTypeBusinessLoan}) && !general.InArr(dbResp.SourceEntityID, []string{constants.DSAGpayABFLBL}) {
		if journey.ShowInsurance(dbResp.UserID, dbResp.SourceEntityID, lenderID) {
			// Call insurane getPremium API
			err = insuranceutils.InsurancePremiumForLoanOffers(loanApplicationID, dbResp.SourceEntityID, dbResp.UserID, lenderID)
			if err != nil {
				logger.WithLoanApplication(loanApplicationID).Warnln(err)
				return "", err
			}
		}
	}

	addKYCModule := true
	if dbResp.SourceEntityID == constants.DSAPreApprovedABFLID {
		iskycRequired, err := users.GetPartnerDataField(dbResp.UserID, "kycRequired")
		if err != nil {
			logger.WithLoanApplication(loanApplicationID).Error(err)
		}
		if iskycRequired == "TRUE" {
			addKYCModule = true
		} else {
			addKYCModule = false
		}
	}

	if addKYCModule {
		err = moduleutils.UpdateKYC(nil, constants.ModuleKYCCompleted, dbResp.UserID, loanApplicationID, dbResp.SourceEntityID)
		if err != nil {
			logger.WithLoanApplication(loanApplicationID).Error(err)
			return "", err
		}

		queryUm := `update user_module_mapping set module_sub_status = 1, updated_at = current_timestamp where loan_application_id = $1 and user_id = $2 and module_name = $3`
		_, err = database.Exec(queryUm, loanApplicationID, dbResp.UserID, "KYC")
		if err != nil {
			logger.WithLoanApplication(loanApplicationID).Warnln(err)
			return "", err
		}
	}

	if dbResp.SourceEntityID == constants.VyaparID {
		lenderID, _ := GetLenderID(dbResp.SourceEntityID, dbResp.UserID, "")
		if lenderID == constants.LendingKartID {
			err := usermodulemapping.Create(nil, dbResp.UserID, dbResp.UserID, constants.EndModule, constants.UserModuleStatusCompleted, loanApplicationID)
			if err != nil {
				logger.WithUser(dbResp.UserID).Error(err)
				return "", err
			}
		}
	}

	if journey.EnablePvtLtdJourney(dbResp.SourceEntityID) {
		// TODO: in future remove EnablePvtLtdJourney check to enable clients with pvt ltd journey disabled but going to kotak / tcap
		lenderID := preselectedlender.Get(dbResp.UserID)
		if general.InArr(lenderID, []string{constants.KotakID, constants.TataCapitalID, constants.LendingKartID}) {
			// end journey here
			err := usermodulemapping.Create(nil, dbResp.UserID, dbResp.UserID, constants.EndModule, constants.UserModuleStatusCompleted, loanApplicationID)
			if err != nil {
				logger.WithUser(dbResp.UserID).Error(err)
				return "", err
			}
		}
	}

	if entityRef != "" {
		go activity.ActivityLogger(dbResp.UserID, dbResp.SourceEntityID, entityRef, entityType, constants.ActivityLoanApproved, eventDescription, loanApplicationID, dateTimeNowString, false)
	} else {
		go activity.ActivityLogger(dbResp.UserID, dbResp.SourceEntityID, "", constants.EntityTypeSystem, constants.ActivityLoanApproved, eventDescription, loanApplicationID, dateTimeNowString, false)
	}

	return "", nil
}

// RejectLoan rejects a loan
func RejectLoan(loanApplicationID string, lenderID string, entityType string, entityRef string, rejectReason string) (string, error) {
	type userStruct struct {
		UserID         string
		SourceEntityID string
		Status         int
	}

	var userDBObj userStruct
	if lenderID == "" {
		query := `select user_id as userid, source_entity_id as sourceentityid, status from loan_application where loan_application_id = $1`
		err := database.Get(&userDBObj, query, loanApplicationID)
		if err != nil {
			return "loan not found", nil
		}
	} else {
		query := `select user_id as userid, source_entity_id as sourceentityid, status from loan_application where loan_application_id = $1 and lender_id = $2`
		err := database.Get(&userDBObj, query, loanApplicationID, lenderID)
		if err != nil {
			return "loan not found", nil
		}
	}

	if userDBObj.Status == constants.LoanStatusLoanRejected {
		return "Loan already rejected", nil
	}

	if userDBObj.Status == constants.LoanStatusClosed || userDBObj.Status == constants.LoanStatusDisbursed || userDBObj.Status == constants.LoanStatusCancelled {
		return "Loan already closed / disbursed / cancelled", nil
	}

	query := `update loan_application set
				 updated_at = current_timestamp, status = $1
				where loan_application_id = $2`

	_, err := database.Exec(query, constants.LoanStatusLoanRejected, loanApplicationID)
	if err != nil {
		return "", err
	}
	dateTimeNowString := general.GetTimeStampString()
	activityObj := activity.ActivityEvent{
		UserID:            userDBObj.UserID,
		SourceEntityID:    userDBObj.SourceEntityID,
		LoanApplicationID: loanApplicationID,
		EntityType:        entityType,
		EntityRef:         entityRef,
		EventType:         constants.ActivityLoanRejected,
		Description:       fmt.Sprintf(`{"rejectionReason":"%s", "lender":"%s"}`, rejectReason, constants.LenderNamesMap[lenderID]),
	}
	activity.RegisterEvent(&activityObj, dateTimeNowString)

	if journey.SendSMSOnDisqualification(userDBObj.SourceEntityID) {
		if journey.IsMuthootCLPartner(userDBObj.SourceEntityID) {
			go func() {
				defer errorHandler.RecoveryNoResponse()
				userObj, err := users.Get(userDBObj.UserID)
				if err != nil {
					logger.WithUser(userDBObj.UserID).Error(err)
					panic(err)
				}
				text := fmt.Sprintf(sms.MuthootCLNotEligible.DLTTemplateID, userObj.Name)
				_, err = sms.SendSMSWithFallback(userDBObj.UserID, userObj.Mobile, text, sms.MuthootCLNotEligible, false)
				if err != nil {
					logger.WithUser(userDBObj.UserID).Errorln("Error sending user SMS:", err)
				}
			}()
		}
	}

	// cancel nach if exists
	// go cashfreeenach.CancelSubscription(loanApplicationID)
	// go digio.CancelENACH(loanApplicationID)
	// go digio.CancelPhysical(loanApplicationID)
	return "", nil
}

// RejectLoanV2  : rejects a loan without lender or source entity specific.  Mainly it doesn't send email
func RejectLoanV2(loanApplicationID string, lenderID string, entityType string, entityRef string, eventType string, rejectReason string) (string, error) {
	type userStruct struct {
		UserID         string
		SourceEntityID string
		Status         int
	}

	var userDBObj userStruct
	if lenderID == "" {
		query := `select user_id as userid, source_entity_id as sourceentityid, status from loan_application where loan_application_id = $1`
		err := database.Get(&userDBObj, query, loanApplicationID)
		if err != nil {
			return "loan not found", nil
		}
	} else {
		query := `select user_id as userid, source_entity_id as sourceentityid, status from loan_application where loan_application_id = $1 and lender_id = $2`
		err := database.Get(&userDBObj, query, loanApplicationID, lenderID)
		if err != nil {
			return "loan not found", nil
		}
	}

	if userDBObj.Status == constants.LoanStatusLoanRejected {
		return "Loan already rejected", nil
	}

	if userDBObj.Status == constants.LoanStatusClosed || userDBObj.Status == constants.LoanStatusDisbursed || userDBObj.Status == constants.LoanStatusCancelled {
		return "Loan already closed / disbursed / cancelled", nil
	}

	query := `update loan_application set
				 updated_at = current_timestamp, status = $1
				where loan_application_id = $2`

	_, err := database.Exec(query, constants.LoanStatusLoanRejected, loanApplicationID)
	if err != nil {
		return "", err
	}
	dateTimeNowString := general.GetTimeStampString()
	activityObj := activity.ActivityEvent{
		UserID:            userDBObj.UserID,
		SourceEntityID:    userDBObj.SourceEntityID,
		LoanApplicationID: loanApplicationID,
		EntityType:        entityType,
		EntityRef:         entityRef,
		EventType:         eventType,
		Description:       rejectReason,
	}
	activity.RegisterEvent(&activityObj, dateTimeNowString)
	return "", nil
}

// UpdateUserQualification checks and update user qualification based on bureau and device connect data
func UpdateUserQualification(userID string, sourceEntityID string) error {

	// new sentinel evaluation flow with percentage rollout
	if journey.IsBureauSentinelEvalFlow(userID, sourceEntityID) || journey.IsMultiOfferV2Allowed(sourceEntityID, userID) {
		return UpdateUserQualificationV2(userID, sourceEntityID)
	}

	// default value is qualified
	userStatus := constants.UserStatusQualified
	acitivityDescriptionUserQualified := "bureau" //default activity description of user qualified.
	entityRef := ""

	var rejectReason string

	type userStruct struct {
		PAN               string
		UniqueID          string
		ClientID          int
		Email             string
		Mobile            string
		SDKVersion        string
		Status            int
		UserProgramStatus int
		Name              string
	}

	var userObj userStruct

	query := `select coalesce(pan, '') as pan, unique_id as uniqueid,
					coalesce(email, '') as email, mobile, u.status as status,
					coalesce(s.client_id, 0) as clientid,
					coalesce(u.sdk_version, '') as sdkversion,
					coalesce(name, '') as name
				from users u join source_entity s on u.source_entity_id = s.source_entity_id where user_id = $1;`
	err := database.Get(&userObj, query, userID)

	if err != nil {
		logger.WithUser(userID).Errorln("couldn't get user information for user: ", userID)
		return err
	}
	userObj.PAN = strings.ToUpper(userObj.PAN)

	if userObj.PAN == constants.MockPANUserDisqualify {
		// testing PAN for auto disqualify
		userStatus = constants.UserStatusDisqualified
		rejectReason = "Testing PAN used for disqualify"
	}

	// switch to multi offer qualification if multi offer is allowed for the source entity
	if journey.IsMultiOfferAllowed(sourceEntityID, userID) {
		err = MultiOfferQualification(sourceEntityID, userID, userObj.Mobile, userObj.UniqueID)
		if err != nil {
			logger.WithUser(userID).Errorln(err)
			return err
		}
		return nil
	}

	// TODO (Get decsion from sentinel)
	if sourceEntityID == constants.TataNexarcID {

		lenderID, _ := GetLenderID(sourceEntityID, userID, "")
		if general.InArr(lenderID, []string{"", constants.XYZLenderID}) {
			bureauScore, err := bureauscore.Get(context.Background(), userID)
			if err != nil {
				logger.WithUser(userID).Errorln(err)
				return err
			}

			if bureauScore.ExperianScore == -1 {

				statusQuery := `SELECT status FROM experian_reports WHERE user_id = $1 ORDER BY created_at DESC LIMIT 1`
				var status string

				err = database.Get(&status, statusQuery, userID)
				if err != nil {
					logger.WithUser(userID).Error(err)
					errorHandler.ReportToSentryWithoutRequest(err)
					return err

				}

				if !general.InArr(status, []string{constants.BureauStatusCompleted, constants.BureauStatusRecordNotFound}) {
					err := errors.New("experian failed to pull bureau score")
					logger.WithUser(userID).Error(err)
					return err
				}
			}

			query := `SELECT constitution from user_business where user_id = $1`
			var constitution string
			err = database.Get(&constitution, query, userID)
			if err != nil {
				logger.WithUser(userID).Error(err)
				errorHandler.ReportToSentryWithoutRequest(err)
				return err
			}
			if constitution != "" && constitution != constants.Proprietorship {
				lenderID = constants.KotakID
			} else {
				if bureauScore.ExperianScore > 713 {
					lenderID = constants.IIFLID
				} else {
					lenderID = constants.KotakID
				}
			}

			err = preselectedlender.Set(userID, lenderID)
			if err != nil {
				logger.WithUser(userID).Error(err)
				errorHandler.ReportToSentryWithoutRequest(err)
				return err
			}

			go func() {
				errorHandler.RecoveryNoResponse()
				err := journeyutils.RouteToLender(userID, lenderID, constants.LenderSelectionTypeBureauScore)
				if err != nil {
					logger.WithUser(userID).Errorln(err)
				}
			}()

		}

		if conf.ENV == conf.ENV_PROD && lenderID == constants.IIFLID {
			duplicityStatus, reason, prospectNo, redirectURL := fraudcheckutils.DoPANDuplicityCheckIIFL(userID, sourceEntityID)
			if duplicityStatus {
				rejectReason = fmt.Sprintf("%s with ProspectID %s redirectURL: %s", reason, prospectNo, redirectURL)
				userStatus = constants.UserStatusDisqualified
			}
		}
	}

	if conf.ENV == conf.ENV_PROD && userStatus != constants.UserStatusDisqualified {
		userStatus, rejectReason, err = CheckIfBlackListedUser(userStatus, userObj.Mobile, sourceEntityID)
		if err != nil {
			logger.WithUser(userID).Errorln(err)
		}
	}

	var underReviewReasons []string

	if userStatus != constants.UserStatusDisqualified && !general.InArr(sourceEntityID, []string{constants.UrbanCompanyID}) && !journey.IsPFLSourcing(sourceEntityID) && conf.ENV == conf.ENV_PROD {
		// check for existing pan in an active cases in loans
		var panCount int
		query = `SELECT count(*) from loan_application la join users u on u.user_id = la.user_id
						where u.pan = $1 and u.source_entity_id = $2 and la.status not in
						($3, $4, $5) and u.unique_id != $6 and u.status not in ($7, $8)`
		err = database.Get(&panCount, query, userObj.PAN, sourceEntityID, constants.LoanStatusLoanRejected,
			constants.LoanStatusClosed, constants.LoanStatusCancelled, userObj.UniqueID, constants.UserStatusArchived, constants.UserStatusExpired)
		if err != nil {
			log.Println(err)
		} else if panCount > 0 {
			userStatus = constants.UserStatusDisqualified
			rejectReason = constants.ErrDuplicatePANMatchWithAnApplication
		} else if sourceEntityID != constants.GeniusID {
			// TODO: IMPORTANT: handle this for closed applications later
			// check for active cases in users
			query = `SELECT count(*) from users
				where pan = $1 and source_entity_id = $2 and status not in ($3, $4, $5) and unique_id != $6`
			err = database.Get(&panCount, query, userObj.PAN, sourceEntityID,
				constants.UserStatusDisqualified, constants.UserStatusArchived, constants.UserStatusExpired, userObj.UniqueID)
			if err != nil {
				log.Println(err)
			} else if panCount > 0 {
				userStatus = constants.UserStatusDisqualified
				rejectReason = constants.ErrDuplicatePANMatchWithAnApplication
			}
		}

		if userStatus != constants.UserStatusDisqualified && journey.IsNexarcLendingKart(userID, sourceEntityID) {
			// using the lender integration service
			isDuplicate := lendingkart.DedupeCheck(context.Background(), userID, sourceEntityID)
			if isDuplicate {
				rejectReason = "lead exists at lendingKart end"
				userStatus = constants.UserStatusDisqualified
			}
		}

		// check for existing email in an active cases in loans
		if userStatus != constants.UserStatusDisqualified && journey.CheckDuplicateMailForRejection(sourceEntityID) && userObj.Email != "" {
			var emailCount int
			rejectReason = "Duplicate email match with an active application/loan"
			query = `SELECT count(*) from loan_application la join users u on u.user_id = la.user_id
						where u.email = $1 and u.source_entity_id = $2 and la.status not in
						($3, $4, $5) and u.unique_id != $6 and u.status not in ($7, $8)`
			err = database.Get(&emailCount, query, strings.ToLower(userObj.Email), sourceEntityID, constants.LoanStatusLoanRejected,
				constants.LoanStatusClosed, constants.LoanStatusCancelled, userObj.UniqueID, constants.UserStatusArchived, constants.UserStatusExpired)
			if err != nil {
				log.Println(err)
			} else if emailCount > 0 {
				if journey.CheckUnderReview(sourceEntityID, "email") {
					userStatus = constants.UserStatusUnderReview
					underReviewReasons = append(underReviewReasons, rejectReason)
				} else {
					userStatus = constants.UserStatusDisqualified
				}
			} else if sourceEntityID != constants.GeniusID {
				// TODO: IMPORTANT: handle this for closed applications later
				// check for active cases in users
				query = `SELECT count(*) from users
				where email = $1 and source_entity_id = $2 and status not in ($3, $4, $5) and unique_id != $6`
				err = database.Get(&emailCount, query, strings.ToLower(userObj.Email), sourceEntityID,
					constants.UserStatusDisqualified, constants.UserStatusArchived, constants.UserStatusExpired, userObj.UniqueID)
				if err != nil {
					log.Println(err)
				} else if emailCount > 0 {
					if journey.CheckUnderReview(sourceEntityID, "email") {
						userStatus = constants.UserStatusUnderReview
						underReviewReasons = append(underReviewReasons, rejectReason)
					} else {
						userStatus = constants.UserStatusDisqualified
					}
				}
			}
		}

		// check for existing mobile in an active cases in loans
		if userStatus != constants.UserStatusDisqualified {
			var mobileCount int
			rejectReason = "Duplicate mobile match with an active application/loan"
			// TODO: optimize this query using above query.
			query = `SELECT count(*) from loan_application la join users u on u.user_id = la.user_id
						where u.mobile = $1 and u.source_entity_id = $2 and la.status not in
						($3, $4, $5) and u.status not in ($6, $7) and u.unique_id != $8`
			err = database.Get(&mobileCount, query, userObj.Mobile, sourceEntityID, constants.LoanStatusLoanRejected,
				constants.LoanStatusClosed, constants.LoanStatusCancelled, constants.UserStatusArchived, constants.UserStatusExpired, userObj.UniqueID)
			if err != nil {
				log.Println(err)
				errorHandler.ReportToSentryWithoutRequest(err)
			} else if mobileCount > 0 {
				if journey.CheckUnderReview(sourceEntityID, "mobile") {
					userStatus = constants.UserStatusUnderReview
					underReviewReasons = append(underReviewReasons, rejectReason)
				} else {
					userStatus = constants.UserStatusDisqualified
				}
			} else if sourceEntityID != constants.GeniusID {
				// TODO: IMPORTANT: handle this for closed applications later
				// check for active cases in users
				// TODO: optimize this query.
				query = `SELECT count(*) from users
				where mobile = $1 and source_entity_id = $2 and status not in ($3, $4, $5) and unique_id != $6`
				err = database.Get(&mobileCount, query, userObj.Mobile, sourceEntityID,
					constants.UserStatusDisqualified, constants.UserStatusArchived, constants.UserStatusExpired, userObj.UniqueID)
				if err != nil {
					log.Println(err)
					errorHandler.ReportToSentryWithoutRequest(err)
				} else if mobileCount > 0 {
					if journey.CheckUnderReview(sourceEntityID, "mobile") {
						userStatus = constants.UserStatusUnderReview
						underReviewReasons = append(underReviewReasons, rejectReason)
					} else {
						userStatus = constants.UserStatusDisqualified
					}
				}
			}
		}

		// check for android id here
		if userObj.ClientID > 0 && userStatus != constants.UserStatusDisqualified {
			var androidID string
			rejectReason = "Duplicate android id match with an active application/loan"
			query = `SELECT coalesce(android_id, '') as androidid from customer_android_id_mapping
						where customer_id = $1 AND client_id = $2`
			err = database.Get(&androidID, query, userObj.UniqueID, userObj.ClientID)
			if err != nil {
				log.Println(err)
			} else if androidID != "" {
				// check if android id matches with an active case
				var disbursedCount int
				query = `select count(*) from loan_application la
				join users u on la.user_id = u.user_id
				join customer_android_id_mapping c on u.unique_id = c.customer_id and c.client_id = $1
				where la.source_entity_id = $2 and la.status not in ($3, $4, $5) and
				u.unique_id != $6 and u.status not in ($7, $8) and (string_to_array('` + androidID + `', '|') && string_to_array(c.android_id, '|'))`
				err = database.Get(&disbursedCount, query, userObj.ClientID, sourceEntityID,
					constants.LoanStatusCancelled, constants.LoanStatusLoanRejected,
					constants.LoanStatusClosed, userObj.UniqueID, constants.UserStatusArchived, constants.UserStatusExpired)
				if err != nil {
					log.Println(err)
				} else if disbursedCount > 0 {
					if journey.CheckUnderReview(sourceEntityID, "android_id") {
						userStatus = constants.UserStatusUnderReview
						underReviewReasons = append(underReviewReasons, rejectReason)
					} else {
						userStatus = constants.UserStatusDisqualified
					}

				} else if sourceEntityID != constants.GeniusID {
					// TODO: IMPORTANT: handle this for closed applications later
					// check if android id matches with an active user
					query = `select count(*) from users u
					join customer_android_id_mapping c on u.unique_id = c.customer_id and c.client_id = $1
					where u.source_entity_id = $2 and u.status not in ($3, $4, $5) and
					u.unique_id != $6 and (string_to_array('` + androidID + `', '|') && string_to_array(c.android_id, '|'))`
					err = database.Get(&disbursedCount, query, userObj.ClientID, sourceEntityID,
						constants.UserStatusDisqualified, constants.UserStatusArchived, constants.UserStatusExpired, userObj.UniqueID)
					if err != nil {
						log.Println(err)
					} else if disbursedCount > 0 {
						if journey.CheckUnderReview(sourceEntityID, "android_id") {
							userStatus = constants.UserStatusUnderReview
							underReviewReasons = append(underReviewReasons, rejectReason)
						} else {
							userStatus = constants.UserStatusDisqualified
						}
					}
				}
			} else if general.InArr(sourceEntityID, []string{constants.ShopKiranaID, constants.GimBooksID}) {
				userStatus = constants.UserStatusDisqualified
				rejectReason = "android id not found"
			}
		}

		//Check for age restriction for under review
		if constants.UnderReviewItems[sourceEntityID]["age"] {
			var ageCheckCount int
			var dob string
			query := `SELECT coalesce(to_char(dob, 'YYYY-MM-DD'), '') from users where user_id = $1`
			err := database.Get(&dob, query, userID)
			if err != nil {
				log.Println(err)
			} else if dob != "" && ageCheckCount == 0 {
				dobObj, _ := time.Parse("2006-01-02", dob)
				age := general.AgeAt(dobObj, time.Now())
				log.Println("age is ", age)
				if (age < 18 || age >= 65) && constants.UnderReviewItems[sourceEntityID]["age18_65"] {
					userStatus = constants.UserStatusUnderReview
					underReviewReasons = append(underReviewReasons, "age should be >= 18 and < 65")
				}
			}
		}
	}

	lenderID, _ := GetLenderID(sourceEntityID, userID, "")
	if sourceEntityID == constants.VyaparID && lenderID == "" {
		rejectReason = "lead exists at lendingKart end"
		userStatus = constants.UserStatusDisqualified
	}

	if userStatus != constants.UserStatusDisqualified && (journey.IIFLBLPolicy(userID, sourceEntityID) || journey.IIFLPLPolicy(sourceEntityID) || journey.IsNexarcIIFL(userID, sourceEntityID)) {
		// check for bureau availability and reject
		userStatus, rejectReason, _, _, err = checkBureauAvailability(userID, userStatus)
		if err != nil {
			log.Println(err)
		}
	}

	if journey.IsNexarcKotak(userID, sourceEntityID) && userStatus != constants.UserStatusDisqualified {
		rejectReason, userStatus, err = kotakFraudChecks(userID, sourceEntityID, "", userStatus)
		if err != nil {
			logger.WithUser(userID).Println(err.Error())
			return err
		}
	}

	// call rule engine
	if journey.IIFLPLPolicy(sourceEntityID) && userStatus != constants.UserStatusDisqualified {
		var decision string
		var firstResp DecisionRunStruct

		var done bool
		var ruleVersion string
		ruleVersionChain := []string{"iifl_pl_bureau_v2.1"}
		ok := unblockpolicy.IsUnblocked(sourceEntityID, constants.UnblockSolarPolicies)
		if ok {
			// ok -> if unblocked solar, use that only
			ruleVersionChain = []string{"iifl_pl_bureau_v2.1", "iifl_pl_bureau_v2.2"}
		}
		if preapproved.IsActiveForLender(userObj.PAN, userObj.Mobile, sourceEntityID, lenderID) {
			ok = unblockpolicy.IsUnblocked(sourceEntityID, constants.PreApprovedPolicies)
			if ok {
				ruleVersionChain = []string{"iifl_pl_bureau_repeat_loan_v1"}
			}
		}
		for idx := 0; idx < len(ruleVersionChain); idx++ {
			ruleVersion = ruleVersionChain[idx]
			decision, done, rejectReason, firstResp = CallRuleEngine(userID, sourceEntityID, constants.IIFLID, ruleVersion)
			if !done {
				// do not update user wait status
				return errors.New("decision API failed for userID: " + userID)
			}
			if decision != constants.UnderwritingDecisionReject {
				break
			}
			call, ok := firstResp.Data.OutputVariables["call"].(bool)
			if !call && ok && general.InArr(ruleVersion, []string{"iifl_pl_bureau_v2.1"}) {
				idx++
			}
		}
		if decision == constants.UnderwritingDecisionReject {
			if !journey.IsOfferFlowV2(userID, sourceEntityID) {
				// update eligibility if rejected
				err = UpdateEligibility(userID, sourceEntityID, decision, firstResp, ruleVersion)
				if err != nil {
					logger.WithUser(userID).Errorln(err)
					return err
				}
			}
			userStatus = constants.UserStatusDisqualified
		} else {
			if journey.IsOfferFlowV2(userID, sourceEntityID) {
				loanType, _, _ := journey.GetLoanType(sourceEntityID)
				userStatus, err = createTempOffer(loanType, constants.IIFLID, userID, sourceEntityID, userStatus)
				if err != nil {
					logger.WithUser(userID).Errorln(err)
					return err
				}
			}
		}

		//else {
		// 	var done bool
		// 	ruleVersion := "iifl_v2.8.1"
		// 	decision, done, rejectReason, firstResp = CallRuleEngine(userID, sourceEntityID, constants.IIFLID, ruleVersion)
		// 	if !done {
		// 		// do not update user status
		// 		return errors.New("decision API failed for userID: " + userID)
		// 	}
		// 	// update eligibility
		// 	err = UpdateEligibility(userID, sourceEntityID, decision, firstResp, ruleVersion)
		// 	if err != nil {
		// 		log.Println(err)
		// 		return err
		// 	}
		// 	if decision == constants.UnderwritingDecisionReject {
		// 		userStatus = constants.UserStatusDisqualified
		// 	}
		// }
	}

	if sourceEntityID == constants.NiyoID && userStatus != constants.UserStatusDisqualified && conf.ENV == conf.ENV_PROD {
		var decision string
		var done bool
		var firstResp DecisionRunStruct
		ruleVersion := "niyo_v1.2"
		decision, done, rejectReason, firstResp = CallRuleEngine(userID, sourceEntityID, constants.TrustLenderID, ruleVersion)
		if !done {
			// do not update user status
			return errors.New("decision API failed for userID: " + userID)
		}
		// update eligibility
		err = UpdateEligibility(userID, sourceEntityID, decision, firstResp, ruleVersion)
		if err != nil {
			log.Println(err)
			return err
		}
		if decision == constants.UnderwritingDecisionReject {
			userStatus = constants.UserStatusDisqualified
		}
	}
	// call rule engine
	if sourceEntityID == constants.SupremeSolarID && userStatus != constants.UserStatusDisqualified {
		var decision string
		var done bool
		var firstResp DecisionRunStruct
		ruleVersion := "ecofy_supreme_solar_v1"
		decision, done, rejectReason, firstResp = CallRuleEngine(userID, sourceEntityID, constants.EcofyID, ruleVersion)
		if !done {
			// do not update user status
			return errors.New("decision API failed for userID: " + userID)
		}
		// update eligibility
		err = UpdateEligibility(userID, sourceEntityID, decision, firstResp, ruleVersion)
		if err != nil {
			logger.WithUser(userID).Errorln(err)
			return err
		}
		if decision == constants.UnderwritingDecisionReject {
			userStatus = constants.UserStatusDisqualified
		}
	}

	// call bureau rule engine forbl, so that if this fails no need to go for bank statement upload
	if userStatus != constants.UserStatusDisqualified && (journey.IIFLBLPolicy(userID, sourceEntityID) || journey.IsNexarcIIFL(userID, sourceEntityID)) {
		var decision string
		var done bool
		var ruleVersion string
		var firstResp DecisionRunStruct
		ruleVersionChain := []string{"iifl_bl_bureau_v3.0.1", "iifl_bl_bureau_v3.1.1"}
		// if sourceEntityID == constants.VyaparID {
		// 	ruleVersionChain = append(ruleVersionChain, "vyapar_v1")
		// }
		if sourceEntityID == constants.TataNexarcID {
			ruleVersionChain = []string{constants.NexarcGatingPolicy}
		}
		for idx := 0; idx < len(ruleVersionChain); idx++ {
			ruleVersion = ruleVersionChain[idx]
			decision, done, rejectReason, firstResp = CallRuleEngine(userID, sourceEntityID, constants.IIFLID, ruleVersion)
			if !done {
				// do not update user wait status
				return errors.New("decision API failed for userID: " + userID)
			}
			if decision != constants.UnderwritingDecisionReject {
				break
			}
			call, ok := firstResp.Data.OutputVariables["call"].(bool)
			if !call && ok && general.InArr(ruleVersion, []string{"iifl_bl_bureau_v3.0.1"}) {
				idx++
			}
		}
		if decision == constants.UnderwritingDecisionReject {
			if !journey.IsOfferFlowV2(userID, sourceEntityID) {
				// update eligibility if rejected
				err = UpdateEligibility(userID, sourceEntityID, decision, firstResp, ruleVersion)
				if err != nil {
					logger.WithUser(userID).Errorln(err)
					return err
				}
			}
			userStatus = constants.UserStatusDisqualified
		} else {
			if journey.IsOfferFlowV2(userID, sourceEntityID) {
				loanType, _, _ := journey.GetLoanType(sourceEntityID)
				userStatus, err = createTempOffer(loanType, constants.IIFLID, userID, sourceEntityID, userStatus)
				if err != nil {
					logger.WithUser(userID).Errorln(err)
					return err
				}
			}
		}
	}

	if sourceEntityID == constants.KhatabookID && userStatus != constants.UserStatusDisqualified && (conf.ENV == conf.ENV_PROD || strings.HasPrefix(conf.ENV, conf.ENV_UAT)) {

		segment := partner.GetKBSegment(userID)
		var firstResp DecisionRunStruct
		var ruleVersion, underReviewVersion, lenderID, decision string
		var done bool

		// Loop until success or no lender found (max of 5 times)
		for try := 0; try < 5; try++ {
			underReviewVersion = ""
			userStatus = constants.UserStatusQualified

			lenderID, _ = GetLenderID(sourceEntityID, userID, segment)
			ruleVersionChain, isValid := journey.GetRuleChain(sourceEntityID, lenderID, segment)
			if len(ruleVersionChain) == 0 && isValid {
				return errors.New("no policies found for userID: " + userID)
			}
			decision = ""
			underReviewCount := 0
			for _, ruleVersionElem := range ruleVersionChain {
				ruleVersion = ruleVersionElem
				decision, done, rejectReason, firstResp = CallRuleEngine(userID, sourceEntityID, lenderID, ruleVersion)
				if !done {
					// do not update user status
					return errors.New("decision API failed for userID: " + userID)
				}
				if decision == constants.UnderwritingDecisionPass {
					if underReviewCount > 0 {
						// pop from underReviewReasons underReviewCount time
						underReviewReasons = underReviewReasons[:len(underReviewReasons)-underReviewCount]
					}
					break
				} else if decision == constants.UnderwritingDecisionCantDecide {
					underReviewVersion = ruleVersion
					underReviewCount++
					underReviewReasons = append(underReviewReasons, rejectReason)
				}
			}
			if underReviewVersion != "" && decision == constants.UnderwritingDecisionReject {
				ruleVersion = underReviewVersion
				// move back to previous version
				decision, done, rejectReason, firstResp = CallRuleEngine(userID, sourceEntityID, lenderID, ruleVersion)
				if !done {
					// do not update user status
					return errors.New("decision API failed for userID: " + userID)
				}
			}
			// if last decision is reject, reset the reasons as its going to be disqualified
			if decision == constants.UnderwritingDecisionReject {
				underReviewReasons = []string{}
				userStatus = constants.UserStatusDisqualified
			}
			if len(underReviewReasons) > 0 {
				// if reasons exists, set to under review
				userStatus = constants.UserStatusUnderReview
			}

			if general.InArr(decision, []string{constants.UnderwritingDecisionCantDecide, constants.UnderwritingDecisionPass}) {
				// don't cascade to next lender if passed or under review
				err = preselectedlender.Set(userID, lenderID)
				if err != nil {
					log.Println(err)
					return err
				}
				go func() {
					errorHandler.RecoveryNoResponse()
					err := journeyutils.RouteToLender(userID, lenderID, constants.LenderSelectionTypeCascading)
					if err != nil {
						logger.WithUser(userID).Errorln(err)
					}
				}()
				break
			}
		}
		if lenderID == "" {
			userStatus = constants.UserStatusDisqualified
		}
		// update eligibility
		err := UpdateEligibility(userID, sourceEntityID, decision, firstResp, ruleVersion)
		if err != nil {
			log.Println(err)
			return err
		}
	}

	if sourceEntityID == constants.BeldaraID && userStatus != constants.UserStatusDisqualified && conf.ENV == conf.ENV_PROD {
		var decision string
		var done bool
		var firstResp DecisionRunStruct
		decision, done, rejectReason, firstResp = CallRuleEngine(userID, sourceEntityID, constants.ArthanLenderID, "beldara_v1")
		if !done {
			// do not update user status
			return errors.New("decision API failed for userID: " + userID)
		}
		if decision == constants.UnderwritingDecisionPass {
			// update eligibility if pass
			err = UpdateEligibleAmount(userID, sourceEntityID, firstResp.ReferenceID, firstResp.Data.Amount)
			if err != nil {
				log.Println(err)
				return err
			}
		} else {
			err = UpdateEligibleAmount(userID, sourceEntityID, firstResp.ReferenceID, 5000)
			if err != nil {
				log.Println(err)
				return err
			}
		}
	}

	if sourceEntityID == constants.TataPLID && userStatus != constants.UserStatusDisqualified {

		var decision, lastRule string
		var firstResp DecisionRunStruct
		var done bool

		lenderID := preselectedlender.Get(userID)
		if lenderID == "" {
			logger.WithUser(userID).Warnln("lender not found in qualification")
			return errors.New("lender not found in qualification")
		}
		log.Infoln("getting lender for userID ", userID, "lender:", lenderID)

		if !general.InArr(conf.ENV, []string{conf.ENV_PROD, conf.ENV_DEV6}) && general.InArr(userObj.PAN, constants.TataCapitalMockPANList) {
			lenderID = constants.TataCapitalID
		}

		dateTimeNowString := general.GetTimeStampString()
		switch lenderID {
		case constants.TataCapitalID:
			errorHandler.ReportToSentryWithoutRequest(fmt.Errorf("unexpected lender assignment for user - %s", userID))
			newWorkflowName := constants.WorkflowTDLPLFresh
			if journey.IsTemporalFlow(userID, sourceEntityID, usermodulemapping.PersonalInfo) {
				newWorkflowName = constants.WorkflowTDLPLFreshPersonalInfo
			}
			err = workflowutils.UpdateWorkFlow(userID, sourceEntityID, newWorkflowName)
			if err != nil {
				logger.WithUser(userID).Errorln("error in updating workflow", err)
				errorHandler.ReportToSentryWithoutRequest(err)
				return err
			}
			userStatus = constants.UserStatusQualified
		case constants.IIFLID:
			if conf.ENV == conf.ENV_PROD {
				duplicityStatus, reason, prospectNo, _ := fraudcheckutils.DoPANDuplicityCheckIIFL(userID, sourceEntityID)
				if duplicityStatus {
					userStatus = constants.UserStatusDisqualified
					description := fmt.Sprintf("%s with ProspectID %s ", reason, prospectNo) // not adding redirect url here like other places to avoid showing redirect url to tata
					ok := usersutil.DisqualifyUser(userID, sourceEntityID, description, "")
					if !ok {
						err := fmt.Errorf("error in disqualifying user, userID: %s", userID)
						logger.WithUser(userID).Error(err)
						return err
					}
					return nil
				}
			}
			ruleChain := []string{"tdl_iifl_pl_compound_policy_v2"}
			for idx := 0; idx < len(ruleChain); idx++ {
				ruleVersion := ruleChain[idx]
				decision, done, rejectReason, firstResp = CallRuleEngine(userID, sourceEntityID, lenderID, ruleVersion)
				if !done {
					// do not update user status
					return errors.New("decision API failed for userID: " + userID)
				}
				lastRule = ruleVersion
				switch decision {
				case constants.UnderwritingDecisionReject:
					userStatus = constants.UserStatusDisqualified
				case constants.UnderwritingDecisionCantDecide:
					userStatus = constants.UserStatusUnderReview
				default:
					minTenure, _ := firstResp.Data.OutputVariables["minTenure"].(int)
					minAmount, _ := firstResp.Data.OutputVariables["minAmount"].(float64)
					if minAmount > firstResp.Data.Amount || minTenure > firstResp.Data.MaxTenure {
						err := fmt.Errorf("invalid offer values - referenceID - %s", firstResp.ReferenceID)
						logger.WithUser(userID).Error(err)
						errorHandler.ReportToSentryWithoutRequest(err)
						return err
					}
					offerType := offergetter.GetTataMockOfferType(userObj.PAN)
					if offerType == "" {
						bcFlag, _ := firstResp.Data.OutputVariables["bankconnect"].(bool)
						bFlag, _ := firstResp.Data.OutputVariables["booster"].(bool)
						if bcFlag && bFlag {
							errorHandler.ReportToSentryWithoutRequest(fmt.Errorf("unable to decide offer for %s", userID))
							return errors.New("unable to decide offer type")
						} else if bcFlag {
							offerType = constants.OfferTypeTentative
						} else if bFlag {
							offerType = constants.OfferTypeBooster
						} else {
							offerType = constants.OfferTypeFinal
						}
					}
					offerGeneratedEvent := constants.ActivityOfferGenerated
					offerGeneratedDescription := fmt.Sprintf("%.2f", firstResp.Data.Amount)
					_, err = personalloanoffer.Create(nil, userID, sourceEntityID, lenderID, firstResp.ReferenceID, firstResp.Data.Amount, minAmount, firstResp.Data.Interest, firstResp.Data.MaxTenure, minTenure, offerType, "rb", firstResp.Data.ProcessingFee, firstResp.Data.ProcessingFeeType, "", "", constants.OfferStatusActive)
					if err != nil {
						offerGeneratedEvent = constants.ActivityOfferGenerationFailed
						offerGeneratedDescription = ""
						logger.WithUser(userID).Error("Error in creating personal loan offer ", err)
						errorHandler.ReportToSentryWithoutRequest(err)
						return err
					}
					dateTimeNowString = general.GetTimeStampString()
					go func() {
						activityObj := activity.ActivityEvent{
							UserID:            userID,
							SourceEntityID:    sourceEntityID,
							LoanApplicationID: "",
							EntityType:        constants.EntityTypeSystem,
							EntityRef:         offerType,
							EventType:         offerGeneratedEvent,
							Description:       offerGeneratedDescription,
							ModuleName:        constants.ModuleBRE,
						}
						activity.RegisterEvent(&activityObj, dateTimeNowString)
					}()
				}
			}
		case "NA":
			underReviewReasons = append(underReviewReasons, "cannot assign lender")
			userStatus = constants.UserStatusUnderReview
			err = usersutil.SetUserWaitState(userID, true) // put user in wait state if lender cannot be assigned
			if err != nil {
				logger.WithUser(userID).Error(err)
				errorHandler.ReportToSentryWithoutRequest(err)
			}
		case "":
			rejectReason = "pincode unserviceable"
			entityRef = constants.PreRoutingStage
			userStatus = constants.UserStatusDisqualified
		}
		log.Debugln(lastRule)
	}

	// call HCIN check
	if journey.IsHCINFlow(sourceEntityID) && userStatus != constants.UserStatusDisqualified {
		pass, failReason, err := hcin.CheckEligibility(userID)
		if err != nil {
			return err
		}
		if !pass {
			userStatus = constants.UserStatusDisqualified
			rejectReason = failReason
		} else {
			userStatus = constants.UserStatusQualified
		}
	}

	if journey.IsMintifiAsLender(sourceEntityID) {
		ctx := context.Background()
		lenderErrHandle := func(resource string, err error) {
			dateTimeNowString := general.GetTimeStampString()
			activityObj := activity.ActivityEvent{
				UserID:         userID,
				SourceEntityID: sourceEntityID,
				EntityType:     constants.EntityTypeDashboardUser,
				EntityRef:      userObj.Email,
				EventType:      constants.ActivityLenderAPIFailed,
				Description:    fmt.Sprintf("Retrigger error in resource: %s", resource),
				ModuleName:     constants.ModuleBRE,
			}
			activity.RegisterAuxiliaryEvent(&activityObj, dateTimeNowString)
			errorHandler.ReportToSentryWithoutRequest(fmt.Errorf("resource: %s, err:%s", resource, err.Error()))
		}
		if userObj.Status == constants.UserStatusDisqualified {
			return errors.New("user is already disqualified")
		}

		appReq := lenderservice.ApplicationReq{
			UserID:         userID,
			SourceEntityID: sourceEntityID,
			LenderID:       constants.MintifiID,
		}
		waitStateFunc := func(userID string, err error) {
			waitErr := usersutil.SetUserWaitState(userID, true)
			if waitErr != nil {
				sentry.CaptureException(fmt.Errorf("error in setting wait state, userID: %s, error: %v", userID, waitErr))
				logger.WithUser(userID).Error(fmt.Sprintf("err: %s. waiterr: %s", err.Error(), waitErr.Error()))
			}
		}
		applicant, err := lenderservice.CreateApplicant(ctx, &appReq)
		if err != nil {
			err = fmt.Errorf("unable update create applicant %s", err)
			go lenderErrHandle(mintifi.UpdateOfferResource, err)
			logger.WithUser(userID).Error(err)
			waitStateFunc(userID, err)
			return err
		}
		if err := users.UpdateCrmID(ctx, userID, applicant.CrmID); err != nil {
			err := fmt.Errorf("unable to update the borrowerID err:%s", err.Error())
			logger.WithUser(userID).Error(err)
			waitStateFunc(userID, err)
			return err
		}
		appReq.CrmID = applicant.CrmID

		coapplicantResp, err := lenderservice.CreateCoApplicant(ctx, &lenderservice.CoApplicantReq{
			ApplicationReq: appReq,
		})
		if err != nil {
			err = fmt.Errorf("unable update create coapplicant %s", err)
			go lenderErrHandle(mintifi.UpdateOfferResource, err)
			logger.WithUser(userID).Error(err)
			waitStateFunc(userID, err)
			return err
		}

		co := coapplicant.Coapplicant{
			Name:           userObj.Name,
			Mobile:         userObj.Mobile,
			Email:          userObj.Email,
			SourceEntityID: uuid.MustParse(sourceEntityID),
			PAN:            userObj.PAN,
			UserID:         uuid.MustParse(userID),
			CrmID:          coapplicantResp.CoApplicantCrmID,
		}
		err = coapplicant.SetCoapplicant(ctx, co)
		if err != nil {
			err := fmt.Errorf("unable to set Coapplicant err:%s", err.Error())
			logger.WithUser(userID).Error(err)
			waitStateFunc(userID, err)
			return err
		}

		var offerResp *lenderservice.GetOfferResp
		// retry after 90 sec, specified by lender.
		err = retry.CustomRetry(3, time.Second*90, func() error {
			offerResp, err = lenderservice.GetOffers(ctx, &lenderservice.GetOfferReq{
				ApplicationReq: appReq,
			})
			if err != nil {
				err := fmt.Errorf("unable to get offers err:%s", err.Error())
				logger.WithUser(userID).Error(err)
				return err
			}
			return nil
		})
		if err != nil {
			logger.WithUser(userID).Error(err, "unable to update user_eligibility for mintifi after 3 retries")
			usersutil.SetUserWaitState(userID, true)
			return err
		}

		// disqualify if no offer found in production
		if offerResp.Offers == nil || len(offerResp.Offers) == 0 {
			if conf.ENV == conf.ENV_PROD {
				alreadyDisqualified := usersutil.DisqualifyUser(userID, sourceEntityID, "no offer generated for user", "")
				if !alreadyDisqualified {
					err := fmt.Errorf("error in disqualifying user, userID: %s", userID)
					sentry.CaptureException(err)
					return err
				}
			}
			return errors.New("no offer generated for user")
		}

		acceptedOfferResp, err := lenderservice.AcceptOffer(ctx, &lenderservice.AcceptOfferReq{
			ApplicationReq: appReq,
			OfferID:        offerResp.Offers[0].ID,
		})
		if err != nil {
			err = fmt.Errorf("unable update offer %s", err)
			go lenderErrHandle(mintifi.UpdateOfferResource, err)
			logger.WithUser(userID).Error(err)
			waitStateFunc(userID, err)
			return err
		}

		// store the offer in user_eligiblity to create an application
		// TODO: expired_at accordingly based on offervalidity attribute
		query, args, err := sqlx.In(`insert into user_eligibility(source_entity_id , user_id, created_at, created_by, is_eligible, eligible_amount, interest, tenure, processing_fee_type, processing_fee)
					values($1, $2, NOW(), $3, $4, $5, $6, $7, $8, $9)`,
			sourceEntityID, userID, "ADMIN", true, acceptedOfferResp.Amount, acceptedOfferResp.InterestRate, acceptedOfferResp.Tenure, "FLAT", acceptedOfferResp.ProcessingFee)
		if err != nil {
			err = fmt.Errorf("unable to update user_eligibility for mintifi. err: %s", err)
			logger.WithUser(userID).Error(err)
			waitStateFunc(userID, err)
			return err
		}
		query = database.Rebind(query)
		_, err = database.ExecContext(ctx, query, args...)
		if err != nil {
			err = fmt.Errorf("unable to update user_eligibility for mintifi. err: %s", err)
			logger.WithUser(userID).Error(err)
			waitStateFunc(userID, err)
			return err
		}
	}

	underReviewStr := ""
	for idx, reason := range underReviewReasons {
		underReviewStr += reason
		if idx != len(underReviewReasons)-1 {
			underReviewStr += ", "
		}
	}
	go func() {
		dateTimeNowString := general.GetTimeStampString()
		var activityObj activity.ActivityEvent
		switch userStatus {
		case constants.UserStatusDisqualified:
			activityObj = activity.ActivityEvent{
				UserID:            userID,
				SourceEntityID:    sourceEntityID,
				LoanApplicationID: "",
				EntityType:        constants.EntityTypeSystem,
				EntityRef:         entityRef,
				EventType:         constants.ActivityUserDisqualified,
				Description:       rejectReason,
				ModuleName:        constants.ModuleBRE,
			}
		case constants.UserStatusUnderReview:
			activityObj = activity.ActivityEvent{
				UserID:            userID,
				SourceEntityID:    sourceEntityID,
				LoanApplicationID: "",
				EntityType:        constants.EntityTypeSystem,
				EntityRef:         "",
				EventType:         constants.ActivityUserUnderReview,
				Description:       underReviewStr,
				ModuleName:        constants.ModuleBRE,
			}
		default:
			activityObj = activity.ActivityEvent{
				UserID:            userID,
				SourceEntityID:    sourceEntityID,
				LoanApplicationID: "",
				EntityType:        constants.EntityTypeSystem,
				EntityRef:         "",
				EventType:         constants.ActivityUserQualified,
				Description:       acitivityDescriptionUserQualified,
				ModuleName:        constants.ModuleBRE,
			}
		}
		activity.RegisterEvent(&activityObj, dateTimeNowString)
	}()

	if sourceEntityID == constants.KhatabookID {
		// if userStatus == constants.UserStatusQualified && conf.ENV != conf.ENV_PROD {
		// 	//insert into user_eligibility
		// 	query := `insert into user_eligibility
		// 	(source_entity_id , user_id, created_at, created_by, is_eligible, eligible_amount,
		// 		interest, processing_fee, processing_fee_type, tenure, max_emi)
		// 	values ($1, $2, NOW(), 'ADMIN', true, $3, $4, $5, $6, $7, $8)`
		// 	_, err := database.Exec(query, sourceEntityID, userID, 2_50_000, 24, 0, "PERC", 24, 25_000)
		// 	if err != nil {
		// 		log.Println(err)
		// 	}
		// }

		//send offer_generated webhook for new KB flow
		if !general.InArr(userStatus, []int{constants.UserStatusDisqualified, constants.UserStatusUnderReview}) {
			go func() {
				dateTimeNowString := general.GetTimeStampString()
				activityObj := activity.ActivityEvent{
					UserID:            userID,
					SourceEntityID:    sourceEntityID,
					LoanApplicationID: "",
					EntityType:        constants.EntityTypeSystem,
					EntityRef:         "",
					EventType:         constants.ActivityOfferGenerated,
					Description:       "",
					ModuleName:        constants.ModuleBRE,
				}
				activity.RegisterEvent(&activityObj, dateTimeNowString)
			}()
		}

	}

	query = "update users set status = $1, updated_at = now() where user_id = $2 and source_entity_id = $3"
	_, err = database.Exec(query, userStatus, userID, sourceEntityID)
	if err != nil {
		logger.WithUser(userID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
	}

	return nil
}

// TODO: extend usage to UpdateUserQualification as well, currently method can only be used in BoostQualification
// TODO: take rule chain to be executed in call scenario as input
func executeRuleChain(userID, sourceEntityID, lenderID string, ruleVersionChain []string) (string, string, DecisionRunStruct, error) {
	var decision string
	var firstResp DecisionRunStruct
	var rejectReason string
	var done bool
	var err error

	for idx := 0; idx < len(ruleVersionChain); idx++ {
		call, ok := firstResp.Data.OutputVariables["call"].(bool)
		if idx > 0 && ok && !call && general.InArr(ruleVersionChain[idx-1], []string{"vyapar_v1", "iifl_bl_bank_mca_cap_v3.4_b", "iifl_supreme_solar_self_employed_v1_b"}) {
			continue
		}
		decision, done, rejectReason, firstResp = CallRuleEngine(userID, sourceEntityID, lenderID, ruleVersionChain[idx])
		if !done {
			// do not update user status
			return decision, rejectReason, firstResp, errors.New("decision API failed for userID: " + userID)
		}
		if decision != constants.UnderwritingDecisionReject {
			break
		}
	}

	return decision, rejectReason, firstResp, err
}

// BoostQualification is called after any boost module or after onboarding flows, when user was already qualified
func BoostQualification(userID string, sourceEntityID string, sourceModule string, ignoreCustom bool) error {

	//new sentinel evaluation flow with percentage rollout
	if journey.IsMultiBankApplicable(userID, sourceEntityID) || journey.IsBankingSentinelEvalFlow(userID, sourceEntityID) {
		return BoostQualificationV2(userID, sourceEntityID, sourceModule, ignoreCustom)
	}

	// do multi offer  boost qualification if IsMultiOfferAllowed
	if journey.IsMultiOfferAllowed(sourceEntityID, userID) {
		err := BoostMultiOfferQualification(sourceEntityID, userID, ignoreCustom)
		if err != nil {
			logger.WithUser(userID).Errorln(err)
			return err
		}
		return nil
	}

	// revert change - add muthoot back
	if conf.ENV != conf.ENV_PROD && !general.InArr(sourceEntityID, []string{constants.TataPLID, constants.OneMuthootID, constants.TataNexarcID, constants.IIFLBLID, constants.VyaparID, constants.HousingID, constants.SwiggyID, constants.GPayIIFLBLID, constants.IIFLID}) && !journey.IsMuthootCLPartner(sourceEntityID) { // Add source entity id here to enable boost qualification in non-prod env
		log.Debugln("skipping boost qualification rule engine run")
		err := usersutil.SetUserWaitState(userID, false)
		if err != nil {
			logger.WithUser(userID).Error(err)
		}
		return err
	}

	lenderID, _ := GetLenderID(sourceEntityID, userID, "")
	if lenderID == "" {
		return errors.New("lenderID not found for userID: " + userID)
	}

	ruleVersionChain := []string{}
	auxRuleVersionChain := []string{}

	switch sourceModule {
	case constants.BankConnect:
		if journey.IsHousingLoanTap(userID, sourceEntityID) {
			ruleVersionChain = []string{constants.LoanTapSalariedPolicy}
		} else if journey.IsNexarcKotak(userID, sourceEntityID) {
			ruleVersionChain = constants.NexarcKotakBankPolicies
		} else if journey.IsNexarcTataCapital(userID, sourceEntityID) {
			ruleVersionChain = constants.NexarcTCapBankPolicies
		} else if journey.IsNexarcLendingKart(userID, sourceEntityID) {
			ruleVersionChain = []string{constants.LendingKartPolicy}
		} else if journey.IsVyaparLendingKart(userID, sourceEntityID) {
			ruleVersionChain = []string{constants.LendingKartVyaparPolicy}
		} else if journey.IIFLBLPolicy(userID, sourceEntityID) || sourceEntityID == constants.TataNexarcID || sourceEntityID == constants.VyaparID {
			isGST, err := users.GetDynamicUserInfoField(userID, "isGST")
			if err != nil {
				logger.WithUser(userID).Error(err)
				return err
			}
			if isGST == "Y" {
				ruleVersionChain = []string{"iifl_bl_gst_bank_v1.1", "iifl_bl_gst_bank_v1.1_a", "iifl_bl_gst_bank_v1.1_b"}
			} else {
				ruleVersionChain = []string{"iifl_bl_bank_mca_cap_v3.1", "iifl_bl_bank_mca_cap_v3.4_a", "iifl_bl_bank_mca_cap_v3.4_b", "iifl_bl_bank_mca_cap_v3.4_c", "iifl_bl_bank_self_employed_stbl_v3.4", "iifl_bl_bank_creditcard_surrogate_v3.4"}
				auxRuleVersionChain = []string{constants.IIFLBLCurrentAccountPolicy}
			}

		} else if journey.IIFLPLPolicy(sourceEntityID) || sourceEntityID == constants.HousingID { // if lender is not LoanTap for Housing then lender is IIFL - call IIFL PL policies
			ruleVersionChain = []string{"iifl_salaried_bank_v1.4.3", "iifl_self_employed_v2.4.4", "iifl_self_employed_v2.4.4_a", "iifl_self_employed_v2.4.4_b", "iifl_bank_pl_creditcard_surrogate_v1.2", "iifl_pl_bank_hl_lap_al_v1"}
			partnerCode, _ := journey.GetPartnerCode(userID)
			ok := unblockpolicy.IsUnblocked(sourceEntityID, constants.UnblockSelfEmployedPolicies)
			if !ok && partnerCode != "" && !ignoreCustom {
				// !ok -> remove self employed if its not unblocked (!ok)
				ruleVersionChain = []string{"iifl_salaried_bank_v1.4.3", "iifl_bank_pl_creditcard_surrogate_v1.2", "iifl_pl_bank_hl_lap_al_v1"}
			}
			ok = unblockpolicy.IsUnblocked(sourceEntityID, constants.UnblockSolarPolicies)
			if ok && partnerCode != "" && !ignoreCustom {
				// ok -> if supreme is unblocked, use only that
				ruleVersionChain = []string{"iifl_supreme_solar_salaried_v1", "iifl_supreme_solar_self_employed_v1", "iifl_supreme_solar_self_employed_v1_a", "iifl_supreme_solar_self_employed_v1_b", "iifl_supreme_solar_self_employed_v1_c", "iifl_bank_pl_creditcard_surrogate_v1.2", "iifl_pl_bank_hl_lap_al_v1"}
			}
		} else if sourceEntityID == constants.TataPLID {
			personalOffer := personalloanoffer.GetSelectedOffer(userID)
			switch personalOffer.OfferType {
			case constants.OfferTypeBooster:
				ruleVersionChain = []string{"iifl_tdl_salaried_bank_boost_v1.3", "iifl_tdl_self_employed_boost_v2.2", "iifl_tdl_self_employed_boost_v2.2_a", "iifl_tdl_self_employed_boost_v2.2_b", "iifl_bank_pl_creditcard_surrogate_v1.2", "iifl_pl_bank_hl_lap_al_v1"}
			default:
				ruleVersionChain = []string{"iifl_salaried_bank_v1.4.3", "iifl_self_employed_v2.4.4", "iifl_self_employed_v2.4.4_a", "iifl_self_employed_v2.4.4_b", "iifl_bank_pl_creditcard_surrogate_v1.2", "iifl_pl_bank_hl_lap_al_v1"}
			}
		} else if journey.IsOneMuthootPartner(sourceEntityID) {
			ruleVersionChain = []string{"one_muthoot_pl_bank_v2"}
		}

	case "gst":
		ruleVersionChain = []string{}
	}

	if len(ruleVersionChain) > 0 {
		var (
			decision        string
			auxDecision     string
			rejectReason    string
			auxRejectReason string
			ruleVersion     string
			firstResp       DecisionRunStruct
			auxFirstResp    DecisionRunStruct
			done            bool
			err             error
			auxErr          error
		)

		decision, rejectReason, firstResp, err = executeRuleChain(userID, sourceEntityID, lenderID, ruleVersionChain)

		if err != nil {
			logger.WithUser(userID).Errorln(err)
			return err
		}
		// TODO: run aux rule chain in parallel
		if len(auxRuleVersionChain) > 0 {
			auxDecision, auxRejectReason, auxFirstResp, auxErr = executeRuleChain(userID, sourceEntityID, lenderID, auxRuleVersionChain)

		}
		if auxErr != nil {
			logger.WithUser(userID).Errorln(auxErr)
			return auxErr
		}
		// take values from aux policy run if certain conditions are satisfied
		// TODO: improve the if-else branching
		if sourceModule == constants.BankConnect && len(auxRuleVersionChain) > 0 {
			if decision == constants.UnderwritingDecisionPass && auxDecision == constants.UnderwritingDecisionPass {
				if auxFirstResp.Data.Amount >= firstResp.Data.Amount {
					decision = auxDecision
					rejectReason = auxRejectReason
					firstResp = auxFirstResp
				}
			} else if decision != constants.UnderwritingDecisionPass && auxDecision == constants.UnderwritingDecisionPass {
				decision = auxDecision
				rejectReason = auxRejectReason
				firstResp = auxFirstResp

			} else if decision != constants.UnderwritingDecisionPass && auxDecision != constants.UnderwritingDecisionPass {
				decision = auxDecision
				rejectReason = auxRejectReason
				firstResp = auxFirstResp
			}
		}

		// TODO: This If block will be removed once we move to the Sentinel Workflows for IIFL
		if journey.IsOfferFlowV2(userID, sourceEntityID) && (journey.IIFLPLPolicy(sourceEntityID) || journey.IIFLBLPolicy(userID, sourceEntityID)) && firstResp.Data.Amount <= constants.IIFLMCAPolicyAmount {
			minTenure, _ := firstResp.Data.OutputVariables["minTenure"].(int)
			minAmount, _ := firstResp.Data.OutputVariables["minAmount"].(float64)
			if minAmount > firstResp.Data.Amount || minTenure > firstResp.Data.MaxTenure {
				err := fmt.Errorf("invalid offer values - referenceID - %s", firstResp.ReferenceID)
				logger.WithUser(userID).Error(err)
				errorHandler.ReportToSentryWithoutRequest(err)
				return err
			}
			programName, found := sourceentity.GetProgramByID(sourceEntityID)
			if !found {
				err := fmt.Errorf("error in getting program name for source entity ID:%s", sourceEntityID)
				logger.WithUser(userID).Error(err)
				errorHandler.ReportToSentryWithoutRequest(err)
				return err
			}
			dateTimeNowString := general.GetTimeStampString()
			switch programName {
			case constants.ProgramPersonalLoan:
				var loanOfferID string
				loanOfferID, err = personalloanoffer.Create(nil, userID, sourceEntityID, lenderID, firstResp.ReferenceID, firstResp.Data.Amount, minAmount, firstResp.Data.Interest, firstResp.Data.MaxTenure, minTenure, constants.OfferTypeOverridden, "rb", firstResp.Data.ProcessingFee, firstResp.Data.ProcessingFeeType, "", "", constants.OfferStatusActive)
				if err != nil {
					err = personalloanoffer.UpdateMaxEMI(nil, loanOfferID, firstResp.Data.MaxEMI)
				}
			case constants.ProgramBusinessLoan:
				_, err = businessloanoffer.Create(nil, userID, sourceEntityID, lenderID, firstResp.ReferenceID, firstResp.Data.Amount, minAmount, firstResp.Data.Interest, firstResp.Data.MaxTenure, minTenure, constants.OfferTypeOverridden, "rb", firstResp.Data.ProcessingFee, firstResp.Data.ProcessingFeeType, firstResp.Data.MaxEMI, "")
			default:
				err = fmt.Errorf("error in getting program name for user ID:%s", userID)
			}
			if err != nil {
				logger.WithUser(userID).Error(err)
				errorHandler.ReportToSentryWithoutRequest(err)
				go func() {
					activityObj := activity.ActivityEvent{
						UserID:            userID,
						SourceEntityID:    sourceEntityID,
						LoanApplicationID: "",
						EntityType:        constants.EntityTypeSystem,
						EntityRef:         "",
						EventType:         constants.ActivityOfferGenerationFailed,
						Description:       "",
					}
					activity.RegisterEvent(&activityObj, dateTimeNowString)
				}()
				return err
			}
			go func() {
				activityObj := activity.ActivityEvent{
					UserID:            userID,
					SourceEntityID:    sourceEntityID,
					LoanApplicationID: "",
					EntityType:        constants.EntityTypeSystem,
					EntityRef:         "",
					EventType:         constants.ActivityOfferGenerated,
					Description:       fmt.Sprintf("%.2f", firstResp.Data.Amount),
				}
				activity.RegisterEvent(&activityObj, dateTimeNowString)
			}()
		} else {
			// update eligibility
			err = UpdateEligibility(userID, sourceEntityID, decision, firstResp, ruleVersion)
			if err != nil {
				log.Println(err)
				return err
			}
		}

		if sourceModule == constants.BankConnect && decision != constants.UnderwritingDecisionReject && (journey.IIFLBLPolicy(userID, sourceEntityID) ||
			journey.IIFLPLPolicy(sourceEntityID)) {
			mca := firstResp.Data.Amount
			if mca <= constants.IIFLMCAPolicyAmount {

				logger.WithUser(userID).Info("MCA 2 lakh policy got executed for IIFL")
				decision, done, rejectReason, firstResp = CallRuleEngine(userID, sourceEntityID, lenderID, constants.IIFLMca2LakhPolicy)
				if !done {
					// do not update user status
					return errors.New("decision API failed for userID: " + userID)
				}
			}
		}

		callAutomationPolicy := unblockpolicy.IsUnblocked(sourceEntityID, constants.UnblockAutomationPolicies)
		if decision != constants.UnderwritingDecisionReject && callAutomationPolicy {
			ruleVersion = constants.AutomationPolicy
			decision, done, rejectReason, firstResp = CallRuleEngine(userID, sourceEntityID, lenderID, ruleVersion)
			if !done {
				// do not update user status
				return errors.New("decision API failed for userID: " + userID)
			}
			// update eligibility
			err = UpdateEligibility(userID, sourceEntityID, decision, firstResp, ruleVersion)
			if err != nil {
				log.Println(err)
				return err
			}
			rejectReason = "checklist failed: " + rejectReason
		}
		switch decision {
		case constants.UnderwritingDecisionReject:
			//Here for IIFL in place of disqualifying we have to move user to bankconnect screen.
			var sendToBankConnect bool
			val, ok := firstResp.Data.OutputVariables["bank_connect"]
			if ok {
				boolVal, ok := val.(bool)
				if ok {
					sendToBankConnect = boolVal
				}
			}
			if journey.ToShowBankConnectPostGSTDisqualification(sourceEntityID, sendToBankConnect, sourceModule) {
				//update user_module_mapping
				err = usermodulemapping.Create(nil, userID, userID, usermodulemapping.BankConnect, constants.UserModuleStatusPending, "")
				if err != nil {
					logger.WithUser(userID).Error("Error updating module to Bankconnect:", err)
					return err
				}
				dateTimeNowString := general.GetTimeStampString()
				go func() {
					activityObj := activity.ActivityEvent{
						UserID:            userID,
						SourceEntityID:    sourceEntityID,
						LoanApplicationID: "",
						EntityType:        constants.EntityTypeSystem,
						EntityRef:         "",
						EventType:         constants.ActivityGSTNotEligible,
						Description:       rejectReason,
					}
					activity.RegisterEvent(&activityObj, dateTimeNowString)
				}()
			} else {
				if journey.IsProgramApplicable(sourceEntityID) {
					if ok := usersutil.DisqualifyUser(userID, sourceEntityID, rejectReason, ""); !ok {
						err := fmt.Errorf("error disqualifying user - %s", userID)
						logger.WithUser(userID).Errorln(err)
						return err
					}
				} else {
					ok := usersutil.DisqualifyUser(userID, sourceEntityID, rejectReason, "")
					if !ok {
						err := fmt.Errorf("error in disqualifying user, userID: %s", userID)
						logger.WithUser(userID).Errorln(err)
						return err
					}
				}
			}
		case constants.UnderwritingDecisionCantDecide:
			log.Println("can't take decision")
			return errors.New("can't take decision")
		case constants.UnderwritingDecisionPass:
			dateTimeNowString := general.GetTimeStampString()
			go func() {
				activityObj := activity.ActivityEvent{
					UserID:            userID,
					SourceEntityID:    sourceEntityID,
					LoanApplicationID: "",
					EntityType:        constants.EntityTypeSystem,
					EntityRef:         "",
					EventType:         constants.ActivityUserQualified,
					Description:       sourceModule,
				}
				activity.RegisterEvent(&activityObj, dateTimeNowString)
			}()
			if journey.IsProgramApplicable(sourceEntityID) || journey.IsOfferFlowV2(userID, sourceEntityID) {
				programName, programEnabled := sourceentity.GetProgramByID(sourceEntityID)
				if !programEnabled {
					err = fmt.Errorf("error fetching program - sourceEntityID - %s", sourceEntityID)
					errorHandler.ReportToSentryWithoutRequest(err)
					return err
				}
				minTenure, _ := firstResp.Data.OutputVariables["minTenure"].(int)
				minAmount, _ := firstResp.Data.OutputVariables["minAmount"].(float64)
				if minAmount > firstResp.Data.Amount || minTenure > firstResp.Data.MaxTenure {
					err := fmt.Errorf("invalid offer values - referenceID - %s", firstResp.ReferenceID)
					logger.WithUser(userID).Error(err)
					errorHandler.ReportToSentryWithoutRequest(err)
					return err
				}
				dateTimeNowString := general.GetTimeStampString()
				minAmount = journey.GetMinAmount(sourceEntityID, lenderID, strings.ToLower(programName), "", minAmount)
				minTenure = int(math.Max(float64(journey.GetMinTenure(sourceEntityID, lenderID, strings.ToLower(programName), "")), float64(minTenure)))
				switch programName {
				case constants.ProgramPersonalLoan:
					var loanOfferID string
					loanOfferID, err = personalloanoffer.Create(nil, userID, sourceEntityID, lenderID, firstResp.ReferenceID, firstResp.Data.Amount, minAmount, firstResp.Data.Interest, firstResp.Data.MaxTenure, minTenure, constants.OfferTypeFinal, "rb", firstResp.Data.ProcessingFee, firstResp.Data.ProcessingFeeType, "", "", constants.OfferStatusActive)
					if err != nil && journey.IIFLPLPolicy(sourceEntityID) {
						err = personalloanoffer.UpdateMaxEMI(nil, loanOfferID, firstResp.Data.MaxEMI)
					}
				case constants.ProgramBusinessLoan:
					_, err = businessloanoffer.Create(nil, userID, sourceEntityID, lenderID, firstResp.ReferenceID, firstResp.Data.Amount, minAmount, firstResp.Data.Interest, firstResp.Data.MaxTenure, minTenure, constants.OfferTypeFinal, "rb", firstResp.Data.ProcessingFee, firstResp.Data.ProcessingFeeType, firstResp.Data.MaxEMI, "")
				default:
					logger.WithUser(userID).Error("Error in fetching program", err)
					errorHandler.ReportToSentryWithoutRequest(err)
					return err
				}
				if err != nil {
					logger.WithUser(userID).Errorf("Error in creating %s loan offer: %s", programName, err.Error())
					errorHandler.ReportToSentryWithoutRequest(err)
					go func() {
						activityObj := activity.ActivityEvent{
							UserID:            userID,
							SourceEntityID:    sourceEntityID,
							LoanApplicationID: "",
							EntityType:        constants.EntityTypeSystem,
							EntityRef:         constants.OfferTypeFinal,
							EventType:         constants.ActivityOfferGenerationFailed,
							Description:       "",
						}
						activity.RegisterEvent(&activityObj, dateTimeNowString)
					}()
					return err
				}
				go func() {
					activityObj := activity.ActivityEvent{
						UserID:            userID,
						SourceEntityID:    sourceEntityID,
						LoanApplicationID: "",
						EntityType:        constants.EntityTypeSystem,
						EntityRef:         constants.OfferTypeFinal,
						EventType:         constants.ActivityOfferGenerated,
						Description:       fmt.Sprintf("%.2f", firstResp.Data.Amount),
					}
					activity.RegisterEvent(&activityObj, dateTimeNowString)
				}()

			}
			qualifyIfStatusIn := []int{constants.UserStatusDisqualified}
			if journey.IsOneMuthootPartner(sourceEntityID) {
				qualifyIfStatusIn = append(qualifyIfStatusIn, constants.UserStatusProfileUpdated)
			}
			query := `UPDATE users SET updated_at = NOW(), status = ?
					WHERE user_id = ? and source_entity_id = ? and status in (?)`
			query, args, err := sqlx.In(query, constants.UserStatusQualified, userID, sourceEntityID, qualifyIfStatusIn)
			if err != nil {
				logger.WithUser(userID).Errorln(err)
				errorHandler.ReportToSentryWithoutRequest(err)
			}
			query = database.Rebind(query)
			_, err = database.Exec(query, args...)
			if err != nil {
				logger.WithUser(userID).Errorln(err)
				errorHandler.ReportToSentryWithoutRequest(err)
			}
		}
	} else {
		errorHandler.ReportToSentryWithoutRequest(fmt.Errorf(" policy does not exist for source : %s, sourceEntity: %s", sourceModule, sourceEntityID))
	}

	if sourceEntityID == constants.ArzoooID {

		type dbStruct struct {
			LoanID   string
			LenderID string
			Amount   float64
		}
		var dbObj dbStruct
		query := `SELECT loan_application_id as loanid, lender_id as lenderid, amount from loan_application where user_id = $1 AND is_valid = TRUE and status != $2 order by created_at desc limit 1`
		err := database.Get(&dbObj, query, userID, constants.LoanStatusClosed)
		if err != nil {
			log.Println(err)
		}

		if dbObj.LenderID == "" {
			dbObj.LenderID = constants.ArthanLenderID
		}

		decision, done, _, firstResp := CallRuleEngine(userID, sourceEntityID, dbObj.LenderID, "arzooo_boost_bank_v1")
		if !done {
			// do not update user wait status
			return errors.New("decision API failed for userID: " + userID)
		}

		if dbObj.LoanID == "" {
			// not booster case
			if decision == constants.UnderwritingDecisionPass {
				var maxCap float64 = 5_00_000
				if partner.IsNewToArzooo(userID) {
					maxCap = 1_00_000
				}
				if firstResp.Data.Amount > maxCap {
					firstResp.Data.Amount = maxCap
				}
				err = UpdateEligibleAmount(userID, sourceEntityID, firstResp.ReferenceID, firstResp.Data.Amount)
				if err != nil {
					log.Println(err)
					return err
				}
			} else {
				err = UpdateEligibleAmount(userID, sourceEntityID, firstResp.ReferenceID, 25_000)
				if err != nil {
					log.Println(err)
					return err
				}
			}
		}
	}

	if sourceEntityID == constants.CashifyID {

		type dbStruct struct {
			LoanID   string
			LenderID string
			Amount   float64
		}
		var dbObj dbStruct
		query := `SELECT loan_application_id as loanid, lender_id as lenderid, amount from loan_application where user_id = $1 order by created_at desc limit 1`
		err := database.Get(&dbObj, query, userID)
		if err != nil {
			log.Println(err)
		}
		if dbObj.LenderID == "" {
			dbObj.LenderID = constants.ArthanLenderID
		}
		decision, done, rejectReason, firstResp := CallRuleEngine(userID, sourceEntityID, dbObj.LenderID, "cashify_v1")
		if !done {
			// do not update user wait status
			return errors.New("decision API failed for userID: " + userID)
		}
		if dbObj.LoanID == "" {
			// not booster case
			if decision == constants.UnderwritingDecisionPass {
				err = UpdateEligibleAmount(userID, sourceEntityID, firstResp.ReferenceID, firstResp.Data.Amount)
				if err != nil {
					log.Println(err)
					return err
				}
			} else {
				// disqualify customer
				query := `update users set updated_at = current_timestamp, status = $1 where user_id = $2`
				_, err := database.Exec(query, constants.UserStatusDisqualified, userID)
				if err != nil {
					log.Println(err)
					return err
				}
				dateTimeNowString := general.GetTimeStampString()
				go func() {
					activityObj := activity.ActivityEvent{
						UserID:            userID,
						SourceEntityID:    sourceEntityID,
						LoanApplicationID: "",
						EntityType:        constants.EntityTypeSystem,
						EntityRef:         "",
						EventType:         constants.ActivityUserDisqualified,
						Description:       rejectReason,
					}
					activity.RegisterEvent(&activityObj, dateTimeNowString)
				}()
			}
		}
	}

	if journey.IsSaraloanAsLender(sourceEntityID) {
		businessID, status, rejectReason, err := saraloan.CallBureauAPIs(userID, sourceEntityID)
		if err != nil {
			log.Println(err)
			return err
		}

		// process based on status returned
		disqualifyUser := false

		switch status {
		case saraloan.LOS:
			creditLineAmount, isRejected, errStr := saraloan.GetCreditLineAmountAndUpdate(businessID, userID)
			if isRejected {
				disqualifyUser = true
			} else {
				if errStr != "" {
					activity.RegisterAuxiliaryEventArgs(userID, sourceEntityID, "", constants.EntityTypeSystem, "", saraloan.CreditLineAmountExternalService, err.Error(), nil)
					return errors.New(errStr)
				} else {
					err = UpdateEligibleAmount(userID, sourceEntityID, "", creditLineAmount)
					if err != nil {
						log.Println(err)
						return err
					}
				}
			}
		case saraloan.Pending:
			underReviewStr := "added in under review for saraloan pending cases"
			dateTimeNowString := general.GetTimeStampString()
			go func() {
				activityObj := activity.ActivityEvent{
					UserID:            userID,
					SourceEntityID:    sourceEntityID,
					LoanApplicationID: "",
					EntityType:        constants.EntityTypeSystem,
					EntityRef:         "",
					EventType:         constants.ActivityUserUnderReview,
					Description:       underReviewStr,
				}
				activity.RegisterEvent(&activityObj, dateTimeNowString)
			}()
			query := "update users set status = $1, updated_at = now() where user_id = $2 and source_entity_id = $3"
			_, err = database.Exec(query, constants.UserStatusUnderReview, userID, sourceEntityID)
			if err != nil {
				log.Println(err)
				return err
			}
		case saraloan.LeadRejected:
			disqualifyUser = true
			// rejectReason = "lender API returned rejected status"
		default:
			return errors.New("invalid status returned from lender APIs")
		}
		if disqualifyUser {
			query := `update users set updated_at = current_timestamp, status = $1 where user_id = $2`
			_, err = database.Exec(query, constants.UserStatusDisqualified, userID)
			if err != nil {
				log.Println(err)
				return err
			}
			dateTimeNowString := general.GetTimeStampString()
			go func() {
				activityObj := activity.ActivityEvent{
					UserID:            userID,
					SourceEntityID:    sourceEntityID,
					LoanApplicationID: "",
					EntityType:        constants.EntityTypeSystem,
					EntityRef:         "",
					EventType:         constants.ActivityUserDisqualified,
					Description:       rejectReason,
				}
				activity.RegisterEvent(&activityObj, dateTimeNowString)
			}()
		}
	}

	err := usersutil.SetUserWaitState(userID, false)
	if err != nil {
		logger.WithUser(userID).Error(err)
	}
	return err
}

// UpdateEligibility updates the eligibility of user based on decision engine
func UpdateEligibility(userID string, sourceEntityID string, decision string, firstResp DecisionRunStruct, ruleVersion string) error {
	if journey.IsOfferFlowV2(userID, sourceEntityID) {
		return nil
	}
	isEligible := decision != constants.UnderwritingDecisionReject
	if !isEligible {
		// set default values before insertion
		firstResp.Data.Interest = -1
		firstResp.Data.ProcessingFee = -1
	}
	if (journey.IIFLBLPolicy(userID, sourceEntityID) || journey.IsNexarcIIFL(userID, sourceEntityID)) && firstResp.Data.Amount > 15_00_000 { // > 15 lakhs
		var userPAN string
		query := `SELECT pan from users where user_id = $1`
		err := database.Get(&userPAN, query, userID)
		if err != nil {
			log.Println(err)
		}
		// check for gst
		gstins := []string{}
		query = `SELECT gstin from user_business_gst where user_id = $1`
		err = database.Select(&gstins, query, userID)
		if err != nil {
			log.Println(err)
		}
		putCap := true
		for _, gstin := range gstins {
			extractedPAN := gstin[2:12]
			if extractedPAN == userPAN {
				putCap = false
				break
			}
		}
		if putCap && firstResp.Data.Amount > 10_00_000 {
			// put cap of 10 lakhs
			firstResp.Data.Amount = 10_00_000
		}
	}

	refIDNullable := sql.NullString{String: "", Valid: false}
	if firstResp.ReferenceID != "" {
		refIDNullable = sql.NullString{String: firstResp.ReferenceID, Valid: true}
	}

	var outputVariables string
	outputBytes, err := json.Marshal(firstResp.Data.OutputVariables)
	if err != nil {
		log.Println(err)
	} else {
		outputVariables = string(outputBytes)
	}

	// Check for renewal user
	if renewal := journey.RenewalFlow(sourceEntityID); renewal {
		// Set eligibility amount as max of current amount and last loan amount
		var lastLoanAmount float64
		// TODO: optimize this query.
		query := `SELECT amount from loan_application where user_id = $1 and status not in ($2, $3) order by created_at desc limit 1`
		err := database.Get(&lastLoanAmount, query, userID, constants.LoanStatusCancelled, constants.LoanStatusLoanRejected)
		if err != nil {
			log.Println(err)
		}
		firstResp.Data.Amount = math.Max(firstResp.Data.Amount, lastLoanAmount)
	}
	// update eligibility
	queryUm := `insert into user_eligibility
			(source_entity_id , user_id, created_at, created_by, is_eligible, eligible_amount,
				interest, processing_fee, processing_fee_type, tenure, risk_bucket, max_emi, output_variables, reference_id)
			values ($1, $2, NOW(), 'ADMIN', $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)`
	_, err = database.Exec(queryUm, sourceEntityID, userID, isEligible, firstResp.Data.Amount,
		firstResp.Data.Interest, firstResp.Data.ProcessingFee, firstResp.Data.ProcessingFeeType,
		firstResp.Data.MaxTenure, firstResp.Data.RiskBucket, firstResp.Data.MaxEMI, outputVariables, refIDNullable)
	if err != nil {
		log.Println(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		_ = usersutil.SetUserWaitState(userID, true)
		return err
	}
	dateTimeNowString := general.GetTimeStampString()
	go func() {
		activityObj := activity.ActivityEvent{
			UserID:            userID,
			SourceEntityID:    sourceEntityID,
			LoanApplicationID: "",
			EntityType:        constants.EntityTypeSystem,
			EntityRef:         "",
			EventType:         constants.ActivityEligibilityCalculated,
			Description:       fmt.Sprintf("%.2f", firstResp.Data.Amount),
		}
		activity.RegisterEvent(&activityObj, dateTimeNowString)
	}()
	return nil
}

// UpdateEligibleAmount updates the eligibility amount of user
func UpdateEligibleAmount(userID string, sourceEntityID string, referenceID string, amount float64) error {
	isEligible := amount > 0
	refIDNullable := sql.NullString{String: "", Valid: false}
	if referenceID != "" {
		refIDNullable = sql.NullString{String: referenceID, Valid: true}
	}
	// update eligibility
	queryUm := `insert into user_eligibility
			(source_entity_id , user_id, created_at, created_by, is_eligible, eligible_amount, reference_id)
			values ($1, $2, NOW(), 'ADMIN', $3, $4, $5)`
	_, err := database.Exec(queryUm, sourceEntityID, userID, isEligible, amount, refIDNullable)
	if err != nil {
		log.Println(err)
		_ = usersutil.SetUserWaitState(userID, true)
		return err
	}
	dateTimeNowString := general.GetTimeStampString()
	go func() {
		activityObj := activity.ActivityEvent{
			UserID:            userID,
			SourceEntityID:    sourceEntityID,
			LoanApplicationID: "",
			EntityType:        constants.EntityTypeSystem,
			EntityRef:         "",
			EventType:         constants.ActivityEligibilityCalculated,
			Description:       fmt.Sprintf("%.2f", amount),
		}
		activity.RegisterEvent(&activityObj, dateTimeNowString)
	}()
	return nil
}

func CheckIfEmailIsPermitted(userID string, sourceEntityID string, lenderID string, emailID string) (isPermitted bool, err error) {
	checkMode, err := emailconfig.GetCheckMode(sourceEntityID, lenderID)
	if err == sql.ErrNoRows {
		isPermitted = true
		return isPermitted, nil
	} else if err != nil {
		logger.WithUser(userID).Errorln(err)
		return false, err
	}

	if !general.ValidateEmail(emailID) {
		return false, errors.New("invalid email")
	}

	var emailDomain string
	emailComponents := strings.Split(emailID, "@")
	if len(emailComponents) >= 2 {
		emailDomain = emailComponents[1]
	}
	exists, err := emailconfig.EmailExists(userID, sourceEntityID, lenderID, emailDomain, checkMode)
	if err != nil {
		logger.WithUser(userID).Errorln(err)
		return false, err
	}

	switch checkMode {
	case constants.PositiveEmailConfigCheck:
		if exists {
			isPermitted = true
		}
	case constants.NegativeEmailConfigCheck:
		if !exists {
			isPermitted = true
		}
	}

	return
}

// CheckPincodeBlocked returns true, if pincode is rejected
func CheckPincodeBlocked(sourceEntityID, lenderID, pincode string) bool {
	if pincode == "" {
		return false
	}

	checkForPincode, serviceMode, listID, _ := pincodeapi.GetPincodeServiceabilityMode(sourceEntityID, lenderID)
	if !checkForPincode {
		return false
	}

	// check for serviceable pincodes
	var count int
	switch serviceMode {
	case constants.PositivePincodeCheck:
		query := "select count(*) from serviceable_pincodes where list_id = $1 and pincode = $2"
		err := database.Get(&count, query, listID, pincode)
		if err != nil {
			log.Println(err)
			return false
		}
		return count == 0 // count should non 0 in case of serviceable and we return if pincode is blocked
	case constants.NegativePincodeCheck:
		// check for non-serviceable pincodes
		query := "select count(*) from non_serviceable_pincodes where list_id = $1 and pincode = $2"
		err := database.Get(&count, query, listID, pincode)
		if err != nil {
			log.Println(err)
			return false
		}
		return count != 0 // count should be 0 in cases of non serviceable
	}
	return false
}

// CheckPincodeBlockedforLenders returns blocked if the provided pincode is unserviceable for all provided lenders
func CheckPincodeBlockedforLenders(pincode, sourceEntityID string, lenderIDs []string, checkMode int) bool {

	if pincode == "" {
		return false
	}

	checkPincodeServiceability, listIDs := pincodeapi.GetPincodeServiceabilityListIDs(sourceEntityID, lenderIDs, checkMode)
	if !checkPincodeServiceability {
		return false
	}

	return serviceablepincode.IsBlocked(pincode, listIDs)
}

type PincodeCheckStruct struct {
	Pincode       string
	IsServiceable bool
}

// CheckPincodeServiceabilityHelper returns boolean if pincode check is applicable or not, service mode, map of address type as key and PincodeCheckStruct as value and error if any
func CheckPincodeServiceabilityHelper(loanApplicationID string, sourceEntityID string, lenderID string) (bool, int, map[string]PincodeCheckStruct, error) {
	checkForPincode, serviceMode, listID, _ := pincodeapi.GetPincodeServiceabilityMode(sourceEntityID, lenderID)
	if !checkForPincode {
		return checkForPincode, serviceMode, nil, nil
	}

	// get pincode from address
	type Address struct {
		CurrentAddressStr   string `db:"current_address"`
		PermanentAddressStr string `db:"permanent_address"`
	}
	var addressObj Address
	query := `SELECT coalesce(current_address,'') as current_address, coalesce(permanent_address,'') as permanent_address from user_loan_details where loan_application_id = $1 and status = $2 order by created_at desc limit 1`
	err := database.Get(&addressObj, query, loanApplicationID, constants.LoanDetailsStatusActive)
	if err != nil {
		log.Println(err)
		return checkForPincode, serviceMode, nil, err
	}

	if addressObj.CurrentAddressStr == "" || addressObj.CurrentAddressStr == "{}" {
		logger.WithLoanApplication(loanApplicationID).Warn("Current address not found")
		addressObj.CurrentAddressStr = addressObj.PermanentAddressStr
	}
	type addrStruct struct {
		Line1   string `json:"line1"`
		Line2   string `json:"line2"`
		City    string `json:"city"`
		Pincode string `json:"pincode"`
		State   string `json:"state"`
	}
	var currentAddObj addrStruct
	rawIn := json.RawMessage(addressObj.CurrentAddressStr)
	bytes, err := rawIn.MarshalJSON()
	if err != nil {
		log.Println(err)
		return checkForPincode, serviceMode, nil, err
	}
	err = json.Unmarshal(bytes, &currentAddObj)
	if err != nil {
		log.Println(err)
		return checkForPincode, serviceMode, nil, err
	}
	var permanentAddObj addrStruct
	rawIn = json.RawMessage(addressObj.PermanentAddressStr)
	bytes, err = rawIn.MarshalJSON()
	if err != nil {
		log.Println(err)
		return checkForPincode, serviceMode, nil, err
	}
	err = json.Unmarshal(bytes, &permanentAddObj)
	if err != nil {
		log.Println(err)
		return checkForPincode, serviceMode, nil, err
	}

	// By default pincode is assumed to be serviceable
	// Matches with the previous functionality
	currentPincodeServiciable := true
	permanentPincodeServiciable := true
	switch serviceMode {
	case constants.PositivePincodeCheck:
		currentPincodeServiciable = serviceablepincode.IsServiceable(currentAddObj.Pincode, listID)
		permanentPincodeServiciable = serviceablepincode.IsServiceable(permanentAddObj.Pincode, listID)

	case constants.NegativePincodeCheck:
		currentPincodeServiciable = !nonserviceablepincode.IsNonServiceable(currentAddObj.Pincode, listID)
		permanentPincodeServiciable = !nonserviceablepincode.IsNonServiceable(permanentAddObj.Pincode, listID)
	}

	return checkForPincode, serviceMode, map[string]PincodeCheckStruct{
		constants.AddressTypeCurrent: {
			Pincode:       currentAddObj.Pincode,
			IsServiceable: currentPincodeServiciable,
		},
		constants.AddressTypePermanent: {
			Pincode:       permanentAddObj.Pincode,
			IsServiceable: permanentPincodeServiciable,
		},
	}, nil
}

// CheckPincodeServiceability checks for pin code serviceability, and returns true in case of pass
func CheckPincodeServiceability(userID string, loanApplicationID string, sourceEntityID string, lenderID string) (isServiceable bool, rejectReason string) {

	// check for deviations if present
	// deviationFound, err := deviations.Present("", loanApplicationID, deviations.TypeAddressVerify)
	// if err != nil {
	// 	errorHandler.ReportToSentryWithoutRequest(err)
	// }
	// if deviationFound {
	// 	return true, ""
	// }
	checkForPincode, _, pMap, err := CheckPincodeServiceabilityHelper(loanApplicationID, sourceEntityID, lenderID)
	if !checkForPincode {
		return true, ""
	}
	if err != nil {
		// By default pincode is assumed to be serviceable
		// Matches with the previous functionality
		return true, ""
	}

	if pMap[constants.AddressTypeCurrent].IsServiceable && !pMap[constants.AddressTypePermanent].IsServiceable {
		return true, constants.ReasonAddressVerificationQueue
	} else if !pMap[constants.AddressTypeCurrent].IsServiceable &&
		!pMap[constants.AddressTypePermanent].IsServiceable {
		return false, constants.RejectReasonPincodeNotServiceable
	} else if !pMap[constants.AddressTypeCurrent].IsServiceable &&
		pMap[constants.AddressTypePermanent].IsServiceable &&
		!journey.IIFLBLPolicy(userID, sourceEntityID) {
		return false, constants.RejectReasonCurrentPincodeNotServiceable
	} else {
		return true, ""
	}
}

// CheckPlatformPincodeBlocked checks if a pincode is blocked for a platform
func CheckPlatformPincodeBlocked(pincode string, sourceEntityID string) bool {
	var blockCount int
	query := `SELECT count(*) from platform_blocked_pincode where source_entity_id = $1 and pincode = $2`
	err := database.Get(&blockCount, query, sourceEntityID, pincode)
	if err != nil {
		log.Errorln(err)
	}
	return blockCount > 0
}

// underwritingCheckIIFLBL fetches final decision for BL flow based on different rule execution
func underwritingCheckIIFLBL(sourceEntityID, userID string) (string, error) {

	var count int

	var (
		query string
		err   error
	)

	query = `SELECT count(1) from decision_engine_response where user_id = $1 and lender_id = $2`
	err = database.Get(&count, query, userID, constants.IIFLID)
	if err != nil {
		log.Println(err)
		if err != sql.ErrNoRows {
			errorHandler.ReportToSentryWithFields(map[string]interface{}{
				"userID": userID,
			}, err)
			return constants.UnderwritingDecisionCantDecide, err
		}
	}
	if count > 0 {
		return constants.UnderwritingDecisionPass, nil
	} else {
		errorHandler.ReportToSentryWithFields(map[string]interface{}{
			"userID": userID,
		}, err)
		return constants.UnderwritingDecisionCantDecide, nil
	}
}

// CheckUnderwriting checks underwriting for loan application based on different parameters
// returns decision, reject reason if any, new lender, and error if any
func CheckUnderwriting(loanApplicationID string) (string, string, string, error) {
	type loanStruct struct {
		PAN               string
		SourceEntityID    string
		LenderID          string
		UserID            string
		Mobile            string
		LoanApplicationNo string  `db:"loan_application_no"`
		Amount            float64 `db:"amount"`
		LoanType          string  `db:"loantype"`
	}

	var loanObj loanStruct
	query := `select coalesce(u.pan , '') as pan,
					u.source_entity_id as sourceentityid,
					l.lender_id as lenderid,
					coalesce(l.loan_type,'') as loantype,
					u.user_id as userid,
					u.mobile as mobile,
					coalesce(l.loan_application_no, '') as loan_application_no,
					coalesce(l.amount, 0) as amount
				from users u join loan_application l on u.user_id = l.user_id where l.loan_application_id = $1`
	err := database.Get(&loanObj, query, loanApplicationID)
	if err != nil {
		return constants.UnderwritingDecisionCantDecide, "", "", err
	}
	decision := constants.UnderwritingDecisionCantDecide

	var rejectReason string

	// uncomment below code if hunter check is to be enabled for PFL Education Loan
	if journey.IsPFLEducationLoanJourney(loanObj.SourceEntityID) {
		decision = constants.UnderwritingDecisionPass
		// sourceEntityID := constants.PFLEducationLoanSEID
		// hunterCheckRes, err := poonawalla.HunterCheck(context.TODO(), loanObj.UserID, loanApplicationID, sourceEntityID, loanObj.LoanType, loanObj.LenderID, poonawalla.OptionsHunterCheck{
		// 	DocType: constants.DocTypeAadhaarPhoto,
		// })
		// if err != nil {
		// 	logger.WithLoanApplication(loanApplicationID).Error(err)
		// 	decision = constants.UnderwritingDecisionCantDecide
		// } else if strings.ToLower(hunterCheckRes.OverallStatus) != poonawalla.HunterGo {
		// 	decision = constants.UnderwritingDecisionReject
		// 	rejectReason = poonawalla.HunterRejectedReason
		// 	go activity.ActivityLogger(loanObj.UserID, loanObj.SourceEntityID, "", constants.EntityTypeSystem, constants.ActivityLoanRejected,
		// 		rejectReason, loanApplicationID, general.GetTimeStampString(), false)
		// }
		return decision, rejectReason, "", nil
	}

	// reject loan condition if lenders are abfl pl or mfl bl
	if general.InArr(loanObj.LenderID, []string{constants.ABFLPLID, constants.MFLBLID}) {
		isCurrentAndPermanentAddressSame, err := users.GetDynamicUserInfoField(loanObj.UserID, "isCurrentAndPermanentAddressSame")
		if err != nil {
			logger.WithUser(loanObj.UserID).Errorln(err)
			return constants.UnderwritingDecisionCantDecide, "", "", fmt.Errorf("error finding field: %s err: %s", "isCurrentAndPermanentAddressSame", err.Error())
		}

		if strings.Contains(strings.ToLower(isCurrentAndPermanentAddressSame), "no") {
			logger.WithUser(loanApplicationID).Errorln("loanapplication reject: current address not same as permanent address")
			return constants.UnderwritingDecisionReject, "current address not same as permanent address", loanObj.LenderID, nil
		}
	}

	// For MFL BL send users to wait state post successful KYC
	if loanObj.LenderID == constants.MFLBLID {
		tx, err := database.Beginx()
		if err != nil {
			logger.WithUser(loanObj.UserID).Errorln(err)
			return constants.UnderwritingDecisionCantDecide, "", "", err
		}
		defer tx.Rollback()

		addressMatchResp, err := users.GetDynamicUserInfoField(loanObj.UserID, constants.MFLBLKYCAddressMatchResultIdentifier)
		if err != nil {
			logger.WithUser(loanObj.UserID).Errorln(err)
			return constants.UnderwritingDecisionCantDecide, "", "", fmt.Errorf("error finding field: %s err: %s", constants.MFLBLKYCAddressMatchResultIdentifier, err.Error())
		}

		if addressMatchResp == constants.MFLBLKYCAddressMatchFail {
			logger.WithUser(loanObj.UserID).Warnln(constants.MFLBLKYCAddressRejectReason)
			return constants.UnderwritingDecisionReject, constants.MFLBLKYCAddressRejectReason, "", nil
		}

		err = loanapplication.UpdateKYCStatus(tx, loanApplicationID, constants.LoanKYCStatusUnderReview)
		if err != nil {
			logger.WithLoanApplication(loanApplicationID).Errorln(err)
			return constants.UnderwritingDecisionCantDecide, "", "", err
		}
		err = userjourney.SetWaitStatus(tx, loanObj.UserID, userjourney.WaitStatusShortPeriod, "waiting for final KYC approval from dashboard")
		if err != nil {
			logger.WithLoanApplication(loanApplicationID).Errorln(err)
			return constants.UnderwritingDecisionCantDecide, "", "", err
		}

		err = tx.Commit()
		if err != nil {
			logger.WithUser(loanObj.UserID).Errorln(err)
			return constants.UnderwritingDecisionCantDecide, "", "", err
		}
	}

	if loanObj.SourceEntityID == constants.NiyoID && conf.ENV == conf.ENV_PROD {
		// check if rule engine was called already
		query = `SELECT decision from decision_engine_response where user_id = $1 and rule_version in ($2, $3) order by created_at desc limit 1`
		err = database.Get(&decision, query, loanObj.UserID, "niyo_v1.2", constants.DisqualifyRevertRule)
		if err != nil {
			log.Println(err)
			var done bool
			// call rule engine, if not called already
			decision, done, rejectReason, _ = CallRuleEngine(loanObj.UserID, loanObj.SourceEntityID, constants.TrustLenderID, "niyo_v1.2")
			if !done {
				// do not update user wait status
				return constants.UnderwritingDecisionCantDecide, "", "", errors.New("decision failed")
			}
		}
	} else if loanObj.SourceEntityID == constants.ArzoooID {
		tempDecision, done, reason, ruleResp := CallRuleEngine(loanObj.UserID, loanObj.SourceEntityID, loanObj.LenderID, "bl_anchor_v1")
		if !done {
			// do not update user wait status
			return constants.UnderwritingDecisionCantDecide, "", "", errors.New("decision failed")
		}
		switch tempDecision {
		case constants.UnderwritingDecisionPass:
			decision = constants.UnderwritingDecisionPass
		case constants.UnderwritingDecisionReject:
			if ruleResp.Data.Decision == "" && reason != "" {
				decision = constants.UnderwritingDecisionReject
				rejectReason = reason
			} else if loanObj.LenderID == constants.IIFLID {
				// update lender
				loanObj.LenderID = constants.ArthanLenderID
				query = `UPDATE loan_application set lender_id = $1, updated_at = NOW() where loan_application_id = $2`
				_, err = database.Exec(query, loanObj.LenderID, loanApplicationID)
				if err != nil {
					log.Println(err)
				}
				var referenceID string
				query := `SELECT reference_id from decision_engine_response where user_id = $1 order by created_at desc limit 1`
				err := database.Get(&referenceID, query, loanObj.UserID)
				if err != nil {
					log.Println(err)
				}
				if referenceID != "" {
					query = `UPDATE decision_engine_response set lender_id = $1 where reference_id = $2`
					_, err = database.Exec(query, constants.ArthanLenderID, referenceID)
					if err != nil {
						log.Println(err)
					}
				}
			}
		}
	} else if loanObj.SourceEntityID == constants.ExpressStoresID {
		var done bool
		decision, done, rejectReason, _ = CallRuleEngine(loanObj.UserID, loanObj.SourceEntityID, constants.ArthanLenderID, "es_bank_v1")
		if !done {
			// do not update user wait status
			return constants.UnderwritingDecisionCantDecide, "", "", errors.New("decision failed")
		}
	} else if loanObj.SourceEntityID == constants.KhatabookID {
		var tempDecision string
		// check if rule engine was called already
		query = `SELECT decision from decision_engine_response where user_id = $1 order by created_at desc limit 1`
		err = database.Get(&tempDecision, query, loanObj.UserID)
		if err != nil {
			log.Println(err)
		}
		if tempDecision != "" {
			decision = tempDecision
		}
		/*if tempDecision == constants.UnderwritingDecisionPass || tempDecision == constants.UnderwritingDecisionCantDecide {
			// check for selected EMI
			maxEMI := commonutils.GetMaxEMIForUI(loanApplicationID)
			if maxEMI > 0 {
				// get user selected EMI
				type offerTemplateStruct struct {
					Method            string
					Interest          float64
					Tenure            int
					ProcessingFee     float64
					ProcessingFeeType string
					GST               float64
				}
				offerTemplate := offerTemplateStruct{}
				query = `SELECT method, interest, tenure, processing_fee as processingfee,
							processing_fee_type as processingfeetype, gst
							FROM loan_offer_template where source_entity_id = $1 and lender_id = $2 limit 1`
				err = database.Get(&offerTemplate, query, constants.KhatabookID, loanObj.LenderID)
				if err != nil {
					log.Println(err)
				} else {
					var eligibleAmount float64
					eligibleAmount, _, _, offerTemplate.Interest, offerTemplate.Tenure, _ = loanprogram.GetUpdatedLoanOfferTemplate(loanObj.UserID, constants.KhatabookID, offerTemplate.ProcessingFee, offerTemplate.ProcessingFeeType, offerTemplate.Interest, offerTemplate.Tenure)
					calculatedEMI, _ := calc.GetEMI(offerTemplate.Method, eligibleAmount, offerTemplate.Tenure, offerTemplate.Interest, time.Now(), constants.KhatabookID, loanObj.LenderID)
					log.Println("EMI", loanApplicationID, calculatedEMI, maxEMI)
					if calculatedEMI > 0 && calculatedEMI <= maxEMI {
						decision = constants.UnderwritingDecisionPass
					}
				}
			} else {
				decision = constants.UnderwritingDecisionPass
			}
		}*/
		// If user already approved from under review, move him forward
		if decision == constants.UnderwritingDecisionCantDecide {
			var count int
			query = "select count(*) from activity_log where event_description = $1 and event_type = $2 and user_id = $3 and extract(epoch from now() - logged_at) > $4"
			err = database.Get(&count, query, "approved from under_review", "user_qualified", loanObj.UserID, constants.ActivityLogTimeLimit)
			if err != nil {
				log.Println(err)
			}
			if count > 0 {
				decision = constants.UnderwritingDecisionPass
			}
		}

	} else if loanObj.SourceEntityID == constants.BeldaraID {
		var tempDecision string
		// check if rule engine was called already
		query = `SELECT decision from decision_engine_response where user_id = $1 order by created_at desc limit 1`
		err = database.Get(&tempDecision, query, loanObj.UserID)
		if err != nil {
			log.Println(err)
		}

		if tempDecision == constants.UnderwritingDecisionPass {
			decision = tempDecision
		}
	} else if loanObj.SourceEntityID == constants.CashifyID {
		// check if rule engine was called already
		query = `SELECT decision from decision_engine_response where user_id = $1 and rule_version in ($2, $3) order by created_at desc limit 1`
		err = database.Get(&decision, query, loanObj.UserID, "cashify_v1", constants.DisqualifyRevertRule)
		if err != nil {
			log.Println(err)
			// call rule engine, if not called already
			var firstResp DecisionRunStruct
			var done bool
			decision, done, rejectReason, firstResp = CallRuleEngine(loanObj.UserID, loanObj.SourceEntityID, constants.ArthanLenderID, "cashify_v1")
			if !done {
				// do not update user wait status
				return constants.UnderwritingDecisionCantDecide, "", "", errors.New("decision failed")
			}
			if decision == constants.UnderwritingDecisionPass {
				err = UpdateEligibleAmount(loanObj.UserID, loanObj.SourceEntityID, firstResp.ReferenceID, firstResp.Data.Amount)
				if err != nil {
					log.Println(err)
					return constants.UnderwritingDecisionCantDecide, "", "", err
				}
			}
		}
	} else if journey.IsSaraloanAsLender(loanObj.SourceEntityID) {
		idMapping, errF := saraloan.IDMapping(loanObj.UserID)
		if errF != nil {
			log.Println(errF)
			activity.RegisterAuxiliaryEventArgs(loanObj.UserID, loanObj.SourceEntityID, loanApplicationID, constants.EntityTypeSystem, "", saraloan.IDMappingExternalService, errF.Error(), nil)
			return constants.UnderwritingDecisionCantDecide, "", "", errF
		}
		applicationID := strconv.Itoa(idMapping.ApplicationID)
		getApplicationDetails, errF := saraloan.GetApplicationDetailsAPI(applicationID, loanObj.UserID)
		if errF != nil {
			log.Println(errF)
			activity.RegisterAuxiliaryEventArgs(loanObj.UserID, loanObj.SourceEntityID, loanApplicationID, constants.EntityTypeSystem, "", saraloan.ApplicationDetailsExternalService, errF.Error(), nil)
			return constants.UnderwritingDecisionCantDecide, "", "", errF
		}
		errF = saraloan.UpdateApplicationNo(loanApplicationID, getApplicationDetails.ApplicationID)
		if errF != nil {
			log.Println(errF)
			activity.RegisterAuxiliaryEventArgs(loanObj.UserID, loanObj.SourceEntityID, loanApplicationID, constants.EntityTypeSystem, "", saraloan.ApplicationDetailsExternalService, errF.Error(), nil)
			return constants.UnderwritingDecisionCantDecide, "", "", errF
		}
		serviceName, errF := saraloan.UpdateBusinessPersonAddressAndDocuments(loanObj.UserID, false, loanApplicationID)
		if errF != nil {
			activity.RegisterAuxiliaryEventArgs(loanObj.UserID, loanObj.SourceEntityID, loanApplicationID, constants.EntityTypeSystem, "", serviceName, errF.Error(), nil)
			log.Println(errF)
			return constants.UnderwritingDecisionCantDecide, "", "", errF
		}
		decision = constants.UnderwritingDecisionPass
	} else if general.InArr(loanObj.LenderID, []string{constants.LoanTapID, constants.EcofyID, constants.MuthootCLID}) {
		query = `SELECT decision from decision_engine_response where user_id = $1 and lender_id = $2 order by created_at desc limit 1`
		err = database.Get(&decision, query, loanObj.UserID, loanObj.LenderID)
		if err != nil {
			logger.WithLoanApplication(loanApplicationID).Errorln(err)
		}
		if decision == "" {
			decision = constants.UnderwritingDecisionCantDecide
		}
	}

	if general.InArr(loanObj.LenderID, []string{constants.ABFLID, constants.ABFLPLID}) {
		// decision = constants.UnderwritingDecisionPass
		documentID, err := media.GetDocIDByType(constants.DocTypeAdditionalDocument, loanObj.UserID)
		if err != nil {
			logger.WithLoanApplication(loanApplicationID).Errorln(err)
			return constants.UnderwritingDecisionCantDecide, "", "", err
		}
		decision, rejectReason, err = ABFLBRE7(loanObj.UserID, loanApplicationID, loanObj.SourceEntityID, loanObj.LenderID, documentID, "success", false)
		if err != nil {
			logger.WithLoanApplication(loanApplicationID).Errorln(err)
			return constants.UnderwritingDecisionCantDecide, "", "", err
		}
		if decision == constants.UnderwritingDecisionCantDecide {
			err = loanapplication.UpdateKYCStatus(nil, loanApplicationID, constants.LoanKYCStatusUnderReview)
			if err != nil {
				logger.WithLoanApplication(loanApplicationID).Errorln(err)
				return constants.UnderwritingDecisionCantDecide, "", "", err
			}
			err = userjourney.SetWaitStatus(nil, loanObj.UserID, userjourney.WaitStatusShortPeriod, "triggering BRE7 workflows")
			if err != nil {
				logger.WithLoanApplication(loanApplicationID).Errorln(err)
				return constants.UnderwritingDecisionCantDecide, "", "", err
			}
		}
	}

	//not checking pincode serviceability for preApproved user
	var isMFLPreApproved bool
	if loanObj.LenderID == constants.MFLBLID {
		isMFLPreApproved, err = partner.IsMFLBLPreApprovedUser(loanObj.UserID)
		if err != nil {
			logger.WithUser(loanObj.UserID).Errorln("error checking pre approved", err)
		}
	}

	var isServiceable bool
	if decision != constants.UnderwritingDecisionReject && loanObj.LenderID != constants.PoonawallaFincorpID && loanObj.LenderID != constants.ABFLPLID && !isMFLPreApproved {
		// check for pincode servicability
		isServiceable, rejectReason = CheckPincodeServiceability(loanObj.UserID, loanApplicationID, loanObj.SourceEntityID, loanObj.LenderID)
		if !isServiceable {
			logger.WithLoanApplication(loanApplicationID).Error("rejected due to pincode check: ", rejectReason)
			if loanObj.SourceEntityID == constants.KhatabookID || journey.IsIIFLBLSourcing(loanObj.SourceEntityID) {
				decision = constants.UnderwritingDecisionCantDecide
			} else {
				decision = constants.UnderwritingDecisionReject
			}
		}
	}

	if loanObj.LenderID == constants.HCINLenderID && decision != constants.UnderwritingDecisionReject {
		err = hcin.CreateCustomer(loanApplicationID)
		if err != nil {
			log.Println(err)
			mailalert.SendInternal("HCIN Create Customer failed for "+loanApplicationID+" - "+conf.ENV, err.Error())
			return constants.UnderwritingDecisionCantDecide, "", loanObj.LenderID, err
		}
		return constants.UnderwritingDecisionCantDecide, "", loanObj.LenderID, nil
	}

	if loanObj.LenderID == constants.PoonawallaFincorpID && decision != constants.UnderwritingDecisionReject {
		// perform official email and aadhaar xml name match
		skipMatch := false
		officialEmail, err := users.GetDynamicUserInfoField(loanObj.UserID, "officialEmail")
		if err != nil {
			logger.WithUser(loanObj.UserID).Errorln("error finding officialEmail:", err)
			skipMatch = true
		}

		if officialEmail == "" {
			logger.WithUser(loanObj.UserID).Errorln("officialEmail is empty, skipping officialEmailAndAadhaarName match")
			skipMatch = true
		}

		_, name, err := loankycdetails.GetIdentifierAndNameForDocType(loanApplicationID, constants.DocTypeAadhaarXML)
		if err != nil {
			logger.WithUser(loanObj.UserID).Errorln(err)
			skipMatch = true
		}

		if name == "" {
			logger.WithUser(loanObj.UserID).Errorln("name is empty, skipping officialEmailAndAadhaarName match")
			skipMatch = true
		}

		if !skipMatch {
			tx, err := database.Beginx()
			if err != nil {
				logger.WithUser(loanObj.UserID).Errorln(err)
				return constants.UnderwritingDecisionCantDecide, "", "", err
			}
			defer tx.Rollback()

			isMatched, _, err := CheckOfficialEmailAndAadhaarNameFuzzyMatch(tx, loanObj.UserID, officialEmail, name, OptionsOfficialEmailAndAadhaarNameMatch{
				SaveMatchResultAndScoreToDUI: true,
				ReqThresholdToPass:           constants.PFLOfficialEmailAndAadhaarNameMatchThresh,
			})
			if err != nil {
				logger.WithUser(loanObj.UserID).Errorln(err)
				return constants.UnderwritingDecisionCantDecide, "", "", err
			}

			err = tx.Commit()
			if err != nil {
				logger.WithUser(loanObj.UserID).Errorln(err)
				return constants.UnderwritingDecisionCantDecide, "", "", err
			}

			if !isMatched {
				logger.WithUser(loanObj.UserID).Errorln("rejecting user as official email and aadhaar name does not match")
				return constants.UnderwritingDecisionReject, "official email and aadhaar name does not match", "", nil
			}
		}

		offerCount := 0
		if journey.IsOfferFlowV2(loanObj.UserID, loanObj.SourceEntityID) {
			offerCount, err = personalloanoffer.GetCount(loanObj.UserID, constants.OfferStatusIsAccepted)
		} else {
			query := `select count(*) from user_eligibility where interest is not null and user_id = $1`
			err = database.Get(&offerCount, query, loanObj.UserID)
		}
		if err != nil {
			logger.WithUser(loanObj.UserID).Error(err)
			errorHandler.ReportToSentryWithoutRequest(err)
			return constants.UnderwritingDecisionCantDecide, "", "", err
		}
		if offerCount <= 0 {
			decision = constants.UnderwritingDecisionCantDecide
		} else {
			var decisionPFL string
			query = `SELECT decision from decision_engine_response where user_id = $1 and lender_id = $2 and rule_version like 'PFL%' order by created_at desc limit 1`
			err = database.Get(&decisionPFL, query, loanObj.UserID, constants.PoonawallaFincorpID)
			if err != nil {
				logger.WithUser(loanObj.UserID).Error(err)
				errorHandler.ReportToSentryWithoutRequest(err)
				return constants.UnderwritingDecisionCantDecide, "", "", err
			}
			if !general.InArr(decisionPFL, []string{constants.UnderwritingDecisionPass, constants.UnderwritingDecisionBoost, constants.UnderwritingDecisionBankConnect, constants.UnderwritingDecisionSkipBankConnect}) {
				errorHandler.ReportToSentryWithFields(errorHandler.ErrorFields{"user-id": loanObj.UserID, "decision": decisionPFL}, errors.New("latest decision for PFL not in pass/boost/bank_connect"))
				decision = constants.UnderwritingDecisionCantDecide
			} else {
				decision = constants.UnderwritingDecisionPass
			}
		}
		// do hunter check only if personal loan journey
		if !journey.IsPFLEducationLoanJourney(loanObj.SourceEntityID) {
			//hunter check for pfl
			hunterCheckRes, err := poonawalla.HunterCheck(context.TODO(), loanObj.UserID, loanApplicationID, loanObj.SourceEntityID, loanObj.LoanType, loanObj.LenderID, poonawalla.OptionsHunterCheck{
				DocType: constants.DocTypeDigilockerAadhaar,
			})
			if err != nil {
				logger.WithLoanApplication(loanApplicationID).Error(err)
				decision = constants.UnderwritingDecisionCantDecide
			} else if strings.ToLower(hunterCheckRes.OverallStatus) != poonawalla.HunterGo {
				decision = constants.UnderwritingDecisionReject
				rejectReason = poonawalla.HunterRejectedReason
				go activity.ActivityLogger(loanObj.UserID, loanObj.SourceEntityID, "", constants.EntityTypeSystem, constants.ActivityLoanRejected,
					rejectReason, loanApplicationID, general.GetTimeStampString(), false)
			}
		}
	}

	// auto approve for now, since qualification already checked for certain cases
	if loanObj.LenderID == constants.IIFLID && decision != constants.UnderwritingDecisionReject {
		if journey.IIFLPLPolicy(loanObj.SourceEntityID) {
			query = `SELECT decision from decision_engine_response where user_id = $1 and lender_id = $2 and (rule_version like '%bank%' OR rule_version like '%iifl_v%' OR rule_version like '%iifl_sal%' OR rule_version like '%self_employed%' OR rule_version = $3) order by created_at desc limit 1`
			err = database.Get(&decision, query, loanObj.UserID, constants.IIFLID, constants.DisqualifyRevertRule)
			if err != nil {
				log.Println(err)
			}
			if decision == "" {
				decision = constants.UnderwritingDecisionCantDecide
			}
		} else if journey.IIFLBLPolicy(loanObj.UserID, loanObj.SourceEntityID) || journey.IsNexarcIIFL(loanObj.UserID, loanObj.SourceEntityID) {

			decision, err = underwritingCheckIIFLBL(loanObj.SourceEntityID, loanObj.UserID)
			if err != nil {
				log.Println(err)
			}

		} else if conf.ENV == conf.ENV_PROD {
			// cross check before blind approval
			decision = ""
			query = `SELECT decision from decision_engine_response where user_id = $1 and lender_id = $2 order by created_at desc limit 1`
			err = database.Get(&decision, query, loanObj.UserID, constants.IIFLID)
			if err != nil {
				log.Println(err)
			}
			if decision == "" {
				decision = constants.UnderwritingDecisionCantDecide
			}
		} else {
			decision = constants.UnderwritingDecisionPass
		}

		if decision == constants.UnderwritingDecisionPass && journey.DoCIBILHardPull(loanObj.SourceEntityID, loanObj.UserID) {
			decision, rejectReason, err = ExecuteBREOnHardPullData(loanObj.UserID, loanObj.SourceEntityID, loanApplicationID)
			if err != nil {
				logger.WithUser(loanObj.UserID).Errorln(err)
				decision = constants.UnderwritingDecisionCantDecide
			}
			key := fmt.Sprintf("%s-%s", loanApplicationID, "hardpull-sentinel-run")
			_ = redis.Set(key, "1", time.Hour*24*45)
		}

	}

	if decision != constants.UnderwritingDecisionReject && journey.RunPerfiosFCU(loanObj.UserID, loanObj.SourceEntityID) {
		fraudStatus, err := bankconnect.ExecutePerfiosFlow(loanObj.UserID, loanObj.SourceEntityID)
		if fraudStatus == constants.PerfiosStatusFraud {
			decision = constants.UnderwritingDecisionReject
			rejectReason = fmt.Sprintf("perfios statement status: %s", fraudStatus)
			return decision, rejectReason, loanObj.LenderID, nil
		} else if err != nil {
			logger.WithUser(loanObj.UserID).Errorln(err)
			activity.ActivityLogger(loanObj.UserID, loanObj.SourceEntityID, "", constants.EntityTypeSystem, constants.ActivityPerfiosFailed, "", loanApplicationID, general.GetTimeStampString(), false)
		}
	}

	if loanObj.SourceEntityID == constants.PagarBookID && decision != constants.UnderwritingDecisionReject {
		decision = constants.UnderwritingDecisionPass
	}

	if general.InArr(loanObj.LenderID, []string{constants.XYZLenderID, constants.ABCLenderID, constants.MintifiID, constants.MCSLID}) && decision != constants.UnderwritingDecisionReject {
		decision = constants.UnderwritingDecisionPass
	}

	//call lending kart apis here.

	if loanObj.LenderID == constants.LendingKartID && general.InArr(loanObj.SourceEntityID, []string{constants.TataNexarcID, constants.VyaparID}) {
		// using the lender integration service
		createApplicantresp, err := lendingkart.CreateApplication(context.Background(), loanObj.UserID, loanApplicationID, loanObj.LenderID, loanObj.SourceEntityID)
		if err != nil {
			logger.WithUser(loanObj.UserID).Println(err)
			return constants.UnderwritingDecisionCantDecide, "", "", err
		}

		tx, tErr := database.Beginx()
		if tErr != nil {
			logger.WithUser(loanObj.UserID).Println(tErr)
			return constants.UnderwritingDecisionCantDecide, "", "", tErr
		}
		defer tx.Rollback()

		err = users.Update(tx, users.User{
			ID:    loanObj.UserID,
			CrmID: createApplicantresp.CrmID,
		})
		if err != nil {
			tx.Rollback()
			logger.WithUser(loanObj.UserID).Println(err)
			return constants.UnderwritingDecisionCantDecide, "", "", err
		}

		err = loanapplication.Update(tx, loanapplication.StructForSet{
			ID:                loanApplicationID,
			LoanApplicationNo: createApplicantresp.LoanApplicationNumber,
			OldApplicationNo:  loanObj.LoanApplicationNo,
		})

		if err != nil {
			tx.Rollback()
			logger.WithUser(loanObj.UserID).Println(err)
			return constants.UnderwritingDecisionCantDecide, "", "", err
		}
		err = tx.Commit()
		if err != nil {
			logger.WithUser(loanObj.UserID).Println(err)
			return constants.UnderwritingDecisionCantDecide, "", "", err
		}

		//upload docs
		go func() {
			err := lendingkart.UploadKycDocuments(context.Background(),
				loanApplicationID,
				loanObj.UserID,
				loanObj.SourceEntityID,
				createApplicantresp.LoanApplicationNumber)
			if err != nil {
				logger.WithUser(loanObj.UserID).Println(err)
			}
		}()
		return constants.UnderwritingDecisionPass, "", "", nil
	}

	if loanObj.LenderID == constants.KotakID && loanObj.SourceEntityID == constants.TataNexarcID {
		leadID, err := kotak.CreateLead(context.Background(), loanApplicationID)
		if err != nil {
			logger.WithUser(loanObj.UserID).Println(err)
			return constants.UnderwritingDecisionCantDecide, "", "", err
		}

		err = kotak.UploadDocument(context.Background(), loanObj.UserID, loanApplicationID, leadID, constants.KotakTLUploadDoc)
		if err != nil {
			logger.WithUser(loanObj.UserID).Println(err)
		}

		err = kotak.CreateCoApplicantAndUploadDoc(context.Background(), leadID, loanObj.SourceEntityID, loanObj.UserID, loanApplicationID)
		if err != nil {
			logger.WithUser(loanObj.UserID).Println(err)
		}
		return constants.UnderwritingDecisionPass, "", "", nil
	}

	//if loanObj.LenderID == constants.TataCapitalID && general.InArr(loanObj.SourceEntityID, []string{constants.BigBasketID, constants.TataNexarcID}) {
	//	getAuthResObj, err := tatacapital.GetAuth(loanObj.UserID)
	//	if err != nil {
	//		logger.WithUser(loanObj.UserID).Println(err)
	//		return constants.UnderwritingDecisionCantDecide, "", "", err
	//	}
	//
	//	sourceName := tatacapital.GetSourceName(loanObj.SourceEntityID)
	//
	//	oppID, createOppIDReq, err := tatacapital.CreateLeadAndOpp(context.Background(), loanApplicationID, loanObj.SourceEntityID, getAuthResObj.AccessToken, sourceName)
	//	if err != nil {
	//		logger.WithUser(loanObj.UserID).Println(err)
	//		return constants.UnderwritingDecisionCantDecide, "", "", err
	//	}
	//	err = tatacapital.UploadDocuments(oppID, loanObj.UserID, loanApplicationID, getAuthResObj.AccessToken, sourceName, createOppIDReq)
	//	if err != nil {
	//		logger.WithUser(loanObj.UserID).Println(err)
	//		return constants.UnderwritingDecisionCantDecide, "", "", err
	//	}
	//	return constants.UnderwritingDecisionPass, "", "", nil
	//}

	//if loanObj.LenderID == constants.TataCapitalID && general.InArr(loanObj.SourceEntityID, []string{constants.BigBasketID}) {
	//	//trigger bureau pull at tcap end only. No response is to be used right now.
	//
	//	_, err := tatacapital.BureauPull(context.Background(), loanObj.UserID, loanApplicationID, loanObj.SourceEntityID)
	//	if err != nil {
	//		logger.WithUser(loanObj.UserID).Error(err)
	//	}
	//
	//	// create application at tcap end.
	//	createApplicantresp, err := tatacapital.CreateApplication(context.Background(), loanObj.UserID, loanApplicationID, loanObj.SourceEntityID)
	//	if err != nil {
	//		logger.WithUser(loanObj.UserID).Println(err)
	//		return constants.UnderwritingDecisionCantDecide, "", "", err
	//	}
	//
	//	log.Println("Create applicant resp: ", createApplicantresp)
	//
	//	err = users.Update(nil, users.User{
	//		ID:         loanObj.UserID,
	//		CrmID:      createApplicantresp.CrmID,
	//		ProspectNo: createApplicantresp.ProspectNo,
	//	})
	//
	//	if err != nil {
	//		logger.WithUser(loanObj.UserID).Println(err)
	//		return constants.UnderwritingDecisionCantDecide, "", "", err
	//	}
	//
	//	//upload docs
	//	go func() {
	//		err := tatacapital.UploadKycDocumentsBL(context.Background(), loanApplicationID, loanObj.UserID, loanObj.SourceEntityID)
	//		if err != nil {
	//			logger.WithUser(loanObj.UserID).Errorln(err)
	//		}
	//	}()
	//
	//	return constants.UnderwritingDecisionPass, "", "", nil
	//}

	if journey.MoveToReviewIfPhoneMismatch(loanObj.UserID, loanObj.SourceEntityID) {
		decision = constants.UnderwritingDecisionCantDecide
	}

	if journey.IsTdlTcapPLFlow(loanObj.UserID, loanObj.SourceEntityID, loanObj.LenderID, loanApplicationID) {
		decision = constants.UnderwritingDecisionPass
	}

	return decision, rejectReason, loanObj.LenderID, nil
}

type dataStruct struct {
	ReferenceID string `json:"reference_id"`
}
type ruleEngineDumpResponse struct {
	Data dataStruct `json:"data"`
}

type BasicGSTData struct {
	NatureOfBusiness   []string       `json:"nature_of_business"`
	RegistrationDate   string         `json:"registration_date"`
	GSTIN              string         `json:"gstin"`
	Constitution       string         `json:"constitution"`
	Status             string         `json:"status"`
	HSNDetails         []HSNData      `json:"hsn_details"`
	NegativeCodeFound  bool           `json:"negative_code_found"`
	GSTMetadataDetails map[string]any `json:"gstMetadata"`
	Address            string         `json:"address"`
}

type HSNData struct {
	HSNCode     string `json:"hsn_code"`
	Description string `json:"description"`
	Industry    string `json:"industry"`
	SubIndustry string `json:"sub_industry"`
}

func GetDumpID(baseURL string, apiKey string, userID string, uniqueID string, sourceEntityID string, lenderID string, ruleVersion string) (string, bool, deviceConnect.DeviceConnectRespStruct) {
	url := fmt.Sprintf("%s/v1/decision/dump", baseURL)
	method := "POST"

	var gobj = map[string]string{
		"url":     url,
		"strReq":  "",
		"strRes":  "",
		"success": "0",
		"userID":  userID,
		"id":      general.GetUUID(),
	}
	payloadStr := "{"
	payloadStr += fmt.Sprintf(`"user_id": "%s"`, userID)

	if journey.GetStage2Bureau(userID, sourceEntityID) == constants.BureauCIBIL {
		cibilReportString, rawXMLReportString, source, _ := GetCIBILReportString(userID, sourceEntityID)
		reportAdded := false
		if general.InArr(source, []string{constants.CIBILReportSourceABFLHardPull, constants.CIBILReportSourceMuthootV2, constants.CIBILReportSourceTransUnionV2}) {
			payloadStr += `,"bureau_connect": {`
			var (
				err           error
				cibilResponse map[string]interface{}
			)
			if len(cibilReportString) > 0 {
				var consumerCreditDataBytes []byte
				if cibilReportString != "" && cibilReportString != "{}" && cibilReportString != "[]" {
					if err := json.Unmarshal([]byte(cibilReportString), &cibilResponse); err != nil {
						logger.WithUser(userID).Errorln(err)
						errorHandler.ReportToSentryWithoutRequest(err)
					}
					consumerCreditData, ok := cibilResponse["consumerCreditData"]
					if !ok {
						err := fmt.Errorf("consumerCreditData not found in cibil report - userID - %s", userID)
						logger.WithUser(userID).Errorln(err)
						errorHandler.ReportToSentryWithoutRequest(err)
					}
					consumerCreditDataBytes, err = json.Marshal(consumerCreditData)
					if err != nil {
						logger.WithUser(userID).Errorln(err)
					}
				} else {
					consumerCreditDataBytes = []byte(cibilReportString)
				}
				base64EncodedCibilReportString := base64.StdEncoding.EncodeToString(consumerCreditDataBytes)
				payloadStr += (`"source_type": "cibil_softpull_specified_user"`)
				payloadStr += fmt.Sprintf(`,"payload": "%s"`, base64EncodedCibilReportString)
				reportAdded = true
			}
			payloadStr += `}`
		} else if general.InArr(source, []string{constants.CIBILReportSourceTransUnion, constants.CIBILReportSourceMuthoot}) {
			payloadStr += `,"bureau_connect": {`
			base64EncodedCibilReportString := base64.StdEncoding.EncodeToString([]byte(cibilReportString))
			if base64EncodedCibilReportString != "" {
				// try with CIBIL first
				payloadStr += (`"source_type": "cibil_softpull_specified_user"`)
				payloadStr += fmt.Sprintf(`,"payload": "%s"`, base64EncodedCibilReportString)
				reportAdded = true
			}
			payloadStr += `}`
		} else if cibilReportString != "{}" && cibilReportString != "" {
			payloadStr += `,"bureau_connect": {`
			base64EncodedCibilReportString := base64.StdEncoding.EncodeToString([]byte(rawXMLReportString))
			if base64EncodedCibilReportString != "" {
				// try with CIBIL first
				payloadStr += (`"source_type": "cibil_softpull_xml"`)
				payloadStr += fmt.Sprintf(`,"payload": "%s"`, base64EncodedCibilReportString)
				reportAdded = true
			}
			payloadStr += `}`
		} else if journey.IsFallBackExperian(sourceEntityID, lenderID) {
			// add experian if CIBIL not available
			var experianReportString string
			query := `SELECT report_data from experian_reports where user_id = $1 and status = 'completed' order by created_at desc limit 1`
			err := database.Get(&experianReportString, query, userID)
			if err != nil {
				log.Println(err)
			} else {
				payloadStr += `,"bureau_connect": {`
				base64EncodedExperianReportString := base64.StdEncoding.EncodeToString([]byte(experianReportString))
				if base64EncodedExperianReportString != "" {
					// try with CIBIL first
					payloadStr += (`"source_type": "experian"`)
					payloadStr += fmt.Sprintf(`,"payload": "%s"`, base64EncodedExperianReportString)
					reportAdded = true
				}
				payloadStr += `}`
			}
		}
		if !reportAdded {
			payloadStr += fmt.Sprintf(`,"cibil_data": %s`, cibilReportString)
		}
	} else {
		// add experian
		var experianReportString string
		query := `SELECT report_data from experian_reports where user_id = $1 and status = 'completed' order by created_at desc limit 1`
		err := database.Get(&experianReportString, query, userID)
		if err != nil {
			log.Println(err)
		} else {
			payloadStr += `,"bureau_connect": {`
			base64EncodedExperianReportString := base64.StdEncoding.EncodeToString([]byte(experianReportString))
			if base64EncodedExperianReportString != "" {
				// try with CIBIL first
				payloadStr += (`"source_type": "experian"`)
				payloadStr += fmt.Sprintf(`,"payload": "%s"`, base64EncodedExperianReportString)
			}
			payloadStr += `}`
		}
	}
	if strings.Contains(ruleVersion, "_bank_") || strings.Contains(ruleVersion, "_macro_") || strings.Contains(ruleVersion, "self_employed") || ruleVersion == "cashify_v1" || ruleVersion == constants.AutomationPolicy || ruleVersion == constants.LoanTapSalariedPolicy || ruleVersion == constants.MuthootMSMEValueMediPolicy || ruleVersion == "nexarc_abfl_booster_v1" || general.InArr(lenderID, []string{constants.ABFLID, constants.ABFLPLID}) {
		// since bank involved pass name as well
		type nameStruct struct {
			Name     string
			FirmName string
		}
		var nameObj nameStruct

		query := `SELECT name, coalesce(firm_name, '') as firmname from users where user_id = $1`
		err := database.Get(&nameObj, query, userID)
		if err != nil {
			log.Println(err)
			errorHandler.ReportToSentryWithoutRequest(err)
		} else {

			nameObj.Name = general.RemoveExtraSpaces(nameObj.Name)
			nameObj.FirmName = general.GetOnlyAlphaNumSpace(nameObj.FirmName)
			if nameObj.FirmName != "" {
				payloadStr += fmt.Sprintf(`,"names": "%s,%s"`, nameObj.Name, nameObj.FirmName)
			} else {
				payloadStr += fmt.Sprintf(`,"names": "%s"`, nameObj.Name)
			}
		}
		// add bank connect data
		bankConnectDetails, err := bankconnectdetails.GetAllByDistinctAccount(userID, constants.BankConnectStatusCompleted)
		if err != nil {
			logger.WithUser(userID).Warnln(err)
		} else {
			bankConnectTransDetails, err := bankconnectdetails.GetLatestWithTransactions(userID, constants.BankConnectStatusCompleted)
			if err != nil {
				logger.WithUser(userID).Warnln(err)
			} else {
				// BC API Key
				clientAPIKey, serverHash, err := bankconnectutils.GetBCCreds(userID, sourceEntityID, bankConnectTransDetails.RequestIdentifier.String)
				if err != nil {
					logger.WithUser(userID).Warnln(err)
				}
				payloadStr += fmt.Sprintf(`,"entity_id": "%s", "account_id": "%s", "api_key": "%s","server_hash": "%s","bank_data": %s`, bankConnectTransDetails.EntityID, bankConnectTransDetails.AccountID, clientAPIKey, serverHash, bankConnectTransDetails.TransactionDataString)
				var accountIDs []string
				for _, bankDetails := range bankConnectDetails {
					accountIDs = append(accountIDs, bankDetails.AccountID)
				}
				payloadStr += `,"account_ids": ["` + strings.Join(accountIDs, `","`) + `"]`
			}
		}
	}
	if strings.Contains(ruleVersion, "_gst_") {
		// add gst json urls
		var jsonKeys []string
		query := `select coalesce(data_s3_key, '') as jsonkey from user_business_gst where user_id = $1 and status = $2`
		err := database.Select(&jsonKeys, query, userID, constants.GSTStatusCompleted)
		if err != nil {
			log.Println(err)
		}
		jsonKeys = general.RemoveEmptyElements(jsonKeys)
		for index, jsonKey := range jsonKeys {
			// add presigned urls in double quotes
			jsonKeys[index] = fmt.Sprintf(`"%s"`, s3.GetPresignedURLS3(jsonKey, 60))
		}
		payloadStr += fmt.Sprintf(`,"gst_karza_jsons": [%s]`, strings.Join(jsonKeys, ", "))
	}

	switch sourceEntityID {
	case constants.TataNexarcID:
		// add platform data
		var partnerDataString string
		partnerData := make(map[string]interface{})
		query := `SELECT coalesce(partner_data::TEXT, '') as partnerData from users where user_id = $1`
		err := database.Get(&partnerDataString, query, userID)
		if err != nil {
			log.Println(err)
		}
		var age int
		userData, err := users.Get(userID)
		if err == nil {
			dobObj, err := time.Parse("2006-01-02", userData.DOB)
			if err == nil {
				age = general.AgeAt(dobObj, time.Now())
			}
		}
		platformData := fmt.Sprintf(`"customer_age": %d`, age)
		if partnerDataString != "" {
			err = json.Unmarshal([]byte(partnerDataString), &partnerData)
			if err != nil {
				log.Println(err)
			} else {

				appScore, found := partnerData["appScore"].(float64)

				if found {
					// add app score if found
					platformData += fmt.Sprintf(`, "app_score": %.2f`, appScore)
				}
			}
		}
		payloadStr += fmt.Sprintf(`, "platform_data": {%s}`, platformData)
		//also append gst_data in case of Nexarc policies
		userBusinessResp, err := userbusiness.Get(context.Background(), userID)
		if err != nil {
			err = fmt.Errorf("error fetching userbusiness info for user_id:%s in underwriting, error:%s", userID, err.Error())
			logger.WithUser(userID).Errorln(err)
			errorHandler.ReportToSentryWithoutRequest(err)
		}
		gstResp, err := gst.GetGSTInfo(userID, []string{})
		if err != nil {
			err = fmt.Errorf("error fetching gst info for user_id: %s in underwriting, error: %s", userID, err.Error())
			logger.WithUser(userID).Errorln(err)
			errorHandler.ReportToSentryWithoutRequest(err)
		}
		var codes []string
		var hsnData []HSNData
		for i := 0; i < len(gstResp.HSNDetails); i++ {
			hsnData = append(hsnData, HSNData{
				HSNCode:     gstResp.HSNDetails[i].Code,
				Description: gstResp.HSNDetails[i].CodeDescription,
			})
			codes = append(codes, gstResp.HSNDetails[i].Code)
		}
		for i := 0; i < len(gstResp.SACDetails); i++ {
			codes = append(codes, gstResp.SACDetails[i].Code)
		}
		isNegativeIndustryFound := hsnsaccodes.IsHSNorSACBlackListed(codes, constants.TataNexarcID)
		var basicGSTData = BasicGSTData{
			Constitution:      userBusinessResp.Constitution,
			RegistrationDate:  userBusinessResp.DateOfIncorporation,
			GSTIN:             gstResp.GSTIN,
			Status:            gstResp.GSTStatus,
			NatureOfBusiness:  gstResp.Industry,
			HSNDetails:        hsnData,
			NegativeCodeFound: isNegativeIndustryFound,
		}
		gstDataBytes, _ := json.Marshal(basicGSTData)
		payloadStr += fmt.Sprintf(`, "gst_basic_data": %s`, string(gstDataBytes))
	case constants.NiyoID:
		// add platform data
		var partnerDataString string
		partnerData := make(map[string]interface{})
		query := `SELECT coalesce(partner_data::TEXT, '') as partnerData from users where user_id = $1`
		err := database.Get(&partnerDataString, query, userID)
		if err != nil {
			log.Println(err)
		}
		if partnerDataString != "" {
			err = json.Unmarshal([]byte(partnerDataString), &partnerData)
			if err != nil {
				log.Println(err)
			} else {
				platformIncome := partnerData["median_income_last_6_months"].(float64)
				payloadStr += fmt.Sprintf(`,"platform_data": {"income": %.2f}`, platformIncome)
			}
		}
	case constants.KhatabookID:
		// add platform data
		var partnerDataString string
		partnerData := make(map[string]interface{})
		query := `SELECT coalesce(partner_data::TEXT, '') as partnerData from users where user_id = $1`
		err := database.Get(&partnerDataString, query, userID)
		if err != nil {
			log.Println(err)
		}
		if partnerDataString != "" {
			err = json.Unmarshal([]byte(partnerDataString), &partnerData)
			if err != nil {
				log.Println(err)
			} else {
				vintageMonths, _ := partnerData["age_in_months"].(float64)
				month1Value, _ := partnerData["credit_amount_current_month1"].(float64)
				month2Value, _ := partnerData["credit_amount_current_month2"].(float64)
				month3Value, _ := partnerData["credit_amount_current_month3"].(float64)
				qrVolM1, _ := partnerData["qr_vol_m1"].(float64)
				qrVolM2, _ := partnerData["qr_vol_m2"].(float64)
				qrVolM3, _ := partnerData["qr_vol_m3"].(float64)
				maxEDI, _ := partnerData["max_edi"].(float64)
				maxTenure, _ := partnerData["max_tenure"].(float64)
				payloadStr += fmt.Sprintf(`,"platform_data": {"vintage_months": %.0f, "credit_amount_current_month1": %.2f, "credit_amount_current_month2": %.2f, "credit_amount_current_month3": %.2f, "qr_vol_m1": %.2f, "qr_vol_m2": %.2f, "qr_vol_m3": %.2f, "max_edi": %.2f, "max_tenure": %.0f`,
					vintageMonths, month1Value, month2Value, month3Value, qrVolM1, qrVolM2, qrVolM3, maxEDI, maxTenure)

				// Add metadata values
				metadataStr, _ := partnerData["metadata"].(string)
				if metadataStr != "" && len(metadataStr) > 1 {
					payloadStr += fmt.Sprintf(`,%s`, metadataStr[1:len(metadataStr)-1])
				}
				payloadStr += "}"
			}
		}
	case constants.BeldaraID:
		// add platform data
		var partnerDataString string
		partnerData := make(map[string]interface{})
		query := `SELECT coalesce(partner_data::TEXT, '') as partnerData from users where user_id = $1`
		err := database.Get(&partnerDataString, query, userID)
		if err != nil {
			log.Println(err)
		}
		if partnerDataString != "" {
			err = json.Unmarshal([]byte(partnerDataString), &partnerData)
			if err != nil {
				log.Println(err)
			} else {
				month1Value, _ := partnerData["month_1_value"].(float64)
				month2Value, _ := partnerData["month_2_value"].(float64)
				month3Value, _ := partnerData["month_3_value"].(float64)
				month4Value, _ := partnerData["month_4_value"].(float64)
				month5Value, _ := partnerData["month_5_value"].(float64)
				month6Value, _ := partnerData["month_6_value"].(float64)
				payloadStr += fmt.Sprintf(`,"platform_data": {"month_1_value": %.2f, "month_2_value": %.2f, "month_3_value": %.2f, "month_4_value": %.2f, "month_5_value": %.2f, "month_6_value": %.2f}`,
					month1Value, month2Value, month3Value, month4Value, month5Value, month6Value)
			}
		}
	case constants.VyaparID:
		// add platform data
		var partnerDataString string
		partnerData := make(map[string]interface{})
		query := `SELECT coalesce(partner_data::TEXT, '{}') as partnerData from users where user_id = $1`
		err := database.Get(&partnerDataString, query, userID)
		if err != nil {
			log.Println(err)
		}
		if general.InArr(ruleVersion, []string{constants.LendingKartVyaparPolicy, constants.LendingKartVyaparKnockOffPolicy}) {
			var age int
			userData, err := users.Get(userID)
			if err == nil {
				dobObj, err := time.Parse("2006-01-02", userData.DOB)
				if err == nil {
					age = general.AgeAt(dobObj, time.Now())
				}
			}
			platformData := fmt.Sprintf(`"customer_age": %d`, age)
			payloadStr += fmt.Sprintf(`, "platform_data": {%s}`, platformData)
		} else if partnerDataString != "{}" {
			err = json.Unmarshal([]byte(partnerDataString), &partnerData)
			if err != nil {
				log.Println(err)
			} else {
				customerType, _ := partnerData["subscription_status"].(string)
				month1Value, _ := partnerData["month_1_sale_amount"].(float64)
				month2Value, _ := partnerData["month_2_sale_amount"].(float64)
				month3Value, _ := partnerData["month_3_sale_amount"].(float64)
				month4Value, _ := partnerData["month_4_sale_amount"].(float64)
				month5Value, _ := partnerData["month_5_sale_amount"].(float64)
				month6Value, _ := partnerData["month_6_sale_amount"].(float64)
				month7Value, _ := partnerData["month_7_sale_amount"].(float64)
				month8Value, _ := partnerData["month_8_sale_amount"].(float64)
				joinDateStr, _ := partnerData["joining_date"].(string)
				joinDate, err := time.Parse("02/01/06", joinDateStr)
				if err != nil {
					log.Println(err)
				}
				vintageMonths := general.GetDifferenceInMonths(time.Now(), joinDate)

				payloadStr += fmt.Sprintf(`,"platform_data": {"month_1_value": %.2f, "month_2_value": %.2f, "month_3_value": %.2f, "month_4_value": %.2f, "month_5_value": %.2f, "month_6_value": %.2f, "month_7_value": %.2f, "month_8_value": %.2f, "vintage_months": %d, "customer_type": "%s"}`,
					month1Value, month2Value, month3Value, month4Value, month5Value, month6Value, month7Value, month8Value, vintageMonths, customerType)

			}
		}
	case constants.TataPLID:
		var partnerDataString string
		partnerData := partner.TataStruct{}
		query := `SELECT coalesce(partner_data::TEXT, '{}') as partnerData from users where user_id = $1`
		err := database.Get(&partnerDataString, query, userID)
		if err != nil {
			log.Errorln(err)
			errorHandler.ReportToSentryWithoutRequest(err)
		}
		err = json.Unmarshal([]byte(partnerDataString), &partnerData)
		if err != nil {
			log.Errorln(err)
			errorHandler.ReportToSentryWithoutRequest(err)
		} else {
			var bbmgclTxnValue, cromaTxnValue float64
			capBucket := "thin" // default set to thin
			if partnerData.Data.CustomerPartnerData.RiskVariables["BBMGCL_avg_permon_txnamt_L12M"] != nil {
				switch partnerData.Data.CustomerPartnerData.RiskVariables["BBMGCL_avg_permon_txnamt_L12M"].(type) {
				case float64:
					bbmgclTxnValue = partnerData.Data.CustomerPartnerData.RiskVariables["BBMGCL_avg_permon_txnamt_L12M"].(float64)
				case string:
					bbmgclTxnValue, _ = strconv.ParseFloat(partnerData.Data.CustomerPartnerData.RiskVariables["BBMGCL_avg_permon_txnamt_L12M"].(string), 64)
				}
			}
			if partnerData.Data.CustomerPartnerData.RiskVariables["Croma_avg_permon_txnamt_L12M"] != nil {
				switch partnerData.Data.CustomerPartnerData.RiskVariables["Croma_avg_permon_txnamt_L12M"].(type) {
				case float64:
					cromaTxnValue = partnerData.Data.CustomerPartnerData.RiskVariables["Croma_avg_permon_txnamt_L12M"].(float64)
				case string:
					cromaTxnValue, _ = strconv.ParseFloat(partnerData.Data.CustomerPartnerData.RiskVariables["Croma_avg_permon_txnamt_L12M"].(string), 64)
				}
			}
			if partnerData.Data.CustomerPartnerData.RiskVariables["tata_thick_flag_L12M"] != nil {
				if thickFlag := fmt.Sprintf("%v", partnerData.Data.CustomerPartnerData.RiskVariables["tata_thick_flag_L12M"]); thickFlag == "1" {
					capBucket = "thick"
				}
			}
			payloadStr += fmt.Sprintf(`,"platform_data":{"bbmgcl_avg_permon_txnamt_l12m": %.2f, "croma_avg_permon_txnamt_l12m": %.2f, "cap_bucket": "%s"}`, bbmgclTxnValue, cromaTxnValue, capBucket)
		}
	}

	// get assisted and sdk version for the user
	var assistedCount int
	query := `SELECT count(*) from assisted_journey where journey_id = $1`
	_ = database.Get(&assistedCount, query, userID)
	type userStruct struct {
		SDKVersion string
		CustomerID string
	}
	var userObj userStruct
	query = `SELECT coalesce(sdk_version, '') as sdkversion, unique_id as customerid from users where user_id = $1`
	_ = database.Get(&userObj, query, userID)

	waitForDC := journey.WaitForDC(userID, sourceEntityID, userObj.SDKVersion)
	// add device connect if not assisted journey
	deviceConnectObj := deviceConnect.DeviceConnectRespStruct{}
	if assistedCount == 0 {
		var deviceConnectDataString string
		var deviceConnectDataResponse string
		type deviceConnectDBStruct struct {
			ResponseData string
			Status       int
		}
		noXSellAttempted := false
		attempt := 0
		for attempt <= 120 {
			attempt++
			log.Println(userID, attempt)
			var dcDBObj deviceConnectDBStruct
			query := `select coalesce(d.device_connect_data::TEXT, '') as responsedata, d.status
					from device_connect_details d
				where
					d.user_id = $1
				order by d.created_at desc limit 1
				`
			err := database.Get(&dcDBObj, query, userID)
			if err != nil {
				log.Println(err)
				// for not found cases max attempt is 65
				if attempt > 65 || !waitForDC {
					break
				}
				time.Sleep(time.Second)
				continue
			}
			if dcDBObj.Status == constants.DeviceConnectStatusCompleted {
				deviceConnectDataResponse = dcDBObj.ResponseData
				if deviceConnectDataResponse != "" {
					err := json.Unmarshal([]byte(deviceConnectDataResponse), &deviceConnectObj)
					if err != nil {
						log.Println(err)
						break // break as not able to parse
					}
					// check processed time
					processedDateStr := strings.Split(deviceConnectObj.Date_processed, ".")[0]
					processedDate, err := time.Parse("2006-01-02T15:04:05", processedDateStr)
					if err != nil {
						log.Println(err)
					} else {
						// if processed date present
						diffHours := time.Since(processedDate).Hours()
						if diffHours > float64(journey.DeviceConnectThreshold(sourceEntityID)) {
							// re-request device connect predictors if > 48 hours passed since last processed
							deviceConnect.FetchRisk(userID, userObj.CustomerID, sourceEntityID)
							continue // reiterate loop
						}
					}
					if len(deviceConnectObj.Data) > 0 {
						// get score_xsell
						_, fetchFIS := journey.GetPredictorsVersion(sourceEntityID, userObj.SDKVersion)
						if fetchFIS && !noXSellAttempted {
							var fisScore float64
							for _, predictor := range deviceConnectObj.Data {
								if predictor.Name == "score_xsell" {
									tempScore, notNull := predictor.Value.(float64)
									if notNull {
										fisScore = tempScore
									}
									break
								}
							}
							if fisScore == 0 {
								// re-request if fis score not present
								deviceConnect.FetchRisk(userID, userObj.CustomerID, sourceEntityID)
								noXSellAttempted = true // to not end up in loop if no score_xsell ever
								continue                // reiterate loop
							}
						}
						byteData, _ := json.Marshal(deviceConnectObj.Data)
						deviceConnectDataString = string(byteData)
						if deviceConnectDataString != "" {
							payloadStr += fmt.Sprintf(`,"device_connect_data": %s`, deviceConnectDataString)
						}
					}
				}
				break // break since got the device connect data
			}
			if dcDBObj.Status != constants.DeviceConnectStatusInProgress {
				// break if status is failed or completed
				break
			}
			if !waitForDC {
				break
			}
			time.Sleep(1 * time.Second)
		}
	}

	payloadStr += "}"

	gobj["strReq"] = payloadStr
	payload := strings.NewReader(payloadStr)
	serviceName := "decision_dump"
	body := make([]byte, 0)
	err := retry.CustomRetry(3, 1000*time.Millisecond, func() error {
		body = make([]byte, 0)

		client := tracer.GetTraceableHTTPClientV2(nil, serviceName, sourceEntityID)
		req, err := requestutils.GetMockableHTTPRequest(userID, serviceName, method, url, payload)
		if err != nil {
			log.Println(err)
			return err
		}
		req.Header.Add("x-api-key", apiKey)
		req.Header.Add("Content-Type", "application/json")

		res, err := client.Do(req)
		if err != nil {
			log.Println(err)
			return err
		}
		defer res.Body.Close()

		body, err = io.ReadAll(res.Body)
		if err != nil {
			log.Println(err)
			return err
		}
		return nil
	})
	gobj["strRes"] = string(body)

	if err != nil {
		log.Println(err)
		go serviceslib.WriteToDB(serviceName, gobj["strReq"], gobj["strRes"], 0, gobj["userID"], gobj["url"], err.Error(), gobj["id"])
		return "", false, deviceConnectObj
	}

	var firstResp ruleEngineDumpResponse
	err = json.Unmarshal(body, &firstResp)
	if err != nil {
		log.Println(err)
		go serviceslib.WriteToDB(serviceName, gobj["strReq"], gobj["strRes"], 0, gobj["userID"], gobj["url"], err.Error(), gobj["id"])
		return "", false, deviceConnectObj
	}
	referenceID := firstResp.Data.ReferenceID
	if referenceID == "" {
		log.Println("Reference id not got for", userID)
		go serviceslib.WriteToDB(serviceName, gobj["strReq"], gobj["strRes"], 0, gobj["userID"], gobj["url"], "Couldn't get reference ID", gobj["id"])
		return "", false, deviceConnectObj
	}
	go serviceslib.WriteToDB(serviceName, gobj["strReq"], gobj["strRes"], 1, gobj["userID"], gobj["url"], "", gobj["id"])
	return referenceID, true, deviceConnectObj
}

type RuleStruct struct {
	Decision string `json:"decision"`
	Details  string `json:"details"`
	Rule     string `json:"rule"`
	Value    string `json:"value"`
}

type DecisionDataStruct struct {
	RequestStatus     int                    `json:"request_status"`
	Amount            float64                `json:"amount"`
	Decision          string                 `json:"decision"`
	Interest          float64                `json:"interest"`
	MaxTenure         int                    `json:"max_tenure"`
	ProcessingFee     float64                `json:"processing_fee"`
	ProcessingFeeType string                 `json:"processing_fee_type"`
	RiskBucket        string                 `json:"risk_bucket"`
	Rules             []RuleStruct           `json:"rules"`
	MaxEMI            float64                `json:"max_emi"`
	OutputVariables   map[string]interface{} `json:"output"`
	Income            float64                `json:"income"`
	MinTenure         int                    `json:"min_tenure"`
}
type DecisionRunStruct struct {
	ReferenceID string             `json:"referenceID"`
	Data        DecisionDataStruct `json:"data"`
	Error       string             `json:"error"`
}

// SentinelRunStruct is the new struct as unified is using DecisionRunStruct and it can have error of type int
type SentinelRunStruct struct {
	DecisionRunStruct
	Progress int `json:"progress"`
}

type SentinelWorkflowRunStruct struct {
	Data struct {
		CompoundResult string `json:"compoundResult"`
		Policies       []struct {
			PolicyVersion string
			Decide        DecisionDataStruct
		} `json:"policies"`
	} `json:"data"`
	Progress int    `json:"progress"`
	Error    string `json:"error"`
}

type SentinelReqStruct struct {
	ReferenceID   string                 `json:"reference_id"`
	PolicyVersion string                 `json:"policy_version"`
	UserID        string                 `json:"user_id"`
	DryRun        bool                   `json:"dryrun"`
	Source        string                 `json:"source"`
	Additional    map[string]interface{} `json:"additional"`
}

func storeUnderwritingData(referenceID string, userID string, sourceEntityID string, lenderID string, ruleVersion string, firstResp DecisionRunStruct) {
	tx, _ := database.Begin()
	// insert underwriting rule response
	query := `insert into decision_engine_response (reference_id, user_id, rule_version, created_at,
				amount, decision, interest, max_tenure, processing_fee, processing_fee_type, risk_bucket,
				lender_id, output_variables, dump_id, evaluation_id)
			VALUES ($1, $2, $3, NOW(), $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
			`
	outputVariables := ""
	bytesArr, err := json.Marshal(firstResp.Data.OutputVariables)
	if err != nil {
		log.Println(err)
	} else {
		outputVariables = string(bytesArr)
	}
	_, err = tx.Exec(query, referenceID, userID, ruleVersion, firstResp.Data.Amount, firstResp.Data.Decision,
		firstResp.Data.Interest, firstResp.Data.MaxTenure, firstResp.Data.ProcessingFee,
		firstResp.Data.ProcessingFeeType, firstResp.Data.RiskBucket, lenderID, outputVariables, referenceID, referenceID)
	if err != nil {
		log.Println(err)
		tx.Rollback()
		errorHandler.ReportToSentryWithoutRequest(err)
		return
	}
	// insert rule data now
	query = `insert into decision_engine_rules (reference_id, rule_version, rule_no, rule, value, details, decision, created_at)
				VALUES ($1, $2, $3, $4, $5, $6, $7, now())`
	for index, rule := range firstResp.Data.Rules {
		_, err = tx.Exec(query, referenceID, ruleVersion, index+1, rule.Rule, rule.Value, rule.Details, rule.Decision)
		if err != nil {
			log.Println(err)
			tx.Rollback()
			errorHandler.ReportToSentryWithoutRequest(err)
			return
		}
	}
	tx.Commit()
}

// CallRuleEngine invokes the rule engine and returns decision, whether rule engine was called successfully, reject reason (if any) and decision engine response
// Deprecated: Use CallRuleEngineV2 instead.
func CallRuleEngine(userID string, sourceEntityID string, lenderID string, ruleVersion string) (string, bool, string, DecisionRunStruct) {
	var unifiedData DecisionRunStruct
	var sentinelData SentinelRunStruct
	//default Progress -1, so that if there is any progress passed by sentinel then the progress won't be 0 and it won't retry
	sentinelData.Progress = DefaultProgress

	ageDeviation, err := deviations.Present(userID, "", deviations.TypeAge)
	if err != nil {
		logger.WithUser(userID).Error(err)
		return "", false, "", unifiedData
	}

	var uniqueID = ""
	uniqueIDQuery := `select unique_id from users where user_id  = $1`
	err = database.Get(&uniqueID, uniqueIDQuery, userID)
	if err != nil {
		log.Println(err)
	}

	programName, _ := sourceentity.GetProgramByID(sourceEntityID)
	sentinelAdditional := make(map[string]interface{})

	// add check on age if lender is IIFL
	switch lenderID {
	case constants.IIFLID:
		type dbStruct struct {
			DOB string `db:"dob"`
			PAN string `db:"pan"`
		}
		var dbObj dbStruct
		var err error
		var query string
		query = `SELECT coalesce(to_char(dob, 'YYYY-MM-DD'), '') as dob, pan from users where user_id = $1`
		err = database.Get(&dbObj, query, userID)
		if err != nil {
			logger.WithUser(userID).Errorln(err)
			errorHandler.ReportToSentryWithoutRequest(err)
		}
		dobObj, _ := time.Parse("2006-01-02", dbObj.DOB)
		age := general.AgeAt(dobObj, time.Now())
		sentinelAdditional["age"] = age
		creditCardVersions := []string{"iifl_bank_pl_creditcard_surrogate_v1.2", "iifl_bl_bank_creditcard_surrogate_v3.4"}
		hlLapVersions := []string{"iifl_pl_bank_hl_lap_al_v1"}
		selfEmployedVersions := []string{"iifl_self_employed_v2.4.4", "iifl_self_employed_v2.4.4_a", "iifl_self_employed_v2.4.4_b"}

		if general.InArr(ruleVersion, creditCardVersions) || general.InArr(ruleVersion, hlLapVersions) || general.InArr(ruleVersion, selfEmployedVersions) {
			sentinelAdditional["panNumber"] = dbObj.PAN
		}
		if general.InArr(ruleVersion, constants.MCACapBankPolicies) {
			sentinelAdditional["IsGst"] = "N"
		}
		if sourceEntityID == constants.TataNexarcID {
			gstObj, err := userbusinessgst.GetGSTInfo(userID, "", []string{})
			if err != nil {
				logger.WithUser(userID).Errorln(err)
			} else {
				isGstFromPan := false
				if gstObj.GSTIN != "" {
					isGstFromPan = true
				}
				sentinelAdditional["isGstFromPan"] = isGstFromPan
			}
		}

		if dbObj.DOB != "" && !ageDeviation {
			// TODO: add program id based loan type in future
			loanType, _, _ := journey.GetLoanType(sourceEntityID)
			var check bool
			var reason string
			switch loanType {
			case constants.LoanTypeBusinessLoan:
				check, reason = ageLimitCheck(lenderID, constants.IIFLBLID, dbObj.DOB)
			default:
				check, reason = ageLimitCheck(lenderID, constants.IIFLID, dbObj.DOB)
			}
			if !check {
				return constants.UnderwritingDecisionReject, true, reason, unifiedData
			}
		}

		bankDetailsObj, err := bankconnectdetails.GetLatest(userID, constants.BankConnectStatusCompleted)
		if err != nil {
			logger.WithUser(userID).Warnln(err)
		}
		switch bankDetailsObj.PerfiosStatementStatus {
		case constants.PerfiosStatusFraud:
			return constants.UnderwritingDecisionReject, true, "statement status is: " + bankDetailsObj.PerfiosStatementStatus, unifiedData
		case constants.PerfiosStatusRefer:
			// TODO: add support for program id here later
			loanType, _, _ := journey.GetLoanType(sourceEntityID)
			if loanType == constants.LoanTypePersonalLoan {
				return constants.UnderwritingDecisionReject, true, "statement status is: " + bankDetailsObj.PerfiosStatementStatus, unifiedData
			}
		}

	case constants.TrustLenderID, constants.ArthanLenderID:
		var dob string
		query := `SELECT coalesce(to_char(dob, 'YYYY-MM-DD'), '') from users where user_id = $1`
		err := database.Get(&dob, query, userID)
		if err != nil {
			log.Println(err)
		} else if dob != "" && !ageDeviation {
			check, reason := ageLimitCheck(lenderID, "", dob)
			// Reject on age restriction for non under review users
			if !check && !constants.UnderReviewItems[sourceEntityID]["age18_65"] {
				return constants.UnderwritingDecisionReject, true, reason, unifiedData
			}
		}
	}

	// add check on employment type for gimbooks PL
	if sourceEntityID == constants.GimBooksID {
		var preLoanDataStr string
		query := `SELECT coalesce(pre_loan_data::TEXT, '') as preloandatastr from user_loan_details where user_id = $1 order by created_at desc limit 1`
		err := database.Get(&preLoanDataStr, query, userID)
		if err != nil {
			log.Println(err)
		} else if preLoanDataStr != "" {
			var preLoanData map[string]interface{}
			err := json.Unmarshal([]byte(preLoanDataStr), &preLoanData)
			if err != nil {
				log.Println(err)
			} else {
				employmentType := preLoanData["employmentType"]
				if employmentType == "Salaried" {
					return constants.UnderwritingDecisionReject, true, "employment type is salaried", unifiedData
				}
			}
		}
	}

	switch {
	case journey.IsHousingLoanTap(userID, sourceEntityID):
		userObj, _ := users.Get(userID)
		dobObj, _ := time.Parse("2006-01-02", userObj.DOB)
		sentinelAdditional = map[string]interface{}{
			"age": general.AgeAt(dobObj, time.Now()),
		}
	case sourceEntityID == constants.TataPLID:
		userObj, _ := users.Get(userID)
		if strings.Contains(ruleVersion, "compound") { // bureau policy
			userDeclaredIncome, _ := users.GetMonthlyIncome(userID)
			var preApprovedOffer preapproved.PreApprovalStruct
			partnerPreApproved, _ := usersutil.GetPreApprovedOffer(userObj.PAN, userObj.Mobile, "", sourceEntityID, "")
			if partnerPreApproved.LenderID == lenderID {
				preApprovedOffer = partnerPreApproved
			}
			sentinelAdditional = map[string]interface{}{
				"amount":             preApprovedOffer.Amount,
				"tenure":             preApprovedOffer.Tenure,
				"interest":           preApprovedOffer.Interest,
				"processingFee":      preApprovedOffer.ProcessingFee,
				"processingFeeType":  preApprovedOffer.ProcessingFeeType,
				"userDeclaredIncome": userDeclaredIncome,
			}

			if preApprovedOffer.AdditionalParameters["income"] != nil {
				imputedIncome, _ := strconv.ParseFloat(preApprovedOffer.AdditionalParameters["income"].(string), 64)
				lenderVariablesNullable := lendervariables.LenderVariablesStructNullable{
					UserID:     userID,
					LenderID:   lenderID,
					Income:     sql.NullFloat64{Valid: imputedIncome > 0, Float64: imputedIncome},
					Obligation: sql.NullFloat64{Valid: false, Float64: 0.0},
				}
				err = lendervariables.Insert(nil, lenderVariablesNullable)
				if err != nil {
					logger.WithUser(userID).Error(err)
				}
			}
		} else { // banking / booster policies
			dobObj, _ := time.Parse("2006-01-02", userObj.DOB)
			loanOffer, _ := personalloanoffer.GetByLenderAndOfferType(userID, lenderID, constants.OfferTypeTentative)
			sentinelAdditional = map[string]interface{}{
				"amount":            loanOffer.Amount,
				"tenure":            loanOffer.Tenure,
				"interest":          loanOffer.Interest,
				"processingFee":     loanOffer.ProcessingFee,
				"processingFeeType": loanOffer.ProcessingFeeType,
				"age":               general.AgeAt(dobObj, time.Now()),
				"panNumber":         userObj.PAN,
			}
		}
	case journey.IsOneMuthootPartner(sourceEntityID):
		var partnerDataString string
		var partnerData map[string]interface{}
		query := `SELECT coalesce(partner_data::TEXT, '') as partnerData from users where user_id = $1`
		err := database.Get(&partnerDataString, query, userID)
		if err != nil {
			logger.WithUser(userID).Error("partner data not found")
		}
		if partnerDataString != "" {
			err = json.Unmarshal([]byte(partnerDataString), &partnerData)
			if err != nil {
				log.Println(err)
			}
		} else {
			logger.WithUser(userID).Error("partner data not found")
		}
		partnerData["bureau_name"] = "EQUIFAX"

		var userObj struct {
			DOB                   string
			CompanyName           string `json:"companyName"`
			CompanyStabilityScore int    `json:"companyStabilityScore"`
		}
		query = `SELECT coalesce(to_char(dob, 'YYYY-MM-DD'), '') as dob,
		dynamic_user_info::jsonb->>'companyName' as companyName,
		partner_data::jsonb->>'company_stability_score' as companyStabilityScore
		from users where user_id = $1`

		err = database.Get(&userObj, query, userID)
		if err != nil {
			logger.WithUser(userID).Error("company data not found")
		} else if userObj.DOB != "" {
			dobObj, _ := time.Parse("2006-01-02", userObj.DOB)
			age := general.AgeAt(dobObj, time.Now())
			partnerData["customer_age_om"] = age
		}
		partnerData["company_stability_score"] = userObj.CompanyStabilityScore
		partnerData["company_category"] = "C"
		if strings.ToLower(userObj.CompanyName) != "" {
			category, err := companydetails.GetCategory(userObj.CompanyName, sourceEntityID)
			if err == nil {
				partnerData["company_category"] = strings.TrimSpace(category)
			}
		}
		sentinelAdditional = partnerData
	case lenderID == constants.EcofyID:
		var amount string
		query := `SELECT coalesce(dynamic_user_info::jsonb->>'loanAmount', '') as amount
		from users where user_id = $1`
		err = database.Get(&amount, query, userID)
		if err != nil {
			logger.WithUser(userID).Warnln("loan amount not found")
		}
		sentinelAdditional["loanAmount"] = amount
	}

	if ruleVersion == constants.AutomationPolicy || ruleVersion == constants.IIFLMca2LakhPolicy {
		if journey.IsOfferFlowV2(userID, sourceEntityID) {
			switch programName {
			case constants.ProgramPersonalLoan:
				latestOffer, err := personalloanoffer.GetByLenderAndOfferType(userID, lenderID, constants.OfferTypeOverridden)
				if err != nil {
					errorHandler.ReportToSentryWithoutRequest(err)
					return "", false, "", sentinelData.DecisionRunStruct
				}
				sentinelAdditional = map[string]interface{}{
					"amount":            latestOffer.Amount,
					"tenure":            latestOffer.Tenure,
					"interest":          latestOffer.Interest,
					"processingFee":     latestOffer.ProcessingFee,
					"processingFeeType": latestOffer.ProcessingFeeType,
					"maxEMI":            latestOffer.MaxEMI,
				}
			case constants.ProgramBusinessLoan:
				latestOffer, err := businessloanoffer.GetByLenderAndOfferType(userID, lenderID, constants.OfferTypeOverridden)
				if err != nil {
					errorHandler.ReportToSentryWithoutRequest(err)
					return "", false, "", sentinelData.DecisionRunStruct
				}
				sentinelAdditional = map[string]interface{}{
					"amount":            latestOffer.Amount,
					"tenure":            latestOffer.Tenure,
					"interest":          latestOffer.Interest,
					"processingFee":     latestOffer.ProcessingFee,
					"processingFeeType": latestOffer.ProcessingFeeType,
					"maxEMI":            latestOffer.MaxEMI,
				}
			default:
				err = fmt.Errorf("unhandled programName passed : %s, userID: %s, sourceEntityID: %s", programName, userID, sourceEntityID)
				errorHandler.ReportToSentryWithoutRequest(err)
				return "", false, "", sentinelData.DecisionRunStruct
			}
		} else {
			latestOffer, err := usereligibility.GetLatest(userID) // TODO: Handle fetching previous offer from personal_loan_offer/business_loan_offer as well to cover TDL & Nexarc.
			if err != nil {
				errorHandler.ReportToSentryWithoutRequest(err)
				return "", false, "", sentinelData.DecisionRunStruct
			}
			sentinelAdditional = map[string]interface{}{
				"amount":            latestOffer.EligibleAmount,
				"tenure":            latestOffer.Tenure,
				"interest":          latestOffer.Interest,
				"processingFee":     latestOffer.ProcessingFee,
				"processingFeeType": latestOffer.ProcessingFeeType,
				"maxEMI":            latestOffer.MaxEMI,
			}
		}
	}

	nexarcPolicies := constants.NexarcKotakBankPolicies
	nexarcPolicies = append(nexarcPolicies, constants.NexarcTCapBankPolicies...)
	nexarcPolicies = append(nexarcPolicies, constants.LendingKartPolicy)
	nexarcPolicies = append(nexarcPolicies, []string{"nexarc_abfl_booster_v1", "nexarc_abfl_knockoff_v1"}...)
	if general.InArr(ruleVersion, nexarcPolicies) {
		userBusinessResp, err := userbusiness.Get(context.Background(), userID)
		if err != nil {
			logger.WithUser(userID).Errorln("Error getting user_business row from DB:", err)
		} else {
			sentinelAdditional["Business"] = strings.ToLower(general.RemoveExtraSpaces(userBusinessResp.OwnershipType.String))
			sentinelAdditional["Residential"] = strings.ToLower(general.RemoveExtraSpaces(userBusinessResp.ResidenceOwnershipType.String))
		}
	}

	// add approval rule engine limiter
	addLimiter, maxCountDay := journey.CheckForDailyApprovalLimiter(sourceEntityID)
	if addLimiter {
		// get count of approvals for the day
		var count int
		query := `select count(distinct d.user_id) from decision_engine_response d join users u on u.user_id = d.user_id
		where decision = $1 and source_entity_id = $2
		and DATE(d.created_at at time zone 'utc' at time zone 'Asia/Calcutta') = DATE(NOW() at time zone 'Asia/Calcutta')`
		err := database.Get(&count, query, constants.UnderwritingDecisionPass, sourceEntityID)
		if err != nil {
			log.Println(err)
		}
		if count > maxCountDay {
			return constants.UnderwritingDecisionReject, true, "daily approval limit crossed", unifiedData
		}
	}

	baseURL := conf.DecisionConf["baseURL"]
	apiKey := conf.DecisionConf["apiKey"]

	sentinelConf := conf.GetSentinelCreds(lenderID, sourceEntityID)
	sentinelBaseURL := sentinelConf["baseURL"]
	sentinelAPIKey := sentinelConf["apiKey"]

	// dump user data get the reference id
	referenceID, success, _ := GetDumpID(baseURL, apiKey, userID, uniqueID, sourceEntityID, lenderID, ruleVersion)
	if !success {
		log.Println("reference id not found")
		return constants.UnderwritingDecisionCantDecide, false, "", unifiedData
	}
	partnerCode, _ := journey.GetPartnerCode(userID)

	sentinelURL := fmt.Sprintf("%v/v1/sentinel/decide", sentinelBaseURL)
	unifiedURL := fmt.Sprintf("%s/v1/decision/run?reference_id=%s&partner_code=%s&rule_version=%s", baseURL, referenceID, partnerCode, ruleVersion)

	var unifiedBody []byte
	var sentinelBody []byte

	sentinelAdditional["partnerCode"] = partnerCode
	sentinelReqBody := SentinelReqStruct{ReferenceID: referenceID, PolicyVersion: ruleVersion, UserID: uniqueID, DryRun: false, Source: "embedded_finance", Additional: sentinelAdditional}

	takeDecisionFromSentinel, policyType := journey.TakeDecisionFromSentinel(ruleVersion)
	finalDecisionData := DecisionRunStruct{}
	decisionWorkflowData := SentinelWorkflowRunStruct{}
	var errString string

	if takeDecisionFromSentinel && policyType != constants.PolicyTypeWorkflow {
		if uniqueID == "" {
			//uniqueID shouldn't be empty
			logger.WithUser(userID).Errorln(constants.EmptyUniqueIDError)
			errorHandler.ReportToSentryWithoutRequest(fmt.Errorf(constants.EmptyUniqueIDError))
			return "", false, "", sentinelData.DecisionRunStruct
		}
		//Todo rn it will exit the sentinel and in future after removing unified return error
		if sentinelAPIKey == "" {
			logger.WithUser(userID).Errorln(constants.EmptySentinelAPIKeyError)
			errorHandler.ReportToSentryWithoutRequest(fmt.Errorf(constants.EmptySentinelAPIKeyError))
			return "", false, "", sentinelData.DecisionRunStruct
		}

		currentTry := 0
		sentinelError := retry.CustomRetry(maxTry, 3*time.Second, func() error {
			sentinelData = SentinelRunStruct{}
			sentinelBody = []byte{}
			currentTry++
			log.Debugln("sentinel try -", currentTry)
			sentinelClient := tracer.GetTraceableHTTPClientV2(nil, sentinelServiceName, sourceEntityID)
			payload := new(bytes.Buffer)
			if err = json.NewEncoder(payload).Encode(sentinelReqBody); err != nil {
				logger.WithUser(userID).Error(err)
				return err
			}
			sentinelReq, err := requestutils.GetMockableHTTPRequest(userID, sentinelServiceName, "POST", sentinelURL, payload)
			if err != nil {
				logger.WithUser(userID).Error(err)
				return err
			}

			sentinelReq.Header.Add("x-api-key", sentinelAPIKey)
			sentinelRes, err := sentinelClient.Do(sentinelReq)
			if err != nil {
				logger.WithUser(userID).Error(err)
				return err
			}
			if sentinelRes.StatusCode >= 400 && sentinelRes.StatusCode < 500 {
				bodyBytes, _ := io.ReadAll(sentinelRes.Body)
				err := fmt.Errorf("client side error with status code %d reason: %s", sentinelRes.StatusCode, string(bodyBytes))
				return retry.NewStop(err.Error())
			}
			if sentinelRes.StatusCode >= 500 && sentinelRes.StatusCode < 600 {
				err := fmt.Errorf("internal server error with status code %d", sentinelRes.StatusCode)
				logger.WithUser(userID).Error(err)
				return err
			}

			defer func() {
				if err := sentinelRes.Body.Close(); err != nil {
					logger.WithUser(userID).Error(err)
				}
			}()

			sentinelBody, err = io.ReadAll(sentinelRes.Body)
			if err != nil {
				logger.WithUser(userID).Error(err)
				return err
			}
			err = json.Unmarshal(sentinelBody, &sentinelData)
			if err != nil {
				logger.WithUser(userID).Error(err)
				errorHandler.ReportToSentryWithoutRequest(err)
				return err
			}

			if sentinelData.Progress == Progressing {
				logger.WithUser(userID).Warnln("received progressing for referenceID: ", referenceID)
				return fmt.Errorf("retrying")
			}

			return nil
		})

		logError := sentinelData.Error
		if sentinelError != nil && sentinelData.Error != "" { //both have error
			logError = fmt.Sprintf("%s:%s", sentinelError.Error(), sentinelData.Error)
		} else if sentinelError != nil { //only sentinelError has err
			logError = sentinelError.Error()
		}

		requestBody, _ := json.Marshal(sentinelReqBody)

		if logError != "" {
			go serviceslib.WriteToDB(sentinelServiceName, string(requestBody), string(sentinelBody), serviceslib.ErrorStatusCode, userID, sentinelURL, logError, general.GetUUID())
			return "", false, "", sentinelData.DecisionRunStruct
		} else {
			go serviceslib.WriteToDB(sentinelServiceName, string(requestBody), string(sentinelBody), serviceslib.SuccessStatusCode, userID, sentinelURL, "", general.GetUUID())
			finalDecisionData = sentinelData.DecisionRunStruct
		}

	} else if takeDecisionFromSentinel { // sentinel compound / workflow policies
		logger.WithUser(userID).Infoln("calling sentinel workflow")
		decisionWorkflowData, errString = CallSentinelWorkflow(userID, ruleVersion, uniqueID, sourceEntityID, referenceID, lenderID, sentinelAdditional)
		if errString != "" {
			logger.WithUser(userID).Errorln(errString)
			return constants.UnderwritingDecisionReject, true, "sentinel workflow failed", finalDecisionData
		}
	} else {
		//take decision from unified only if this is false
		currentTry := 0
		err = retry.CustomRetry(maxTry, 3*time.Second, func() error {
			unifiedData = DecisionRunStruct{}
			unifiedBody = []byte{}
			currentTry++
			log.Debugln("unified try -", currentTry)
			client := tracer.GetTraceableHTTPClientV2(nil, "decision_run", sourceEntityID)
			req, err := requestutils.GetMockableHTTPRequest(userID, "decision_run", "GET", unifiedURL, nil)
			if err != nil {
				log.Println(err)
				return err
			}
			req.Header.Add("x-api-key", apiKey)
			res, err := client.Do(req)
			if err != nil {
				log.Println(err)
				return err
			}
			defer res.Body.Close()
			unifiedBody, err = io.ReadAll(res.Body)
			if err != nil {
				log.Println(err)
				return err
			}
			err = json.Unmarshal(unifiedBody, &unifiedData)
			if err != nil {
				log.Println(err)
				return err
			}
			unifiedData.ReferenceID = referenceID
			if unifiedData.Data.RequestStatus == 0 || unifiedData.Data.Decision == "" {
				// retry if request progress not done or decision not taken
				log.Println("retrying, request status is 0", referenceID)
				return errors.New("retrying")
			}
			return nil
		})

		if err != nil {
			logger.WithUser(userID).Error(err)
			go serviceslib.WriteToDB("decision_run", "", string(unifiedBody), serviceslib.ErrorStatusCode, userID, unifiedURL, err.Error(), general.GetUUID())
			return "", false, "", unifiedData
		} else {
			go serviceslib.WriteToDB("decision_run", "", string(unifiedBody), serviceslib.SuccessStatusCode, userID, unifiedURL, "", general.GetUUID())
		}

		finalDecisionData = unifiedData
	}

	var rejectReason, decision string
	if policyType == constants.PolicyTypeWorkflow {
		for index, decisionData := range decisionWorkflowData.Data.Policies {
			finalDecisionData = DecisionRunStruct{
				ReferenceID: referenceID,
				Data:        decisionData.Decide,
			}
			dummyReferenceID := general.GetUUID()                   // since reference key is a primary key duplicate reference keys cannot be added even though all policies have run on same reference key
			if index == len(decisionWorkflowData.Data.Policies)-1 { //if last copy actual referenceID
				dummyReferenceID = referenceID
			}
			storeUnderwritingData(dummyReferenceID, userID, sourceEntityID, lenderID, decisionData.PolicyVersion, finalDecisionData)
		}
		decision = general.Coalesce(decisionWorkflowData.Data.CompoundResult, constants.UnderwritingDecisionReject)
		for _, rule := range finalDecisionData.Data.Rules { // this is assuming the objects in the list are in the order of run
			if rule.Decision == FailDecision || rule.Decision == FailSentinelDecision {
				rejectReason = "rule failed: " + rule.Rule
				break
			}
		}
		finalDecisionData.ReferenceID = referenceID
	} else {
		storeUnderwritingData(referenceID, userID, sourceEntityID, lenderID, ruleVersion, finalDecisionData)
		decision = finalDecisionData.Data.Decision
		for _, rule := range finalDecisionData.Data.Rules {
			if rule.Decision == FailDecision || rule.Decision == FailSentinelDecision {
				rejectReason = "rule failed: " + rule.Rule
				break
			}
		}
		finalDecisionData.ReferenceID = referenceID
	}
	return decision, true, rejectReason, finalDecisionData
}

// GetCIBILReportString returns JSON response string, rawXML response string & source
func GetCIBILReportString(userID string, sourceEntityID string) (string, string, string, string) {
	reportData := map[string]interface{}{
		"status":   constants.BureauStatusNotFound,
		"error":    "",
		"data":     "",
		"reportID": "",
	}
	type reportStruct struct {
		Status   string
		Response string
		ReportID string
		Source   string
		Score    int
	}
	var reportObj reportStruct
	query := `SELECT status, coalesce(response, '') as response, report_id as reportid, coalesce(score, -1) as score, coalesce(source, '') as source
				from cibil_reports where user_id = $1 and source NOT IN ($2, $3)
				order by created_at desc limit 1`
	err := database.Get(&reportObj, query, userID, constants.CIBILReportSourceIIFLHardPull, constants.CommercialCIBILReportSourceABFL)
	if err != nil {
		log.Println(err)
	} else {
		reportData["status"] = reportObj.Status
		reportData["data"] = reportObj.Response
		reportData["reportID"] = reportObj.ReportID
		if reportObj.Status == constants.BureauStatusFailed || reportObj.Status == constants.BureauStatusFailedBypassed {
			reportData["error"] = constants.BureauFailMessage
		}
	}
	if reportData["status"] == constants.BureauStatusCompleted && general.InArr(reportObj.Source, []string{constants.CIBILReportSourceMuthoot, constants.CIBILReportSourceTransUnion, constants.CIBILReportSourceABFLHardPull, constants.CIBILReportSourceMuthootV2, constants.CIBILReportSourceTransUnionV2}) {
		if reportObj.Score < journey.GetBureauThreshold(sourceEntityID) {
			return "[]", "", reportObj.Source, reportObj.ReportID
		}
		return reportObj.Response, "", reportObj.Source, reportObj.ReportID
	}
	if reportData["status"] == constants.BureauStatusCompleted {
		xmlContent, _ := reportData["data"].(string)
		xmlContent = strings.ReplaceAll(xmlContent, `\"`, `"`)
		var jsonObj structs.CIBILDataStruct
		err := general.UnmarshalXML([]byte(xmlContent), &jsonObj)
		if err != nil {
			log.Println(err)
			return "{}", xmlContent, reportObj.Source, reportObj.ReportID
		}

		// Handle thin history cases by not considering
		// Exoerian will be used for them
		bureauScore, _ := strconv.Atoi(jsonObj.GetCustomerAssetsSuccess.Asset.TrueLinkCreditReport.Borrower.CreditScore.RiskScore)
		if bureauScore < journey.GetBureauThreshold(sourceEntityID) {
			return "{}", xmlContent, reportObj.Source, reportObj.ReportID
		}

		jsonBytes, err := json.Marshal(jsonObj)
		if err != nil {
			log.Println(err)
			return "{}", xmlContent, reportObj.Source, reportObj.ReportID
		}
		return string(jsonBytes), xmlContent, reportObj.Source, reportObj.ReportID
	}
	return "{}", "", reportObj.Source, reportObj.ReportID
}

// GetRejectReasonFromRuleEngine returns a string indicating the reject reason based on rule engine
func GetRejectReasonFromRuleEngine(userID string) string {
	type ruleStruct struct {
		Details string
		Rule    string
	}
	var ruleObj ruleStruct
	query := `select details, rule from decision_engine_response d join decision_engine_rules r on d.reference_id = r.reference_id and user_id = $1
		and r.decision = 'FAIL' order by d.created_at desc, rule_no asc limit 1`
	err := database.Get(&ruleObj, query, userID)
	if err != nil {
		log.Println(err)
		return ""
	}
	if ruleObj.Details == "Bank Statement" {
		return "Sorry, you don't meet our banking eligibility. Please try again later after improved banking history"
	} else if ruleObj.Details == "Bureau" && strings.Contains(ruleObj.Rule, "Score") {
		return "Sorry, you are not eligible for the loan due to a low credit score. Please check the Analysis of your score to learn how to improve the same"
	} else if ruleObj.Details == "Bureau" {
		return "Sorry, you don't meet our bureau eligibility. Please check the Analysis of your credit bureau report to learn how to improve the same"
	}
	return "Sorry, you don't meet our internal eligibility checks. Please try again later."
}

// CheckMarkUserPincodeBlock checks whether pincode needs to be blocked - declared / lat lon based
// returns a boolean indicating whether the user is to be blocked, and pincode based on which blocking happened
// and the source of pincode blocking
// final pincode to store in DB
func CheckMarkUserPincodeBlock(userID string, sourceEntityID string, declaredPincode string, lat string, lon string) (bool, string, string, string) {

	// final pincode to save in DB
	finalPincode := declaredPincode

	checkForLocation := journey.CheckLocationPincode(sourceEntityID)

	// take the pincode from location if required
	var pincodeFromLocation string
	if (declaredPincode == "" || checkForLocation) && lat != "" && lon != "" {
		pincodeFromLocation, _ = reversepincode.FetchPincodeFromLocation(lat, lon)
		if finalPincode == "" {
			finalPincode = pincodeFromLocation
		}
	}
	if finalPincode == "" {
		finalPincode = "560102" // default value if not pincode found
	}

	// blocking based on declared pincode
	if declaredPincode != "" {
		// Getting the lender for the user
		lenderID := ""
		if journey.IIFLBLPolicy(userID, sourceEntityID) || journey.IIFLPLPolicy(sourceEntityID) || journey.IsNexarcIIFL(userID, sourceEntityID) {
			lenderID = constants.IIFLID
		} else if journey.IsPFLSourcing(sourceEntityID) {
			lenderID = constants.PoonawallaFincorpID
		}

		blocked := false
		if general.InArr(lenderID, []string{constants.IIFLID, constants.PoonawallaFincorpID}) {
			blocked = CheckPincodeBlocked(sourceEntityID, lenderID, declaredPincode)
		}
		if !blocked && journey.CheckPlatformPincode(sourceEntityID) {
			blocked = CheckPlatformPincodeBlocked(declaredPincode, sourceEntityID)
		}
		if blocked {
			return true, declaredPincode, constants.PincodeSourceDeclared, finalPincode
		}
	}
	// check blocking based on location if available and required
	if pincodeFromLocation != "" && checkForLocation {
		if CheckPlatformPincodeBlocked(pincodeFromLocation, sourceEntityID) {
			return true, pincodeFromLocation, constants.PincodeSourceLocation, finalPincode
		}
	}
	return false, "", "", finalPincode
}

// CheckAndUpdateLoanApplicationStatus checks for active loan application and updates status to rejected if user is disqualified
func CheckAndUpdateLoanApplicationStatus(userID, sourceEntityID string) error {

	type statusStruct struct {
		UserStatus            int            `db:"userstatus"`
		LoanApplicationStatus int            `db:"loanapplicationstatus"`
		LoanApplicationID     string         `db:"loan_application_id"`
		BREReferenceID        sql.NullString `db:"reference_id"`
	}

	statusObj := statusStruct{}

	query := `SELECT u.status as userstatus, la.status as loanapplicationstatus, der.reference_id, la.loan_application_id
	FROM users u
	JOIN loan_application la ON u.user_id = la.user_id AND u.source_entity_id = la.source_entity_id
	JOIN decision_engine_response der ON u.user_id = der.user_id
	WHERE u.user_id = $1 and u.source_entity_id=$2
	order by der.created_at desc limit 1`

	err := database.Get(&statusObj, query, userID, sourceEntityID)
	if err != nil {
		if err == sql.ErrNoRows {
			logger.WithUser(userID).Warn("loan application not created yet")
			return nil
		}
		log.Errorln(err)
		return err
	}

	if statusObj.UserStatus == constants.UserStatusDisqualified && isLoanApplicationActive(statusObj.LoanApplicationStatus) {
		query := `UPDATE loan_application SET status = $1, reference_id = $2, updated_at = NOW() where loan_application_id = $3`
		_, err := database.Exec(query, constants.LoanStatusLoanRejected, statusObj.BREReferenceID, statusObj.LoanApplicationID)
		if err != nil {
			log.Errorln(err)
			return err
		}
	}
	return nil
}

func isLoanApplicationActive(status int) bool {
	return !general.InArr(status, []int{constants.LoanStatusDisbursed, constants.LoanStatusCancelled, constants.LoanStatusClosed})
}

// IsIIFLBureauPolicy returns true for bureau policy, false otherwise
func IsIIFLBureauPolicy(ruleVersion string) bool {
	return strings.Contains(ruleVersion, "bureau") || strings.Contains(ruleVersion, "iifl_v2")
}

func CallSentinel(userID, ruleVersion, uniqueID, sourceEntityID, lenderID string, additionalData map[string]interface{}) SentinelRunStruct {
	var sentinelData SentinelRunStruct
	sentinelConf := conf.GetSentinelCreds(lenderID, sourceEntityID)
	sentinelBaseURL := sentinelConf["baseURL"]
	sentinelAPIKey := sentinelConf["apiKey"]

	sentinelURL := fmt.Sprintf("%s/v1/sentinel/decide", sentinelBaseURL)
	var sentinelBody []byte

	sentinelReqBody := SentinelReqStruct{ReferenceID: general.GetUUID(), PolicyVersion: ruleVersion, UserID: uniqueID, DryRun: false, Source: "embedded_finance", Additional: additionalData}
	idx := 0

	sentinelError := retry.CustomRetry(maxTry, 3*time.Second, func() error {
		sentinelBody = []byte{}
		sentinelData = SentinelRunStruct{}

		logger.WithUser(userID).Debugln("calling sentinel try ", idx)
		idx++
		sentinelClient := tracer.GetTraceableHTTPClientV2(nil, sentinelServiceName, sourceEntityID)
		payload := new(bytes.Buffer)
		if err := json.NewEncoder(payload).Encode(sentinelReqBody); err != nil {
			logger.WithUser(userID).Error(err)
			return err
		}
		sentinelReq, err := requestutils.GetMockableHTTPRequest(userID, sentinelServiceName, "POST", sentinelURL, payload)
		if err != nil {
			logger.WithUser(userID).Error(err)
			return err
		}
		sentinelReq.Header.Add("x-api-key", sentinelAPIKey)
		sentinelRes, err := sentinelClient.Do(sentinelReq)
		if err != nil {
			logger.WithUser(userID).Error(err)
			return err
		}
		if sentinelRes.StatusCode > 500 && sentinelRes.StatusCode < 600 {
			err := fmt.Errorf("internal server error with statuscode %d", sentinelRes.StatusCode)
			logger.WithUser(userID).Error(err)
			return err
		}

		defer func() {
			if err := sentinelRes.Body.Close(); err != nil {
				logger.WithUser(userID).Error(err)
			}
		}()
		sentinelBody, err = io.ReadAll(sentinelRes.Body)
		if err != nil {
			logger.WithUser(userID).Error(err)
			return err
		}
		err = json.Unmarshal(sentinelBody, &sentinelData)
		if err != nil {
			logger.WithUser(userID).Error(err)
			errorHandler.ReportToSentryWithoutRequest(err)
			return err
		}

		if sentinelData.Progress == Progressing {
			logger.WithUser(userID).Warnln("received progressing")
			return fmt.Errorf("retrying")
		}
		return nil
	})

	requestBody, _ := json.Marshal(sentinelReqBody)

	var errRes string
	if sentinelError != nil {
		errRes = sentinelError.Error()
		go serviceslib.WriteToDB(sentinelServiceName, string(requestBody), string(sentinelBody), 0, userID, sentinelURL, errRes, general.GetUUID())
	} else {
		go serviceslib.WriteToDB(sentinelServiceName, string(requestBody), string(sentinelBody), 1, userID, sentinelURL, errRes, general.GetUUID())
	}
	return sentinelData

}

// GetLenderID returns the lender ID for a source entity
func GetLenderID(sourceEntityID, userID, segment string) (lenderID string, selectionType string) {
	selectionType = constants.LenderSelectionTypeDefault
	// Check PreSelectedLender for multi lenders
	if journey.HasMultiLender(sourceEntityID) {
		lenderID = preselectedlender.Get(userID)
	}
	if lenderID != "" {
		return lenderID, selectionType
	}
	lenderID = constants.XYZLenderID
	if journey.IsPFLEducationLoanJourney(sourceEntityID) {
		lenderID = constants.PoonawallaFincorpID
	} else if general.InArr(sourceEntityID, []string{constants.GeniusID, constants.GimBooksID, constants.NiyoID, constants.PagarBookID}) {
		lenderID = constants.TrustLenderID
	} else if general.InArr(sourceEntityID, []string{constants.LetsTransportID, constants.ExpressStoresID, constants.ShopKiranaID, constants.BeldaraID, constants.CityMallID, constants.CashifyID}) {
		lenderID = constants.ArthanLenderID
	} else if journey.IsMuthootCLPartner(sourceEntityID) || journey.IsMuthootEDIPartner(sourceEntityID) {
		lenderID = constants.MuthootCLID
	} else if general.InArr(sourceEntityID, []string{constants.VyaparID}) {
		lenderID, selectionType = AssignLenderForVyapar(userID)
		if selectionType != constants.LenderSelectionTypeDefault && !general.InArr(lenderID, []string{"NA", ""}) {
			timestamp := general.GetTimeStampString()
			go func() {
				activityObj := activity.ActivityEvent{
					UserID:            userID,
					SourceEntityID:    sourceEntityID,
					LoanApplicationID: "",
					EntityType:        constants.EntityTypeSystem,
					EntityRef:         selectionType,
					EventType:         constants.ActivityLenderPreSelected,
					Description:       constants.LenderNamesMap[lenderID],
				}
				activity.RegisterEvent(&activityObj, timestamp)
			}()
			go func() {
				errorHandler.RecoveryNoResponse()
				err := journeyutils.RouteToLender(userID, lenderID, selectionType)
				if err != nil {
					logger.WithUser(userID).Errorln(err)
				}
			}()
		}
	} else if journey.IsIIFLExclusiveLender(sourceEntityID) {
		lenderID = constants.IIFLID
	} else if journey.IsBigBasketFlow(sourceEntityID) {
		lenderID = constants.KotakID
	} else if journey.IsHCINFlow(sourceEntityID) {
		lenderID = constants.HCINLenderID
	} else if journey.IsOneMuthootPartner(sourceEntityID) {
		lenderID = constants.MCSLID
	} else if journey.IsSaraloanAsLender(sourceEntityID) {
		lenderID = constants.SaraloanID
	} else if journey.IsMintifiAsLender(sourceEntityID) {
		lenderID = constants.MintifiID
	} else if journey.IsPFLSourcing(sourceEntityID) {
		lenderID = constants.PoonawallaFincorpID
	} else if sourceEntityID == constants.SupremeSolarID {
		lenderID = constants.EcofyID
	} else if journey.IsMFLEMIPartner(sourceEntityID) {
		lenderID = constants.MFLID
	} else if sourceEntityID == constants.HousingID {
		lenderID, selectionType = AssignLenderForHousing(userID)
		if selectionType != constants.LenderSelectionTypeDefault && !general.InArr(lenderID, []string{"NA", ""}) {
			timestamp := general.GetTimeStampString()
			go func() {
				activityObj := activity.ActivityEvent{
					UserID:            userID,
					SourceEntityID:    sourceEntityID,
					LoanApplicationID: "",
					EntityType:        constants.EntityTypeSystem,
					EntityRef:         selectionType,
					EventType:         constants.ActivityLenderPreSelected,
					Description:       constants.LenderNamesMap[lenderID],
				}
				activity.RegisterEvent(&activityObj, timestamp)
			}()
			go func() {
				errorHandler.RecoveryNoResponse()
				err := journeyutils.RouteToLender(userID, lenderID, selectionType)
				if err != nil {
					logger.WithUser(userID).Errorln(err)
				}
			}()
		}
	} else if journey.IsABFLBLSourcing(sourceEntityID) {
		lenderID = constants.ABFLID
	} else if journey.IsABFLPLSourcing(sourceEntityID) {
		lenderID = constants.ABFLPLID
	} else if sourceEntityID == constants.KhatabookID {
		lenderID = ""
		selectionType = constants.LenderSelectionTypeCascading
		activeLenders := journey.GetActiveLenders(sourceEntityID, segment)

		for _, lender := range activeLenders {
			var decision string
			// Check if decision was rejected for this lender's policy
			query := "select decision from decision_engine_response where user_id = $1 and now() - created_at < interval '1 day' and lender_id = $2 order by created_at desc limit 1"
			err := database.Get(&decision, query, userID, lender)
			if err != nil {
				logger.WithUser(userID).Error(err)
			}

			if decision != constants.UnderwritingDecisionReject {
				duplicate, _, _, _ := fraudcheckutils.DoPANDuplicityCheckIIFL(userID, sourceEntityID)
				if !(lender == constants.IIFLID && duplicate) {
					lenderID = lender
					break
				}
			}
		}
	} else if sourceEntityID == constants.TataPLID {
		lenderID, selectionType = AssignLenderForTataV2(userID, sourceEntityID)
	} else if sourceEntityID == constants.ABFLMarketplaceID { // TODO: move to multi-offer
		loanApplication, err := loanapplication.GetLatestValidByUser(userID)
		if err != nil {
			logger.WithUser(userID).Errorln(err)
		} else {
			lenderID = loanApplication.LenderID
		}
	} else if journey.IsMFLBLSourcing(sourceEntityID) {
		lenderID = constants.MFLBLID
	} else if sourceEntityID == constants.SuperMoneyID {
		lenderID = constants.PoonawallaFincorpID
	} else if journey.IsIncredSourcing(sourceEntityID) {
		lenderID = constants.IncredID
	} else if sourceEntityID == constants.PrefrSuperMoneyID {
		lenderID = constants.PrefrSMLenderID
	}

	if lenderID == "NA" {
		errorHandler.ReportToSentryWithoutRequest(fmt.Errorf("empty lender ID for source - %s, user - %s, segment - %s", sourceEntityID, userID, segment))
	}
	return lenderID, selectionType
}

// DisqualifyRevert func reverts the disqualification of the user and return errMsg and error
func DisqualifyRevert(userID, sourceEntityID, description string, amount, tenure int, entityRef string) (string, error) {
	var userDBObj struct {
		Status     int
		CustomerID string
	}

	query := `SELECT status, unique_id as customerid from users where user_id = $1
			  AND source_entity_id = $2`
	err := database.Get(&userDBObj, query, userID, sourceEntityID)
	if err != nil {
		logger.WithUser(userID).Error(err)
		return "user not found", nil
	}

	if userDBObj.Status != constants.UserStatusDisqualified {
		return "user not in disqualified status", nil
	}

	// Check revert limits
	minAmount, maxAmount, minTenure, maxTenure := journey.DisqualifyRevertLimits(sourceEntityID)

	var offerObj struct {
		Interest          float64
		ProcessingFee     float64
		ProcessingFeeType string
	}

	lenderID, _ := GetLenderID(sourceEntityID, userID, "")

	extraCondition := "and lender_id = $2"
	args := []interface{}{sourceEntityID, lenderID}
	if lenderID == "" || lenderID == constants.XYZLenderID {
		extraCondition = ""
		args = args[:1]
	}

	if minAmount != -1 || maxAmount != -1 || minTenure != -1 || maxTenure != -1 {
		query = `select interest, processing_fee as processingfee, processing_fee_type as processingfeetype
				from loan_offer_template where source_entity_id = $1` + extraCondition + ` order by created_at desc limit 1`
		err = database.Get(&offerObj, query, args...)
		if err != nil {
			logger.WithUser(userID).Error(err)
			return "", err
		}
	}

	tx, _ := database.Begin()

	query = `UPDATE users set status = $1, updated_at = NOW() where user_id = $2`
	_, err = tx.Exec(query, constants.UserStatusQualified, userID)
	if err != nil {
		logger.WithUser(userID).Error(err)
		_ = tx.Rollback()
		return "", nil
	}

	sqlAmount := sql.NullInt64{Int64: int64(amount), Valid: amount != 0}
	sqlTenure := sql.NullInt64{Int64: int64(tenure), Valid: tenure != 0}
	var decision string
	if amount != 0 && tenure != 0 && (minAmount != -1 || maxAmount != -1 || minTenure != -1 || maxTenure != -1) {
		decision = constants.UnderwritingDecisionPass
	} else {
		decision = constants.UnderwritingDecisionCantDecide
	}

	referenceID := general.GetUUID()

	query = `INSERT into decision_engine_response(reference_id, user_id, decision, max_tenure, amount, rule_version, created_at, lender_id, dump_id, evaluation_id)
		values (
			$1, $2, $3, $4, $5, $6, NOW(), $7, $8, $9
		)`
	_, err = tx.Exec(query, referenceID, userID, decision, sqlTenure, sqlAmount, constants.DisqualifyRevertRule, sql.NullString{String: lenderID, Valid: lenderID != ""}, referenceID, referenceID)
	if err != nil {
		logger.WithUser(userID).Error(err)
		_ = tx.Rollback()
		return "", err
	}

	if minAmount == -1 || maxAmount == -1 || minTenure == -1 || maxTenure == -1 {
		query = `DELETE from user_eligibility where user_id = $1 and eligible_amount = 0`
		_, err = tx.Exec(query, userID)
		if err != nil {
			logger.WithUser(userID).Error(err)
			_ = tx.Rollback()
			return "", err
		}
	} else {
		// Validate and generate offer
		if amount <= maxAmount && tenure <= maxTenure && amount >= minAmount && tenure >= minTenure {
			query = `insert into user_eligibility
				(source_entity_id , user_id, created_at, created_by, is_eligible, eligible_amount,
				interest, processing_fee, processing_fee_type, tenure, reference_id)
				values ($1, $2, NOW(), 'ADMIN', true, $3, $4, $5, $6, $7, $8)`
			_, err = tx.Exec(query, sourceEntityID, userID, amount, offerObj.Interest, offerObj.ProcessingFee, offerObj.ProcessingFeeType, tenure, referenceID)
			if err != nil {
				logger.WithUser(userID).Error(err)
				_ = tx.Rollback()
				return "", err
			}
		} else {
			_ = tx.Rollback()
			return "amount or tenure is not in range", nil
		}
	}
	_ = tx.Commit()

	dateTimeNowString := general.GetTimeStampString()
	go func() {
		activityObj := activity.ActivityEvent{
			UserID:            userID,
			SourceEntityID:    sourceEntityID,
			LoanApplicationID: "",
			EntityType:        constants.EntityTypeDashboardUser,
			EntityRef:         entityRef,
			EventType:         constants.ActivityDisqualifyReverted,
			Description:       description,
		}
		activity.RegisterEvent(&activityObj, dateTimeNowString)
	}()
	return "", nil
}

// CheckSelfieFaceMatchThreshold takes userID, sourceEntityID,
// matching score as per hyperverge, selfie matched as per hyperverge
// and returns whether selfie is okay
func CheckSelfieFaceMatchThreshold(userID string, sourceEntityID string, score int, selfieMatch bool) bool {
	if sourceEntityID == constants.KhatabookID {
		lenderID, _ := GetLenderID(sourceEntityID, userID, "") // TODO: remove passing programID as blank, currently it does not affect as lender will come from pre_selected_lender
		if lenderID != constants.IIFLID {
			return (score >= 40 || selfieMatch)
		}
	}
	if journey.IsPFLSourcing(sourceEntityID) {
		return score >= 60
	}
	if journey.IsMuthootCLPartner(sourceEntityID) {
		return score >= 80
	}
	return selfieMatch
}

// CheckSelfieLivelinessThreshold takes sourceEntityID,
// liveliness score as per hyperverge, is live as per hyperverge
// and returns whether selfie is live and error
func CheckSelfieLivelinessThreshold(sourceEntityID string, livelinessScore string, isLiveHV bool) (bool, error) {
	if journey.IsMuthootCLPartner(sourceEntityID) {
		score, err := strconv.ParseFloat(livelinessScore, 64)
		if err != nil {
			return false, err
		}
		if score >= 0.8 {
			return true, nil
		}
		return false, nil
	}
	return isLiveHV, nil
}

func CallSentinelWorkflow(userID, ruleVersion, uniqueID, sourceEntityID, referenceID, lenderID string, additionalData map[string]interface{}) (SentinelWorkflowRunStruct, string) {
	var sentinelData SentinelWorkflowRunStruct
	sentinelConf := conf.GetSentinelCreds(lenderID, sourceEntityID)
	sentinelBaseURL := sentinelConf["baseURL"]
	sentinelAPIKey := sentinelConf["apiKey"]

	sentinelURL := fmt.Sprintf("%s/v1/sentinel/decideCompound", sentinelBaseURL)
	var sentinelBody []byte

	sentinelReqBody := SentinelReqStruct{ReferenceID: referenceID, PolicyVersion: ruleVersion, UserID: uniqueID, DryRun: false, Source: "embedded_finance", Additional: additionalData}
	idx := 0
	sentinelError := retry.CustomRetry(maxTry, 3*time.Second, func() error {
		sentinelBody = []byte{}
		sentinelData = SentinelWorkflowRunStruct{}

		logger.WithUser(userID).Debugln("calling sentinel try ", idx)
		idx++
		sentinelClient := tracer.GetTraceableHTTPClientV2(nil, sentinelWorkflowServiceName, sourceEntityID)
		payload := new(bytes.Buffer)
		if err := json.NewEncoder(payload).Encode(sentinelReqBody); err != nil {
			logger.WithUser(userID).Error(err)
			return err
		}
		sentinelReq, err := requestutils.GetMockableHTTPRequest(userID, sentinelWorkflowServiceName, "POST", sentinelURL, payload)
		if err != nil {
			logger.WithUser(userID).Error(err)
			return err
		}
		sentinelReq.Header.Add("x-api-key", sentinelAPIKey)
		sentinelRes, err := sentinelClient.Do(sentinelReq)
		if err != nil {
			logger.WithUser(userID).Error(err)
			return err
		}
		if sentinelRes.StatusCode > 500 && sentinelRes.StatusCode < 600 {
			err := fmt.Errorf("internal server error with statuscode %d", sentinelRes.StatusCode)
			logger.WithUser(userID).Error(err)
			return err
		}

		defer func() {
			if err := sentinelRes.Body.Close(); err != nil {
				logger.WithUser(userID).Error(err)
			}
		}()
		sentinelBody, err = io.ReadAll(sentinelRes.Body)
		if err != nil {
			logger.WithUser(userID).Error(err)
			return err
		}
		err = json.Unmarshal(sentinelBody, &sentinelData)
		if err != nil {
			logger.WithUser(userID).Error(err)
			errorHandler.ReportToSentryWithoutRequest(err)
			return err
		}

		if sentinelData.Progress == Progressing {
			logger.WithUser(userID).Warnln("received progressing for referenceID: ", referenceID)
			return fmt.Errorf("retrying")
		}
		return nil
	})
	logError := sentinelData.Error
	if sentinelError != nil && sentinelData.Error != "" { //both have error
		logError = fmt.Sprintf("%s:%s", sentinelError.Error(), sentinelData.Error)
	} else if sentinelError != nil { //only sentinelError has err
		logError = sentinelError.Error()
	}

	requestBody, _ := json.Marshal(sentinelReqBody)

	if logError != "" {
		serviceslib.WriteToDB(sentinelWorkflowServiceName, string(requestBody), string(sentinelBody), serviceslib.ErrorStatusCode, userID, sentinelURL, logError, general.GetUUID())
	} else {
		serviceslib.WriteToDB(sentinelWorkflowServiceName, string(requestBody), string(sentinelBody), serviceslib.SuccessStatusCode, userID, sentinelURL, "", general.GetUUID())
	}
	return sentinelData, logError

}

// CallSentinelWorkflowV2 To accommodate latest sentinel workflow response
func CallSentinelWorkflowV2(userID, ruleVersion, uniqueID, sourceEntityID, referenceID, lenderID string, additionalData map[string]interface{}) (sentinel.FetchEvaluationResStructV2, string) {
	var endPointCode string
	if conf.ENV == conf.ENV_PROD {
		endPointCode = "emb_per_tdl_len_ojv"
	} else {
		endPointCode = "emb_per_tdl_len_vai"
	}
	source := "tdl"
	ctx := context.Background()
	success, evaluationID := sentinel.TriggerEval(ctx, referenceID, userID, lenderID, sourceEntityID, uniqueID, endPointCode, source, additionalData)
	if !success {
		errString := constants.ErrStringEvaluationIDNotFound
		logger.WithUser(userID).Errorln(errString)
		return sentinel.FetchEvaluationResStructV2{}, errString
	}

	errString, evaluationData := sentinel.FetchEvalV2(userID, evaluationID, lenderID, sourceEntityID)
	if len(errString) > 0 {
		logger.WithUser(userID).Errorln(errString)
		return sentinel.FetchEvaluationResStructV2{}, errString
	}

	return evaluationData, ""

}

func checkIfSalaried(userID string) (bool, error) {
	var dynamicuserinfo string
	query := `select dynamic_user_info as dynamicuserinfo from users where user_id = $1`
	err := database.Get(&dynamicuserinfo, query, userID)
	if err != nil {
		logger.WithUser(userID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return false, err
	}

	var dynamicUserInfo users.DynamicUserInfo
	if err = json.Unmarshal([]byte(dynamicuserinfo), &dynamicUserInfo); err != nil {
		logger.WithUser(userID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return false, err
	}
	if dynamicUserInfo.Occupation == "SALARIED" {
		return true, nil
	}
	return false, nil
}

// ExecuteBREOnHardPullData fetches hard pull data for a user and executes BRE based on that data for a loan application
func ExecuteBREOnHardPullData(userID, sourceEntityID, loanApplicationID string) (string, string, error) {
	userObj, _ := users.Get(userID)
	city, _ := pincodeapi.GetCityState(userObj.Pincode)
	_, _, _, name := iiflutils.GetNameForIIFL(userObj.Name)
	ctx := context.Background()
	crmLeadID := iiflutils.GetOrCreateLeadID(ctx, iiflutils.LoanStruct{
		Email:          userObj.Email,
		Mobile:         userObj.Mobile,
		UserID:         userID,
		PartnerCode:    userObj.PartnerCode,
		Name:           name,
		CurrentAddress: fmt.Sprintf(`{"city": "%s"}`, city),
	}, constants.LoanTypeBusinessLoan)
	if crmLeadID == "" {
		logger.WithUser(userObj.ID).Errorln("error in fetching CRM Lead ID")
		return constants.UnderwritingDecisionCantDecide, "", errors.New("error in fetching CRM Lead ID")
	}
	dobObj := strings.Split(userObj.DOB, "-")
	dob := dobObj[2] + dobObj[1] + dobObj[0]
	hardPullSuccessful, err := SelectCIBILWithoutProspect(crmLeadID, name, userObj.ID, dob, "10000", constants.GenderNumToStr[*userObj.Gender], userObj.PAN, userObj.Pincode, userObj.Mobile)
	if err != nil {
		logger.WithUser(userObj.ID).Errorln(err)
		return constants.UnderwritingDecisionCantDecide, "", err
	}
	if hardPullSuccessful {
		decision, rejectReason, err := KYCQualification(userID, loanApplicationID, sourceEntityID, constants.IIFLID, "", "", false)
		if err != nil {
			logger.WithUser(userObj.ID).Errorln(err)
			return constants.UnderwritingDecisionCantDecide, rejectReason, err
		}
		return decision, rejectReason, nil
	}
	return constants.UnderwritingDecisionCantDecide, "", errors.New("iifl hard pull failed")
}

// InsertPAFromPartnerDataForTataPL ... @deprecated
func InsertPAFromPartnerDataForTataPL(userID, pan, mobile, email, uniqueID string) error {
	partnerData, err := users.GetPartnerData(userID)
	if err != nil {
		return err
	}

	var partnerPush partner.TataStruct

	err = json.Unmarshal([]byte(partnerData), &partnerPush)
	if err != nil {
		logger.WithUser(userID).Errorln(err)
		return err
	}

	if len(partnerPush.Data.CustomerPartnerData.PreApprovedOffer) > 0 {
		preApprovedOffer := partnerPush.Data.CustomerPartnerData.PreApprovedOffer[0]
		lenderID := TDLLenderCodeAndIDMapping[strings.ToLower(preApprovedOffer.LenderCode)]
		if lenderID != "" && preApprovedOffer.SanctionedAmount > 0 && preApprovedOffer.Tenure != "" {
			tenureTemp, err := strconv.ParseFloat(preApprovedOffer.Tenure, 64)
			if err != nil {
				logger.WithUser(userID).Errorln(err)
				return err
			}
			tenureInt := int(tenureTemp)
			tenureType := "PERC"
			err = preapproved.Insert("", "", lenderID, constants.TataPLID, preApprovedOffer.RateOfInterest, float64(preApprovedOffer.SanctionedAmount), tenureInt, "tata_marketplace", preApprovedOffer.ProcessingFee, tenureType, email, nil, uniqueID, "")
			if err != nil {
				logger.WithUser(userID).Errorln(err)
				return err
			}
		}
	}
	return nil
}

// InsertPAFromPartnerDataForTataPLV2 accepts multi pre approved from the partner push and inserts into pre approved table
func InsertPAFromPartnerDataForTataPLV2(userID, pan, mobile, email, uniqueID string) error {
	partnerData, err := users.GetPartnerData(userID)
	if err != nil {
		return err
	}

	var partnerPush partner.TataStruct

	err = json.Unmarshal([]byte(partnerData), &partnerPush)
	if err != nil {
		logger.WithUser(userID).Errorln(err)
		return err
	}

	preApprovedOffers := partnerPush.Data.CustomerPartnerData.PreApprovedOffer

	preQualifiedOffers := partnerPush.Data.CustomerPartnerData.PreQualifiedOffer

	for _, preApprovedOffer := range preApprovedOffers {
		lenderID := TDLLenderCodeAndIDMapping[strings.ToLower(preApprovedOffer.LenderCode)]
		if lenderID != "" && preApprovedOffer.SanctionedAmount > 0 && preApprovedOffer.Tenure != "" {
			isExpired, err := general.CheckExpiryOnDate(preApprovedOffer.ValidTill, "2006-01-02")
			if err != nil {
				logger.WithUser(userID).Errorln(err)
				continue
			}
			if isExpired {
				continue
			}
			tenureTemp, err := strconv.ParseFloat(preApprovedOffer.Tenure, 64)
			if err != nil {
				logger.WithUser(userID).Errorln(err)
				continue
			}
			tenureInt := int(tenureTemp)
			tenureType := "PERC"
			additionalParams := map[string]any{
				"approvalType": constants.ApprovalTypePreApproved,
			}
			err = preapproved.Insert("", "", lenderID, constants.TataPLID, preApprovedOffer.RateOfInterest, float64(preApprovedOffer.SanctionedAmount), tenureInt, "tata_marketplace", preApprovedOffer.ProcessingFee, tenureType, email, additionalParams, uniqueID, preApprovedOffer.ValidTill)
			if err != nil {
				logger.WithUser(userID).Errorln(err)
				return err
			}
		}
	}

	for _, preQualifiedOffer := range preQualifiedOffers {
		lenderID := TDLLenderCodeAndIDMapping[strings.ToLower(preQualifiedOffer.LenderCode)]
		if lenderID != "" && preQualifiedOffer.SanctionedAmount > 0 && preQualifiedOffer.Tenure != "" {
			isExpired, err := general.CheckExpiryOnDate(preQualifiedOffer.ValidTill, "2006-01-02")
			if err != nil {
				logger.WithUser(userID).Errorln(err)
				continue
			}
			if isExpired {
				continue
			}
			tenureTemp, err := strconv.ParseFloat(preQualifiedOffer.Tenure, 64)
			if err != nil {
				logger.WithUser(userID).Errorln(err)
				continue
			}
			tenureInt := int(tenureTemp)
			tenureType := "PERC"
			additionalParams := map[string]any{
				"approvalType": constants.ApprovalTypePreQualified,
			}
			err = preapproved.Insert("", "", lenderID, constants.TataPLID, preQualifiedOffer.RateOfInterest, float64(preQualifiedOffer.SanctionedAmount), tenureInt, "tata_marketplace", preQualifiedOffer.ProcessingFee, tenureType, email, additionalParams, uniqueID, preQualifiedOffer.ValidTill)
			if err != nil {
				logger.WithUser(userID).Errorln(err)
				return err
			}
		}
	}

	return nil
}

var TDLLenderCodeAndIDMapping = map[string]string{
	"dmi":    constants.DMIID,
	"cashe":  constants.CasheID,
	"kb":     constants.TDLKreditBeeID,
	"mv":     constants.MoneyViewID,
	"abfl":   constants.ABFLID,
	"prefr":  constants.PrefrID,
	"kissht": constants.KisshtID,
	"tcap":   constants.TataCapitalID,
	"hdfc":   constants.HDFCLenderID,
	"axis":   constants.AxisBankID,
}

type SentinelWorkflowRunStructV2 struct {
	Data   Data   `json:"data"`
	Error  string `json:"error"`
	Status bool   `json:"status"`
}

type Data struct {
	EvalEndpointID string `json:"evalEndpointID"`
}

// IsTDLPAOfferExistInPartnerPush checks the existence of pa offer and validity from partner push
func IsTDLPAOfferExistInPartnerPush(userID string) (bool, bool) {
	partnerData, err := users.GetPartnerData(userID)
	if err != nil {
		logger.WithUser(userID).Errorln(err)
		errorHandler.ReportToSentryWithFields(errorHandler.ErrorFields{"userID": userID}, err)
		return false, false
	}

	var partnerPush partner.TataStruct

	err = json.Unmarshal([]byte(partnerData), &partnerPush)
	if err != nil {
		logger.WithUser(userID).Errorln(err)
		return false, false
	}

	preApprovedOffers := partnerPush.Data.CustomerPartnerData.PreApprovedOffer

	preQualifiedOffers := partnerPush.Data.CustomerPartnerData.PreQualifiedOffer

	var paOfferExist, tcapPAExist = false, false

	for _, preApprovedOffer := range preApprovedOffers {
		lenderID := TDLLenderCodeAndIDMapping[strings.ToLower(preApprovedOffer.LenderCode)]
		if lenderID != "" && preApprovedOffer.SanctionedAmount > 0 && preApprovedOffer.Tenure != "" {
			isExpired, err := general.CheckExpiryOnDate(preApprovedOffer.ValidTill, "2006-01-02")
			if err != nil {
				logger.WithUser(userID).Errorln(err)
				errorHandler.ReportToSentryWithFields(errorHandler.ErrorFields{"userID": userID}, err)
				continue
			}
			if !isExpired {
				paOfferExist = true
				if lenderID == constants.TataCapitalID {
					tcapPAExist = true
				}
			}
		}

		if paOfferExist && tcapPAExist {
			return true, true
		}
	}

	for _, preQualifiedOffer := range preQualifiedOffers {
		lenderID := TDLLenderCodeAndIDMapping[strings.ToLower(preQualifiedOffer.LenderCode)]
		if lenderID != "" && preQualifiedOffer.SanctionedAmount > 0 && preQualifiedOffer.Tenure != "" {
			isExpired, err := general.CheckExpiryOnDate(preQualifiedOffer.ValidTill, "2006-01-02")
			if err != nil {
				logger.WithUser(userID).Errorln(err)
				errorHandler.ReportToSentryWithFields(errorHandler.ErrorFields{"userID": userID}, err)
				continue
			}
			if !isExpired {
				paOfferExist = true
				if lenderID == constants.TataCapitalID {
					tcapPAExist = true
				}
			}
		}

		if paOfferExist && tcapPAExist {
			return true, true
		}
	}

	return paOfferExist, tcapPAExist
}
