// Package sms .
package sms

import (
	"context"
	"finbox/go-api/conf"
	"finbox/go-api/constants"
	"finbox/go-api/errorHandler"
	"finbox/go-api/functions/journey"
	"finbox/go-api/functions/legallogs"
	"finbox/go-api/functions/logger"
	"finbox/go-api/infra/redis"
	"finbox/go-api/models/masterdashboard"
	"finbox/go-api/models/users"
	"finbox/go-api/thirdparty/karix"
	"finbox/go-api/utils/general"
	"fmt"
	"strconv"
	"time"
)

var log = logger.Log

func userNameOTPTemplate(sourceEntityID, otpType string) bool {
	return journey.IsMFLBLSourcing(sourceEntityID) || (journey.IsABFLPLSourcing(sourceEntityID) && otpType == constants.OTPTypeESign)

}

// SendOTPWithAuthID sends OTP to given number and returns an error message if any
// TODO: TataOTPReq to be removed
func SendOTPWithAuthID(authID string, mobile string, userID string, autoOTPReadHash string, otpType string, sourceEntityID string, lenderID string, tataOTPStruct *TataOTPReq) (int, string) {

	var redisOTPAttemptDataKey = getRedisOTPAttemptDataKey(authID, otpType, "")
	// if otpType == ""; log.Println("")
	var otpConf OtpConfigs
	if otpType == constants.OTPTypeMDMobileOTPLogin {
		var maxOtpResendAttempt int
		organizationID, err := masterdashboard.GetOrganizationIDByMobile(mobile)
		if err != nil {
			return 0, err.Error()
		}
		maxOtpResendAttempt, err = masterdashboard.GetMaxOTPResendAttempt(organizationID)
		if err != nil {
			return 0, err.Error()
		}
		otpConf = OtpConfigs{
			MaxOTPLimitPerHour:    maxOtpResendAttempt,
			MaxSameOTPUseCount:    7,
			MaxOTPValidityMinutes: 5,
			MaxFailedAttempts:     3,
			OTPBlockedMinutes:     10,
		}
	} else {
		otpConf = getOTPConfigs(otpType)
	}

	otpAttemptValid, otpAttemptData, err := checkOTPAttemptValidity(redisOTPAttemptDataKey, otpConf)
	if !otpAttemptValid {
		updateRedisOTPAttempts(redisOTPAttemptDataKey, otpAttemptData)
		return 0, err.Error()
	}

	var redisOTPDataKey = getRedisOTPDataKey(authID, otpType, "")
	otpData := fetchLastOTP(redisOTPDataKey)

	if otpData.OTP == -1 || (otpData.SentCount != 0 && otpConf.MaxSameOTPUseCount != -1 && otpData.SentCount%otpConf.MaxSameOTPUseCount == 0) || (isOTPExpired(otpData.GeneratedAt, otpConf) && otpConf.MaxOTPValidityMinutes != -1) {
		// otp is not found
		if !general.InArr(conf.ENV, []string{conf.ENV_PROD, conf.ENV_UAT, conf.ENV_DEV + "3", conf.ENV_DEV_TDL, conf.ENV_UAT_TDL, conf.ENV_UAT3, conf.ENV_UAT10, conf.ENV_UAT11}) {
			otpData.OTP = 0
		} else {
			otpData.OTP, err = general.RandomIntInRange(MinOTP, MaxOTP)
			if err != nil {
				log.Error(err)
			}
		}
		otpData.GeneratedAt = time.Now().Format(redisOTPTimeFormat)
	}
	otpAttemptData.SentCount += 1
	otpAttemptData.UpdatedAt = time.Now().Format(redisOTPTimeFormat)

	if sourceEntityID == constants.TataBNPLID {
		loc, err := time.LoadLocation("Asia/Calcutta")
		if err != nil {
			logger.WithUser(userID).Error(err)
			errorHandler.ReportToSentryWithoutRequest(err)
			return 0, ErrGenericOTPDownMessage
		}
		otpGenerationTime, err := time.Parse(redisOTPTimeFormat, otpData.GeneratedAt) // this time will be in utc
		if err != nil {
			errorHandler.ReportToSentryWithoutRequest(err)
			return 0, ErrGenericOTPDownMessage
		}
		otpGenerationTimeIST := otpGenerationTime.In(loc)
		tataOTPStruct.Data.TxnTimestamp = otpGenerationTimeIST.Format("02/01/2006 03:04:05 PM")
		tataOTPStruct.Data.ValidTill = otpGenerationTimeIST.Add(time.Duration(otpConf.MaxOTPValidityMinutes) * time.Minute).Format("03:04:05 PM")
	}

	otpData.SentCount += 1

	updateRedisOTP(redisOTPDataKey, otpData, otpConf)
	updateRedisOTPAttempts(redisOTPAttemptDataKey, otpAttemptData)

	// if !general.InArr(conf.ENV, []string{conf.ENV_PROD, conf.ENV_UAT, conf.ENV_UAT_TDL, conf.ENV_UAT + "7", conf.ENV_DEV_TDL}) {
	if !general.InArr(conf.ENV, []string{conf.ENV_PROD, conf.ENV_UAT, conf.ENV_DEV_TDL, conf.ENV_UAT3}) {
		// don't send sms in environment other than uat and prod
		return 0, ""
	}

	otpString := strconv.Itoa(otpData.OTP)
	dltTemplateDetails := getDLTDetails(sourceEntityID, lenderID, otpType)
	text := ""
	if dltTemplateDetails.IgnoreAutoOtpReadHash {
		// TODO: Optimize the code, make it configurable and take it from db
		if userNameOTPTemplate(sourceEntityID, otpType) {
			var name = "Customer"
			user, err := users.Get(userID)
			if err != nil {
				logger.WithUser(userID).Error("Error in getting user details", err)
			} else {
				name = user.Name
			}
			text = fmt.Sprintf(dltTemplateDetails.DLTTemplateID, name, otpString)
		} else {
			text = fmt.Sprintf(dltTemplateDetails.DLTTemplateID, otpString)
		}

	} else {
		text = fmt.Sprintf(dltTemplateDetails.DLTTemplateID, otpString, autoOTPReadHash)
	}

	isOTP := true //making this flag here to indicate if API key for OTP is to be used.
	if journey.IsMuthootCLPartner(sourceEntityID) {
		isOTP = false // For Muthoot CL the OTP template was whitelisted for FinBOx's non-otp api key of Karix
	}

	if otpData.SentCount%3 == 0 {
		// all third attempt
		if sourceEntityID == constants.TataBNPLID {
			tataOTPStruct.Data.OTPNumber = fmt.Sprintf("%d", otpData.OTP)
			if err = SendSMSTata(userID, tataOTPStruct); err != nil {
				logger.WithUser(userID).Error(err)
				return otpData.OTP, ErrGenericOTPDownMessage
			}
			return otpData.OTP, ""
		}
		if SendSMSMSG91(userID, mobile, text, dltTemplateDetails) {
			return otpData.OTP, ""
		}
		if SendSMSKarix(userID, mobile, text, dltTemplateDetails, isOTP) {
			return otpData.OTP, ""
		}
	} else {
		if sourceEntityID == constants.TataBNPLID {
			tataOTPStruct.Data.OTPNumber = fmt.Sprintf("%d", otpData.OTP)
			if err = SendSMSTata(userID, tataOTPStruct); err != nil {
				logger.WithUser(userID).Error(err)
				return otpData.OTP, ErrGenericOTPDownMessage
			}
			return otpData.OTP, ""
		}
		if SendSMSKarix(userID, mobile, text, dltTemplateDetails, isOTP) {
			return otpData.OTP, ""
		}
		if SendSMSMSG91(userID, mobile, text, dltTemplateDetails) {
			return otpData.OTP, ""
		}
	}
	return otpData.OTP, ErrGenericOTPDownMessage
}

// SendOTP sends OTP to given number and returns an error message if any
func SendOTP(mobile string, userID string, autoOTPReadHash string, otpType string, sourceEntityID string) (int, string) {
	return SendOTPWithAuthID(mobile, mobile, userID, autoOTPReadHash, otpType, sourceEntityID, "", &TataOTPReq{})
}

// VerifyOTPPanic verifies the OTP and panics in case of mismatch or any other error
func VerifyOTPPanic(ctx context.Context, mobile string, userID string, otpType string, otp int, lat, lon, height, accuracy, ipAddress, otpDataKey string) {
	valid, errorString := VerifyOTP(ctx, mobile, userID, otpType, otp, lat, lon, height, accuracy, ipAddress, otpDataKey)
	if !valid {
		log.Println(errorString)
		panic(errorString)
	}
}

// VerifyOTP verifies if otp entered is correct
func VerifyOTP(ctx context.Context, mobile string, userID string, otpType string, otp int, lat, lon, height, accuracy, ipAddress, otpDataKey string) (bool, string) {
	return VerifyOTPWithAuthID(ctx, mobile, mobile, userID, otpType, otp, lat, lon, height, accuracy, ipAddress, otpDataKey)
}

// VerifyOTPWithAuthID verifies if otp entered is correct
func VerifyOTPWithAuthID(ctx context.Context, authID string, mobile string, userID string, otpType string, otp int, lat, lon, height, accuracy, ipAddress, otpDataKey string) (bool, string) {

	// if ((!general.InArr(conf.ENV, []string{conf.ENV_PROD, conf.ENV_UAT + "7", conf.ENV_UAT_TDL, conf.ENV_DEV_TDL})) || general.InArr(mobile, prodTestingNumbers)) && otp == 0 {
	if ((!general.InArr(conf.ENV, []string{conf.ENV_PROD, conf.ENV_UAT3, conf.ENV_UAT, conf.ENV_UAT11})) || general.InArr(mobile, prodTestingNumbers)) && otp == 0 {
		return true, ""
	}

	var redisOTPAttemptDataKey = getRedisOTPAttemptDataKey(authID, otpType, otpDataKey)
	var otpConf = getOTPConfigs(otpType)
	otpAttemptData, err := checkOTPVerifyValidity(redisOTPAttemptDataKey, otpConf)

	if err != nil {
		if err.Error() == ErrOTPGenerationBlocked {
			return false, ErrOTPGenerationBlocked
		}
		log.Error(err)
		if conf.ENV != conf.ENV_PROD {
			return false, ErrIncorrectOTP
		}
		return false, ErrGenericOTPDownMessage
	}
	var redisOTPDataKey = getRedisOTPDataKey(authID, otpType, otpDataKey)
	otpData := fetchLastOTP(redisOTPDataKey)

	if otpData.OTP == -1 {
		errMsg := otpTypeErrorMap[otpType]
		if errMsg == "" {
			errMsg = ErrResendOTP
		}
		return false, errMsg
	}
	otpExpired := isOTPExpired(otpData.GeneratedAt, otpConf)

	if otpExpired && otpConf.MaxOTPValidityMinutes != -1 {
		err = redis.Delete(ctx, redisOTPDataKey)
		if err != nil {
			log.Error(err)
		}
		return false, ErrOTPExpired
	}

	if otpConf.MaxFailedAttempts != -1 && otpData.FailedAttempts > otpConf.MaxFailedAttempts {
		// too many failed verification attempts for current otp
		// OTP expired
		err = redis.Delete(ctx, redisOTPDataKey)
		if err != nil {
			log.Error(err)
		}
		if otpExpired {
			return false, ErrOTPExpired
		}
		return false, ErrMaxFailedAttempts
	}

	if otp == otpData.OTP {

		// log login OTP success attempt in db
		go legallogs.Save(userID, fmt.Sprintf("otp_type=%s&attempt=%d", otpType, otpData.FailedAttempts+1), lat, lon, height, accuracy, ipAddress)
		// delete redis key as OTP is no longer usable
		err = redis.Delete(ctx, redisOTPDataKey)
		if err != nil {
			log.Error("OTP VERIFICATION: delete otp post verification from redis failed ", err)
		}
		return true, ""
	} else {
		otpData.FailedAttempts += 1
	}

	if otpConf.MaxFailedAttempts != -1 && otpData.FailedAttempts > otpConf.MaxFailedAttempts {
		// update otpAttemptData and block user from otp generation for constants.OTPBlockedMinutes
		otpAttemptData.BlockedAt = time.Now().Format(redisOTPTimeFormat)
		updateRedisOTPAttempts(redisOTPAttemptDataKey, otpAttemptData)

		// This otp is no longer needed
		err = redis.Delete(ctx, redisOTPDataKey)
		if err != nil {
			log.Error(err)
		}
		return false, ErrMaxFailedAttempts
	} else {
		updateRedisOTP(redisOTPDataKey, otpData, otpConf)
	}
	return false, ErrIncorrectOTP
}

func SendSMSWithFallback(userID string, mobile string, text string, dltTemplateDetails karix.DltDetails, isOTP bool) (bool, error) {
	smsSent := false
	smsSent = SendSMSKarix(userID, mobile, text, dltTemplateDetails, isOTP)
	if !smsSent {
		smsSent = SendSMSMSG91(userID, mobile, text, dltTemplateDetails)
	}
	if !smsSent {
		err := fmt.Errorf("unable to send sms to userID:%s, with mobile - %s", userID, mobile)
		logger.WithUser(userID).Errorln(err)
		return false, err
	}
	return true, nil
}

// TODO will make it generic
// SendPhysicalMandateSMSAlert sends an sms to notify user about physical mandate status
func MuthootCLPhysicalMandateSMSAlert(userID, journeyWebSDKURL, name, mobile string, smsKarixDtls karix.DltDetails) {
	if mobile != "" {
		text := fmt.Sprintf(smsKarixDtls.DLTTemplateID, name, journeyWebSDKURL)
		_, err := SendSMSWithFallback(userID, mobile, text, smsKarixDtls, false)
		if err != nil {
			logger.WithUser(userID).Error(err)
		}
		log.Debug("Digio status sms sent successfully")
	}
}

// GenerateOTPWithAuthID generates OTP using authID as the unique identifier of the otp
func GenerateOTPWithAuthID(userID string, authID string, otpType string, otpConf *OtpConfigs, otpDataKey string) (int, string) {

	var redisOTPAttemptDataKey = getRedisOTPAttemptDataKey(authID, otpType, otpDataKey)
	if otpConf == nil {
		otpConfig := getOTPConfigs(otpType)
		otpConf = &otpConfig
	}

	otpAttemptValid, otpAttemptData, err := checkOTPAttemptValidity(redisOTPAttemptDataKey, *otpConf)
	if !otpAttemptValid {
		updateRedisOTPAttempts(redisOTPAttemptDataKey, otpAttemptData)
		logger.WithUser(userID).Error(err)
		return 0, err.Error()
	}

	var redisOTPDataKey = getRedisOTPDataKey(authID, otpType, otpDataKey)
	otpData := fetchLastOTP(redisOTPDataKey)

	if otpData.OTP == -1 || (otpData.SentCount != 0 && otpConf.MaxSameOTPUseCount != -1 && otpData.SentCount%otpConf.MaxSameOTPUseCount == 0) || (isOTPExpired(otpData.GeneratedAt, *otpConf) && otpConf.MaxOTPValidityMinutes != -1) {
		// otp is not found
		if !general.InArr(conf.ENV, []string{conf.ENV_PROD, conf.ENV_UAT, conf.ENV_DEV + "3", conf.ENV_DEV_TDL, conf.ENV_UAT_TDL, conf.ENV_UAT3, conf.ENV_UAT6, conf.ENV_UAT11}) {
			otpData.OTP = 0
		} else {
			otpData.OTP, err = general.RandomIntInRange(MinOTP, MaxOTP)
			if err != nil {
				logger.WithUser(userID).Error(err)
			}
		}
		otpData.GeneratedAt = time.Now().Format(redisOTPTimeFormat)
	}
	otpAttemptData.SentCount += 1
	otpAttemptData.UpdatedAt = time.Now().Format(redisOTPTimeFormat)

	otpData.SentCount += 1

	updateRedisOTP(redisOTPDataKey, otpData, *otpConf)
	updateRedisOTPAttempts(redisOTPAttemptDataKey, otpAttemptData)

	// if !general.InArr(conf.ENV, []string{conf.ENV_PROD, conf.ENV_UAT, conf.ENV_UAT_TDL, conf.ENV_UAT + "7", conf.ENV_DEV_TDL}) {
	if !general.InArr(conf.ENV, []string{conf.ENV_PROD, conf.ENV_UAT, conf.ENV_DEV_TDL, conf.ENV_UAT3, conf.ENV_UAT11}) {
		// don't send sms in environment other than uat and prod
		return 0, ""
	}

	return otpData.OTP, ""
}

// ClearOTPAttempts clears the Redis cache for OTP attempts for a given email/authID
func ClearOTPAttempts(userID string, ctx context.Context, authID string, otpType string, otpDataKey string) error {
    // Generate the same Redis keys that are used in VerifyOTPWithAuthID
    var redisOTPDataKey = getRedisOTPDataKey(authID, otpType, otpDataKey)
    var redisOTPAttemptDataKey = getRedisOTPAttemptDataKey(authID, otpType, otpDataKey)
    
    logger.WithUser(userID).Info(fmt.Sprintf("Clearing OTP Redis keys for authID: %s, otpType: %s", authID, otpType))
    logger.WithUser(userID).Info(fmt.Sprintf("Keys to clear: %s, %s", redisOTPDataKey, redisOTPAttemptDataKey))
    
    err := redis.Delete(ctx, redisOTPDataKey)
    if err != nil {
        logger.WithUser(userID).Error(fmt.Sprintf("Failed to delete redisOTPDataKey: %v", err))
        return err
    }
    
    err = redis.Delete(ctx, redisOTPAttemptDataKey)
    if err != nil {
        logger.WithUser(userID).Error(fmt.Sprintf("Failed to delete redisOTPAttemptDataKey: %v", err))
        return err
    }
    
    logger.WithUser(userID).Info(fmt.Sprintf("Successfully cleared OTP cache for authID: %s", authID))
    return nil
}
