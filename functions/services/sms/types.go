package sms

import (
	"finbox/go-api/conf"
	"finbox/go-api/constants"
	"finbox/go-api/thirdparty/karix"
)

type SendDLTSMSViaKarixReq struct {
	UserID      string           `json:"userID"`
	Mobile      string           `json:"mobile"`
	SMSBody     string           `json:"smsBody"`
	DLTTemplate karix.DltDetails `json:"dltTemplate"`
}

type messageStruct struct {
	Destination   []string `json:"dest"`
	Text          string   `json:"text"`
	Type          string   `json:"type"`
	SenderID      string   `json:"send"`
	DltEntityID   string   `json:"dlt_entity_id"`
	DltTemplateID string   `json:"dlt_template_id"`
}

type reqStruct struct {
	Version  string          `json:"ver"`
	Key      string          `json:"key"`
	Encrypt  string          `json:"encrypt"`
	Messages []messageStruct `json:"messages"`
}

type statusStruct struct {
	Code        string `json:"code"`
	Description string `json:"desc"`
}

type karixRespStruct struct {
	Status statusStruct `json:"status"`
	Ackid  string       `json:"ackid"`
	Time   string       `json:"time"`
}

type respStuct struct {
	Message string `json:"message"`
	Type    string `json:"type"`
}

type otpDataStruct struct {
	OTP            int
	GeneratedAt    string
	SentCount      int
	FailedAttempts int
}

type otpAttemptDataStruct struct {
	SentCount int
	CreatedAt string
	UpdatedAt string
	BlockedAt string
}

type OtpConfigs struct {
	MaxOTPLimitPerHour    int
	MaxSameOTPUseCount    int
	MaxOTPValidityMinutes int
	MaxFailedAttempts     int
	OTPBlockedMinutes     int
}

type TataOTPData struct {
	EventType    string `json:"event_type"`
	CustomerHash string `json:"customer_hash"`
	MerchantName string `json:"merchant_name"`
	OTPNumber    string `json:"otp_number"`
	TxnAmount    string `json:"txn_amount"`
	TxnTimestamp string `json:"txn_timestamp"`
	ValidTill    string `json:"valid_till"`
	KFSURL       string `json:"kfs_url"`
}

type TataOTPReq struct {
	Data TataOTPData `json:"data"`
}

type tataOTPResp struct {
	Status    string      `json:"status"`
	Data      interface{} `json:"data"`
	ErrorInfo struct {
		ErrorCode    string `json:"errorCode"`
		ErrorMessage string `json:"errorMessage"`
	} `json:"errorInfo"`
}

var txnConfigs = OtpConfigs{
	MaxOTPLimitPerHour:    3,
	MaxSameOTPUseCount:    3,
	MaxOTPValidityMinutes: 3,
	MaxFailedAttempts:     4,
	OTPBlockedMinutes:     -1,
}

var pflEmailConfigs = OtpConfigs{
	MaxOTPLimitPerHour:    -1,
	MaxSameOTPUseCount:    3,
	MaxOTPValidityMinutes: 5,
	MaxFailedAttempts:     3,
	OTPBlockedMinutes:     -1,
}

var pflPersonalEmailConfigs = OtpConfigs{
	MaxOTPLimitPerHour:    -1,
	MaxSameOTPUseCount:    3,
	MaxOTPValidityMinutes: 1,
	MaxFailedAttempts:     3,
	OTPBlockedMinutes:     -1,
}

var abflplEmailConfigs = OtpConfigs{
	MaxOTPLimitPerHour:    -1,
	MaxSameOTPUseCount:    3,
	MaxOTPValidityMinutes: 5,
	MaxFailedAttempts:     3,
	OTPBlockedMinutes:     -1,
}

var mflblEmailConfigs = OtpConfigs{
	MaxOTPLimitPerHour:    -1,
	MaxSameOTPUseCount:    3,
	MaxOTPValidityMinutes: 5,
	MaxFailedAttempts:     -1,
	OTPBlockedMinutes:     -1,
}

var paylaterLinkConfig = OtpConfigs{
	MaxOTPLimitPerHour:    3,
	MaxSameOTPUseCount:    3,
	MaxOTPValidityMinutes: 3,
	MaxFailedAttempts:     4,
	OTPBlockedMinutes:     -1,
}

var defaultConfigs = OtpConfigs{
	MaxOTPLimitPerHour:    10,
	MaxSameOTPUseCount:    10,
	MaxOTPValidityMinutes: 5,
	MaxFailedAttempts:     5,
	OTPBlockedMinutes:     10,
}

var otpConfigsMapping = map[string]OtpConfigs{
	constants.OTPTypeTransaction:              txnConfigs,
	constants.OTPTypePayLaterLinkAccount:      paylaterLinkConfig,
	constants.OTPPflEmailVerification:         pflEmailConfigs,
	constants.OTPABFLPLEmailVerification:      abflplEmailConfigs,
	constants.OTPMFLBLEmailVerification:       mflblEmailConfigs,
	constants.OTPPflPersonalEmailVerification: pflPersonalEmailConfigs,
}

// type DltDetails struct {
// 	Sender                string
// 	DLTID                 string
// 	DLTEntityID           string
// 	DLTTemplateID         string
// 	IgnoreAutoOtpReadHash bool
// }

var defaultDLTDetails = karix.DltDetails{
	Sender:                conf.Sender,
	DLTID:                 conf.DLTID,
	DLTEntityID:           "1707161519691441441",
	DLTTemplateID:         "<#> Your FinBox Lending verification code is %s\n%s",
	IgnoreAutoOtpReadHash: false,
}

var tdlCapitalfloatDltDetails = karix.DltDetails{
	Sender:                conf.Sender,
	DLTID:                 conf.DLTID,
	DLTEntityID:           "1707161519691441441",
	DLTTemplateID:         "<#> Your FinBox Lending verification code is %s\n%s",
	IgnoreAutoOtpReadHash: false,
}

var iiflDltDetails = karix.DltDetails{
	Sender:                conf.IIFLSender,
	DLTID:                 conf.IIFLDLTID,
	DLTEntityID:           "1107168025536794344",
	DLTTemplateID:         "<#> Your verification code is %s\n-IIFLFN",
	IgnoreAutoOtpReadHash: true,
}

var abflDltDetails = karix.DltDetails{
	Sender:                conf.ABFLSender,
	DLTID:                 conf.ABFLDLTID,
	DLTEntityID:           "1107174100150239492",
	DLTTemplateID:         "Dear Customer,\nYour OTP for login request is  %s.\nPlease do NOT share this OTP with anyone.\nRegards,\n-Aditya Birla Capital Limited",
	IgnoreAutoOtpReadHash: true,
}

var abflplDltDetails = karix.DltDetails{
	Sender:                conf.ABFLSender,
	DLTID:                 conf.ABFLDLTID,
	DLTEntityID:           "1107174108075545577",
	DLTTemplateID:         "Dear %s,\nYour OTP to complete the loan agreement execution process with Aditya Birla Capital Limited is %s.\nRegards,\nAditya Birla Capital Limited",
	IgnoreAutoOtpReadHash: true,
}

var PflDltDetails = karix.DltDetails{
	Sender:                conf.PFLSender,
	DLTID:                 conf.PFLDLTID,
	DLTEntityID:           "1107168691963075790",
	DLTTemplateID:         "%s is your one time password for verification and is valid for 5 mins. Please do not share this OTP with anyone.\nPoonawalla Fincorp",
	IgnoreAutoOtpReadHash: true,
}

var AgreementDltDetails = karix.DltDetails{
	Sender:                conf.Sender,
	DLTID:                 conf.DLTID,
	DLTEntityID:           conf.AgreementTemplateID,
	IgnoreAutoOtpReadHash: false,
}

var ResetPasswordDltDetails = karix.DltDetails{
	Sender:                conf.Sender,
	DLTID:                 conf.DLTID,
	DLTEntityID:           conf.ResetPasswordTemplateID,
	IgnoreAutoOtpReadHash: false,
}

var SendSessionLinkDltDetailsPreApproved = karix.DltDetails{
	Sender:                conf.MuthootCLSender,
	DLTID:                 conf.MuthootCLPrincipalEntityID,
	DLTEntityID:           conf.MFLPreAppSMSTemplateID,
	IgnoreAutoOtpReadHash: false,
}

var SendSessionLinkDltDetailsNTM = karix.DltDetails{
	Sender:                conf.MuthootCLSender,
	DLTID:                 conf.MuthootCLPrincipalEntityID,
	DLTEntityID:           conf.MFLNTMSMSTemplateID,
	IgnoreAutoOtpReadHash: false,
}

var BulkAddSDSADltDetails = karix.DltDetails{
	Sender:                conf.Sender,
	DLTID:                 conf.DLTID,
	DLTEntityID:           conf.BulkAddSDSATemplateID,
	IgnoreAutoOtpReadHash: false,
}

var MuthootCLOTPDLTDetails = karix.DltDetails{
	Sender:                conf.MuthootCLSender,
	DLTID:                 conf.MuthootCLPrincipalEntityID,
	DLTEntityID:           "1107168855333579281",
	DLTTemplateID:         "Hi, %s is your OTP to verify your mobile number. This OTP is valid for 10 mins - Muthoot Fincorp Limited.",
	IgnoreAutoOtpReadHash: true,
}

var MFLOTPDLTDetails = karix.DltDetails{
	Sender:                conf.MuthootCLSender,
	DLTID:                 conf.MuthootCLPrincipalEntityID,
	DLTEntityID:           "1107172588009030507",
	DLTTemplateID:         "Hi %s, Your one-time password (OTP) is %s. Please use this OTP to e-sign your Business Loan documents. - Muthoot Fincorp Limited",
	IgnoreAutoOtpReadHash: true,
}

var MuthootCLNotEligible = karix.DltDetails{
	Sender:                conf.MuthootCLSender,
	DLTID:                 conf.MuthootCLPrincipalEntityID,
	DLTEntityID:           "1107168855406949437",
	DLTTemplateID:         "Hi %s, you are not eligible for a MSME business loan from Muthoot FinCorp Ltd at this moment. Please try reapplying after 90-days with a fresh application - Muthoot Fincorp Limited.",
	IgnoreAutoOtpReadHash: true,
}

var ABFLVkycVerification = karix.DltDetails{
	Sender:                conf.ABFLSender,
	DLTID:                 conf.ABFLDLTID,
	DLTEntityID:           "1107171800067238853",
	IgnoreAutoOtpReadHash: false,
}

var MFLVkycVerification = karix.DltDetails{
	Sender:                conf.MuthootCLSender,
	DLTID:                 conf.MuthootCLPrincipalEntityID,
	DLTEntityID:           conf.MFLVkycTemplateID,
	IgnoreAutoOtpReadHash: false,
}

var MuthootCLVerificationPending = karix.DltDetails{
	Sender:                conf.MuthootCLSender,
	DLTID:                 conf.MuthootCLPrincipalEntityID,
	DLTEntityID:           "1107168855432044408",
	DLTTemplateID:         "Hi %s, your MSME business loan application with Muthoot FinCorp Ltd is now complete. A physical verification of your business premises will be conducted, post which your credit limit of %s will be activated - Muthoot FinCorp Ltd.",
	IgnoreAutoOtpReadHash: true,
}

var MuthootCLPaymentVerification = karix.DltDetails{
	Sender:                conf.MuthootCLSender,
	DLTID:                 conf.MuthootCLPrincipalEntityID,
	DLTEntityID:           "1107169719848058999",
	DLTTemplateID:         "Hi %s, We have successfully received a repayment of Rs. %s against your loan with Muthoot Fincorp Limited. We would like to inform you that your credit limit has been updated to Rs. %s - Muthoot Fincorp Ltd.",
	IgnoreAutoOtpReadHash: true,
}

var MuthootCLInsurance = karix.DltDetails{
	Sender:                conf.MuthootCLSender,
	DLTID:                 conf.MuthootCLPrincipalEntityID,
	DLTEntityID:           "1107171826089370030",
	DLTTemplateID:         "Dear Customer, below is the link to access policy document for Insurance premium collected against your Loan no. %s. Click here - %s . For any queries, please reach <NAME_EMAIL>-Muthoot FinCorp Ltd",
	IgnoreAutoOtpReadHash: true,
}

var MuthootPostDisbursement = karix.DltDetails{
	Sender:                conf.MuthootCLSender,
	DLTID:                 conf.MuthootCLPrincipalEntityID,
	DLTEntityID:           "1107172725053287257",
	DLTTemplateID:         "Dear Customer, Congratulations on the successful disbursement of your Loan %s with Muthoot Fincorp Limited through %s. As part of our enhanced verification process, you will be contacted by one of our executives, followed by a visit from our field team in the coming days. We kindly request you to keep the required documents/details ready to complete the verification process. Thank you for choosing us. - Muthoot Fincorp Limited",
	IgnoreAutoOtpReadHash: true,
}

var mflBLConsentOtpDltDetails = karix.DltDetails{
	Sender:                conf.MFLBLSender,
	DLTID:                 conf.MFLBLPrincipalEntityID,
	DLTEntityID:           "1107172588004269807",
	DLTTemplateID:         "Hi %s, Your one-time password (OTP) is %s. Use this OTP to verify your mobile number and initiate your business loan journey. This OTP is valid for 10 minutes. -  Muthoot Fincorp Limited",
	IgnoreAutoOtpReadHash: true,
}

var dltDetailsMapping = map[string]karix.DltDetails{
	constants.TataBNPLID + "_" + constants.CapitalFloatID: tdlCapitalfloatDltDetails,
	constants.IIFLID:              iiflDltDetails,
	constants.MuthootCLID:         MuthootCLOTPDLTDetails,
	constants.ABFLID:              abflDltDetails,
	constants.ABFLPLID:            abflplSigninDltDetails,
	constants.PoonawallaFincorpID: PflDltDetails,
	constants.MFLBLID + "_" + "Privacy_Policy_Consent": mflBLConsentOtpDltDetails,
	constants.MFLBLID + "_" + "esign_otp":              MFLOTPDLTDetails,
	constants.MFLBLID + "_" + constants.OTPTypeSignin:  mflBLConsentOtpDltDetails,
	constants.ABFLPLID + "_" + constants.OTPTypeESign:  abflplDltDetails,
}

const (
	MinOTP = 100000
	MaxOTP = 999999
)

var otpTypeErrorMap = map[string]string{
	constants.OTPTypeTransaction: constants.ErrAuthIDInvalidState,
}

var ApproveCreditlineLimitDltDetails = karix.DltDetails{
	Sender:                conf.MuthootCLSender,
	DLTID:                 conf.MuthootCLPrincipalEntityID,
	DLTEntityID:           "1107168855435762713",
	DLTTemplateID:         "Congratulations %s! The physical verification of your business premises has been completed successfully and your credit limit is activated for use - Muthoot FinCorp Ltd.",
	IgnoreAutoOtpReadHash: true,
}

var RejectCreditlineLimitDltDetails = karix.DltDetails{
	Sender:                conf.MuthootCLSender,
	DLTID:                 conf.MuthootCLPrincipalEntityID,
	DLTEntityID:           "1107169156763323185",
	DLTTemplateID:         "Hi %s, your Loan disbursal request has failed. <NAME_EMAIL> for assistance - Muthoot FinCorp Ltd.",
	IgnoreAutoOtpReadHash: true,
}

var WithdrawalCreatedDltDetails = karix.DltDetails{
	Sender:                conf.MuthootCLSender,
	DLTID:                 conf.MuthootCLPrincipalEntityID,
	DLTEntityID:           "1107168855466082784",
	DLTTemplateID:         "Hi %s, your MSME business loan disbursal request for Rs. %.2f has been raised successfully. Your updated credit limit is Rs. %.2f - Muthoot FinCorp Ltd",
	IgnoreAutoOtpReadHash: true,
}

var DisbursalAlertCustomerDltDetails = karix.DltDetails{
	Sender:                conf.MuthootCLSender,
	DLTID:                 conf.MuthootCLPrincipalEntityID,
	DLTEntityID:           "1107169719854138128",
	DLTTemplateID:         "Hi, An amount of Rs. %.2f is disbursed against an invoice to %s. Your available credit limit is Rs. %.2f. Please reach out to <NAME_EMAIL> in case of any queries - Muthoot Fincorp Ltd.",
	IgnoreAutoOtpReadHash: true,
}

var DisbursalAlertMerchantDltDetails = karix.DltDetails{
	Sender:                conf.MuthootCLSender,
	DLTID:                 conf.MuthootCLPrincipalEntityID,
	DLTEntityID:           "1107169832561505001",
	DLTTemplateID:         "Hi, An amount of Rs. %.2f has been paid to you by %s against the invoice no. %s. Please reach out to <NAME_EMAIL> in case of any queries - Muthoot Fincorp Ltd.",
	IgnoreAutoOtpReadHash: true,
}

var DisbursalAlertCustomerMFLDltDetails = karix.DltDetails{
	Sender:                conf.MuthootCLSender,
	DLTID:                 conf.MuthootCLPrincipalEntityID,
	DLTEntityID:           "1107170973213047334",
	DLTTemplateID:         "Hi %s, your Term Loan of Rs.%s is approved. Click here for Loan agreement and related documents %s - Muthoot FinCorp Ltd.",
	IgnoreAutoOtpReadHash: true,
}

var CreditLineActivationAlertMFLDltDetails = karix.DltDetails{
	Sender:                conf.MuthootCLSender,
	DLTID:                 conf.MuthootCLPrincipalEntityID,
	DLTEntityID:           "1107170973207022052",
	DLTTemplateID:         "Hi %s, your Supply Chain Finance Credit line of Rs.%.2f is approved. Click here for Loan agreement and related documents %s - Muthoot FinCorp Ltd.",
	IgnoreAutoOtpReadHash: true,
}

var CreditLineDisbursalChangeAlertMFLDltDetails = karix.DltDetails{
	Sender:                conf.MuthootCLSender,
	DLTID:                 conf.MuthootCLPrincipalEntityID,
	DLTEntityID:           "1107170973201468112",
	DLTTemplateID:         "Hi, based on our Review, your revised Supply Chain Finance Credit Line limit is Rs.%.2f. Loan/credit terms and conditions are the same, and any extra processing fees charged by us will be refunded to your account in 7 working days. Please feel free to reach <NAME_EMAIL> - Muthoot FinCorp Ltd.",
	IgnoreAutoOtpReadHash: true,
}

var NachSuccessAlertMFDltDetails = karix.DltDetails{
	Sender:                conf.MuthootCLSender,
	DLTID:                 conf.MuthootCLPrincipalEntityID,
	DLTEntityID:           "1107171231065707466",
	DLTTemplateID:         "Hi %s, your Physical NACH is approved. Click here to complete the loan application %s - Muthoot FinCorp Ltd",
	IgnoreAutoOtpReadHash: true,
}

var NachRejectAlertMFDltDetails = karix.DltDetails{
	Sender:                conf.MuthootCLSender,
	DLTID:                 conf.MuthootCLPrincipalEntityID,
	DLTEntityID:           "1107171231072586656",
	DLTTemplateID:         "Hi %s, Your Physical NACH has been rejected by the bank. Click here to retry the journey %s - Muthoot FinCorp Ltd",
	IgnoreAutoOtpReadHash: true,
}

var ABFLPLAggrementMsgDetails = karix.DltDetails{
	Sender:                conf.ABFLPLSender,
	DLTID:                 conf.ABFLPLDLTID,
	DLTEntityID:           "1107167810295889068",
	DLTTemplateID:         "Dear Customer, \nWelcome to Aditya Birla Capital Limited. Please find below a link where you can access your digitally signed Sanction Letter, Welcome Letter, Key Fact statement, repayment schedule.\nThe password to access these documents is a combination of your Date of Birth and PAN. For example: If your birth date is 2 June 1970 and your primary PAN is **********, then the password is 02061970**********\nLink: %s\nRegards, -Aditya Birla Capital Limited",
	IgnoreAutoOtpReadHash: true,
}

var abflplSigninDltDetails = karix.DltDetails{
	Sender:                conf.ABFLSender,
	DLTID:                 conf.ABFLDLTID,
	DLTEntityID:           "1107174851303687184",
	DLTTemplateID:         "Dear Valued Customer, \nThank you for choosing Udyog Plus!\nEnter OTP  %s to continue your Personal Loan Journey. \nRegards, \n-Aditya Birla Capital Limited",
	IgnoreAutoOtpReadHash: true,
}
