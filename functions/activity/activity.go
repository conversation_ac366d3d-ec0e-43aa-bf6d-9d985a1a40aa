package activity

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"finbox/go-api/async/producer"
	"finbox/go-api/conf"
	"finbox/go-api/constants"
	"finbox/go-api/errorHandler"
	"finbox/go-api/functions/journey"
	"finbox/go-api/functions/lenders/poonawalla"
	"finbox/go-api/functions/logger"
	"finbox/go-api/functions/partner/supermoney"
	"finbox/go-api/functions/requestutils"
	"finbox/go-api/functions/retry"
	"finbox/go-api/functions/services/pincodeapi"
	"finbox/go-api/functions/serviceslib"
	"finbox/go-api/functions/taskmanagement"
	eventpkg "finbox/go-api/functions/taskmanagement/event"
	"finbox/go-api/functions/taskmanagement/model"
	"finbox/go-api/functions/tracer"
	"finbox/go-api/functions/webhooks"
	"finbox/go-api/infra/db"
	taskmanagementsql "finbox/go-api/internal/repository/psql/taskmanagement"
	"finbox/go-api/models/dashboard"
	dashboardModel "finbox/go-api/models/dashboard"
	"finbox/go-api/models/entitylogs"
	"finbox/go-api/models/featureflag"
	"finbox/go-api/models/featureflagsettings"
	"finbox/go-api/models/loanapplication"
	"finbox/go-api/models/octopuswebhook"
	"finbox/go-api/models/personalloanoffer"
	"finbox/go-api/models/users"
	"finbox/go-api/models/webhooklogs"
	"finbox/go-api/requestHandler/customRoutes"
	"finbox/go-api/utils/general"
	"finbox/go-api/utils/iiflutils"
	topuputils "finbox/go-api/utils/topup"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"maps"

	"github.com/hibiken/asynq"
	"github.com/jmoiron/sqlx"
	"github.com/spf13/cast"
)

var database = db.GetDB()
var log = logger.Log
var RedisKey = "_webhook_events_list"

const MaxTry = 3

type WebhookStruct struct {
	SourceEntityID  string   `db:"source_entity_id" json:"source_entity_id"`
	WebhookURL      string   `db:"webhookurl" json:"webhook_url"`
	ServiceName     string   `db:"servicename" json:"service_name"`
	HeaderValuePair string   `db:"headervaluepair" json:"header_value_pair"`
	Events          []string `db:"events" json:"events"`
	SelectionMode   string   `db:"selection_mode" json:"selection_mode"`
	IsActive        bool     `db:"is_active" json:"is_active"`
}
type ActivityLogForAPI struct {
	UserID            string `json:"userID"`
	SourceEntityID    string `json:"sourceEntityID"`
	LoanApplicationID string `json:"loanApplicationID"`
	EntityType        string `json:"entityType"`
	EntityRef         string `json:"entityRef"`
	EventType         string `json:"eventType"`
	Description       string `json:"eventDescription"`
	LoggedAt          string `json:"loggedAt"`
}

type ActivityEvent struct {
	UserID            string                 `json:"user_id"`
	SourceEntityID    string                 `json:"source_entity_id"`
	LoanApplicationID string                 `json:"loan_application_id"`
	EntityType        string                 `json:"entity_type"`
	EntityRef         string                 `json:"entity_ref"`
	EventType         string                 `json:"event_type"`
	Description       string                 `json:"description"`
	Metadata          map[string]interface{} `json:"metadata"`
	DateTime          string                 `json:"date_time"`

	// ModuleName is an optional field which is set under rejection_logs and can be passed if a user is supposed to be rejected.
	// This signifies which module rejected the user. This can be used to make decisions based on the module the user was rejected at.
	ModuleName string `json:"moduleName"`
}

type WebhookEventData struct {
	WebhookURL        string `json:"webhook_url"`
	ServiceName       string `json:"service_name"`
	HeaderValuePair   string `json:"header_value_pair"`
	UserID            string `json:"user_id"`
	SourceEntityID    string `json:"source_entity_id"`
	LoanApplicationID string `json:"loan_application_id"`
	EntityType        string `json:"entity_type"`
	EntityRef         string `json:"entity_ref"`
	EventType         string `json:"event_type"`
	Description       string `json:"description"`
	DateTime          string `json:"date_time"`
}

func (a *ActivityEvent) Stringify() string {
	if a == nil {
		return "{}"
	}
	webhookBytes, err := json.Marshal(a)
	if err != nil {
		log.Errorln(err)
		return "{}"
	}
	return string(webhookBytes)
}

type EntityEvent struct {
	EntityID       string
	UniqueID       string
	OrganizationID string
	EventType      string
	EntityType     string
	EntityRef      string
	Description    string
	LoggedAt       string
}

type ActivityEventNullable struct {
	UserID            sql.NullString
	SourceEntityID    sql.NullString
	LoanApplicationID sql.NullString
	EntityType        sql.NullString
	EntityRef         sql.NullString
	EventType         sql.NullString
	Description       sql.NullString
	Metadata          map[string]interface{}
}

type ActivityWebhookEvent struct {
	ID            string
	EventType     string
	EventLoggedAt string
	MetaData      map[string]interface{}
}

func (a *ActivityWebhookEvent) Stringify() string {
	if a == nil {
		return "{}"
	}
	webhookBytes, err := json.Marshal(a)
	if err != nil {
		log.Errorln(err)
		return "{}"
	}
	return string(webhookBytes)
}

func convertEmptyItemsToNull(e *ActivityEvent) *ActivityEventNullable {
	var event ActivityEventNullable

	if e.UserID == "" {
		event.UserID = sql.NullString{String: "", Valid: false}
	} else {
		event.UserID = sql.NullString{String: e.UserID, Valid: true}
	}

	if e.SourceEntityID == "" {
		event.SourceEntityID = sql.NullString{String: "", Valid: false}
	} else {
		event.SourceEntityID = sql.NullString{String: e.SourceEntityID, Valid: true}
	}

	if e.LoanApplicationID == "" {
		event.LoanApplicationID = sql.NullString{String: "", Valid: false}
	} else {
		event.LoanApplicationID = sql.NullString{String: e.LoanApplicationID, Valid: true}
	}

	if e.EntityType == "" {
		event.EntityType = sql.NullString{String: "", Valid: false}
	} else {
		event.EntityType = sql.NullString{String: e.EntityType, Valid: true}
	}

	if e.EntityRef == "" {
		event.EntityRef = sql.NullString{String: "", Valid: false}
	} else {
		event.EntityRef = sql.NullString{String: e.EntityRef, Valid: true}
	}

	if e.EventType == "" {
		event.EventType = sql.NullString{String: "", Valid: false}
	} else {
		event.EventType = sql.NullString{String: e.EventType, Valid: true}
	}

	if e.Description == "" {
		event.Description = sql.NullString{String: "", Valid: false}
	} else {
		event.Description = sql.NullString{String: e.Description, Valid: true}
	}

	return &event
}

// RegisterAuxiliaryEvent registers auxiliary event to aux_activity_log table
// These are events that we don't want to show in the activity log on the dashboard
// but might need for other analytics - for example - finding out when the banner is clicked
func RegisterAuxiliaryEvent(event *ActivityEvent, dateTime string) error {

	query := `
    insert into aux_activity_log
    (log_id, user_id, source_entity_id, loan_application_id, entity_type, entity_ref, logged_at, event_type, event_description)
    values (uuid_generate_v4(), $1, $2, $3, $4, $5, $6, $7, $8)
	`
	e := convertEmptyItemsToNull(event)

	if event.Metadata != nil {
		bytes, _ := json.Marshal(map[string]interface{}{
			"description": event.Description,
			"metadata":    event.Metadata,
		})
		e.Description.String = string(bytes)
		e.Description.Valid = true
	}

	_, err := database.Exec(query, e.UserID, e.SourceEntityID, e.LoanApplicationID, e.EntityType, e.EntityRef, dateTime, e.EventType, e.Description)
	if err != nil {
		log.Println(err)
		log.Println(fmt.Sprintf("%+v", event))
		return err
	}

	return nil
}

func enqueuWebhookEvents(event *WebhookEventData) {
	opts := []asynq.Option{
		asynq.Queue(constants.QueueNameWebhookEvents),
		asynq.Timeout(2 * time.Minute),
	}
	logger.WithUser(event.UserID).Infof("sent events to queue, \n%v", event)
	err := producer.CreateNewtaskAndEnqueue(context.Background(), constants.TaskWebhookEvents, event, 3, opts)
	if err != nil {
		log.Errorf("error while enquing the webhook task to queue, err: %v", err)
		errorHandler.ReportToSentryWithoutRequest(err)
	}
}

// no code usage?
func ConsumeAndProcessWebhook(event *WebhookEventData) (err error) {
	if event.ServiceName == GenericWebhookService {
		switch {
		case event.SourceEntityID == constants.TataBNPLID:
			err = webhooks.TriggerTataWebhook(event.WebhookURL, event.ServiceName, event.HeaderValuePair, event.UserID, event.SourceEntityID, event.LoanApplicationID, event.EntityType, event.EntityRef, event.EventType, event.Description, event.DateTime)
		case event.SourceEntityID == constants.TataPLID:
			err = HitActivityWebhook(WebhookStruct{
				WebhookURL:      event.WebhookURL,
				ServiceName:     event.ServiceName,
				HeaderValuePair: event.HeaderValuePair,
			}, ActivityEvent{
				UserID:            event.UserID,
				SourceEntityID:    event.SourceEntityID,
				LoanApplicationID: event.LoanApplicationID,
				EntityType:        event.EntityType,
				EntityRef:         event.EntityRef,
				EventType:         event.EventType,
				Description:       event.Description,
				DateTime:          event.DateTime,
			}, event.DateTime, event.ServiceName)
		case journey.IsABFLBLSourcing(event.SourceEntityID):
			err = webhooks.TriggerABFLWebhook(event.WebhookURL, event.ServiceName, event.HeaderValuePair, event.UserID, event.SourceEntityID, event.LoanApplicationID, event.EntityType, event.EntityRef, event.EventType, event.Description, event.DateTime)
		case event.SourceEntityID == constants.MoneyControlID:
			err = webhooks.TriggerMoneyControlWebhook(event.WebhookURL, event.ServiceName, event.HeaderValuePair, event.UserID, event.SourceEntityID, event.LoanApplicationID, event.EntityType, event.EntityRef, event.EventType, event.Description, event.DateTime)
		// case event.SourceEntityID == constants.DSAMoneyControlIncredID:
		// 	err = webhooks.TriggerDSAMoneyControlIncredWebhook(event.WebhookURL, event.ServiceName, event.HeaderValuePair, event.UserID, event.SourceEntityID, event.LoanApplicationID, event.EntityType, event.EntityRef, event.EventType, event.Description, event.DateTime)
		default:
			err = HitActivityWebhook(WebhookStruct{
				WebhookURL:      event.WebhookURL,
				ServiceName:     event.ServiceName,
				HeaderValuePair: event.HeaderValuePair,
			}, ActivityEvent{
				UserID:            event.UserID,
				SourceEntityID:    event.SourceEntityID,
				LoanApplicationID: event.LoanApplicationID,
				EntityType:        event.EntityType,
				EntityRef:         event.EntityRef,
				EventType:         event.EventType,
				Description:       event.Description,
				DateTime:          event.DateTime,
			}, event.DateTime, event.ServiceName)
		}
	}
	return err
}

var ActivtityEventToTaskEvent = map[string]taskmanagementsql.Event{
	constants.ActivityLoanApplicationCreated: taskmanagementsql.LoanApplicationCreated,
	constants.ActivityDocumentsUpload:        taskmanagementsql.MediaAdded,
	constants.DDECompleted:                   taskmanagementsql.DDECompleted,
}

func registerTaskmanagementEvent(event *ActivityEvent) (err error) {
	if !featureflag.Get(constants.ABFLID, dashboard.FlagRegisterTaskEvent) {
		return
	}
	var (
		eventName taskmanagementsql.Event
		ok        bool
	)

	if eventName, ok = ActivtityEventToTaskEvent[event.EventType]; !ok {
		return
	}

	jsonData, err := json.Marshal(event.Metadata)
	if err != nil {
		logger.WithUser(event.UserID).Errorf("[registerTaskmanagementEvent] error in json marshal for taskmanagement : %s\n", err)
		return
	}

	taskManagementService := taskmanagement.NewTaskManagementService(eventpkg.Factory)
	err = taskManagementService.HandleEvent(context.Background(), event.EntityRef, dashboardModel.LoanApplicationID.String(), event.LoanApplicationID, model.Event{
		Event:     eventName.String(),
		EventData: jsonData,
	})

	if err != nil {
		logger.WithUser(event.UserID).Errorf("[registerTaskmanagementEvent] error in handle event in taskmanagement : %s\n", err)
		return
	}
	return
}

func RegisterEvent(event *ActivityEvent, dateTime string) (err error) {
	defer errorHandler.RecoveryNoResponse()
	go registerTaskmanagementEvent(event)

	event.DateTime = dateTime
	logID := general.GetUUID()
	query := `
    insert into activity_log
    (log_id, user_id, source_entity_id, loan_application_id, entity_type, entity_ref, logged_at, event_type, event_description)
    values ($1, $2, $3, $4, $5, $6, $7, $8, $9)
	`
	logger.WithUser(event.UserID).Debugf("[RegisterEvent] event %v", event)

	e := convertEmptyItemsToNull(event)
	_, err = database.Exec(query, logID, e.UserID, e.SourceEntityID, e.LoanApplicationID, e.EntityType, e.EntityRef, dateTime, e.EventType, e.Description)
	if err != nil {
		err = fmt.Errorf("could not fire activity_log for userID: %s, eventType: %s, %+v -> error %v", event.UserID, event.EventType, event, err)
		errorHandler.ReportToSentryWithoutRequest(err)
		logger.WithUser(event.UserID).Errorln(err)
		return
	}
	repeatLoanEligibile, maxDisbursedLoanCount, _ := journey.EnableRepeatLoan(e.SourceEntityID.String)
	if general.InArr(event.EventType, []string{constants.ActivityLoanRejected, constants.ActivityUserDisqualified}) {
		RegisterRejection(logID, event, dateTime)

		if toExpire, expiryType, expiryOffset := journey.ExpireUsersOnDisqualification(e.SourceEntityID.String, constants.ActivityUserDisqualified, e.Description.String); toExpire {
			query = fmt.Sprintf(`INSERT INTO expiry(expiry_id, user_id, expiry_type, event_type, created_at, expiry_at) VALUES (uuid_generate_v4(), $1, $2, $3, NOW(), NOW() + interval '30 days') 
					ON CONFLICT (user_id, expiry_type) DO
					UPDATE SET expiry_at = NOW() + interval '%d days'`, expiryOffset)
			_, err = database.Exec(query, e.UserID.String, expiryType, constants.ActivityUserDisqualified)
			if err != nil {
				errorHandler.ReportToSentryWithoutRequest(err)
				logger.WithUser(event.UserID).Errorln(err)
				return
			}
		}

		if repeatLoanEligibile {
			switch event.EventType {
			case constants.ActivityUserDisqualified:
				err = topuputils.SetEligibilityStatusRepeatLoan(e.UserID.String, constants.TopupDisqualified)
				if err != nil {
					errorHandler.ReportToSentryWithoutRequest(err)
					logger.WithUser(event.UserID).Errorln(err)
					//return (?)
				}
			case constants.ActivityLoanRejected:
				//TOOD: Add check on other loan offer types as well to make this generic
				if hasValidOffers, _ := personalloanoffer.HasValidOffer(event.UserID); !hasValidOffers {
					err = topuputils.SetEligibilityStatusRepeatLoan(e.UserID.String, constants.TopupDisqualified)
					if err != nil {
						errorHandler.ReportToSentryWithoutRequest(err)
						logger.WithUser(event.UserID).Errorln(err)
						//return (?)
					}
				}
			}
		}
	}

	logger.WithUser(event.UserID).Debugf("[RegisterEvent] repeatloan: %v <---------------> repeatLoanEligibile: %v <---------------> maxDisbursedLoanCount: %v", e, repeatLoanEligibile, maxDisbursedLoanCount)

	if repeatLoanEligibile && event.EventType == constants.ActivityLoanDisbursed {

		logger.WithUser(event.UserID).Debugln("True: repeatLoanEligibile")

		disbursedCount := 0
		query = `SELECT count(*) FROM loan_application WHERE user_id = $1 and status = $2`
		err = database.Get(&disbursedCount, query, e.UserID.String, constants.LoanStatusDisbursed)
		if err != nil {
			errorHandler.ReportToSentryWithoutRequest(err)
			logger.WithUser(event.UserID).Errorln(err)
			return
		}
		logger.WithUser(event.UserID).Debugf("[RegisterEvent] disbursedCount: %d", disbursedCount)
		// TODO: this handling will not work for maxDisbursedLoanCount > 2. Improve this.
		if disbursedCount > 1 && disbursedCount >= maxDisbursedLoanCount {
			logger.WithUser(event.UserID).Debugln("True: disbursedCount")

			var prevLoanApplicationID string
			query = `SELECT loan_application_id FROM loan_application WHERE user_id = $1 and status = $2 ORDER BY created_at DESC LIMIT 1 OFFSET 1`
			err = database.Get(&prevLoanApplicationID, query, e.UserID.String, constants.LoanStatusDisbursed)
			if err != nil {
				errorHandler.ReportToSentryWithoutRequest(err)
				logger.WithUser(event.UserID).Errorln(err)
				return
			}

			err = topuputils.SetEligibilityStatusByLoan(nil, prevLoanApplicationID, constants.TopupDisbursed)
			if err != nil {
				errorHandler.ReportToSentryWithoutRequest(err)
				logger.WithUser(event.UserID).Errorln(err)
				return
			}

			err = topuputils.InitEligibilityForRepeatLoan(e.UserID.String)
			if err != nil {
				errorHandler.ReportToSentryWithoutRequest(err)
				logger.WithUser(event.UserID).Errorln(err)
				return
			}
		} else {
			logger.WithUser(event.UserID).Debugln("False: disbursedCount")
			err = topuputils.InitEligibilityForRepeatLoan(e.UserID.String)
			if err != nil {
				errorHandler.ReportToSentryWithoutRequest(err)
				logger.WithUser(event.UserID).Errorln(err)
				return
			}
		}
	}

	// Skip Webhook for MuthootEDI partner
	if journey.IsMuthootEDIPartner(e.SourceEntityID.String) || journey.IsMFLEMIPartner(e.SourceEntityID.String) {
		logger.DebugWithUser(e.UserID.String, "skipping external webhook")
		return
	}

	webhook, err := GetEntitiesAndTheirEvents(event.SourceEntityID, GenericWebhookService)
	if err != nil {
		logger.WithUser(event.UserID).Error(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		log.Infoln("skipping webhook for source - ", event.SourceEntityID)
		return
	}

	// if iifl agg then send webhook to IIFL as well
	webhook, err = addingIIFLSourceEvents(*event, *e, webhook)
	if err != nil {
		log.Errorf("error while addingIIFLSourceEvents err: %v", err)
	}
	log.Debugln(fmt.Sprintf("%v", webhook))

	for idx := range webhook {
		if webhook[idx].WebhookURL == "" || !webhook[idx].IsActive {
			continue
		}
		switch webhook[idx].SelectionMode {
		case constants.BlacklistedEvents:
			if general.InArr(event.EventType, webhook[idx].Events) {
				logger.WithUser(event.UserID).Infof("skipped the blacklisted events for %v", event.SourceEntityID)
			} else {
				sendEventsToWebhookEndpoints(*event, *e, webhook[idx], dateTime)
			}
		case constants.WhitelistedEvents:
			if general.InArr(event.EventType, webhook[idx].Events) {
				sendEventsToWebhookEndpoints(*event, *e, webhook[idx], dateTime)
			} else {
				logger.WithUser(event.UserID).Infof("skipped the events : %v as this is not whitelisted events %v", event.EventType, event.SourceEntityID)
			}
		case constants.Default:
			//todo remove this after full rollout
			sendEventsToWebhookEndpoints(*event, *e, webhook[idx], dateTime)
		}

	}
	// TODOConfig: make this more configurable
	go func() {
		if e.SourceEntityID.Valid && (journey.IsPFLSourcing(e.SourceEntityID.String) || journey.IsPFLEducationLoanJourney(e.SourceEntityID.String)) && (general.InArr(event.EventType, []string{constants.ActivityENachAuthSuccess, constants.ActivityUserUnderReview, constants.ActivityUserQualified, constants.ActivityBankVerificationFailed, constants.ActivityBankDetailsVerified, constants.ActivityAutoKYCFailed, constants.ActivityKYCVerified, constants.ActivityFormUpdated, constants.ActivityLoanApplicationCreated, constants.ActivityKYCDocRejected, constants.ActivityLoanRejected, constants.ActivityBankConnectInitiated, constants.ActivityBankConnectCompleted, constants.ActivityCoApplicantDocumentsUploaded, constants.ActivityApplicantDocumentUploaded, constants.ActivityCourseInfoUpdated, constants.ActivityCoApplicantFinancialInfoUpdated, constants.ActivityApplicationSubmitted, constants.ActivitySanctionLetterGenerated, constants.ActivityPaymentInitiated, constants.ActivityPaymentCompleted, constants.ActivityApplicantCreated}) || (event.EventType == constants.ActivityLoanCancelled && event.Description == constants.MaxPennydropAttempts) || (event.EventType == constants.ActivityUserDisqualified && !strings.HasPrefix(event.Description, "Dedupe Reject") && event.Description != constants.PANVerificationAttemptExausted)) {
			userData, err := users.Get(e.UserID.String)
			if err != nil {
				logger.WithUser(e.UserID.String).Println(err)
			}

			var (
				segmentData string
				isPrime     bool
			)

			segmentData, _ = userData.DynamicUserInfoMap["segment"].(string)
			isPrime = segmentData == poonawalla.SegmentPrime
			_, err = poonawalla.UpdateApplication(context.Background(), event.UserID, event.LoanApplicationID, event.EventType, map[string]interface{}{"isPrime": isPrime, "rejectionReason": event.Description}, e.SourceEntityID.String)
			if err != nil {
				logger.WithUser(event.UserID).Println(err)
			}

			var docs []string
			if general.InArr(event.EventType, []string{constants.ActivityKYCVerified, constants.ActivityKYCDocRejected, constants.ActivityCoApplicantDocumentsUploaded}) {
				docs = append(docs, "KYC")
				err = poonawalla.UploadKYC(context.Background(), event.UserID, event.LoanApplicationID, docs)

				if err != nil {
					err = fmt.Errorf("unable to upload document. errr: %s", err.Error())
					logger.WithUser(event.UserID).Println(err)
				}
			}
		}
	}()

	return nil
}

type webhookHeaderValue struct {
	Header string `json:"header"`
	Value  string `json:"value"`
}

func GetEntitiesAndTheirEvents(sourceEntityID string, serviceName string) (webhook []WebhookStruct, err error) {
	query := `SELECT 
				COALESCE(wd.webhook_url, se.webhook_url, '') AS webhookurl, 
				COALESCE(wd.service_name, $2) AS servicename,
				COALESCE(wd.header_value_pair, se.header_value_pair::TEXT, '[]') AS headervaluepair,
				COALESCE(wd.events, '[]') AS events,
				COALESCE(wd.selection_mode,'DEFAULT')AS selection_mode,
				COALESCE(wd.is_active, true) AS is_active
			FROM 
				source_entity se
			LEFT JOIN 
				source_entity_webhook_data wd 
			ON 
				se.source_entity_id = wd.source_entity_id
			WHERE 
				se.source_entity_id = $1`
	rows, err := database.Query(query, sourceEntityID, serviceName)
	if err != nil {
		logger.Log.Errorln(err)
		return webhook, err
	}
	defer rows.Close()

	for rows.Next() {
		var webhookData WebhookStruct
		var eventsText string
		var events []string
		if err := rows.Scan(&webhookData.WebhookURL, &webhookData.ServiceName, &webhookData.HeaderValuePair, &eventsText, &webhookData.SelectionMode, &webhookData.IsActive); err != nil {
			logger.Log.Errorln(err)
			return webhook, err
		}
		if err := json.Unmarshal([]byte(eventsText), &events); err != nil {
			logger.Log.Errorln("Failed to unmarshal events:", err)
			errorHandler.ReportToSentryWithFields(errorHandler.ErrorFields{"webhookData": webhookData, "sourceEntityID": sourceEntityID}, err)
		}
		webhookData.Events = events
		webhook = append(webhook, webhookData)
	}
	for idx := range webhook {
		webhook[idx].SourceEntityID = sourceEntityID
	}
	return webhook, nil
}
func addingIIFLSourceEvents(event ActivityEvent, e ActivityEventNullable, webhook []WebhookStruct) ([]WebhookStruct, error) {
	agg := journey.IIFLAgg(e.SourceEntityID.String, false)

	if event.EventType == constants.ActivityPANVerified &&
		(agg.IsAgg || general.InArr(e.SourceEntityID.String, []string{constants.IIFLID, constants.IIFLBLID})) {
		loanType := agg.LoanType
		switch e.SourceEntityID.String {
		case constants.IIFLID:
			loanType = constants.LoanTypePersonalLoan
		case constants.IIFLBLID:
			loanType = constants.LoanTypeBusinessLoan
		}

		input, err := users.Get(event.UserID)
		if err != nil {
			log.Warnln(err)
		}

		city, _ := pincodeapi.GetCityState(input.Pincode)
		ctx := context.Background()
		iiflutils.GetOrCreateLeadID(ctx, iiflutils.LoanStruct{
			Email:          input.Email,
			Mobile:         input.Mobile,
			UserID:         event.UserID,
			PartnerCode:    agg.PartnerCode,
			Name:           input.Name,
			CurrentAddress: fmt.Sprintf(`{"city": "%s"}`, city),
		}, loanType)
	}
	query := `SELECT 
				COALESCE(wd.webhook_url, se.webhook_url, '') AS webhookurl, 
				COALESCE(wd.service_name, $2) AS servicename,
				COALESCE(wd.header_value_pair, se.header_value_pair::TEXT, '[]') AS headervaluepair,
				COALESCE(wd.events, '[]') AS events,
				COALESCE(wd.selection_mode,'DEFAULT')AS selection_mode,
				COALESCE(wd.is_active, true) AS is_active
			FROM 
				source_entity se
			LEFT JOIN 
				source_entity_webhook_data wd 
			ON 
				se.source_entity_id = wd.source_entity_id
			WHERE 
				se.source_entity_id = $1`

	if agg.IsAgg {
		e.SourceEntityID.String = constants.IIFLID
		if agg.LoanType == constants.LoanTypeBusinessLoan {
			e.SourceEntityID.String = constants.IIFLBLID
		}
		var parentWebhook []WebhookStruct
		rows, err := database.Query(query, e.SourceEntityID.String, GenericWebhookService)
		if err != nil {
			logger.Log.Errorln(err)
			return webhook, err
		}
		defer rows.Close()
		for rows.Next() {
			var webhookData WebhookStruct
			var eventsText string
			var events []string
			if err := rows.Scan(&webhookData.WebhookURL, &webhookData.ServiceName, &webhookData.HeaderValuePair, &eventsText, &webhookData.SelectionMode, &webhookData.IsActive); err != nil {
				logger.Log.Errorln(err)
				return webhook, err
			}
			if err := json.Unmarshal([]byte(eventsText), &events); err != nil {
				logger.Log.Errorln("Failed to unmarshal events:", err)
				errorHandler.ReportToSentryWithFields(errorHandler.ErrorFields{"webhookData": webhookData, "sourceEntityID": e.SourceEntityID.String}, err)
			}
			webhookData.Events = events
			parentWebhook = append(parentWebhook, webhookData)
		}
		for idx := range parentWebhook {
			parentWebhook[idx].SourceEntityID = e.SourceEntityID.String
		}
		webhook = append(webhook, parentWebhook...)
	}
	return webhook, nil
}
func sendEventsToWebhookEndpoints(event ActivityEvent, e ActivityEventNullable, webhook WebhookStruct, dateTime string) {
	serviceName := webhook.ServiceName
	// Please note we are finding program of the webhook, not of the user
	// so that we can trigger specific functions which handle them

	if !journey.ShowRejectReason(webhook.SourceEntityID) {
		switch event.EventType {
		case constants.ActivityLoanRejected:
			event.Description = "rejected based on policy"
		case constants.ActivityUserDisqualified:
			if !strings.Contains(strings.ToLower(event.Description), "duplicate") {
				event.Description = "disqualified based on policy"
			}
		}
	}

	choiceOfWebhookFlow := general.WeightedRandomness([]general.WeightedEntities[string]{
		{Type: constants.NewFlow, Weight: 1},
		{Type: constants.OldFlow, Weight: 99},
	})
	if choiceOfWebhookFlow == constants.NewFlow {
		// 5% of the requests go through the new code path
		data := WebhookEventData{
			WebhookURL:        webhook.WebhookURL,
			ServiceName:       webhook.ServiceName,
			HeaderValuePair:   webhook.HeaderValuePair,
			UserID:            event.UserID,
			SourceEntityID:    event.SourceEntityID,
			LoanApplicationID: event.LoanApplicationID,
			EntityType:        event.EntityType,
			EntityRef:         event.EntityRef,
			EventType:         event.EventType,
			Description:       event.Description,
			DateTime:          event.DateTime,
		}
		enqueuWebhookEvents(&data)
	} else {
		// 90% of the requests go through the old code path
		if webhook.ServiceName == GenericWebhookService || webhook.ServiceName == "abfl_bl_ubona_webhooks" {
			switch {
			case e.SourceEntityID.String == constants.TataBNPLID:
				go webhooks.TriggerTataWebhook(webhook.WebhookURL, serviceName, webhook.HeaderValuePair, event.UserID, event.SourceEntityID, event.LoanApplicationID, event.EntityType, event.EntityRef, event.EventType, event.Description, dateTime)
			case e.SourceEntityID.String == constants.TataPLID:
				go HitActivityWebhook(webhook, event, dateTime, serviceName)
			case journey.IsABFLBLSourcing(e.SourceEntityID.String):
				go webhooks.TriggerABFLWebhook(webhook.WebhookURL, serviceName, webhook.HeaderValuePair, event.UserID, event.SourceEntityID, event.LoanApplicationID, event.EntityType, event.EntityRef, event.EventType, event.Description, dateTime)
			case e.SourceEntityID.String == constants.MoneyControlID:
				go webhooks.TriggerMoneyControlWebhook(webhook.WebhookURL, serviceName, webhook.HeaderValuePair, event.UserID, event.SourceEntityID, event.LoanApplicationID, event.EntityType, event.EntityRef, event.EventType, event.Description, event.DateTime)
			// case event.SourceEntityID == constants.DSAMoneyControlIncredID:
			// 	go webhooks.TriggerDSAMoneyControlIncredWebhook(webhook.WebhookURL, serviceName, webhook.HeaderValuePair, event.UserID, event.SourceEntityID, event.LoanApplicationID, event.EntityType, event.EntityRef, event.EventType, event.Description, event.DateTime)
			default:
				go HitActivityWebhook(webhook, event, dateTime, serviceName)
			}
		}
	}

}

func HitActivityWebhook(webhookObj WebhookStruct, event ActivityEvent, dateTime, serviceName string) (err error) {
	defer errorHandler.RecoveryNoResponse()

	url := webhookObj.WebhookURL
	var request string
	var response string
	var reqHeaders http.Header
	var respHeaders http.Header
	var httpStatusCode int
	var success = 0

	aggObj := journey.IIFLAgg(event.SourceEntityID, false)

	userObj, err := users.Get(event.UserID)
	if err != nil {
		logger.WithUser(event.UserID).Warnln("no row for userID found")
		return err
	}

	customerID := userObj.UniqueID
	// replace customerID with CRMID only for non-DSA webhook
	if userObj.CrmID != "" && aggObj.IsAgg && general.InArr(webhookObj.SourceEntityID, []string{constants.IIFLBLID, constants.IIFLID}) {
		customerID = userObj.CrmID
	}

	// hacks
	if event.SourceEntityID == constants.TataPLID && event.EventType == constants.ActivityUserArchived {
		customerID = event.EntityRef
	}
	if event.LoanApplicationID != "" && event.SourceEntityID == constants.MoneyControlID {
		lenderID, err := loanapplication.GetLender(event.LoanApplicationID)
		if err == nil && lenderID == constants.ABFLPLID {
			var desc = fmt.Sprintf(`{"lender":"%s"}`, constants.LenderNamesMap[lenderID])
			if event.Description != "" {
				event.Description = fmt.Sprintf("%s %s", event.Description, desc)
			} else {
				event.Description = desc
			}
		}
	}

	loanType, _, _ := journey.GetLoanType(event.SourceEntityID)
	var strPayLoad map[string]interface{}
	// Check if custom webhook payload is enabled for this source entity
	if percent, _ := featureflagsettings.GetRolloutPercentageForFeature(journey.FlagCustomWebhookPayload, event.SourceEntityID); percent >= 100 {
		strPayLoad, err = getCustomWebhookPayload(context.TODO(), event, customerID, loanType, dateTime)
		if err != nil {
			logger.WithUser(event.UserID).Warnln("skipping webhook - ", err)
			return err
		}
	} else if journey.IsPFLSourcing(event.SourceEntityID) {
		strPayLoad, err = getPayloadForPFL(customerID, loanType, dateTime, event)
		if err != nil {
			logger.WithUser(event.UserID).Warnln("skipping webhook - ", err)
			return
		}
	} else {
		var lastSource string
		query := `SELECT source FROM user_source where source != 'dashboard' and user_id = $1 order by created_at limit 1`
		err = database.Get(&lastSource, query, event.UserID)
		if err != nil {
			log.Println(err)
		}

		var leadID string
		var loanObj *loanapplication.LoanApplication
		if event.LoanApplicationID != "" {
			loanObj, _ = loanapplication.Get(context.Background(), event.LoanApplicationID)
			if loanObj != nil {
				leadID = loanObj.LoanApplicationNo
			}
		}
		// check for IIFL Agg
		if aggObj.IsAgg {
			lastSource = aggObj.PartnerCode // add DSA partner code if IIFL Agg
		}
		loanType, _, _ = journey.GetLoanType(event.SourceEntityID)
		strPayLoad = map[string]interface{}{
			"customerID":        customerID,
			"entityType":        event.EntityType,
			"loggedAt":          dateTime,
			"eventType":         event.EventType,
			"eventDescription":  event.Description,
			"loanApplicationID": event.LoanApplicationID,
			"source":            lastSource,
			"journeyType":       loanType,
		}
		if journey.IsProgramApplicable(event.SourceEntityID) {
			var tempDesc map[string]interface{}
			err := json.Unmarshal([]byte(event.Description), &tempDesc)
			if err != nil {
				log.Errorln(err)
			}
			metaData := map[string]interface{}{
				"offerType":              "",
				"routingLogic":           "",
				"preRoutingRejectReason": "",
				"apr":                    "",
				"webtopId":               "",
				"opportunityId":          "",
				"leadId":                 leadID,
				"loanType":               "",
				"lender":                 "",
				"amount":                 "",
				"customerType":           "",
				"rejectReason":           "",
				"loanapplicationno":      leadID,
				"emi":                    "",
				"interest":               "",
				"tenure":                 "",
				"processingFee":          "",
			}
			switch event.EventType {
			case constants.ActivityOfferGenerated:
				if event.EntityRef == constants.OfferTypeTentative {
					metaData["offerType"] = "banking_mandatory"
				} else {
					metaData["offerType"] = event.EntityRef
				}
			case constants.ActivityLenderPreSelected:
				metaData["routingLogic"] = event.EntityRef
			case constants.ActivityUserDisqualified:
				if event.EntityRef == constants.PreRoutingStage {
					metaData["preRoutingRejectReason"] = event.Description
				}
			case constants.ActivityBankConnectInitiated, constants.ActivityBankConnectUploaded, constants.ActivityBankConnectCompleted, constants.ActivityBankConnectFailed:
				metaData["mode"] = event.EntityRef
			case constants.ActivityLeadPushedToLender:
				if s, ok := userObj.DynamicUserInfoMap["customerSegment"].(string); ok {
					metaData["customerType"] = s
				}
			case constants.EventLoanDisbursed:
				if loanObj != nil {
					metaData["emi"] = loanObj.EMI
					metaData["loanapplicationno"] = loanObj.LoanApplicationNo
					metaData["processingFee"] = loanObj.ProcessingFee
					metaData["interest"] = loanObj.Interest
				}
			}
			for _, key := range []string{"lender", "amount", "customerType", "rejectReason", "apr", "webtopId", "opportunityId", "leadId", "loanType", "offerType", "campaignType", "loanapplicationno", "emi", "processingFee", "interest", "tenure"} {
				if _, ok := tempDesc[key]; ok {
					metaData[key] = tempDesc[key]
				}
			}

			if event.Metadata != nil {
				for _, key := range []string{"loanAmount", "tenure", "rateOfInterest", "status", "processingFee", "interest", "emi", "offerType", "loanApplicationNo"} {
					if _, ok := event.Metadata[key]; ok {
						metaData[key] = event.Metadata[key]
					}
				}
			}

			strPayLoad["metaData"] = metaData
		}
		if journey.IsABFLPLSourcing(event.SourceEntityID) {
			strPayLoad = map[string]interface{}{
				"customerID":        customerID,
				"entityType":        event.EntityType,
				"loggedAt":          dateTime,
				"eventType":         event.EventType,
				"eventDescription":  event.Description,
				"loanApplicationNo": leadID,
				"source":            lastSource,
				"journeyType":       loanType,
				"mobile":            userObj.Mobile,
				"name":              userObj.Name,
				"emailID":           userObj.Email,
			}
		}
	}

	// additional temporary condition for Incred Sourcing
	// temp removal of checks for PFL regarding loan app ID
	if journey.IsIncredSourcing(event.SourceEntityID) {
		var lenderID string
		if event.SourceEntityID == constants.DSAMoneyControlIncredID {
			lenderID = constants.IncredID
		}
		if event.LoanApplicationID == "" {
			loanApplicationID, err := loanapplication.GetActiveByLenderAndUser(event.UserID, lenderID)
			if err != nil && err != sql.ErrNoRows {
				logger.WithUser(event.UserID).Errorf("\nerror fetching loan application ID for userID: %s and lenderID: %s, error: %s", event.UserID, lenderID, err)
			}
			strPayLoad["loanApplicationID"] = loanApplicationID
		}
	}

	bytes, err := json.Marshal(strPayLoad)
	if err != nil {
		log.Println(err)
		serviceslib.LogOutgoingWebhook(event.UserID, event.SourceEntityID, event.EventType, serviceName, url, request, response, err.Error(), general.GetUUID(), success, httpStatusCode, reqHeaders, respHeaders, aggObj.IsAgg)
		return err
	}
	request = string(bytes)
	var body []byte

	err = retry.CustomRetry(MaxTry, 1*time.Second, func() error {
		payload := strings.NewReader(string(bytes))
		timeout := 90 * time.Second
		client := tracer.GetTraceableHTTPClient(&timeout, fmt.Sprintf("activity_webhook_%s", event.SourceEntityID))
		req, err := requestutils.GetMockableHTTPRequest(event.UserID, fmt.Sprintf("activity_webhook_%s", event.SourceEntityID), "POST", url, payload)
		if err != nil {
			log.Println(err)
			return err
		}
		req.Header.Add("Content-Type", "application/json")
		if webhookObj.HeaderValuePair != "[]" {
			// TODO: Dynamic webhook header configuration
			var headers []webhookHeaderValue
			err := json.Unmarshal([]byte(webhookObj.HeaderValuePair), &headers)
			if err != nil {
				log.Println(err)
			} else {
				for _, header := range headers {
					req.Header.Add(header.Header, header.Value)
				}
			}
		}
		reqHeaders = req.Header
		res, err := client.Do(req)
		if err != nil {
			log.Println("Webhook call failed for", customerID, event.EventType, url, "Error :", err)
			return err
		}
		respHeaders = res.Header
		httpStatusCode = res.StatusCode

		defer res.Body.Close()
		body, err = io.ReadAll(res.Body)
		if err != nil {
			log.Println("Webhook cannot retrieve body", customerID, event.EventType, url, "Error :", err)
			return err
		}
		response = string(body)
		if res.StatusCode < 200 || res.StatusCode >= 300 {
			log.Println("Webhook failure for", customerID, event.EventType, url, "response:", res.StatusCode)
			return errors.New(string(body))
		}
		return nil
	})
	if err == nil {
		success = 1
		serviceslib.LogOutgoingWebhook(event.UserID, event.SourceEntityID, event.EventType,
			serviceName, url, request, response, "", general.GetUUID(), success, httpStatusCode, reqHeaders, respHeaders, aggObj.IsAgg)
	} else {
		serviceslib.LogOutgoingWebhook(event.UserID, event.SourceEntityID, event.EventType, serviceName, url, request, response, err.Error(), general.GetUUID(), success, httpStatusCode, reqHeaders, respHeaders, aggObj.IsAgg)
	}
	return err
}

// getCustomWebhookPayload generates a customized webhook payload based on source entity configuration
func getCustomWebhookPayload(ctx context.Context, event ActivityEvent, customerID, loanType, dateTime string) (map[string]interface{}, error) {

	// Get custom payload config for this source entity
	client, err := customRoutes.GetClientFromSourceEntity(ctx, event.SourceEntityID)
	if err != nil {
		logger.WithUser(event.UserID).Errorln("failed to get client from source entity ID: ", event.SourceEntityID)
		return nil, fmt.Errorf("failed to get client from source entity ID: %s", event.SourceEntityID)
	}

	// Create base payload with common fields
	payload := map[string]interface{}{
		"customerID":        customerID,
		"userID":            event.UserID,
		"eventType":         event.EventType,
		"dateTime":          dateTime,
		"loanApplicationID": event.LoanApplicationID,
	}

	// Add source-specific data
	switch {
	case journey.IsSuperMoneySourcing(event.SourceEntityID):
		additionalData, err := supermoney.GetAdditionalLoanData(ctx, event.UserID)
		if err != nil {
			logger.WithUser(event.UserID).Warnln("skipping webhook - ", err)
			return nil, err
		}

		additionalDataMap, err := additionalData.ToStringMap()
		if err != nil {
			logger.WithUser(event.UserID).Warnln("skipping webhook - ", err)
			return nil, err
		}

		// TODO: Ideally this could've been underwriting.GetLenderID(), but import cycles knew better :)
		// Add lender ID based on source entity
		lenderID := constants.PoonawallaFincorpID
		if event.SourceEntityID == constants.PrefrSuperMoneyID {
			lenderID = constants.PrefrSMLenderID
		}
		payload["lenderID"] = lenderID

		// Merge additional data into payload
		maps.Copy(payload, additionalDataMap)
	}

	logger.WithUser(event.UserID).Debugln("Webhook Payload before modification: ", general.AnyToJSONString(payload))

	// Get modified payload using custom routes configuration
	res := customRoutes.PayloadDetails{
		Obj:        payload,
		Header:     http.Header{},
		StatusCode: http.StatusOK,
		URI:        "webhook/*",
	}

	modifiedPayload, _, err := customRoutes.GetCustomModifiedWebhookPayload(ctx, res, client)
	if err != nil {
		logger.WithUser(event.UserID).Errorln("failed to modify webhook payload: ", err)
		return nil, fmt.Errorf("failed to modify webhook payload: %w", err)
	}

	return cast.ToStringMap(modifiedPayload), nil
}

func RegisterAuxiliaryEventArgs(userID string, sourceEntityID string, loanApplicationID string, entityType string, entityRef string, eventType string, description string, metadata map[string]interface{}) {
	activityObj := ActivityEvent{
		UserID:            userID,
		SourceEntityID:    sourceEntityID,
		LoanApplicationID: loanApplicationID,
		EntityType:        entityType,
		EntityRef:         entityRef,
		EventType:         eventType,
		Description:       description,
		Metadata:          metadata,
	}
	RegisterAuxiliaryEvent(&activityObj, general.GetTimeStampString())
}

// ActivityLogger is a wrapper function around RegisterEvent
func ActivityLogger(userID, sourceEntityID, entityRef, entityType, eventType, description, loanApplicationID, timestampString string, isAuxiliary bool) {
	activityObj := ActivityEvent{
		UserID:            userID,
		SourceEntityID:    sourceEntityID,
		LoanApplicationID: loanApplicationID,
		EntityType:        entityType,
		EntityRef:         entityRef,
		EventType:         eventType,
		Description:       description,
	}
	if isAuxiliary {
		_ = RegisterAuxiliaryEvent(&activityObj, timestampString)
		return
	}

	RegisterEvent(&activityObj, timestampString)
}

// getLISADataForActivityLog fetches data from octopus_webhook for activity log insertion
func getLISADataForActivityLog(event *ActivityWebhookEvent) (ActivityEvent, error) {
	var activityObj ActivityEvent

	activityObj.UserID = event.ID

	if sourceEntityID, ok := event.MetaData["source_entity_id"]; ok {
		activityObj.SourceEntityID = sourceEntityID.(string)
	}

	if loanApplicationID, ok := event.MetaData["loan_application_id"]; ok {
		activityObj.LoanApplicationID = loanApplicationID.(string)
	}

	return activityObj, nil
}

// getOctopusDataForActivityLog fetches data from octopus_webhook for activity log insertion
func getOctopusDataForActivityLog(event *ActivityWebhookEvent) (ActivityEvent, error) {
	var activityObj ActivityEvent
	webhookData, err := octopuswebhook.GetByWebhookRequestID(event.ID)
	if err != nil {
		log.Print(err)
		return ActivityEvent{}, err
	}
	activityObj.UserID = webhookData.UserID
	activityObj.SourceEntityID = webhookData.SourceEntityID

	return activityObj, nil
}

// getKYCDataForActivityLog fetches data from kyc_service for activity log insertion
func getKYCDataForActivityLog(event *ActivityWebhookEvent) (ActivityEvent, error) {
	var activityObj ActivityEventNullable
	query := `select ks.user_id as userid, 
					la.source_entity_id as sourceentityid,
					la.loan_application_id as loanapplicationid
				from kyc_service ks
				inner join loan_application la
				on la.loan_application_id = ks.loan_application_id
				where ks.application_id = $1`
	err := database.Get(&activityObj, query, event.ID)
	if err != nil {
		log.Print(err)
		return ActivityEvent{}, err
	}

	activityEvent := ActivityEvent{
		UserID:            activityObj.UserID.String,
		SourceEntityID:    activityObj.SourceEntityID.String,
		LoanApplicationID: activityObj.LoanApplicationID.String,
	}
	if general.InArr(event.EventType, constants.KYCCustomerEvents) {
		activityEvent.EntityType = constants.EntityTypeCustomer
		activityEvent.EntityRef = activityObj.UserID.String
	}
	return activityEvent, nil
}

// getBCDataForActivityLog fetches data from bank_connect_details for activity log insertion
func getBCDataForActivityLog(event *ActivityWebhookEvent) (ActivityEvent, error) {
	var activityObj ActivityEventNullable
	query := `select users.user_id as userid, 
	users.source_entity_id as sourceentityid,
	la.loan_application_id as loanapplicationid
	from users 
	left join loan_application la using (user_id)
	where unique_id = $1`
	err := database.Get(&activityObj, query, event.ID)
	if err != nil {
		log.Print(err)
		return ActivityEvent{}, err
	}
	return ActivityEvent{
		UserID:            activityObj.UserID.String,
		SourceEntityID:    activityObj.SourceEntityID.String,
		LoanApplicationID: activityObj.LoanApplicationID.String,
	}, nil
}

// RegisterWebhookEvent is a wrapper function around RegisterEvent for events received through webhook
func RegisterWebhookEvent(event *ActivityWebhookEvent, activityService string) {

	webhookID := general.GetUUID()
	var webhookName string
	var activityObj ActivityEvent
	var err error

	switch activityService {
	case constants.OctopusActivity:
		activityObj, err = getOctopusDataForActivityLog(event)
		webhookName = constants.OctopusActivityWebhook
	case constants.LISAActivity:
		activityObj, err = getLISADataForActivityLog(event)
		webhookName = constants.LISAActivityWebhook
	case constants.KYCServiceActivity:
		activityObj, err = getKYCDataForActivityLog(event)
		webhookName = constants.KYCServiceActivityWebhook
	case constants.BCActivity:
		activityObj, err = getBCDataForActivityLog(event)
		webhookName = constants.BCActivityWebhook
	}

	webhookErr := webhooklogs.Create(nil, webhookID, webhookName, event.Stringify())
	if webhookErr != nil {
		logger.WithUser(activityObj.UserID).Errorln(webhookErr)
		webhookErr = fmt.Errorf("[RegisterWebhookEvent] failed to create webhook_log entry for webhookName: %s, webhookEventInfo: %s, err: %s", webhookName, event.Stringify(), webhookErr.Error())
		errorHandler.ReportToSentryWithFields(map[string]interface{}{
			"webhookName": webhookName,
			"user_id":     activityObj.UserID,
		}, webhookErr)
	}

	if err == sql.ErrNoRows {
		log.Errorf("[RegisterWebhookEvent] no data for event: %s", event.Stringify())
		return
	} else if err != nil {
		err = fmt.Errorf("[RegisterWebhookEvent] error in getting data for activity log, userID: %s, err: %v, webhookName: %s,  webhook event received: %+v", activityObj.UserID, err, webhookName, event)
		logger.WithUser(activityObj.UserID).Errorln(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{
			"webhookName": webhookName,
			"user_id":     activityObj.UserID,
		}, err)
		return
	}

	webhookErr = webhooklogs.Update(nil, activityObj.UserID, webhookID)
	if webhookErr != nil {
		logger.WithUser(activityObj.UserID).Errorln(webhookErr)
		webhookErr = fmt.Errorf("[RegisterWebhookEvent] failed to update webhook_log with entry for webhookName: %s, webhookEventInfo: %s, userID: %s, err: %s", webhookName, event.Stringify(), activityObj.UserID, webhookErr.Error())
		errorHandler.ReportToSentryWithFields(map[string]interface{}{
			"webhookName": webhookName,
			"user_id":     activityObj.UserID,
		}, webhookErr)
	}

	if activityObj.EntityType == "" {
		activityObj.EntityType = constants.EntityTypeExternal
	}
	activityObj.EventType = event.EventType

	if description, ok := event.MetaData["description"]; ok {
		activityObj.Description = description.(string)
	}

	RegisterEvent(&activityObj, event.EventLoggedAt)
}

func (event *EntityEvent) RegisterEntityEvent() {
	if !general.InArr(event.EventType, []string{constants.EntityCreated}) {
		err := errors.New("invalid entity event")
		errorHandler.ReportToSentryWithoutRequest(err)
		log.Errorln(err)
		return
	}
	err := entitylogs.Insert(nil, event.EntityID, event.OrganizationID, constants.EntityTypeBorrower, event.EntityID, event.EventType, event.Description)
	if err != nil {
		log.Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return
	}
}

// MultipleInsertActiityLogsData inserts logs in Activity log in bulk
func MultipleInsertActivityLogsData(ctx context.Context, data []ActivityEvent, loggedAt string) error {
	if len(data) == 0 {
		return nil
	}

	query := `INSERT INTO activity_log (log_id, user_id, source_entity_id, loan_application_id, entity_type, entity_ref, logged_at, event_type, event_description) VALUES `
	values := []interface{}{}

	for _, row := range data {
		logID := general.GetUUID()
		values = append(values, logID, row.UserID, row.SourceEntityID, row.LoanApplicationID, row.EntityType, row.EntityRef, loggedAt, row.EventType, row.Description)
	}

	// placeholder
	valueStrings := make([]string, len(data))
	for i := range data {
		valueStrings[i] = "(?, ?, ?, ?, ?, ?, ?, ?, ?)"
	}

	query += sqlx.Rebind(sqlx.DOLLAR, strings.Join(valueStrings, ", "))

	// Execute the query with the expanded values
	query, args, err := sqlx.In(query, values...)
	if err != nil {
		return fmt.Errorf("failed to expand query with sqlx.In: %w", err)
	}

	query = database.Rebind(query)
	if _, err := database.ExecContext(ctx, query, args...); err != nil {
		return fmt.Errorf("failed to execute batch insert: %w", err)
	}

	return nil
}

func SendFailedEventsToWebhookEndpoints(event ActivityEvent, webhook WebhookStruct, dateTime string) {
	serviceName := webhook.ServiceName
	// Please note we are finding program of the webhook, not of the user
	// so that we can trigger specific functions which handle them
	log.Infof("failed events data, \n%v   and   \n%v", event, webhook)
	if !journey.ShowRejectReason(webhook.SourceEntityID) {
		switch event.EventType {
		case constants.ActivityLoanRejected:
			event.Description = "rejected based on policy"
		case constants.ActivityUserDisqualified:
			if !strings.Contains(strings.ToLower(event.Description), "duplicate") {
				event.Description = "disqualified based on policy"
			}
		}
	}
	if webhook.ServiceName == GenericWebhookService {
		switch {
		case event.SourceEntityID == constants.TataBNPLID:
			webhooks.TriggerTataWebhook(webhook.WebhookURL, serviceName, webhook.HeaderValuePair, event.UserID, event.SourceEntityID, event.LoanApplicationID, event.EntityType, event.EntityRef, event.EventType, event.Description, dateTime)
		case event.SourceEntityID == constants.TataPLID:
			HitActivityWebhook(webhook, event, dateTime, serviceName)
		case journey.IsABFLBLSourcing(event.SourceEntityID):
			webhooks.TriggerABFLWebhook(webhook.WebhookURL, serviceName, webhook.HeaderValuePair, event.UserID, event.SourceEntityID, event.LoanApplicationID, event.EntityType, event.EntityRef, event.EventType, event.Description, dateTime)
		case event.SourceEntityID == constants.MoneyControlID:
			err := webhooks.TriggerMoneyControlWebhook(webhook.WebhookURL, serviceName, webhook.HeaderValuePair, event.UserID, event.SourceEntityID, event.LoanApplicationID, event.EntityType, event.EntityRef, event.EventType, event.Description, event.DateTime)
			if err != nil {
				logger.WithUser(event.UserID).Errorln("[SendFailedEventsToWebhookEndpoints] Error from TriggerMoneyControlWebhook() ", err)
			}
		default:
			HitActivityWebhook(webhook, event, dateTime, serviceName)
		}
	}
}

func getPayloadForPFL(customerID, loanType, dateTime string, event ActivityEvent) (strPayLoad map[string]interface{}, err error) {
	type sourceData struct {
		Source        string `db:"source"`
		AppsflyerID   string `db:"appsflyer_id"`
		IDFA          string `db:"idfa"`
		AdvertisingID string `db:"adverstising_id"`
	}
	var sData sourceData
	query := `SELECT
		DISTINCT ON (u.user_id) u.source,
		coalesce(campaign_params ->> 'appsflyer_id', '') AS appsflyer_id,
		coalesce(campaign_params ->> 'advertising_id', '') As adverstising_id, --- For Android Users
		coalesce(campaign_params ->> 'idfa', '') As idfa ----- For IOS Users
		FROM 
		users u
		JOIN user_source us on u.user_id = us.user_id
		WHERE
		u.user_id = $1
		ORDER BY
		u.user_id,
		us.created_at DESC`
	err = database.Get(&sData, query, event.UserID)
	if err != nil {
		logger.WithUser(event.UserID).Error(err)
		if err != sql.ErrNoRows {
			errorHandler.ReportToSentryWithFields(errorHandler.ErrorFields{"event": event}, err)
		}
	}
	if sData.AppsflyerID == "" {
		// avoid sending webhook
		return nil, errors.New("appsflyerID not available")
	}

	eventValue := map[string]interface{}{
		"entityType":        event.EntityType,
		"loggedAt":          dateTime,
		"eventDescription":  event.Description,
		"loanApplicationID": event.LoanApplicationID,
		"source":            sData.Source,
		"journeyType":       loanType,
	}

	if event.EventType == constants.ActivityLoanDisbursed {
		loanDetails, err := loanapplication.Get(context.Background(), event.LoanApplicationID)
		if err != nil {
			logger.WithUser(event.UserID).Errorln(err)
			return nil, err

		}
		eventValue["af_revenue"] = loanDetails.Amount

	}

	data := map[string]interface{}{
		"customer_user_id": customerID,
		"appsflyer_id":     sData.AppsflyerID,
		"eventName":        event.EventType,
		"eventCurrency":    "INR",
		"eventValue":       eventValue,
	}
	if sData.Source == "hybrid" {
		data["advertising_id"] = sData.AdvertisingID

	} else if sData.Source == "PFL_IOS_WEB" {
		data["idfa"] = sData.IDFA

	}

	serviceID, err := getOctoServiceIDAppsflyer(sData.Source)
	if err != nil {
		logger.WithUser(event.UserID).Errorln(err)
		return nil, err

	}
	strPayLoad = map[string]interface{}{
		"serviceID": serviceID,
		"data":      data,
	}
	return strPayLoad, nil
}

func getOctoServiceIDAppsflyer(source string) (string, error) {
	switch conf.ENV {
	case conf.ENV_PROD:
		switch source {
		case "hybrid":
			return "f92792ee-a896-11ef-81bc-4ee67cfb5b1b", nil
		case "PFL_IOS_WEB":
			return "88ad909b-a897-11ef-8cf6-36ca03ef3a9f", nil
		default:
			return "", errors.New("no configuration found for source")
		}
	default:
		switch source {
		case "hybrid":
			return "51ec1e53-21a2-11ef-bf73-bea74f6e3f45", nil
		case "PFL_IOS_WEB":
			return "756fa2c3-232b-11ef-bf73-bea74f6e3f45", nil
		default:
			return "", errors.New("no configuration found for source")
		}
	}
}
