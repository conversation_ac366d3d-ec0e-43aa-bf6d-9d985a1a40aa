package octopus_test

import (
	"bytes"
	"context"
	"encoding/json"
	"finbox/go-api/functions/octopus"
	"finbox/go-api/utils/general"
	"fmt"
	"io"
	"net/http"
	"testing"

	"github.com/finbox-in/octoclient"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

type MockHTTPClient struct {
	mock.Mock
}
type MockRoundTripper struct {
	mock.Mock
	hitCount        int
	receivedPayload []byte
	receivedHeaders http.Header
}

func (m *MockRoundTripper) RoundTrip(req *http.Request) (*http.Response, error) {
	m.hitCount++ // For testing retry count

	// Capture the request body
	body, err := io.ReadAll(req.Body)
	if err != nil {
		return nil, err
	}

	m.receivedHeaders = req.Header
	m.receivedPayload = body

	args := m.Called(req)
	return args.Get(0).(*http.Response), args.Error(1)
}

func (m *MockRoundTripper) GetHitCount() int {
	return m.hitCount
}

func (m *MockRoundTripper) GetReceivedPayload() []byte {
	return m.receivedPayload
}

func (m *MockRoundTripper) GetReceivedHeaders() http.Header {
	return m.receivedHeaders
}

func (m *MockHTTPClient) Do(req *http.Request) (*http.Response, error) {
	args := m.Called(req)
	return args.Get(0).(*http.Response), args.Error(1)
}

func TestInvokeWithOptions_SuccessfulRequest(t *testing.T) {
	type YourValidationStruct struct {
		UdyamNumber string `json:"udyamNumber" validate:"required"`
		PDFURL      string `json:"pdfUrl"`
		Gender      string `json:"gender" validate:"required"`
	}

	type MyRequestData struct {
		UserPAN    string `json:"userPAN"`
		UserMobile string `json:"userMobile"`
	}

	ctx := context.Background()
	userID := "2d6ee31d-f1a0-4153-90ea-cb376354bdc3"
	serviceName := "test-service"
	payload := octoclient.OctoPayloadGeneric{
		RequestID: "4a1dc453-cb46-445f-acb6-48f5201eb6be",
		ServiceID: "539a1610-eb40-48f6-9e4d-cbe494ad4bcd",
		Data: MyRequestData{
			UserPAN:    "TestPANValue",
			UserMobile: "value",
		},
	}
	// Mock the RoundTripper
	mockRoundTripper := new(MockRoundTripper)

	// Create an http.Client with the mock RoundTripper
	httpClient := &http.Client{
		Transport: mockRoundTripper,
	}

	opts := octopus.InvokeOptions{
		ValidateWithStruct:       true,
		RetryOnValidationFailure: true,
		ValidationStruct:         &YourValidationStruct{},
		HTTPClient:               httpClient,
	}

	// Mock HTTP response
	mockResp := &http.Response{
		StatusCode: 200,
		Body:       io.NopCloser(bytes.NewBufferString(`{"data": {"udyamNumber": "value", "pdfUrl":"", "gender":"MALE"}, "requestId":"349f3d36-fb3b-44a7-95db-b7b6d5380764", "msg":"invoked"}`)),
		Header:     http.Header{},
	}

	mockRoundTripper.On("RoundTrip", mock.Anything).Return(mockResp, nil)

	// Call InvokeWithOptions
	respStruct, err := octopus.InvokeWithOptions(ctx, userID, serviceName, payload, opts)

	// Assert no error and correct response
	assert.NoError(t, err)
	fmt.Println("Response - ", general.AnyToJSONString(respStruct.APIResponse))
	fmt.Println("Request Headers", mockRoundTripper.GetReceivedHeaders())

	sentPayload := mockRoundTripper.GetReceivedPayload()
	assert.NotEmpty(t, sentPayload) // Ensure payload is sent

	payloadBytes, _ := json.Marshal(payload)
	assert.Equal(t, payloadBytes, sentPayload)                                 // Ensure payload is unchanged
	assert.Contains(t, string(sentPayload), "TestPANValue")                    // Ensure payload contains the PAN value
	assert.Equal(t, 1, mockRoundTripper.GetHitCount())                         // Expecting 1 hit in this case, i.e. no retries
	assert.Equal(t, "4a1dc453-cb46-445f-acb6-48f5201eb6be", payload.RequestID) // Ensure RequestID is unchanged
	assert.NotEmpty(t, respStruct.ExternalServiceID)                           //	Ensure externalServiceID is returned

	assert.Equal(t, respStruct.APIResponse.Data, map[string]interface{}{"udyamNumber": "value", "pdfUrl": "", "gender": "MALE"})
}

func TestInvokeWithOptions_ValidationFailureWithRetry(t *testing.T) {
	type YourValidationStruct struct {
		UdyamNumber string `json:"udyamNumber" validate:"required"`
		PDFURL      string `json:"pdfUrl"`
		Gender      string `json:"gender" validate:"required"`
	}

	ctx := context.Background()
	userID := "2d6ee31d-f1a0-4153-90ea-cb376354bdc3"
	serviceName := "test-service"
	payload := octoclient.OctoPayloadGeneric{
		RequestID: "4a1dc453-cb46-445f-acb6-48f5201eb6be",
		ServiceID: "539a1610-eb40-48f6-9e4d-cbe494ad4bcd",
		Data:      map[string]interface{}{"key": "value"},
	}

	// Mock the RoundTripper
	mockRoundTripper := new(MockRoundTripper)

	// Create an http.Client with the mock RoundTripper
	httpClient := &http.Client{
		Transport: mockRoundTripper,
	}

	opts := octopus.InvokeOptions{
		ValidateWithStruct:       true,
		RetryOnValidationFailure: true,
		ValidationStruct:         &YourValidationStruct{},
		HTTPClient:               httpClient,
	}

	// Mock HTTP response with missing required fields (to trigger validation failure)
	mockResp := &http.Response{
		StatusCode: 200,
		Body:       io.NopCloser(bytes.NewBufferString(`{"data": {"pdfUrl":"some-url"}, "msg":"invoked"}`)),
		Header:     http.Header{},
	}

	mockRoundTripper.On("RoundTrip", mock.Anything).Return(mockResp, nil)

	// Call InvokeWithOptions
	_, err := octopus.InvokeWithOptions(ctx, userID, serviceName, payload, opts)

	// Assert that validation failure triggers a retry
	assert.Error(t, err)                               // Expect an error due to validation failure
	assert.Equal(t, 4, mockRoundTripper.GetHitCount()) // Expecting 4 hits (default retry attempts) due to retry
}

func TestInvokeWithOptions_ValidationFailureWithoutRetry(t *testing.T) {
	type YourValidationStruct struct {
		UdyamNumber string `json:"udyamNumber" validate:"required"`
		PDFURL      string `json:"pdfUrl"`
		Gender      string `json:"gender" validate:"required"`
	}

	ctx := context.Background()
	userID := "2d6ee31d-f1a0-4153-90ea-cb376354bdc3"
	serviceName := "test-service"
	payload := octoclient.OctoPayloadGeneric{
		RequestID: "4a1dc453-cb46-445f-acb6-48f5201eb6be",
		ServiceID: "539a1610-eb40-48f6-9e4d-cbe494ad4bcd",
		Data:      map[string]interface{}{"key": "value"},
	}

	// Mock the RoundTripper
	mockRoundTripper := new(MockRoundTripper)

	// Create an http.Client with the mock RoundTripper
	httpClient := &http.Client{
		Transport: mockRoundTripper,
	}

	opts := octopus.InvokeOptions{
		ValidateWithStruct:       true,
		RetryOnValidationFailure: false,
		ValidationStruct:         &YourValidationStruct{},
		HTTPClient:               httpClient,
		CustomRetryAttempts:      3,
	}

	// Mock HTTP response with missing required fields (to trigger validation failure)
	mockResp := &http.Response{
		StatusCode: 200,
		Body:       io.NopCloser(bytes.NewBufferString(`{"data": {"pdfUrl":"some-url"}, "msg":"invoked"}`)),
		Header:     http.Header{},
	}

	mockRoundTripper.On("RoundTrip", mock.Anything).Return(mockResp, nil)

	// Call InvokeWithOptions
	_, err := octopus.InvokeWithOptions(ctx, userID, serviceName, payload, opts)

	// Assert validation failure without retry
	assert.Error(t, err)                               // Expect an error due to validation failure
	assert.Equal(t, 1, mockRoundTripper.GetHitCount()) // Expecting 1 hit (no retry)
}

func TestInvokeWithOptions_FailedHTTPRequest(t *testing.T) {
	ctx := context.Background()
	userID := "2d6ee31d-f1a0-4153-90ea-cb376354bdc3"
	serviceName := "test-service"
	payload := octoclient.OctoPayloadGeneric{
		RequestID: "4a1dc453-cb46-445f-acb6-48f5201eb6be",
		ServiceID: "539a1610-eb40-48f6-9e4d-cbe494ad4bcd",
		Data:      map[string]interface{}{"key": "value"},
	}

	// Mock the RoundTripper
	mockRoundTripper := new(MockRoundTripper)

	// Create an http.Client with the mock RoundTripper
	httpClient := &http.Client{
		Transport: mockRoundTripper,
	}

	opts := octopus.InvokeOptions{
		HTTPClient: httpClient,
	}

	// Mock HTTP response with non-200 status code
	mockResp := &http.Response{
		StatusCode: 500,
		Body:       io.NopCloser(bytes.NewBufferString(`Internal Server Error`)),
		Header:     http.Header{},
	}

	mockRoundTripper.On("RoundTrip", mock.Anything).Return(mockResp, nil)

	// Call InvokeWithOptions
	_, err := octopus.InvokeWithOptions(ctx, userID, serviceName, payload, opts)

	// Assert that an error is returned for non-200 status code
	assert.Error(t, err)                               // Expect an error due to non-200 status
	assert.Contains(t, err.Error(), "status code 500") // Ensure error contains correct status code
	assert.Equal(t, 4, mockRoundTripper.GetHitCount()) // Expecting 4 hit (default retry attempts)
}
