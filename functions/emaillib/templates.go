// Package emaillib has templates and utils related to emails
package emaillib

// verification of customer email (first time)
// const VerificationEmailSubject = "Verify your email for Freedom Loans"
// const VerificationEmailHTML = `
// <html>
// <body>
// Dear {{.Name}},<br /><br />
// Thank you for downloading Freedom Loans app. You are just a few steps away from your loan application. Please verify your email by clicking the link below.<br /><br />
// <a href="{{.EmailVerificationLink}}">{{.EmailVerificationLink}}</a>
// <br /><br />
// Best Regards
// <br /><br />
// Freedom Loans
// </body>
// </html>`

// loan disbursal notification to customer
const (
	DisbursalEmailSubject = "Funds disbursed to bank account"
	DisbursalEmailHTML    = `
<html>
<body>
Dear {{.Name}},<br /><br />
We are happy to inform you that in reference to your application for a loan (ID: {{.LoanID}}) of ₹{{.LoanAmount}}, ₹{{.DisbursalAmount}} has been credited to your bank account via NEFT Reference number {{.TransactionNum}}.
<br /><br />
<br /><br />
<b>Disbursal Summary</b>
<br /><br />
<table style="border-collapse: collapse;border: 1px solid black;">
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">Loan application amount</th>
    <td>₹{{.LoanAmount}}</td>
  </tr>
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">Processing fee (inclusive of GST)</th>
    <td>₹{{.ProcessingFeePlusGST}}</td>
  </tr>
  <tr style="border: 1px solid black;">
  <th style="text-align:left;border: 1px solid black;">Insurance Premium (inclusive of GST)</th>
  <td>₹{{.InsurancePremiumPlusGST}}</td>
  </tr>
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">Pre EMI Amount</th>
    <td>₹{{.AdvanceEmi}}</td>
  </tr>
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">Disbursal Amount</th>
    <td>₹{{.DisbursalAmount}}</td>
  </tr>
</table>
<br /><br />
<b>Repayment Details</b>
<br /><br />
<table style="border-collapse: collapse;border: 1px solid black;">
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">Tenure</th>
    <td>{{.Tenure}} months</td>
  </tr>
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">Interest</th>
    <td>{{.Interest}}% per annum</td>
  </tr>
</table>
<br /><br />
<table style="border-collapse: collapse;border: 1px solid black;">
  <tr style="border: 1px solid black;">
    <th style="border: 1px solid black;">Due Date</th>
    <th style="border: 1px solid black;">EMI</th>
  </tr>
  {{.InstallmentRowHTML}}
</table>
<br /><br />
We are committed to providing our customers with the highest level of service. Kindly use the Loan Application Number in all your further communications with us. To contact us, you can email us at {{.SupportEmail}}. 
<br /><br />
Best Regards
<br /><br />
{{.LoanAppName}}
<br /><br />
Powered by FinBox
</body>
</html>`

	DisbursalEmailMFLHTML = `
<html>
<body>
Dear {{.Name}},<br /><br />
We are happy to inform you that in reference to your application for a loan (ID: {{.LoanID}}) of ₹{{.LoanAmount}}, ₹{{.DisbursalAmount}} has been credited to your bank account via NEFT Reference number {{.TransactionNum}}.
<br /><br />
<br /><br />
<b>Disbursal Summary</b>
<br /><br />
<table style="border-collapse: collapse;border: 1px solid black;">
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">Loan application amount</th>
    <td>₹{{.LoanAmount}}</td>
  </tr>
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">Processing fee (inclusive of GST)</th>
    <td>₹{{.ProcessingFeePlusGST}}</td>
  </tr>
  <tr style="border: 1px solid black;">
  <th style="text-align:left;border: 1px solid black;">Insurance Premium (inclusive of GST)</th>
  <td>₹{{.InsurancePremiumPlusGST}}</td>
  </tr>
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">Pre {{.InstallmentProgramme}} Amount</th>
    <td>₹{{.AdvanceEmi}}</td>
  </tr>
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">Disbursal Amount</th>
    <td>₹{{.DisbursalAmount}}</td>
  </tr>
</table>
<br /><br />
<b>Repayment Details</b>
<br /><br />
<table style="border-collapse: collapse;border: 1px solid black;">
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">Tenure</th>
    <td>{{.Tenure}} {{if eq .InstallmentProgramme "EMI"}}  Months {{else}} Days {{end}}</td>
  </tr>
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">Interest</th>
    <td>{{.Interest}}% per annum</td>
  </tr>
</table>
<br /><br />
<table style="border-collapse: collapse;border: 1px solid black;">
  <tr style="border: 1px solid black;">
    <th style="border: 1px solid black;">Due Date</th>
    <th style="border: 1px solid black;"> {{if eq .InstallmentProgramme "EMI"}} EMI {{else}} EDI {{end}}</th>
  </tr>
  {{if eq .InstallmentProgramme "EMI"}}
  {{.InstallmentRowHTML}}
  {{else}}
  {{.EDIInstallmentRowHTML}}
  {{end}}
</table>
<br /><br />
We are committed to providing our customers with the highest level of service. Kindly use the Loan Application Number in all your further communications with us. To contact us, you can email us at {{.SupportEmail}}. 
<br /><br />
Best Regards
<br />
Muthoot FinCorp Ltd.
<br /><br />
Powered by FinBox
</body>
</html>`
)

// email sent to customer after loan is e-signed
const (
	WelcomeEmailSubject = "Welcome to {{.LoanAppName}} Family!"
	WelcomeEmailHTML    = `
<html>
<body>
Hi {{.Name}},<br /><br />

Thank you for applying for loan at {{.LoanAppName}}{{.ThroughApp}}. Your loan application has been acknowledged by {{.LenderName}}. Your funds will be disbursed within 2 working days, subject to final approval. The terms and conditions of the loans are mentioned in the attached signed agreement.
<br /><br />
<br /><br />
Your loan details are mentioned below. EMI amount of ₹{{.Emi}} will be deducted every month for the next {{.Tenure}} months.
<br /><br />
<table style="border-collapse: collapse;border: 1px solid black;">
  <tr style="border: 1px solid black;">
  <th style="text-align:left;border: 1px solid black;">Loan Application Number</th>
  <td>{{.LoanApplicationNum}}</td>
  </tr>
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">Loan Application Amount</th>
    <td>₹{{.LoanAmount}}</td>
  </tr>
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">Processing Fee (inclusive of GST)</th>
    <td>₹{{.ProcessingFeePlusGST}}</td>
  </tr>
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">Pre EMI (subjected to disbursal date)</th>
    <td>₹{{.AdvanceEmiAmount}}</td>
  </tr>
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">Insurance Premium (inclusive of GST)</th>
    <td>₹{{.InsurancePremiumPlusGST}}</td>
  </tr>
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">Final Disbursal Amount (subjected to disbursal date)</th>
    <td>₹{{.DisbursalAmount}}</td>
  </tr>
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">Tenure</th>
    <td>{{.Tenure}} months</td>
  </tr>
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">Interest</th>
    <td>{{.Interest}}% per annum</td>
  </tr>
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">EMI Amount</th>
    <td>₹{{.Emi}}</td>
  </tr>
</table>
<br /><br />
We are committed to providing our customers with the highest level of service. Kindly use the Loan Application Number in all your further communications with us. To contact us, you can email us at {{.SupportEmail}}. 
<br /><br />
Best Regards
<br /><br />
{{.LoanAppName}}
<br /><br />
Powered by FinBox
</body>
</html>`
)

// mfl bl email changes
const (
	WelcomeEmailSubjectMFLBL = "Welcome to Muthoot FinCorp Limited!"
	WelcomeEmailHTMLMFLBL    = `
<html>
<body>
Dear {{.Name}},<br /><br />

We are delighted that you have chosen us as your Business Loan partner. Your Loan application has been successfully processed.<br/>
Your funds will be disbursed to your account within 2 working days. Please note that the total interest payable on your Loan may vary depending on the actual disbursal date. <br/>
Attached is the signed agreement outlining the Terms and Conditions of your Loan. Kindly take a moment to review it for your reference. <br/><br/>

Your loan details are mentioned below. <br/> 
{{if eq .InstallmentProgramme "EMI"}}
<b> EMI amount of ₹{{.Emi}} will be deducted every month for the next {{.Tenure}} months.</b> 
{{else}}
<b> Easy Daily Installment (EDI) amount of ₹{{.Edi}} will be deducted daily.</b>
{{end}}

<br/><br/>

<table style="border-collapse: collapse;border: 1px solid black;">
  <tr style="border: 1px solid black;">
  <th style="text-align:left;border: 1px solid black;">Loan Application Number</th>
  <td>{{.LoanApplicationNum}}</td>
  </tr>
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">Loan Application Amount</th>
    <td>₹{{.LoanAmount}}</td>
  </tr>
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">Processing Fee (inclusive of GST)</th>
    <td>₹{{.ProcessingFeePlusGST}}</td>
  </tr>
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">Pre {{.InstallmentProgramme}} (subjected to disbursal date)</th>
    <td>₹{{.AdvanceEmiAmount}}</td>
  </tr>
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">Insurance Premium (inclusive of GST)</th>
    <td>₹{{.InsurancePremiumPlusGST}}</td>
  </tr>
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">Final Disbursal Amount (subjected to disbursal date)</th>
    <td>₹{{.DisbursalAmount}}</td>
  </tr>
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">Tenure</th>
    <td>{{.Tenure}} {{if eq .InstallmentProgramme "EMI"}}  Months {{else}} Days {{end}}</td>
  </tr>
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">Interest</th>
    <td>{{.Interest}}% per annum</td>
  </tr>
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">{{.InstallmentProgramme}} Amount</th>
    <td>₹  {{if eq .InstallmentProgramme "EMI"}} {{.Emi}} {{else}}  {{.Edi}} {{end}}</td>
  </tr>
</table>
<br /><br />
We are committed to delivering the highest level of service to our customers. To ensure we assist you efficiently, please mention your Loan Application Number in all future communication with us. 
<br/>
For any assistance, feel free to reach out to us at {{.SupportEmail}}.
<br/>
Thank you for choosing us as your trusted financial partner. We look forward to supporting your business journey.
<br /><br />
Warm Regards <br />
Muthoot FinCorp Ltd.
<br /><br />
Powered by FinBox
</body>
</html>`

	WelcomeEmailHTMLABFL = `
<html>
<body>
Hi {{.Name}},
<br /><br />
Thank you for applying for loan at {{.LoanAppName}}{{.ThroughApp}}. Your loan application has been acknowledged by {{.LenderName}}. Your funds will be disbursed within 2 working days, subject to final approval. The terms and conditions of the loans are mentioned in the attached signed agreement.
<br /><br />
<br /><br />
Your loan details are mentioned below. EMI amount of ₹{{.Emi}} will be deducted every month for the next {{.Tenure}} months.
<br /><br />
<table style="border-collapse: collapse;border: 1px solid black;">
  <tr style="border: 1px solid black;">
  <th style="text-align:left;border: 1px solid black;">Loan Application Number</th>
  <td>{{.LoanApplicationNum}}</td>
  </tr>
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">Loan Application Amount</th>
    <td>₹{{.LoanAmount}}</td>
  </tr>
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">Processing Fee (inclusive of GST)</th>
    <td>₹{{.ProcessingFeePlusGST}}</td>
  </tr>
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">Insurance Premium (inclusive of GST)</th>
    <td>₹{{.InsurancePremiumPlusGST}}</td>
  </tr>
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">Final Disbursal Amount (subjected to disbursal date)</th>
    <td>₹{{.DisbursalAmount}}</td>
  </tr>
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">Tenure</th>
    <td>{{.Tenure}} months</td>
  </tr>
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">Interest</th>
    <td>{{.Interest}}% per annum</td>
  </tr>
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">EMI Amount</th>
    <td>₹{{.Emi}}</td>
  </tr>
</table>
<br /><br />
We are committed to providing our customers with the highest level of service. Kindly use the Loan Application Number in all your further communications with us. To contact us, you can email us at {{.SupportEmail}}. 
<br /><br />
Best Regards
<br /><br />
{{.LoanAppName}}
<br /><br />
Powered by FinBox
</body>
</html>`
)

// email sent to lender after kyc is done
const (
	LenderKitEmailSubject = "KYC and Underwriting for {{.UserName}} | {{.LoanID}}"
	LenderKitEmailHTML    = `
<html>
Hi Team,<br /><br />
Please find attached the details for following loan.
<br /><br />
<h2>Basic Details &#9989;</h2>
<table style="border-collapse: collapse;border: 1px solid black;">
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">Customer Name</th>
    <td>{{.UserName}}</td>
  </tr>
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">PAN</th>
    <td>{{.PanNumber}}</td>
  </tr>
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">Current Address	</th>
    <td>{{.CurrentAddress}}</td>
  </tr>
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">Pincode</th>
    <td>{{.PinCode}}</td>
  </tr>
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">Mobile Number</th>
    <td>{{.Mobile}}</td>
  </tr>
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">Email</th>
    <td>{{.Email}}</td>
  </tr>
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">DOB</th>
    <td>{{.DOB}}</td>
  </tr>
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">Age</th>
    <td>{{.Age}}</td>
  </tr>
</table>
<br /><br />
<h2>KYC Docs Submitted &#9989;</h2>
<table style="border-collapse: collapse;border: 1px solid black;">
  {{.KycDocsHTML}}
</table>
<br /><br />
<h2>Credit Underwriting Details</h2>
<table style="border-collapse: collapse;border: 1px solid black;">
<tr style="border: 1px solid black;">
  <td style="text-align:left;border: 1px solid black;">Partner verification</td>
  <td>PASS</td>
</tr>
{{.UnderwritingHTML}}
</table>
<br /><br />
<h2>Document Details</h2>
<table style="border-collapse: collapse;border: 1px solid black;">
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">Loan Agreement</th>
    <td>Attached</td>
  </tr>
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">KYC Docs</th>
    <td>Attached</td>
  </tr>
</table>
<br /><br />
<h2>Loan Details</h2>
<table style="border-collapse: collapse;border: 1px solid black;">
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">Loan ID</th>
    <td>{{.LoanID}}</td>
  </tr>
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">Loan Type</th>
    <td>{{.LoanType}}</td>
  </tr>
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">Disbursal Amount</th>
    <td>₹{{.DisbursalAmount}}</td>
  </tr>
</table>
<br /><br />
<h2>EMI Details</h2>
<table style="border-collapse: collapse;border: 1px solid black;">
  <tr style="border: 1px solid black;">
    <th>Due Date</th>
    <th>EMI</th>
  </tr>
  {{.InstallmentRowHTML}}
</table>
<br /><br />
<br /><br />
Best Regards
<br /><br />
FinBox
</html>`
)

// email sent to lender after e-sign is done
const (
	LenderKitEmailSignedSubject = "Loan Details for {{.LoanID}}"
	LenderKitEmailSignedHTML    = `
<html>
Hi Team,<br /><br />
Please find attached the details for following loan.
<br /><br />
<h2>Basic Details &#9989;</h2>
<table style="border-collapse: collapse;border: 1px solid black;">
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">Customer Name</th>
    <td>{{.UserName}}</td>
  </tr>
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">PAN</th>
    <td>{{.PanNumber}}</td>
  </tr>
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">Current Address	</th>
    <td>{{.CurrentAddress}}</td>
  </tr>
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">Pincode</th>
    <td>{{.PinCode}}</td>
  </tr>
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">Mobile Number</th>
    <td>{{.Mobile}}</td>
  </tr>
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">Email</th>
    <td>{{.Email}}</td>
  </tr>
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">DOB</th>
    <td>{{.DOB}}</td>
  </tr>
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">Age</th>
    <td>{{.Age}}</td>
  </tr>
</table>
<br /><br />
<h2>KYC Docs Submitted &#9989;</h2>
<table style="border-collapse: collapse;border: 1px solid black;">
  {{.KycDocsHTML}}
</table>
<br /><br />
<h2>Bank Account Details &#9989;</h2>
<table style="border-collapse: collapse;border: 1px solid black;">
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">Bank Account Number</th>
    <td>{{.AccountNumber}}</td>
  </tr>
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">IFSC Code</th>
    <td>{{.IFSC}}</td>
  </tr>
</table>
<br /><br />
<h2>Document Details</h2>
<table style="border-collapse: collapse;border: 1px solid black;">
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">Loan Agreement</th>
    <td>Attached</td>
  </tr>
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">KYC Docs</th>
    <td>Attached</td>
  </tr>
</table>
<br /><br />
<h2>Loan Details</h2>
<table style="border-collapse: collapse;border: 1px solid black;">
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">Loan ID</th>
    <td>{{.LoanID}}</td>
  </tr>
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">Loan Type</th>
    <td>{{.LoanType}}</td>
  </tr>
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">Disbursal Amount</th>
    <td>₹{{.DisbursalAmount}}</td>
  </tr>
</table>
<br /><br />
<h2>EMI Details</h2>
<table style="border-collapse: collapse;border: 1px solid black;">
  <tr style="border: 1px solid black;">
    <th>Due Date</th>
    <th>EMI</th>
  </tr>
  {{.InstallmentRowHTML}}
</table>
<br /><br />
<br /><br />
Best Regards
<br /><br />
FinBox
</html>`
)

// email sent to sourcing entity for authorization letter physical signature
const (
	AuthorizationLetterEmailSubject = "Authorization letter for {{.UserName}} | ID: {{.UniqueID}}"
	AuthorizationLetterEmailHTML    = `
<html>
Hi Team,<br /><br />

Please find attached the salary deduction authorization letter for the following loan
<br /><br />
<table style="border-collapse: collapse;border: 1px solid black;">
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">Loan Application ID</th>
    <td>{{.LoanID}}</td>
  </tr>
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">Employee ID</th>
    <td>{{.UniqueID}}</td>
  </tr>
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">Customer Name</th>
    <td>{{.UserName}}</td>
  </tr>
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">Customer Phone Number</th>
    <td>{{.Mobile}}</td>
  </tr>
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">Customer Address</th>
    <td>{{.CurrentAddress}}</td>
  </tr>
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">Customer Pincode</th>
    <td>{{.PinCode}}</td>
  </tr>
</table>
<br /><br />

Best Regards
<br /><br />
FinBox
</html>`
)

// email sent to customer after credit line activation
const CreditLineActivationEmailSubject = "Credit Line Activated for {{.LoanAppName}} (Powered by FinBox)"

/* #nosec */
const CreditLineActivationEmailHTML = `
<html>
<body>
Hi {{.Name}},<br /><br />

Thank you for applying for credit line facility at {{.LoanAppName}}. We have reviewed your profile and are glad to inform you that your application has been approved by {{.LenderName}}. You can start using the facility during checkout. The terms and conditions of the facility are mentioned in the attached signed agreement.
<br /><br />
<br /><br />
Your credit line details are mentioned below. 
<br /><br />
<table style="border-collapse: collapse;border: 1px solid black;">
  <tr style="border: 1px solid black;">
  <th style="text-align:left;border: 1px solid black;">Application Number</th>
  <td>{{.LoanApplicationNum}}</td>
  </tr>
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">Credit Line Amount</th>
    <td>₹{{.LoanAmount}}</td>
  </tr>
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">Valid Till</th>
    <td>{{.Tenure}} months</td>
  </tr>
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">Interest</th>
    <td>{{.Interest}}% per annum</td>
  </tr>
</table>
<br /><br />

We are committed to providing our customers with the highest level of service. Kindly use the Loan Application Number in all your further communications with us. To contact us, you can email us at {{.SupportEmail}}. 
<br /><br />
Best Regards
<br /><br />
{{.LoanAppName}}
<br /><br />
Powered by FinBox
</body>
</html>`

// email sent to customer after credit line withdrawl
/* #nosec */
const CreditLineWithdrawlEmailSubject = "{{.OrderAmount}} charged via {{.LoanAppName}} (Powered by FinBox)"

/* #nosec */
const CreditLineWithdrawlEmailHTML = `
<html>
<body>
Hi {{.Name}},<br /><br />
Thank you for using your credit limit for a purchase of {{.OrderAmount}} at {{.LoanAppName}}.
<br /><br />
<br /><br />
The details of your order are mentioned below. 
<br /><br />
<table style="border-collapse: collapse;border: 1px solid black;">
  <tr style="border: 1px solid black;">
  <th style="text-align:left;border: 1px solid black;">Order Number</th>
  <td>{{.OrderID}}</td>
  </tr>
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">Withdrawal Amount</th>
    <td>₹{{.OrderAmount}}</td>
  </tr>
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">Repayment</th>
    <td>{{.RepaymentTenure}}</td>
  </tr>
</table>
<br /><br />
In case of payment delay, following late fees and interest will be applicable
<table style="border-collapse: collapse;border: 1px solid black;">
  <tr style="border: 1px solid black;">
  <th style="text-align:left;border: 1px solid black;">EMI Bounce Charges</th>
  <td>{{.BounceCharge}}</td>
  </tr>
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">Late Fee</th>
    <td>{{.LateFee}}</td>
  </tr>
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">Interest Amount Charged per day at </th>
    <td>{{.Interest}} % per month</td>
  </tr>
</table>
Note that your account will be automatically debited for ₹{{.TotalPayable}} months after delivery. Kindly keep your account funded.
<br /><br />
We are committed to providing our customers with the highest level of service. To contact us, you can email us at {{.SupportEmail}}. 
<br /><br />
Best Regards
<br /><br />
{{.LoanAppName}}
<br /><br />
Powered by FinBox
</body>
</html>`

// email sent to platform after credit line withdrawl
const (
	CLWithdrawlPlatformEmailSubject = "{{.Mobile}} - # {{.OrderID}} - ₹{{.OrderAmount}} | {{.LoanAppName}} powered by FinBox"
	CLWithdrawlPlatformEmailHTML    = `
<html>
<body>
Hi Team,<br /><br />
<br /><br />
New Transaction has been created with following details:
<br /><br />
<table style="border-collapse: collapse;border: 1px solid black;">
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">Customer Mobile</th>
    <td>{{.Mobile}}</td>
  </tr>
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">Customer Name</th>
    <td>{{.Name}}</td>
  </tr>
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">Partner Transaction ID</th>
    <td>{{.OrderID}}</td>
  </tr>
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">Amount</th>
    <td>₹{{.OrderAmount}}</td>
  </tr>
</table>

<br /><br />
Best Regards
<br /><br />
FinBox
</body>
</html>`
)

// email sent to lender after credit line withdrawl confirmation
const LenderCreditLineWithdrawlEmailSubject = "Credit Line withdrawl {{.OrderID}} - {{.LoanApplicationNo}}"

/* #nosec */
const LenderCreditLineWithdrawlEmailHTML = `
<html>
<body>
Please find attached the details for following credit line withdrawl.
<br /><br />
<h2>Basic Details &#9989;</h2>
<table style="border-collapse: collapse;border: 1px solid black;">
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">Customer Name</th>
    <td>{{.UserName}}</td>
  </tr>
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">PAN</th>
    <td>{{.PanNumber}}</td>
  </tr>
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">Mobile Number</th>
    <td>{{.Mobile}}</td>
  </tr>
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">Email</th>
    <td>{{.Email}}</td>
  </tr>
</table>
<br /><br />
<h2>Withdrawl Details &#9989;</h2>
<table style="border-collapse: collapse;border: 1px solid black;">
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">Parent Loan ID</th>
    <td>{{.LoanApplicationNo}}</td>
  </tr>
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">Transaction ID</th>
    <td>{{.OrderID}}</td>
  </tr>
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">Transaction Amount</th>
    <td>{{.OrderAmount}}</td>
  </tr>
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">Repayment</th>
    <td>{{.RepaymentTenure}}</td>
  </tr>
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">Total Interest</th>
    <td>{{.Interest}}% per month</td>
  </tr>
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">Processing Fee</th>
    <td>{{.ProcessingFee}}</td>
  </tr>
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">GST</th>
    <td>{{.GST}} %</td>
  </tr>
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">Disbursal Amount</th>
    <td>{{.DisbursalAmount}}</td>
  </tr>
</table>
<br /><br />
Attachments if present indicates the proof of transaction/order confirmation.
<br /><br />
Best Regards
<br /><br />
FinBox
</body>
</html>`

// email sent to customer with physical mandate form
const (
	PhysicalMandateEmailSubject = "Auto Pay Form for {{.LoanAppName}} (Powered by FinBox)"
	PhysicalMandateEmailHTML    = `
<html>
<body>
Hi {{.Name}},<br /><br />
Please find attached the Auto Pay Form.
<br /><br />
Next step is to print out the form, sign right above your name with a dark ink pen and upload its picture in the app.
<br /><br />
In case of any issues, email us at {{.SupportEmail}}. 
<br /><br />
Best Regards
<br /><br />
{{.LoanAppName}}
<br /><br />
Powered by FinBox
</body>
</html>`
)

// email sent to ops team on physical mandate approval / reject
const (
	PhysicalMandateAlertEmailSubject = "Physical Mandate - {{.SourceEntityName}} - {{.LoanID}} - {{.Status}}"
	PhysicalMandateAlertEmailBody    = `
<html>
<body>
Physical Mandate Status
<table 
<br /><br />
<table style="border-collapse: collapse;border: 1px solid black;">
  <tr style="border: 1px solid black;">
  <th style="text-align:left;border: 1px solid black;">Platform</th>
  <td>{{.SourceEntityName}}</td>
  </tr>
  <tr style="border: 1px solid black;">
  <th style="text-align:left;border: 1px solid black;">Loan ID</th>
  <td>{{.LoanID}}</td>
  </tr>
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">Status</th>
    <td>{{.Status}}</td>
  </tr>
  <tr style="border: 1px solid black;">
    <th style="text-align:left;border: 1px solid black;">Failure Reason</th>
    <td>{{.Reason}}</td>
  </tr>
</table>
<br /><br />
Best Regards
<br /><br />
FinBox Team
</body>
</html>`
)

// email sent while adding a partner
const (
	PartnerAddEmailSubject = "{{.LenderName}} invited you to FinBox Dashboard"
	PartnerAddEmailBody    = `
<html>
<body>
Dear {{.PartnerName}},<br /><br />
{{.LenderName}} has invited you to signup for FinBox Embedded Finance Partner Dashboard. Sign up from the link below. Should you have any questions, please reach <NAME_EMAIL>
<br /><br />
<a href="{{.InviteURL}}">{{.InviteURL}}</a>
<br /><br />
Best
<br /><br />
FinBox Team
</body>
</html>`
)

// email sent after signup
const (
	SignUpEmailSubject = "Welcome to FinBox Embedded Finance!"
	SignUpEmailBody    = `
<html>
<body>
Hi {{.Name}},<br /><br />
Welcome to FinBox Embedded Finance! Your account for {{.CompanyName}} is ready, and you can log in <a href="https://platformuat.finbox.in/">here</a>.
<br /><br />
Should you have any questions, please reach <NAME_EMAIL>
<br /><br />
Best
<br /><br />
FinBox Team
</body>
</html>`
)

// email sent to customer for the export data
const (
	ExportDumpEmailSubject = "FinBox Data Export - {{.NameHeading}} - {{.Type}} Data Dump CSV"
	ExportDumpEmailHTML    = `
<html>
<body>
Hi<br /><br />
Please find below the CSV file containing {{.Type}} data for {{.Filter}} date range {{.FromDate}} to {{.ToDate}}
<br /><br />
{{.Link}}
<br /><br />
Best
<br /><br />
FinBox Team
</body>
</html>`
)

const (
	AdhocNachPresentationEmailSubject = "FinBox Adhoc {{.LoanType}} NACH Presentation Report"
	AdhocNachPresentationEmailHTML    = `
<html>
<body>
Hi<br /><br />
Please find attached the CSV file containing Adhoc NACH presentation for {{.LoanType}} on {{.Date}}
<br />
Request ID for this presentation is <b>{{.RequestID}}</b>
<br /><br />
Best
<br /><br />
FinBox Team
</body>
</html>`
)

// AddMemberEmailHTML is the email sent to admin, new user in masterdashboard apis
const AddMemberEmailHTML = `
<html>
<body>
Hi<br /><br />
Please find the credentials data for {{.Name}} <br /><br />
Email : {{.Email}} <br /><br />
Password {{.Password}}<br /><br />
<br /><br />
Best
<br /><br />
FinBox Team
</body>
</html>`

const (
	AdhocNachNoPresentationEmailSubject = "FinBox Adhoc NACH Presentation Report"
	AdhocNachNoPresentationEmailHTML    = `
<html>
<body>
Hi<br /><br />
No presentation initiated for the requested sheet.
<br />
Please contact with the support team for further assistance
<br /><br />
Best
<br /><br />
FinBox Team
</body>
</html>`
)

const (
	AdhocNachPresentationFailedEmailSubject = "FinBox Adhoc NACH Presentation Report"
	AdhocNachPresentationFailedEmailHTML    = `
<html>
<body>
Hi<br /><br />
Adhoc presentation for Request ID {{.RequestID}} failed.
<br />
Please contact with the support team for further assistance
<br /><br />
Best
<br /><br />
FinBox Team
</body>
</html>`
)

const (
	AdhocNachTriggerDAGFailedEmailSubject = "FinBox Adhoc NACH DAG Trigger Failed"
	AdhocNachTriggerDAGFailedHTML         = `
<html>
<body>
Hi<br /><br />
Adhoc presentation for Request ID {{.RequestID}} failed due to failure to trigger the DAG.
<br />
API response body:
{{.APIResp}}
<br /><br />
Best
<br /><br />
FinBox Team
</body>
</html>`
)

const (
	WelcomeSubjectForMasterDashboard = "Welcome to {{.Organization}} Platform Dashboard!"
	WelcomeHTMLForMasterDashboard    = `
Hi {{.Name}},<br /><br />
Welcome to {{.Organization}} Platform Dashboard! Your account is ready, and you can log in <a href="{{.MasterDashboardFrontendURL}}">here</a> with following credentials:<br />
Email: <b>{{.Email}}</b><br />
Password: <b>{{.Password}}</b><br /><br />
PS: Please change your password after logging in for the first time.
<br /><br />
For any further queries, you can reach out to <NAME_EMAIL>
<br /><br />
You are receiving this email because you were invited on FinBox Platform Dashboard. Please ignore this email if you have received this by mistake.
<br /><br />
Best,<br />
Team FinBox<br />
https://finbox.in`
)

const PasswordChangedSubject = "Account Activity: Password changed"

/* #nosec */
const PasswordChangedHTML = `
Hi {{.Name}},<br /><br />
We noticed the password for your FinBox account was recently changed. If this was you, you can safely ignore this email.<br /><br />
<b>When</b> {{.WhenDateString}}<br />
<b>From IP Address</b> {{.IPAddress}}<br /><br />
In case you have not changed the password, please inform us immediately by sending a <NAME_EMAIL>
<br /><br />
Regards,<br />
Team FinBox<br />
https://finbox.in`

const VKycVerificationHTML = `
Dear {{.Name}},<br /><br />
Please complete your Udyog Plus Video KYC Verification using the following link.<br /><br />
 {{.VKycLinkString}}<br />
Best,<br />
Team ABCL<br />`

const MFLVKycVerificationHTML = `
Dear {{.Name}},<br /><br />
Please complete your  Video KYC Verification using the following link.<br /><br />
 {{.VKycLinkString}}<br />
Best,<br />
Team MFL<br />`

const ABFLSanctionTaskWorkflowEmailSubject = `
Sanction Task Workflow request {{.TaskName}} for {{.NextActionTeamGroup}} - Loan Application ID: {{.LoanApplicationNo}}
`
const ABFLSanctionTaskWorkflowEmail = `
Hi Team,<br /><br />

A new Sanction Task Workflow request, <strong>{{.TaskName}}</strong>, has been created for the <strong>{{.NextActionTeamGroup}}</strong> Team associated with Loan Application ID: <strong>{{.LoanApplicationNo}}</strong>.<br /><br />

Please click the link below to process the request:<br /><br />

<a href="{{.DashboardLink}}" target="_blank">Click here to access the dashboard</a><br /><br />

Regards,<br />
The FinBox Team<br />`

const ABFLPLSanctionAssignmentTaskWorkflowEmailSubject = `
Query Responded | {{.LoanApplicationNo}}
`
const ABFLPLSanctionAssignmentTaskWorkflowEmailBody = `
Hi,<br /><br />

The query raised by <strong>{{.TaskCreatorGroup}}</strong> team for loan application id <strong>{{.LoanApplicationNo}}</strong> has been responded by <strong>{{.CurrentActionTeamGroup}}</strong> and assigned back to <strong>{{.TaskCreatorGroup}}</strong>.<br /><br />

Employment Type: <strong>{{.EmploymentType}}</strong><br /><br />

Please click the link below to process the request:<br /><br />

<a href="{{.DashboardLink}}" target="_blank">Click here to access the dashboard</a><br /><br />

Best,<br />
The FinBox Team<br />`

const ABFLPLSanctionCreationTaskWorkflowEmailSubject = `
Query Created | {{.LoanApplicationNo}}
`

const ABFLPLSanctionCreationTaskWorkflowEmailBody = `
Hi,<br /><br />

A query has been raised by <strong>{{.TaskCreatorGroup}}</strong> team for loan application id <strong>{{.LoanApplicationNo}}</strong>.<br /><br />

Employment Type: <strong>{{.EmploymentType}}</strong><br /><br />

Please click the link below to process the request:<br /><br />

<a href="{{.DashboardLink}}" target="_blank">Click here to access the dashboard</a><br /><br />

Best,<br />
The FinBox Team<br />`

const PasswordChangedSubjectABFL = "Account Activity: Password changed"

const PasswordChangedHTMLABFL = `
Hi {{.Name}},<br /><br />
We noticed the password for your ABCL Dashboard account was recently changed. If this was you, you can safely ignore this email.<br /><br />
<b>When</b> {{.WhenDateString}}<br />
<b>From IP Address</b> {{.IPAddress}}<br /><br />
In case you have not changed the password, please inform us immediately by replying to this mail
<br /><br />
Regards,<br />
Team ABCL<br />`

const LifeInsuranceHTMLABFL = `
Dear {{.Name}},<br /><br />
I hope this message finds you well.<br /><br />
A warm welcome from Aditya Birla Capital Limited! We are pleased to have you with us.<br /><br />
Your financial journey with Loan Account Number - {{.LoanAccountNumber}} is now secured with our ABSLI Group Smart Supreme Plan.<br /><br />
Please find attached your Certificate of Insurance, which provides details about your coverage and other insurance specifics. In case of any queries, feel free to reach out on ______ or you can write us on________ <br /><br />
Thank you for your trust in us. We are delighted to have you as part of our family and look forward to serving you with excellence. <br /><br />

Kind regards, <br /><br />
Team ABCL <br /><br />
`

const (
	InsuranceSubjectABFL = "Welcome! Here’s Your Aditya Birla Insurance Certificate"

	InsuranceHTMLABFL = `
  Dear {{.Name}}, <br /><br />
  
  I hope this message finds you well.<br /><br />
  
  A warm welcome from Aditya Birla Capital Limited! We are pleased to have you with us. <br /><br />
  
  Your financial journey with Loan Account Number {{.LoanAccountNumber}} is now secured with our Aditya Birla Health Insurance.<br /><br />
  
  Please find attached your Certificate of Insurance, which provides details about your coverage and other insurance specifics. In case of any queries, feel free to reach out on 1800-270 7000 or you can write <NAME_EMAIL> <br /><br />
  
  Thank you for your trust in us. We are delighted to have you as part of our family and look forward to serving you with excellence.<br /><br />
  
  Kind regards,<br /><br />
  Team ABCL <br /><br />`
)

const PasswordResetByAdminSubject = "Account Activity: Password reset requested"

/* #nosec */
const PasswordResetByAdminHTML = `
Hi {{.Name}},<br /><br />
A password reset request was raised for your FinBox account by admin.<br /><br />
<b>New Password</b> {{.NewPassword}}<br /><br />
In case you have not made this request, please login using the above password and change the password immediately. You can also send an <NAME_EMAIL> for further assistance.<br /><br />
Regards,<br />
Team FinBox<br />
https://finbox.in`

const (
	ExportPaymentDumpEmailSubject = "FinBox Data Export - {{.RequestID}} - {{.Type}} Data Dump CSV"
	ExportPaymentDumpEmailHTML    = `
<html>
<body>
Hi<br /><br />
Please find attached the CSV file containing {{.Type}} data for {{.Filter}} requestID {{.RequestID}}
<br /><br />
Best
<br /><br />
FinBox Team
</body>
</html>
`
)

const (
	IIFLDREMakerEmailSubject = "DRE Maker Report - {{.RequestID}} - {{.Date}}"
	IIFLDREMakerEmailHTML    = `
<html>
<body>
Hi<br /><br />
Please find the attached CSV File containing DRE Maker Report
<br /><br />
This was requested by {{.Email}}
<br /><br />
Best
<br /><br />
FinBox Team
</body>
</html>`
)

const TataCreditLineTxnCallbackHTML = `
<html>
<body>
Hi<br /><br />
Please find the status of your transaction<br />
{{.Status}}
<br /><br />
Best
<br /><br />
FinBox Team
</body>
</html>
`

const TataAxisFailureHTML = `
<html>
<body>
Hi<br /><br />
Please find the Axis API Failure Details<br />
Url<br />
{{.Url}}
<br /><br />
CustomerID<br />
{{.CustomerID}}
<br /><br />
Mobile<br />
{{.Mobile}}
<br /><br />
Request<br />
<pre id = "request_payload">{{.Request}}</pre>
<br /><br />
Response<br />
<pre id = "response_payload">{{.Response}}</pre>
<br /><br />
Error<br />
{{.Error}}
<br /><br />
Timestamp in UTC<br />
{{.Timestamp}}
<br /><br />
Action Taken<br />
{{.ActionTaken}}
<br /><br />
Best
<br /><br />
FinBox Team
</body>
</html>
`

const (
	PincodeCustomizationEmailSubject = "Pincode Customization Results"
	PincodeCustomizationEmailHTML    = `
<html>
<body>
Hi {{.Name}},<br /><br />
Your pincode settings are updated. Please find the attached CSV File containing results of {{.OperationName}} activity.
<br /><br />
Best
<br /><br />
FinBox Team
</body>
</html>`
)

const (
	TxnReverseEmailSubject = "Reversal of Transactions report"
	TxnReverseEmailHTML    = `
<html>
<body>
Hi,<br /><br />
Transaction reversals were performed.<br />
{{.SuccessCount}} out of {{.RequestCount}} were processed successfully. Please find attached CSV containing detailed report.
<br /><br />
Best
<br /><br />
FinBox Team
</body>
</html>
`
)

const TataWebhookPayloadHTML = `
<html>
<body>
Hi<br /><br />
Please find the webhook payload<br />
CustomerID<br/>
{{.customerID}}
<br /><br />
Event<br/>
{{.event}}
<br /><br />
Payload<br />
<pre id = "payload">{{.webhookPayload}}</pre>
<br /><br />
Best
<br /><br />
FinBox Team
</body>
</html>
`

const (
	BulkAddSDSAEmailSubject = "Bulk Add Agents - Report"
	BulkAddSDSAEEmailHTML   = `
<html>
<body>
Hi {{.Name}},<br /><br />
Your agent onboarding activity was successful. Please find the attached CSV File containing results of the activity.
<br /><br />
Best
<br /><br />
FinBox Team
</body>
</html>
`
)

const TataCapitalFloatFailureHTML = `
<html>
<body>
Hi<br /><br />
Please find the Axio API Failure Details<br />
Url<br />
{{.Url}}
<br /><br />
CustomerID<br />
{{.CustomerID}}
<br /><br />
Mobile<br />
{{.Mobile}}
<br /><br />
Request<br />
<pre id = "request_payload">{{.Request}}</pre>
<br /><br />
Response<br />
<pre id = "response_payload">{{.Response}}</pre>
<br /><br />
Request Headers<br />
<pre id = "request_headers">{{.RequestHeaders}}</pre>
<br /><br />
Response Headers<br />
<pre id = "response_headers">{{.ResponseHeaders}}</pre>
<br /><br />
Error<br />
{{.Error}}
<br /><br />
Timestamp in UTC<br />
{{.Timestamp}}
<br /><br />
Action Taken<br />
{{.ActionTaken}}
<br /><br />
Best
<br /><br />
FinBox Team
</body>
</html>`

const (
	ODBillAPIFailuresSubject = "OD Billing API failure report"
	ODBillAPIFailuresHTML    = `
<html>
<body>
Hi<br /><br />
Following errors occurred during OD Billing API
<br />
{{.Failures}}
<br /><br />
Best
<br /><br />
FinBox Team
</body>
</html>
`
)

const TataCallbackFailureHTML = `
<html>
<body>
Hi<br /><br />
Please find the Callback API Failure Details<br />
Url<br />
{{.Url}}
<br /><br />
CustomerID<br />
{{.CustomerID}}
<br /><br />
Mobile<br />
{{.Mobile}}
<br /><br />
Request<br />
<pre id = "request_payload">{{.Request}}</pre>
<br /><br />
Response<br />
<pre id = "response_payload">{{.Response}}</pre>
<br /><br />
Request Headers<br />
<pre id = "request_headers">{{.RequestHeaders}}</pre>
<br /><br />
Response Headers<br />
<pre id = "response_headers">{{.ResponseHeaders}}</pre>
<br /><br />
Error<br />
{{.Error}}
<br /><br />
Timestamp in UTC<br />
{{.Timestamp}}
<br /><br />
Action Taken<br />
{{.ActionTaken}}
<br /><br />
Best
<br /><br />
FinBox Team
</body>
</html>`

const (
	PreApproveUsersSuccessSubject = "Pre Approve Users %s Upload Success : %s"
	PreApproveUsersSuccessHTML    = `
<html>
<body>
Hi {{.Name}},<br /><br />
Pre approved users {{.Revoke}} sheet uploaded successfully
<br /><br />
Best
<br /><br />
FinBox Team
</body>
</html>
`
)

const PreApproveUsersFailureSubject = "Pre Approve Users %s Upload Error : %s"

const PreApproveUsersFailureHTML = `
<html>
<body>
Hi {{.Name}},<br /><br />
Pre approved users {{.Revoke}} sheet upload failed for following users.
<br /><br />
<table style="border-collapse: collapse;border: 1px solid black;">
  <tr style="border: 1px solid black;">
  <th style="text-align:left;border: 1px solid black;">PAN</th>
  <th style="text-align:left;border: 1px solid black;">Row No</th>
  <th style="text-align:left;border: 1px solid black;">Error(s)</th>
  </tr>
  {{ range .Errors}}
  <tr style="border: 1px solid black;">
      <td  style="text-align:center;border: 1px solid black;">{{ .PAN }}</td>
      <td  style="text-align:center;border: 1px solid black;">{{ .Row }}</td>
      <td  style="text-align:center;border: 1px solid black;">{{ .Error }}</td>
  </tr>
{{ end}}
</table>
<br /><br />
Best
<br /><br />
FinBox Team
</body>
</html>`

const (
	UploadSuccessSubject = "%s Upload Success : %s %s"
	UploadFailureSubject = "%s Upload Failure : %s %s"
)

const UploadSheetSuccessHTML = `
<html>
<body>
Hi {{.Name}},<br /><br />
{{.Action}} sheet uploaded successfully
<br /><br />
Best
<br /><br />
FinBox Team
</body>
</html>
`

const UploadSheetFailureHTML = `
<html>
<body>
Hi {{.Name}},<br /><br />
{{.Action}} sheet upload failed for following users.
<br /><br />
<table style="border-collapse: collapse;border: 1px solid black;">
  <tr style="border: 1px solid black;">
  <th style="text-align:left;border: 1px solid black;">{{.IdentifierTitle}}</th>
  <th style="text-align:left;border: 1px solid black;">Row No</th>
  <th style="text-align:left;border: 1px solid black;">Error(s)</th>
  </tr>
  {{ range .Errors}}
  <tr style="border: 1px solid black;">
      <td  style="text-align:center;border: 1px solid black;">{{ .Identifier }}</td>
      <td  style="text-align:center;border: 1px solid black;">{{ .Row }}</td>
      <td  style="text-align:center;border: 1px solid black;">{{ .Error }}</td>
  </tr>
{{ end}}
</table>
<br /><br />
Best
<br /><br />
FinBox Team
</body>
</html>`
const PasswordResetLinkSubject = "Reset your password"
const VKycVerificationSubject = "ABCL Udyog Plus - Complete your Video KYC Verification"
const MflVKycVerificationSubject = "MFL - Complete your Video KYC Verification"

const PasswordResetLinkHTML = `
Hi {{.Name}}, <br /><br />
A password reset request was raised for your FinBox account.<br /><br />
Please click on the link below to reset your password. Link is Valid for 30 Minutes.<br /><br />
<b>Link:</b> {{.Link}}<br />
In case you have not made this request, you can safely ignore this email.
<br /><br />
Regards,<br />
Team FinBox<br />
https://finbox.in`

const PasswordResetLinkSubjectABFL = "Reset your password"

const PasswordResetLinkHTMLABFL = `
Hi {{.Name}}, <br /><br />
A password reset request was raised for your ABCL account.<br /><br />
Please click on the link below to reset your password. Link is Valid for 30 Minutes.<br /><br />
<b>Link:</b> {{.Link}}<br />
In case you have not made this request, you can safely ignore this email.
<br /><br />
Regards,<br />
Team ABCL<br />`

const (
	AgentReassignmentEmailSubject = "Agent Reassignment - Report"
	AgentReassignmentHTML         = `
<html>
<body>
Hi {{.Name}},<br /><br />
Your agent reassignment activity was successful. Please find the attached CSV File containing results of the activity.
<br /><br />
Best
<br /><br />
FinBox Team
</body>
</html>
`
)

const (
	WelcomeLenderDashboardSubject = "Welcome to FinBox Lending Dashboard!"
	WelcomeLenderDashboardHTML    = `
Hi {{.Name}},<br /><br />
Welcome to FinBox Lending Dashboard! Your account is ready, and you can log in <a href="{{.LenderDashboardFrontendURL}}">here</a> with following credentials:<br />
Email: <b>{{.Email}}</b><br />
Password: <b>{{.Password}}</b><br /><br />
PS: Please change your password after logging in for the first time.
<br /><br />
For any further queries, you can reach out to <NAME_EMAIL>
<br /><br />
You are receiving this email because you were invited on FinBox Lending Dashboard. Please ignore this email if you have received this by mistake.
<br /><br />
Best,<br />
Team FinBox<br />
https://finbox.in`
)

const (
	RemoveInsuranceSuccessSubject = "Remove Insurance Success"
	RemoveInsuranceSuccessHTML    = `
<html>
<body>
Hi {{.Name}},<br /><br />
Insurance has been successfully removed for the loan application with ID {{.LoanApplicationNum}}.
<br /><br />
Best
<br /><br />
FinBox Team
</body>
</html>`
)

const (
	RemoveInsuranceFailureSubject = "Remove Insurance Failed"
	RemoveInsuranceFailureHTML    = `
<html>
<body>
Hi {{.Name}},<br /><br />
Insurance could not be removed for the loan application with ID {{.LoanApplicationNum}}
<br /><br />
Please try again or reach out to FinBox <NAME_EMAIL>.
<br /><br />
Best
<br /><br />
FinBox Team
</body>
</html>`
)

const (
	IIFLKYCSuccessSubject = "KYC Status Approved"
	IIFLKYCSuccessHTML    = `
Dear Partner,<br /><br />
With reference to application in process for below mentioned customer:
<br /><br />
Name: {{.Name}}<br />
Mobile: {{.Mobile}}<br />
Loan Application ID: {{.LoanApplicationID}}<br />
<br />
Submitted KYC document has been Approved<br />
<br />
Regards,<br />
Team IIFL<br />
`
)

const (
	IIFLKYCFailureSubject = "KYC Status Rejected"
	IIFLKYCFailureHTML    = `
Dear Partner,<br /><br />
With reference to application in process for below mentioned customer:<br />
<br />
Name: {{.Name}}<br />
Mobile: {{.Mobile}}<br />
Loan Application ID: {{.LoanApplicationID}}<br />
<br />
Submitted KYC document has been Rejected. Please find the Rejection Reason below:<br />
{{.DocType}} - {{.RejectionReason}}<br />
<br />
Kindly share correct documents with our <NAME_EMAIL><br />
Regards,<br />
Team IIFL<br />
`
)

const MasterDashboardNoteUpdatedSubject = "Note added for {{.LoanAppName}} (Powered by FinBox)"

const MasterDashboardNoteUpdatedHTML = `
<html>
<body>
Hi {{.Name}},<br /><br />

A note has been added on Loan Application - {{.LoanApplicationNo}}.
<br /><br />
<br /><br />

<p>Please visit this url to view the <a href="{{.NoteUrl}}" target="_blank">note</a></p>
<br /><br />
<br /><br />

Best Regards
<br /><br />
{{.LoanAppName}}
<br /><br />
Powered by FinBox
</body>
</html>`

const (
	ManagerReassignmentEmailSubject = "Manager Reassignment - Report"
	ManagerReassignmentHTML         = `
<html>
<body>
Hi {{.Name}},<br /><br />
Your manager reassignment activity was successful. Please find the attached CSV File containing results of the activity.
<br /><br />
Best
<br /><br />
FinBox Team
</body>
</html>
`
)

const (
	ServiceBusinessRequestSubject = "Loan Deviation Request {{.RequestID}}  Raised for Business Team, Loan Application id: {{.LoanApplicationNo}}"
	ServiceBusinessRequestHTML    = `
Hi Team, <br /><br />
The following Loan Deviation request has been created for the Business Team against Loan Application ID: {{.LoanApplicationNo}}<br /><br />
Service Request ID: {{.RequestID}} <br />
Request: Check Loan Deviation Request and Take Action <br />
Comments: {{.Comments}} <br />
Please use the following link to process the request <br />
<a href="{{.DashboardURL}}/loan-applications/details/{{.SourceEntityID}}/{{.LoanApplicationID}}/loan-term-deviation">
{{.DashboardURL}}/loan-applications/details/{{.SourceEntityID}}/{{.LoanApplicationID}}/loan-term-deviation
</a>
<br /><br />
Regards,<br />
Team FinBox<br />`
)

const (
	ServiceSalesRequestSubject = "Loan Deviation has been {{.Action}} for {{.RequestID}}, Loan Application id: {{.LoanApplicationNo}}"
	ServiceSalesRequestHTML    = `
Hi Team, <br /><br />
The Loan Deviation request has been {{.Action}} for the Loan Application ID: {{.LoanApplicationNo}}<br /><br />
Service Request ID: {{.RequestID}} <br />
Comments: {{.Comments}} <br />
Please use the following link to check the deviation decision - <br />
<a href="{{.DashboardURL}}/loan-applications/details/{{.SourceEntityID}}/{{.LoanApplicationID}}/loan-term-deviation">
{{.DashboardURL}}/loan-applications/details/{{.SourceEntityID}}/{{.LoanApplicationID}}/loan-term-deviation
</a>
<br /><br />
Regards,<br />
Team FinBox<br />`
)

const (
	ManualKYCInitSubject = "KYC Check Request {{.RequestID}} raised for {{.ActionTo}}, Loan Application id: {{.LoanApplicationNo}}"
	ManualKYCInitHTML    = `
Hi Team, <br /><br />
The following KYC Workflow request has been created for the {{.ActionTo}} Team against Loan Application ID: {{.LoanApplicationNo}}
<br /><br />
Service Request ID: {{.RequestID}} <br /><br />
Please use the following link to process the request - <br />
<a href="{{.DashboardURL}}/loan-applications/details/{{.LoanApplicationID}}/kyc">
{{.DashboardURL}}/loan-applications/details/{{.LoanApplicationID}}/kyc
</a>
<br /><br />
Best,<br />
FinBox Team<br />
`
)

const (
	RequestAdditionalDocumentsSubject = "KYC Check Request {{.RequestID}} raised for {{.ActionTo}} Team, Loan Application id: {{.LoanApplicationNo}}"
	RequestAdditionDocumentsHTML      = `
Hi Team, <br /><br />
The following KYC Workflow request has been created for the {{.ActionTo}} Team against Loan Application ID: {{.LoanApplicationNo}}
<br /><br />
Service Request ID: {{.RequestID}} <br />
Request: Fetch and Upload Additional Documents <br />
Comments: {{.Comments}} <br />
<br />
Please use the following link to process the request - <br />
<a href="{{.DashboardURL}}/loan-applications/details/{{.SourceEntityID}}/{{.LoanApplicationID}}/additional-docs">
{{.DashboardURL}}/loan-applications/details/{{.SourceEntityID}}/{{.LoanApplicationID}}/additional-docs
</a>
<br /><br />
Best, <br />
FinBox Team <br />
`
)

const (
	RequestVKYCReportSubject = "KYC Check Request {{.RequestID}} raised for {{.ActionTo}} Team, Loan Application id: {{.LoanApplicationNo}}"
	RequestVKYCReportHTML    = `
Hi Team, <br /><br />
The following KYC Workflow request has been created for the {{.ActionTo}} Team against Loan Application ID: {{.LoanApplicationNo}}
<br /><br />
Service Request ID: {{.RequestID}} <br /> 
Request: Complete Video KYC For Customer and Upload the Video KYC Report <br />
Comments: {{.Comments}} <br />
<br />
Please use the following link to process the request - <br />
<a href="{{.DashboardURL}}/loan-applications/details/{{.LoanApplicationID}}/kyc">
{{.DashboardURL}}/loan-applications/details/{{.LoanApplicationID}}/kyc
</a>
<br /><br />
Best, <br />
FinBox Team <br />
`
)

const (
	UploadedAdditionalDocsSubject = "KYC Check Request {{.RequestID}} raised for {{.ActionTo}} Team, Loan Application id: {{.LoanApplicationNo}}"
	UploadedAdditionalDocsHTML    = `
Hi Team, <br /><br />
The following KYC Workflow request has been created for the {{.ActionTo}} Team against Loan Application ID: {{.LoanApplicationNo}}
<br /><br />
Service Request ID: {{.RequestID}} <br /> 
Request: Check Documents updated <br />
Comments: {{.Comments}} <br />
<br />
Please use the following link to process the request - <br />
<a href="{{.DashboardURL}}/loan-applications/details/{{.LoanApplicationID}}/additional-docs">
{{.DashboardURL}}/loan-applications/details/{{.LoanApplicationID}}/additional-docs
</a>
<br /><br />
Best, <br />
FinBox Team <br />
`
)

const (
	UploadedVKYCReportSubject = "KYC Check Request {{.RequestID}} raised for {{.ActionTo}} Team, Loan Application id: {{.LoanApplicationNo}}"
	UploadedVKYCReportHTML    = `
Hi Team, <br /><br />
The following KYC Workflow request has been created for the RCU Team against Loan Application ID: {{.LoanApplicationNo}}
<br /><br />
Service Request ID: {{.RequestID}} <br /> 
Request: Check Video KYC Report updated <br />
Comments: {{.Comments}} <br />
<br /><br />
Please use the following link to process the request - <br />
<a href="{{.DashboardURL}}/loan-applications/details/{{.LoanApplicationID}}/kyc">
{{.DashboardURL}}/loan-applications/details/{{.LoanApplicationID}}/kyc
</a>
<br /><br />
Best, <br />
FinBox Team <br />
`
)

const (
	BREWorkflowInitiationSubject = "Manual Credit Review Request {{.RequestID}} Raised for {{.ActionTo}} Team, Loan Application id: {{.LoanApplicationNo}}"
	BREWorkflowInitiationHTML    = `
Hi Team, <br /><br />
The following Manual Credit Review request has been created for the {{.ActionTo}} Team against Loan Application ID: {{.LoanApplicationNo}}
<br /><br />
Service Request ID: {{.RequestID}} <br />
Request: Check Application in relation to BRE Amber Output <br />
Request From: {{.ActionBy}} <br />
<br /><br />
Please use the following link to process the request - <br />
<a href="{{.DashboardURL}}/loan-applications/details/{{.LoanApplicationID}}/manual-credit-review">
{{.DashboardURL}}/loan-applications/details/{{.LoanApplicationID}}/manual-credit-review
</a>
<br /><br />
Best, <br />
FinBox Team <br />
`
)

const (
	BREWorkflowProcessingSubject = "Manual Credit Review Request {{.RequestID}} Raised for {{.ActionTo}} Team, Loan Application id: {{.LoanApplicationNo}}"
	BREWorkflowProcessingHTML    = `
Hi Team, <br /><br />
The following Manual Credit Review request has been created for the {{.ActionTo}} Team against Loan Application ID: {{.LoanApplicationNo}}
<br /><br />
Service Request ID: {{.RequestID}} <br />
Request: Review Application in relation to BRE Amber Output <br />
Request From: {{.ActionBy}} <br />
Comments: {{.Comments}} <br />
<br /><br />
Please use the following link to process the request - <br />
<a href="{{.DashboardURL}}/loan-applications/details/{{.LoanApplicationID}}/manual-credit-review">
{{.DashboardURL}}/loan-applications/details/{{.LoanApplicationID}}/manual-credit-review
</a>
<br /><br />
Best, <br />
FinBox Team <br />
`
)

const (
	BREWorkflowDocumentRequestSubject = "Manual Credit Review Request {{.RequestID}} Raised for {{.ActionTo}} Team, Loan Application id: {{.LoanApplicationNo}}"
	BREWorkflowDocumentRequestHTML    = `
Hi Team, <br /><br />
The following Manual Credit Review request has been created for the {{.ActionTo}} Team against Loan Application ID: {{.LoanApplicationNo}}
<br /><br />
Service Request ID: {{.RequestID}} <br />
Request: Fetch and Upload Additional Documents in relation to BRE Amber Output <br />
Request From: {{.ActionBy}} <br />
Comments: {{.Comments}} <br />
<br /><br />
Please use the following link to process the request - <br />
<a href="{{.DashboardURL}}/loan-applications/details/{{.SourceEntityID}}/{{.LoanApplicationID}}/additional-docs">
{{.DashboardURL}}/loan-applications/details/{{.SourceEntityID}}/{{.LoanApplicationID}}/additional-docs
</a>
<br /><br />
Best, <br />
FinBox Team <br />
`
)
const LenderDashboardLoginOTPOnEmailSubject = "FinBox Lender Dashboard Login OTP"

const LenderDashboardLoginOTPOnEmailHTML = `
Hi <br /><br />
Your OTP for FinBox Lender Dashboard Login is {{.OTP}}
<br /><br />
This OTP valid for 30 minutes. In case you have not made this login request, you can safely ignore this email.
<br /><br />
Regards,<br />
Team FinBox<br />
https://finbox.in`

const MasterDashboardLoginOTPOnEmailSubject = "FinBox Platform Dashboard Login OTP"

const MasterDashboardLoginOTPOnEmailHTML = `
Hi <br /><br />
Your OTP for FinBox Platform Dashboard Login is {{.OTP}}
<br /><br />
This OTP valid for 30 minutes. In case you have not made this login request, you can safely ignore this email.
<br /><br />
Regards,<br />
Team FinBox<br />
https://finbox.in`

const MasterDashboardABFLLoginOTPOnEmailSubject = "ABCL Platform Dashboard Login OTP"

const MasterDashboardABFLLoginOTPOnEmailHTML = `
Hi <br /><br />
Your OTP for ABCL Platform Dashboard Login is {{.OTP}}
<br /><br />
This OTP valid for 30 minutes. In case you have not made this login request, you can safely ignore this email.
<br /><br />
Regards,<br />
Team ABCL<br />`

const (
	TataPLLoanClosureRequestSubject = `{{.LenderName}} - Loan Cancellation Requestion by {{.LoanApplicationNo}}`
	TataPLLoanClosureRequestHTML    = `
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Loan Application Form</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        margin: 20px;
        padding: 20px;
      }
      h5 {
        margin: 0;
      }
    </style>
  </head>
  <body>
    <div style="display: flex; flex-direction: row; margin-top: 16px">
      <i
        >Hello, <br />

        &emsp;&emsp;This email is to notify {{.LoanApplicationNo}} has raised a
        loan cancellation request for {{.LoanApplicationID}}.
        <br />&emsp;&emsp;Please forward the details to the appropriate point of
        connect in {{.LenderName}}. <br />
        <br />
        This is an automated email, please do not reply. <br /><br /><br />
        Thank You.</i
      >
    </div>
  </body>
  </html>
`
)

// email sent to customer for the export data
const (
	AdminPageEmailSubject        = "FinBox Admin Page Data Export - {{.Type}} Data Dump CSV"
	AdminPageExportDumpEmailHTML = `
<html>
<body>
Hi<br /><br />
Please find below the CSV file containing {{.Type}} data.
<br /><br />
{{.Link}}
<br /><br />
Best
<br /><br />
FinBox Team
</body>
</html>`
)

const (
	WelcomeSubjectForMasterDashboardABFL = `Welcome to ABCL Platform Dashboard!`
	WelcomeHTMLForMasterDashboardABFL    = `
    <p>Hi {{.Name}}<br>
    <br>
    Welcome to <span class="il">ABCL</span> Platform Dashboard! Your account is ready, and you can log in <a href="{{.MasterDashboardFrontendURL}}">
    here</a> with following credentials:<br>
    Email: {{.Email}}<br>
    Password: {{.Password}}<br>
    <br>
    PS: Please change your password after logging in for the first time. <br>
    <br>
    For any further queries, you can reach out to us.<br>
    <br>
    You are receiving this email because you were invited on <span class="il">ABCL</span> Platform Dashboard. Please ignore this email if you have received this by mistake.
    <br>
    <br>
    Best,<br>
    Team <span class="il">ABCL</span><br>
    <br>
    <u></u><u></u></p>
  `
)

const (
	WelcomeLenderDashboardSubjectABFL = "Welcome to ABCL Lending Dashboard!"
	WelcomeLenderDashboardHTMLABFL    = `
Hi {{.Name}},<br /><br />
Welcome to <span class="il">ABCL</span> Lending Dashboard! Your account is ready, and you can log in <a href="{{.LenderDashboardFrontendURL}}">here</a> with following credentials:<br />
Email: <b>{{.Email}}</b><br />
Password: <b>{{.Password}}</b><br /><br />
PS: Please change your password after logging in for the first time.
<br /><br />
For any further queries, you can reach out to <NAME_EMAIL>
<br /><br />
You are receiving this email because you were invited on ABCL Lending Dashboard. Please ignore this email if you have received this by mistake.
<br /><br />
Best,<br />
Team ABCL<br />`
)

const (
	FinalLoanOfferSubjectABFL = "Business Loan of Rs.{{.LoanAmount}} awaits!"
	FinalLoanOfferHTMLABFL    = `
Dear {{.Name}}<br /><br />
We are excited to share with you that your application ID {{.LoanID}} has been approved for loan amount Rs.{{.LoanAmount}}. Thank you for choosing Aditya Birla Capital Limited as your financial partner.
<br /><br />
Resume the journey by clicking on the link <a href="https://udyogplus.adityabirlacapital.com/">here</a>.
<br /><br />
Regards,<br />
Team Udyog Plus<br />`
)

const (
	DisbursementSuccessInsuranceOptedSubjectABFL = "Loan Application {{.LoanID}} has been disbursed!"
	DisbursementSuccessInsuranceOptedHTMLABFL    = `
Dear {{.Name}}<br /><br />
Congratulations, your loan has been disbursed successfully. The loan amount of Rs.{{.LoanAmount}} will be disbursed in your bank account number ending with {{.AccountNumber}} against your application ID {{.LoanID}}. Please find the signed copy of the terms and conditions, schedule of charges, key fact statement and Certificate of Insurance attached for your pursual.
<br /><br />
Please do explore our solutions to scale up your business needs. Thank you for selecting Aditya Birla Capital-Udyog Plus as your financial partner. We are committed to making every small business big!
<br /><br />
Explore all & more on Udyog Plus <a href="https://udyogplus.adityabirlacapital.com/">here</a>.
<br /><br />
Regards,<br />
Aditya Birla Capital-Udyog Plus<br />`
)

const (
	DisbursementSuccessInsuranceNotOptedWithWellnessSubjectABFL = "Loan Application {{.LoanID}} has been disbursed!"
	DisbursementSuccessInsuranceNotOptedWithWellnessHTMLABFL    = `
Dear {{.Name}}<br /><br />
Congratulations, your loan has been disbursed successfully. The loan amount of Rs.{{.LoanAmount}} will be disbursed in your bank account number ending with {{.AccountNumber}} against your application ID {{.LoanID}}. Please find the signed copy of the terms and conditions, schedule of charges and key fact statement attached for your pursual.
<br /><br />
Please do explore our solutions to scale up your business needs. Thank you for selecting Aditya Birla Capital-Udyog Plus as your financial partner. We are committed to making every small business big!
<br /><br />
Explore all & more on Udyog Plus <a href="https://udyogplus.adityabirlacapital.com/">here</a>.
<br /><br />
Get ready for an exciting journey towards wellness! Stay tuned to your email inbox as Complementary Wellness Card awaits you! Unlock a world of health and vitality just for you!   
<br /><br />
You will receive an <NAME_EMAIL>  for your card details 
<br /><br />
Got questions? We're here for you! Dial +************ or shoot us an <NAME_EMAIL>
<br /><br />
Regards,<br />
Aditya Birla Capital-Udyog Plus<br />`
)

const (
	DisbursementSuccessInsuranceNotOptedSubjectABFL = "Loan Application {{.LoanID}} has been disbursed!"
	DisbursementSuccessInsuranceNotOptedHTMLABFL    = `
Dear {{.Name}}<br /><br />
Congratulations, your loan has been disbursed successfully. The loan amount of Rs.{{.LoanAmount}} will be disbursed in your bank account number ending with {{.AccountNumber}} against your application ID {{.LoanID}}. Please find the signed copy of the terms and conditions, schedule of charges and key fact statement attached for your pursual.
<br /><br />
Please do explore our solutions to scale up your business needs. Thank you for selecting Aditya Birla Capital Udyog Plus as your financial partner. We are committed to making every small business big!
<br /><br />
Explore all & more on Udyog Plus <a href="https://udyogplus.adityabirlacapital.com/">here</a>.
<br /><br />
Regards,<br />
Team Udyog Plus<br />`
)

const (
	DigioEmandateCancellationSubject   = "Revoke Case - Name: {{ .customerName }}, Mobile: {{ .mobile }} has revoked the mandate"
	DigioEmandateCancellationEmailBody = `
  Hi Team, <br /><br />
Customer Name: {{ .customerName }} having Mobile: {{ .mobile }} has revoked the mandate. Please reach out to the customer and setup a new mandate on priority.
<br /><br />
Regards,<br />
FinBox Team.<br />`
)

const (
	DisbursementSuccessInsuranceOptedWithWellnessSubjectABFL = "Loan Application {{.LoanID}} has been disbursed!"
	DisbursementSuccessInsuranceOptedWithWellnessHTMLABFL    = `
Dear {{.Name}}<br /><br />
Congratulations, your loan has been disbursed successfully. The loan amount of Rs.{{.LoanAmount}} will be disbursed in your bank account number ending with {{.AccountNumber}} against your application ID {{.LoanID}}. Please find the signed copy of the terms and conditions, schedule of charges, key fact statement and Certificate of Insurance attached for your pursual.
<br /><br />
Please do explore our solutions to scale up your business needs. Thank you for selecting Aditya Birla Finance Udyog Plus as your financial partner. We are committed to making every small business big!
<br /><br />
Explore all & more on Udyog Plus <a href="https://udyogplus.adityabirlacapital.com/">here</a>.
<br /><br />
Get ready for an exciting journey towards wellness! Stay tuned to your email inbox as Complementary Wellness Card awaits you! Unlock a world of health and vitality just for you!   
<br /><br />
You will receive an <NAME_EMAIL>  for your card details 
<br /><br />
Got questions? We're here for you! Dial +************ or shoot us an <NAME_EMAIL>
<br /><br />
Regards,<br />
Team Udyog Plus<br />`
)

const (
	BulkReassignManagerEmailSubject = "Bulk Reassign Manager - Report"
	BulkReassignManagerEmailHTML    = `
<html>
<body>
Hi {{.Name}},<br /><br />
Your agent reassignment activity was successful. Please find the attached CSV File containing results of the activity.
<br /><br />
Best
<br /><br />
FinBox Team
</body>
</html>
`
)

// email sent to customer after loan is e-signed
const (
	MarketplaceEmailSubjectABFL = "You're Almost There! One More Step to Receive Your Loan Offer"
	MarketplaceEmailHTMLABFL    = `
<html>
<body>
Dear {{.Name}},<br /><br />

We’re excited to let you know that you’re just one step away for getting loan offer from our trusted partner lender!
<br /><br />
<br /><br />
Please click on the link below to complete your application:
<a href={{.Session}}>Complete Your Application</a>
<br /><br />

<br /><br />
Thank you for choosing ABCL. We look forward to helping you achieve your financial goals.
<br /><br />
Best Regards,
ABCL
</body>
</html>`
)

const (
	OtpVerificationSubjectPfl = "OTP for Email ID Verification"
	OtpVerificationHTMLPfl    = `Dear {{.CustomerName}},<br /><br />
  Thank you for applying for a Personal Loan with us. To proceed with your loan application, we need to make sure that this email address is yours.<br /><br />
  To verify your email address, use this OTP Code: {{.OTPCode}}<br /><br />
  This OTP is valid for 5 minutes. Kindly enter the same in the verification field on the application form to complete the email verification process.<br /><br />
  If you did not initiate this request, please ignore this email.<br /><br />
  We appreciate your co-operation.<br /><br />
  Poonawalla Fincorp Limited<br /><br />
  This is a system-generated automated e-mail. Please do not reply to this e-mail.<br />
  `
)

const (
	OtpVerificationSubjectPflPersonalEmail = "Email Verification OTP – Poonawalla Fincorp Limited"
	OtpVerificationHTMLPflPersonalEmail    = `Dear {{.CustomerName}},<br /><br />
  Thank you for applying for a Personal Loan with us. To proceed with your loan application, we need to make sure that this email address is yours.<br /><br />
  To verify your email address, use this OTP Code: {{.OTPCode}}<br /><br />
  This OTP is valid for 1 minute. Kindly enter the same in the verification field on the application form to complete the email verification process.<br /><br />
  If you did not initiate this request, please ignore this email.<br /><br />
  We appreciate your co-operation.<br /><br />
  Poonawalla Fincorp Limited<br /><br />
  This is a system-generated automated e-mail. Please do not reply to this e-mail.<br />
  `
)

const (
	OtpVerificationSubjectAbflPl = "OTP for Email ID Verification"
	OtpVerificationHTMLAbflPl    = `Dear {{.CustomerName}},<br /><br />
  Thank you for applying for a Personal Loan with us. To proceed with your loan application, we need to make sure that this email address is yours.<br /><br />
  To verify your email address, use this OTP Code: {{.OTPCode}}<br /><br />
  This OTP is valid for 5 minutes. Kindly enter the same in the verification field on the application form to complete the email verification process.<br /><br />
  If you did not initiate this request, please ignore this email.<br /><br />
  We appreciate your co-operation.<br /><br />
  Aditya Birla Capital Limited<br /><br />
  This is a system-generated automated e-mail. Please do not reply to this e-mail.<br />
  `
)

// pick otp expiry from config
const (
	OtpVerificationSubjectMFLBL = "OTP for Email ID Verification"
	OtpVerificationHTMLMFLBL    = `Dear {{.CustomerName}},<br /><br />
  Use the following One-time password (OTP) to verify your e-mail ID with Muthoot FinCorp.<br /><br />
  This OTP will be valid for the next 5 minutes.<br /><br />
  Please do not share this OTP with anyone.<br /><br />

  {{.OTPCode}}<br /><br />

  Warm regards<br />
  Muthoot FinCorp
  `
)

const (
	DropdownSuccessSubject = "Dropdown %s Upload Success : %s"
	DropdownSuccessHTML    = `
<html>
<body>
Hi {{.Name}},<br /><br />
Dropdown sheet uploaded successfully
<br /><br />
Best
<br /><br />
FinBox Team
</body>
</html>
`
)

const DropdownFailureSubject = "Dropdown %s Upload Error : %s"

const DropdownFailureHTML = `
<html>
<body>
Hi {{.Name}},<br /><br />

{{if .IsError}}
Your file is not uploaded successfully. Please contact Fibox team!
{{end}}

Dropdown sheet upload failed for following users.
<br /><br />
<table style="border-collapse: collapse;border: 1px solid black;">
  <tr style="border: 1px solid black;">
  <th style="text-align:left;border: 1px solid black;">Row No</th>
  <th style="text-align:left;border: 1px solid black;">Error(s)</th>
  </tr>
  {{ range .Errors}}
  <tr style="border: 1px solid black;">
      <td  style="text-align:center;border: 1px solid black;">{{ .Row }}</td>
      <td  style="text-align:center;border: 1px solid black;">{{ .Error }}</td>
  </tr>
{{ end}}
</table>
<br /><br />
Best
<br /><br />
FinBox Team
</body>
</html>`
