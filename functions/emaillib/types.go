package emaillib

const (
	FinalOfferEmailBodyABFL                  = "final_loan_offer_email_body_abfl"
	FinalOfferEmailSubjectABFL               = "final_loan_offer_email_subject_abfl"
	InsuranceStatusEmailBodyABFL             = "insurance_status_email_body_abfl"
	InsuranceStatusEmailSubjectABFL          = "insurance_status_email_subject_abfl"
	OtpVerificationEmailSubjectPFL           = "opt_verification_email_subject_pfl"
	OtpVerificationEmailBodytPFL             = "opt_verification_email_body_pfl"
	OtpVerificationEmailSubjectAbfl          = "otp_verification_email_subject_abfl"
	OtpVerificationEmailBodyAbfl             = "otp_verification_email_body_abfl"
	OtpVerificationEmailSubjectMFLBL         = "otp_verification_email_subject_mfl_bl"
	OtpVerificationEmailBodyMFLBL            = "otp_verification_email_body_mfl_bl"
	OtpVerificationEmailBodyPFLPersonalEmail = "otp_verification_email_body_pfl_personal_email"
)

var EmailSubjectKeyMap = map[string]string{
	FinalOfferEmailSubjectABFL:          FinalLoanOfferSubjectABFL,
	InsuranceStatusEmailSubjectABFL:     InsuranceSubjectABFL,
	OtpVerificationEmailSubjectPFL:      OtpVerificationSubjectPfl,
	OtpVerificationEmailSubjectAbfl:     OtpVerificationSubjectAbflPl,
	OtpVerificationEmailSubjectMFLBL:    OtpVerificationSubjectMFLBL,
	OtpVerificationHTMLPflPersonalEmail: OtpVerificationSubjectPflPersonalEmail,
}

var EmailBodyKeyMap = map[string]string{
	FinalOfferEmailBodyABFL:                  FinalLoanOfferHTMLABFL,
	InsuranceStatusEmailBodyABFL:             InsuranceHTMLABFL,
	OtpVerificationEmailBodytPFL:             OtpVerificationHTMLPfl,
	OtpVerificationEmailBodyAbfl:             OtpVerificationHTMLAbflPl,
	OtpVerificationEmailBodyMFLBL:            OtpVerificationHTMLMFLBL,
	OtpVerificationEmailBodyPFLPersonalEmail: OtpVerificationHTMLPflPersonalEmail,
}

type MailStruct struct {
	UserID         string
	FromEmailInput string
	FromNameInput  string
	ToEmails       []string
	ToNames        []string
	Subject        string
	HTMLBody       string
	Attachments    []EmailAttachment
	AddCC          bool
	Host           string
	Port           int
	Username       string
	Password       string
}
