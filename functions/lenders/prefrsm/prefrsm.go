package prefrsm

import (
	"context"
	"finbox/go-api/constants"
	"finbox/go-api/errorHandler"
	"finbox/go-api/functions/activity"
	"finbox/go-api/functions/lenderservice"
	"finbox/go-api/functions/logger"
	"finbox/go-api/temporal/temporalutility"
	"finbox/go-api/utils/general"
)

func ProcessCallBack(callbackDtls lenderservice.CallbackReq) error {
	callBackReqData := callbackDtls.Data
	userID := callbackDtls.ApplicationReq.UserID
	sourceEntityID := callbackDtls.ApplicationReq.SourceEntityID

	logger.WithUser(userID).Debugf("callBackReqData: %+v", general.AnyToJSONString(callBackReqData))

	var webhookStruct struct {
		ApplicationStatus    string `json:"applicationStatus"`
		ApplicationSubStatus string `json:"applicationSubStatus"`
		AdditionalData       struct {
			Amount         float64 `json:"amount"`
			RateOfInterest float64 `json:"roi"`
			Tenure         int     `json:"tenure"`
			ProcessingFee  float64 `json:"processingFee"`
		} `json:"additionalData"`
	}

	if err := general.DecodeToStruct(callBackReqData, &webhookStruct); err != nil {
		logger.WithUser(userID).Errorln(err)
		return err
	}

	logger.WithUser(userID).Debugf("webhookStruct: %+v", general.AnyToJSONString(webhookStruct))

	nowString := general.GetTimeStampString()

	if err := activity.RegisterEvent(&activity.ActivityEvent{
		UserID:         userID,
		SourceEntityID: sourceEntityID,
		EntityType:     constants.System,
		EntityRef:      userID,
		EventType:      webhookStruct.ApplicationSubStatus,
		DateTime:       nowString,
	}, nowString); err != nil {
		logger.WithUser(userID).Errorln(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userID, "sourceEntityID": sourceEntityID}, err)
		return err
	}

	if general.InArr(webhookStruct.ApplicationSubStatus, []string{"offer_accepted", "offer_final_generated", "user_disqualified"}) {
		// signal the OFFER workflow
		if _, err := temporalutility.SignalWorkflow(context.TODO(), userID, "OFFER", webhookStruct.ApplicationSubStatus, "LISA_WEBHOOK", nil, map[string]interface{}{
			"amount":        webhookStruct.AdditionalData.Amount,
			"tenure":        webhookStruct.AdditionalData.Tenure,
			"interest":      webhookStruct.AdditionalData.RateOfInterest,
			"processingFee": webhookStruct.AdditionalData.ProcessingFee,
		}); err != nil {
			logger.WithUser(userID).Errorln(err)
			errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": userID, "sourceEntityID": sourceEntityID}, err)
			return err
		}
	}

	return nil
}
