module finbox/go-api

go 1.23.0

// toolchain go1.23.4

require (
	github.com/Blank-Xu/sqlx-adapter v0.0.0-20230423090211-0a30309eefa6
	github.com/IguteChung/casbin-psql-watcher v1.0.0
	github.com/alicebob/miniredis v2.5.0+incompatible
	github.com/aws/aws-sdk-go v1.44.279
	github.com/beevik/etree v1.2.0
	github.com/casbin/casbin/v2 v2.70.0
	github.com/cespare/xxhash/v2 v2.3.0
	github.com/clbanning/mxj v1.8.4
	github.com/didip/tollbooth/v7 v7.0.1
	github.com/fatih/structs v1.1.0
	github.com/finbox-in/api-schema-mapper v0.3.0
	github.com/finbox-in/oauth2 v1.0.2
	github.com/finbox-in/road-runner v0.1.18
	github.com/gavv/httpexpect/v2 v2.16.0
	github.com/getsentry/sentry-go v0.21.0
	github.com/go-chi/chi/v5 v5.1.0
	github.com/go-chi/cors v1.2.1
	github.com/go-playground/validator/v10 v10.14.1
	github.com/go-redis/redis/v8 v8.11.5
	github.com/golang-jwt/jwt v3.2.2+incompatible
	github.com/google/uuid v1.6.0
	github.com/graphql-go/graphql v0.8.1
	github.com/hibiken/asynq v0.24.1
	github.com/itchyny/gojq v0.12.15
	github.com/jinzhu/copier v0.3.5
	github.com/jmoiron/sqlx v1.3.5
	github.com/lib/pq v1.10.9
	github.com/mitchellh/mapstructure v1.5.0
	github.com/ory/client-go v1.14.3
	github.com/ozankasikci/go-image-merge v0.2.2
	github.com/paul-mannino/go-fuzzywuzzy v0.0.0-20200127021948-54652b135d0e
	github.com/pdfcrowd/pdfcrowd-go v0.0.0-20230330132212-052ecf6fb44d
	github.com/pkg/errors v0.9.1
	github.com/prometheus/client_golang v1.20.5
	github.com/rs/zerolog v1.32.0 // indirect
	github.com/russellhaering/goxmldsig v1.4.0
	github.com/sendgrid/sendgrid-go v3.12.0+incompatible
	github.com/sirupsen/logrus v1.9.3
	github.com/uber-go/tally/v4 v4.1.10
	github.com/xuri/excelize/v2 v2.7.1
	github.com/yeka/zip v0.0.0-20180914125537-d046722c6feb
	go.temporal.io/api v1.29.1
	go.temporal.io/sdk v1.26.0
	go.temporal.io/sdk/contrib/tally v0.2.0
	golang.org/x/crypto v0.38.0
	golang.org/x/exp v0.0.0-20231127185646-65229373498e
	golang.org/x/sync v0.14.0
	golang.org/x/text v0.25.0
	golang.org/x/tools v0.30.0
	gopkg.in/DataDog/dd-trace-go.v1 v1.51.0
	gopkg.in/yaml.v2 v2.4.0
	gopkg.in/yaml.v3 v3.0.1
	zgo.at/zcache/v2 v2.1.0
)

require (
	github.com/araddon/dateparse v0.0.0-20210429162001-6b43995a97de
	github.com/expr-lang/expr v1.16.9
	github.com/samber/lo v1.47.0
	github.com/serverlessworkflow/sdk-go/v2 v2.2.2
	github.com/spf13/cast v1.3.1
	github.com/stretchr/testify v1.10.0
	// go.opentelemetry.io/contrib/bridges/otellogrus v1.32.0
	go.opentelemetry.io/otel v1.36.0
	go.opentelemetry.io/otel/log v0.8.0 // indirect
	go.opentelemetry.io/otel/metric v1.36.0 // indirect
	go.opentelemetry.io/otel/sdk v1.35.0
	go.opentelemetry.io/otel/sdk/log v0.8.0
	go.opentelemetry.io/otel/sdk/metric v1.34.0 // indirect
	go.opentelemetry.io/otel/trace v1.36.0
	go.uber.org/zap v1.27.0
	gopkg.in/mail.v2 v2.3.1
	gopkg.in/natefinch/lumberjack.v2 v2.2.1
)

require (
	github.com/ClickHouse/clickhouse-go/v2 v2.36.0
	github.com/Nerzal/gocloak/v13 v13.9.0
	github.com/finbox-in/data-dancer v0.7.0
	github.com/finbox-in/pickle-protos v0.2.1
	github.com/graph-gophers/dataloader v5.0.0+incompatible
	github.com/kaptinlin/jsonschema v0.2.3-0.20250218023942-334795ee54af
	github.com/riandyrn/otelchi v0.10.1
	github.com/test-go/testify v1.1.4
	github.com/xwb1989/sqlparser v0.0.0-20180606152119-120387863bf2
	go.opentelemetry.io/contrib/instrumentation/net/http/otelhttp v0.59.0
	go.opentelemetry.io/otel/exporters/otlp/otlplog/otlploghttp v0.8.0
	go.opentelemetry.io/otel/exporters/otlp/otlptrace/otlptracegrpc v1.32.0
	gorm.io/datatypes v1.2.5
	gorm.io/driver/postgres v1.5.11
	gorm.io/gorm v1.25.12
)

require (
	cloud.google.com/go/compute/metadata v0.5.2 // indirect
	github.com/go-jose/go-jose/v3 v3.0.3 // indirect
	github.com/go-resty/resty/v2 v2.7.0 // indirect
	github.com/goccy/go-json v0.10.5 // indirect
	github.com/goccy/go-yaml v1.15.23 // indirect
	github.com/golang-jwt/jwt/v5 v5.0.0 // indirect
	github.com/gotnospirit/makeplural v0.0.0-20180622080156-a5f48d94d976 // indirect
	github.com/gotnospirit/messageformat v0.0.0-20221001023931-dfe49f1eb092 // indirect
	github.com/kaptinlin/go-i18n v0.1.3 // indirect
	github.com/munnerz/goautoneg v0.0.0-20191010083416-a7dc8b61c822 // indirect
	github.com/opentracing/opentracing-go v1.2.0 // indirect
	github.com/segmentio/ksuid v1.0.4 // indirect
	github.com/tidwall/gjson v1.18.0 // indirect
	github.com/tidwall/match v1.1.1 // indirect
	github.com/tidwall/pretty v1.2.1 // indirect
	github.com/tidwall/sjson v1.2.5 // indirect
	github.com/wI2L/jsondiff v0.6.1
	go.opentelemetry.io/auto/sdk v1.1.0 // indirect
)

require (
	filippo.io/edwards25519 v1.1.0 // indirect
	github.com/ClickHouse/ch-go v0.66.0 // indirect
	github.com/go-faster/city v1.0.1 // indirect
	github.com/go-faster/errors v0.7.1 // indirect
	github.com/go-sql-driver/mysql v1.8.1 // indirect
	github.com/jinzhu/inflection v1.0.0 // indirect
	github.com/jinzhu/now v1.1.5 // indirect
	github.com/paulmach/orb v0.11.1 // indirect
	github.com/pierrec/lz4/v4 v4.1.22 // indirect
	github.com/segmentio/asm v1.2.0 // indirect
	github.com/shopspring/decimal v1.4.0 // indirect
	gorm.io/driver/mysql v1.5.6 // indirect
)

require (
	github.com/TylerBrock/colorjson v0.0.0-20200706003622-8a50f05110d2 // indirect
	github.com/ajg/form v1.5.1 // indirect
	github.com/andybalholm/brotli v1.1.1 // indirect
	github.com/beorn7/perks v1.0.1 // indirect
	github.com/cashfree/cashfree-pg/v3 v3.2.14 // indirect
	github.com/cenkalti/backoff/v4 v4.3.0 // indirect
	github.com/davecgh/go-spew v1.1.1 // indirect
	github.com/facebookgo/clock v0.0.0-20150410010913-600d898af40a // indirect
	github.com/fatih/color v1.18.0 // indirect
	github.com/felixge/httpsnoop v1.0.4 // indirect
	github.com/go-logr/logr v1.4.2 // indirect
	github.com/go-logr/stdr v1.2.2 // indirect
	github.com/gobwas/glob v0.2.3 // indirect
	github.com/gogo/protobuf v1.3.2 // indirect
	github.com/golang/mock v1.6.0 // indirect
	github.com/google/go-querystring v1.1.0 // indirect
	github.com/google/gofuzz v1.2.0 // indirect
	github.com/gorilla/websocket v1.5.3 // indirect
	github.com/grpc-ecosystem/go-grpc-middleware v1.3.0 // indirect
	github.com/grpc-ecosystem/grpc-gateway/v2 v2.23.0 // indirect
	github.com/hhrutter/lzw v1.0.0 // indirect
	github.com/hhrutter/tiff v1.0.1 // indirect
	github.com/imkira/go-interpol v1.1.0 // indirect
	github.com/klauspost/compress v1.18.0 // indirect
	github.com/mattn/go-colorable v0.1.13 // indirect
	github.com/mattn/go-isatty v0.0.20 // indirect
	github.com/mattn/go-runewidth v0.0.16 // indirect
	github.com/mitchellh/go-wordwrap v1.0.1 // indirect
	github.com/onsi/gomega v1.27.10 // indirect
	github.com/pborman/uuid v1.2.1 // indirect
	github.com/pmezard/go-difflib v1.0.0 // indirect
	github.com/prometheus/client_model v0.6.1 // indirect
	github.com/prometheus/common v0.61.0 // indirect
	github.com/prometheus/procfs v0.15.1 // indirect
	github.com/redis/go-redis/v9 v9.0.3 // indirect
	github.com/richardartoul/molecule v1.0.1-0.20221107223329-32cfee06a052 // indirect
	github.com/rivo/uniseg v0.4.7 // indirect
	github.com/robfig/cron v1.2.0 // indirect
	github.com/robfig/cron/v3 v3.0.1 // indirect
	github.com/sanity-io/litter v1.5.5 // indirect
	github.com/senseyeio/duration v0.0.0-20180430131211-7c2a214ada46 // indirect
	github.com/sergi/go-diff v1.3.1 // indirect
	github.com/spaolacci/murmur3 v1.1.0 // indirect
	github.com/stretchr/objx v0.5.2 // indirect
	github.com/twmb/murmur3 v1.1.5 // indirect
	github.com/valyala/bytebufferpool v1.0.0 // indirect
	github.com/valyala/fasthttp v1.40.0 // indirect; indirectf
	github.com/valyala/fasttemplate v1.2.2
	github.com/xeipuuv/gojsonpointer v0.0.0-20190905194746-02993c407bfb // indirect
	github.com/xeipuuv/gojsonreference v0.0.0-20180127040603-bd5ef7bd5415 // indirect
	github.com/xeipuuv/gojsonschema v1.2.0 // indirect
	github.com/yalp/jsonpath v0.0.0-20180802001716-5cc68e5049a0 // indirect
	github.com/yudai/gojsondiff v1.0.0 // indirect
	github.com/yudai/golcs v0.0.0-20170316035057-ecda9a501e82 // indirect
	go.nhat.io/otelsql v0.14.0
	go.opentelemetry.io/otel/exporters/otlp/otlptrace v1.32.0 // indirect
	go.opentelemetry.io/proto/otlp v1.3.1 // indirect
	go.temporal.io/sdk/contrib/opentelemetry v0.2.0
	go.uber.org/multierr v1.11.0 // indirect
	golang.org/x/oauth2 v0.26.0
	google.golang.org/appengine v1.6.8 // indirect
	google.golang.org/genproto/googleapis/api v0.0.0-20250106144421-5f5ef82da422 // indirect
	gopkg.in/alexcesaro/quotedprintable.v3 v3.0.0-20150716171945-2caba252f4dc // indirect
	gopkg.in/go-playground/assert.v1 v1.2.1 // indirect
	k8s.io/apimachinery v0.26.2 // indirect
	k8s.io/klog/v2 v2.80.2-0.20221028030830-9ae4992afb54 // indirect
	moul.io/http2curl/v2 v2.3.0 // indirect
	sigs.k8s.io/yaml v1.3.0 // indirect
)

require (
	github.com/DataDog/datadog-agent/pkg/obfuscate v0.45.0 // indirect
	github.com/DataDog/datadog-agent/pkg/remoteconfig/state v0.45.0 // indirect
	github.com/DataDog/datadog-go/v5 v5.3.0 // indirect
	github.com/DataDog/go-tuf v0.3.0--fix-localmeta-fork // indirect
	github.com/DataDog/gostackparse v0.6.0 // indirect
	github.com/DataDog/sketches-go v1.4.2 // indirect
	github.com/Knetic/govaluate v3.0.1-0.20171022003610-9aa49832a739+incompatible // indirect
	github.com/Microsoft/go-winio v0.6.2 // indirect
	github.com/alicebob/gopher-json v0.0.0-20200520072559-a9ecdc9d1d3a // indirect
	github.com/boombuler/barcode v1.0.1
	github.com/dgryski/go-rendezvous v0.0.0-20200823014737-9f7001d12a5f // indirect
	github.com/dustin/go-humanize v1.0.1
	github.com/finbox-in/octoclient v0.3.1-0.20240924201246-f44497ebd5d0
	github.com/gabriel-vasile/mimetype v1.4.3 // indirect
	github.com/go-pkgz/expirable-cache v1.0.0 // indirect
	github.com/gomodule/redigo v1.8.9 // indirect
	github.com/google/pprof v0.0.0-20230602150820-91b7bce49751 // indirect
	github.com/itchyny/timefmt-go v0.1.5 // indirect
	github.com/jackc/pgpassfile v1.0.0 // indirect
	github.com/jackc/pgservicefile v0.0.0-20231201235250-de7065d80cb9 // indirect
	github.com/jackc/pgx/v5 v5.5.5 // indirect
	github.com/jackc/puddle/v2 v2.2.2 // indirect
	github.com/jonboulle/clockwork v0.4.0 // indirect
	github.com/mattn/go-sqlite3 v2.0.3+incompatible // indirect
	github.com/secure-systems-lab/go-securesystemslib v0.5.0 // indirect
	github.com/tinylib/msgp v1.1.8 // indirect
	github.com/xlzd/gotp v0.1.0
	github.com/xuri/efp v0.0.0-20230422071738-01f4e37c47e9 // indirect
	github.com/xuri/nfp v0.0.0-20230503010013-3f38cdbb0b83 // indirect
	github.com/yuin/gopher-lua v0.0.0-20220504180219-658193537a64 // indirect
	go.uber.org/atomic v1.11.0 // indirect
	go4.org/intern v0.0.0-20230525184215-6c62f75575cb // indirect
	go4.org/unsafe/assume-no-moving-gc v0.0.0-20230525183740-e7c30c78aeb2 // indirect
	golang.org/x/image v0.21.0 // indirect
	golang.org/x/mod v0.23.0 // indirect
	golang.org/x/net v0.40.0
	golang.org/x/sys v0.33.0 // indirect
	golang.org/x/time v0.9.0 // indirect
	google.golang.org/grpc v1.70.0
	inet.af/netaddr v0.0.0-20230525184311-b8eac61e914a // indirect
)

require (
	github.com/DATA-DOG/go-sqlmock v1.5.2
	github.com/DataDog/appsec-internal-go v1.0.0 // indirect
	github.com/DataDog/go-libddwaf v1.2.0 // indirect
	github.com/cashfree/cashfree-pg/v4 v4.1.2
	github.com/go-playground/locales v0.14.1 // indirect
	github.com/go-playground/universal-translator v0.18.1 // indirect
	github.com/golang/protobuf v1.5.4 // indirect
	github.com/jmespath/go-jmespath v0.4.0 // indirect
	github.com/leodido/go-urn v1.4.0 // indirect
	github.com/mohae/deepcopy v0.0.0-20170929034955-c48cc78d4826 // indirect
	github.com/outcaste-io/ristretto v0.2.2 // indirect
	github.com/pdfcpu/pdfcpu v0.9.1
	github.com/philhofer/fwd v1.1.2 // indirect
	github.com/richardlehane/mscfb v1.0.4 // indirect
	github.com/richardlehane/msoleps v1.0.3 // indirect
	github.com/sendgrid/rest v2.6.9+incompatible
	github.com/wamuir/go-xslt v0.1.5
	golang.org/x/xerrors v0.0.0-20220907171357-04be3eba64a2 // indirect
	google.golang.org/genproto/googleapis/rpc v0.0.0-20250115164207-1a7da9e5054f // indirect
	google.golang.org/protobuf v1.36.4
)
