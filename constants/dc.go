package constants

// constants for device connect
const (
	DeviceConnectStatusInProgress = 0
	DeviceConnectStatusCompleted  = 1
	DeviceConnectStatusFailed     = 2
)

var DeviceConnectStatusToStrMapping = map[int]string{
	DeviceConnectStatusInProgress: "in_progress",
	DeviceConnectStatusCompleted:  "completed",
	DeviceConnectStatusFailed:     "failed",
}

var AllowedSelectedDeviceConnect = []string{
	"acc0_acc_number",
	"acc0_av_balance_c30",
	"acc0_min_bal_c30",
	"acc1_acc_number",
	"acc1_av_balance_c30",
	"acc1_min_bal_c30",
	"acc2_acc_number",
	"acc2_av_balance_c30",
	"acc2_min_bal_c30",
	"all_sms_count",
	"amt_credit_txn_m1",
	"amt_credit_txn_m2",
	"amt_credit_txn_m3",
	"amt_debit_txn_m1",
	"amt_debit_txn_m2",
	"amt_debit_txn_m3",
	"amt_delinquncy_loan_c15",
	"amt_delinquncy_loan_c30",
	"amt_delinquncy_loan_c60",
	"auto_debit_bounce_m0",
	"auto_debit_bounce_m1",
	"auto_debit_bounce_m2",
	"auto_debit_bounce_m3",
	"cc1_bank",
	"cc1_credit_limit",
	"cnt_apps_genre_business",
	"cnt_apps_genre_finance",
	"cnt_apps_genre_social",
	"cnt_credit_txn_m1",
	"cnt_credit_txn_m2",
	"cnt_credit_txn_m3",
	"cnt_debit_txn_m1",
	"cnt_debit_txn_m2",
	"cnt_debit_txn_m3",
	"cnt_delinquncy_loan_c15",
	"cnt_delinquncy_loan_c30",
	"cnt_delinquncy_loan_c60",
	"cnt_ewallets_used",
	"cnt_loan_approved_c30",
	"cnt_loan_rejected_c30",
	"cnt_risky_apps",
	"cnt_salary_txns",
	"cnt_sms_organization_nbfc_digital_lender",
	"communication_metric_government_service",
	"days_since_first_install",
	"emi_loan_acc1",
	"latest_location_latitude",
	"latest_location_longitude",
	"latest_location_timestamp",
	"location_permission_flag",
	"mobile_model",
	"r_cnt_apps_genre_finance_c30_c90",
	"recommended_account_number",
	"salary_m123",
	"score_acquisition",
	"score_cd_v2_emsemble_probability",
	"score_xsell_v4_probability",
	"score_xsell",
	"sdk_device_first_active",
	"sdk_device_last_active",
	"sms_recency",
	"sms_vintage",
	"temp_acc0_acc_number",
	"temp_acc0_avg_bal_m1",
	"temp_acc0_avg_bal_m2",
	"temp_acc0_avg_bal_m3",
	"temp_acc0_sms_count",
	"wallet_user_flag",
	"credit_card_user_flag",
	"debit_card_user_flag",
	"insurance_flag",
	"investor_flag",
	"net_banking_flag",
	"phone_state_permission_flag",
	"score_acquisition_rejection_flag",
	"sms_permission_flag",
	"upi_user_flag",
	"data_sufficiency_flag",
	"score_xsell_rejection_flag",
	"cheque_bounce_c30",
	"loan_acc1_autodebitflag",
	"loan_acc2_autodebitflag",
	"loan_acc3_autodebitflag",
	"prepaid_flag",
	"postpaid_flag",
	"lpg_flag",
	"electricity_flag",
	"dth_flag",
	"broadband_flag",
	"salaried_flag",
	"dual_sim",
	"phonepe_user_flag",
	"paytm_user_flag",
	"mobikwik_user_flag",
	"jio_user_flag",
	"freecharge_user_flag",
	"amazonpay_user_flag",
	"rd_flag",
	"po_flag",
	"mf_flag",
	"fd_flag",
	"equity_flag",
	"si_bounce_m3",
	"si_bounce_m2",
	"si_bounce_m1",
	"si_bounce_m0",
	"ecs_bounce_m3",
	"ecs_bounce_m2",
	"ecs_bounce_m1",
	"ecs_bounce_m0",
	"cheque_bounce_m3",
	"cheque_bounce_m2",
	"cheque_bounce_m1",
	"cheque_bounce_m0",
	"cnt_digital_payment_apps",
	"cnt_trading_apps",
	"cnt_overdue_sms_c30",
	"cnt_loan_rejected_c90",
	"cnt_loan_rejected_c60",
	"cnt_loan_rejected_c7",
	"apps_data_null_reason",
	"device_data_null_reason",
	"location_data_null_reason",
	"sms_data_null_reason",
	"location_vintage",
	"location_recency",
	"cnt_accurate_location_datapoints",
	"cnt_location_datapoints",
	"days_since_max_bal_m2",
	"days_since_max_bal_m1",
	"days_since_max_bal_m0",
	"cnt_delinquncy_acc1",
	"cnt_delinquncy_acc2",
	"cnt_delinquncy_acc3",
	"loan_acc1_recency",
	"loan_acc2_recency",
	"loan_acc3_recency",
	"cnt_loan_accounts",
	"max_dpd_acc1",
	"max_dpd_acc2",
	"max_dpd_acc3",
	"cnt_apps_less_popular",
	"source_app_vintage",
	"cnt_apps_more_popular",
	"cnt_apps_bad_ratings",
	"cnt_apps_good_ratings",
	"cnt_apps_content_rating_teen",
	"cnt_apps_content_rating_adult",
	"cnt_apps_genre_productivity_i30",
	"cnt_apps_genre_communication_i30",
	"cnt_apps_genre_social_i30",
	"cnt_apps_genre_business_i30",
	"cnt_apps_genre_food_and_drink_i30",
	"cnt_apps_genre_lifestyle_i30",
	"cnt_apps_genre_entertainment_i30",
	"cnt_apps_genre_books_and_reference_i30",
	"cnt_apps_genre_finance_i30",
	"days_since_last_update",
	"cnt_apps_genre_productivity",
	"cnt_apps_genre_communication",
	"cnt_apps_genre_food_and_drink",
	"cnt_apps_genre_lifestyle",
	"cnt_apps_genre_entertainment",
	"cnt_apps_genre_books_and_reference",
	"cnt_paid_apps",
	"cnt_play_store_apps",
	"cnt_apps",
	"acc2_vintage",
	"acc2_sms_count",
	"acc2_recency",
	"acc2_cnt_debits_p30",
	"acc2_cnt_debits_c30",
	"acc2_cnt_credits_p30",
	"acc2_cnt_credits_c30",
	"acc1_vintage",
	"acc1_sms_count",
	"acc1_recency",
	"acc1_cnt_debits_p30",
	"acc1_cnt_debits_c30",
	"acc1_cnt_credits_p30",
	"acc1_cnt_credits_c30",
	"acc0_vintage",
	"acc0_sms_count",
	"acc0_recency",
	"acc0_cnt_debits_p30",
	"acc0_cnt_debits_c30",
	"acc0_cnt_credits_p30",
	"acc0_cnt_credits_c30",
	"acc_data_vintage",
	"telecom_operator2_vintage",
	"telecom_operator2_recency",
	"telecom_operator1_vintage",
	"telecom_operator1_recency",
	"max_dpd_electric",
	"max_dpd_dth",
	"max_dpd_broadband",
	"lpg_vintage",
	"lpg_recency",
	"electricity_vintage",
	"electricity_recency",
	"dth_vintage",
	"dth_recency",
	"cnt_topup_m3",
	"cnt_topup_m2",
	"cnt_topup_m1",
	"cnt_postpaid_bill_overdue_c180",
	"cnt_postpaid_bill_m6",
	"cnt_postpaid_bill_m5",
	"cnt_postpaid_bill_m4",
	"cnt_postpaid_bill_m3",
	"cnt_postpaid_bill_m2",
	"cnt_postpaid_bill_m1",
	"cnt_postpaid_bill_m0",
	"cnt_phone_num_c90",
	"cnt_phone_num_c60",
	"cnt_phone_num_c30",
	"broadband_vintage",
	"broadband_recency",
	"salary_recency",
	"num_total_debit_sms",
	"num_total_credit_sms",
	"num_months_txn_data",
	"cnt_transfer_out_m3",
	"cnt_transfer_out_m2",
	"cnt_transfer_out_m1",
	"cnt_transfer_out_m0",
	"cnt_savings_accounts",
	"cnt_debit_txn_m0",
	"cnt_debit_self_transf_m3",
	"cnt_debit_self_transf_m2",
	"cnt_debit_self_transf_m1",
	"cnt_debit_self_transf_m0",
	"cnt_dc_txn_m3",
	"cnt_dc_txn_m2",
	"cnt_dc_txn_m1",
	"cnt_dc_txn_m0",
	"cnt_credit_txn_m0",
	"cnt_cc_txn_m3",
	"cnt_cc_txn_m2",
	"cnt_cc_txn_m1",
	"cnt_cc_txn_m0",
	"cnt_cash_wdl_m3",
	"cnt_cash_wdl_m2",
	"cnt_cash_wdl_m1",
	"cnt_cash_wdl_m0",
	"sms_period",
	"transactional_sms_count",
	"unique_devices",
	"num_times_sdk_device_active",
	"wallet_vintage_sms",
	"wallet_recency",
	"phonepe_vintage_sms",
	"phonepe_recency",
	"paytm_vintage_sms",
	"paytm_recency",
	"mobikwik_vintage_sms",
	"mobikwik_recency",
	"jio_vintage_sms",
	"jio_recency",
	"freecharge_vintage_sms",
	"freecharge_recency",
	"cnt_wallet_txn_m3",
	"cnt_wallet_txn_m2",
	"cnt_wallet_txn_m1",
	"cnt_wallet_debits",
	"cnt_wallet_credits",
	"cnt_unique_wallets_used",
	"cnt_paytm_wallet_recharge_m3",
	"cnt_paytm_wallet_recharge_m2",
	"cnt_paytm_wallet_recharge_m1",
	"cnt_paytm_txn_m3",
	"cnt_paytm_txn_m2",
	"cnt_paytm_txn_m1",
	"cnt_paytm_p2p_m3",
	"cnt_paytm_p2p_m2",
	"cnt_paytm_p2p_m1",
	"cnt_paytm_ecom_m3",
	"cnt_paytm_ecom_m2",
	"cnt_paytm_ecom_m1",
	"cnt_ewallet_txns_c90",
	"amazonpay_vintage_sms",
	"amazonpay_recency",
	"rd_trx_recency",
	"po_trx_recency",
	"mf_trx_recency",
	"insurance_trx_recency",
	"fd_trx_recency",
	"expense_data_vintage",
	"equity_trx_recency",
	"cnt_rd_accounts",
	"cnt_insurance_accounts",
	"cnt_fd_accounts",
	"cnt_delinquency_electricity",
	"cnt_delinquency_dth",
	"cnt_delinquency_broadband",
	"rejection_rule",
	"profession_type",
	"calculated_income_confidence",
	"estimated_age_bracket",
	"estimated_gender",
	"cc2_bank",
	"lender1",
	"lender2",
	"lender3",
	"loan_acc1",
	"loan_acc2",
	"loan_acc3",
	"acc2_bank",
	"acc1_bank",
	"acc0_bank",
	"telecom_operator2",
	"telecom_operator1",
	"lpg_provider",
	"electricity_provider",
	"dth_provider",
	"broadband_provider",
	"primary_usage_acc_id",
	"primary_debit_acc_id",
	"display_language",
	"sim_2_operator_name",
	"sim_1_operator_name",
	"network_type",
	"os_version",
	"brand",
	"communication_metric_telco",
	"total_avg_bal_30",
	"acc0_av_balance_c60",
	"score_digital_acquisition",
	"cnt_cc_applications_c90",
	"cnt_cc_applications_c60",
	"cnt_cc_applications_c30",
	"cnt_cc_applications_c7",
	"cnt_loan_approved_c90",
	"cnt_loan_approved_c60",
	"cnt_loan_approved_c7",
	"calculated_income",
	"obligations",
	"recommended_duedate",
	"digital_savviness",
	"cc1_utilisation",
	"home_work_dist",
	"work_long",
	"work_lat",
	"home_long",
	"home_lat",
	"bestplace_long",
	"bestplace_lat",
	"max_bal_m2",
	"max_bal_m1",
	"max_bal_m0",
	"cc1_bill_m1",
	"emi_loan_acc2",
	"emi_loan_acc3",
	"r_cnt_apps_genre_productivity_c30_c90",
	"r_cnt_apps_genre_communication_c30_c90",
	"r_cnt_apps_genre_social_c30_c90",
	"r_cnt_apps_genre_business_c30_c90",
	"r_cnt_apps_genre_food_and_drink_c30_c90",
	"r_cnt_apps_genre_lifestyle_c30_c90",
	"r_cnt_apps_genre_entertainment_c30_c90",
	"r_cnt_apps_genre_books_and_reference_c30_c90",
	"avg_price_paid_apps",
	"acc2_min_balance",
	"acc2_min_bal_p7",
	"acc2_min_bal_p30",
	"acc2_min_bal_m3",
	"acc2_min_bal_m2",
	"acc2_min_bal_m1",
	"acc2_min_bal_m0",
	"acc2_min_bal_c7",
	"acc2_min_bal_3_mo",
	"acc2_max_balance",
	"acc2_max_bal_p7",
	"acc2_max_bal_p30",
	"acc2_max_bal_m3",
	"acc2_max_bal_m2",
	"acc2_max_bal_m1",
	"acc2_max_bal_m0",
	"acc2_max_bal_c7",
	"acc2_max_bal_c30",
	"acc2_max_bal_3_mo",
	"acc2_latest_balance",
	"acc2_eom_av_balance",
	"acc2_av_balance_c90",
	"acc2_av_balance_c7",
	"acc2_amt_debits_p30",
	"acc2_amt_debits_c30",
	"acc2_amt_credits_p30",
	"acc2_amt_credits_c30",
	"acc1_min_balance",
	"acc1_min_bal_p7",
	"acc1_min_bal_p30",
	"acc1_min_bal_m3",
	"acc1_min_bal_m2",
	"acc1_min_bal_m1",
	"acc1_min_bal_m0",
	"acc1_min_bal_c7",
	"acc1_min_bal_3_mo",
	"acc1_max_balance",
	"acc1_max_bal_p7",
	"acc1_max_bal_p30",
	"acc1_max_bal_m3",
	"acc1_max_bal_m2",
	"acc1_max_bal_m1",
	"acc1_max_bal_m0",
	"acc1_max_bal_c7",
	"acc1_max_bal_c30",
	"acc1_max_bal_3_mo",
	"acc1_latest_balance",
	"acc1_eom_av_balance",
	"acc1_av_balance_c90",
	"acc1_av_balance_c7",
	"acc1_amt_debits_p30",
	"acc1_amt_debits_c30",
	"acc1_amt_credits_p30",
	"acc1_amt_credits_c30",
	"acc0_min_balance",
	"acc0_min_bal_p7",
	"acc0_min_bal_p30",
	"acc0_min_bal_m3",
	"acc0_min_bal_m2",
	"acc0_min_bal_m1",
	"acc0_min_bal_m0",
	"acc0_min_bal_c7",
	"acc0_min_bal_3_mo",
	"acc0_max_balance",
	"acc0_max_bal_p7",
	"acc0_max_bal_p30",
	"acc0_max_bal_m3",
	"acc0_max_bal_m2",
	"acc0_max_bal_m1",
	"acc0_max_bal_m0",
	"acc0_max_bal_c7",
	"acc0_max_bal_c30",
	"acc0_max_bal_3_mo",
	"acc0_latest_balance",
	"acc0_eom_av_balance",
	"acc0_av_balance_c90",
	"acc0_av_balance_c7",
	"acc0_amt_debits_p30",
	"acc0_amt_debits_c30",
	"acc0_amt_credits_p30",
	"acc0_amt_credits_c30",
	"sum_bal_at_topup_m3",
	"sum_bal_at_topup_m2",
	"sum_bal_at_topup_m1",
	"sum_amt_topup_m3",
	"sum_amt_topup_m2",
	"sum_amt_topup_m1",
	"min_postpaid_dd_pd_diff_c180",
	"max_amt_topup_m3",
	"max_amt_topup_m2",
	"max_amt_topup_m1",
	"min_amt_topup_m3",
	"min_amt_topup_m2",
	"min_amt_topup_m1",
	"max_postpaid_dd_pd_diff_c180",
	"bill_electricity_m3",
	"bill_electricity_m2",
	"bill_electricity_m1",
	"bill_electricity_m0",
	"bill_dth_m3",
	"bill_dth_m2",
	"bill_dth_m1",
	"bill_dth_m0",
	"bill_broadband_m3",
	"bill_broadband_m2",
	"bill_broadband_m1",
	"bill_broadband_m0",
	"avg_postpaid_bill_4_6m",
	"avg_postpaid_bill_3m",
	"avg_bal_at_topup_m3",
	"avg_bal_at_topup_m2",
	"avg_bal_at_topup_m1",
	"av_amt_topup_m3",
	"av_amt_topup_m2",
	"av_amt_topup_m1",
	"amt_postpaid_bill_m6",
	"amt_postpaid_bill_m5",
	"amt_postpaid_bill_m4",
	"amt_postpaid_bill_m3",
	"amt_postpaid_bill_m2",
	"amt_postpaid_bill_m1",
	"amt_postpaid_bill_m0",
	"total_income_m3",
	"total_income_m2",
	"total_income_m1",
	"total_income_m0",
	"salary_m3",
	"salary_m2",
	"salary_m1",
	"other_income_m3",
	"other_income_m2",
	"other_income_m1",
	"max_credit_amount_m3",
	"max_credit_amount_m2",
	"max_credit_amount_m1",
	"max_credit_amount_m0",
	"max_amt_debit_txn_m3",
	"max_amt_debit_txn_m2",
	"max_amt_debit_txn_m1",
	"max_amt_debit_txn_m0",
	"calculated_salary",
	"amt_transfer_out_m3",
	"amt_transfer_out_m2",
	"amt_transfer_out_m1",
	"amt_transfer_out_m0",
	"amt_debit_wo_transf_m3",
	"amt_debit_wo_transf_m2",
	"amt_debit_wo_transf_m1",
	"amt_debit_wo_transf_m0",
	"amt_debit_txn_m0",
	"amt_debit_self_transf_m3",
	"amt_debit_self_transf_m2",
	"amt_debit_self_transf_m1",
	"amt_debit_self_transf_m0",
	"amt_dc_txn_m3",
	"amt_dc_txn_m2",
	"amt_dc_txn_m1",
	"amt_dc_txn_m0",
	"amt_credit_txn_m0",
	"amt_cc_txn_m3",
	"amt_cc_txn_m2",
	"amt_cc_txn_m1",
	"amt_cc_txn_m0",
	"amt_cash_wdl_m3",
	"amt_cash_wdl_m2",
	"amt_cash_wdl_m1",
	"amt_cash_wdl_m0",
	"total_ram",
	"available_ram",
	"total_external_storage",
	"available_external_storage",
	"total_internal_storage",
	"available_internal_storage",
	"amt_wallet_txn_m3",
	"amt_wallet_txn_m2",
	"amt_wallet_txn_m1",
	"amt_wallet_debits",
	"amt_wallet_credits",
	"amt_paytm_wallet_recharge_m3",
	"amt_paytm_wallet_recharge_m2",
	"amt_paytm_wallet_recharge_m1",
	"amt_paytm_txn_m3",
	"amt_paytm_txn_m2",
	"amt_paytm_txn_m1",
	"amt_paytm_p2p_m3",
	"amt_paytm_p2p_m2",
	"amt_paytm_p2p_m1",
	"amt_paytm_ecom_m3",
	"amt_paytm_ecom_m2",
	"amt_paytm_ecom_m1",
	"amt_rd_accounts_c180",
	"amt_mf_accounts_c180",
	"amt_insurance_accounts_c180",
	"amt_fd_accounts_c180",
}

const (
	DeviceConnectAsync   = "async"
	DeviceConnectSync    = "sync"
	DeviceConnectDisable = "disable"
)
