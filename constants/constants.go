package constants

import (
	"finbox/go-api/models/dashboardworkflow"
	"fmt"
	"math/rand"
	"strings"
	"time"
)

const (
	InternalHeaderAPIKey = "x-api-key"
	HTTPHeaderClientID   = "X-Client-ID"
	HeaderToken          = "token"
	SourceEntityID       = "source-entity-id"
	AuthorizationToken   = "authorization"
)

const DefaultSDKSessionTimeout = time.Hour * 24
const TokenLength = 64
const DateFormat = "2006-01-02"
const TimeZoneDiff = "05:30:00"
const DBTimeFormat = "2006-01-02 15:04:05"
const DateTimeGenericFormat = "2006-01-02T15:04:05"
const DefaultTimeZone = "Asia/Calcutta"
const YYYYMMDDHyphenDateFormatConstant = "2006-01-02"
const DBDateFormat = "02-Jan-2006"

const MFATokenTimeout = time.Minute * 15

const PlantixLimit = 25_000

const DefaultBureauRefetchThreshold = 60 * 24 * 60
const ABFLBureauRefetchThreshold = 30 * 24 * 60

const BounceChargeConstant = 500.0
const LateChargeConstant = 40.0

const RedisOTPDataSuffix = "_otp"
const RedisOTPAttemptDataSuffix = "_otp_attempts"
const RedisLenderDashboardEmailLoginMFAOTPDataSuffix = "_ld_email_login_mfa_otp"
const RedisMasterDashboardEmailLoginMFAOTPDataSuffix = "_md_email_login_mfa_otp"
const RedisMasterDashboardMobileLoginMFAOTPDataSuffix = "_md_mobile_login_mfa_otp"
const ActivityLogTimeLimit = 3600 * 24 * 60
const OneMonth = 3600 * 24 * 30
const OneWeek = time.Hour * 24 * 7
const OneYear = time.Hour * 24 * 365
const OneDay = 3600 * 24
const ThirtyMinutes = time.Minute * 30

const KYCValidity = 3600 * 24 * 30 * 6 // 6 months
const DisqualifyRevertRule = "disqualify_revert_rule"

const ResetPasswordLinkTTL = time.Minute * 30

const MaxPennydropAttempts = "max penny drop attempts reached"

/* #nosec */
const (
	MaxEligibleAmountBL = 30_00_000 // 30 lakhs
	MaxEligibleAmountPL = 5_00_000  // 5 lakhs
	MaxCreditLineLimit  = 10_00_000
	MaxEligibleAmountEL = 1_00_00_000
)

// SkipEDIDays specifies the weekday to skip the EDI to be considered for
// TODO : Remove this and use journey func instead
var SkipEDIDays = map[time.Weekday]bool{time.Sunday: true}

const (
	InstallmentProgrammeDaily   = "EDI"
	InstallmentProgrammeMonthly = "EMI"
	InstallmentProgrammeWeekly  = "EWI"
)

const (
	MasterDashboardTokenSuffix         = "-md"
	MFATokenSuffix                     = "-mfa"
	LenderTokenSuffix                  = "-l"
	SessionRedisSuffix                 = "-w"
	FreedomTokenSuffix                 = "-f"
	MasterDashboardResetPasswordSuffix = "-mdrp"
	LenderDashboardResetPasswordSuffix = "-lrp"
)

const (
	AxioTokenGenerationKey     = "axio_token_generation"
	MaxRetryOnGenerateTokenAPI = 10
)

// constants for get modules
const (
	ModuleStateFailed    = "FAILED"
	ModuleStateWait      = "WAIT"
	ModuleStateStart     = "START"
	ModuleStateCompleted = "COMPLETED" // used for booster status
)

const (
	StepperStateCompleted = "COMPLETED"
	StepperStateFailed    = "FAILED"
	StepperStateOngoing   = "ONGOING"
	StepperStateUpcoming  = "UPCOMING"
)

const EndModule = "END"

// constants for freedom users
const (
	FreedomUserCreated  = 1
	FreedomUserVerified = 2
)

// constants for user status
const (
	UserStatusExpired        = -2
	UserStatusArchived       = -1
	UserStatusDisqualified   = 0
	UserStatusCreated        = 1
	UserStatusTokenIssued    = 2
	UserStatusProfileUpdated = 3
	UserStatusQualified      = 4
	UserStatusUnderReview    = 5
)

// UserStatusNumToStr is the map to get user status text
var UserStatusNumToStr = map[int]string{
	-2: "USER_EXPIRED",
	-1: "USER_ARCHIVED",
	0:  "USER_DISQUALIFIED",
	1:  "USER_CREATED",
	2:  "USER_TOKEN_ISSUED",
	3:  "USER_PROFILE_UPDATED",
	4:  "USER_QUALIFIED",
	5:  "USER_UNDER_REVIEW",
}

// GetUserStatusNumber gets user status number from text
func GetUserStatusNumber(statusText string) int {
	switch statusText {
	case "USER_DISQUALIFIED":
		return 0
	case "USER_CREATED":
		return 1
	case "USER_TOKEN_ISSUED":
		return 2
	case "USER_PROFILE_UPDATED":
		return 3
	case "USER_QUALIFIED":
		return 4
	case "USER_UNDER_REVIEW":
		return 5
	case "USER_EXPIRED":
		return -2
	}
	return -1
}

// constants for loan status
const (
	LoanStatusCancelled    = 0
	LoanStatusFresh        = 1
	LoanStatusDetails      = 2
	LoanStatusLoanRejected = 3
	LoanStatusLoanApproved = 4
	LoanStatusBankAdded    = 5
	LoanStatusESign        = 6
	LoanStatusDisbursed    = 7
	LoanStatusClosed       = 8
)

// constants for loan kyc status
const (
	LoanKYCStatusNotStarted     = 0
	LoanKYCStatusDocProcessing  = 1
	LoanKYCStatusDocApproved    = 2
	LoanKYCStatusDocRejected    = 3
	LoanKYCStatusBankApproved   = 4
	LoanKYCStatusBankRejected   = 5
	LoanKYCStatusBankProcessing = 6
	LoanKYCStatusUnderReview    = 7
	LoanKYCStatusPreAgreement   = 8
)
const (
	LoanKYCSubStatusDOBMismatch        = 1
	LoanKYCSubStatusNameMismatch       = 2
	LoanKYCSubStatusDOBAndNameMismatch = 3
)

// constants for external service
const (
	ExternalServiceStatusCompleted = 1
)

// constants for webhooks
const (
	WebhookStatusSuccess    = 1
	WebhookFailedInternally = 2
	WebhookFailedExternal   = 3
)

// constants for bank connect
const (
	BankConnectStatusArchived   = -1
	BankConnectStatusInProgress = 0
	BankConnectStatusCompleted  = 1
	BankConnectStatusFailed     = 2
)

const (
	BankConnectExtractionFailure = "extraction_failure"
	BankConnectValidationError   = "validation_error"
	BankConnectAccountDedupe     = "account_dedupe"
	BankConnectFraud             = "fraud"
)

var BankConnectStatusMapNumtoStr = map[int]string{
	0: "IN_PROGRESS",
	1: "COMPLETED",
	2: "FAILED",
}

// constants for user loan details
const (
	LoanDetailsStatusInactive = 0
	LoanDetailsStatusActive   = 1
)

// constants for user bank details
const (
	UserBankStatusArchived = -1
	UserBankStatusAdded    = 1
	UserBankStatusApproved = 2
	UserBankStatusRejected = 3
)

var BankStatusMapNumtoStr = map[int]string{
	1: "PENDING",
	2: "APPROVED",
	3: "REJECTED",
}

// constants for bank (master)
const (
	BankStatusInactive = 0
	BankStatusActive   = 1
)

// constants for loan variant
const (
	LoanVariantDefault          = "indvidual"
	LoanVariantCoApplication    = "co_application"
	LoantVariantTopUp           = "top_up"
	LoanVariantMarketplace      = "marketplace"
	LoanVariantPrivateLimited   = "private_limited"
	LoanVariantPartnership      = "partnership"
	LoanVariantCompanies        = "companies"
	LoanVariantPublicLimited    = "public_limited"
	LoanVariantLimitedLiability = "limited_liability_partnership"
)

// constants for OTP Data
const (
	OTPTypeSignin                           = "signin"
	OTPTypeESign                            = "esign"
	OTPTypeBureauConsent                    = "bureau_consent"
	OTPTypeWithdrawl                        = "withdrawl"
	OTPTypeTesting                          = "testing"
	OTPTypeMasterDashboardLogin             = "master_dashboard"
	OTPTypeMDMobileOTPLogin                 = "master_dashboard_mobile_otp"
	OTPTypeTransaction                      = "transaction"
	OTPTypePayLaterLinkAccount              = "paylater_link_account"
	OTPTypeUpdateMasterDashboardUserProfile = "master_dashboard_update_user_profile"
	OTPTypeUpdateLenderDashboardUserProfile = "lender_dashboard_update_user_profile"
	OTPTypeSessionAuth                      = "session_auth"
	OTPPflEmailVerification                 = "pfl_email_verification"
	OTPABFLPLEmailVerification              = "abflpl_email_verification"
	OTPMFLBLEmailVerification               = "mflbl_email_verification"
	OTPPflPersonalEmailVerification         = "pfl_personal_email_verification"
)

var OTPTypes = map[string]bool{
	OTPTypeSignin:                           true,
	OTPTypeESign:                            true,
	OTPTypeBureauConsent:                    true,
	OTPTypeWithdrawl:                        true,
	OTPTypeTesting:                          true,
	OTPTypeMasterDashboardLogin:             true,
	OTPTypeTransaction:                      true,
	OTPTypeUpdateMasterDashboardUserProfile: true,
	OTPTypeUpdateLenderDashboardUserProfile: true,
}

// constants for organization ids
const (
	TataID = "99bf10ac-0247-4d7c-86d7-0d639bbdb6ff"
)

// constants holding sourcing entity IDS
/* #nosec */
const (
	FinBoxID             = "f117b011-f117-b011-8d47-f117b011a259"
	UrbanCompanyID       = "b71c5106-b92e-4073-9d93-ef8196e91a27"
	TestingAccount       = "8647fbb9-8788-453e-88a9-a3abf4da63f7"
	ODTestID             = "e21a734a-c8ca-49bf-9c10-f1e01342aa9e"
	GeniusID             = "808ababb-8515-445d-a1d2-828d06ba23a7"
	OKCreditID           = "961409ae-0396-455f-bf3b-3b0fc07d7d33"
	LetsTransportID      = "5f40859f-1867-4e99-be91-aaff726c604e"
	DukaanPeID           = "27d2baad-709e-4d26-8921-dc5b43194238"
	PAXID                = "3ae87f7e-b871-4dc8-b0b8-e2c789f94ce3"
	IIFLBLID             = "488ca8e9-b821-4502-b4f0-a0c0a6d02fb1"
	KredilyID            = "156f9f56-9824-42c4-b3b9-54c2c22bc423"
	ShopCluesID          = "05f7e54c-d285-4482-86ae-e2c217e4f917"
	DropShopID           = "4b7bbeef-036b-42c0-bf64-115260afd90d"
	ShopKiranaID         = "60601f87-fcab-4a51-80fb-57564f25e9ec"
	ShopKiranaCLID       = "2fd8571d-71e6-46db-9bd5-4c57638fd5bb"
	ArzoooID             = "5c106012-ef0f-4d81-ae4f-7adabff6b225"
	GimBooksID           = "6d4ddb0d-92b4-4c4f-b2ac-6921ebaf4f34"
	GimBooksBLID         = "083e604b-b269-4285-a03e-8040bf074232"
	NiyoID               = "5c6d73ae-e527-42b8-a883-49e60e33c32c"
	PagarBookID          = "a5f8e940-bcc7-4936-826f-300e82336083"
	PFLEducationLoanSEID = "7ee360c9-8bf2-40a2-8d86-b7bb3a3d0223"
	MediboxID            = "95fd69df-d60c-43c0-bc37-070789692feb"
	BeldaraID            = "6524be38-a046-4695-ab39-6b33fe6503c6"
	KhatabookID          = "a6f10630-ccbc-41c2-9a17-89f10c06db56"
	ExpressStoresID      = "935e4b4c-8caf-4c92-a58f-1e2c335c392d"
	IndiaMARTID          = "0beaa52b-beb4-43c4-8e4d-a53ce88b332f"
	DeazzleID            = "c57abc4f-b8ac-401f-91c5-187ac248f46e"
	PlantixID            = "4d2e8bc8-2f72-40d3-b73e-be243edcac38"
	ClearTaxID           = "31ad474e-a35d-42bb-a41c-5a486a8b8a9f"
	CashifyID            = "c89cd150-50b0-4467-9461-882668eb40b4"
	PayMateID            = "7b6de077-8082-418f-9127-8c2432a81929"
	FivePaisaID          = "92a589f1-840e-4ecd-93ff-becaef87111c"
	IIFLMarkets          = "448028e7-20a5-4477-86ee-894eab9128e3"
	VyaparID             = "a44330e9-61a6-4c80-a88b-6ec55e423193"
	VyaparCLID           = "294d0ac2-4835-49b1-8ac6-874feecb5aa4"
	NovopayID            = "2d3ad329-36e4-43cb-9ac7-e264150a3abe"
	CityMallID           = "50cc08ab-82dc-4197-b04c-1be7ce3016a0"
	NiyoBLID             = "5b18827c-b5ab-403b-b056-7c4ef76b7ccf"
	NinjaCartID          = "e37c6f2b-34f8-457d-9a08-acf6b06fb552"
	SolvID               = "28c912ed-231e-4d7b-ac6b-406066f92010"
	BigBasketID          = "23e66f04-1d04-4b3f-b9d8-a4053f43b5fb"
	InfraMarketID        = "90054d3e-1372-4812-92b5-9f1c6c84dec8"
	MitronTVID           = "3b360db4-ed62-4db3-b288-8c5e4528b90a"
	HousingID            = "a0ab9ed9-ff45-487a-b9be-76ac13d7963d"
	AquaConnectID        = "a94b6488-c191-4a1e-8f41-6f5715990c63"
	IppoPayID            = "42720b5d-ff6f-4e30-972b-8e30eba20648"
	AgrostarID           = "19252272-91b9-46ed-bf14-6f36c8d7e785"
	PorterID             = "01c8100a-980d-4a2b-aeff-5d3bc14939fd"
	JioID                = "221a9a05-31d2-4968-aaae-e31b6dad0169"
	FyndID               = "eaaa02f1-2088-45ca-ae32-435e132f4e2f"
	GojoID               = "d6b3ceb1-33c5-4f8b-99ef-1f02504e2f50"
	TataNexarcID         = "b2b9a111-32a7-4001-bb1d-216f46ab8a93"
	CostboID             = "4948f2ec-1ec6-40f6-98e3-ed76a73380ec"
	JarID                = "89edff53-bda7-45e3-88bf-741eb6a48270"
	FloBizID             = "a9bbe5eb-6830-47e1-a604-6e6cbf9ab36c"
	JMFinancialID        = "0142b920-248e-4192-81b4-4a201cb26bab"
	Lab2LandID           = "2a3f6cb4-75be-4754-ab02-4c2e2f87a711"
	OneMuthootID         = "3d069310-82f8-4d75-8479-6eb5b13ceb9f"
	TataBNPLID           = "ec075800-d51f-4864-90c5-f1d05a53ce15"
	TataPLID             = "681392ab-b55c-44aa-9238-5de170e729d7"
	SupremeSolarID       = "adc3034f-c29b-45f4-918a-dacf1d711f65"
	SwiggyID             = "5d55f015-b105-407d-99f4-aba2045d250b"
	MoneyControlID       = "b209a014-b4e4-42ec-9ab6-209fe60be01d"
	PhonePeID            = "bf0cead2-2d9f-4125-8fc0-203e605170c7"
	PayTMID              = "d3b823f7-71bb-419b-b079-c5f8feee5873"
	BharatPeID           = "92c4e013-b664-4df4-a4fc-d319186c1bc4"
	ABFLMarketplaceID    = "17c1d22a-07dc-47ce-b6ce-8d003a1e7992"
	MoneyViewMFLID       = "c5fc5e8f-7f2f-480f-af8d-63b6f7b2b6c5"
	SuperMoneyID         = "6c1d29be-83ec-4d92-ab5d-f7dab4342a00"
	ABCDMarketPlaceID    = "33c84de2-c691-4758-b6d5-f4cf5dd6c769"
	MuthootLAPID         = "8aa51319-62cc-4c7e-a97f-26c3853ff021"
	KreditBeePlatformID  = "04ae8a45-f006-4627-96ff-9331cb077add"
	PrefrSuperMoneyID    = "f06cf417-f415-4d93-b733-a45e885a4d36"
)

// DSA IDs constants
const (
	DSAMoneyControlIncredID          = "e16bb1a7-54b8-41c7-b1b3-b7f1f32af389"
	DSAAndromedaPL                   = "a27bbd40-67e0-43c5-a2bf-b204cbd6d003"
	DSAAndromedaBL                   = "4b110086-b0f3-47bc-88a4-4b5f8c204f6a"
	DSABFDLID                        = "97bbb4ea-bf94-472d-a08e-420a01df02d0"
	GPayIIFLPLID                     = "8994882f-e593-4a28-9e9d-5d0a4fe0fc03"
	GPayIIFLBLID                     = "f5884ff4-3f4b-45c0-89ef-7825bb562229"
	DSAIndiaLendsPLID                = "b0d8d015-bb23-4989-9bc3-2a6d9944d75f"
	DSAWishFinPLID                   = "d4f209f7-cede-4e04-bf83-973e1b687577"
	IIFLGLBLID                       = "04cbde2c-bc1b-4315-b619-7a94b677b7e6"
	IIFLGLPLID                       = "eadae642-f00a-40f0-ac12-96c77dc6e4bb"
	BFDLIIFLBLID                     = "828f4333-94ad-4aca-b263-3f0ac54a7879"
	SolvMuthootCLDSAID               = "6964da5b-7740-4d38-8fd4-ebe6e09dec3f"
	FloBizMuthootCLDSAID             = "16bc7393-060e-42d8-8c71-de94c7945a02"
	ValueMediMuthootCLDSAID          = "86efc042-7020-444c-a892-fc20d1d93c5f"
	KredMintABFLDSAID                = "f571848b-549f-4c1c-997a-a441398b728b"
	DSATataCapitalIIFLBLID           = "42aafd46-0001-4656-8e4c-61cbb1dde19e"
	DSAGromoIIFLBLID                 = "8f04762a-7b49-4cf6-9a7a-2de063944719"
	DSAIndiaMARTIIFLID               = "9eacd4a2-a6ae-4a23-8b0c-c32b9c14b1cd"
	DSAPaisaBazaarIIFLID             = "22a6eeee-0ed0-4bdd-b57e-ee3e31e8339f"
	DSAMyMoneyMantraIIFLBLID         = "89606f73-1cd1-4178-9b6d-7ae4db8db4ac"
	DSAPreApprovedABFLID             = "2ec61618-c20c-4d1f-b72e-1a87071e3a48"
	VyaparMuthootCLDSAID             = "751e7cc3-cb1a-4c81-8ba4-1e183b22ac51"
	IIFLBLTopUpID                    = "12039459-25f0-443a-ae3a-4fa4fba22027"
	DSAPreApprovedABFLPartnerABCID   = "f245e163-f339-4ebf-9824-f803e29de973"
	MoneyControlABFLBLDSAID          = "b51d720d-9712-47fc-baa6-18bd6b16f0bd"
	DSAPreApprovedABFLPartnerABCIDV2 = "c9dc2c64-c104-4ecb-95a0-8fc9c3a06fb6"
	DSAPayTMIIFL                     = "f934846d-4374-40e0-a611-f96b504cd1f6"
	DSAPreApprovedABFLPartnerDSTID   = "4a860df8-bd67-473e-a597-b84a8d444c6a"
	ABFLUdyogPlusWebsiteBLPROD       = "1aeae794-1043-45b8-accf-6b8fac165573"
	ABFLUdyogPlusWebsiteBLNonPROD    = "019fad6f-2f5a-4995-83e5-4478337c149b"
	DSAAshvABFLBLID                  = "9d8fbeb1-1951-41cd-b67b-4a75f889c215"
	DSAHemaPvtABFLBLID               = "6a1fc0db-a68f-4411-b3b5-ae0c2ddf2432"
	DSAOpenFinanceABFLBLID           = "1baa6ab0-90e7-48f3-a72a-50ab52df70ab"
	DSAAndromedaSalesABFLBLID        = "c9aceb5f-878c-4d88-b255-33337da07cde"
	DSAPaytmIIFLBLID                 = "f934846d-4374-40e0-a611-f96b504cd1f6"
	NiyoABFLPLDSAID                  = "b48b7c3f-4a06-40ba-af6f-a2f40faaa699"
	MFLBLOrganizationID              = "148fa0b6-f63a-4938-ac69-79831e54200f"
	DSAPreApprovedABFLDigiPartner    = "f221c08f-2f84-4ceb-a9de-6a0ba48d8b90"
	DSAPreApprovedABFLDistribution   = "5c5b2861-e674-4430-8e7b-df32f2265b28"
	DSAGpayABFLBL                    = "6cead2b1-3f00-4ca5-a56b-ca3bb1a0b052"
	ABFLPLDSAFiMoney                 = "8f05604c-0ab4-4081-886b-29ab915d342b"
	ABFLPLBillcutID                  = "6ecea6e0-b0b8-4be8-b80f-4fa7072c189f"
	ABFLPLJARID                      = "583ce85d-b14f-4c80-b08d-b9596309eb83"
	ABFLPLNoBrokerID                 = "4a25c642-cd00-48d6-beaa-2e9fef32e89b"
	DSAPreApprovedABFLABG            = "b63224c6-abeb-4515-8dad-8ed61fe923f1"
	ABFLNonProdUbonaTestID           = "019fad6f-2f5a-4995-83e5-4478337c149b"
	ABFLDSAGetVantage                = "6b7e26c1-11bf-414e-b2a4-d2271dcb8f04"
	DSAKreditBeeMCID                 = "9c73787f-4c29-4231-b0be-1071e4f7ec68" // Money Control DSA for KreditBee Platform
)

var ABFLNonProdUbonaTestIDs = []string{
	"019fad6f-2f5a-4995-83e5-4478337c149b",
	"d714ea41-9834-4608-be6d-4b948c725fe6",
	"6cead2b1-3f00-4ca5-a56b-ca3bb1a0b052",
	"580a916e-2bbd-4fba-bff8-59a29b7644c1",
	"ce4ab605-8ae2-4435-89c6-7e989bd713dd",
	"a7683929-eca2-4fed-8db5-3c505ff46e21",
}

var ABFLPropUbonaDSAs = []string{
	"b51d720d-9712-47fc-baa6-18bd6b16f0bd",
	"1aeae794-1043-45b8-accf-6b8fac165573",
	"60b778b9-77b1-4ed5-a4b8-928f732e2db8",
	"088fc980-c2a6-403a-8c12-9ffcf29cd76f",
	"8eb42b41-63ab-4aa2-98f3-5862777802c1",
	"26eb6ef8-5fb0-4588-9ff3-584c07336394",
	"6cead2b1-3f00-4ca5-a56b-ca3bb1a0b052",
	"f4385d3f-46f0-4474-81b7-1f753c662305",
	"766bdebd-b13e-4a09-ba6e-f66f6d8cf8c4",
	"1aeae794-1043-45b8-accf-6b8fac165573",
}

// constants for lenders
const (
	TrustLenderID       = "8a878fb7-a527-4b34-95ed-7947c878f54b"
	ArthanLenderID      = "100f9dff-b34a-4730-b94a-73f17905c658"
	XYZLenderID         = "a99c54c5-a1b9-42c9-9a2e-977014f754dc"
	ABCLenderID         = "db88fac3-3400-4806-9a97-052da6a89c03"
	HCINLenderID        = "b7fd906e-ed73-4df8-8a2e-040a270c9fb4"
	IIFLID              = "b46205ae-14f3-43ae-bedb-86d4d82df2f8" // also sourcing entity
	TataCapitalID       = "********-3593-44d4-80a8-b20f12ed1860"
	KotakID             = "7aa78e69-ec48-49be-8d95-3201743ff71f"
	RupifiID            = "a5dd51e8-03dc-4f57-9b1e-aa05c061d5ad"
	SaraloanID          = "5908575b-101f-4023-adb6-1764c7544b5a"
	IndifiID            = "be833457-1ece-453a-9966-ba19a9fbcc02"
	AxisBankID          = "4daa78d1-1000-43b1-9ca6-7f87a78f3c82"
	WesternCapitalID    = "e053b320-b541-4654-af79-3185a187d019"
	MintifiID           = "bf7f082a-f01b-46a6-a033-b3396e7db7f3"
	CapitalFloatID      = "a33d7624-1978-482a-a8e0-457a7e9975a0"
	MCSLID              = "74b13f10-7e5b-45cf-8da7-596aa90001d7"
	PoonawallaFincorpID = "e023bd9e-46e2-4964-a8e7-d3e73e62421c" // also sourcing entity
	LendingKartID       = "e416ee4c-5a15-4e2e-a39e-d5245e21135c"
	MuthootCLID         = "ad97435f-af0b-4bed-849e-1b309f943087" // also sourcing entity
	LoanTapID           = "ade4c6b4-0f43-11ee-be56-0242ac120002"
	EcofyID             = "c3610db9-74fb-4afc-bb8f-81b42fd11cf0"
	ABFLID              = "c1a55e53-4701-49a1-923c-3cf32a66b5ec" // also sourcing entity
	TDLKreditBeeID      = "f10397d6-341e-44a8-9d63-4422beb07258"
	DMIID               = "818ce546-ffb1-4cbe-b98b-b0e7d0ce6d22"
	CasheID             = "0f92a263-f6b3-4f5e-bd25-8b40f45b5bd2"
	CasheMCID           = "b9200827-4ca9-455c-8767-aa043bc3620f"
	MoneyViewID         = "17785070-5480-4e13-bf47-ad02c1e4e42a"
	FibeID              = "611eb1d9-5d07-49cb-803e-52f4b68022c5"
	LandTID             = "eda6412a-1723-4ca0-aca3-f697710512fd"
	NiroID              = "cad6b551-fb06-450c-a11c-f50f725b462c"
	MFLID               = "ff8ea9c0-8d7c-4e53-9892-a5d443016f21"
	MFLBLID             = "8efd25fb-9c1f-4adc-90a8-7e2cc0b17775" // also sourcing entity
	ABFLPLID            = "da77e829-52c2-4b1f-9686-2d5bf04660ed" // also sourcing entity
	PrefrID             = "022ab7ad-a7c4-4eae-83b0-56e8664f5601"
	KisshtID            = "66605390-47c9-47e3-ba71-ab5dc79e8477"
	HDFCLenderID        = "1cff2f61-76c1-4097-9450-aadd6bc4d20b"
	FlexiLoansID        = "4ce52768-efe7-48c7-92ed-b56e96143978"
	PrefrMCID           = "005a84bc-eadb-4afe-9b86-41cc0bc13231"
	RingMCID            = "4fad60e9-b612-4f58-8eb1-fde206534ede"
	BajajID             = "0bd7f198-03d5-408e-a2f0-d857862d1d5a"
	SentinelTestID      = "b0c64d39-871f-4429-8f15-7ba2c2fa00aa"
	OndcFibeID          = "bd65cd29-d9e6-4e29-ad21-fec96d5b5039"
	OndcBajajID         = "b3b2bd4e-2407-4f5b-9298-f3b9a2262d47"
	IncredID            = "f1234567-89ab-4cde-bf12-34567890abcd"
	IncredApiStackID    = "7f1a0a63-07be-40a8-8592-67fa452f53a7"
	ABCDPFLID           = "9ef2d46e-157c-411f-ad03-87b5a83d7329"
	MCHDFCID            = "7c057e46-9390-4a71-848e-1accc94573dd"
	KreditBeeLenderID   = "07d0b5dd-d098-48b2-beec-a99f7948c537"
	MCPFLID             = "ac18d4ae-865b-4823-a8b8-4c3ffb5582a8" // Money Control PFL ID
	MCKreditbeeID       = "a817a31c-2d66-49cb-b66f-3e19c27cb18d" // Lender ID for Kreditbee on MoneyControl Platform
	PrefrSMLenderID     = "9c8551e3-fd00-46f0-b61b-64d0011f3bbd"
)

// constants for organization id
const (
	IIFLOrganizationID         = "d83705ab-6bce-43a2-8b17-5ef7d9d460af"
	FinBoxOrganizationID       = "7ae9ddeb-85d1-4dfa-a8c9-2662a3663c29"
	TataNexarcOrganizationID   = "9fc5bc30-4db1-48a6-84b0-6ac06386d68a"
	TataOrganizationID         = "99bf10ac-0247-4d7c-86d7-0d639bbdb6ff"
	HousingOrganizationID      = "b1288148-6d99-4d1e-8adf-6b4ecbda19f8"
	TataCapitalOrganizationID  = "6b0daa1d-79cf-44ec-bed3-d4e348663bfe"
	ABFLOrganizationID         = "eabdf414-d3f3-4f1a-ad71-f01d59d9c05b"
	ABFLPLOrganizationID       = "65c5927f-5673-440e-a7be-e0b51e42c103"
	PoonawallaOrganizationID   = "db5fcba9-70cb-42b3-aaf1-d0afc0903e79"
	MuthootCLOrganizationID    = "068fa0b6-f63a-4938-ac69-79831e54200f"
	MoneyControlOrganizationID = "3079a023-0b62-46cc-ad17-da5b60a0751a"
	MFLOrganizationID          = "148fa0b6-f63a-4938-ac69-79831e54200f"
)

// LenderNamesMap is a map which stores lender name for a lender id
var LenderNamesMap = map[string]string{
	TrustLenderID:       "Trustlenders Capital Pvt Ltd",
	ArthanLenderID:      "Arthan Finance",
	XYZLenderID:         "XYZ lender",
	ABCLenderID:         "ABC Bank",
	HCINLenderID:        "Home Credit India Finance Private Limited",
	IIFLID:              "IIFL Finance Limited",
	TataCapitalID:       "Tata Capital Financial Services Limited",
	KotakID:             "Kotak Mahindra Bank",
	RupifiID:            "Rupifi",
	SaraloanID:          "Saraloan",
	IndifiID:            "Indifi",
	AxisBankID:          "Axis Bank",
	WesternCapitalID:    "Western Capital",
	MintifiID:           "Mintifi",
	CapitalFloatID:      "CapFloat Financial Services Private Limited",
	MCSLID:              "Muthoot Capital Service Limited",
	LendingKartID:       "Lendingkart Finance Limited",
	PoonawallaFincorpID: "Poonawalla Fincorp Limited",
	MuthootCLID:         "Muthoot Fincorp Limited", // TODO : Muthoot CL "this has to be changed to differentiate between existing and CL lender of Muthoot"
	LoanTapID:           "LoanTap Financial Technologies Private Limited",
	EcofyID:             "Ecofy",
	ABFLID:              "Aditya Birla Finance Limited",
	ABFLPLID:            "Aditya Birla Finance Limited - Personal Loan",
	DMIID:               "DMI Finance",
	MoneyViewID:         "Moneyview",
	TDLKreditBeeID:      "KrazyBee",
	CasheID:             "CASHe",
	FibeID:              "Fibe",
	LandTID:             "LTFS",
	NiroID:              "Niro",
	CasheMCID:           "Cashe Lender",
	MFLBLID:             "Muthoot Fincorp - BL",
	PrefrID:             "Prefr",
	KisshtID:            "Kissht",
	FlexiLoansID:        "Flexi Loans",
	PrefrMCID:           "Prefr",
	RingMCID:            "Ring",
	BajajID:             "Bajaj Finance",
	HDFCLenderID:        "HDFC",
	OndcFibeID:          "ONDC Fibe",
	OndcBajajID:         "ONDC Bajaj",
	IncredApiStackID:    "Incred",
	ABCDPFLID:           "PFL",
	MCPFLID:             "Poonawalla Fincorp Limited",
	MCKreditbeeID:       "KreditBee",
}

// offer expiry for money control lender changed to 30 days
var LenderOfferExpiryMapping = map[string]int{
	TDLKreditBeeID: 7,
	TataCapitalID:  7,
	LandTID:        30,
	NiroID:         30,
	ABFLPLID:       30,
	CasheMCID:      30,
	PrefrMCID:      30,
	RingMCID:       30,
	FibeID:         30,
	BajajID:        30,
}

// PlatformNamesMap is a map which stores platform name for a platform id
var PlatformNamesMap = map[string]string{
	"One Muthoot":             OneMuthootID,
	"Poonawalla Fincorp - PL": PoonawallaFincorpID,
	"IIFL":                    IIFLID,
	"MoneyControl":            MoneyControlID,
	"ABFL Personal Loans":     ABFLPLID,
	"UdyogPlus PL":            ABFLPLID,
	"FlexiLoan PL":            ABFLPLID,
	"BTC PL":                  ABFLPLID,
	"Muthoot Fincorp - BL":    MFLBLID,
	"Niyo PL":                 ABFLPLID,
	"ABCD":                    ABCDMarketPlaceID,
}

var ProgramNameMap = map[string]string{
	TataBNPLID: "DUAL_LIMIT_PAYLATER",
	TataPLID:   "PERSONAL_LOAN",
}

// constants for loan offer states
const (
	LoanOfferStateTentative     = "tentative"
	LoanOfferStateOffered       = "offered"
	LoanOfferStateOfferAccepted = "offer_accepted"
)

// constants for entity types
const (
	EntityTypeSourcingEntity = "sourcing_entity"
	EntityTypeDashboardUser  = "sourcing_entity_agent"
	EntityTypeCustomer       = "customer"
	// EntityTypeLender         = "lender"
	EntityTypeLenderUser = "lender_agent"
	EntityTypeSystem     = "system"
	EntityTypeExternal   = "external"
	EntityTypeBorrower   = "borrower"

	SubEntityTypeNote        = "sub_entity_notes"
	SubEntityTypeKycDoc      = "sub_entity_kyc_doc"
	SubEntityTypeBreWorkflow = "sub_entity_bre_workflow"
)

// constants for Entity
const (
	EntityCreated = "entity_created"
)

const (
	EntityStatusCreated = 1
)
const (
	EntityCategoryBorrower      = "borrower"
	EntityCategoryDSA           = "dsa"
	EntityCategoryDashboardUser = "dashboard_user"
)

// constants for activities
const (
	ActivityUserCreated               = "user_created"
	ActivityPartnerDataPushed         = "partner_data_pushed"
	ActivityPartnerDataPushFailed     = "partner_data_push_failed"
	ActivityPreQualificationFailed    = "pre_qualification_failed"
	ActivityEligibilityCalculated     = "eligibility_calculated"
	ActivityProfileUpdated            = "profile_updated"
	ActivityBureauConsentGiven        = "bureau_consent_given"
	ActivityPANVerificationSuccess    = "pan_verification_success"
	ActivityPANFetchBypassed          = "pan_details_fetch_bypassed"
	ActivityPANFetchSuccess           = "pan_details_fetch_success"
	ActivityPANFetchFailed            = "pan_details_fetch_failed"
	ActivityPANVerificationFailed     = "pan_verification_failed"
	ActivityPANVerified               = "pan_verified"
	ActivityPanVerificationByPassed   = "pan_verification_bypassed"
	ActivityPanSubmitted              = "pan_submitted"
	ActivityPANInfoUpdated            = "pan_info_updated"
	ActivityUserFormSubmitted         = "user_form_submitted"
	ActivityBureauAuthAnswerSubmitted = "bureau_auth_answer_submitted"
	ActivityUserDisqualified          = "user_disqualified"
	ActivityUserDedupe                = "lender_dedupe"
	ActivityUserQualified             = "user_qualified"
	ActivityUserExpired               = "user_expired"
	ActivityUserOnboardingStarted     = "user_onboarding_started"
	ActivityLenderPreSelected         = "lender_pre_selected"
	ActivityLenderRejected            = "lender_rejected"
	ActivityPanForm2Submitted         = "pan_form_2_submitted"
	ActivityProfessionalInfoUpdated   = "professional_info_updated"
	ActivityPrivacyPolicyConsentGiven = "privacy_policy_consent_given"
	ActivityTDLPreSOFail              = "Pre_SO_failure"
	ActivityMultiLoanPanFormUpdated   = "multi_loan_pan_form_updated"

	ActivityLenderEligibilityInitiated = "eligibility_initiated"
	ActivityLenderEligibilitySuccess   = "eligibility_success"
	ActivityLenderEligibilityFailure   = "eligibility_failure"
	ActivityLenderEligibilityEval      = "evaluating_eligibility"
	ActivityLenderNotSelected          = "lender_not_selected"
	ActivityLenderSubModuleSelected    = "lender_sub_module_selected"
	ActivityCIBILPullStarted           = "cibil_pull_started"
	ActivityCIBILPullFailed            = "cibil_pull_failed"
	ActivityCIBILPullSuccess           = "cibil_pull_success"
	ActitvityCIBILAuthFlowTriggered    = "cibil_auth_flow_triggered"

	ActivityExperianPullStarted = "experian_pull_started"
	ActivityExperianPullSuccess = "experian_pull_success"
	ActivityExperianPullFailed  = "experian_pull_failed"

	ActivityCIBILHardPullStarted = "cibil_hard_pull_started"
	ActivityCIBILHardPullFailed  = "cibil_hard_pull_failed"
	ActivityCIBILHardPullSuccess = "cibil_hard_pull_success"

	ActivityUserArchived = "user_archived"

	ActivityUserStatusChanged        = "user_status_changed"
	ActivityApplicationStatusChanged = "application_status_changed"
	ActivityLastModuleChanged        = "last_module_changed"

	ActivitySpecialOfferApplied = "special_offer_applied"

	ActivityBusinessInfoUpdated = "business_info_updated"

	ActivityGSTSkipped          = "gst_skipped"
	ActivityGSTINAdded          = "gstin_added"
	ActivityGSTINSelected       = "gstin_selected"
	ActivityGSTConnectAttempted = "gst_connect_attempted"
	ActivityGSTConnectFailed    = "gst_connect_failed"
	ActivityGSTConnected        = "gst_connected"
	ActivityGSTCompleted        = "gst_completed"
	ActivityGSTNotEligible      = "gst_not_eligible"

	ActivityBankConnectInitiated = "bank_connect_session_initiated"
	ActivityBankConnectUploaded  = "bank_connect_uploaded"
	ActivityBankConnectCompleted = "bank_connect_completed"
	ActivityBankConnectFailed    = "bank_connect_failed"
	ActivityBankConnectSkipped   = "bank_connect_skipped"

	ActivityPerfiosStarted              = "perfios_started"
	ActivityPerfiosCompleted            = "perfios_completed"
	ActivityPerfiosFailed               = "perfios_failed"
	ActivityBypassBankConnect           = "bypass_bank_connect"
	ActivityBoostAttemptInitiated       = "boost_attempt_initiated"
	ActivityMandatoryMultiBankInitiated = "mandatory_multi_bank_initiated"

	ActivityLoanApplicationCreated = "loan_application_created"
	DDECompleted                   = "dde_completed"
	ActivityFormUpdated            = "form_updated"
	ActivityPreLoanDataSubmitted   = "pre_loan_data_submitted"
	ActivityLoanFormSkipped        = "loan_form_skipped"

	ActivityDOBUpdated         = "dob_updated"
	ActivityDynamicInfoUpdated = "dynamic_info_updated"
	ActivityGenderUpdated      = "gender_updated"
	ActivityNameUpdated        = "name_updated"
	ActivityFathersNameUpdated = "fathers_name_updated"
	ActivityMobileUpdated      = "mobile_updated"
	ActivityEmailUpdated       = "email_updated"

	ActivityAddressUpdated         = "address_updated"
	ActivityCurrentAddressUpdated  = "current_address_updated"
	ActivityBusinessAddressUpdated = "business_address_updated"
	ActivityOfficeAddressUpdated   = "office_address_updated"
	ActivityFetchLisaOffer         = "fetch_lisa_offer"
	ActivityRedirectionURL         = "fetch_redirection_url"

	// bank kyc
	ActivityBankKYCOTPVerified      = "bank_kyc_otp_verified"
	ActivityBankKYCOTPFailed        = "bank_kyc_otp_verification_failed"
	ActivityBankKYCUserConfirmation = "bank_kyc_user_confirmation"
	ActivityBankKYCSuccessful       = "bank_kyc_successful"
	ActivityBankKYCFailed           = "bank_kyc_failed"
	// liveness
	ActivitySelfieUploadInitiated  = "selfie_upload_initiated"
	ActivitySelfieUploaded         = "selfie_uploaded"
	ActivityPanCardUploaded        = "pan_card_uploaded"
	ActivityLivenessCheckInitiated = "liveness_check_initiated"
	ActivityLivenessCheckFailed    = "liveness_check_failed"
	ActivityLivenessCheckSuccess   = "liveness_check_success"
	ActivityFaceMatchInitiated     = "face_match_initiated"
	ActivityFaceMatchFailed        = "face_match_fail"
	ActivityFaceMatchSuccess       = "face_match_success"

	ActivityOKYCInitiated  = "okyc_initiated"
	ActivityOKYCInProgress = "okyc_processing"
	ActivityOKYCCompleted  = "okyc_completed"

	ActivityKYCSubmitted       = "kyc_submitted"
	ActivityAutoKYCFailed      = "auto_kyc_failed"
	ActivityKYCInitiated       = "kyc_initiated"
	ActivityKYCFailed          = "kyc_failed"
	ActivityKYCVerified        = "kyc_verified"
	ActivityKYCDocRejected     = "kyc_doc_rejected"
	ActivityKYCDocChanged      = "kyc_doc_changed"
	ActivityKYCResubmitted     = "kyc_resubmitted"
	ActivityKYCManualRequested = "kyc_manual_requested"
	ActivityKYCManualSuccess   = "kyc_manual_success"
	ActivityKYCManualFailed    = "kyc_manual_failed"
	ActivityKYCSkipped         = "kyc_skipped"
	// ActivityKYCAddressVerificationQueue = "kyc_address_verification_queue"
	ActivityLenderAssigned    = "lender_assigned"
	ActivityLoanApproved      = "loan_approved"
	ActivityLoanRejected      = "loan_rejected"
	ActivityLoanOfferAccepted = "loan_offer_accepted"
	ActivityEKYCSubmitFailed  = "ekyc_doc_submit_failed"
	ActivityEKYCSubmitSuccess = "ekyc_doc_submit_success"
	ActivityLoanDetails       = "loan_details"
	ActivityloanFresh         = "fresh_loan"

	ActivityCKYCSearchInitiated   = "ckyc_search_initiated"
	ActivityCKYCSearchCompleted   = "ckyc_search_completed"
	ActivityCKYCSearchFailed      = "ckyc_search_failed"
	ActivityCKYCDownloadInitiated = "ckyc_data_download_initiated"
	ActivityCKYCDownloadRetry     = "ckyc_data_download_retry"
	ActivityCKYCDownloadCompleted = "ckyc_data_download_completed"
	ActivityCKYCDownloadFailed    = "ckyc_data_download_failed"
	ActivityCKYConfirmed          = "ckyc_confirmed"
	ActivityCKYCValidationFailed  = "ckyc_validation_failed"

	ActivityAdditionalDocsUploaded = "additional_documents_uploaded"
	ActivityApplicationApproved    = "application_approved"

	ActivityBankDetailsAdded               = "bank_details_added"
	ActivityBankDetailsUpdated             = "bank_details_updated"
	ActivityBankVerificationFailed         = "bank_verification_failed"
	ActivityBankDetailsVerified            = "bank_details_verified"
	ActivityBankDetailsVerificationPending = "bank_details_verification_pending"

	ActivityENachLinkGenerationFailed = "enach_link_generation_failed"
	ActivityENachStarted              = "enach_started"
	ActivityENachAuthSuccess          = "enach_auth_success"
	ActivityENachAuthFailed           = "enach_auth_failed"
	ActivityENachCompleted            = "enach_completed"
	ActivityENachSkipped              = "enach_skipped"
	ActivityEnachFailed               = "enach_failed"

	ActivityUPIAutoPayLinkGenerationFailed = "upi_autopay_link_generation_failed"
	ActivityUPIAutoPayStarted              = "upi_autopay_started"
	ActivityUPIAutoPayAuthSuccess          = "upi_autopay_auth_success"
	ActivityUPIAutoPayAuthFailed           = "upi_autopay_auth_failed"
	ActivityUPIAutoPayCompleted            = "upi_autopay_completed"
	ActivityUPIAutoPayRevoked              = "upi_autopay_revoked"
	ActivityUPIAutoPayFailed               = "upi_autopay_failed"

	ActivityPhysicalMandateDownloaded = "physical_mandate_downloaded"
	ActivityPhysicalMandateEmailed    = "physical_mandate_emailed"
	ActivityPhysicalMandateSubmitted  = "physical_mandate_submitted"
	ActivityPhysicalMandateApproved   = "physical_mandate_approved"
	ActivityPhysicalMandateFailed     = "physical_mandate_failed"

	ActivityNachPresentationFailed    = "nach_presentation_failed"
	ActivityNachPresentationSuccess   = "nach_presentation_success"
	ActivityNachPresentationInitiated = "nach_presentation_initiated"
	ActivityNachPresentationCancelled = "nach_presentation_cancelled"

	// elms check ABFLPL
	ActivityELMSCheckUserInitiated = "ELMS_check_initiated"
	ActivityELMSCheckUserAccepted  = "ELMS_approved"
	ActivityELMSCheckUserRejected  = "ELMS_rejected"

	ActivityESignURLCreated = "esign_url_created"
	ActivityESignURLFailed  = "esign_url_failed"
	ActivityESignInitiated  = "esign_initiated"

	ActivityLoanESigned                  = "loan_esigned"
	ActivityLoanSignedAgreementGenerated = "loan_signed_agreement_generated"
	ActivityLoanDisbursed                = "loan_disbursed"
	ActivityLoanESignFailed              = "loan_esign_failed"
	ActivityPrimaryApplicantEsigned      = "primary_applicant_esigned" // To be deprecated
	ActivityCoApplicantEsigned           = "co_applicant_esigned"      // // To be deprecated
	ActivityUserEsigned                  = "user_esigned"
	ActivityUserEsignFailed              = "user_esign_failed"
	ActivityLoanDisbursalInitiated       = "disbursal_initiated"

	ActivityLoanClosed    = "loan_closed"
	ActivityLoanCancelled = "loan_cancelled"
	// loan offer
	ActivityLoanOfferUpdated  = "loan_offer_updated"
	ActivityLoanOfferInactive = "loan_offer_inactive"
	ActivityAmountUpdated     = "application_amount_updated"
	// loan actions
	ActivityLoanActionsUpdated      = "loan_actions_updated"
	ActivityUTRDetailsUpdated       = "utr_details_updated"
	ActivityDisbursalDetailsUpdated = "disbursal_details_updated"

	ActivityLoanPreAgreement = "loan_moved_to_pre_agreement"

	// business kyc documents
	ActivityBusinessDocsUploaded = "business_docs_uploaded"

	ActivityRepeatUsers = "repeat_users"

	// Credit Line
	ActivityCreditLineCreated   = "credit_line_created"
	ActivityCreditLineWithdrew  = "credit_line_withdrew"
	ActivityCreditLineActivated = "credit_line_activated"
	MediaTypeInvoice            = "INVOICE"
	MediaTypePOD                = "POD"
	MediaTypeAgreement          = "AGREEMENT"

	// callback
	ActivityLenderCallbackReceived = "lender_callback_received"

	/* #nosec */
	ActivityCreditLineDeactivated             = "credit_line_deactivated"
	ActivityCreditLineReactivated             = "credit_line_reactivated"
	ActivityCreditLineWithdrewInitiated       = "credit_line_withdrew_initiated"
	ActivityCreditLineWithdrewFailed          = "credit_line_withdrew_failed"
	ActivityCreditLineLimitUpdated            = "credit_line_limit_updated"
	ActivityCreditLineMaxLimitChangeRequested = "credit_line_max_limit_change_requested"
	ActivityCreditLineMaxLimitIncreased       = "credit_line_max_limit_increased"
	ActivityCreditLineMaxLimitDecreased       = "credit_line_max_limit_decreased"
	/* #nosec */
	ActivityCreditLineTxnConfirmed = "credit_line_txn_confirmed"
	ActivityCreditLineTxnEdited    = "credit_line_txn_details_edited"
	/* #nosec */
	ActivityCreditLineTxnCancelled = "credit_line_txn_cancelled"
	/* #nosec */
	ActivityCreditLineTxnDisbursed       = "credit_line_txn_disbursed"
	ActivityCreditLineTxnDisbursalFailed = "credit_line_txn_disbursal_failed"
	/* #nosec */
	ActivityCreditLineTxnPaid = "credit_line_txn_paid"
	/* #nosec */
	ActivityCreditLineTxnSplitted = "credit_line_txn_splitted"
	/* #nosec */
	ActivityCreditLineQueryRaised = "credit_line_txn_query_raised"
	/* #nosec */
	ActivityCreditLineQueryResolved = "credit_line_txn_query_resolved"
	/* #nosec */
	ActivityCreditLineTxnMoveToProcessing     = "credit_line_txn_move_to_processing"
	ActivityCreditLineTxnBoostRequestRejected = "credit_line_boost_request_rejected"
	ActivityCreditLineTxnBoostRequestApproved = "credit_line_boost_request_approved"
	/* #nosec */
	ActivityCreditLineTxnMerged = "credit_line_txn_merged"
	/* #nosec */
	ActivityCreditLineTxnReversed    = "credit_line_txn_reversed"
	ActivityCreditLineDetailsUpdated = "credit_line_details_updated"

	// credit line txn refund reversal activities
	ActivityRefundCreated        = "refund_created"
	ActivityRefundCreationFailed = "refund_creation_failed"
	ActivityRefundSuccess        = "refund_success"
	ActivityRefundFailed         = "refund_failed"

	// repayment related actiivities
	ActivityRepaymentInitiated  = "manual_repayment_initiated"
	ActivityRepaymentSuccessful = "repayment_success"
	ActivityRepaymentFailed     = "repayment_failed"

	// credit line txn reversal activities
	ActivityReverseSuccess = "reversal_success"
	ActivityReverseFailure = "reversal_failure"

	ActivityPaymentReceived     = "payment_received"
	ActivityEMIPaid             = "emi_paid"
	ActivityEMICollect          = "emi_collect"
	ActivityEMIPaymentInitiated = "emi_payment_initiated"

	// Activities related to tags
	ActivityTagApplied = "tag_applied"
	ActivityTagRemoved = "tag_removed"

	ActivityLateFeeAdded   = "late_fee_added"
	ActivityLateFeeUpdated = "late_fee_updated"
	ActivityLateFeeWaived  = "late_fee_waived"
	ActivityEMIDue7Days    = "emi_due_in_7_days"
	ActivityEMIDue3Days    = "emi_due_in_3_days"
	ActivityEMIDueTomorrow = "emi_due_in_tomorrow"
	ActivityEMIOverdue     = "emi_overdue"

	ActivityWebLinkGenerated              = "web_link_generated"
	ActivityThirdPartyLinkGenerated       = "third_party_link_generated"
	ActivityLenderThirdPartyLinkGenerated = "lender_third_party_link_generated"
	ActivityENachLinkGenerated            = "enach_link_generated"
	ActivityAgreementRegenerateRequested  = "agreement_regenerate_requested"
	ActivityNACHCancelled                 = "nach_cancelled"
	ActivityEKYCCancelled                 = "ekyc_cancelled"
	ActivityCreditPolicyRerun             = "credit_policy_rerun"
	ActivityCreditBureauRefetched         = "bureau_refetched"

	ActivityAssistedJourneySubmitted       = "assisted_journey_submitted"
	ActivityAssistedJourneyApproved        = "assisted_journey_approved"
	ActivityAssistedJourneyActionRequested = "assisted_journey_action_requested"
	ActivityBureauDoesNotRecognizePhone    = "user_does_not_recognize_bureau_phone"

	ActivityDeviceConnectFetched = "device_connect_fetched"
	ActivityLoanUTRUpdated       = "loan_utr_updated"
	/* #nosec */
	ActivityCreditLineTxnUTRUpdated = "credit_line_txn_utr_updated"

	ActivityDisqualifyReverted = "disqualify_reverted"
	ActivityRejectReverted     = "reject_reverted"

	ActivityDualName                = "added_dual_name_agreement"
	ActivityDualDOB                 = "added_dual_dob_agreement"
	ActivityDualNameAndDOB          = "added_dual_name_dob_agreement"
	ActivityNewApplicationInitiated = "new_application_initiated"
	ActivityJourneyReset            = "journey_reset"

	ActivityRepeatLoanJourneyInitiated        = "repeat_loan_journey_initiated"
	ActivityRepeatLoanRedirectedToMultiOffers = "repeat_loan_redirected_to_multi_offers"
	ActivityRepeatLoanSetReminder             = "repeat_loan_set_reminder"

	ActivityCreditLineClosed = "credit_line_closed"
	ActivityMakeCall         = "make_call_completed"

	ActivityOfferGenerationInitiated      = "offer_generation_initiated"
	ActivityOfferGenerated                = "offer_generated"
	ActivityBankingOfferGenerated         = "banking_offer_generated"
	ActivityOfferGenerationFailed         = "offer_generation_failed"
	ActivityFinalOfferGenerationInitiated = "offer_final_generation_initiated"
	ActivityFinalOfferGenerated           = "offer_final_generated"
	ActivityFinalOfferGenerationFailed    = "offer_final_generation_failed"
	ActivityOfferAcceptanceInitialized    = "offer_acceptance_initiated"
	ActivityOfferAccepted                 = "offer_accepted"
	ActivityOfferExpired                  = "offer_expired"
	ActivityNoOfferGenerated              = "no_offer_generated"
	ActivityDeleteRequested               = "delete_requested"
	ActivityDeleteRequestRevoked          = "delete_request_revoked"

	// Insurance activities
	ActivityInsuranceRequested = "insurance_requested"
	ActivityInsuranceCreated   = "insurance_created"
	ActivityInsuranceFailed    = "insurance_failed"
	ActivityInsuranceUpdated   = "insurance_updated"
	ActivityInsurancePending   = "insurance_details_pending"

	ActivityUserUnderReview = "under_review"

	ActivityOverdraftPaymentReceived = "overdraft_payment_received"
	ActivityOverdraftBillGenerated   = "overdraft_bill_generated"

	ActivityDigilockerRequested = "digilocker_requested"
	ActivityDigilockerSuccess   = "digilocker_success"
	ActivityDigilockerFailed    = "digilocker_failed"

	ActivityExcessFundRefunded = "excess_fund_refunded"
	ActivityExcessFundSettled  = "excess_fund_settled"

	// KYC Service activities

	ActivityKYCServiceInitiated = "kyc_inititated"
	ActivityKYCSelection        = "kyc_selection"

	// Additional docs activities
	ActivityDocumentsUpload             = "documents_uploaded"
	ActivityDocumentDelete              = "document_deleted"
	ActivityNoteAdd                     = "note_added"
	ActivityAgreementSentToLender       = "agreement_sent_to_lender"
	ActivityDocumentVerificationPending = "document_verification_pending"

	ActivityLenderAPIFailed    = "lender_api_failed"
	ActivityLeadPushedToLender = "lead_pushed_to_lender"
	ActivityDocsPushedToLender = "docs_pushed_to_lender"

	ActivityPolicyRetriggerFailed     = "policy_retrigger_failed"
	ActivityPolicyRetriggerGSTFailed  = "policy_retrigger_gst_failed"
	ActivityPolicyRetriggerBankFailed = "policy_retrigger_bank_failed"

	ActivityCartCreationSuccess         = "cart_creation_success"
	ActivityCartCreationFailed          = "cart_creation_failed"
	ActivitytOfferSelected              = "offer_selected"
	ActivitytOfferDeSelected            = "offer_deselected"
	ActivityCKYCDataFetchSuccess        = "ckyc_data_fetch_success"
	ActivityEKYCRedirected              = "redirected_ekyc_vkyc"
	ActivityKYCDataFetchFailure         = "kyc_data_fetch_failure"
	ActivityCKYCDataConfirmationSuccess = "ckyc_data_confirmation_success"
	ActivityCKYCDataConfirmationFailed  = "ckyc_data_confirmation_failed"
	ActivityHardOfferFetchSuccess       = "hard_offer_fetch_success"
	ActiviytHardOfferFetchFailed        = "hard_offer_fetch_failed"
	ActivityHardOfferAcceptanceSuccess  = "hard_offer_acceptance_success"
	ActivityHardOfferAcceptanceFailed   = "hard_offer_acceptance_failed"
	ActivityLivenessCompleted           = "liveness_completed"
	ActivityPaymentRevereted            = "payment_reverted"
	ActivityApplicationCreated          = "application_created_success"

	ActivityKFSConsentGiven       = "kfs_consent_given"
	ActivityKfsAgreementStarted   = "kfs_agreement_started"
	ActivityKfsAgreementCompleted = "kfs_agreement_completed"
	ActivityEKYCStarted           = "ekyc_redirected"
	ActivityEKYCSuccess           = "ekyc_success"
	ActivityENachCallback         = "enach_callback_received"
	ActivityEKYCFailed            = "ekyc_failed"

	ActivityAccountAggregatorCompleted = "account_aggregator_completed"
	ActivityAccountAggregatorFailed    = "account_aggregator_failed"
	ActivityAccountAggregatorStarted   = "account_aggregator_started"

	ActivityAgentReassigned      = "agent_reassigned"
	ActivityLoanClosureRequested = "loan_closure_requested"

	ActivityRemoveInsuranceRequested = "remove_insurance_requested"
	ActivityRemoveInsuranceFailed    = "remove_insurance_failed"
	ActivityRemoveInsuranceSuccess   = "remove_insurance_success"

	ActivityUserDropOff           = "user_drop_off"
	ActivityOTPVerified           = "otp_verified"
	ActivityOTPGenerated          = "otp_generated"
	ActivityOTPGenerationFailed   = "otp_generation_failed"
	ActivityOTPVerificationFailed = "otp_verification_failed"
	ActivityOTPAttemptsExhausted  = "otp_attempts_exhausted"
	ActivityOTPResend             = "otp_resend"

	ActivityUserModuleDeleted        = "user_module_mapping_deleted"
	ActivityBusinessAddressConfirmed = "business_address_confirmed"

	ActivityUANAdded              = "uan_added"
	ActivityUANVerified           = "uan_verified"
	ActivityUANVerificationFailed = "uan_verification_failed"

	ActivityCreditWorkflowApprovedTriggered = "credit_workflow_approved_triggered"
	ActivityCreditWorkflowRejectedTriggered = "credit_workflow_rejected_triggered"
	ActivityKYCWorkflowApprovedTriggered    = "kyc_workflow_approved_triggered"
	ActivityKYCWorkflowRejectedTriggered    = "kyc_workflow_rejected_triggered"

	ActivityBoosterSkipped          = "booster_skipped"
	ActivityBoosterSelected         = "booster_selected"
	ActivityKYCWorkflowTriggered    = "kyc_workflow_triggered"
	ActivityCreditWorkflowTriggered = "credit_workflow_triggered"

	ActivityUserDashboardTriggered  = "user_dashboard_triggered"
	ActivityRedirectedToMultiOffers = "redirected_to_multi_offers"
	ActivityUserWaitListed          = "user_waitlisted"

	// Auxiliary Activities - Not shown to the user anywhere used for Analytics
	// and logged in aux_activity_log table
	AuxiliaryActivityTokenIssued                  = "user_token_issued"
	AuxiliaryActivityRetriggerIIFLDecision        = "retrigger_iifl_decision"
	AuxiliaryActivityRetriggerIIFL                = "retrigger_iifl"
	AuxiliaryActivityRetriggerIIFLCIBIL           = "retrigger_iifl_cibil"
	AuxiliaryActivityRetriggerIIFLKYC             = "retrigger_iifl_kyc"
	AuxiliaryActivityRetriggerIIFLAgreement       = "retrigger_iifl_agreement"
	AuxiliaryActivityRetriggerPolicy              = "retrigger_policy"
	AuxiliaryActivityRetriggerKYCEngineDG         = "retrigger_kycengine_digilocker"
	AuxiliaryActivityOTPFetched                   = "otp_fetched"
	AuxiliaryActivityRetriggerVAN                 = "retrigger_virtual_account_number"
	AuxiliaryActivityRetriggerCKYC                = "retrigger_ckyc"
	AuxiliaryActivityRetriggerIIFLCLMerchantLogin = "retrigger_iifl_cl_merchant_login" //TODO: remove this after retrigger cl merchant login is removed``
	AuxiliaryActivityExcessFundRefund             = "mark_od_excess_fund_refund"
	AuxiliaryActivityUpdateODLedger               = "update_od_ledger"
	AuxiliaryActivityUpdateAvailableLimit         = "update_cl_available_limit"
	AuxiliaryActivityUpdateODBills                = "update_od_bills"

	AuxiliaryActivityTriggerExternalAPI = "trigger_external_api"

	AuxiliaryActivityRetriggerPFL = "retrigger_pfl"

	AuxiliaryFeatureFlagRemoved = "feature_flag_removed"

	ActivityVKYCSuccess    = "vkyc_success"
	ActivityVKYCInitiated  = "vkyc_initiated"
	ActivityVKYCFailed     = "vkyc_failed"
	ActivityVKYCRedirected = "vkyc_redirected"
	ActivityBankKycStarted = "bank_kyc_started"
	ActivityVKYCDone       = "vkyc_done"
	ActivityUserRedirected = "user_redirected"

	ActivityAdditionalInfoStarted            = "additional_info_started"
	ActivityRelationshipDeclarationStarted   = "relationship_declaration_started"
	ActivityAdditionalInfoCompleted          = "additional_info_completed"
	ActivityRelationshipDeclarationCompleted = "relationship_declaration_completed"
	ActivityReviewConfirmStarted             = "review_confirm_started"
	ActivityReviewConfirmCompleted           = "review_confirm_completed"
	ActivitySendKFSCompleted                 = "sendkfs_completed"
	ActivityDashboardLoad                    = "dashboard_load"
	ActivityCalculatorLoad                   = "calculator_load"
	ActivityLoanEsignedTcap                  = "loan_esigned_tcap"
	// Income Verification Activities
	ActivityIncomeVerified               = "income_verified"
	ActivityIncomeVerificationInProgress = "income_verification_inprogress"
	ActivityIncomeVerificationFailed     = "income_verifation_failed"

	ActivityLoanApplicationReview = "loan_application_review"

	ActivityLoanApplicationSubmitted   = "loan_application_submitted"
	ActivityLoanApplicationReSubmitted = "loan_application_resubmitted"
	ActivityLoanSanctionPending        = "loan_sanction_pending"
	ActivityOpsReviewPendingLTFS       = "pending_ops_review"

	// Co-Applicant
	ActivityCoApplicantCreated      = "co_applicant_created"
	ActivityCoApplicantUpdated      = "co_applicant_updated"
	ActivityCoApplicantDeleted      = "co_applicant_deleted"
	ActivityCoApplicantQualified    = "co_applicant_qualified"
	ActivityCoApplicantDisqualified = "co_applicant_disqualified"
	ActivityCoApplicantKYCSubmitted = "co_applicant_kyc_submitted"
	ActivityCoApplicantKYCCompleted = "co_applicant_kyc_completed"
	ActivityCoApplicantKYCUpdated   = "co_applicant_kyc_updated"
	ActivityEligibleForOtherOffers  = "eligible_for_other_offers"

	ActivityGSTOTPValidationFailed = "gst_otp_validation_failed"
	ActivityGSTWaitStatusReverted  = "gst_wait_status_reverted"

	ActivityMovedToMarketplace = "moved_to_marketplace"

	ActivityOpsWorkflowApproved        = "ops_workflow_approved"
	ActivityCheckLoanBreakdown         = "check_loan_breakdown"
	ActivityAgreementAccepted          = "agreement_accepted"
	ActivityOpsApproved                = "ops_approved"
	ActivityEmploymentDetailsSubmitted = "employment_details_submitted"
	ActivityBasicDetailsSubmitted      = "basic_details_completed"
	ActivityLenderRuleEngineSuccess    = "lender_rule_engine_success"
	ActivityLenderRuleEngineInProgress = "lender_rule_engine_in_progress"
	ActivityUserBringBack              = "user_bring_back"
	ActivitySanctionSuccess            = "sanction_success"

	ActivityProfessionalInfoSubmitted = "professional_info_submitted"
	ActivityProfessionalInfoVerified  = "professional_info_verified"

	ActivityUdyamApplicationCreated             = "udyam_application_created"
	ActivityUdyamOTPSentToUser                  = "udyam_otp_sent_to_user"
	ActivityUdyamOTPSentToVendorForVerification = "otp_sent_to_vendor_for_verification"
	ActivityUdyamDetailsFetched                 = "udyam_details_fetched"
	ActivityAuthenticationRequired              = "authentication_required"
	ActivityPaymentAlert                        = "payment_alert"
	ActivityCoApplicantDocumentsUploaded        = "co_applicant_documents_uploaded"
	ActivityApplicantDocumentUploaded           = "applicant_documents_uploaded"
	ActivityCourseInfoUpdated                   = "course_info_updated"
	ActivityCoApplicantFinancialInfoUpdated     = "co_applicant_financial_info_updated"
	ActivityApplicantCreated                    = "applicant_created"
	// application_submitted
	ActivityApplicationSubmitted = "application_submitted"
	// sanction_letter_generated
	ActivitySanctionLetterGenerated = "sanction_letter_generated"
	// payment_initiated
	ActivityPaymentInitiated = "payment_initiated"
	// payment_completed
	ActivityPaymentCompleted       = "payment_completed"
	ActivityLoanApplicationUpdated = "loan_application_updated"
)

var KYCCustomerEvents = []string{ActivityDigilockerRequested, ActivityKYCSubmitted}

var GenderNumToStr = map[int]string{
	1:  "Male",
	0:  "Female",
	2:  "Third gender",
	-1: "",
}

var GenderStrToNum = map[string]int{
	"Male":         1,
	"MALE":         1,
	"M":            1,
	"Female":       0,
	"FEMALE":       0,
	"F":            0,
	"Third gender": 2,
	"Other":        2,
	"OTHER":        2,
}

var TDLFailureAlertRecipientEmailIDs = []string{"<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"}
var TDLFailureAlertRecipientNames = []string{"BNPL_API_Monitoring", "Saigaurav", "Mohanish", "Vasu", "Muralikrishna", "Akash"}

var ResidenceNumToStr = map[int]string{
	0: "",
	1: "Owned",
	2: "Rented",
	3: "Owned by Self or Spouse",
	4: "Owned by Parents",
}

var ResidenceStrToNum = map[string]int{
	"Owned":                   1,
	"Rented":                  2,
	"Owned by Self or Spouse": 3,
	"Owned by Parents":        4,
}

var ResidenceENUMToLendingStr = map[string]string{
	"OWNED":                "Owned",
	"RENTED":               "Rented",
	"SELF_OR_SPOUSE_OWNED": "Owned by Self or Spouse",
	"PARENTS_OWNED":        "Owned by Parents",
}

var MaritalNumToStr = map[int]string{
	0: "",
	1: "Married",
	2: "Unmarried",
	3: "Divorced",
	4: "Widow",
	5: "Separated",
}

var MaritalStrToNum = map[string]int{
	"Married":   1,
	"Unmarried": 2,
	"Single":    2,
	"Divorced":  3,
	"Widow":     4,
	"Separated": 5,
	"Other":     6,
}

var YesNumStrToNum = map[int]string{
	1: "Yes",
	0: "No",
}

var RefRelationNumToStr = map[int]string{
	0: "",
	1: "Father",
	2: "Mother",
	3: "Spouse",
	4: "Sister",
	5: "Brother",
	6: "Uncle",
	7: "Aunt",
	8: "Coworker",
	9: "Friend",
}

var RefRelationStrToNum = map[string]int{
	"Father":   1,
	"Mother":   2,
	"Spouse":   3,
	"Sister":   4,
	"Brother":  5,
	"Uncle":    6,
	"Aunt":     7,
	"Coworker": 8,
	"Friend":   9,
}

// constants for underwriting
const (
	UnderwritingDecisionCantDecide = "cant_decide"
	UnderwritingDecisionPass       = "pass"
	UnderwritingDecisionReject     = "reject"
)

// constants for GST
const (
	GSTStatusAdded      = "added"
	GSTStatusInProgress = "in_progress"
	GSTStatusFailed     = "failed"
	GSTStatusCompleted  = "completed"
)

// journey status constants for GST
const (
	GSTJourneyStatusSelected = "selected"
	GSTJourneyStatusOTPFlow  = "otp_flow"
)

// Loan Status functions and maps
// else if status == 2 && kycStatus == 7 {
// 	return "ADDRESS_VERIFICATION_QUEUE"

// GetLoanStatusText returns the loan status text based on loan status and KYC status
func GetLoanStatusText(status int, kycStatus int) string {

	switch {
	case status == 0:
		return LoanStatusTextCancelled
	case status == 1:
		return LoanStatusTextFreshLoan
	case status == 2 && kycStatus == -1:
		return LoanStatusTextDetailsSubmitted
	case status == 2 && kycStatus == 0:
		return LoanStatusTextDetailsSubmitted
	case status == 2 && kycStatus == 1:
		return LoanStatusTextKYCProcessing
	case status == 2 && kycStatus == 2:
		return LoanStatusTextKYCSuccess
	case status == 2 && kycStatus == 3:
		return LoanStatusTextKYCRejected
	case status == 2 && kycStatus == 7:
		return LoanStatusTextUnderReview
	case status == 3:
		return LoanStatusTextLoanRejected
	case status == 4 && kycStatus == 2:
		return LoanStatusTextLoanApproved
	case status == 4 && kycStatus == 5:
		return LoanStatusTextBankFailed
	case status == 4 && kycStatus == 6:
		return LoanStatusTextBankProcessing
	case status == 5 && kycStatus == 4:
		return LoanStatusTextBankAdded
	case status == 5 && kycStatus == 8:
		return LoanStatusPreAgreement
	case status == 6:
		return LoanStatusTextSignAgreement
	case status == 7:
		return LoanStatusTextDisbursed
	case status == 8:
		return LoanStatusTextClosed
	default:
		return ""
	}
}

// Define constants for loan status texts
const (
	LoanStatusTextDisbursed        = "DISBURSED"
	LoanStatusTextLoanRejected     = "LOAN_REJECTED"
	LoanStatusTextCancelled        = "CANCELLED"
	LoanStatusTextFreshLoan        = "FRESH_LOAN"
	LoanStatusTextDetailsSubmitted = "LOAN_DETAILS_SUBMITTED"
	LoanStatusTextKYCProcessing    = "KYC_PROCESSING"
	LoanStatusTextKYCSuccess       = "KYC_SUCCESS"
	LoanStatusTextKYCRejected      = "KYC_REJECTED"
	LoanStatusTextUnderReview      = "UNDER_REVIEW"
	LoanStatusTextLoanApproved     = "LOAN_APPROVED"
	LoanStatusTextBankFailed       = "BANK_FAILED"
	LoanStatusTextBankProcessing   = "BANK_PROCESSING"
	LoanStatusTextBankAdded        = "BANK_ADDED"
	LoanStatusPreAgreement         = "PRE_AGREEMENT"
	LoanStatusTextSignAgreement    = "SIGN_AGREEMENT"
	LoanStatusTextClosed           = "CLOSED"
)

var LoanStatusToActivityEventType = map[string]string{
	LoanStatusTextDisbursed:    ActivityLoanDisbursed,
	LoanStatusTextLoanRejected: ActivityLoanRejected,
	LoanStatusTextCancelled:    ActivityLoanCancelled,
}

type loanStatusStruct struct {
	Status    int
	KYCStatus int
}

var LoanStatusStrToNum = map[string]loanStatusStruct{
	"CANCELLED":              {0, -1},
	"FRESH_LOAN":             {1, -1},
	"LOAN_DETAILS_SUBMITTED": {2, 0},
	"KYC_PROCESSING":         {2, 1},
	"KYC_SUCCESS":            {2, 2},
	// "ADDRESS_VERIFICATION_QUEUE": {2, 7},
	"KYC_REJECTED":    {2, 3},
	"UNDER_REVIEW":    {2, 7},
	"LOAN_REJECTED":   {3, -1},
	"LOAN_APPROVED":   {4, 2},
	"BANK_FAILED":     {4, 5},
	"BANK_PROCESSING": {4, 6},
	"BANK_ADDED":      {5, 4},
	"PRE_AGREEMENT":   {5, 8},
	"SIGN_AGREEMENT":  {6, -1},
	"DISBURSED":       {7, -1},
	"CLOSED":          {8, -1},
}

// constants for loan status
const (
	LoanStatusCancelledMessage    = "loan status is cancelled"
	LoanStatusClosedMessage       = "loan status is closed"
	LoanStatusDisbursedMessage    = "loan status is disbursed"
	LoanStatusLoanRejectedMessage = "loan status is rejected"
	LoanStatusESignMessage        = "customer has already signed the agreement"
)

const LenderPolicyRejectedReason = "rejected based on lender policy"

const (
	MethodSimpleInterest  = "si"
	MethodReducingBalance = "rb"
	MethodDailyReducing   = "dr"
)

func GetEMIMethod(method string) string {
	switch method {
	case MethodSimpleInterest:
		return "flat_rate"
	case MethodReducingBalance:
		return "reducing_balance"
	case MethodDailyReducing:
		return "daily_reducing"
	}
	return ""
}

func GetSampleRandomExperianScore() string {
	min := 735
	max := 746
	// set seed
	rand.New(rand.NewSource(time.Now().UnixNano()))
	// generate random number and print on console
	return fmt.Sprintf("%d", rand.Intn(max-min)+min)
}

var SampleExperianJSON = `
{"CAPS":{"Text":"","CAPSSummary":{"Text":"","CAPSLast7Days":"0","CAPSLast30Days":"0","CAPSLast90Days":"0","CAPSLast180Days":"0"}},"Text":"","SCORE":{"Text":"","BureauScore":"750","BureauScoreConfidLevel":"H"},"Header":{"Text":"","ReportDate":"********","ReportTime":"152648","SystemCode":"0","MessageText":""},"XMLName":{"Local":"INProfileResponse","Space":""},"CAISAccount":{"Text":"","CAISSummary":{"Text":"","CreditAccount":{"Text":"","CreditAccountTotal":"2","CreditAccountActive":"1","CreditAccountClosed":"1","CreditAccountDefault":"0","CADSuitFiledCurrentBalance":"0"},"TotalOutstandingBalance":{"Text":"","OutstandingBalanceAll":"100000","OutstandingBalanceSecured":"70000","OutstandingBalanceUnSecured":"30000","OutstandingBalanceSecuredPercentage":"70","OutstandingBalanceUnSecuredPercentage":"30"}},"CAISAccountDETAILS":[{"Text":"","Income":"","OpenDate":"********","DateClosed":"","AccountType":"10","CurrencyCode":"INR","DateReported":"********","AccountNumber":"XXXXXXX0531","AccountStatus":"11","AmountPastDue":"0","PaymentRating":"0","PortfolioType":"R","TermsDuration":"","CurrentBalance":"30000","DateOfAddition":"********","OccupationCode":"","RateOfInterest":"","SpecialComment":"","SubscriberName":"American Express Banking Corp","TermsFrequency":"","IncomeIndicator":"","RepaymentTenure":"0","ConsumerComments":"","SettlementAmount":"","TypeOfCollateral":"","CAISHolderDetails":[{"Text":"","Alias":"","GenderCode":"1","DateOfBirth":"********","IncomeTAXPAN":"XXXXXXXXX","VoterIDNumber":"","PassportNumber":"","SurnameNonNormalized":"SRIJAN","FirstNameNonNormalized":"NAGAR","MiddleName1NonNormalized":"","MiddleName2NonNormalized":"","MiddleName3NonNormalized":""}],"CreditLimitAmount":"200000","DateOfLastPayment":"","DefaultStatusDate":"","ValueOfCollateral":"","CAISAccountHistory":[{"Text":"","Year":"2020","Month":"04","DaysPastDue":"0","AssetClassification":"?"}],"SubscriberComments":"","WriteOffStatusDate":"","WrittenOffAmtTotal":"","CAISHolderIDDetails":{"Text":"","EMailId":"","IncomeTAXPAN":"XXXXXXXXX","PANIssueDate":"","VoterIDNumber":"","PassportNumber":"","RationCardNumber":"","VoterIDIssueDate":"","PANExpirationDate":"","PassportIssueDate":"","UniversalIDNumber":"","DriverLicenseNumber":"","RationCardIssueDate":"","UniversalIDIssueDate":"","VoterIDExpirationDate":"","DriverLicenseIssueDate":"","PassportExpirationDate":"","RationCardExpirationDate":"","UniversalIDExpirationDate":"","DriverLicenseExpirationDate":""},"PromotionalRateFlag":"","IdentificationNumber":"","LitigationStatusDate":"","AccountHoldertypeCode":"1","PaymentHistoryProfile":"N","CAISHolderPhoneDetails":[{"Text":"","EMailId":"<EMAIL>","FaxNumber":"","TelephoneType":"","TelephoneNumber":"**********","TelephoneExtension":"","MobileTelephoneNumber":""}],"DateOfFirstDelinquency":"","SuitFiledWilfulDefault":"","WrittenOffAmtPrincipal":"","OriginalChargeOffAmount":"","ValueOfCreditsLastMonth":"","WrittenOffSettledStatus":"","CAISHolderAddressDetails":[{"Text":"","CityNonNormalized":"PUNE","StateNonNormalized":"27","CountryCodeNonNormalized":"IB","ResidenceCodeNonNormalized":"","ZIPPostalCodeNonNormalized":"412207","AddressIndicatorNonNormalized":"","FifthLineOfAddressNonNormalized":"","FirstLineOfAddressNonNormalized":"S-1 032","ThirdLineOfAddressNonNormalized":"NAGAR RD","SecondLineOfAddressNonNormalized":"HARI OM APT"}],"IncomeFrequencyIndicator":"","ScheduledMonthlyPaymentAmount":"","HighestCreditOrOriginalLoanAmount":"200000","SuitFiledWillfulDefaultWrittenOffStatus":""},{"Text":"","Income":"","OpenDate":"********","DateClosed":"","AccountType":"04","CurrencyCode":"INR","DateReported":"********","AccountNumber":"XXXXXXX0535","AccountStatus":"71","AmountPastDue":"400","PaymentRating":"1","PortfolioType":"I","TermsDuration":"","CurrentBalance":"70000","DateOfAddition":"********","OccupationCode":"","RateOfInterest":"","SpecialComment":"","SubscriberName":"American Express Banking Corp","TermsFrequency":"","IncomeIndicator":"","RepaymentTenure":"0","ConsumerComments":"","SettlementAmount":"","TypeOfCollateral":"","CAISHolderDetails":[{"Text":"","Alias":"","GenderCode":"1","DateOfBirth":"********","IncomeTAXPAN":"XXXXXXXXX","VoterIDNumber":"","PassportNumber":"","SurnameNonNormalized":"SRIJAN","FirstNameNonNormalized":"NAGAR","MiddleName1NonNormalized":"","MiddleName2NonNormalized":"","MiddleName3NonNormalized":""}],"CreditLimitAmount":"","DateOfLastPayment":"","DefaultStatusDate":"","ValueOfCollateral":"","CAISAccountHistory":[{"Text":"","Year":"2020","Month":"04","DaysPastDue":"0","AssetClassification":"?"}],"SubscriberComments":"","WriteOffStatusDate":"","WrittenOffAmtTotal":"","CAISHolderIDDetails":{"Text":"","EMailId":"","IncomeTAXPAN":"XXXXXXXXX","PANIssueDate":"","VoterIDNumber":"","PassportNumber":"","RationCardNumber":"","VoterIDIssueDate":"","PANExpirationDate":"","PassportIssueDate":"","UniversalIDNumber":"","DriverLicenseNumber":"","RationCardIssueDate":"","UniversalIDIssueDate":"","VoterIDExpirationDate":"","DriverLicenseIssueDate":"","PassportExpirationDate":"","RationCardExpirationDate":"","UniversalIDExpirationDate":"","DriverLicenseExpirationDate":""},"PromotionalRateFlag":"","IdentificationNumber":"","LitigationStatusDate":"","AccountHoldertypeCode":"1","PaymentHistoryProfile":"N","CAISHolderPhoneDetails":[{"Text":"","EMailId":"<EMAIL>","FaxNumber":"","TelephoneType":"","TelephoneNumber":"**********","TelephoneExtension":"","MobileTelephoneNumber":""}],"DateOfFirstDelinquency":"","SuitFiledWilfulDefault":"","WrittenOffAmtPrincipal":"","OriginalChargeOffAmount":"","ValueOfCreditsLastMonth":"","WrittenOffSettledStatus":"","CAISHolderAddressDetails":[{"Text":"","CityNonNormalized":"PUNE","StateNonNormalized":"27","CountryCodeNonNormalized":"IB","ResidenceCodeNonNormalized":"","ZIPPostalCodeNonNormalized":"412207","AddressIndicatorNonNormalized":"","FifthLineOfAddressNonNormalized":"","FirstLineOfAddressNonNormalized":"S-1 032","ThirdLineOfAddressNonNormalized":"NAGAR RD","SecondLineOfAddressNonNormalized":"HARI OM APT"}],"IncomeFrequencyIndicator":"","ScheduledMonthlyPaymentAmount":"","HighestCreditOrOriginalLoanAmount":"200000","SuitFiledWillfulDefaultWrittenOffStatus":""}]},"MatchResult":{"Text":"","ExactMatch":"Y"},"UserMessage":{"Text":"","UserMessageText":"Normal Response"},"NonCreditCAPS":{"Text":"","NonCreditCAPSSummary":{"Text":"","NonCreditCAPSLast7Days":"0","NonCreditCAPSLast30Days":"0","NonCreditCAPSLast90Days":"0","NonCreditCAPSLast180Days":"0"}},"TotalCAPSSummary":{"Text":"","TotalCAPSLast7Days":"0","TotalCAPSLast30Days":"0","TotalCAPSLast90Days":"0","TotalCAPSLast180Days":"0"},"CurrentApplication":{"Text":"","CurrentApplicationDetails":{"Text":"","EnquiryReason":"6","AmountFinanced":"0","FinancePurpose":"","CurrentOtherDetails":{"Text":"","Income":"0","MaritalStatus":"","EmploymentStatus":"","TimeWithEmployer":"","NumberOfMajorCreditCardHeld":""},"DurationOfAgreement":"0","CurrentApplicantDetails":{"Text":"","EMailId":"<EMAIL>","LastName":"surName","FirstName":"Srijan Nagar","GenderCode":"1","MiddleName1":"","MiddleName2":"","MiddleName3":"","IncomeTaxPan":"XXXXXXXXX","PANIssueDate":"","TelephoneType":"","PassportNumber":"","RationCardNumber":"","VoterIDIssueDate":"","MobilePhoneNumber":"**********","PANExpirationDate":"","PassportIssueDate":"","UniversalIDNumber":"","TelephoneExtension":"","VoterSIdentityCard":"","DriverLicenseNumber":"","RationCardIssueDate":"","DateOfBirthApplicant":"********","UniversalIDIssueDate":"","VoterIDExpirationDate":"","DriverLicenseIssueDate":"","PassportExpirationDate":"","RationCardExpirationDate":"","UniversalIDExpirationDate":"","DriverLicenseExpirationDate":"","TelephoneNumberApplicant1st":""},"CurrentApplicantAddressDetails":{"City":"Pune","Text":"","State":"27","PINCode":"412207","Landmark":"","CountryCode":"IB","BldgNoSocietyName":"","FlatNoPlotNoHouseNo":"123","RoadNoNameAreaLocality":""},"CurrentApplicantAdditionalAddressDetails":""}},"CreditProfileHeader":{"Text":"","Version":"V2.4","ReportDate":"********","ReportTime":"152648","Subscriber":"","ReportNumber":"1600855008605","SubscriberName":"Bureau Disclosure Report with Credit Caps","EnquiryUsername":"bureau_report_1__finbox_fm"}}
`

// Loan Type
const (
	LoanTypeEducationLoan   = "education_loan"
	LoanTypePersonalLoan    = "personal_loan"
	LoanTypeBusinessLoan    = "business_loan"
	LoanTypeCreditLine      = "credit_line"
	LoanTypeOverDraft       = "overdraft"
	LoanTypeBusinessLoanEmi = "business_loan_emi"
	LoanTypeBusinessLoanEdi = "business_loan_edi"
)

// GetLoanTypeString returns a human-readable string for each loan type
func GetLoanTypeString(loanType string) string {
	switch loanType {
	case LoanTypeEducationLoan:
		return "Education Loan"
	case LoanTypePersonalLoan:
		return "Personal Loan"
	case LoanTypeBusinessLoan:
		return "Business Loan"
	case LoanTypeCreditLine:
		return "Credit Line"
	case LoanTypeOverDraft:
		return "Overdraft"
	case LoanTypeBusinessLoanEmi:
		return "Business Loan EMI"
	case LoanTypeBusinessLoanEdi:
		return "Business Loan EDI"
	default:
		return "Unknown Loan Type"
	}
}

var ValidLoanTypeForMFLBLenderID = []string{LoanTypeBusinessLoanEmi, LoanTypeBusinessLoanEdi}
var ValidLoanTypeList = []string{LoanTypePersonalLoan, LoanTypeBusinessLoan, LoanTypeCreditLine, LoanTypeOverDraft, LoanTypeBusinessLoanEmi, LoanTypeBusinessLoanEdi}

const (
	BankAccountTypeCurrent = "current"
	BankAccountTypeSavings = "savings"
)

// constants for constitution
const (
	Proprietorship   = "Proprietorship"
	Partnership      = "Partnership"
	PrivateLimited   = "Private Limited"
	Companies        = "Companies"
	PublicLimited    = "Public Limited"
	LimitedLiability = "Limited Liability Partnership"
)

var ConstitutionList = []string{Proprietorship, Partnership, PrivateLimited, PublicLimited, LimitedLiability}

var IncomeRangeList = []string{"₹0 - ₹3 L", "₹3 L - ₹12 L", "₹12 L - ₹30 L", "₹30 L - ₹60 L", "₹60 L - ₹1 C", "₹1 - ₹10 C", "More than ₹10 C"}

var IncomeRangeListNexArc = []string{"Less than ₹ 1 Cr", "₹ 1-5 Cr", "₹ 5-10 Cr", "More than ₹ 10 Cr"}

var NatureOfBusinessList = []string{"Manufacturing", "Trading", "Services"}

const (
	PositivePincodeCheck = 1
	NegativePincodeCheck = 2
)

// these constants point to the check_modes in email_config table which mean:
// 1: Positive email check, email should exist in corresponding list
// 2: Negative email check, email should not exist in corresponding list
const (
	PositiveEmailConfigCheck = 1
	NegativeEmailConfigCheck = 2
)

// const (
// 	LenderPartnerInvited = 1
// 	LenderPartnerJoined  = 2
// )

const (
	RoleTypeFull       = "Full"
	RoleTypeReadOnly   = "Read_only"
	RoleTypeSearchOnly = "Search_only"
)

const (
	PincodeSourceDeclared = "declared"
	PincodeSourceLocation = "location"
)

const (
	BannerTypeApply      = "APPLY"
	BannerTypeRejected   = "REJECTED"
	BannerTypeIncomplete = "INCOMPLETE"
	BannerTypeApproved   = "APPROVED"
	BannerTypeOverdue    = "OVERDUE"
	BannerTypeDue        = "DUE"
	BannerTypeInactive   = "INACTIVE"
	BannerTypeActive     = "ACTIVE"
	BannerTypeCancelled  = "CANCELLED"
	BannerTypeClosed     = "CLOSED"
	BannerTypeReApply    = "REAPPLY"
)

// Insurance status
const (
	InsuranceStatusInquired = 1
	InsuranceStatusUnpaid   = 2
	InsuranceStatusPaid     = 3
	InsuranceStatusRemoved  = 4
	InsuranceStatusFailed   = 5
)

// DefaultBankIcon cdn link
const DefaultBankIcon = "https://finbox-cdn.s3.ap-south-1.amazonaws.com/lending_app/banks/default.png"

// MaskedAccountNumberPrefix contains x repeated 9 times
var MaskedAccountNumberPrefix = strings.Repeat("x", 9)

type FormStruct struct {
	Title    string
	Field    string
	Options  []string
	Regex    string
	Type     string
	Required bool
	Value    string
}

var LoanFormFields = []FormStruct{
	{
		Title:    "Date of Birth",
		Field:    "dob",
		Type:     "date",
		Regex:    "(0?[1-9]|[12][0-9]|3[01])/(0?[1-9]|1[012])/((19|20)\\d\\d)",
		Required: false,
	},
	{
		Title:    "Email",
		Field:    "email",
		Type:     "string",
		Regex:    EmailRegex,
		Required: false,
	},
	{
		Title:    "Firm Name",
		Field:    "firmName",
		Type:     "string",
		Regex:    "^.{5,45}[a-zA-z]+([\\s][a-zA-Z]+)*$", // Min 6 chars, max 45 chars, alphabets and single space between words
		Required: true,
	},
	{
		Title:    "GSTIN",
		Field:    "gstin",
		Type:     "string",
		Regex:    GSTINRegex,
		Required: false,
	},
	{
		Title:    "Name",
		Field:    "name",
		Type:     "string",
		Regex:    "^.{5,45}[a-zA-z]+([\\s][a-zA-Z]+)*$",
		Required: false,
	},
	{
		Title:    "PAN",
		Field:    "pan",
		Type:     "string",
		Regex:    PersonalPANRegex,
		Required: false,
	},
	{
		Title:    "Gender",
		Field:    "gender",
		Type:     "string",
		Required: false,
		Options:  []string{"Male", "Female", "Other"},
	},
	{
		Title:    "Father's Name",
		Field:    "fathersName",
		Type:     "string",
		Required: true,
	},
}

var DummyPennyUser string = "14f9b65e-1a94-46a5-b76f-b8a640537e3e"
var DummyJobUser string = "5c21aa49-85a5-452f-912b-dc788ddfc984"
var DummyExternalServiceLogUser string = "ae3a8d0c-4041-41d0-ab4a-5a80172dcafd"       // this user has entry in users table under testing account source entity
var DummyMuthootExternalServiceLogger string = "327a1fe0-c989-41f7-8ed3-72abbe845951" // this user has entry in users table under muthoots source entity
var DummyABFLExternalServiceLogger string = "fcce2e7a-f7ca-48ff-9eb8-cd28c326706f"    // this user has entry in users table under muthoots source entity
var DummyUserIDBulkPresentation = "6fbb3c67-083d-4774-8bdf-f0919fd82ab2"              // this user has entry in users table under muthoots source entity
var LongRunningJob string = "long_running_job"
var DummyMFLExternalServiceLogger string = "b101e9a5-c3a5-4e7a-85ef-73829d3cff45"

const (
	PlatformDashboardRef = "platform_dashboard"
	LenderDashboardRef   = "lender_dashboard"
	MasterDashboardRef   = "master_dashboard"
)

// leegality constants
const (
	LeegalityCreateESignServiceName    = "esign-leegality-create-request"
	LeegalityDeleteDocESignServiceName = "esign-leegality-delete-document"
	LeegalityDocDetailsServiceName     = "esign-leegality-document-details"

	LeegalityStatusActive   = "ACTIVE"
	LeegalityStatusRejected = "REJECTED"
	LeegalityStatusSigned   = "SIGNED"
	LeegalityStatusFailed   = "FAILED"

	// Real Status Of Leegality
	LeegalityStatusDraft     = "DRAFT"
	LeegaltityStatusSent     = "SENT"
	LeegalityStatusCompleted = "COMPLETED"
	LeegalityStatusExpired   = "EXPIRED"
)

var LeegalityStatusList = []string{LeegalityStatusDraft, LeegaltityStatusSent, LeegalityStatusCompleted, LeegalityStatusExpired}

// TODO: deprecate this once we have the pending screen from PFL for status SENT
var LeegalityStatusListPFL = []string{LeegalityStatusDraft, LeegalityStatusCompleted, LeegalityStatusExpired}

const (
	ESignStatusError   = 0
	ESignStatusSuccess = 1
	ESignStatusActive  = 2
	ESignStatusFailed  = 3
	ESignStatusExpired = 4
	ESignStatusDeleted = 5

	// --- This status used only in code not db -----
	ESignStatusWaitingForOthers = 6
)

const (
	EsignModeMultipleSignURLStatusCompleted = "COMPLETED"
	EsignModeMultipleSignURLStatusPending   = "PENDING"
)

const (
	EsignAttemptVersion1 = 1
	EsignAttemptVersion2 = 2
)

const (
	ESignModeOTP             = "otp"
	ESignModeSignURL         = "sign_url"
	EsignModeMultipleSignURL = "multiple_sign_url"
)

const (
	ESignTypeEmbeddedSigning      = "EMBEDDED_SIGNING"
	ESignTypeSigningWithoutEstamp = "SIGNING_WITHOUT_ESTAMP"
)

const RequestStatusCompleted = 1
const RequestStatusFailed = 0

const QualificationStartPrefix = "qual_started_"
const QualificationAttemptPrefix = "qual_attempt_"
const RetriggerQualificationStartPrefix = "retrigger_qual_started_"

const (
	WeekInMinutes    = 10080
	ThreeDaysInMilli = 259200000
	MonthInMilli     = 2629800000
	YearInMilli      = 31556926000
)

// time Type
const (
	TimeTypeDay   = 1
	TimeTypeMonth = 2
	TimeTypeYear  = 3
)

// TimeTypeStrToNum maps the time type with value
// if empty string is passed then value of month is returned
var TimeTypeStrToNum = map[string]int{
	"":        TimeTypeMonth,
	"DAYS":    TimeTypeDay,
	"DAY":     TimeTypeDay,
	"DAILY":   TimeTypeDay,
	"MONTHS":  TimeTypeMonth,
	"MONTH":   TimeTypeMonth,
	"MONTHLY": TimeTypeMonth,
	"YEARS":   TimeTypeYear,
	"YEAR":    TimeTypeYear,
	"YEARLY":  TimeTypeYear,
}

var TenureTypeNumToStr = map[int]string{
	TimeTypeDay:   "DAYS",
	TimeTypeMonth: "MONTHS",
	TimeTypeYear:  "YEARS",
}

const (
	SoftOffer = 1
	HardOffer = 2
)

const (
	OfferSourceLenderAPI = "lender_api"
	OfferSourceFinBox    = "finbox"
)

var OfferTypeMapIntToStr = map[int]string{
	SoftOffer: "SOFT_OFFER",
	HardOffer: "HARD_OFFER",
}

const (
	OfferStatusActive      = 1
	OfferStatusInactive    = 2
	OfferStatusIsAccepted  = 3
	OfferStatusWasAccepted = 4
	OfferStatusExpired     = 5
	OfferStatusSelected    = 6
)

var OfferStatusList = []int{OfferStatusActive, OfferStatusInactive, OfferStatusIsAccepted, OfferStatusWasAccepted, OfferStatusExpired, OfferStatusSelected}

const (
	OfferLimitTypeBNPL = "bnpl"
	OfferLimitTypeEMI  = "emi"
)

var OfferStatusMapIntToStr = map[int]string{
	OfferStatusActive:      "ACTIVE",
	OfferStatusInactive:    "INACTIVE",
	OfferStatusIsAccepted:  "IS_ACCEPTED",
	OfferStatusWasAccepted: "WAS_ACCEPTED",
	OfferStatusExpired:     "EXPIRED",
	OfferStatusSelected:    "SELECTED",
}

var OfferStatusMapStrToInt = map[string]int{
	"ACTIVE":       OfferStatusActive,
	"INACTIVE":     OfferStatusInactive,
	"IS_ACCEPTED":  OfferStatusIsAccepted,
	"WAS_ACCEPTED": OfferStatusWasAccepted,
	"EXPIRED":      OfferStatusExpired,
	"SELECTED":     OfferStatusSelected,
}

const (
	Waiting   = "waiting"
	Completed = "completed"
)

const (
	ProgramPersonalLoan      = "PERSONAL_LOAN"
	ProgramDualLimitPayLater = "DUAL_LIMIT_PAYLATER"
	ProgramBusinessLoan      = "BUSINESS_LOAN"
	ProgramEducationLoan     = "EDUCATION_LOAN"
)

const (
	RepaymentSubModeNACH = "NACH"
	RepaymentSubModeIMPS = "IMPS"
)

const (
	OwnerTypeLender   = "lender"
	OwnerTypeDSA      = "dsa"
	OwnerTypePlatform = "platform"
)

const (
	DSAStatusPending     = 0
	DSAStatusApproved    = 1
	DSAStatusRejected    = 2
	DSAStatusDeactivated = 3
	DSAStatusDeleted     = 4
)

const (
	DSAHierarchyDisplayType = "hierarchy"
	DSAReportingDisplayType = "reporting"
	DSAChildrenDisplayType  = "children"
)

const (
	DSAOrganizationType     = "organization"
	DSAIndividualType       = "individual"
	DSAInternalEmployeeType = "internal_employee"
)

const (
	CollaboratorAdmin  = "admin"
	CollaboratorViewer = "viewer"
)

const (
	MaxSizeLogoUpload = 1024 * 1024
	MaxSizeCSVUpload  = 1024 * 1024
)

const (
	RejectReasonCurrentPincodeNotServiceable = "current address pincode unserviceable"
	RejectReasonPincodeNotServiceable        = "current address and KYC pincode unserviceable"
	ReasonAddressVerificationQueue           = "address verification queue"
)
const QueryTimeoutExports = 15 * time.Minute
const QueryTimeoutExportsViaDAG = 45 * time.Minute

const (
	UnblockSelfEmployedPolicies = "self_employed"
	UnblockSolarPolicies        = "solar_policies" // this modifies both gating criteria and bank policies
	UnblockAutomationPolicies   = "automation"
	PreApprovedPolicies         = "pre_approved"
	UnblockBankConnect          = "bank_connect"
)

const (
	ReferenceTableExternalService     = "external_service"
	ReferenceTableOutgoingWebhookLogs = "outgoing_webhook_logs"
)

const (
	OfferTypeTentative   = "tentative"
	OfferTypeFinal       = "final"
	OfferTypeBooster     = "booster"
	OfferTypeSoft        = "soft"
	OfferTypeOverridden  = "overridden"
	OfferTypePreapproved = "preapproved"
	OfferTypeTransient   = "transient"
)

const (
	LeadSourceOrganic     = "organic"
	LeadSourceCreditLink  = "credit link"
	LeadSourceMarketplace = "marketplace"
	LeadSourceSystem      = "system"         // This source is used when the used is created automatically by our system. Eg: In Pvt ltd journey, directors are created from the cin api.
	LeadSourceDashboard   = "dashboard"      // This source is used when the users are created in dashboard.
	LeadSourceJourney     = "journey"        // This source is used when the users are created from journey. Eg - co-applicants created by primary user
	LeadSourceAPIStack    = "API_STACK_USER" // This source is used when the users are created from API stack.
)

const (
	LenderSelectionTypeDefault                 = "default"
	LenderSelectionTypePartnerPush             = "partner_data"
	LenderSelectionTypeCascading               = "cascade"
	LenderSelectionTypePincode                 = "pincode_based_elimination"
	LenderSelectionTypePreApproved             = "pre_approved"
	LenderSelectionTypeAge                     = "age_criteria"
	LenderSelectionTypeNTC                     = "ntc"
	LenderSelectionTypeProbability             = "probability_sampling"
	LenderSelectionTypeRoutingAPI              = "routing_api"
	LenderSelectionTypeMarketplaceOnFailure    = "multioffer_on_failure"
	LenderSelectionTypeUserSalariedProbability = "user_salaried_probability_sampling"
	LenderSelectionTypeSelfEmployed            = "self_employed"
	LenderSelectionTypeOfferAccepted           = "lender_offer_accepted"
	LenderSelectionTypeBureauScore             = "bureau_score_criteria"
	LenderSelectionTypeAxisETB                 = "axis_etb"
	LenderSelectionTypeRepeatLender            = "repeat_lender"
)

const PreRoutingStage = "pre-routing"

// constants for custom partner code which is being used to run custom logic for certain clients in common policies (this partner code is different from creditlink partner code)
// has dependency on logic configured on Sentinel
const (
	TDLBREPartnerCode    = "TDL"
	NexarcBREPartnerCode = "NEXARC"
	VyaparBREPartnerCode = "Vyapar BL"
	IIFLPartnerCode      = "IIFL_PL"
	IIFLBLPartnerCode    = "IIFL_BL"
)

// Constants for Reports
const (
	SelectAll   = "all"         // When select all is selected from the UI
	MultiSelect = "multiselect" // When multiple source entities are selected is selected from the UI
)
const (
	OctopusActivity    = "octopus_activity"
	LISAActivity       = "lisa_activity"
	KYCServiceActivity = "kyc_service_activity"
	BCActivity         = "bc_activity"
)

const (
	OctopusActivityWebhook    = "octopus_activity_webhook"
	LISAActivityWebhook       = "lisa_activity_webhook"
	KYCServiceActivityWebhook = "kyc_service_activity_webhook"
	BCActivityWebhook         = "bc_activity_webhook"
)
const BankConnect = "bank_connect"
const GST = "gst"
const KYC = "kyc"

const (
	OccupationTypeSelfEmployed = "self-employed"
	OccupationTypeSalaried     = "salaried"
)
const ExpiryType = "user"
const ExpiryTypeArchival = "archival"

const SDKVersionWeb = "web"

const (
	ServiceTypeGeocodingAddress                            = "gmaps-geocoding-api"
	ServiceTypeCIBIL                                       = "cibil"
	ServiceTypeUANLookup                                   = "uan-lookup"
	ServiceTypeEmailAttribute                              = "email-attribute"
	ServiceTypeCamsPayReg                                  = "cams-pay-reg"
	ServiceTypeCamsPayCancel                               = "cams-pay-cancel"
	ServiceTypeMuthootCibil                                = "muthoot-cibil"
	ServiceTypeOneFin                                      = "onefin"
	ServiceTypePANNameHyperverge                           = "pan-name-hyperverge"
	ServiceTypePANNamePoonawallaFincorp                    = "get-name-from-pan-poonawala"
	ServiceTypePANNameGridline                             = "pan-name-gridline"
	ServiceTypePANDetailsHyperverge                        = "pan-details-hyperverge"
	ServiceTypePANDetailsGridline                          = "pan-details-gridline"
	ServiceTypePANDetailsBEFISC                            = "pan-details-befisc"
	ServiceTypeVerifyPANGridline                           = "pan-verify-gridline"
	ServiceTypeVerifyPANHyperverge                         = "pan-verify-hyperverge"
	ServiceTypeVerifyPANNSDLHyperverge                     = "pan-verify-nsdl-hyperverge"
	ServiceAuthbridgeGSTDetailsAPI                         = "authbridge-gst-details-api"
	ServiceTypePANGSTDetailsKarza                          = "karza-pan-gst-details"
	ServiceTypeGSTDetailsKarza                             = "karza-gst-details"
	ServiceTypeSendSMSMSG91                                = "msg91-send-sms"
	ServiceTypeSendSMSKarix                                = "karix-send-sms"
	ServiceTypeSendSMSKarixMuthoot                         = "karix-send-sms-muthoot"
	ServiceTypeSendOTPKarix                                = "karix-send-otp"
	ServiceTypeSendOTPKarixMuthoot                         = "karix-send-otp-muthoot"
	ServiceTypeUNSCR                                       = "unscr"
	ServiceTypeUdyamAadhar                                 = "udyam-aadhar"
	ServiceTypeCIBILABFL                                   = "abfl-cibil"
	ServiceTypeRiscoveryInsurancePricingEMIProtect         = "riscovery-insurance-pricing-emi-protect"
	ServiceTypeRiscoveryInsurancePurchaseEMPProtect        = "riscovery-insurance-purchase-emi-protect"
	ServiceTypeRiscoveryInsurancePricingPAHospitalization  = "riscovery-insurance-pricing-pa-hospitalization"
	ServiceTypeRiscoveryInsurancePurchasePAHospitalization = "riscovery-insurance-purchase-pa-hospitalization"
	ServiceTypeRiscoveryInsurancePricingLifeInsurance      = "riscovery-insurance-pricing-life-insurance"
	ServiceTypeRiscoveryInsurancePurchaseLifeInsurance     = "riscovery-insurance-purchase-life-insurance"
	ServiceTypeRiscoveryLifeInsuranceStatus                = "riscovery-life-insurance-status"
	ServiceTypeRiscoveryInsuranceStatus                    = "riscovery-insurance-status"
	ServiceTypeElectricity                                 = "karza-electricity"
	ServiceTypeRazorpayCreateCustomer                      = "razorpay-create_customer"
	ServiceTypeRazorpayCreateOrder                         = "razorpay-create-order"
	ServiceTypePANGSTDetailOnGrid                          = "ongrid-pan-gst-details"
	ServiceTypeGSTDetailOnGrid                             = "ongrid-gst-details"
	ServiceTypeTDLCIBILSoftPull                            = "tdl-cibil-soft-pull"
	ServiceTypeTDLCIBILVerifyAuth                          = "tdl-cibil-verify-auth"
	ServiceTypeTDLCIBILRetriggerOTP                        = "tdl-cibil-retrigger-otp"
	ServiceTypeGSTGSPFilling                               = "gst-gsp-filling"
	ServiceTypeGSTTRRNRequestOTP                           = "gst-trrn-request-otp"
	ServiceTypeGSTTRRNSubmitOTP                            = "gst-trrn-submit-otp"
	ServiceTypeCashfreePennydropPFL                        = "cashfree-pennydrop-pfl"
	ServiceTypeCashfreePennydropFinbox                     = "cashfree-pennydrop-finbox"
	ServiceTypeCashfreeCreateOrderV2Muthoot                = "cashfree-payment-create-order-v2-muthoot"
	ServiceTypeCashfreeCreateOrderV2Finbox                 = "cashfree-payment-create-order-v2-finbox"
	ServiceTypeCashfreeGetPaymentStatusV2Muthoot           = "cashfree-payment-get-status-v2-muthoot"
	ServiceTypeCashfreeGetPaymentStatusV2Finbox            = "cashfree-payment-get-status-v2-finbox1"
	ServiceTypeExperian                                    = "experian"
	ServiceTypeExperianABCD                                = "abcd-experian"
	ServiceTypeSearchCINByCompanyName                      = "signzy-search-cin-by-company"
	ServiceTypeGetMCASignatories                           = "karza-get-mca-signatories"
	ServiceTypeNSDLPANVerifyPoonawalla                     = "nsdl-pan-verify-poonawalla"
	ServiceTypeVKYCABFL                                    = "abfl-vkyc"
	ServiceTypeVKYCMFL                                     = "mfl-vkyc"
	ServiceTypeNCLT                                        = "nclt"
	ServiceTypeABFLCommercialCIBIL                         = "abfl-commercial-cibil"
	ServiceTypeKarzaEmploymentVerification                 = "karza-employment-verification"
	ServiceTypeKarzaDomainAuthentication                   = "karza-domain-authentication"
	ServiceTypeHealthNHubIHOInsurancePurchase              = "healthnhub-iho-insurance-purchase"
	ServiceTypePANPhoneToUdyam                             = "pan-phone-to-udyam"
	ServiceTypeAckoInsuranceMuthoot                        = "acko-insurance-muthoot"
	ServiceTypeDecentroUdyam                               = "decentro-udyam"
	ServiceTypeDecentroPDFDownload                         = "decentro-udyam-pdf-download"
	ServiceTypeFieldInvestigation                          = "abfl-field-investigation"
	ServiceCKYCSearchAndDownload                           = "ckyc_search_and_download"
	ServiceTartanUdyamGetApplicationID                     = "tartan-udyam-get-application-id"
	ServiceTartanUdyamSendOTP                              = "tartan-udyam-send-otp"
	ServiceTartanUdyamVerifyOtp                            = "tartan-udyam-verify-otp"
	ServiceTartanUdyamFetchDetails                         = "tartan-udyam-fetch-details"
	ServiceTypeKarzaPanVerifyTDL                           = "karza-pan-verify"
	ServiceTypeMoneyControlPANDetails                      = "moneycontrol-send-pan-details"
	ServiceTypeDecentroMobileToUdyam                       = "decentro-mobile-to-udyam"
	ServiceTypeDecentroUdyamValidation                     = "decentro-udyam-validation"
	ServiceTypeCreatePaymentLinkPayU                       = "payment-create-payu"
	ServiceTypeRazorpayIFSCDetails                         = "razorpay-ifsc-details"
	ServiceTypeABFLGpayWebhook                             = "abfl-gpay-webhook"
	ServiceTypeMuthootMNRL                                 = "muthoot-mnrl"
	ServiceTypeMuthootEmudhraEmbeddedSigning               = "muthoot-emudhra-embedded-signing"
	ServiceTypeMuthootEmudhraSigningWithoutEStamp          = "muthoot-emudhra-signing-without-estamp"
	ServiceTypeMuthootEmudhraDownloadDocument              = "muthoot-emudhra-download-document"
)

// separate service ids for digio for different vendors
const (
	ServiceTypeDigioEmandateCreate              = "digio-create-mandate-form"
	ServiceTypeDigioEmandateCreatePFL           = "digio-create-mandate-form-pfl"
	ServiceTypeDigioEmandateCreateMuthootCL     = "digio-create-mandate-form-muthootCL"
	ServiceTypeDigioEmandateGetDetails          = "digio-emandate-getdetails"
	ServiceTypeDigioEmandateGetDetailsPFL       = "digio_emandate_details_pfl"
	ServiceTypeDigioEmandateGetDetailsMuthootCL = "digio_emandate_details_muthootCL"
	ServiceNameEmandateDetails                  = "emandate-details"
	ServiceNameEmandateCreate                   = "emandate-create"
)

// octopus related service ids
const (
	// Cams-pay
	ServiceIDCAMSPAYRegistration = "81616a5d-e5f3-4828-b38f-37b10be916a2"
	ServiceIDCAMSPAYCancellation = "9f26f7c9-911b-4c50-907a-6c4a86df0ec8"
	// CIBIL
	ServiceIDCIBILNonProd = "f680a71a-c4c9-40b2-9ef3-7ed4652e0af6"
	ServiceIDCIBILProd    = "6f0a2aa4-f489-11ed-a1ca-6eeefe2e6f8b"
	// UAN Lookup
	ServiceIDUANLookupNonProd = "5cbe4c9d-1be6-11ee-8995-22530fbdeb9b"
	ServiceIDUANLookupProd    = "0860f6fe-2c74-11ee-82f6-92bd432f96bd"
	// Email attribute
	ServiceIEmailAttributeNonProd = "f9b25174-17da-4a1c-a750-def7a81a79b8"
	ServiceIDEmailAttributeProd   = "4ec106ab-2d0c-11ee-82f6-92bd432f96bd"
	// Muthoot CIBIL
	ServiceIDMuthootCIBILNonProd = "95d2f9b1-0e75-11ee-90e4-76b5f6817020"
	ServiceIDMuthootCIBILProd    = "78dfa3ed-27a5-11ee-ae0c-f62952fc71bf"
	// Muthoot MNRL
	ServiceIDMuthootMNRLNonProd = "d5d09d23-1f7b-11f0-bd97-e6d98ed3eeb8"
	ServiceIDMuthootMNRLProd    = "800b6670-3aea-11f0-8229-ba219ed89337"
	// Onefin
	ServiceIDOneFinNachRegistrationNonProd = "416ac7c2-2604-11ee-b469-82e74ebafa52"
	ServiceIDOneFinNachRegistrationProd    = "c2a8b3c3-2b9d-11ee-90dd-5e74f8067a9a"

	// PAN Name
	ServiceIDPANNameHypervergeNonProd = "a8f4540e-13ff-11ee-b5b8-9a21f4b69ad2"
	ServiceIDPANNameHypervergeProd    = "61c24c7e-2614-11ee-ae0c-f62952fc71bf"

	ServiceIDPANNameGridlineNonProd = "aab25404-5b67-11ee-8b7b-6e880bf62456"
	ServiceIDPANNameGridlineProd    = "20a79e57-69af-11ee-95f2-2a9773eef987"

	ServiceIDPANNameProdPFL = "b4c091cb-5799-11ee-a29c-361da4276043"

	// PAN Extended Details
	ServiceIDPANDetailsHypervergeNonProd = "195edadf-26c6-11ee-b469-82e74ebafa52"
	ServiceIDPANDetailsHypervergeProd    = "8a71667a-26c9-11ee-ae0c-f62952fc71bf"

	ServiceIDPANDetailsGridlineNonProd = "8e027d67-5b6f-11ee-8b7b-6e880bf62456"
	ServiceIDPANDetailsGridlineProd    = "310a596c-6e53-11ee-95f2-2a9773eef987"

	ServiceIDPANDetailsBEFISCProd    = "ed5ebeaf-3988-11f0-90a7-c26cc1719ead"
	ServiceIDPANDetailsBEFISCNonProd = "61199c98-3bcc-11f0-9483-3283e7bddc07"

	// Verify PAN
	ServiceIDVerifyPANGridlineNonProd = "8c114d27-5b70-11ee-8b7b-6e880bf62456"
	ServiceIDVerifyPANGridlineProd    = "abb4245a-7c77-11ee-9358-c6aee74a5aa1"

	ServiceIDVerifyHypervergeNonProd = "0061f29c-f7aa-444d-af09-9d091d5c03c0"
	ServiceIDVerifyHypervergeProd    = "566c1351-98c3-11ee-9334-168a18707bb0"

	//Autbridge GST Details
	ServiceIDNonProdGSTDetailsAPI = "f4971672-efe0-11ed-b58a-ce1e3e11eaa3"
	ServiceIDProdGSTDetailsAPI    = "87d7d649-fa08-11ed-967d-56c042f7ee5a"

	// PAN GST Details
	ServiceIDPANGSTDetailsNonProd = "5c08aece-2c58-11ee-8286-4278caa4025a"
	ServiceIDPANGSTDetailsProd    = "d9f254e0-4703-11ee-a352-92a795434a57"
	// PAN GST Details - OnGrid
	ServiceIDPANGSTDetailsOnGridNonProd = "626eeace-5b7d-11ee-8b7b-6e880bf62456"
	ServiceIDPANGSTDetailsOnGridProd    = "699a59b9-7a3d-11ee-95f2-2a9773eef987"
	// GST Details
	ServiceIDGSTDetailsNonProd = "59109087-2c5d-11ee-8286-4278caa4025a"
	ServiceIDGSTDetailsProd    = "6efa8092-4704-11ee-a901-6ed89aa9b14d"
	// GST Details - OnGrid
	ServiceIDGSTDetailsOnGridNonProd = "7c7d6715-5b7b-11ee-8b7b-6e880bf62456"
	ServiceIDGSTDetailsOnGridProd    = "ee84329e-7a3a-11ee-95f2-2a9773eef987"

	// MSG91 send-SMS serviceID
	ServiceIDMSG91SendSMSNonProd = "a4dee362-aebe-11ee-9ed6-56fd845e8a0b"
	ServiceIDMSG91SendSMSProd    = "d2b10870-aeeb-11ee-9416-766116f9a8a7"

	//Karix send OTP and SMS serviceID
	ServiceIDKarixSendSMSProd        = "c50c981c-4705-11ee-a352-92a795434a57"
	ServiceIDKarixSendSMSProdMuthoot = "40f6e8a1-27d8-11ef-98da-baee400a08fb"
	ServiceIDKarixSendOTPProd        = "ad8fb09d-4708-11ee-a901-6ed89aa9b14d"
	ServiceIDKarixSendOTPProdMuthoot = "d0d202d8-2969-11ef-a69c-328a5969402f"

	ServiceIDKarixSendSMSNonProd        = "21a410be-2aeb-11ee-8c18-9aa4c11c00f4"
	ServiceIDKarixSendSMSNonProdMuthoot = "34097fc7-296b-11ef-bf73-bea74f6e3f45"
	ServiceIDKarixSendOTPNonProd        = "2220445a-2c52-11ee-8286-4278caa4025a"
	ServiceIDKarixSendOTPNonProdMuthoot = "eeecc75b-296a-11ef-bf73-bea74f6e3f45"

	//gmaps-geocoding-api
	ServiceIDNonProdGmapsGeocodingAPI = "44992424-40f5-11ee-a0bd-3a887f9dfe01"
	ServiceIDProdGmapsGeocodingAPI    = "9b0e94c2-e029-11ed-bfaf-4292702c9dde"

	// unscr
	ServiceIDNonProdUNSCRAPI = "a24a39a6-419f-11ee-a0bd-3a887f9dfe01"
	ServiceIDProdUNSCRAPI    = "59df99e1-7712-11ee-9358-c6aee74a5aa1"

	// udyam aadhar
	ServiceIDNonProdUdyamAadhar = "9cb3eac2-4b00-11ee-a1be-860ee6d6bc92"
	ServiceIDProdUdyamAadhar    = "2bcdea43-7d3f-11ee-9358-c6aee74a5aa1"

	// riscovery pricing
	ServiceIDNonProdRiscoveryPricingEMIProtect = "c126d7d0-4574-11ee-b645-6e754f769587"
	ServiceIDProdRiscoveryPricingEMIProtect    = "e7ad2797-795c-11ee-9358-c6aee74a5aa1"

	ServiceIDNonProdRiscoveryPricingPAHospitalizaton = "bd2209fe-71d6-11ee-8b0c-7a6bdef595e3"
	ServiceIDProdRiscoveryPricingPAHospitalizaton    = "6f1197a2-7a54-11ee-95f2-2a9773eef987"

	ServiceIDNonProdRiscoveryPricingLifeInsurance = "7dc61056-4206-11ef-9d00-f2cd6b6bf96b"
	ServiceIDProdRiscoveryPricingLifeInsurance    = "38b39d12-6ed2-11ef-8680-aaa3249133a4"

	// riscovery purchase
	ServiceIDNonProdRiscoveryPurchaseEMIProtect = "1cb943da-4588-11ee-b645-6e754f769587"
	ServiceIDProdRiscoveryPurchaseEMIProtect    = "3d86a468-7960-11ee-9358-c6aee74a5aa1"

	ServiceIDNonProdRiscoveryPurchasePAHospitalizaton = "66acf7ad-7282-11ee-8e00-7ee5819ffb12"
	ServiceIDProdRiscoveryPurchasePAHospitalizaton    = "3d86a468-7960-11ee-9358-c6aee74a5aa1"

	ServiceIDNonProdRiscoveryPurchaseLifeInsurance = "02e40999-3fa7-11ef-9d00-f2cd6b6bf96b"
	ServiceIDProdRiscoveryPurchaseLifeInsurance    = "8918adf5-6ed2-11ef-8680-aaa3249133a4"

	ServiceIDNonProdRiscoveryStatusLifeInsurance = "93285e2b-3fa7-11ef-9d00-f2cd6b6bf96b"
	ServiceIDProdRiscoveryStatusLifeInsurance    = "ef960aa4-6ed2-11ef-8680-aaa3249133a4"

	ServiceIDNonProdRiscoveryStatusInsurance = "a7a0a5af-56c1-11ee-8b7b-6e880bf62456"
	ServiceIDProdRiscoveryStatusInsurance    = "18656356-6b60-11ef-8fde-9684b480b2bb"

	// abfl-cibil
	ServiceIDNonProdABFLCIBIL = "70edc34a-53c3-11ee-b4e3-42cf5f2fa017"
	ServiceIDProdABFLCIBIL    = "d63f3fb1-797e-11ee-9358-c6aee74a5aa1"

	//digio enach
	ServiceIDNonProdDigioEmandateCreate = "f1a9d8ec-e5db-11ee-8528-f6a4f6297b36"
	ServiceIDProdDigioEmandateCreate    = "f1a9d8ec-e5db-11ee-8528-f6a4f6297b36"

	ServiceIDNonProdDigioEmandateCreatePFL = "6654068e-0d31-11ef-a9d7-46703b15b65a"
	ServiceIDProdDigioEmandateCreatePFL    = "6654068e-0d31-11ef-a9d7-46703b15b65a"

	ServiceIDNonProdDigioEmandateCreateMuthoot = "8f500858-eb99-11ee-8fa9-9e3c8eaeb747"
	ServiceIDProdDigioEmandateCreateMuthoot    = "8f500858-eb99-11ee-8fa9-9e3c8eaeb747"

	ServiceIDNonProdDigioEmandateGetDetails = "0d5d2657-eb56-11ee-8fa9-9e3c8eaeb747"
	ServiceIDProdDigioEmandateGetDetails    = "0d5d2657-eb56-11ee-8fa9-9e3c8eaeb747"

	ServiceIDNonProdDigioEmandateGetDetailsPFL = "9dc91f67-eb95-11ee-8fa9-9e3c8eaeb747"
	ServiceIDProdDigioEmandateGetDetailsPFL    = "9dc91f67-eb95-11ee-8fa9-9e3c8eaeb747"

	ServiceIDNonProdDigioEmandateGetDetailsMuthoot = "9dc91f67-eb95-11ee-8fa9-9e3c8eaeb747"
	ServiceIDProdDigioEmandateGetDetailsMuthoot    = "9dc91f67-eb95-11ee-8fa9-9e3c8eaeb747"

	// karza-electricity-bill
	ServiceIDNonProdElectricity = "dcefdf1c-457e-11ee-b645-6e754f769587"
	ServiceIDProdElectricity    = "3be2519b-770a-11ee-9358-c6aee74a5aa1"

	// razorpay-create-customer
	ServiceIDNonProdRazorpayCreateCustomer = "37307b82-4b14-11ee-a1be-860ee6d6bc92"
	ServiceIDProdRazorpayCreateCustomer    = "3a78719a-7701-11ee-95f2-2a9773eef987"

	// razorpay-create-order
	ServiceIDNonProdRazorpayCreateOrder = "2d1d6626-4b20-11ee-a1be-860ee6d6bc92"
	ServiceIDProdRazorpayCreateOrder    = "6451857f-7704-11ee-95f2-2a9773eef987"

	//tdl-cibil-soft-pull
	ServiceIDNonProdOctopusTDLCibilV2 = "7a078963-8384-11ee-901c-a28b0497b59c"
	ServiceIDProdOctopusTDLCibilV2    = "8fa313ed-94e2-11ee-b2cb-4e59be8c9871"

	//tdl-cibil-soft-pull
	ServiceIDNonProdOctopusTDlVerifyAuth = "9719a3f1-9500-11ee-a297-fe7b34073c29"
	ServiceIDProdOctopusTDlVerifyAuth    = "f07cc810-9501-11ee-9334-168a18707bb0"

	//tdl-cibil-retrigger-otp
	ServiceIDNonProdOctopusTDLRetriggerOTP = "6fdc5ad8-9503-11ee-a297-fe7b34073c29"
	ServiceIDProdOctopusTDLRetriggerOTP    = "086e9711-9504-11ee-b2cb-4e59be8c9871"

	//gst-gsp-filling
	ServiceIDGSTGSPFillingProd    = "09840509-9298-11ee-957b-d6065ddfd0d9"
	ServiceIDGSTGSPFillingNonProd = "1486fd17-9296-11ee-85b6-b6b8578a9333"

	//gst-trrn-request-otp
	ServiceIDGSTTRRNRequestOTPProd    = "a827e365-9fd1-11ee-b15d-522cc2990736"
	ServiceIDGSTTRRNRequestOTPNonProd = "597dbee4-9fd0-11ee-9eb0-3e0cf7e4415f"

	//gst-trrn-submit-otp
	ServiceIDGSTTRRNSubmitOTPProd    = "4af1bae3-9fd4-11ee-b15d-522cc2990736"
	ServiceIDGSTTRRNSubmitOTPNonProd = "593a4ffb-9fd3-11ee-9eb0-3e0cf7e4415f"

	//cashfree-pfl-pennydrop
	ServiceIDCashfreePennydropPFLNonProd    = "f9a416f5-eb49-11ee-8fa9-9e3c8eaeb747"
	ServiceIDCashfreePennydropPFLProd       = "8ff156a3-f05b-11ee-9216-d20ad66af75f"
	ServiceIDCashfreePennydropFinboxNonProd = "4ea8f615-2a1d-11ef-bf73-bea74f6e3f45"
	ServiceIDCashfreePennydropFinboxProd    = "307c4eba-2e03-11ef-ada5-a69f954332a6"

	//cashfree-payment-gateway
	ServiceTypeCashfreeCreateOrderV2MuthootNonProd      = "c453b621-392b-11ef-aea7-f6627b20d68b"
	ServiceTypeCashfreeCreateOrderV2MuthootProd         = "7122bdd8-392e-11ef-81ab-aa973f609137"
	ServiceTypeCashfreeCreateOrderV2FinboxNonProd       = "37b035a5-3aa5-11ef-9d00-f2cd6b6bf96b"
	ServiceTypeCashfreeCreateOrderV2FinboxProd          = "03eeee39-3d27-11ef-81ab-aa973f609137"
	ServiceTypeCashfreeGetPaymentStatusV2MuthootProd    = "2683473b-3d27-11ef-81ab-aa973f609137"
	ServiceTypeCashfreeGetPaymentStatusV2MuthootNonProd = "633d57cd-393b-11ef-9d00-f2cd6b6bf96b"
	ServiceTypeCashfreeGetPaymentStatusV2FinboxProd     = "3fb5d8d8-3d27-11ef-81ab-aa973f609137"
	ServiceTypeCashfreeGetPaymentStatusV2FinboxNonProd  = "3ecbd931-3aa6-11ef-9d00-f2cd6b6bf96b"

	//experian
	ServiceIDExperianProd    = "13e80f2a-f596-11ee-9216-d20ad66af75f"
	ServiceIDExperianNonProd = "b9a9e350-eb39-11ee-8fa9-9e3c8eaeb747"

	// experian abcd
	ServiceIDExperianProdABCDPROD    = "cfbebc15-d4bc-11ef-bcd7-2a42c7afd840"
	ServiceIDExperianNonProdABCDPROD = "c3cf83dc-c13a-11ef-bdc9-d2059cc7bc4a"

	//Get CIN from Company Name
	ServiceIDSearchCINByCompanyNameNonProd = "f3058a74-ffc9-11ee-9b6c-3ec84c31306b"
	ServiceIDSearchCINByCompanyNameProd    = "050f769d-2a57-11ef-98da-baee400a08fb"

	//GetMCASignatories
	ServiceIDGetMCASignatoriesNonProd = "05877c90-fbcf-11ee-9b6c-3ec84c31306b"
	ServiceIDGetMCASignatoriesProd    = "baeebb6f-2973-11ef-98da-baee400a08fb"

	ServiceIDNSDLPANVerifyPFLProd    = "6f59fd7f-022e-11ef-b5db-d20cd7ecd675"
	ServiceIDNSDLPANVerifyPFLNonProd = "cabf8b13-014c-11ef-9b6c-3ec84c31306b"

	//InvokeVKYC
	ServiceTypeVKYCABFLProd    = "9b0e94c2-e029-11ed-bfaf-4292702c9dde"
	ServiceTypeVKYCABFLNonProd = "3c33ebc4-1044-11ef-b1f4-7e1119bb39d4"

	//TO-DO Add correct Service ID for prod before deploying
	ServiceTypeVKYCMFLProd    = "d05f2c52-f5a5-11ef-892d-8ac4585e3ce1"
	ServiceTypeVKYCMFLNonProd = "dbbdd591-defc-11ef-ba92-fa5836c0f7b6"

	//abfl-commercial-cibil
	ServiceIDABFLCommercialCIBILNonProd = "d5e8df82-e6bb-11ee-82cb-9a48902124e8"
	ServiceIDABFLCommercialCIBILProd    = "35c7ccc9-230f-11ef-98da-baee400a08fb"

	ServiceIDKarzaEmploymentVerificationProd    = "cbf15b53-4531-11ef-a52a-62ae2c1eabd8"
	ServiceIDKarzaEmploymentVerificationNonProd = "d1db0b33-1cc0-11ef-9b21-1204ea8d09a8"

	ServiceIDKarzaDomainAuthenticationProd    = "6ecf547e-4533-11ef-9bdc-9684b480b2bb"
	ServiceIDKarzaDomainAuthenticationNonProd = "abf71381-141c-11ef-b05b-9aaf6c995718"

	ServiceIDNCLTNonProd = "d122e374-fe18-11ee-9b6c-3ec84c31306b"
	ServiceIDNCLTProd    = "183195e3-2974-11ef-98da-baee400a08fb"

	ServiceIDHealthNHubIHOInsurancePurchaseProd    = "345fc812-597e-11ef-b308-aaa3249133a4"
	ServiceIDHealthNHubIHOInsurancePurchaseNonProd = "5a9f3e35-504a-11ef-a97b-26728e3ea7f9"

	// pan phone  to udham
	ServiceIDNonProdPanPhoneToUdyam = "f3311531-33a9-11ef-aea7-f6627b20d68b"
	ServiceIDProdPanPhoneToUdyam    = "b347ce38-6abb-11ef-8680-aaa3249133a4"

	// Acko Insurance
	ServiceIDAckoInsuranceMuthootNonProd = "30726791-2339-11ef-bf73-bea74f6e3f45"
	ServiceIDAckoInsuranceMuthootProd    = "fc3b0585-639a-11ef-8406-aaa3249133a4"

	//decentro udyam
	ServiceIDNonProdDecentroUdyam = "b582d7f2-74db-11ef-a9fd-06f1cd2cb6b1"
	ServiceIDDecentroUdyamProd    = "a83147ab-7bd4-11ef-b56a-aaa3249133a4"

	ServiceIDDecentroUdyamPDFDownloadNonProd = "fd315c50-7b08-11ef-a9fd-06f1cd2cb6b1"
	ServiceIDDecentroUdyamPDFDownloadProd    = "51a54370-7bd5-11ef-b56a-aaa3249133a4"

	ServiceIDVerifyPANNSDLHypervergeNonProd = "fdb732e5-e5ef-11ee-8528-f6a4f6297b36"
	ServiceIDVerifyPANNSDLHypervergeProd    = "31ab1001-e5f1-11ee-ac99-7ec181be1f9a"

	//ckyc details
	ServiceIDCKYCSearchAndDownloadNonProd = "b57c615d-a1a9-11ef-b359-22643d2c8174"
	ServiceIDCKYCSearchAndDownloadProd    = "0ea083f8-a1b8-11ef-9177-a2dbbbc77411"

	//tartan udyam get application ID
	ServiceIDTartanUdyamGetApplicationIDNonProd = "d31bd9f8-b6ba-11ef-8019-66590d0d5fb3"
	ServiceIDTartanUdyamGetApplicationIDProd    = "32976f10-cd07-11ef-b133-56e00ba28005"

	//tartan udyam send otp
	ServiceIDTartanUdyamSendOTPNonProd = "805d2553-b6be-11ef-8019-66590d0d5fb3"
	ServiceIDTartanUdyamSendOTPProd    = "a73cfab0-cd08-11ef-a0b2-62d1ff9d6614"

	//tartan udyam verify otp
	ServiceIDTartanUdyamVerifyOtpNonProd = "36d2a167-b6bd-11ef-8019-66590d0d5fb3"
	ServiceIDTartanUdyamVerifyOtpProd    = "4b921cd7-cd08-11ef-898d-2a42c7afd840"

	//tartan udyam fetch details
	ServiceIDTartanUdyamFetchDetailsNonProd = "c3635399-b6bf-11ef-8019-66590d0d5fb3"
	ServiceIDTartanUdyamFetchDetailsProd    = "18d612c3-cd09-11ef-b133-56e00ba28005"

	ServiceIDKarzaPANVerifyTDLNonProd = "fb1c99f2-65e4-11ef-a217-82310160041c"
	ServiceIDKarzaPANVerifyTDLProd    = "7d2279e5-beb1-11ef-bc21-4ee67cfb5b1b"
	// moneycontrol pan details
	ServiceIDMoneyControlPanDetailsNonProd = "71a9ccaf-bd21-11ef-bdc9-d2059cc7bc4a"
	ServiceIDMoneyControlPanDetailsProd    = "83d77322-c423-11ef-bdb6-62d1ff9d6614"

	// Decentro Mobile to Udyam
	ServiceIDDecentroMobileToUdyamNonProd = "e0e2f20e-b609-11ef-8019-66590d0d5fb3"
	ServiceIDDecentroMobileToUdyamProd    = "f90b90f7-eeaf-11ef-b59c-52558265da89"

	// Decentro Udyam Validation
	ServiceIDDecentroUdyamValidationNonProd = "f3625791-c1e5-11ef-bdc9-d2059cc7bc4a"
	ServiceIDDecentroUdyamValidationProd    = "2dd8e170-eeb0-11ef-a84f-8ed99577df7c"

	ServiceIDCreatePaymentPayUNonProd = "d46a2381-e92d-11ef-8920-e2e30635fd3f"
	ServiceIDCreatePaymentPayUProd    = "721abd62-f841-11ef-892d-8ac4585e3ce1"

	ServiceIDFieldInvestigationNonProd = "********-92ac-11ef-a0e0-ae44266a368c"
	ServiceIDFieldInvestigationProd    = "46ce8b7c-f7e8-11ef-a84f-8ed99577df7c"

	//Razorpay Bank Details using IFSC Code
	ServiceIDRazorpayIFSCDetailsNonProd = "294515c5-1105-11f0-a4d5-9241aeda0b74"
	ServiceIDRazorpayIFSCDetailsProd    = "d85fc376-19b9-11f0-8a18-6667ea32b28f"
	ServiceIDABFLGpayWebhookNonProd     = "********-f369-11ef-a15a-a29a65836555"
	ServiceIDABFLGpayWebhookProd        = "07f4a2bb-08a6-11f0-8845-1ed25f68368d"

	ServiceIDMuthootEmudhraEmbeddedSigningNonProd = "6c563407-2ef2-11f0-8cc6-26cad8d7650f"
	ServiceIDMuthootEmudhraEmbeddedSigningProd    = ""

	ServiceIDMuthootEmudhraSigningWithoutEStampNonProd = "26cc8100-1812-4173-9364-bd04a263c796"
	ServiceIDMuthootEmudhraSigningWithoutEStampProd    = ""

	ServiceIDMuthootEmudhraDownloadDocumentNonProd = "3a0494cf-099d-47bf-9fc7-294f2de6b2f7"
	ServiceIDMuthootEmudhraDownloadDocumentProd    = ""
)

var ServiceMapProd = map[string]string{
	ServiceTypeCIBIL:                                       ServiceIDCIBILProd,
	ServiceTypeUANLookup:                                   ServiceIDUANLookupProd,
	ServiceTypeEmailAttribute:                              ServiceIDEmailAttributeProd,
	ServiceTypeCamsPayReg:                                  ServiceIDCAMSPAYRegistration,
	ServiceTypeCamsPayCancel:                               ServiceIDCAMSPAYCancellation,
	ServiceTypeMuthootCibil:                                ServiceIDMuthootCIBILProd,
	ServiceTypeMuthootMNRL:                                 ServiceIDMuthootMNRLProd,
	ServiceTypePANNameHyperverge:                           ServiceIDPANNameHypervergeProd,
	ServiceTypePANNamePoonawallaFincorp:                    ServiceIDPANNameProdPFL,
	ServiceTypeOneFin:                                      ServiceIDOneFinNachRegistrationProd,
	ServiceTypePANDetailsHyperverge:                        ServiceIDPANDetailsHypervergeProd,
	ServiceTypePANDetailsGridline:                          ServiceIDPANDetailsGridlineProd,
	ServiceTypePANDetailsBEFISC:                            ServiceIDPANDetailsBEFISCProd,
	ServiceTypePANNameGridline:                             ServiceIDPANNameGridlineProd,
	ServiceTypeVerifyPANGridline:                           ServiceIDVerifyPANGridlineProd,
	ServiceTypeVerifyPANHyperverge:                         ServiceIDVerifyHypervergeProd,
	ServiceAuthbridgeGSTDetailsAPI:                         ServiceIDProdGSTDetailsAPI,
	ServiceTypePANGSTDetailsKarza:                          ServiceIDPANGSTDetailsProd,
	ServiceTypeGSTDetailsKarza:                             ServiceIDGSTDetailsProd,
	ServiceTypeSendSMSMSG91:                                ServiceIDMSG91SendSMSProd,
	ServiceTypeSendSMSKarix:                                ServiceIDKarixSendSMSProd,
	ServiceTypeSendSMSKarixMuthoot:                         ServiceIDKarixSendSMSProdMuthoot,
	ServiceTypeSendOTPKarix:                                ServiceIDKarixSendOTPProd,
	ServiceTypeSendOTPKarixMuthoot:                         ServiceIDKarixSendOTPProdMuthoot,
	ServiceTypeGeocodingAddress:                            ServiceIDProdGmapsGeocodingAPI,
	ServiceTypeUNSCR:                                       ServiceIDProdUNSCRAPI,
	ServiceTypeUdyamAadhar:                                 ServiceIDProdUdyamAadhar,
	ServiceTypeCIBILABFL:                                   ServiceIDProdABFLCIBIL,
	ServiceTypeRiscoveryInsurancePricingEMIProtect:         ServiceIDProdRiscoveryPricingEMIProtect,
	ServiceTypeRiscoveryInsurancePurchaseEMPProtect:        ServiceIDProdRiscoveryPurchaseEMIProtect,
	ServiceTypeRiscoveryInsurancePricingPAHospitalization:  ServiceIDProdRiscoveryPricingPAHospitalizaton,
	ServiceTypeRiscoveryInsurancePurchasePAHospitalization: ServiceIDProdRiscoveryPurchasePAHospitalizaton,
	ServiceTypeRiscoveryInsurancePricingLifeInsurance:      ServiceIDProdRiscoveryPricingLifeInsurance,
	ServiceTypeRiscoveryInsurancePurchaseLifeInsurance:     ServiceIDProdRiscoveryPurchaseLifeInsurance,
	ServiceTypeRiscoveryLifeInsuranceStatus:                ServiceIDProdRiscoveryStatusLifeInsurance,
	ServiceTypeRiscoveryInsuranceStatus:                    ServiceIDProdRiscoveryStatusInsurance,
	ServiceTypeElectricity:                                 ServiceIDProdElectricity,
	ServiceTypeDigioEmandateCreate:                         ServiceIDProdDigioEmandateCreate,
	ServiceTypeDigioEmandateCreatePFL:                      ServiceIDProdDigioEmandateCreatePFL,
	ServiceTypeDigioEmandateCreateMuthootCL:                ServiceIDProdDigioEmandateCreateMuthoot,
	ServiceTypeDigioEmandateGetDetails:                     ServiceIDProdDigioEmandateGetDetails,
	ServiceTypeDigioEmandateGetDetailsPFL:                  ServiceIDProdDigioEmandateGetDetailsPFL,
	ServiceTypeDigioEmandateGetDetailsMuthootCL:            ServiceIDProdDigioEmandateGetDetailsMuthoot,
	ServiceTypeRazorpayCreateCustomer:                      ServiceIDProdRazorpayCreateCustomer,
	ServiceTypeRazorpayCreateOrder:                         ServiceIDProdRazorpayCreateOrder,
	ServiceTypePANGSTDetailOnGrid:                          ServiceIDPANGSTDetailsOnGridProd,
	ServiceTypeGSTDetailOnGrid:                             ServiceIDGSTDetailsOnGridProd,
	ServiceTypeTDLCIBILSoftPull:                            ServiceIDProdOctopusTDLCibilV2,
	ServiceTypeTDLCIBILVerifyAuth:                          ServiceIDProdOctopusTDlVerifyAuth,
	ServiceTypeTDLCIBILRetriggerOTP:                        ServiceIDProdOctopusTDLRetriggerOTP,
	ServiceTypeGSTGSPFilling:                               ServiceIDGSTGSPFillingProd,
	ServiceTypeGSTTRRNRequestOTP:                           ServiceIDGSTTRRNRequestOTPProd,
	ServiceTypeGSTTRRNSubmitOTP:                            ServiceIDGSTTRRNSubmitOTPProd,
	ServiceTypeExperian:                                    ServiceIDExperianProd,
	ServiceTypeNSDLPANVerifyPoonawalla:                     ServiceIDNSDLPANVerifyPFLProd,
	ServiceTypeCashfreePennydropPFL:                        ServiceIDCashfreePennydropPFLProd,
	ServiceTypeCashfreePennydropFinbox:                     ServiceIDCashfreePennydropFinboxProd,
	ServiceTypeCashfreeCreateOrderV2Muthoot:                ServiceTypeCashfreeCreateOrderV2MuthootProd,
	ServiceTypeCashfreeCreateOrderV2Finbox:                 ServiceTypeCashfreeCreateOrderV2FinboxProd,
	ServiceTypeCashfreeGetPaymentStatusV2Muthoot:           ServiceTypeCashfreeGetPaymentStatusV2MuthootProd,
	ServiceTypeCashfreeGetPaymentStatusV2Finbox:            ServiceTypeCashfreeGetPaymentStatusV2FinboxProd,
	ServiceTypeNCLT:                                        ServiceIDNCLTProd,
	ServiceTypeABFLCommercialCIBIL:                         ServiceIDABFLCommercialCIBILProd,
	ServiceTypeSearchCINByCompanyName:                      ServiceIDSearchCINByCompanyNameProd,
	ServiceTypeGetMCASignatories:                           ServiceIDGetMCASignatoriesProd,
	ServiceTypeVKYCABFL:                                    ServiceTypeVKYCABFLProd,
	ServiceTypeVKYCMFL:                                     ServiceTypeVKYCMFLProd,
	ServiceTypeKarzaEmploymentVerification:                 ServiceIDKarzaEmploymentVerificationProd,
	ServiceTypeKarzaDomainAuthentication:                   ServiceIDKarzaDomainAuthenticationProd,
	ServiceTypeHealthNHubIHOInsurancePurchase:              ServiceIDHealthNHubIHOInsurancePurchaseProd,
	ServiceTypePANPhoneToUdyam:                             ServiceIDProdPanPhoneToUdyam,
	ServiceTypeAckoInsuranceMuthoot:                        ServiceIDAckoInsuranceMuthootProd,
	ServiceTypeDecentroUdyam:                               ServiceIDDecentroUdyamProd,
	ServiceTypeDecentroPDFDownload:                         ServiceIDDecentroUdyamPDFDownloadProd,
	ServiceTypeVerifyPANNSDLHyperverge:                     ServiceIDVerifyPANNSDLHypervergeProd,
	ServiceTypeFieldInvestigation:                          ServiceIDFieldInvestigationProd,
	ServiceCKYCSearchAndDownload:                           ServiceIDCKYCSearchAndDownloadProd,
	ServiceTypeKarzaPanVerifyTDL:                           ServiceIDKarzaPANVerifyTDLProd,
	ServiceTypeExperianABCD:                                ServiceIDExperianProdABCDPROD,
	ServiceTypeMoneyControlPANDetails:                      ServiceIDMoneyControlPanDetailsProd,
	ServiceTartanUdyamGetApplicationID:                     ServiceIDTartanUdyamGetApplicationIDProd,
	ServiceTartanUdyamSendOTP:                              ServiceIDTartanUdyamSendOTPProd,
	ServiceTartanUdyamVerifyOtp:                            ServiceIDTartanUdyamVerifyOtpProd,
	ServiceTartanUdyamFetchDetails:                         ServiceIDTartanUdyamFetchDetailsProd,
	ServiceTypeDecentroMobileToUdyam:                       ServiceIDDecentroMobileToUdyamProd,
	ServiceTypeDecentroUdyamValidation:                     ServiceIDDecentroUdyamValidationProd,
	ServiceTypeCreatePaymentLinkPayU:                       ServiceIDCreatePaymentPayUProd,
	ServiceTypeRazorpayIFSCDetails:                         ServiceIDRazorpayIFSCDetailsProd,
	ServiceTypeABFLGpayWebhook:                             ServiceIDABFLGpayWebhookProd,
	ServiceTypeMuthootEmudhraEmbeddedSigning:               ServiceIDMuthootEmudhraEmbeddedSigningProd,
	ServiceTypeMuthootEmudhraSigningWithoutEStamp:          ServiceIDMuthootEmudhraSigningWithoutEStampProd,
	ServiceTypeMuthootEmudhraDownloadDocument:              ServiceIDMuthootEmudhraDownloadDocumentProd,
}

var ServiceMapNonProd = map[string]string{
	ServiceTypeCIBIL:                                       ServiceIDCIBILNonProd,
	ServiceTypeUANLookup:                                   ServiceIDUANLookupNonProd,
	ServiceTypeEmailAttribute:                              ServiceIEmailAttributeNonProd,
	ServiceTypeCamsPayReg:                                  ServiceIDCAMSPAYRegistration,
	ServiceTypeCamsPayCancel:                               ServiceIDCAMSPAYCancellation,
	ServiceTypeMuthootCibil:                                ServiceIDMuthootCIBILNonProd,
	ServiceTypeMuthootMNRL:                                 ServiceIDMuthootMNRLNonProd,
	ServiceTypePANNameHyperverge:                           ServiceIDPANNameHypervergeNonProd,
	ServiceTypePANNameGridline:                             ServiceIDPANNameGridlineNonProd,
	ServiceTypeOneFin:                                      ServiceIDOneFinNachRegistrationNonProd,
	ServiceTypePANDetailsHyperverge:                        ServiceIDPANDetailsHypervergeNonProd,
	ServiceTypePANDetailsGridline:                          ServiceIDPANDetailsGridlineNonProd,
	ServiceTypePANDetailsBEFISC:                            ServiceIDPANDetailsBEFISCNonProd,
	ServiceTypeVerifyPANGridline:                           ServiceIDVerifyPANGridlineNonProd,
	ServiceTypeVerifyPANHyperverge:                         ServiceIDVerifyHypervergeNonProd,
	ServiceAuthbridgeGSTDetailsAPI:                         ServiceIDNonProdGSTDetailsAPI,
	ServiceTypePANGSTDetailsKarza:                          ServiceIDPANGSTDetailsNonProd,
	ServiceTypeGSTDetailsKarza:                             ServiceIDGSTDetailsNonProd,
	ServiceTypeSendSMSMSG91:                                ServiceIDMSG91SendSMSNonProd,
	ServiceTypeSendSMSKarix:                                ServiceIDKarixSendSMSNonProd,
	ServiceTypeSendSMSKarixMuthoot:                         ServiceIDKarixSendSMSNonProdMuthoot,
	ServiceTypeSendOTPKarix:                                ServiceIDKarixSendOTPNonProd,
	ServiceTypeSendOTPKarixMuthoot:                         ServiceIDKarixSendOTPNonProdMuthoot,
	ServiceTypeGeocodingAddress:                            ServiceIDNonProdGmapsGeocodingAPI,
	ServiceTypeUNSCR:                                       ServiceIDNonProdUNSCRAPI,
	ServiceTypeUdyamAadhar:                                 ServiceIDNonProdUdyamAadhar,
	ServiceTypeCIBILABFL:                                   ServiceIDNonProdABFLCIBIL,
	ServiceTypeRiscoveryInsurancePricingEMIProtect:         ServiceIDNonProdRiscoveryPricingEMIProtect,
	ServiceTypeRiscoveryInsurancePurchaseEMPProtect:        ServiceIDNonProdRiscoveryPurchaseEMIProtect,
	ServiceTypeRiscoveryInsurancePricingPAHospitalization:  ServiceIDNonProdRiscoveryPricingPAHospitalizaton,
	ServiceTypeRiscoveryInsurancePurchasePAHospitalization: ServiceIDNonProdRiscoveryPurchasePAHospitalizaton,
	ServiceTypeRiscoveryInsurancePricingLifeInsurance:      ServiceIDNonProdRiscoveryPricingLifeInsurance,
	ServiceTypeRiscoveryInsurancePurchaseLifeInsurance:     ServiceIDNonProdRiscoveryPurchaseLifeInsurance,
	ServiceTypeRiscoveryInsuranceStatus:                    ServiceIDNonProdRiscoveryStatusInsurance,
	ServiceTypeElectricity:                                 ServiceIDNonProdElectricity,
	ServiceTypeDigioEmandateCreate:                         ServiceIDNonProdDigioEmandateCreate,
	ServiceTypeDigioEmandateCreatePFL:                      ServiceIDNonProdDigioEmandateCreatePFL,
	ServiceTypeDigioEmandateCreateMuthootCL:                ServiceIDNonProdDigioEmandateCreateMuthoot,
	ServiceTypeDigioEmandateGetDetails:                     ServiceIDNonProdDigioEmandateGetDetails,
	ServiceTypeDigioEmandateGetDetailsPFL:                  ServiceIDNonProdDigioEmandateGetDetailsPFL,
	ServiceTypeDigioEmandateGetDetailsMuthootCL:            ServiceIDNonProdDigioEmandateGetDetailsMuthoot,
	ServiceTypeRazorpayCreateCustomer:                      ServiceIDNonProdRazorpayCreateCustomer,
	ServiceTypeRazorpayCreateOrder:                         ServiceIDNonProdRazorpayCreateOrder,
	ServiceTypePANGSTDetailOnGrid:                          ServiceIDPANGSTDetailsOnGridNonProd,
	ServiceTypeGSTDetailOnGrid:                             ServiceIDGSTDetailsOnGridNonProd,
	ServiceTypeTDLCIBILSoftPull:                            ServiceIDNonProdOctopusTDLCibilV2,
	ServiceTypeTDLCIBILVerifyAuth:                          ServiceIDNonProdOctopusTDlVerifyAuth,
	ServiceTypeTDLCIBILRetriggerOTP:                        ServiceIDNonProdOctopusTDLRetriggerOTP,
	ServiceTypeGSTGSPFilling:                               ServiceIDGSTGSPFillingNonProd,
	ServiceTypeGSTTRRNRequestOTP:                           ServiceIDGSTTRRNRequestOTPNonProd,
	ServiceTypeGSTTRRNSubmitOTP:                            ServiceIDGSTTRRNSubmitOTPNonProd,
	ServiceTypeCashfreePennydropPFL:                        ServiceIDCashfreePennydropPFLNonProd,
	ServiceTypeCashfreePennydropFinbox:                     ServiceIDCashfreePennydropFinboxNonProd,
	ServiceTypeCashfreeCreateOrderV2Muthoot:                ServiceTypeCashfreeCreateOrderV2MuthootNonProd,
	ServiceTypeCashfreeCreateOrderV2Finbox:                 ServiceTypeCashfreeCreateOrderV2FinboxNonProd,
	ServiceTypeCashfreeGetPaymentStatusV2Muthoot:           ServiceTypeCashfreeGetPaymentStatusV2MuthootNonProd,
	ServiceTypeCashfreeGetPaymentStatusV2Finbox:            ServiceTypeCashfreeGetPaymentStatusV2FinboxNonProd,
	ServiceTypeExperian:                                    ServiceIDExperianNonProd,
	ServiceTypeSearchCINByCompanyName:                      ServiceIDSearchCINByCompanyNameNonProd,
	ServiceTypeGetMCASignatories:                           ServiceIDGetMCASignatoriesNonProd,
	ServiceTypeNSDLPANVerifyPoonawalla:                     ServiceIDNSDLPANVerifyPFLNonProd,
	ServiceTypeVKYCABFL:                                    ServiceTypeVKYCABFLNonProd,
	ServiceTypeVKYCMFL:                                     ServiceTypeVKYCMFLNonProd,
	ServiceTypeNCLT:                                        ServiceIDNCLTNonProd,
	ServiceTypeABFLCommercialCIBIL:                         ServiceIDABFLCommercialCIBILNonProd,
	ServiceTypeKarzaEmploymentVerification:                 ServiceIDKarzaEmploymentVerificationNonProd,
	ServiceTypeKarzaDomainAuthentication:                   ServiceIDKarzaDomainAuthenticationNonProd,
	ServiceTypeHealthNHubIHOInsurancePurchase:              ServiceIDHealthNHubIHOInsurancePurchaseNonProd,
	ServiceTypePANPhoneToUdyam:                             ServiceIDNonProdPanPhoneToUdyam,
	ServiceTypeAckoInsuranceMuthoot:                        ServiceIDAckoInsuranceMuthootNonProd,
	ServiceTypeDecentroUdyam:                               ServiceIDNonProdDecentroUdyam,
	ServiceTypeDecentroPDFDownload:                         ServiceIDDecentroUdyamPDFDownloadNonProd,
	ServiceTypeVerifyPANNSDLHyperverge:                     ServiceIDVerifyPANNSDLHypervergeNonProd,
	ServiceCKYCSearchAndDownload:                           ServiceIDCKYCSearchAndDownloadNonProd,
	ServiceTartanUdyamGetApplicationID:                     ServiceIDTartanUdyamGetApplicationIDNonProd,
	ServiceTartanUdyamSendOTP:                              ServiceIDTartanUdyamSendOTPNonProd,
	ServiceTartanUdyamVerifyOtp:                            ServiceIDTartanUdyamVerifyOtpNonProd,
	ServiceTartanUdyamFetchDetails:                         ServiceIDTartanUdyamFetchDetailsNonProd,
	ServiceTypeKarzaPanVerifyTDL:                           ServiceIDKarzaPANVerifyTDLNonProd,
	ServiceTypeExperianABCD:                                ServiceIDExperianNonProdABCDPROD,
	ServiceTypeMoneyControlPANDetails:                      ServiceIDMoneyControlPanDetailsNonProd,
	ServiceTypeDecentroMobileToUdyam:                       ServiceIDDecentroMobileToUdyamNonProd,
	ServiceTypeDecentroUdyamValidation:                     ServiceIDDecentroUdyamValidationNonProd,
	ServiceTypeFieldInvestigation:                          ServiceIDFieldInvestigationNonProd,
	ServiceTypeCreatePaymentLinkPayU:                       ServiceIDCreatePaymentPayUNonProd,
	ServiceTypeRazorpayIFSCDetails:                         ServiceIDRazorpayIFSCDetailsNonProd,
	ServiceTypeABFLGpayWebhook:                             ServiceIDABFLGpayWebhookNonProd,
	ServiceTypeMuthootEmudhraEmbeddedSigning:               ServiceIDMuthootEmudhraEmbeddedSigningNonProd,
	ServiceTypeMuthootEmudhraSigningWithoutEStamp:          ServiceIDMuthootEmudhraSigningWithoutEStampNonProd,
	ServiceTypeMuthootEmudhraDownloadDocument:              ServiceIDMuthootEmudhraDownloadDocumentNonProd,
}

const FinboxOktaLoginEmail = "<EMAIL>"

const TemporalOperationName = "temporal.activity"

var OccupationTypeMapForAgreement = map[string]string{
	"Self Employed":  "Others (Professional / Self Employed / Retired / Housewife / Student)",
	"Salaried":       "Service (Private Sector/ Public Sector / Govt. Sector)",
	"Business Owner": "Business",
}

const (
	LenderStatusActive   = "active"
	LenderStatusRejected = "rejected"
	LenderStatusInactive = "inactive"
)

const (
	MinOTP = 100000
	MaxOTP = 999999
)

const (
	LenderDashboardMFADisabled         = 0
	LenderDashboardMFAEnabled          = 1
	LenderDashboardMFAAuthenticated    = 2
	LenderDashboardMFAOTPAuthenticated = 3
)

// constants for userworkflows status
const (
	TemporalStatusRunning        = "RUNNING"
	TemporalStatusFailed         = "FAILED"
	TemporalStatusSuccess        = "SUCCESS"
	TemporalStatusArchivedSuffix = "_ARCHIVED"
	TemporalStatusNotStarted     = "NOT_STARTED"
	TemporalStatusReset          = "RESET"
)

// constants for generic failure reasons based on usecase
const (
	TemporalFailureReasonApplicationReinitiated = "Application Reinitiated"
	TemporalFailureReasonBringBackToMultiOffer  = "Bring Back User To Multi Offer"
)

const (
	// logic constants
	TemporalActivityStringMatchLogicFuzzyToken = "fuzzy-token-set-ratio"
	TemporalActivityStringMatchLogicExact      = "exact"
	TemporalActivityStringMatchLogicFinBoxAPI  = "finbox-api"
	// Success-failure constants
	TemporalActivityStringMatchSuccess = "SUCCESS"
	TemporalActivityStringMatchFailed  = "FAILED"
)

// Insurance Vendors
const (
	RiscoveryID = "2300d39c-c123-44b4-a4e3-dc7e1f57ebe7"
	DocOnlineID = "90286fac-e85f-4b0e-86f4-48b182f2d7f0"
	HealthNHub  = "4b03c06e-8aed-4e54-a2be-8877c5f363dc"
	AckoID      = "c1192bdd-21a7-4933-8828-60d4127cf699"
)

// InsuranceVendorMapping defines sourceEntityID -> insurance vendor mapping
var InsuranceVendorMapping = map[string]string{
	IIFLBLID:            IIFLID,
	IIFLID:              IIFLID,
	ABFLID:              RiscoveryID,
	PoonawallaFincorpID: DocOnlineID,
}

var InsuranceVendorToVendorID = map[string]string{
	InsuranceVendorAcko: AckoID,
}

const (
	ABFLRequestIDPrefix = "SR"
	// WF = WORK FLOW
	ABFLWFRequestProcessing = "processing"
	ABFLWFRequestAccepted   = "accepted"
	ABFLWFRequestRejected   = "rejected"
	ABFLWFRequestExpired    = "expired"
	ABFLWFRequestRaised     = "raised"
	ABFLWFRequestCancelled  = "cancelled"
	ABFLWFRequestDocuments  = "documents"
	ABFLWFRequestApproved   = "approved"
	ABFLWFUnderReview       = "under_review"
	ABFLWFRAD               = "rad"
	ABFLWFVKYC              = "vkyc"
	ABFLWFRADAndVKYC        = "rad_and_vkyc"

	//WF model IDs
	WFTypeOfferNegotiation     = "abfl_offer_negotiation_wf"
	WFTypeKYC                  = "abfl_kyc_wf"
	WFTypeBRE                  = "abfl_bre_wf"
	WFTypeCreditApprovalAbflPL = "abfl_pl_credit_approval_wf"
	WFTypeRCUApprovalAbflPL    = "abfl_pl_rcu_approval_wf"

	//temporal dashboard WF names

	WFTypeOfferNegotiationV2        = WFTypeOfferNegotiation + "_v2"
	WFTypeKYCV2                     = WFTypeKYC + "_v2"
	WFTypeBREV2                     = WFTypeBRE + "_v2"
	WorkflowRejectionReversalPrefix = "workflow_rejection_reversal_"
	ABFLOfferNegotiationWFV         = "abfl_offer_negotiation_wf_v2"
	ABFLPLCreditWorkflow            = "abfl_pl_credit_approval_wf"
	ABFLPLRCUWorkflow               = "abfl_pl_rcu_approval_wf"

	ConfigEnabled = 1

	//WF Resource type
	ResourceTypeLoanApplicationID = "loan_application_id"
	ResourceTypeLeadID            = "lead_id"

	//WF Email Types
	ABFLLoanOfferNegoSalesToBusinessEmail  = "abfl_business_request_email"
	ABFLLoanOfferNegoBusinessToSalesEmail  = "abfl_sales_request_email"
	ABFLManualKYCInitEmail                 = "abfl_manual_kyc_init_email"
	ABFLPLManualApprovalsInitEmail         = "abfl_pl_manual_approvals_init_email"
	ABFLRequestForDocumentEmail            = "abfl_request_for_documents_email"
	ABFLRequestForVKYCReportEmail          = "abfl_request_for_vkyc_report_email"
	ABFLAdditionalDocsUploadedEmail        = "abfl_additional_docs_uploaded_email"
	ABFLVKYCReportUploadedEmail            = "abfl_vkyc_report_uploaded_email"
	ABFLSanctionConditionEmail             = "abfl_sanction_condition_email"
	ABFLPLSanctionCreationConditionEmail   = "abflpl_sanction_creation_condition_email"
	ABFLPLSanctionAssignmentConditionEmail = "abflpl_sanction_assignment_condition_email"

	//WF Actions
	ABFLWFInitiatedAction           = "abfl_initiate_wf"
	ABFLWFApproveKYCAction          = "abfl_approve_kyc"
	ABFLWFApproveBusinessDocsAction = "abfl_approve_business_docs"
	ABFLWFMoveToRADAction           = "abfl_move_to_rad"
	ABFLWFMoveToVKYCAction          = "abfl_move_to_vkyc"
	ABFLWFRADCompleteAction         = "abfl_rad_complete"
	ABFLWFVKYCCompleteAction        = "abfl_vkyc_complete"
	ABFLWFRejectKYCAction           = "abfl_reject_kyc"
	ABFLWFSendEmailAction           = "email_sent"
	ABFLWFBankFraudApproveAction    = "abfl_bank_fraud_approved"

	WorkflowStarted                   = "workflow_started"
	ABFLBREWorkFlowProcessingCreditL1 = "workflow_processing_credit_l1"
	ABFLBREWorkFlowProcessingCreditL2 = "workflow_processing_credit_l2"
	ABFLBREWorkFlowProcessingCreditL3 = "workflow_processing_credit_l3"
	ABFLBREWorkFlowProcessingSales    = "workflow_processing_sales"
	ABFLBREWorkFlowProcessingRCU      = "workflow_processing_rcu"
	WorkflowEmailTriggered            = "email_triggered"
)

var TerminalStates = []string{ABFLWFRequestAccepted, ABFLWFRequestApproved, ABFLWFRequestRejected,
	ABFLWFRequestExpired, ABFLWFRequestCancelled}

var WFActionReadableMapping = map[string]string{
	ABFLWFInitiatedAction:           "Initate Workflow",
	ABFLWFApproveKYCAction:          "<CURR_GROUP> approves KYC",
	ABFLWFMoveToRADAction:           "Request to <NEXT_GROUP> for Additional Docs",
	ABFLWFMoveToVKYCAction:          "Request to <NEXT_GROUP> for V-KYC",
	ABFLWFRADCompleteAction:         "<CURR_GROUP> Team uploads documents",
	ABFLWFVKYCCompleteAction:        "<CURR_GROUP> Team uploads V-KYC report from dashboard",
	ABFLWFRejectKYCAction:           "KYC workflow closed by <CURR_GROUP>",
	ABFLWFSendEmailAction:           "Email triggered to <NEXT_GROUP>",
	ABFLWFApproveBusinessDocsAction: "Business documents approved by <CURR_GROUP>",
	WorkflowEmailTriggered:          "Email triggered to <NEXT_GROUP>",

	WorkflowStarted:                              "Initiate Workflow",
	RequestAdditionalDocumentsEvent:              "Request to <NEXT_GROUP> for Additional Docs",
	RequestVKYCReportEvent:                       "Request to <NEXT_GROUP> for V-KYC",
	UploadAdditionalDocumentsEvent:               "<CURR_GROUP> Team uploads documents",
	UploadVKYCReportEvent:                        "<CURR_GROUP> Team uploads V-KYC report from dashboard",
	ApprovePersonalKYCEvent:                      "<CURR_GROUP> approves KYC",
	ApproveBusinessKYCEvent:                      "Business documents approved by <CURR_GROUP>",
	RejectEvent:                                  "KYC workflow closed by <CURR_GROUP>",
	dashboardworkflow.AssignWorkflowTaskAction:   "Task assigned to <NEXT_GROUP> user",
	dashboardworkflow.ReassignWorkflowTaskAction: "Task reassigned to <NEXT_GROUP> user",
	dashboardworkflow.InitiateWorkflowTaskAction: "Task initiated",
	ABFLWFBankFraudApproveAction:                 "Bank Statement Approved",
}

var WFStateReadableMapping = map[string]string{
	ABFLWFRAD:        "requested additional documents",
	ABFLWFVKYC:       "requested vkyc report",
	ABFLWFRADAndVKYC: "requested additional documents and vkyc report",
}

func GetReadableWFState(wf string) string {
	if name, ok := WFStateReadableMapping[wf]; ok {
		return name
	}
	return wf
}

const ProcessingFeeTypeFlat = "FLAT"
const ProcessingFeeTypePercent = "PERC"

const (
	InsuranceTypeHealthWellness = "Health_Wellness"
	InsuranceTypeEDIProtect     = "EDI_PROTECT"
	InsuranceTypeLifeInsurance  = "LIFE_INSURANCE"
)

var DisbursalContent = map[string]string{
	PoonawallaFincorpID: "Dear Applicant, please do not pay any person or agency for completing the online Instant Personal loan application form on the Poonawalla Fincorp website or Mobile App. Poonawalla Fincorp does not charge a fee for online application submission, nor has it authorized any person or agent to act on their behalf when applying through our website (All fees are deducted from your disbursal amount if any). If someone asks for cash or a portion of the payout amount, please report it by calling us on our toll-free number 18002663201.",
	ABFLID:              "Congratulations! Your loan has been successfully disbursed",
	LendingKartID:       "Congratulations, on your loan disbursement! To check your loan details, please visit our lending partner dashboard.",
	FlexiLoansID:        "Congratulations, on your loan disbursement! To check your loan details, please visit our lending partner dashboard.",
	IndifiID:            "Congratulations, on your loan disbursement! To check your loan details, please visit our lending partner dashboard.",
}

var ScamAlertContent = map[string]string{
	IIFLID: `<p>IIFL Finance will never request any additional fees or charges from you before disbursing the loan amount in your bank account. If anyone asks you for the same, please <b>do not pay them</b> and report the incident on 022-******** or <a href="mailto:<EMAIL>"><EMAIL></a> immediately.</p>`,
}

var DisbursalCTA = map[string]string{
	LendingKartID: LendingKartDashboardURL,
}

var DisbursalCTAText = map[string]string{
	LendingKartID: "Partner Dashboard",
	FlexiLoansID:  "Partner Dashboard",
	IndifiID:      "Partner Dashboard",
}

// Channel Management Display types
const (
	ChildHierarchyDisplayType     = "children"
	ReportingHierarchyDisplayType = "reporting"
	OrgHierarchyDisplayType       = "organization"
)

var HierarchyDisplayTypes = []string{ChildHierarchyDisplayType, ReportingHierarchyDisplayType, OrgHierarchyDisplayType}

const DefaultLocalPassword = "Finbox@123"

const (
	SourceEntityStatusInactive             = 0
	SourceEntityStatusActive               = 1
	SourceEntityStatusActiveJourneyBlocked = 2
)

const (
	LoanDeviationActive      = WFTypeOfferNegotiation + "_active"
	LoanDeviationClosed      = WFTypeOfferNegotiation + "_closed"
	KYCWFActive              = WFTypeKYC + "_active"
	KYCWFClosed              = WFTypeKYC + "_closed"
	ManualCreditReviewActive = WFTypeBRE + "_active"
	ManualCreditReviewClosed = WFTypeBRE + "_closed"
)

var AllowedWFFilterTypes = []string{LoanDeviationActive, LoanDeviationClosed, KYCWFActive, KYCWFClosed, ManualCreditReviewActive, ManualCreditReviewClosed}

var AllowedSchemeValues = []string{VyaparMitra, BandhanPlus}

var AllowedSegmentTypes = []string{SegmentPrime}

const (
	PerfiosStatusVerfied = "VERIFIED"
	PerfiosStatusRefer   = "REFER"
	PerfiosStatusFraud   = "FRAUD"
)

const (
	BankConnectAuthorFraud      = "author_fraud"
	BankConnectInconsistentTxns = "inconsistent_transaction"
)

const (
	BankConnectModePDF        = "pdf"
	BankConnectModeAA         = "aa"
	BankConnectModeNetbanking = "online"

	BankConnectModeStatement     = "statement" // used as a blanket option for mode PDF in v2/user.go#TriggerBankAnalysis
	BankConnectStatementFileExt  = ".pdf"
	BankConnectStatementMIMEType = "application/pdf"

	BankConnectUploadTypePDF    = "PDF"
	BankConnectUploadTypeBase64 = "BASE64"

	BankConnectAAEntityOneMoney = "onemoney-aa"
	BankConnectAAEntityFinvu    = "finvu-aa"
	BankConnectAAEntitySetu     = "setu-aa"
)

var (
	BankConnectUploadTypes = []string{BankConnectUploadTypePDF, BankConnectUploadTypeBase64}
	BankConnectModes       = []string{BankConnectModePDF, BankConnectModeNetbanking, BankConnectModeAA, BankConnectModeStatement}
	BankConnectAAEntities  = []string{BankConnectAAEntityFinvu, BankConnectAAEntityOneMoney, BankConnectAAEntitySetu}
)

const (
	LoanVariantTopUp      = "topup"
	LoanVariantIndividual = "individual"
)

const (
	TopupIneligibile           = 0
	TopupEligibilityDetermined = 1
	TopupJourneyStarted        = 2
	TopupDisqualified          = 3
	TopupEligibilityExpired    = 4
	TopupDisbursed             = 5
)

var TopUpStatusIntToStrMap = map[int]string{
	0: "Ineligibile",
	1: "Eligibility Determined",
	2: "Journey Started",
	3: "Disqualified",
	4: "Eligibility Expired",
	5: "Disbursed",
}

const LendingGenericTaskQueue = "lending_middleware_queue_v1"
const DashboardTaskQueue = "dashboard_queue_v1"
const WebhookTaskQueue = "webhook_task_queue_v1"
const PFLTaskQueue = "pfl_queue_v1"
const ABFLTaskQueue = "abfl_queue_v1"
const IIFLTaskQueue = "iifl_queue_v1"
const TDLTaskQueue = "tdl_queue_v1"
const TSMTaskQueue = "temporal_state_management_task_queue"

const (
	WorkflowInitiated = "workflow_initiated"

	// BRE events
	ToSales    = "move_to_sales"
	ToRCU      = "move_to_rcu"
	ToCreditL1 = "move_to_credit_l1"
	ToCreditL2 = "move_to_credit_l2"
	ToCreditL3 = "move_to_credit_l3"
	ToCreditL4 = "move_to_credit_l4"
	ToCreditL5 = "move_to_credit_l5"
	ToCreditL6 = "move_to_credit_l6"
	ToCreditL7 = "move_to_credit_l7"

	// ABFLWFInitiatedAction           = "abfl_initiate_wf"
	// ABFLWFApproveKYCAction          = "abfl_approve_kyc"
	// ABFLWFApproveBusinessDocsAction = "abfl_approve_business_docs"
	// ABFLWFMoveToRADAction           = "abfl_move_to_rad"
	// ABFLWFMoveToVKYCAction          = "abfl_move_to_vkyc"
	// ABFLWFRADCompleteAction         = "abfl_rad_complete"
	// ABFLWFVKYCCompleteAction        = "abfl_vkyc_complete"
	// ABFLWFRejectKYCAction           = "abfl_reject_kyc"
	// ABFLWFSendEmailAction           = "email_sent"

	// KYC events
	RequestAdditionalDocumentsEvent = "request_additional_documents"
	RequestVKYCReportEvent          = "request_vkyc_report"
	UploadAdditionalDocumentsEvent  = "upload_additional_documents"
	UploadVKYCReportEvent           = "upload_vkyc_report"
	ApprovePersonalKYCEvent         = "approve_personal_kyc"
	ApproveBusinessKYCEvent         = "approve_business_kyc"
	RejectEvent                     = "reject"
)

var KYCWFActionsMap = map[string]string{
	ABFLWFApproveKYCAction:          ApprovePersonalKYCEvent,
	ABFLWFApproveBusinessDocsAction: ApproveBusinessKYCEvent,
	ABFLWFMoveToRADAction:           RequestAdditionalDocumentsEvent,
	ABFLWFMoveToVKYCAction:          RequestVKYCReportEvent,
	ABFLWFRADCompleteAction:         UploadAdditionalDocumentsEvent,
	ABFLWFVKYCCompleteAction:        UploadVKYCReportEvent,
	ABFLWFRejectKYCAction:           RejectEvent,
}

var BREValidEventTypes = []string{ToSales, ToRCU, ToCreditL1, ToCreditL2, ToCreditL3, ToCreditL4}

var WFGroupMap = map[string]string{
	ToSales:    "ABFL Sales",
	ToRCU:      "RCU",
	ToCreditL1: "Credit L1",
	ToCreditL2: "Credit L2",
	ToCreditL3: "Credit L3",
	ToCreditL4: "Credit L4",
	ToCreditL5: "Credit L5",
	ToCreditL6: "Credit L6",
	ToCreditL7: "Credit L7",
}

var GroupEventTypeMap = map[string]string{
	"ABFL Sales": ToSales,
	"RCU":        ToRCU,
	"Credit L1":  ToCreditL1,
	"Credit L2":  ToCreditL2,
	"Credit L3":  ToCreditL3,
	"Credit L4":  ToCreditL4,
	"Credit L5":  ToCreditL5,
	"Credit L6":  ToCreditL6,
	"Credit L7":  ToCreditL7,
}

const (
	BureauSourceCibil    = "cibil"
	BureauSourceExperian = "experian"
)

const (
	RunnerPersonalKYCApprovedKey = "personalKYCApproved"
	RunnerBusinessKYCApprovedKey = "businessKYCApproved"
)

const IIFLSmeBrmServiceName = "iifl_sme_brm"
const AuthModeMobileOTP = "mobile_otp"

const (
	BankConnectKeyCategoryOrganic             = "organic"
	BankConnectKeyCategoryDSA                 = "dsa"
	BankConnectKeyCategoryEFPartners          = "ef-partners"
	BankConnectKeyCategoryAPIPartners         = "api-partners"
	BankConnectKeyCategoryMarketplacePartners = "marketplace-partners"
)

const (
	UserTypePrimaryApplicant = "primary_applicant"
	UserTypeCoApplicant      = "co_applicant"
	UserTypeMarketplaceUser  = "marketplace_user"
)

var EntityTypes = []string{EntityTypeCustomer, EntityTypeBorrower, EntityTypeExternal, EntityTypeSystem, EntityTypeSourcingEntity, EntityTypeDashboardUser, EntityTypeLenderUser}

const (
	ServerTypeApiServer = "apis"
	ServerTypeApiWorker = "worker"
)

const (
	Topup         = "topup"
	TopupAncestor = "top_up_ancestor"
)

// vkyc pvt limited
const (
	VKycInitiated               = "vkyc_session_initiated"
	VkycInvokedStatus           = "invoked"
	VKycStatusWebhookPrefix     = "vkyc_webhook_"
	VkycWebhookApproved         = "vkyc_webhook_approved"
	VkycWebhookRejected         = "vkyc_webhook_rejected"
	VkycMobileMessageRetryCount = 3
)

const (
	EducationTypeCA          = "CA"
	EducationTypeDoctor      = "Doctor"
	EducationTypeMasters     = "Masters"
	EducationTypeGraduate    = "Graduate"
	EducationTypeNonGraduate = "Non-graduate"
)

const (
	UpdateUserDetails    = "UPDATE_USER_DETAILS"
	UserBusinessLocation = "BUSINESS_LOCATION"
)

var EducationalQualificationTypes = []string{EducationTypeCA, EducationTypeDoctor, EducationTypeMasters, EducationTypeGraduate, EducationTypeNonGraduate}

var ApplicationStatusToLoanStatusMap = map[string]int{
	"LoanStatusFresh":        LoanStatusFresh,
	"LoanStatusDetails":      LoanStatusDetails,
	"LoanStatusLoanApproved": LoanStatusLoanApproved,
	"LoanStatusCancelled":    LoanStatusCancelled,
	"LoanStatusLoanRejected": LoanStatusLoanRejected,
	"LoanStatusESign":        LoanStatusESign,
	"LoanStatusDisbursed":    LoanStatusDisbursed,
	"LoanStatusClosed":       LoanStatusClosed,
	"LoanStatusBankAdded":    LoanStatusBankAdded,
}

var KYCstringToIntMap = map[string]int{
	"LoanKYCStatusNotStarted":     LoanKYCStatusNotStarted,
	"LoanKYCStatusDocProcessing":  LoanKYCStatusDocProcessing,
	"LoanKYCStatusDocApproved":    LoanKYCStatusDocApproved,
	"LoanKYCStatusDocRejected":    LoanKYCStatusDocRejected,
	"LoanKYCStatusBankApproved":   LoanKYCStatusBankApproved,
	"LoanKYCStatusBankRejected":   LoanKYCStatusBankRejected,
	"LoanKYCStatusBankProcessing": LoanKYCStatusBankProcessing,
	"LoanKYCStatusUnderReview":    LoanKYCStatusUnderReview,
	"LoanKYCStatusPreAgreement":   LoanKYCStatusPreAgreement,
}

var ConstitutionMap = map[string]string{
	"Public Limited Company":  PublicLimited,
	"Private Limited Company": PrivateLimited,
	"Partnership Firm":        Partnership,
	"Proprietorship Firm":     Proprietorship,
	"Proprietary":             Proprietorship,
	Proprietorship:            Proprietorship,
	Partnership:               Partnership,
	PrivateLimited:            PrivateLimited,
	PublicLimited:             PublicLimited,
	LimitedLiability:          LimitedLiability,
}

const (
	OKYCTemporaliseFlag             = "okyc_temporalise_flow"
	FeatureRolloutCashfreeRepayment = "feature_rollout_cashfree_repayment_v2"
	FeatureRolloutBulkPresentation  = "feature_rollout_bulk_presentation"
	ABFLConstitutionFeatureFlagKey  = "abfl_constitution_flow"
	VKYCFlowFlag                    = "vkyc_flow_v1"
	UANFlowFlag                     = "uan_flow_v1"
	ABFLHunterFeatureFlagKey        = "abfl_hunter_flow"
	HunterFlowKey                   = "hunter_flow_key"
	ABFLBankFraudKey                = "abfl_bank_fraud"
	BankFraudFlag                   = "bank_fraud_flag"
	ABFLRiskRatingKey               = "abfl_risk_rating"
	ABFLLeiFlow                     = "abfl_lei_flow"
	RiskRatingFlag                  = "risk_rating_flag"
	LeiExposureFlag                 = "lei_exposure_flag"
	FeatureBankAAUnmaskedAccountNum = "feature_bank_aa_unmasked_account_num" // Flag denotes feature where bank account number recieved in AA data needs to be unmasked for certain entities
)

const (
	BlacklistedEvents = "BLACKLIST"
	WhitelistedEvents = "WHITELIST"
	Default           = "DEFAULT"
)

var NiroGenericErrors = []string{"OFFER_USED", "REJECTED", "NO_DETAILS_FOUND", "INVALID_LOAN_ID"}

var NiroGenericErrorsMap = map[string]string{
	"OFFER_USED":       "Offer is already used",
	"REJECTED":         "Lender rejected",
	"NO_DETAILS_FOUND": "Invalid User",
	"INVALID_LOAN_ID":  "Incorrect Details",
}

const MarkerplaceCustomerIDPrefix = "MPLC"
const LendingKartDashboardURL = "https://www.lendingkart.com/dashboard/auth"

const (
	FileTypeHTML = "html"
	FileTypePDF  = "pdf"
)

// Chargne name constants
const (
	ChargeNameProcessingFee = "processingFee"
)
const (
	HunterClearedStatus = "Cleared"
)

const InsuranceAmountSelectorCheckboxType = "checkbox"
const InsuranceAmountSelectorSliderType = "slider"
const InsuranceAmountSelectorNoneType = "None"

const InsuranceAmountSelector = `
{
  "insuranceAmountSelector": [
    {
      "selectionType": "checkbox",
      "minimumAmount": "2500",
      "maximumAmount": "15000",
      "allowedAmountValuesWithGST": [
        "2500",
        "5000",
        "10000",
        "15000"
      ],
     "allowedAmountValuesWithoutGST": [
        "2118.64",
        "4237.28",
        "8474.57",
        "12711.86"
      ],
	  "insuranceGST": "18"
    },
    {
      "selectionType": "slider",
      "minimumAmount": "0",
      "maximumAmount": "100000"
    }
  ]
}
`

var StatusesAndSubStatusesToBeSkipped = []string{FibeStatusKYCPending, FibeStatusBankStatement, FibeStatusRejected, "expired", InvalidCallbackType, NoLoanActiveForUser, ErrorLoanApplicationInTerminalState, FibeStatusUnderReview}

const IHO = "IHO"

const MoneyControlNameMatchScoreThreshold = 70
const ABFLPLAddressCompareThreshold = 70

const (
	ABFLBLDropDownType              = "abfl_bl_business_details"
	IIFLDropDownType                = "iifl_bl_business_details"
	IndustrySubIndustryDropDownName = "industry_sub_industry"
)
const EmptyString = ""

const ErrorLoanApplicationInTerminalState = "loan application is in terminal state"
const DecisionAPIFailed = "decision API failed"

// reject reason
const ErrorLenderAPIFailed = "lender API failed"
const ErrorPincodeNotFound = "pincode not found in master"
const ErrorBREFailed = "BRE failed"
const ErrorNoActiveOffers = "no active offers"
const InvalidCallbackType = "invalid callback type"
const NoLoanActiveForUser = "no loan application active for this user"
const ErrorUserRejectedFromBooster = "user has no offer from bureau and booster"
const UserInactivity = "user inactivity"
const ErrorPolicyRejected = "policy_rejected"

const (
	ApprovalTypePreApproved  = "PA"
	ApprovalTypePreQualified = "PQ"
)

// decentro udhyam details
const (
	MFLUdyamDecentroFetchConsentPurpose = "Fetch Udyam details for MFL"
	UdyamDecentroDateFormat             = "02/01/2006"
)

const (
	ActivityLogTimestampFormat = "2006-01-02 15:04:05.000000"
)

const (
	TransposeTypePreApproved = "pre_approved"
	TransposeTypeInitial     = "initial"
	TransposeTypeFinal       = "final"
)

const (
	ErrUserRiskRatingNotAvailable      = "User risk rating is not available"
	ErrBusinessRiskRatingNotAvailable  = "Business risk rating is not available"
	ErrGettingUserRiskRating           = "Error retrieving user risk rating"
	ErrGettingBusinessRiskRating       = "Error retrieving business risk rating"
	ErrGettingAggregateExposureDetails = "Error retrieving aggregate exposure details"
	ErrAggregateDetailsNotAvailable    = "LEI response not updated"
)

// constants for offer expiry in moneycontrol
const (
	OfferExpiryRemainingDays = 7
	FibeExpiryDays           = 90
	CasheExpiryDays          = 30
	LandTExpiryDays          = 30
	ABFLPLExpiryDays         = 30
	NiroExpiryDays           = 45
	ABFLPLCancellationDays   = 60
	PrefrExpiryDays          = 30
	RingExpiryDays           = 30
	BajajExpiryDays          = 30
)

const (
	EvalCodeAbflDeviationNonProd = "abf_bus_abf_dev_rIe"
	EvalCodeAbflDeviationProd    = "abf_bus_abf_dev_rnp"
	EvalCodeAbflSourceNonProd    = "ABFL_v1"
	EvalCodeAbflSourceProd       = "deviation_policy_bl"
	ABFLDeviationPolicy          = "abfl_deviation_policy"
)

const (
	ProgramIDPfl      = "f193a183-de4f-4419-9b6c-b63ada2e7cea"
	ProgramIDPflPrime = "43c20808-e4ea-4800-a155-c74ff104c937"
)

const (
	LenderDropdownActionReplace = "replace"
	LenderDropdownActionAppend  = "append"
	LenderDropDownActionRemove  = "remove"
)

// constants for accept offer thresholds
const (
	AcceptOfferMaxInterestRate     = 36
	AcceptOfferMinInterestRate     = 24
	AcceptOfferMaxProcessingFeePct = 5.0 // AcceptOffer Max Processing Fee Percentage
	AcceptOfferMinProcessingFeePct = 2.0 // AcceptOffer Min Processing Fee Percentage
)

const (
	VyaparMitra = "Vyapar Mitra"
	BandhanPlus = "Bandhan Plus"
)

const (
	SegmentPrime = "PRIME"
)

const (
	UserDynamicInfoOfficeAddressState = "officeAddressState"
	TaskMetaDataComment               = "comment"
	TaskMetaDataSubStatus             = "subStatus"
	TaskMetaDataEmploymentType        = "employmentType"
)

var MFLBLInstallmentTypeToScheme = map[string]string{
	"EDI": VyaparMitra,
	"EMI": BandhanPlus,
}

var MFLBLSchemeToInstallmentType = map[string]string{
	VyaparMitra: "EDI",
	BandhanPlus: "EMI",
}

var LenderToInstallmentTypeToScheme = map[string]map[string]string{
	MFLBLID: MFLBLInstallmentTypeToScheme,
}

// types of disbursal called for tradeline API
const (
	DisbursalStatusOpen       = "OPEN"
	DisbursalStatusClosed     = "CLOSED"
	DisbursalStatusForeClosed = "FORECLOSED"
	DisbursalStatusActive     = "ACTIVE"
)

// Lisa service
const (
	ApplicationStatusService = "APPLICATIONSTATUS"
)

// Redirection channel name and source
const (
	ChannelTata       = "TATA_NEU"
	ChannelSourceRing = "RING_WEB"
	ChannelMC         = "MONEY_CONTROL"
	ChannelDSA        = "DSA"
)

var LenderApplicationCancellationMapping = map[string]int{
	PrefrMCID: 30,
	CasheMCID: 30,
	RingMCID:  30,
	LandTID:   30,
	FibeID:    30,
	NiroID:    45,
	ABFLPLID:  60,
}

const (
	MasterTaskCreditApprovalABFL           = "730b5c0a-541a-4dcb-ab10-0e4086c0bd5d"
	MasterTaskCreditTaskTypeABFL           = "8f4996d0-940b-4be9-8b77-aa55c82337e1"
	CreditApprovalDefaultPendingStatusABFL = "844ae962-39b5-4e13-876f-24ddf639ea10"
	MasterTaskRCUApprovalABFL              = "875cb2c6-11e4-473e-a4cb-3c28339e8131"
	MasterTaskRCUTaskTypeABFL              = "3c943a39-4a3b-41fe-b13c-61acce180a54"
	RCUApprovalDefaultPendingStatusABFL    = "1fcf0c07-60b6-428c-9660-db2b795eb4d5"
	CreateTaskEvent                        = "create_task"
	RCUApprovalApprovalStatusABFL          = "3170fece-c1bb-4692-82f4-0b5be9f71372"
	RCUApprovalRejectedStatusABFL          = "ba5dcad5-c842-42f0-8cb6-86ec709f3462"
	CreditApprovalApprovalStatusABFL       = "5b23d6fa-c87d-425a-877a-9612236d34e4"
	CreditApprovalRejectedStatusABFL       = "b7376476-4d45-42ff-8f78-0d9243485440"
)

const (
	PANVendorHyperverge  = "hyperverge"
	PANVendorGridlines   = "gridlines"
	PANVendorZoop        = "zoop"
	PANFetchTypeExtended = "pan_extended"
	PANFetchTypeName     = "name_from_pan"
	PANVerification      = "pan_verification"
)

const BulkLoanClosure = "bulk_loan_closure"

const (
	Credit_Assignment_Questionnaire_Check_ResourceName = "credit_assignment_questionnaire_check"
	Credit_Assignment_Questionnaire_Check_Flag         = "credit_assignment_questionnaire_check"
)

// pfl callback types
const (
	DigilockerInitType        = "DigilockerInitService"
	DigilockerStatusType      = "DigilockerStatusService"
	KycValidationType         = "KycValidationService"
	EsignStatusType           = "EsignStatusService"
	LoanBookingType           = "LoanBookingService"
	PaymentStatusType         = "PaymentStatusService"
	RejectionStatusType       = "RejectionStatusService"
	InitialOfferServiceType   = "InitialOfferCallback"
	FinalOfferServiceType     = "FinalOfferCallback"
	PerfiosJourneyServiceType = "PerfiosCallback"
)

// pfl redirect urls
const (
	KYCRredirectUrl   = "v1/services/kycRedirectSM"
	EsignRredirectUrl = "v1/services/leegalityRedirectSM"
)

const (
	PFLPennyDropAccountIsValidCodeType       = "ACCOUNT_IS_VALID"
	PFLPennyDropMaximumAttemptExceedCodeType = "MAXIMUM_ATTEMPT_EXCEED"
	PFLPennyDropNameMatchFailedCodeType      = "NAME_MATCH_FAILED"
	PFLPennyDropValidationErrorCodeType      = "VALIDATION_ERROR"
	PFLPennyDropInvalidBankAccountCodeType   = "INVALID_BANK_ACCOUNT"
	PFLPennyDropFailedAtBankCodeType         = "FAILED_AT_BANK"
	PFLPennyDropNACodeType                   = "NA"
)

// Module Names
const (
	ModuleNameBRE1 = "BRE_1"
	ModuleNameBRE2 = "BRE_2"
)

const (
	APISourcePLTDL = "pl_tdl"
)

const (
	BILBankingDeviationKey = "all_devaitions_banking"
	BILBureauDeviationKey  = "all_deviations_bureau"
)

// Mapping for monthly income
var MonthlyIncomeRangeMap = map[string]string{
	"Less Than ₹ 20,000":    "20000",
	"₹ 20,000 - ₹ 25,000":   "25000",
	"₹ 25,000 - ₹ 35,000":   "35000",
	"₹ 35,000 - ₹ 50,000":   "50000",
	"₹ 50,000 - ₹ 75,000":   "75000",
	"₹ 75,000 - ₹ 1 Lac.":   "100000",
	"₹ 1 Lac. - ₹ 2 Lac.":   "200000",
	"Greater Than ₹ 2 Lac.": "250000",
}

// Mapping for loan amount
var LoanAmountRangeMap = map[string]string{
	"Upto ₹ 1 Lac.":          "100000",
	"₹ 1 Lac. - ₹ 3 Lac.":    "300000",
	"₹ 3 Lac. - ₹ 5 Lac.":    "500000",
	"₹ 5 Lac. - ₹ 7 Lac.":    "700000",
	"₹ 7 Lac. - ₹ 10 Lac.":   "1000000",
	"Greater Than ₹ 10 Lac.": "1500000",
}

var OndcLenderMapping = map[string]string{
	"ONDCBAJAJ": OndcBajajID,
	"ONDCFIBE":  OndcFibeID,
}

const OndcLender = "ondc_lender"

// MFLDailyReportFileRoot s3 file root for reports
const MFLDailyReportFileRoot = "a32f8aff-b513-45bd-9dd1-86e89f2aa77b/"

// intent constants
const (
	IntentDigilocker  = "digilocker"
	IntentVKYC        = "vkyc"
	IntentNachInit    = "nach_init"
	IntentBankingInit = "banking_init"
)

const (
	RedirectSignalKYC       = "kyc_redirected"
	RedirectSignalVKYC      = "vkyc_redirected"
	RedirectSignalNach      = "enach_redirected"
	RedirectSignalBanking   = "banking_redirected"
	CompletedSignalDisbured = "disbursal_completed"
)

const (
	AdditionalDocs                  = "Additional Docs"
	AdditionalDocCategoryDocumentID = "8a7b3c1d-4e5f-6a2b-9c8d-7e6f5a4b3c2d"
	ABFL_NEW_STUL_FLAG              = "abfl_bl_non_higher_limit_dsa"
)

const (
	DynamicUserInfoPermanentAddressLine1   = "permanentAddressLine1"
	DynamicUserInfoPermanentAddressLine2   = "permanentAddressLine2"
	DynamicUserInfoPermanentAddressPincode = "permanentAddressPincode"
)

// Redgate service constants
const (
	CREDIT_LIMIT_APPROVEL_RESOURCE_NAME = "CREDIT_LIMIT_APPROVAL"
	VALIDATE_ACTION                     = "VALIDATE"
	LENDING_DASHBOARD_APP_ID            = "lending_dashboard"
)
