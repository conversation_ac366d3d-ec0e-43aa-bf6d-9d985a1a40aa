package userapimodulemapping

import "time"

type APIUserModuleMapping struct {
	UmmID        string    `db:"umm_id"`
	ModuleID     string    `db:"module_id"`
	ModuleName   string    `db:"module_name"`
	ModuleStatus int       `db:"module_status"`
	WorkflowStep int       `db:"workflow_step"`
	CreatedAt    time.Time `db:"created_at"`
}

type CreateOptions struct {
	SignalTSM bool `json:"signalTSM"`
}

// APIWorkflowData is the payload for creating a user API module mapping
// from the service and repository layer
type APIWorkflowData struct {
	UserID            string
	ModuleName        string
	WorkflowStep      int
	ModuleStatus      int
	LoanApplicationID string
	CreatedBy         string
}
