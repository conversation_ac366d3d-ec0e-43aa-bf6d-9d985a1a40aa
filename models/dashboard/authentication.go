package dashboard

import (
	"finbox/go-api/constants"
	"finbox/go-api/thirdparty/redgate"
	"finbox/go-api/utils/general"
	"fmt"
	"strings"
)

type InitiateSSORequest struct {
	Email          string `json:"email"`
	SSOProvider    string `json:"ssoProvider"`
	OrganizationID string `json:"organizationID"`
	LenderID       string `json:"lenderID"`
	ReturnURL      string `json:"returnURL"`
}

type SSOCallbackRequest struct {
	SessionID   string `json:"sessionID"`
	SSOProvider string `json:"ssoProvider"`

	/*
		With Keycloak based login (ABFL), this will contain the email ID. With Ory based login (MFL)
		it will be a larger string from which email ID should be extracted
	*/
	UserID         string `json:"userID"`
	OrganizationID string `json:"organizationID"`
	LenderID       string `json:"lenderID"`
}

func (req InitiateSSORequest) ToRedgateRequest() redgate.InitiateSSORequest {
	var redgateReq redgate.InitiateSSORequest
	if req.OrganizationID != "" {
		redgateReq = redgate.InitiateSSORequest{
			AppID:       constants.AppIDPlatformDashboard,
			AppClientID: req.OrganizationID,
			SSOProvider: req.SSOProvider,
			Email:       req.Email,
			ReturnURL:   req.ReturnURL,
		}
	} else if req.LenderID != "" {
		redgateReq = redgate.InitiateSSORequest{
			AppID:       constants.AppIdLendingDashboard,
			AppClientID: req.LenderID,
			SSOProvider: req.SSOProvider,
			Email:       req.Email,
			ReturnURL:   req.ReturnURL,
		}
	}
	if req.SSOProvider == constants.GENERIC_ORY {
		redgateReq.OrganizationID = req.OrganizationID
	}
	return redgateReq
}

func (req *SSOCallbackRequest) Validate() error {
	if req.OrganizationID == "" && req.LenderID == "" {
		return fmt.Errorf("both organizationID and lenderID are missing")
	}
	if req.OrganizationID != "" && req.LenderID != "" {
		return fmt.Errorf("only one of organizationID and lenderID can be set")
	}
	if req.OrganizationID != "" && !general.ValidateUUID(req.OrganizationID) {
		return fmt.Errorf("organizationID is invalid")
	}
	if req.LenderID != "" && !general.ValidateUUID(req.LenderID) {
		return fmt.Errorf("lenderID is invalid")
	}
	if req.SessionID == "" {
		return fmt.Errorf("sessionID is missing")
	}
	if req.UserID == "" {
		return fmt.Errorf("missing userID")
	}
	if req.OrganizationID == constants.MFLOrganizationID {
		if userID := extractMFLUserID(req.UserID); userID == "" {
			return fmt.Errorf("invalid userID")
		} else {
			req.UserID = userID
		}
	} else {
		if !general.ValidateEmail(req.UserID) {
			return fmt.Errorf("invalid userID, expected a valid email ID")
		}
	}
	return nil
}

func extractMFLUserID(input string) string {
	if !strings.Contains(input, "\\") {
		return ""
	}

	parts := strings.Split(input, "\\")

	if len(parts) < 2 {
		return ""
	}
	return parts[len(parts)-1]
}
