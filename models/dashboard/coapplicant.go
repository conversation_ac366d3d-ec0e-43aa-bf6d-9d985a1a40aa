package dashboard

type GetCoApplicantListRequest struct {
	LoanApplicationID string `json:"loanApplicationID"`
	UserID            string `json:"userID"`
}

type GetCoApplicantsKYCRequest struct {
	LoanApplicationID string `json:"loanApplicationID"`
}

// CoApplicantUserSessionRequest represents the request payload for co-applicant creation.
type CreateCoApplicantUserParamV2 struct {
	Mobile            string `json:"mobile"`
	PAN               string `json:"pan,omitempty"`
	Relation          string `json:"relation,omitempty"`
	DOB               string `json:"dob,omitempty"`
	LoanApplicationID string `json:"loanApplicationID"`
}
