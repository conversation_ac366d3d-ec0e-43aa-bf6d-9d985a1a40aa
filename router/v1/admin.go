package v1

import (
	servicesCont "finbox/go-api/controller/services"
	"finbox/go-api/responseHandler/responseCommon"

	userReq "finbox/go-api/requestHandler/user/v2"

	"github.com/go-chi/chi/v5"

	auth "finbox/go-api/authentication"
	adminCont "finbox/go-api/controller/admin"
	adminReq "finbox/go-api/requestHandler/admin"
)

// AdminRouter for /admin pattern
func AdminRouter(r chi.Router) {
	r.Route("/", adminRoutes)
}

func AdminConfigManagementRouter(r chi.Router) {

	r.With(
		auth.AdminAuthFilter,
		adminReq.GetResourcesReq,
		adminCont.GetConfigResourcesCont,
	).Get("/resources", responseCommon.GenericRes)

	r.With(
		auth.AdminAuthFilter,
		adminReq.GetConfigForResourceReq,
		adminCont.GetConfigForResourcesCont,
	).Get("/config", responseCommon.GenericRes)

	r.With(
		auth.AdminAuthFilter,
		adminReq.GetConfigKeysReq,
		adminCont.GetConfigKeysCont,
	).Get("/keys", responseCommon.GenericRes)

	r.With(
		auth.AdminAuthFilter,
		adminReq.CreateConfigResourceReq,
		adminCont.CreateConfigResourceCont,
	).Post("/resource", responseCommon.GenericRes)

	r.With(
		auth.AdminAuthFilter,
		adminReq.CreateConfigDataReq,
		adminCont.CreateConfigDataCont,
	).Post("/config", responseCommon.GenericRes)
}

// adminRoutes contain all the routes required for admin apis
func adminRoutes(r chi.Router) {

	r.With(
		auth.NonAuthFilter,
		adminReq.OktaLogin,
		adminCont.OktaLogin,
	).Post("/oktaAuth", responseCommon.GenericRes)

	r.With(
		auth.AdminAuthFilter,
		adminReq.GetCachedValueReq,
		adminCont.GetCachedValueCont,
	).Get("/getFromCache", responseCommon.GenericRes)

	r.With(
		auth.AdminAuthFilter,
		adminReq.ClearCachedValueReq,
		adminCont.ClearCachedValueCont,
	).Post("/cache/clear", responseCommon.GenericRes)

	r.With(
		auth.AdminAuthFilter,
		adminReq.UpdateCustomerStatusReq,
		servicesCont.UpdateCustomerStatusCont,
	).Post("/cashe/updateCustomerStatus", responseCommon.GenericRes)

	r.With(
		auth.AdminAuthFilter,
		adminReq.UpdateRolloutReq,
		adminCont.UpdateRolloutsCont,
	).Post("/octopus/updateRollout", responseCommon.GenericRes)

	r.With(
		auth.AdminAuthFilter,
		adminReq.UdyamServiceParsingErrorFixReq,
		adminCont.UdyamServiceParsingErrorFixCont,
	).Post("/udyam/ParsingErrorDataFix", responseCommon.GenericRes)

	r.With(
		auth.AdminAuthFilter,
		adminReq.SendEventsWebhookReq,
		adminCont.SendEventsWebhookController,
	).Post("/webhook/send", responseCommon.GenericRes)

	r.With(
		auth.AdminAuthFilter,
		adminReq.SendFailedEventsReqCtrlr,
		adminCont.SendFailedEventsWebhookController,
	).Post("/failed_webhook/send", responseCommon.GenericRes)

	r.With(
		auth.AdminAuthFilter,
		adminReq.WhitelistIPReq,
		adminCont.WhitelistIPCont,
	).Post("/whitelistIP", responseCommon.GenericRes)

	r.With(
		auth.AdminAuthFilter,
		adminReq.ResetTSMWorkflowReq,
		adminCont.ResetTSMWorkflowCont,
	).Post("/resetTSMWorkflow", responseCommon.GenericRes)

	r.With(
		auth.AirflowOrAdminAuthFilter,
		adminReq.TriggerBulkActivityReq,
		adminCont.TriggerBulkActivityCont,
	).Post("/batched-workflow-by-type", responseCommon.GenericResV3)

	r.With(
		auth.AirflowOrAdminAuthFilter,
		userReq.APIStackGetResultReq,
		adminCont.GetBulkActivityResultCont,
	).Get("/batched-workflow-by-type", responseCommon.GenericRes)

	r.Route("/configService", AdminConfigManagementRouter)

	r.With(
		auth.AdminAuthFilter,
		adminCont.MergeSchemaJSON,
	).Post("/jsonschema/merge", responseCommon.GenericResV3)

	r.With(
		auth.AdminAuthFilter,
		adminCont.VisualiseSchemaJSON,
	).Post("/jsonschema/visualise", responseCommon.GenericTextResponse)
}
