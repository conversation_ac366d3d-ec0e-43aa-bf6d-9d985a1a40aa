package v1

import (
	adminCont "finbox/go-api/controller/admin"
	adminReq "finbox/go-api/requestHandler/admin"
	kycRes "finbox/go-api/responseHandler/kyc"

	"github.com/go-chi/chi/v5"

	auth "finbox/go-api/authentication"
	servicesCont "finbox/go-api/controller/services"
	"finbox/go-api/functions/lenders/tatacapital"
	"finbox/go-api/functions/services/bankconnect"
	"finbox/go-api/functions/webhooks"
	servicesReq "finbox/go-api/requestHandler/services"
	loanRes "finbox/go-api/responseHandler/loan"
	"finbox/go-api/responseHandler/responseCommon"
	servicesRes "finbox/go-api/responseHandler/services"
)

// Router for /services pattern
func ServicesRouter(r chi.Router) {
	r.Route("/", servicesRoutes)
}

// add new routes here
func servicesRoutes(r chi.Router) {

	r.With(
		auth.AuthFilter,
		servicesCont.UploadMediaCont,
	).Post("/uploadMedia", responseCommon.GenericRes)

	r.With(
		auth.AuthFilter,
		servicesReq.ValidateDocumentReq,
		servicesCont.ValidateDocumentCont,
	).Get("/validateDocument", responseCommon.GenericRes)

	r.With(
		auth.LenderAuthFilter,
		servicesCont.UploadLenderMediaCont,
	).Post("/uploadLenderMedia", responseCommon.GenericRes)

	r.With(
		auth.AuthFilter,
		servicesReq.GetMediaReq,
		servicesCont.GetMediaCont,
	).Get("/getMedia", responseCommon.GenericRes)

	r.With(
		auth.NonAuthFilter,
		servicesCont.HCINWebhookCont,
	).Post("/hcinNotification", responseCommon.GenericRes)

	r.With(
		auth.NonAuthFilter,
		servicesCont.IIFLWebhookCont,
	).Post("/iiflHook", servicesRes.IIFLRes)

	r.With(
		auth.NonAuthFilter,
		servicesCont.OneMuthootWebhookCont,
	).Post("/oneMuthootHook", responseCommon.GenericRes)

	r.With(
		auth.ServerAuthFilterV2,
		servicesReq.BulkUserCreationReq,
		servicesCont.BulkUserCreationCont,
	).Post("/bulkUserCreation", responseCommon.GenericRes)

	r.With(
		servicesReq.DocHTMLReq,
		servicesCont.DocHTMLCont,
	).Get("/docHTML", servicesRes.HTMLRes)

	r.With(
		auth.AuthFilter,
		servicesCont.DisbursedContentCont,
	).Get("/getDisbursedContent", responseCommon.GenericRes)

	r.With(
		auth.AuthFilter,
		servicesReq.SearchIFSCReq,
		servicesCont.SearchIFSCCont,
	).Get("/searchIFSC", responseCommon.GenericRes)

	r.With(
		auth.AuthFilter,
		servicesReq.IFSCStatesReq,
		servicesCont.IFSCStatesCont,
	).Get("/ifsc/states", responseCommon.GenericRes)

	r.With(
		auth.AuthFilter,
		servicesReq.IFSCCitiesReq,
		servicesCont.IFSCCitiesCont,
	).Get("/ifsc/cities", responseCommon.GenericRes)

	r.With(
		auth.AuthFilter,
		servicesReq.IFSCBranchesReq,
		servicesCont.IFSCBranchesCont,
	).Get("/ifsc/branches", responseCommon.GenericRes)

	r.With(
		auth.NonAuthFilter,
		servicesReq.SearchCompanyNameReq,
		servicesCont.SearchCompanyNameCont,
	).Get("/searchCompanyName", responseCommon.GenericRes)

	r.With(
		auth.AuthFilter,
		servicesCont.GetBanksCont,
	).Get("/getBanks", responseCommon.GenericRes)

	r.With(
		auth.AuthFilter,
		servicesCont.RetryPipelineCont,
	).Post("/retry", responseCommon.GenericRes)

	// [Deprecated] Leegality webhook version 2.3 integration
	r.With(
		servicesCont.LeegalityESignCallback,
	).Post("/leegality/webhook", responseCommon.GenericRes)

	// Leegality webhook version 2.5 integration
	r.With(
		servicesCont.LeegalityESignCallbackV2,
	).Post("/leegality/webhookV2", responseCommon.GenericRes)

	r.With(
		auth.ServerAuthFilter,
		servicesReq.PennyDropReq,
		servicesCont.PennyDropCont,
	).Post("/penny", responseCommon.GenericRes)

	r.With(
		auth.ServerAuthFilter,
		servicesReq.PennyDropReq,
		servicesCont.SubmitBankDetailsCont,
	).Post("/submitBankDetails", responseCommon.GenericRes)

	r.With(
		auth.ServerAuthFilter,
		servicesReq.MarkPennyDropReq,
		servicesCont.ValidateBankDetailsCont,
	).Post("/validateBankDetails", responseCommon.GenericRes)

	r.With(
		auth.NonAuthFilter,
		servicesCont.SaraloanApplicationStatusWebhookCont,
	).Post("/saraloanApplicationStatusWebhook", responseCommon.GenericRes)

	r.With(
		auth.NonAuthFilter,
		servicesReq.AxioWebhookReq,
		servicesCont.AxioRepaymentDetailsWebhookCont,
	).Post("/axio/hook/repayment", responseCommon.GenericRes)

	r.With(
		auth.NonAuthFilter,
		servicesReq.AxioWebhookReq,
		servicesCont.AxioAccountDetailsWebhookCont,
	).Post("/axio/hook/accountDetails", responseCommon.GenericRes)

	r.With(
		auth.NonAuthFilter,
		servicesReq.AxioWebhookReq,
		servicesCont.AxioUserJourneyUpdatesWebhookCont,
	).Post("/axio/hook/userJourneyUpdates", responseCommon.GenericRes)

	r.With(
		auth.NonAuthFilter,
		servicesReq.AxioWebhookReq,
		servicesCont.AxioWebhookIndependentSICont,
	).Post("/axio/hook/independentSI", responseCommon.GenericRes)

	r.With(
		auth.NonAuthFilter,
		servicesReq.AxioRedirectReq,
		servicesCont.AxioKYCRedirectCont,
	).Post("/axio/redirect/KYC", responseCommon.GenericRes)

	r.With(
		auth.NonAuthFilter,
		servicesReq.AxioRedirectReq,
		servicesCont.AxioNACHRedirectCont,
	).Post("/axio/redirect/NACH", responseCommon.GenericRes)

	r.With(
		auth.NonAuthFilter,
		servicesReq.AxioRedirectReq,
		servicesCont.AxioRepaymentRedirectCont,
	).Post("/axio/redirect/repayment", responseCommon.GenericRes)

	r.With(
		auth.NonAuthFilter,
		servicesCont.WesternCapitalWebhookCont,
	).Post("/westerncapitalWebhook", responseCommon.GenericRes)

	r.With(
		auth.NonAuthFilter,
		servicesCont.MintifiApplicationStatusCont,
	).Put("/applicationStatus", responseCommon.GenericRes)

	r.With(
		auth.NonAuthFilter,
		servicesCont.MintifiDrawdownStatusCont,
	).Put("/drawdownnStatus", responseCommon.GenericRes)

	r.With(
		auth.NonAuthFilter,
		servicesCont.ThirdPartyEmandateCallbackCont,
	).Get("/thirdPartyMandateCallback", loanRes.ENachHTMLCallbackRes)

	r.With(
		auth.NonAuthFilter,
		servicesCont.ThirdPartyESignCallbackCont,
	).Get("/thirdPartyEsignCallback", loanRes.ESignHTMLCallbackRes)

	r.With(
		auth.ServerAuthFilter,
		servicesCont.ClearCacheCont,
	).Post("/clearCache", responseCommon.GenericRes)

	r.With(
		auth.AirflowAuthFilter,
		servicesReq.SendReportReq,
		servicesCont.SendTransactionReportCont,
	).Post("/sendTransactionReport", responseCommon.GenericRes)

	r.With(
		auth.AirflowAuthFilter,
		servicesReq.SendReportReq,
		servicesCont.SendRefundReportCont,
	).Post("/sendRefundReport", responseCommon.GenericRes)

	r.With(
		auth.AirflowAuthFilter,
		servicesReq.SendReportReq,
		servicesCont.GetTransactionRecordsCont,
	).Post("/getTransactions", responseCommon.GenericRes)

	r.With(
		auth.AirflowAuthFilter,
		servicesReq.SendReportReq,
		servicesCont.GetRefundRecordsCont,
	).Post("/getRefunds", responseCommon.GenericRes)

	r.With(
		auth.NonAuthFilter,
		servicesReq.AxisCallbackReq,
		servicesCont.AxisCallbackCont,
	).Post("/axis/hook/kyc", servicesRes.AxisCallbackResponse)

	r.With(
		auth.AirflowAuthFilter,
		servicesReq.DisburseLoanReq,
		servicesCont.DisburseLoanCont,
	).Post("/disburseLoan", responseCommon.GenericRes)

	r.With(
		auth.NonAuthFilter,
		servicesCont.OctopusHookCont,
	).Post("/octopusHook", responseCommon.GenericRes)

	r.With(
		servicesCont.CamspayEmandateCallbackCont,
	).Get("/camspayEmandateCallback", loanRes.ENachHTMLCallbackRes)

	r.With(
		servicesCont.OneFinEmandateCallbackCont,
	).Get("/onefinEmandateCallback", loanRes.ENachHTMLCallbackRes)

	r.With(
		servicesCont.CamspayEmandateCallbackCont,
	).Get("/iframeCamspayEmandateCallback", loanRes.IframeCampspayENachHTMLCallbackRes)

	r.With(
		auth.NonAuthFilter, // TODO add auth
		servicesReq.ENachRegisterReq,
		servicesCont.CancelCamspayEnachCont,
	).Post("/cancelCamspayEnach", responseCommon.GenericRes)

	r.With(
		auth.NonAuthFilter,
		servicesReq.ActivityHookReq,
		servicesCont.ActivityHookCont,
	).Post("/activityHook", responseCommon.GenericRes)

	r.With(
		auth.NonAuthFilter,
		servicesCont.ThirdpartyMandateStatusCont,
	).Get("/thirdparty/mandateStatus", loanRes.RedirectPage)

	r.With(
		auth.AuthFilter,
		servicesCont.RPSSignalCont,
	).Get("/rpsSignal", responseCommon.GenericRes)

	r.With(
		auth.NonAuthFilter,
		servicesReq.KYCRedirectSMReq,
		servicesCont.KYCRedirectSMCont,
	).Get("/kycRedirectSM", kycRes.DigilockerHTMLRes)

	r.With(
		auth.NonAuthFilter,
		servicesCont.RedirectLeegalitySMCont,
	).Get("/leegalityRedirectSM", servicesRes.LeegalityRedirectURLRes)

	r.With(
		auth.NonAuthFilter,
		servicesCont.PFLSuperMoneyRedirectCont,
	).Get("/pfl/redirect", loanRes.RedirectPage)

	r.With(
		auth.ServerAuthFilter,
		servicesCont.PFLApplicationCallbackCont,
	).Post("/pfl/applicationStatus", responseCommon.GenericRes)

	r.With(
		auth.AirflowAuthFilter,
		servicesCont.KotakBLStatusUpdateCont,
	).Post("/kotak/bl/statusUpdate", responseCommon.GenericRes)

	r.With(
		auth.AirflowAuthFilter,
		servicesCont.LendingKartStatusUpdateCont,
	).Post("/lk/statusUpdate", responseCommon.GenericRes)

	r.With(
		auth.AuthFilter,
		servicesCont.TataReferralCont,
	).Get("/tata/referral", responseCommon.GenericRes)

	r.With(
		auth.NonAuthFilter,
		servicesCont.DMIAACallBackCont,
	).Get("/dmi/aaRedirect", responseCommon.GenericTextResponse)

	r.With(
		auth.NonAuthFilter,
		servicesCont.DMIKYCCallBackCont,
	).Get("/dmi/kycRedirect", responseCommon.GenericTextResponse)

	r.With(
		auth.NonAuthFilter,
		servicesCont.SMAACallBackCont,
	).Get("/sm/aaRedirect", responseCommon.GenericTextResponse)

	r.With(
		auth.NonAuthFilter,
		servicesCont.DMINACHCallBackCont,
	).Get("/dmi/nachRedirect", responseCommon.GenericTextResponse)

	r.With(
		auth.NonAuthFilter,
		servicesCont.MoneyControlConsumeInfo,
	).Post("/moneyControl/consumeInfo", responseCommon.GenericRes)

	r.With(
		auth.AdminAuthFilter,
		servicesCont.LISACallback,
	).Post("/lisa/webhook", responseCommon.GenericRes)

	r.With(
		auth.AirflowAuthFilter,
		servicesCont.MuthootBNPLClosureAmountUpdateCont,
	).Post("/muthoot/bnpl/chargesUpdate", responseCommon.GenericRes)

	r.With(
		auth.AirflowAuthFilter,
		servicesCont.DigioFetchAndUpdateUmrn,
	).Post("/digio/updateUmrn", responseCommon.GenericRes)

	r.With(
		auth.NonAuthFilter,
		servicesReq.GetLoanDetailsReq,
		servicesCont.RazorpayENACHRedirectCont,
	).Get("/razorpayRedirectHTML", loanRes.RazorpayENACHRedirectHTML)

	r.With(
		auth.NonAuthFilter,
		servicesReq.PayURedirectionReq,
		servicesCont.PayURedirectionCont,
	).Post("/payURedirectHTML", loanRes.HTTPRedirection)

	r.With(
		auth.NonAuthFilter,
		servicesReq.RazorpayCallbackReq,
		servicesCont.RazorpayCallbackCont,
	).Post("/razorpay/callback", loanRes.ENachHTMLCallbackRes)

	r.With(
		auth.NonAuthFilter,
		servicesCont.RazorpayWebhookCont,
	).Post("/razorpay/hook", responseCommon.GenericRes)

	r.With(
		auth.AuthFilter,
		servicesCont.ElectricityServiceProvidersCont,
	).Get("/electricityProviders", responseCommon.GenericRes)

	r.With(
		auth.AuthFilter,
		servicesReq.SubmitAdditionalDocsReq,
		servicesCont.SubmitAdditionalDocsCont,
	).Post("/additionalDocs", responseCommon.GenericRes)

	r.With(
		auth.AirflowAuthFilter,
		servicesCont.DisbursalStatusCont,
	).Post("/disbursal/status", responseCommon.GenericRes)

	r.With(
		auth.AirflowAuthFilter,
		servicesReq.PFLUserDataDumpReq,
		servicesCont.PFLCustomUserDataDumpCont,
	).Get("/pfl/customLeadDump", responseCommon.GenericRes)

	r.With(
		auth.AirflowAuthFilter,
		servicesReq.GenerateSignedURL,
		servicesCont.GenerateSignedURLForExportsCont,
	).Get("/generateSignedURL", responseCommon.GenericRes)

	r.With(auth.NonAuthFilter,
		servicesCont.FibeRedirectCont,
	).Get("/fibe/redirect", loanRes.RedirectPage)

	r.With(
		webhooks.LoggingMiddlewareGET(tatacapital.KYCCallback),
		servicesReq.JocataKYCReq,
		servicesCont.JocataKYCRedirectCont,
	).Get("/jocataKYCCallback", responseCommon.GenericRes)

	r.With(
		webhooks.LoggingMiddlewareGET(tatacapital.NACHCallback),
		servicesReq.JocataNACHReq,
		servicesCont.JocataNACHRedirectCont,
	).Get("/jocataNACHCallback", responseCommon.GenericRes)

	r.With(
		auth.ServerAuthFilterV2,
		webhooks.LoggingMiddlewarePOST(tatacapital.PennydropCallback),
		servicesReq.TCAPPennydropReq,
		servicesCont.TCAPPennydropCont,
	).Post("/tcap/pennydrop", servicesRes.JSONResponse)

	r.With(
		auth.ServerAuthFilterV2,
		webhooks.LoggingMiddlewarePOST(tatacapital.NameMatchCallback),
		servicesReq.TCAPNameMatchReq,
		servicesCont.TCAPNameMatchCont,
	).Post("/tcap/namematch", servicesRes.JSONResponse)

	r.With(
		auth.AirflowAuthFilter,
		servicesCont.TCAPCLosureRequests,
	).Post("/tcap/closureRequests", responseCommon.GenericRes)

	r.With(
		auth.AirflowAuthFilter,
		servicesReq.UserLoanReq,
		servicesCont.UpdateLoanStatus,
	).Post("/tcap/updateLoanStatus", responseCommon.GenericRes)

	r.With(
		auth.AirflowAuthFilter,
		servicesCont.UpdateLandTDisbursalDetailsCont,
	).Post("/landt/updateLoanStatus", responseCommon.GenericRes)

	r.With(
		auth.NonAuthFilter,
		servicesReq.KarzaGSTINHookReq,
		servicesCont.KarzaGSTINHookCont,
	).Post("/karza/hook", responseCommon.GenericRes)

	r.With(
		auth.AirflowAuthFilter,
		servicesCont.UpdateDisbursalDetailsCont,
	).Post("/cashe/updateDisbursalDetails", responseCommon.GenericRes)

	r.With(
		auth.AirflowAuthFilter,
		servicesReq.ExpireUserDailyReq,
		servicesCont.ExpireABFLUsersCont,
	).Post("/expireCustomUsers", responseCommon.GenericRes)

	r.With(
		auth.NonAuthFilter,
		webhooks.LoggingMiddlewarePOST(bankconnect.BankConnectWebhook),
		servicesCont.BankConnectWebhookListener,
	).Post("/bcHook", responseCommon.GenericRes)

	r.With(
		auth.AirflowAuthFilter,
		servicesCont.MuthootCreditLineUTRUpdateCont,
	).Post("/muthoot/updateCreditLineUTR", responseCommon.GenericRes)

	r.With(
		auth.AuthFilter,
		servicesReq.SearchBankReq,
		servicesCont.SearchBankNameCont,
	).Get("/searchBank", responseCommon.GenericRes)

	r.With(
		auth.AuthFilter,
		servicesReq.SearchBranchNameReq,
		servicesCont.SearchBranchNameCont,
	).Get("/searchBranch", responseCommon.GenericRes)

	r.With(
		auth.AirflowAuthFilter,
		servicesCont.TempMuthootCreditLineUTRUpdateCont,
	).Post("/muthoot/tempUpdateCreditLineUTR", responseCommon.GenericRes) // TODO deprecate

	r.With(
		auth.AirflowAuthFilter,
		servicesReq.GSTWaitRevertReq,
		servicesCont.GSTWaitRevertCont,
	).Post("/gstWaitRevert", responseCommon.GenericRes)

	r.With(
		auth.AuthFilter,
		servicesCont.SearchIndustryCont,
	).Get("/searchIndustry", responseCommon.GenericRes)

	r.With(
		auth.AuthFilter,
		servicesCont.SearchSubIndustryCont,
	).Get("/searchSubIndustry", responseCommon.GenericRes)

	r.With(
		auth.AuthFilter,
		servicesReq.SearcLenderDropDownReq,
		servicesCont.SearchLenderDropDownCont,
	).Post("/lenderDropdown/search", responseCommon.GenericRes)

	r.With(
		auth.AuthFilter,
		servicesReq.CallExternalAPIReq,
		servicesCont.CallExternalAPICont,
	).Post("/callExternalAPI", responseCommon.GenericRes)

	r.With(
		auth.AirflowAuthFilter,
		servicesReq.AbflLoanStatusUpdateReq,
		servicesCont.AbflLoanStatusUpdateCont,
	).Post("/abfl/loanStatusUpdate", responseCommon.GenericRes)

	r.With(
		auth.AuthFilter,
		servicesCont.ActivityEventCont,
	).Post("/activityEvent", responseCommon.GenericRes)

	r.With(
		auth.AirflowAuthFilter,
		servicesCont.TDLPLBringBackToMLOCont,
	).Post("/tdl/bringBackInActiveUsersToMLO", responseCommon.GenericRes)

	r.With(
		auth.AirflowAuthFilter,
		servicesCont.ArchiveTdlUsersCont,
	).Post("/tdl/archiveUsers", responseCommon.GenericRes)
	// TODO: remove this route, only for testing
	r.With(
		auth.AirflowAuthFilter,
		servicesCont.InsuranceGetStatusCont,
	).Post("/insurance/getStatus", responseCommon.GenericRes)

	r.With(
		auth.AirflowAuthFilter,
		servicesReq.BulkInsuranceGetStatusReq,
		servicesCont.BulkInsuranceGetStatusCont,
	).Post("/insurance/bulkGetStatus", responseCommon.GenericRes)

	r.With(
		auth.NonAuthFilter,
		servicesReq.GetDownloadableDocumentsReq,
		servicesCont.GetDownloadableDocumentsCont,
	).Get("/document/download", responseCommon.GenericRes)

	r.With(
		auth.ServerAuthFilterV2,
		servicesReq.GetDeDupeReq,
		servicesCont.DedupeCont,
	).Post("/dedupe", responseCommon.GenericResponse)

	r.With(
		auth.AirflowAuthFilter,
		servicesReq.TdlLoanStatusUpdateReq,
		servicesCont.UpdateLoanStatusTDL,
	).Post("/tdlplHDFC/updateLoanStatus", responseCommon.GenericRes)
	r.With(
		auth.AirflowAuthFilter,
		servicesCont.UpdateCasheDisbursalDetailsCont,
	).Post("/cashemc/updateLoanStatus", responseCommon.GenericRes)

	r.With(
		auth.AirflowAuthFilter,
		servicesCont.IndifiLoansStatusUpdateCont,
	).Post("/indifi/statusUpdate", responseCommon.GenericRes)

	r.With(
		auth.AirflowAuthFilter,
		servicesCont.FlexiLoansStatusUpdateCont,
	).Post("/flexi/statusUpdate", responseCommon.GenericRes)

	r.With(
		auth.AirflowAuthFilter,
		servicesReq.BulkInitiateNewApplicationReq,
		servicesCont.BulkInitiateNewApplicationCont,
	).Post("/bulkInitiateNewApplication", responseCommon.GenericRes)

	r.With(
		auth.AirflowAuthFilter,
		servicesCont.ArchiveTdlInactiveUsersCont,
	).Post("/tdl/archiveInactiveUsers", responseCommon.GenericRes)

	r.With(
		auth.AirflowAuthFilter,
		adminReq.TriggerBulkActivityReq,
		adminCont.TriggerBulkActivityCont,
	).Post("/batched-workflow-by-type", responseCommon.GenericResV3)

	r.With(
		auth.AirflowAuthFilter,
		adminCont.MFLReportDailySyncCont,
	).Get("/mfl-report-daily-sync", responseCommon.GenericResV3)

	r.With(
		auth.AuthFilter,
		servicesCont.TSMSignalRedirectionCont,
	).Post("/tsm/signal/redirect", responseCommon.GenericResponse)

	r.With(
		auth.NonAuthFilter,
		servicesCont.RedirectAxisAppStatusCont,
	).Get("/axisApplication", loanRes.RedirectPage)

	r.With(
		auth.NonAuthFilter,
		servicesCont.EmuhdraWebhookCont,
	).Post("/emudhra/webhook", responseCommon.GenericRes)
}
