package v1

import (
	"finbox/go-api/internal/app"
	lenderRes "finbox/go-api/responseHandler/lender"
	"finbox/go-api/responseHandler/responseCommon"

	"github.com/go-chi/chi/v5"

	auth "finbox/go-api/authentication"
	dashboardAuthorization "finbox/go-api/authorization/middleware/dashboard"
	dashboardContV2 "finbox/go-api/controller/dashboard"
	lenderCont "finbox/go-api/controller/lender"
	dashboardCont "finbox/go-api/controller/masterDashboard"
	paymentCont "finbox/go-api/controller/payment"
	creditLineReq "finbox/go-api/requestHandler/creditline"
	dashboardReqV2 "finbox/go-api/requestHandler/dashboard"
	dashboardRequestV2 "finbox/go-api/requestHandler/dashboard"
	lenderReq "finbox/go-api/requestHandler/lender"
	dashboardReq "finbox/go-api/requestHandler/masterDashboard"
	paymentReq "finbox/go-api/requestHandler/payment"
	servicesReq "finbox/go-api/requestHandler/services"
)

func MasterDashboardRouter(r chi.Router) {
	r.Route("/", masterDashboardRoutes)
}

func masterDashboardRoutes(r chi.Router) {
	r.With(
		auth.NonAuthFilter,
		dashboardReq.LoginReq,
		dashboardCont.LoginCont,
	).Post("/login", responseCommon.GenericRes)

	r.With(
		auth.NonAuthFilter,
		dashboardReq.SendLoginOTPOnEmailReq,
		dashboardCont.SendLoginOTPOnEmailCont,
	).Post("/sendLoginOTPOnEmail", responseCommon.GenericRes)

	r.With(
		auth.NonAuthFilter,
		dashboardReq.SendLoginOTPOnMobileReq,
		dashboardCont.SendLoginOTPOnMobileCont,
	).Post("/sendLoginOTPOnMobile", responseCommon.GenericRes)

	r.With(
		auth.MFAAuthFilter,
		dashboardCont.MFASetupCont,
	).Post("/mfaSetup", responseCommon.GenericRes)

	r.With(
		auth.MFAAuthFilter,
		dashboardReq.AuthenticateMFAReq,
		dashboardCont.AuthenticateMFACont,
	).Post("/authenticateMFA", responseCommon.GenericRes)

	r.With(
		auth.MFAAuthFilter,
		dashboardReq.RefetchMFADeatilsReq,
		dashboardCont.RefetchMFADeatilsCont,
	).Post("/refetchMFADetails", responseCommon.GenericRes)

	r.With(
		auth.NonAuthFilter,
		dashboardReq.MobileLoginSendOTPReq,
		dashboardCont.MobileLoginSendOTPCont,
	).Post("/loginOTP", responseCommon.GenericRes)

	r.With(
		auth.NonAuthFilter,
		dashboardReq.MobileVerifyOTPReq,
		dashboardCont.MobileOTPVerifyCont,
	).Post("/verifyOTP", responseCommon.GenericRes)

	r.With(
		auth.NonAuthFilter,
		dashboardReq.VerifyMobilePasswordReq,
		dashboardCont.VerifyMobilePasswordCont,
	).Post("/verifyMobilePassword", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardCont.GetPermissionsCont,
	).Get("/getPermissions", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.UserListReq,
		dashboardCont.UserListCont,
	).Get("/users", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.SearchOnlyUserListReq,
		dashboardCont.UserListCont,
	).Get("/searchOnlyUsers", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.GetLoanDetailsReq,
		dashboardCont.GetCreditLineDetailsCont,
	).Get("/getCreditLineDetails", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.GetLoanDetailsReq,
		dashboardCont.GetCreditLineTransactionsCont,
	).Get("/getCreditLineTransactions", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.UserProfileReq,
		dashboardCont.GetBusinessDetailsCont,
	).Get("/getBusinessDetails", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.UpdateDOBReq,
		dashboardCont.UpdateDOBCont,
	).Post("/updateDOB", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.UpdateCurrentAddressReq,
		dashboardCont.UpdateCurrentAddressCont,
	).Post("/updateCurrentAddress", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.LoanActivityReq,
		dashboardCont.LoanActivityCont,
	).Get("/loanActivity", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.UpdateWebhookURLReq,
		dashboardCont.UpdateWebhookURLCont,
	).Post("/updateWebhookURL", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardCont.GetWebhookURLCont,
	).Get("/getWebhookURL", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilterV2,
		dashboardReqV2.UpdateCreditLineTxnStatusReq,
		dashboardAuthorization.Authorization,
		dashboardContV2.UpdateCreditLineTxnStatusCont,
	).Post("/updateCreditLineTxnStatus", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.UpdateCreditLineTxnDetailsReq,
		dashboardCont.UpdateCreditLineTxnDetailsCont,
	).Post("/updateCreditLineTxnDetails", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilterV2,
		dashboardReqV2.UpdateCreditLineTxnDetailsReq,
		dashboardAuthorization.Authorization,
		dashboardContV2.UpdateCreditLineTxnDetailsCont,
	).Post("/updateCLTxnDetails", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.GetAuthLetterReq,
		dashboardCont.GetAuthLetterCont,
	).Get("/getAuthLetter", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.ApproveLoanKYCReq,
		dashboardCont.ApproveLoanKYCCont,
	).Post("/approveLoanKyc", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.GetLoanDetailsReq,
		dashboardCont.GetLoanKYCCont,
	).Get("/getLoanKYC", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.GetLoanDetailsReq,
		dashboardCont.GetLoanOfferCont,
	).Get("/getLoanOffer", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilterV2,
		dashboardReqV2.GetBankDetailsReq,
		dashboardAuthorization.Authorization,
		app.Srv.DashboardService.GetBankDetailsCont,
	).Get("/getBankDetails", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilterV2, // Using dashboard auth filter instead of lender
		dashboardReqV2.FetchUIConfigReq,  // The request validator
		app.Srv.DashboardService.FetchUIConfig,
	).Post("/fetch-uiconfig", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilterV2,
		dashboardReqV2.UpdateBusinessDetailsReq,
		dashboardAuthorization.Authorization,
		dashboardContV2.UpdateBusinessDetailsCont,
	).Post("/updateBusinessDetails", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.UserProfileReq,
		dashboardCont.GetBankConnectAnalysisReportCont,
	).Get("/getBankConnectReport", responseCommon.GenericRes)

	// Route registration
	// Generic POST endpoint that acts as a bridge for executing various GraphQL queries with pagination support.
	// POST method is used as request body contains complex query parameters and follows GraphQL convention of using POST for queries.
	r.With(
		auth.MasterDashboardAuthFilterV2,
		dashboardReqV2.FetchGraphQLDataReq,
		dashboardContV2.FetchDataByConfigAndGraphQL,
	).Post("/fetch-graphql-data", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.UserProfileReq,
		dashboardCont.GetBankConnectReportContV2,
	).Get("/getBankConnectReportV2", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.BankDetailsIDReq,
		dashboardCont.GetBankConnectReportByID,
	).Get("/getBankConnectReportByID", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.GetLoanDetailsReq,
		dashboardCont.GetLoanDetailsCont,
	).Get("/getLoanDetails", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.UserProfileReq,
		dashboardCont.RetriggerDeviceConnectCont,
	).Get("/retriggerDevice", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.RejectSingleDocReq,
		dashboardCont.RejectSingleDocCont,
	).Post("/rejectSingleDoc", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.GetLoanDetailsReq,
		dashboardCont.RetriggerIIFLCont,
	).Get("/retriggerIIFL", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.GetLoanDetailsReq,
		dashboardCont.RetriggerIIFLKYCCont,
	).Get("/retriggerIIFLKYC", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.GetLoanDetailsReq,
		dashboardCont.RetriggerIIFLAgreementCont,
	).Get("/retriggerIIFLAgreement", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.GetLoanDetailsReq,
		dashboardCont.RetriggerIIFLDecisionCont,
	).Get("/retriggerIIFLDecision", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.GetLoanDetailsReq,
		dashboardCont.RetriggerIIFLCIBILCont,
	).Get("/retriggerIIFLCIBIL", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.GetLoanDetailsReq,
		dashboardCont.RetriggerAssignLenderCont,
	).Get("/retriggerAssignLender", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.RetriggerRuleEngineReq,
		dashboardCont.RetriggerRuleEngineCont,
	).Post("/retriggerRuleEngine", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.ListAppliedTagRequest,
		dashboardCont.ListAppliedTagController,
	).Get("/getAppliedTagsList", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.RetriggerReq,
		dashboardCont.RetriggerUpdateQualificationCont,
	).Get("/retriggerQualification", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.RetriggerBREOnFailureReq,
		dashboardCont.RetriggerBREOnFailureCont,
	).Get("/retriggerBREOnFailure", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.UserProfileCustomReq,
		dashboardCont.RetriggerBCBoostCont,
	).Get("/retriggerBCBoost", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.UserProfileReq,
		dashboardCont.RetriggerGSTBoostCont,
	).Get("/retriggerGSTBoost", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.GetLoanDetailsReq,
		dashboardCont.RetriggerKYCEngineCont,
	).Get("/retriggerKYCEngine", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.GetLoanDetailsReq,
		dashboardCont.SkipNachCont,
	).Get("/skipNach", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.UserProfileReq,
		dashboardCont.SkipBankConnectCont,
	).Get("/skipBankConnect", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardCont.ManualApproveBankDetailsCont,
	).Post("/manualApproveBankDetails", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.GenerateWebLinkReq,
		dashboardCont.GenerateWebLinkCont,
	).Get("/generateWebLink", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.AssistedSchemaReq,
		dashboardCont.AssistedSchemaCont,
	).Get("/assisted", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.AssistedListReq,
		dashboardCont.AssistedListCont,
	).Get("/assistedList", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardCont.AssistedSubmitCont,
	).Post("/assistedPost", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.GetSourceEntityIDReq,
		dashboardCont.SampleAssistedBulkCont,
	).Get("/sampleAssistedBulk", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardCont.UploadAssistedBulkCont,
	).Post("/uploadAsssistedBulk", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.UserProfileReq,
		dashboardCont.UserProfileCont,
	).Get("/userProfile", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.UserProfileReq,
		dashboardCont.GetPartnerDataCont,
	).Get("/partnerData", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.UserActivityReq,
		dashboardCont.UserActivityCont,
	).Get("/userActivity", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.GetLoanListReq,
		dashboardCont.GetLoanListCont,
	).Get("/getLoanList", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.SearchOnlyGetLoanListReq,
		dashboardCont.GetLoanListCont,
	).Get("/searchOnlyGetLoanList", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.UserDataDumpReq,
		dashboardCont.UserDataDumpCont,
	).Get("/userDataDump", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.LoanDateRangeDumpReq,
		dashboardCont.LoanDateRangeDumpCont,
	).Get("/loanDateRangeDump", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.CollectionDumpReq,
		dashboardCont.CollectionDumpCont,
	).Get("/collectionDump", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.GetLoanDetailsReq,
		dashboardCont.GetKYCDetailsCont,
	).Get("/getKYCDetails", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.SubmitKYCReq,
		dashboardCont.SubmitKYCCont,
	).Post("/submitKYC", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.SubmitKYCReq,
		dashboardCont.ResubmitKYCCont,
	).Post("/resubmitKYC", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.GetSourceEntityIDReq,
		dashboardCont.BCRangeCont,
	).Get("/bcRange", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.BankConnectCompletedReq,
		dashboardCont.BankConnectCompletedCont,
	).Post("/bankConnectCompleted", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.UserProfileReq,
		dashboardCont.RetriggerBCWithPolicyCont,
	).Get("/retriggerBCWithPolicy", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.UserProfileReq,
		dashboardCont.BCStatusCont,
	).Get("/bcStatus", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.CancelLoanReq,
		dashboardCont.CancelLoanCont,
	).Post("/cancelLoan", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.SplitTxnReq,
		dashboardCont.SplitTxnCont,
	).Post("/splitTxn", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.UpdateBusinessAddressReq,
		dashboardCont.UpdateBusinessAddressCont,
	).Post("/updateBusinessAddress", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.TransactionListReq,
		dashboardCont.TransactionListCont,
	).Get("/transactions", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.SearchOnlyTransactionListReq,
		dashboardCont.TransactionListCont,
	).Get("/searchOnlyTransactions", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.TransactionListDumpReq,
		dashboardCont.TransactionListDumpCont,
	).Get("/transactionsDump", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.UpdateGenderReq,
		dashboardCont.UpdateGenderCont,
	).Post("/updateGender", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.UpdateFathersNameReq,
		dashboardCont.UpdateFathersNameCont,
	).Post("/updateFathersName", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.UpdateMobileReq,
		dashboardCont.UpdateMobileCont,
	).Post("/updateMobile", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.UpdateEmailReq,
		dashboardCont.UpdateEmailCont,
	).Post("/updateEmail", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.UpdateKYCDocReq,
		dashboardCont.UpdateKYCDocCont,
	).Post("/updateKYCDoc", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.GetLoanDetailsReq,
		dashboardCont.LoanPaymentsHistoryCont,
	).Get("/payments", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.MakeCallReq,
		dashboardCont.MakeCallCont,
	).Post("/makeCall", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.ResolveQueryReq,
		dashboardCont.ResolveQueryCont,
	).Post("/resolveQuery", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.GetLoanDetailsReq,
		dashboardCont.GetPreLimitCont,
	).Get("/getPreLimit", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.GetLoanDetailsReqForMandate,
		dashboardCont.GenerateEmandateURLCont,
	).Get("/generateEmandateUrl", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.GetCreditLineListReq,
		dashboardCont.GetCreditLineListCont,
	).Get("/getCreditLineList", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.SearchOnlyGetCreditLineListReq,
		dashboardCont.GetCreditLineListCont,
	).Get("/searchOnlyGetCreditLineList", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.GetSourceEntityIDReq,
		dashboardCont.ExportCreditLineListCont,
	).Get("/exportCreditLineList", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.GetExportHistoryReq,
		dashboardCont.GetExportHistoryCont,
	).Get("/getExportHistory", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.FunnelMetricsReq,
		dashboardCont.FunnelMetricsCont,
	).Get("/funnelMetrics", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.DownloadMISReq,
		dashboardCont.DownloadMISCont,
	).Get("/downloadMIS", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.DownloadLoanApplicationMISReq,
		dashboardCont.DownloadLoanApplicationMISCont,
	).Get("/downloadLoanApplicationMIS", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.DownloadRejectedMISReq,
		dashboardCont.DownloadRejectedMISCont,
	).Get("/downloadRejectedMIS", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.CancelToProcessingReq,
		dashboardCont.CancelToProcessingCont,
	).Post("/cancelToProcessing", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.RetriggerExperianReq,
		dashboardCont.RetriggerExperianCont,
	).Post("/retriggerExperian", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.GetLoanDetailsReq,
		dashboardCont.GetSignedAgreementCont,
	).Get("/getSignedAgreement", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.GetODBillsReq,
		dashboardCont.GetODBillsCont,
	).Get("/getODBills", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.SearchOnlyGetODBillsReq,
		dashboardCont.GetODBillsCont,
	).Get("/searchOnlyGetODBills", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardCont.ChangeAccountNameCont,
	).Post("/changeAccountName", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.UpdateBankAccountTypeReq,
		dashboardCont.UpdateBankAccountTypeCont,
	).Post("/updateBankAccountType", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.FillWithGSTINReq,
		dashboardCont.FillWithGSTINCont,
	).Post("/fillWithGSTIN", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.GSTINDocReq,
		dashboardCont.GenerateGSTINDoc,
	).Get("/gstinDoc", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.SpecialOfferReq,
		dashboardCont.SpecialOfferCont,
	).Post("/specialOffer", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.UserProfileReq,
		dashboardCont.BCSaveCont,
	).Get("/refreshStatements", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.UserProfileReq,
		dashboardCont.GetBankStatementsCont,
	).Get("/getBankStatements", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.AddToPDCReq,
		dashboardCont.AddToPDCCont,
	).Post("/addToPDC", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.UpdatePANReq,
		dashboardCont.UpdatePANCont,
	).Post("/updatePAN", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.UpdateNameReq,
		dashboardCont.UpdateNameCont,
	).Post("/updateName", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.TransactionStatustoProcessingReq,
		dashboardCont.TransactionStatusToProcessingCont,
	).Post("/txnToProcessing", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.GetSourceEntityIDReq,
		dashboardCont.GetIntegrationDetailsCont,
	).Get("/getIntegrationDetails", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.CreatePhysicalMandateReq,
		dashboardCont.CreatePhysicalMandateCont,
	).Post("/createPhysicalMandate", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.SignPhysicalMandateReq,
		dashboardCont.SignPhysicalMandateCont,
	).Post("/signPhysicalMandate", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.AddCreditLinkDetailsReq,
		dashboardCont.AddCreditLinkDetailsCont,
	).Post("/addCreditLinkdetails", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.UpdateCreditLinkDetailsReq,
		dashboardCont.UpdateCreditLinkDetailsCont,
	).Patch("/updateCreditLinkdetails", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.GetSourceEntityIDReq,
		dashboardCont.GetCreditLinkDetailsCont,
	).Get("/getCreditLinkdetails", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.NachStatusDumpReq,
		dashboardCont.NachStatusDumpCont,
	).Get("/nachStatusDump", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.UserProfileReq,
		dashboardCont.GetKarzaGSTJSONCont,
	).Get("/getKarzaGSTJSON", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.UpdateProfileOTPReq,
		dashboardCont.UpdateProfileOTPCont,
	).Post("/updateProfileOTP", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.UpdateUserProfileReq,
		dashboardCont.UpdateUserProfileCont,
	).Post("/updateUserProfile", responseCommon.GenericRes)

	r.With(
		auth.NonAuthFilter,
		dashboardReq.SendForgetPasswordLinkReq,
		dashboardCont.SendForgetPasswordLinkCont,
	).Post("/sendForgetPasswordLink", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.SendChangePasswordLinkReq,
		dashboardCont.SendChangePasswordLinkCont,
	).Post("/sendChangePasswordLink", responseCommon.GenericRes)

	r.With(
		auth.NonAuthFilter,
		dashboardReq.ResetPasswordReq,
		dashboardCont.ResetPasswordCont,
	).Post("/resetPassword", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.ChangePasswordReq,
		dashboardCont.ChangePasswordCont,
	).Post("/changePassword", responseCommon.GenericRes)

	r.With(
		auth.NonAuthFilter,
		dashboardReq.ResetExpiredPasswordReq,
		dashboardCont.ResetExpiredPasswordCont,
	).Post("/resetExpiredPassword", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardCont.UploadDashboardMediaCont,
	).Post("/uploadDashboardMedia", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.GetLoanDetailsReq,
		dashboardCont.GetWaitStateCont,
	).Get("/getWaitState", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		servicesReq.CSVFileUploadReq,
		dashboardCont.SaveDigioENACHMandateInfoCont,
	).Post("/updateDigioEnachMandateInfo", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardCont.SaveDigioPhysicalMandateInfoCont,
	).Post("/updateDigioPhysicalMandateInfo", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		creditLineReq.MergeTxnReq,
		dashboardCont.MergeTxnCont,
	).Post("/mergeTxns", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.RetriggerUploadGSTMediaDocReq,
		dashboardCont.RetriggerUploadGSTMediaDocCont,
	).Post("/retriggerUploadGST", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.ODBillsDumpReq,
		dashboardCont.ODBillsDumpCont,
	).Get("/odBillsDump", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.ApplyNewLoanReq,
		dashboardCont.ApplyNewLoanCont,
	).Post("/applyNewLoan", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.GetLoanDetailsReq,
		dashboardCont.RevertReject,
	).Get("/revertReject", responseCommon.GenericRes)

	// r.With(
	// 	auth.MasterDashboardAuthFilter,
	// 	dashboardReq.DisqualifyRevertReq,
	// 	dashboardCont.RevertDisqualify,
	// ).Post("/revertDisqualify", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		lenderReq.UpdateLoanOfferReq,
		dashboardCont.UpdateLoanOfferCont,
	).Post("/updateLoanOffer", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		lenderReq.ApproveLoanReq,
		dashboardCont.ApproveLoanCont,
	).Post("/approveLoan", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.GetLoanDetailsReq,
		dashboardCont.RetriggerPushInsuranceDetailsCont,
	).Post("/retriggerPushInsuranceDetails", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.GetLoanDetailsReq,
		dashboardCont.AddDualDOBClauseCont,
		lenderCont.RegenerateAgreementCont,
	).Post("/addDOBClause", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.GetLoanDetailsReq,
		dashboardCont.GetFlagsCont,
	).Get("/getFlags", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.UserProfileReq,
		dashboardCont.DeviceConnectFetchDetailsCont,
	).Get("/fetchDeviceDetails", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.UserProfileReq,
		dashboardCont.GetGSTDetailsCont,
	).Get("/getGSTDetails", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.UserProfileReq,
		dashboardCont.RefetchGSTCont,
	).Get("/refetchGST", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.UserProfileReq,
		dashboardCont.GetUnderwritingCont,
	).Get("/getUnderwriting", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.UserProfileReq,
		dashboardCont.GetMultipleBureauReportsCont,
	).Get("/getBureauReports", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.RejectLoanReq,
		dashboardCont.RejectLoanCont,
	).Post("/rejectLoan", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthNoRbacFilter,
		dashboardCont.LogoutCont,
	).Post("/logout", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.RuleEngineDumpReq,
		dashboardCont.RuleEngineDumpCont,
	).Get("/ruleEngineDump", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		paymentReq.GetVirtualAccountDetailsReq,
		dashboardCont.GetVirtualAccountDetailsCont,
	).Get("/getVirtualAccountDetails", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.UserProfileReq,
		dashboardCont.RetriggerIIFLHardPullCont,
	).Post("/retriggerIIFLHardPull", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.UserProfileReq,
		dashboardCont.ArchiveUserCont,
	).Get("/archiveUser", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.AddThemeReq,
		dashboardCont.AddThemeCont,
	).Post("/addTheme", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.ThemeListReq,
		dashboardCont.ThemeListCont,
	).Get("/themeList", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.SetCurrentThemeReq,
		dashboardCont.SetCurrentTheme,
	).Post("/setTheme", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.DeleteThemeReq,
		dashboardCont.DeleteThemeCont,
	).Delete("/theme", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.ExportLedgerReq,
		paymentCont.ExportLedgerCont,
	).Get("/exportLedger", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.GetSourceListReq,
		dashboardCont.GetSourceListCont,
	).Get("/getSourceList", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		lenderReq.UpdateBankDetailsReq,
		dashboardCont.UpdateBankDetailsCont,
	).Post("/updateBankDetails", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		lenderReq.RegenerateAgreementReq,
		dashboardCont.RegenerateAgreementCont,
	).Get("/regenerateAgreement", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardCont.GetDisqualifyRevertLimitsCont,
	).Get("/getDisqualifyRevertLimits", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.UserProfileReq,
		dashboardCont.ApproveUserUnderReviewCont,
	).Post("/approveUserUnderReview", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.RejectUserUnderReviewReq,
		dashboardCont.RejectUserUnderReviewCont,
	).Post("/rejectUserUnderReview", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.ChangeUserStatusReq,
		dashboardCont.ChangeUserStatusCont,
	).Post("/changeUserStatus", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.ChangeApplicationStatusReq,
		dashboardCont.ChangeApplicationStatusCont,
	).Post("/changeApplicationStatus", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.SetLastModuleReq,
		dashboardCont.SetLastModuleCont,
	).Post("/setLastModule", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		paymentReq.GetExcessFundDetailsReq,
		paymentCont.GetExcessFundDetailsCont,
	).Get("/getExcessFundDetails", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		paymentReq.MarkRefundReq,
		paymentCont.MarkRefundCont,
	).Post("/markRefund", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		paymentReq.SettleExcessFundReq,
		paymentCont.SettleExcessFundCont,
	).Post("/settleExcessFund", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		paymentReq.ListAllExcessFundReq,
		paymentCont.ListAllExcessFundCont,
	).Get("/listAllExcessFunds", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.ReverseDisbursedTxnReq,
		dashboardCont.ReverseDisbursedTxnCont,
	).Post("/reverseODTxns", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.CloseLoanIDIfPaidReq,
		dashboardCont.CloseLoanIDIfPaidCont,
	).Post("/closeLoanIDIfPaid", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.ODLedgerReq,
		dashboardCont.ODLedgerCont,
	).Get("/ledger", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		paymentReq.MarkLoanPaymentReq,
		paymentCont.MarkLoanPaymentCont,
	).Post("/markLoanPayment", responseCommon.GenericRes)

	// api for search
	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.GetAgentListReq,
		dashboardCont.GetAgentListCont,
	).Get("/agentList", responseCommon.GenericRes)

	// api for showing all the partners in dashboard
	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.ListPartnersReq,
		dashboardCont.ListPartnersCont,
	).Get("/listPartners", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardCont.GetSourceEntityListCont,
	).Get("/getSourceEntityList", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.AddValidatedPincodeReq,
		dashboardCont.AddValidatedPincodeCont,
	).Post("/addValidatedPincode", responseCommon.GenericRes)

	// api for showing his dsa list
	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardCont.GetUserDSAList,
	).Get("/userDSAList", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.UserProfileReq,
		dashboardCont.BackfillCIBILScoreCont,
	).Get("/backfillCIBILScore", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.SendSignAgreementDocReq,
		dashboardCont.SendSignAgreementDocCont,
	).Post("/sendSignAgreementDoc", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.UploadAdditionalDocsReq,
		dashboardCont.UploadAdditionalDocsCont,
	).Post("/uploadDocs", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.GetAdditionalDocsReq,
		dashboardCont.GetAdditionalDocsCont,
	).Get("/docs", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.DeleteDocumentReq,
		dashboardCont.DeleteDocumentCont,
	).Post("/deleteDocs", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.AddNoteReq,
		dashboardCont.AddNoteCont,
	).Post("/addNote", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.GetNotesReq,
		dashboardCont.GetNotesCont,
	).Get("/notes", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardCont.GetLenderListCont,
	).Get("/getLenderList", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.MobileFetchUserOTPReq,
		dashboardCont.MobileFetchUserOTPCont,
	).Post("/fetchUserOTP", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.CallDREIIFLBulkReq,
		dashboardCont.CallDREIIFLBulkCont,
	).Post("/bulkCallDREMaker", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.DeleteAgentReq,
		dashboardCont.DeleteAgentCont,
	).Delete("/deleteAgent", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.UpdateAgentCodeReq,
		dashboardCont.UpdateAgentCodeCont,
	).Post("/updateAgentCode", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.UpdateSDSACredsReq,
		dashboardCont.UpdateSDSACredsCont,
	).Post("/updateSDSACreds", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.ReportReq,
		dashboardCont.DisqualificationReportCont,
	).Get("/disqualificationReport", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.ReportReq,
		dashboardCont.DocumentReportCont,
	).Get("/documentReport", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.ReportReq,
		dashboardCont.LoanStatusReportCont,
	).Get("/loanStatusReport", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.ReportReq,
		dashboardCont.InsuranceReportCont,
	).Get("/insuranceReport", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.ReportReq,
		dashboardCont.EmploymentReportCont,
	).Get("/employmentReport", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.ReportReq,
		dashboardCont.FunnelReportCont,
	).Get("/funnelReport", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.GetLoanDetailsReq,
		dashboardCont.RetriggerKYCEngineDGCont,
	).Get("/retriggerKYCEngineDG", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.UserProfileReq,
		dashboardCont.RetriggerSaraloanBusinessAPI,
	).Get("/retriggerSaraloanBusinessAPI", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.ReassignLeadReq,
		dashboardCont.ReassignLeadCont,
	).Post("/reassignLead", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.GetLoanDetailsReq,
		dashboardCont.RetriggerVANCont,
	).Get("/retriggerVAN", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.RetriggerPFLReq,
		dashboardCont.RetriggerPFLCont,
	).Post("/retriggerPFL", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.UpdateCurrentAddressReq,
		dashboardCont.UpdatePermanentAddressCont,
	).Post("/updatePermanentAddress", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		servicesReq.CSVFileUploadReq,
		dashboardReq.BulkUploadUserIDReq,
		dashboardCont.BulkRetriggerQualificationCont,
	).Post("/bulkRetriggerQualification", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.RevokeDeleteReq,
		dashboardCont.RevokeDeleteCont,
	).Post("/revokeDelete", responseCommon.GenericRes)

	// internal
	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.UnblockPolicyReq,
		dashboardCont.UnblockPolicyCont,
	).Get("/unblockPolicy", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.UserListReq,
		dashboardCont.UserStatusAggregateCount,
	).Get("/usersAggregateCount", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.GetLoanListReq,
		dashboardCont.LoanStatusAggregateCount,
	).Get("/loanStatusAggregateCount", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.CacheInvalidateReq,
		dashboardCont.CacheInvalidateCont,
	).Get("/cacheInvalidate", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.PaymentNotifyLenderReq,
		dashboardCont.PaymentNotifyLenderCont,
	).Post("/paymentNotifyLender", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.ODExcessFundsRefundReq,
		dashboardCont.ODExcessFundsRefundCont,
	).Post("/odExcessFundRefund", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.UpdateCLAvailableLimitReq,
		dashboardCont.UpdateCLAvailableLimitCont,
	).Post("/updateAvailableLimit", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.UpdateODBillDataReq,
		dashboardCont.UpdateODBillDataCont,
	).Post("/updateODBill", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.UpdateODLedgerReq,
		dashboardCont.UpdateODLedgerCont,
	).Post("/updateODLedger", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.GetCKYCDecryptedResponseReq,
		dashboardCont.GetCKYCDecryptedResponseCont,
	).Get("/decryptedCKYCResponse", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.UserProfileCustomReq,
		dashboardCont.RetriggerCKYCCont,
	).Get("/retriggerCKYC", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.RetriggerIIFLCLMerchantLoginReq,
		dashboardCont.RetriggerIIFLCLMerchantLoginCont,
	).Get("/retriggerIIFLCLMerchantLogin", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.RevertPaymentCollectReq,
		dashboardCont.RevertRepayamentCollectCont,
	).Post("/revertRepaymentCollect", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.UserProfileReq,
		dashboardCont.RetriggerMintifiCont,
	).Put("/retriggerMintifi", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.RetriggerLendingKartApiReq,
		dashboardCont.RetriggerLendingKartApisCont,
	).Post("/retriggerLendingKart", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.UserProfileReq,
		dashboardCont.RetriggerPreSelectLender,
	).Put("/retriggerPreSelectLender", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		servicesReq.CSVFileUploadReq,
		dashboardCont.UpdateInsuranceConfigCont,
	).Post("/updateInsuranceConfig", responseCommon.GenericRes)

	// bulk reassign managers
	// r.With(
	// 	auth.MasterDashboardAuthFilter,
	// 	dashboardReq.BulkReassignAgentsReq,
	// 	servicesReq.CSVFileUploadReq,
	// 	dashboardCont.BulkReassignAgentsCont,
	// ).Post("/bulkReassignAgents", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.RevertDisqualifyReq,
		dashboardCont.RevertDisqualifyCont,
	).Post("/disqualifyRevert", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.PFLLeadDumpReportReq,
		dashboardCont.PFLCustomUserDataDumpCont,
	).Get("/pflCustomUserDataDump", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.CustomReportReq,
		dashboardCont.PFLEnachMandateReportCont,
	).Get("/pflEnachMandateReport", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.ExpireUserReq,
		dashboardCont.ExpireUserCont,
	).Post("/expireUser", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.BypassBankConnectReq,
		dashboardCont.BypassBankConnectCont,
	).Post("/bypassBankConnect", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.GetAllGroupsReq,
		dashboardCont.GetAllGroupsCont,
	).Post("/rbac/getAllGroups", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.GetGroupPermissionReq,
		dashboardCont.GetGroupPermissionCont,
	).Post("/rbac/getGroupPermission", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardCont.GetAllMaskingLevelsCont,
	).Get("/rbac/getAllMaskingLevels", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.AddNewGroupPermissionsReq,
		dashboardCont.AddNewGroupPermissionsCont,
	).Post("/rbac/createGroup", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.AddGroupPermissionsReq,
		dashboardCont.AddGroupPermissionsCont,
	).Post("/rbac/addGroupPermission", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.DeleteGroupPermissionsReq,
		dashboardCont.DeleteGroupPermissionsCont,
	).Post("/rbac/deleteGroupPermission", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.DeleteGroupReq,
		dashboardCont.DeleteGroupCont,
	).Post("/rbac/deleteGroup", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.UpdateMaskingLevelReq,
		dashboardCont.UpdateMaskingLevelCont,
	).Post("/rbac/updateMaskingLevel", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.CreateRoleReq,
		dashboardCont.CreateRoleCont,
	).Post("/rbac/createRole", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.UpdateRoleReq,
		dashboardCont.UpdateRoleCont,
	).Post("/rbac/updateRole", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.DeleteRoleReq,
		dashboardCont.DeleteRoleCont,
	).Post("/rbac/deleteRole", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.GetAllRolesReq,
		dashboardCont.GetAllRolesCont,
	).Post("/rbac/getAllRoles", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.GetRoleHelperDataReq,
		dashboardCont.GetRoleHelperDataCont,
	).Post("/rbac/getRoleHelperData", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.CreateUserReq,
		dashboardCont.CreateUserCont,
	).Post("/rbac/createUser", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.DeleteUserReq,
		dashboardCont.DeleteUserCont,
	).Post("/rbac/deleteUser", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.ActivateUserReq,
		dashboardCont.ActivateUserCont,
	).Post("/rbac/activateUser", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.GetAllUsersReq,
		dashboardCont.GetAllUsersCont,
	).Post("/rbac/getAllUsers", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.PromoteUserToAdminSettingsReq,
		dashboardCont.PromoteUserToAdminSettingsCont,
	).Post("/rbac/promoteUserToAdminSettings", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.DemoteUserAdminSettingsReq,
		dashboardCont.DemoteUserAdminSettingsCont,
	).Post("/rbac/demoteUserAdminSettings", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardCont.ReloadMDCasbinPolicyCont,
	).Get("/rbac/reloadMDCasbinPolicy", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardCont.ReloadLDCasbinPolicyCont,
	).Get("/rbac/reloadLDCasbinPolicy", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.UpdateMFAStatusReq,
		dashboardCont.UpdateMFAStatusCont,
	).Post("/rbac/updateMFAStatus", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.AddPlatformAccessReq,
		dashboardCont.AddPlatformAccessCont,
	).Post("/rbac/addPlatformAccess", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.RemovePlatformsAccessReq,
		dashboardCont.RemovePlatformsAccessCont,
	).Post("/rbac/removePlatformsAccess", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.GetPlatformAccessFilterViewReq,
		dashboardCont.GetPlatformAccessFilterViewCont,
	).Get("/rbac/getPlatformAccessFilterView", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.ExportUsersViewReq,
		dashboardCont.ExportUsersViewCont,
	).Post("/rbac/exportUsersView", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.ExportGroupsPolicyReq,
		dashboardCont.ExportGroupsPolicyCont,
	).Post("/rbac/exportGroupsPolicy", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.CreateDirectTransactionReq,
		dashboardCont.CreateDirectTransactionCont,
	).Post("/createDirectTransaction", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.GetDatadogDashboardURLReq,
		dashboardCont.GetDatadogDashboardURLCont,
	).Get("/getDatadogDashboardURL", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.GetSalesDispositionConfigReq,
		dashboardCont.GetSalesDispositionConfigCont,
	).Get("/getSalesDispositionConfig", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.GetSalesDispReq,
		dashboardCont.GetSalesDispositionCont,
	).Get("/getSalesDisposition", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.AddSalesDispReq,
		dashboardCont.AddSalesDispositionCont,
	).Post("/addSalesDisposition", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardCont.GetSalesDispositionReport,
	).Post("/getSalesDispositionReport", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.AddOfferNegotiationReq,
		dashboardCont.AddOfferNegotiationCont,
	).Post("/addOfferNegotiation", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.GetLoanDetailsReq,
		dashboardCont.GetOfferNegotiationStatusCont,
	).Get("/getOfferNegotiationWFStatus", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.GetWFsReq,
		dashboardCont.GetWFsCont,
	).Get("/getWorkflows", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.GetWFTimelineReq,
		dashboardCont.GetWFTimelineCont,
	).Get("/getWorkflowTimeline", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardCont.GetWorkflowTaskReport,
	).Post("/getWorkflowTaskReport", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.GetOfferNegotiationEMIReq,
		dashboardCont.GetOfferNegotiationEMICont,
	).Post("/getOfferNegotiationEMI", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.MultiOfferDumpReq,
		dashboardCont.MultiOfferDumpCont,
	).Get("/multiOfferReport", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardCont.ApproveBusinessDocs,
	).Post("/approveBusinessDocs", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.GetLoanDetailsReq,
		dashboardCont.RetriggerABFLLMSAPI,
	).Get("/retriggerABFLLMSAPI", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.CustomReportReq,
		dashboardCont.PFLUTMReportCont,
	).Get("/pflUtmReport", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.GetLoanDetailsReq,
		dashboardCont.GetUnsignedAgreementCont,
	).Get("/getUnsignedAgreement", lenderRes.GetUnsignedAgreementRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.RetriggerPostKYCQualmReq,
		dashboardCont.RetriggerPostKYCQualificationCont,
	).Get("/retriggerPostKYCQualification", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.GetLoanDetailsReq,
		dashboardCont.RegenerateAgreementSignURLCont,
	).Get("/regenerateAgreementSignURL", responseCommon.GenericRes)

	// r.With(
	// 	auth.MasterDashboardAuthFilter,
	// 	dashboardReq.CustomReportReq,
	// 	dashboardCont.ABFLCustomReport,
	// ).Get("/abflCustomReport", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.CustomReportReq,
		dashboardCont.ABFLCustomReportV2,
	).Get("/abflCustomReport", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.CreateSDSAReq,
		dashboardCont.CreateSDSACont,
	).Post("/createSDSA", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.CreateCollaboratorReq,
		dashboardCont.CreateCollaboratorCont,
	).Post("/createCollaborator", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.DeleteCollaboratorReq,
		dashboardCont.DeleteCollaboratorCont,
	).Post("/deleteCollaborator", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.ListCollaboratorsReq,
		dashboardCont.ListCollaboratorsCont,
	).Get("/listCollaborators", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.EditSDSAReq,
		dashboardCont.EditSDSACont,
	).Post("/editSDSA", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.DeactivateAgentReq,
		dashboardCont.DeactivateAgentCont,
	).Post("/deactivateAgent", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.ListManagersReq,
		dashboardCont.ListManagersCont,
	).Get("/listManagers", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.ListChannelHierarchyReq,
		dashboardCont.ListChannelHierarchyCont,
	).Get("/listChannelHierarchy", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.GetAgentDetailsReq,
		dashboardCont.GetAgentDetailsCont,
	).Get("/getAgentDetails", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.ReassignLeadsForDSAReq,
		dashboardCont.ReassignLeadsForDSACont,
	).Post("/reassignLeads", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.ReassignManagerReq,
		dashboardCont.ReassignManagerCont,
	).Post("/reassignManager", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.ReactivateAgentReq,
		dashboardCont.ReactivateAgentCont,
	).Post("/reactivateAgent", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.BulkAddSDSAReq,
		dashboardCont.BulkAddSDSACont,
	).Post("/bulkAddSDSA", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.DeleteAgentReq,
		dashboardCont.DeleteAgentCont,
	).Delete("/deleteAgent", responseCommon.GenericRes)

	// r.With(
	// 	auth.MasterDashboardAuthFilter,
	// 	dashboardReq.BulkReassignAgentsReq,
	// 	servicesReq.CSVFileUploadReq,
	// 	dashboardCont.BulkReassignAgentsCont,
	// ).Post("/bulkReassignManagers", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.GetCategoriesDropdown,
		app.Srv.MasterDashBoardService.GetCategoriesDropdown,
	).Get("/dropdownL2", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.PushAdditionalDocs,
		app.Srv.MasterDashBoardService.PushAdditionalDocs,
	).Post("/pushAdditionalDocs", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardCont.GetOrganizationDsaMetadataMappingCont,
	).Get("/getOrganizationDsaMetadataMapping", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.GetProcessedPDFReportReq,
		dashboardCont.GetProcessedPDFReportCont,
	).Get("/getCibilPDFReport", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.CustomReportReq,
		dashboardCont.ABFLUTMReportCont,
	).Get("/abflUTMReport", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.UserProfileRetriggerReq,
		dashboardCont.RetriggerCibilCont,
	).Get("/retriggerCibil", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.GetCamReportsReq,
		dashboardCont.GetCamReportsCont,
	).Get("/cibilReportsExcel", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		servicesReq.CSVFileUploadReq,
		dashboardReq.BulkReassignManagerReq,
		dashboardCont.BulkReassignManagerCont,
	).Post("/bulkReassignManagers", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.UserDataDumpReq,
		dashboardCont.UserDataDumpV2Cont,
	).Get("/userDataDumpV2", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.AddReferencesForUser,
		dashboardCont.AddReferencesForUser,
	).Put("/addReferencesForUser", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.GetCoApplicantsKYC,
		dashboardCont.GetCoApplicantsKYC,
	).Get("/getCoApplicantsLoanKYC", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.GetCoApplicantList,
		app.Srv.MasterDashBoardService.GetCoApplicantList,
	).Get("/getCoApplicants", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.AddDeviationReq,
		dashboardCont.AddDeviationCont,
	).Post("/addDeviation", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.RetriggerTcapPLApiReq,
		dashboardCont.RetriggerTcapPLApisCont,
	).Post("/retriggerTcapPL", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.UserProfileReq,
		dashboardCont.ListBankConnectAttempts,
	).Get("/listBankConnectAttempts", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.ApiMetadataCacheRefreshReq,
		dashboardCont.ApiMetadataCacheRefreshCont,
	).Post("/apiMetadataCacheRefresh", responseCommon.GenericRes)
	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.RunCasbinMigrationReq,
		dashboardCont.RunCasbinMigrationCont,
	).Post("/casbinMigration", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilterV2,
		dashboardReqV2.GetUserLogsReq,
		dashboardAuthorization.Authorization,
		dashboardContV2.GetUserLogsCont,
	).Get("/getUserLogs", responseCommon.GenericRes)

	r.With(
		auth.NonAuthFilter,
		dashboardReqV2.LoginSSOReq,
		dashboardContV2.LoginSSOCont,
	).Post("/login-sso", responseCommon.GenericRes)

	r.With(
		auth.NonAuthFilter,
		dashboardReqV2.GenerateTokenReq,
		dashboardContV2.GenerateTokenCont,
	).Post("/generate-token", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.UpdateDSAUserTypeReq,
		dashboardCont.UpdateDSAUserTypeCont,
	).Post("/updateDSAUserType", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReqV2.GetUTMReportReq,
		dashboardContV2.GetUTMReportCont,
	).Get("/getUtmReport", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilterV2,
		dashboardReqV2.UpdateUserMetaDataReq,
		dashboardAuthorization.Authorization,
		dashboardContV2.UpdateUserMetaDataCont,
	).Post("/updateUserMetadata", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReqV2.GetPreApprovedOffersReq,
		dashboardContV2.GetPreApprovedOffersCont,
	).Get("/getPreApprovedOffers", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilterV2,
		dashboardReqV2.CreateUserReq,
		dashboardAuthorization.Authorization,
		dashboardContV2.CreateUserCont,
	).Put("/addUser", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilterV2,
		dashboardReqV2.SendSessionLinkToCustomerReq,
		dashboardAuthorization.Authorization,
		dashboardContV2.SendSessionToCustomerCont,
	).Post("/sendSessionLinkToCustomer", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilterV2,
		dashboardReqV2.UpdateRelationManagerDetailsReq,
		dashboardAuthorization.Authorization,
		dashboardContV2.UpdateRelationManagerDetailsCont,
	).Post("/updateRelationManagerDetails", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilterV2,
		dashboardReqV2.GetDropdownConfigReq,
		dashboardAuthorization.Authorization,
		dashboardContV2.GetDropdownConfigCont,
	).Get("/getDropdownConfig", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilterV2,
		dashboardReqV2.GetTasksByLenderAndTypeReq,
		dashboardAuthorization.Authorization,
		app.Srv.DashboardService.GetTasksByLenderAndTypeCont,
	).Post("/getTasksByLenderAndType", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilterV2,
		dashboardReqV2.GetSoftApproveStatusReq,
		dashboardAuthorization.Authorization,
		app.Srv.DashboardService.GetSoftApproveStatusCont,
	).Get("/getSoftApproveStatus", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilterV2,
		dashboardReqV2.GetTaskListReq,
		dashboardAuthorization.Authorization,
		app.Srv.DashboardService.GetTaskListCont,
	).Post("/getTaskList", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilterV2,
		dashboardReqV2.UpdateSoftApproveStatusReq,
		dashboardAuthorization.Authorization,
		app.Srv.DashboardService.UpdateSoftApproveStatusCont,
	).Post("/updateSoftApproveStatus", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilterV2,
		dashboardRequestV2.HandleEventDetailsReq,
		dashboardAuthorization.Authorization,
		app.Srv.DashboardService.HandleEventDetailsCont,
	).Post("/handle-event", responseCommon.GenericRes)

	// remove this after all users are retriggered for pfl
	r.With(
		auth.MasterDashboardAuthFilter,
		dashboardReq.GetLoanDetailsReq,
		dashboardCont.RegenerateAgreementSignURLPflCont,
	).Get("/regenerateAgreementSignURLPfl", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilterV2,
		dashboardRequestV2.WorkflowAction,
		dashboardAuthorization.Authorization,
		app.Srv.DashboardService.WorkflowActionCont,
	).Post("/workflow-action", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilterV2,
		dashboardRequestV2.WorkflowData,
		dashboardAuthorization.Authorization,
		dashboardContV2.WorkflowDataCont,
	).Get("/workflow-data", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilterV2,
		dashboardReqV2.CreateCoApplicant,
		dashboardAuthorization.Authorization,
		app.Srv.DashboardService.CreateCoApplicant,
	).Put("/createCoApplicant", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilterV2,
		dashboardReqV2.UpdateCoApplicant,
		dashboardAuthorization.Authorization,
		app.Srv.DashboardService.UpdateCoApplicant,
	).Post("/updateCoApplicant", responseCommon.GenericRes)

	r.With(
		auth.AdminAuthFilter,
		dashboardReqV2.MigrateUsersReq,
		dashboardContV2.MigrateUsersCont,
	).Post("/migrate-dashboard-users", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilterV2,
		dashboardReqV2.MisLoanDateRangeDumpReq,
		dashboardContV2.MisLoanDateRangeDumpCont,
	).Get("/misloanDateRangeDump", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilterV2,
		dashboardRequestV2.FetchUIBuilderConfigReq,
		app.Srv.DashboardService.FetchUIBuilderConfig,
	).Post("/fetch-uibuilder-config", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilterV2,
		dashboardRequestV2.SubmitFormDataReq,
		app.Srv.DashboardService.SubmitFormDataCont,
	).Post("/submit-form-data", responseCommon.GenericRes)

	r.With(
		auth.MasterDashboardAuthFilterV2,
		dashboardRequestV2.UploadMedia,
		app.Srv.DashboardService.UploadMedia,
	).Post("/upload-media", responseCommon.GenericRes)

}
