package journeyservice

import (
	"context"
	"database/sql"
	"encoding/json"
	"finbox/go-api/conf"
	"finbox/go-api/constants"
	"finbox/go-api/core/models"
	"finbox/go-api/core/repository/usersrepository"
	"finbox/go-api/functions/activity"
	"finbox/go-api/functions/logger"
	studioconstants "finbox/go-api/studio/constants"
	"finbox/go-api/utils/general"
	"fmt"

	studiomodels "finbox/go-api/studio/models"
	resourceservice "finbox/go-api/studio/services/resources"
	stagingservice "finbox/go-api/studio/services/staging"
	"finbox/go-api/utils/apischemamapper"

	runnerconstants "github.com/finbox-in/road-runner/constants"
	runnerutils "github.com/finbox-in/road-runner/utils"
	"go.temporal.io/sdk/client"
)

type JourneyService struct {
	UsersRepository usersrepository.IUsersRepository
	TemporalClient  client.Client
	ResourceService resourceservice.IResourceService
	StagingService  stagingservice.IStagingService
}

func New(u usersrepository.IUsersRepository) *JourneyService {
	return &JourneyService{
		UsersRepository: u,
	}
}

func (j *JourneyService) DisqualifyUser(ctx context.Context, userID, sourceEntityID, disqualificationReason string, entityDetails models.EntityDetails) error {

	status := constants.UserStatusDisqualified
	userUpdateObj := models.User{
		ID:     userID,
		Status: &status,
	}
	err := j.UsersRepository.UpdateUserDetails(ctx, nil, userUpdateObj)
	if err != nil {
		logger.WithContextV2(ctx).Errorln(err)
		return err
	}
	dateTimeNowString := general.GetTimeStampString()
	activityObj := activity.ActivityEvent{
		UserID:            userID,
		SourceEntityID:    sourceEntityID,
		LoanApplicationID: "",
		EntityType:        entityDetails.EntityType,
		EntityRef:         entityDetails.EntityRef,
		EventType:         constants.ActivityUserDisqualified,
		Description:       disqualificationReason,
	}
	activity.RegisterEvent(&activityObj, dateTimeNowString)

	return nil
}

func (j *JourneyService) GetCurrentSubModule(ctx context.Context, userID string, moduleName string) (string, error) {

	wf, err := j.UsersRepository.GetLatestWorkflowByModule(ctx, userID, usersrepository.UserWorkflowParams{
		ModuleName: moduleName,
	})
	if err != nil {
		logger.Log.WithContext(ctx).Errorln(err)
		return "", err
	}
	module, err := runnerutils.FindKeyByPoll(j.TemporalClient, wf.WorkflowID, wf.RunID, runnerconstants.WorkflowOutput, runnerconstants.WorkflowModuleKey, 1000, 4)
	if err != nil {
		logger.WithUser(userID).Errorln(err)
		return "", err
	}
	moduleMap, _ := module.(map[string]interface{})
	subModule, _ := moduleMap[runnerconstants.WorkflowSubModuleNameTag].(string)
	return subModule, nil
}

func (j *JourneyService) FetchSingleSubmitConfig(ctx context.Context, params FetchConfigsParams) (*apischemamapper.Schema, error) {

	var (
		err          error
		groupConfig  *studiomodels.Group
		schema       apischemamapper.Schema
		schemaConfig studiomodels.SingleSubmitConfigDetails
	)

	groupID, err := j.UsersRepository.GetGroupID(ctx, params.UserID)
	if err != nil {
		logger.Log.WithContext(ctx).Errorln(err)
		return nil, err
	}
	groupConfig, err = j.FetchGroupDetailsForUser(ctx, params)
	if params.IsChildUser {
		schemaConfig = groupConfig.GetChildGroupSingleSubmitConfigDetails(params.ModuleName, params.ResourceName)
	} else {
		schemaConfig = groupConfig.GetSingleSubmitConfigDetails(params.ModuleName, params.ResourceName)
	}
	if schemaConfig.ConfigID == "" {
		logger.Log.WithContext(ctx).Error("invalid resourceName")
		return nil, fmt.Errorf("invalid resourceName")
	}
	if !schemaConfig.IsStaged {
		singleSubmitConfig, customErr := j.ResourceService.GetSingleSubmitConfigByID(ctx, schemaConfig.ConfigID)
		if customErr != nil {
			logger.Log.WithContext(ctx).Errorln(customErr.Err)
			return nil, customErr.Err
		}
		err = json.Unmarshal([]byte(singleSubmitConfig.ConfigData), &schema)
		if err != nil {
			logger.Log.WithContext(ctx).Errorln(err)
			return nil, err
		}
	} else {
		stagedConfig, customErr := j.StagingService.GetGroupConfigData(context.TODO(), groupID, schemaConfig.ConfigID, studioconstants.ConfigTypeSingleSubmit)
		if customErr != nil {
			logger.Log.WithContext(ctx).Errorln(err)
			return nil, customErr.Err
		}
		err = json.Unmarshal(stagedConfig.ConfigData, &schema)
		if err != nil {
			logger.Log.WithContext(ctx).Errorln(err)
			return nil, err
		}
		schema.SchemaID = schemaConfig.ConfigID
	}
	return &schema, nil
}

func (j *JourneyService) FetchGroupDetailsForUser(ctx context.Context, params FetchConfigsParams) (*studiomodels.Group, error) {

	var (
		customErr    *studiomodels.CustomError
		groupDetails studiomodels.Group
	)
	groupID, err := j.UsersRepository.GetGroupID(ctx, params.UserID)
	if err != nil {
		logger.Log.WithContext(ctx).Errorln(err)
		return nil, err
	}

	if conf.ENV == conf.ENV_PROD {
		groupDetails, customErr = j.StagingService.GetGroupDetails(ctx, groupID)
	} else {
		groupDetails, customErr = j.StagingService.GetGroupDetails(ctx, groupID)
		if customErr != nil && customErr.Err == sql.ErrNoRows {
			groupDetails, customErr = j.ResourceService.GetGroupConfigByID(ctx, groupID)
		}
	}
	if customErr != nil {
		logger.Log.WithContext(ctx).Errorln("Failed to fetch group config from DB:", customErr)
		return &groupDetails, customErr.Err
	}
	return &groupDetails, nil

}
