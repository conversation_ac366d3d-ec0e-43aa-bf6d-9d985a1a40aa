package emudhra

// APIResponse represents the response from Emudhra API
type APIResponse struct {
	IsSuccess bool     `json:"IsSuccess" validate:"required"`
	Messages  []string `json:"Messages"`
	ErrorCode int      `json:"ErrorCode"`
	Response  struct {
		ReferenceNo        string      `json:"ReferenceNo"`
		DocumentNumberList []string    `json:"DocumentNumberList"`
		DocumentIDList     []int       `json:"DocumentIdList"`
		Status             bool        `json:"Status" validate:"required"`
		WorkflowID         int         `json:"WorkflowId" validate:"required,min=1"`
		SignedDocument     interface{} `json:"SignedDocument"`
		URL                string      `json:"URL"`
	} `json:"Response"`
}

// SigningWithoutEStampResponse represents the processed response for the activity
type SigningWithoutEStampResponse struct {
	ReferenceNumber string `json:"referenceNo"`
	DocumentIDList  []int  `json:"documentIDList"`
	WorkflowID      string `json:"workflowID"`
	URL             string `json:"url"`
	EsignAttemptID  string `json:"esignAttemptID"`
}

// EmbeddedSigningResponse represents the response for embedded signing
type EmbeddedSigningResponse struct {
	ReferenceNumber string `json:"referenceNo"`
	DocumentIDList  []int  `json:"documentIDList"`
	WorkflowID      string `json:"workflowID"`
	URL             string `json:"url"`
	EsignAttemptID  string `json:"esignAttemptID"`
}

// DownloadDocumentFile represents a single file in the download response
type DownloadDocumentFile struct {
	DocumentName   string `json:"DocumentName"`
	Base64FileData string `json:"Base64FileData"`
	DocumentID     int    `json:"DocumentId"`
	IsAttachment   bool   `json:"IsAttachment"`
}

// DownloadDocumentAPIResponse represents the API response for document download
type DownloadDocumentAPIResponse struct {
	IsSuccess bool     `json:"IsSuccess" validate:"required"`
	Messages  []string `json:"Messages"`
	ErrorCode int      `json:"ErrorCode"`
	Response  struct {
		ReferenceNo string                 `json:"ReferenceNo"`
		WorkflowID  int                    `json:"WorkflowId"`
		FileList    []DownloadDocumentFile `json:"FileList"`
	} `json:"Response"`
}

// DownloadDocumentResponse represents the processed response for the activity
type DownloadDocumentResponse struct {
	S3ObjectKey string `json:"s3ObjectKey"`
	DocumentID  int    `json:"documentID"`
	FileName    string `json:"fileName"`
}
