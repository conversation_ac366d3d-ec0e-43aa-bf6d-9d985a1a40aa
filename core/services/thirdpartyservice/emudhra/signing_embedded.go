package emudhra

import (
	"context"
	"database/sql"
	"finbox/go-api/constants"
	"finbox/go-api/errorHandler"
	"finbox/go-api/functions/logger"
	"finbox/go-api/functions/octopus"
	"finbox/go-api/infra/s3"
	"finbox/go-api/models/esignattempt"
	"finbox/go-api/models/loanapplication"
	"finbox/go-api/utils/general"
	"fmt"
	"time"

	"github.com/finbox-in/octoclient"
)

// buildEmbeddedSigningDocuments creates document payload for embedded signing
func (s *Service) buildEmbeddedSigningDocuments(agreementS3Key string) []map[string]interface{} {
	agreementBase64 := s3.GetBase64FromS3(agreementS3Key)

	return []map[string]interface{}{
		{
			"base64": agreementBase64,
			"name":   "agreement.pdf",
		},
	}
}

// buildEmbeddedSigningSignatories converts embedded signing applicants into API format
func (s *Service) buildEmbeddedSigningSignatories(primaryApplicant EmbeddedSigningApplicant, coApplicants []EmbeddedSigningApplicant) []map[string]interface{} {
	var signatories []map[string]interface{}

	// Add primary applicant
	signatories = append(signatories, map[string]interface{}{
		"constitution": primaryApplicant.Constitution,
		"dob":          primaryApplicant.DOB,
		"email":        primaryApplicant.Email,
		"mobile":       primaryApplicant.Mobile,
		"name":         primaryApplicant.Name,
		"pan":          primaryApplicant.PAN,
		"pin":          primaryApplicant.Pincode,
		"relationship": primaryApplicant.Relationship,
		"type":         primaryApplicant.Type,
	})

	// Add co-applicants
	for _, applicant := range coApplicants {
		signatories = append(signatories, map[string]interface{}{
			"constitution": applicant.Constitution,
			"dob":          applicant.DOB,
			"email":        applicant.Email,
			"mobile":       applicant.Mobile,
			"name":         applicant.Name,
			"pan":          applicant.PAN,
			"pin":          applicant.Pincode,
			"relationship": applicant.Relationship,
			"type":         applicant.Type,
		})
	}

	return signatories
}

// buildEStamp creates the e-stamp payload
func (s *Service) buildEStamp(req EmbeddedSigningRequest) EStamp {
	return EStamp{
		ArticleCode:        req.ArticleCode,
		ConsiderationPrice: req.Amount,
		Description:        req.Description,
		StampDutyAmount:    req.StampDutyAmount,
		State:              req.StateCode,
	}
}

func (s *Service) buildLoanDetails(req EmbeddedSigningRequest) map[string]interface{} {
	return map[string]interface{}{
		"dateOfSanction":   req.loanDetails.DateOfSanction.Format("2006-01-02"),
		"emiAmount":        fmt.Sprintf("%.2f", req.loanDetails.EMIAmount),
		"rateOfInterest":   fmt.Sprintf("%.2f", req.loanDetails.InterestRate),
		"sanctionedAmount": fmt.Sprintf("%.2f", req.Amount),
		"tenureInMonths":   fmt.Sprint(req.loanDetails.Tenure),
	}
}

// buildEmbeddedSigningPayload creates the complete API payload for embedded signing
func (s *Service) buildEmbeddedSigningPayload(req EmbeddedSigningRequest) map[string]interface{} {
	payload := map[string]interface{}{
		"documents":         s.buildEmbeddedSigningDocuments(req.AgreementS3Key),
		"referenceNo":       fmt.Sprintf("%s_%s", req.LoanApplicationNo, general.GenerateRandomString(8)),
		"signatories":       s.buildEmbeddedSigningSignatories(req.PrimaryApplicant, req.CoApplicants),
		"eStamp":            s.buildEStamp(req),
		"loanDetails":       s.buildLoanDetails(req),
		"loanApplicationNo": req.LoanApplicationNo,
	}

	return payload
}

// processEmbeddedSigningAPIResponse converts the embedded signing API response
func (s *Service) processEmbeddedSigningAPIResponse(apiResponse APIResponse) EmbeddedSigningResponse {
	return EmbeddedSigningResponse{
		ReferenceNumber: apiResponse.Response.ReferenceNo,
		DocumentIDList:  apiResponse.Response.DocumentIDList,
		WorkflowID:      fmt.Sprint(apiResponse.Response.WorkflowID),
		URL:             apiResponse.Response.URL,
	}
}

// saveEmbeddedSigningEsignAttempt saves the embedded signing e-sign attempt to the database
func (s *Service) saveEmbeddedSigningEsignAttempt(ctx context.Context, req EmbeddedSigningRequest, apiResponse APIResponse, response EmbeddedSigningResponse) (string, error) {
	loanApplicationID, err := loanapplication.GetLoanApplicationIDByNo(ctx, req.LoanApplicationNo)
	if err != nil {
		logger.WithUser(req.UserID).Errorf("failed to get loan application ID: %v", err)
		return "", fmt.Errorf("failed to get loan application ID: %w", err)
	}

	esignAttemptID := general.GetUUID()

	esignAttempt := esignattempt.EsignAttempt{
		EsignAttemptID:    esignAttemptID,
		LoanApplicationID: loanApplicationID,
		DocumentID:        fmt.Sprint(apiResponse.Response.WorkflowID),
		SignerObject:      []byte(general.AnyToJSONString(response)),
		Vendor:            constants.VendorEmudhra,
		ESignStatus:       constants.ESignStatusActive,
		EsignLink:         response.URL,
		Type:              sql.NullString{String: constants.ESignTypeEmbeddedSigning, Valid: true},
	}

	if err := esignattempt.Insert(nil, esignAttempt); err != nil {
		logger.WithUser(req.UserID).Errorf("failed to insert e-sign attempt: %v", err)
		return "", fmt.Errorf("failed to insert e-sign attempt: %w", err)
	}

	return esignAttemptID, nil
}

// EmbeddedSigning implements the Emudhra embedded signing service with e-stamp
func (s *Service) EmbeddedSigning(ctx context.Context, req EmbeddedSigningRequest) (EmbeddedSigningResponse, error) {

	// Get Loan Application Details
	loanApplication, err := loanapplication.GetByNo(ctx, req.LoanApplicationNo)
	if err != nil {
		logger.WithUser(req.UserID).Errorln(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": req.UserID}, err)
		return EmbeddedSigningResponse{}, fmt.Errorf("failed to get loan application: %w", err)
	}

	loanApplication, err = loanapplication.Get(ctx, loanApplication.ID.String())
	if err != nil {
		logger.WithUser(req.UserID).Errorln(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": req.UserID}, err)
		return EmbeddedSigningResponse{}, fmt.Errorf("failed to get loan application: %w", err)
	}

	req.loanDetails = loanDetails{
		DateOfSanction: time.Now(),
		EMIAmount:      loanApplication.EMI,
		InterestRate:   loanApplication.Interest,
		Tenure:         loanApplication.Tenure,
	}

	// Build API payload for embedded signing request
	payload := s.buildEmbeddedSigningPayload(req)

	// Make API call to Emudhra service via Octopus
	octopusResponse, err := octopus.InvokeWithOptions(ctx, req.UserID, req.ServiceName, octoclient.OctoPayloadGeneric{
		Data: payload,
	}, octopus.InvokeOptions{
		ValidateWithStruct:       true,
		ValidationStruct:         &APIResponse{},
		RetryOnValidationFailure: false,
		CastToResponseStruct:     true,
		ResponseStructToCastInto: APIResponse{},
		CustomRetryAttempts:      1, // Don't retry, retries should be done via activity itself
	})

	if err != nil {
		logger.WithUser(req.UserID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return EmbeddedSigningResponse{}, err
	}

	// Process API response
	apiResponse, ok := octopusResponse.APIResponse.Data.(APIResponse)
	if !ok {
		err = fmt.Errorf("failed to cast octopus response to APIResponse")
		logger.WithUser(req.UserID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return EmbeddedSigningResponse{}, err
	}

	response := s.processEmbeddedSigningAPIResponse(apiResponse)

	// Save e-sign attempt to database
	esignAttemptID, err := s.saveEmbeddedSigningEsignAttempt(ctx, req, apiResponse, response)
	if err != nil {
		logger.WithUser(req.UserID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return EmbeddedSigningResponse{}, err
	}

	// Add e-sign attempt ID to response
	response.EsignAttemptID = esignAttemptID

	return response, nil
}
