package emudhra

import (
	"context"
	"finbox/go-api/errorHandler"
	"finbox/go-api/functions/logger"
	"finbox/go-api/functions/octopus"
	"finbox/go-api/infra/s3"
	"finbox/go-api/utils/general"
	"fmt"
	"strconv"

	"github.com/finbox-in/octoclient"
)

// buildDownloadPayload creates the API payload for document download request
func (s *Service) buildDownloadPayload(req DownloadDocumentRequest) (map[string]interface{}, error) {
	workflowID, err := strconv.Atoi(req.WorkflowID)
	if err != nil {
		logger.WithUser(req.UserID).Errorf("invalid workflow ID: %v", err)
		return nil, fmt.Errorf("invalid workflow ID: %s", err.Error())
	}

	payload := map[string]interface{}{
		"WorkFlowId": workflowID,
	}

	return payload, nil
}

// processDownloadAPIResponse converts the download API response to our internal format
func (s *Service) processDownloadAPIResponse(ctx context.Context, userID string, apiResponse DownloadDocumentAPIResponse) (DownloadDocumentResponse, error) {
	if !apiResponse.IsSuccess {
		if len(apiResponse.Messages) > 0 {
			logger.WithUser(userID).Errorf("download failed: %s", apiResponse.Messages[0])
			return DownloadDocumentResponse{}, fmt.Errorf("download failed: %s", apiResponse.Messages[0])
		}
		logger.WithUser(userID).Errorf("download failed with error code: %d", apiResponse.ErrorCode)
		return DownloadDocumentResponse{}, fmt.Errorf("download failed with error code: %d", apiResponse.ErrorCode)
	}

	if len(apiResponse.Response.FileList) == 0 {
		logger.WithUser(userID).Errorln("no files found in download response")
		return DownloadDocumentResponse{}, fmt.Errorf("no files found in download response")
	}

	// Pick the first file as specified in requirements
	firstFile := apiResponse.Response.FileList[0]

	// Generate S3 key for the downloaded document
	s3Key := fmt.Sprintf("%s/emudhra/signed/%s.pdf", userID, general.GenerateRandomString(8))

	// Upload base64 content directly to S3
	_, success := s3.UploadBase64ToS3(firstFile.Base64FileData, s3Key)
	if !success {
		logger.WithUser(userID).Errorln("failed to upload document to S3")
		return DownloadDocumentResponse{}, fmt.Errorf("failed to upload document to S3")
	}

	logger.WithContext(ctx).Infof("Successfully downloaded and uploaded document to S3: %s", s3Key)

	return DownloadDocumentResponse{
		S3ObjectKey: s3Key,
		DocumentID:  firstFile.DocumentID,
		FileName:    firstFile.DocumentName,
	}, nil
}

// DownloadDocument implements the Emudhra document download service
func (s *Service) DownloadDocument(ctx context.Context, req DownloadDocumentRequest) (DownloadDocumentResponse, error) {
	// Build API payload for download request
	payload, err := s.buildDownloadPayload(req)
	if err != nil {
		logger.WithUser(req.UserID).Errorln(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{"userID": req.UserID}, err)
		return DownloadDocumentResponse{}, err
	}

	// Make API call to Emudhra service via Octopus
	octopusResponse, err := octopus.InvokeWithOptions(ctx, req.UserID, req.ServiceName, octoclient.OctoPayloadGeneric{
		Data: payload,
	}, octopus.InvokeOptions{
		ValidateWithStruct:       true,
		ValidationStruct:         &DownloadDocumentAPIResponse{},
		RetryOnValidationFailure: false,
		CastToResponseStruct:     true,
		ResponseStructToCastInto: DownloadDocumentAPIResponse{},
		CustomRetryAttempts:      1, // Don't retry, retries should be done via activity itself
	})

	if err != nil {
		logger.WithUser(req.UserID).Errorln(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{
			"userID":      req.UserID,
			"serviceName": req.ServiceName,
			"payload":     payload,
		}, err)
		return DownloadDocumentResponse{}, err
	}

	// Process API response
	apiResponse, ok := octopusResponse.APIResponse.Data.(DownloadDocumentAPIResponse)
	if !ok {
		err = fmt.Errorf("failed to cast octopus response to DownloadDocumentAPIResponse")
		logger.WithUser(req.UserID).Errorln(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{
			"userID":      req.UserID,
			"serviceName": req.ServiceName,
			"payload":     payload,
		}, err)
		return DownloadDocumentResponse{}, err
	}

	response, err := s.processDownloadAPIResponse(ctx, req.UserID, apiResponse)
	if err != nil {
		logger.WithUser(req.UserID).Errorln(err)
		errorHandler.ReportToSentryWithFields(map[string]interface{}{
			"userID":      req.UserID,
			"serviceName": req.ServiceName,
			"payload":     payload,
		}, err)
		return DownloadDocumentResponse{}, err
	}

	return response, nil
}
