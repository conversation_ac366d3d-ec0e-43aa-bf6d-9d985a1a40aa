package emudhra

// Service handles Emudhra-specific signing operations
type Service struct{}

// NewService creates a new instance of SigningService
func NewService() *Service {
	return &Service{}
}

// Interface implementation methods are defined in:
// - signing_without_estamp.go: SignWithoutEStamp and related helper methods
// - signing_embedded.go: EmbeddedSigning and related helper methods
// - download.go: DownloadDocument and related helper methods
