package emudhra

import "time"

// ControlDetail represents control positioning information for document signing
type ControlDetail struct {
	AssignedTo int    `arg:"assignedTo"`
	ControlID  int    `arg:"controlID"`
	Height     int    `arg:"height"`
	Left       int    `arg:"left"`
	Page       string `arg:"page"`
	PageNo     int    `arg:"pageNo"`
	Top        int    `arg:"top"`
	Width      int    `arg:"width"`
}

type apiControlDetailRequest struct {
	AssignedTo int    `json:"AssignedTo"`
	ControlID  int    `json:"ControlID"`
	Height     int    `json:"Height"`
	Left       int    `json:"Left"`
	Page       string `json:"Page,omitempty"`
	PageNo     int    `json:"PageNo,omitempty"`
	Top        int    `json:"Top"`
	Width      int    `json:"Width"`
}

// Document represents a document to be signed
type Document struct {
	S3Key          string          `arg:"s3Key" required:"true"`
	Name           string          `arg:"name" required:"true"`
	ControlDetails []ControlDetail `arg:"controlDetails"`
	TemplateID     string          `arg:"templateID"`
}

// Signatory represents a person who will sign the document
type Signatory struct {
	Email  string `arg:"email"`
	Name   string `arg:"name" required:"true"`
	Mobile string `arg:"mobile"`
}

// Callbacks contains URLs for different signing outcomes
type Callbacks struct {
	CancelURL  string `arg:"cancelURL" required:"true"`
	FailureURL string `arg:"failureURL" required:"true"`
	SuccessURL string `arg:"successURL" required:"true"`
}

// SigningWithoutEStampRequest represents the request for signing without e-stamp
type SigningWithoutEStampRequest struct {
	LoanApplicationNo string
	UserID            string
	ServiceName       string
	Documents         []Document
	Callbacks         Callbacks
	PrimaryApplicant  Signatory
	CoApplicants      []Signatory
}

// EmbeddedSigningApplicant represents a full applicant with all required details for embedded signing
type EmbeddedSigningApplicant struct {
	Constitution string `arg:"constitution" required:"true"`
	DOB          string `arg:"dob" required:"true"`
	Email        string `arg:"email" required:"true"`
	Mobile       string `arg:"mobile" required:"true"`
	Name         string `arg:"name" required:"true"`
	PAN          string `arg:"pan" required:"true"`
	Pincode      string `arg:"pincode" required:"true"`
	Relationship string `arg:"relationship" required:"true"`
	Type         string `arg:"type" required:"true"`
}

// EStamp represents the electronic stamp configuration
type EStamp struct {
	ArticleCode        string  `json:"articleCode"`
	ConsiderationPrice float64 `json:"considerationPrice"`
	Description        string  `json:"description"`
	StampDutyAmount    float64 `json:"stampDutyAmount"`
	State              string  `json:"state"`
}

// EmbeddedSigningRequest represents the request for embedded signing with e-stamp
type EmbeddedSigningRequest struct {
	LoanApplicationNo string
	StateCode         string
	AgreementS3Key    string
	ArticleCode       string
	UserID            string
	ServiceName       string
	Amount            float64
	StampDutyAmount   float64
	Description       string
	PrimaryApplicant  EmbeddedSigningApplicant
	CoApplicants      []EmbeddedSigningApplicant
	loanDetails       loanDetails
}

type loanDetails struct {
	DateOfSanction time.Time
	EMIAmount      float64
	InterestRate   float64
	Tenure         int
}

type DownloadDocumentRequest struct {
	WorkflowID  string
	UserID      string
	ServiceName string
}
