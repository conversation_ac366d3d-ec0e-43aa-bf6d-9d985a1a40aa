package emudhra

import (
	"context"
	"database/sql"
	"finbox/go-api/constants"
	"finbox/go-api/errorHandler"
	"finbox/go-api/functions/logger"
	"finbox/go-api/functions/octopus"
	"finbox/go-api/infra/s3"
	"finbox/go-api/models/esignattempt"
	"finbox/go-api/models/loanapplication"
	"finbox/go-api/utils/general"
	"fmt"

	"github.com/finbox-in/octoclient"
)

// buildDocuments converts document arguments into API payload format
func (s *Service) buildDocuments(documents []Document) []map[string]interface{} {
	var apiDocuments []map[string]interface{}

	for _, doc := range documents {
		documentBase64 := s3.GetBase64FromS3(doc.S3Key)

		document := map[string]interface{}{
			"base64": documentBase64,
			"name":   doc.Name,
		}

		switch {
		case doc.TemplateID != "":
			document["templateID"] = doc.TemplateID
		default:
			// Build control details for signing positions
			controlDetails := s.buildControlDetails(doc.ControlDetails)
			document["controlDetails"] = controlDetails
		}

		apiDocuments = append(apiDocuments, document)
	}

	return apiDocuments
}

// buildControlDetails converts control detail arguments into API payload format
func (s *Service) buildControlDetails(controlDetails []ControlDetail) []apiControlDetailRequest {
	var apiControlDetails []apiControlDetailRequest

	for _, control := range controlDetails {
		apiControlDetails = append(apiControlDetails, apiControlDetailRequest{
			AssignedTo: control.AssignedTo,
			ControlID:  control.ControlID,
			Height:     control.Height,
			Left:       control.Left,
			Page:       control.Page,
			PageNo:     control.PageNo,
			Top:        control.Top,
			Width:      control.Width,
		})
	}

	return apiControlDetails
}

// buildSignatories converts signatory arguments into API payload format
func (s *Service) buildSignatories(primaryApplicant Signatory, coApplicants []Signatory) []map[string]interface{} {
	var signatories []map[string]interface{}

	// Add primary applicant
	signatories = append(signatories, map[string]interface{}{
		"email":  primaryApplicant.Email,
		"name":   primaryApplicant.Name,
		"mobile": primaryApplicant.Mobile,
	})

	// Add co-applicants
	for _, signatory := range coApplicants {
		signatories = append(signatories, map[string]interface{}{
			"email":  signatory.Email,
			"name":   signatory.Name,
			"mobile": signatory.Mobile,
		})
	}

	return signatories
}

// buildSigningPayload creates the complete API payload for Emudhra signing
func (s *Service) buildSigningPayload(req SigningWithoutEStampRequest) map[string]interface{} {
	payload := map[string]interface{}{
		"referenceNo": fmt.Sprintf("%s_%s", req.LoanApplicationNo, general.GenerateRandomString(8)),
		"callbacks": map[string]interface{}{
			"cancelURL":  req.Callbacks.CancelURL,
			"failureURL": req.Callbacks.FailureURL,
			"successURL": req.Callbacks.SuccessURL,
		},
	}

	payload["documents"] = s.buildDocuments(req.Documents)
	payload["signatories"] = s.buildSignatories(req.PrimaryApplicant, req.CoApplicants)

	return payload
}

// processAPIResponse converts the API response to our internal format
func (s *Service) processAPIResponse(apiResponse APIResponse) SigningWithoutEStampResponse {
	return SigningWithoutEStampResponse{
		ReferenceNumber: apiResponse.Response.ReferenceNo,
		DocumentIDList:  apiResponse.Response.DocumentIDList,
		WorkflowID:      fmt.Sprint(apiResponse.Response.WorkflowID),
		URL:             apiResponse.Response.URL,
	}
}

// saveEsignAttempt saves the e-sign attempt to the database
func (s *Service) saveEsignAttempt(ctx context.Context, req SigningWithoutEStampRequest, apiResponse APIResponse, response SigningWithoutEStampResponse) (string, error) {
	loanApplicationID, err := loanapplication.GetLoanApplicationIDByNo(ctx, req.LoanApplicationNo)
	if err != nil {
		logger.WithUser(req.UserID).Errorf("failed to get loan application ID: %v", err)
		return "", fmt.Errorf("failed to get loan application ID: %w", err)
	}

	esignAttemptID := general.GetUUID()

	esignAttempt := esignattempt.EsignAttempt{
		EsignAttemptID:    esignAttemptID,
		LoanApplicationID: loanApplicationID,
		DocumentID:        fmt.Sprint(apiResponse.Response.WorkflowID),
		SignerObject:      []byte(general.AnyToJSONString(response)),
		Vendor:            constants.VendorEmudhra,
		ESignStatus:       constants.ESignStatusActive,
		EsignLink:         response.URL,
		Type:              sql.NullString{String: constants.ESignTypeSigningWithoutEstamp, Valid: true},
	}

	if err := esignattempt.Insert(nil, esignAttempt); err != nil {
		logger.WithUser(req.UserID).Errorln(err)
		return "", fmt.Errorf("failed to insert e-sign attempt: %w", err)
	}

	return esignAttemptID, nil
}

// SignWithoutEStamp implements the Emudhra signing service for documents without e-stamp
func (s *Service) SignWithoutEStamp(ctx context.Context, req SigningWithoutEStampRequest) (SigningWithoutEStampResponse, error) {
	// Build API payload for Emudhra signing request
	payload := s.buildSigningPayload(req)

	// Make API call to Emudhra service via Octopus
	octopusResponse, err := octopus.InvokeWithOptions(ctx, req.UserID, req.ServiceName, octoclient.OctoPayloadGeneric{
		Data: payload,
	}, octopus.InvokeOptions{
		ValidateWithStruct:       true,
		ValidationStruct:         &APIResponse{},
		RetryOnValidationFailure: false,
		CastToResponseStruct:     true,
		ResponseStructToCastInto: APIResponse{},
		CustomRetryAttempts:      1, // Don't retry, retries should be done via activity itself
	})

	if err != nil {
		logger.WithUser(req.UserID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return SigningWithoutEStampResponse{}, err
	}

	// Process API response
	apiResponse, ok := octopusResponse.APIResponse.Data.(APIResponse)
	if !ok {
		err = fmt.Errorf("failed to cast octopus response to EmudhraAPIResponse")
		logger.WithUser(req.UserID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return SigningWithoutEStampResponse{}, err
	}

	response := s.processAPIResponse(apiResponse)

	// Save e-sign attempt to database
	esignAttemptID, err := s.saveEsignAttempt(ctx, req, apiResponse, response)
	if err != nil {
		logger.WithUser(req.UserID).Errorln(err)
		errorHandler.ReportToSentryWithoutRequest(err)
		return SigningWithoutEStampResponse{}, err
	}

	// Add e-sign attempt ID to response
	response.EsignAttemptID = esignAttemptID

	return response, nil
}
