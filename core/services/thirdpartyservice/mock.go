package thirdpartyservice

import (
	"context"
	"finbox/go-api/core/services/thirdpartyservice/emudhra"
	"finbox/go-api/functions/octopus"
	panUtils "finbox/go-api/utils/pan"

	"github.com/stretchr/testify/mock"
)

type MockThirdPartyService struct {
	mock.Mock
}

func (m *MockThirdPartyService) HypervergePANExtendedAPI(ctx context.Context, req PANExtendedAPIRequest, opts Options) (panUtils.PANDetailedAPI, string, error) {

	args := m.Called(ctx, req, opts)
	return args.Get(0).(panUtils.PANDetailedAPI), args.String(1), args.Error(2)

}

func (m *MockThirdPartyService) GridlinesPANExtendedAPI(ctx context.Context, req PANExtendedAPIRequest, opts Options) (panUtils.PANDetailedAPI, string, error) {

	args := m.Called(ctx, req, opts)
	return args.Get(0).(panUtils.PANDetailedAPI), args.String(1), args.Error(2)

}

func (m *MockThirdPartyService) FetchTransUnionCIBILV2Report(ctx context.Context, req TransUnionCIBILV2Req, opts Options) (resp octopus.CIBILResponseStruct, rawResponseJSON string, err error) {

	args := m.Called(ctx, req, opts)
	return args.Get(0).(octopus.CIBILResponseStruct), args.String(1), args.Error(2)
}

func (m *MockThirdPartyService) FetchExperianReport(ctx context.Context, req TransUnionExperianReq, opts Options) (experianResp octopus.InvokeRespStructV2, rawResponseJSON string, externalServiceID string, err error) {
	args := m.Called(ctx, req, opts)
	return args.Get(0).(octopus.InvokeRespStructV2), args.String(1), args.String(2), args.Error(3)
}

func (m *MockThirdPartyService) NSDLPANVeriyAPI(ctx context.Context, req PANVerifyAPIRequest, opts Options) (bool, bool, bool, string, string, error) {

	args := m.Called(ctx, req, opts)
	return args.Bool(0), args.Bool(1), args.Bool(2), args.String(3), args.String(4), args.Error(5)

}

func (m *MockThirdPartyService) PayUCreatePaymentLink(ctx context.Context, req CreatePaymentPayURequest, opts Options) (PayUCreatePaymentLinkRespStruct, error) {

	args := m.Called(ctx, req, opts)
	return args.Get(0).(PayUCreatePaymentLinkRespStruct), args.Error(1)

}

func (m *MockThirdPartyService) PoonawallaNSDLPANVeriyAPI(ctx context.Context, req PANVerifyAPIRequest, opts Options) (bool, bool, bool, string, PFLNSDLPANVerifyRespStruct, string, string, error) {

	args := m.Called(ctx, req, opts)
	return args.Bool(0), args.Bool(1), args.Bool(2), args.String(3), args.Get(4).(PFLNSDLPANVerifyRespStruct), args.String(5), args.String(6), args.Error(7)

}

func (m *MockThirdPartyService) GetAndSaveCreatePaymentLinkPayU(ctx context.Context, userID, loanApplicationID string, amount float64) (string, error) {
	args := m.Called(ctx, userID)
	return args.String(0), args.Error(1)
}

func (m *MockThirdPartyService) CheckDeviceConnectDataExistsForThreshold(ctx context.Context, userID string, interval int, intervalUnit string) (bool, error) {
	args := m.Called(ctx, userID, interval, intervalUnit)
	return args.Bool(0), args.Error(1)
}

func (m *MockThirdPartyService) PollForDeviceConnectPreds(ctx context.Context, params DeviceConnectTriggerParams) (DeviceConnectResponse, error) {
	args := m.Called(ctx, params)
	return args.Get(0).(DeviceConnectResponse), args.Error(1)
}

func (m *MockThirdPartyService) ProcessDeviceConnectPredictors(ctx context.Context, userID, deviceConnectDetailsID, predictorVersion string, opts DeviceConnectPredictorsFetchOptions) error {
	args := m.Called(ctx, userID, deviceConnectDetailsID, predictorVersion, opts)
	return args.Error(0)
}

func (m *MockThirdPartyService) EmudhraSigningWithoutEStamp(ctx context.Context, req emudhra.SigningWithoutEStampRequest) (emudhra.SigningWithoutEStampResponse, error) {
	args := m.Called(ctx, req)
	return args.Get(0).(emudhra.SigningWithoutEStampResponse), args.Error(1)
}

func (m *MockThirdPartyService) EmudhraEmbeddedSigning(ctx context.Context, req emudhra.EmbeddedSigningRequest) (emudhra.EmbeddedSigningResponse, error) {
	args := m.Called(ctx, req)
	return args.Get(0).(emudhra.EmbeddedSigningResponse), args.Error(1)
}

func (m *MockThirdPartyService) EmudhraDownloadDocument(ctx context.Context, req emudhra.DownloadDocumentRequest) (emudhra.DownloadDocumentResponse, error) {
	args := m.Called(ctx, req)
	return args.Get(0).(emudhra.DownloadDocumentResponse), args.Error(1)
}
