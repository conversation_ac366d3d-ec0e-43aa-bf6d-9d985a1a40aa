package thirdpartyservice

import (
	"context"
	"finbox/go-api/core/services/thirdpartyservice/emudhra"
	"finbox/go-api/functions/octopus"
	panUtils "finbox/go-api/utils/pan"
)

type IThirdPartyService interface {
	HypervergePANExtendedAPI(ctx context.Context, req PANExtendedAPIRequest, opts Options) (panUtils.PANDetailedAPI, string, error)
	GridlinesPANExtendedAPI(ctx context.Context, req PANExtendedAPIRequest, opts Options) (panUtils.PANDetailedAPI, string, error)
	FetchTransUnionCIBILV2Report(ctx context.Context, req TransUnionCIBILV2Req, opts Options) (resp octopus.CIBILResponseStruct, rawResponseJSON string, err error)
	FetchExperianReport(ctx context.Context, req TransUnionExperianReq, opts Options) (experianResp octopus.InvokeRespStructV2, rawResponseJSON string, externalServiceID string, err error)
	NSDLPANVeriyAPI(ctx context.Context, req PANVerifyAPIRequest, opts Options) (bool, bool, bool, string, string, error)
	PayUCreatePaymentLink(ctx context.Context, req CreatePaymentPayURequest, opts Options) (PayUCreatePaymentLinkRespStruct, error)
	PoonawallaNSDLPANVeriyAPI(ctx context.Context, req PANVerifyAPIRequest, opts Options) (bool, bool, bool, string, PFLNSDLPANVerifyRespStruct, string, string, error)
	GetAndSaveCreatePaymentLinkPayU(ctx context.Context, userID, loanApplicationID string, amount float64) (string, error)
	CheckDeviceConnectDataExistsForThreshold(ctx context.Context, userID string, interval int, intervalUnit string) (bool, error)
	PollForDeviceConnectPreds(ctx context.Context, params DeviceConnectTriggerParams) (DeviceConnectResponse, error)
	ProcessDeviceConnectPredictors(ctx context.Context, userID, deviceConnectDetailsID, predictorVersion string, opts DeviceConnectPredictorsFetchOptions) error
	EmudhraSigningWithoutEStamp(ctx context.Context, req emudhra.SigningWithoutEStampRequest) (emudhra.SigningWithoutEStampResponse, error)
	EmudhraEmbeddedSigning(ctx context.Context, req emudhra.EmbeddedSigningRequest) (emudhra.EmbeddedSigningResponse, error)
	EmudhraDownloadDocument(ctx context.Context, req emudhra.DownloadDocumentRequest) (emudhra.DownloadDocumentResponse, error)
}
